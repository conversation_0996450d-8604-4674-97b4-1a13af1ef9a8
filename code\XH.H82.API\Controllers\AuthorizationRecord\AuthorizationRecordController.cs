using System.ComponentModel.DataAnnotations;
using AutoMapper;
using H.Utility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components.RenderTree;
using Microsoft.AspNetCore.Mvc;
using NuGet.Packaging;
using XH.H82.API.Extensions;
using XH.H82.IServices.AuthorizationRecord;
using XH.H82.Models.Dtos.AuthorizationRecord;
using XH.H82.Models.Entities;
using XH.H82.Services.AuthorizationRecord;

namespace XH.H82.API.Controllers.AuthorizationRecord;
/// <summary>
/// 设备授权记录相关
/// </summary>
[Route("api/[controller]/[action]")]
[ApiController]
[Authorize]
public class AuthorizationRecordController : ControllerBase
{
    private readonly IAuthorizationRecordService _authorizationRecordService;
    private readonly IMapper _mapper;

    public AuthorizationRecordController(IAuthorizationRecordService authorizationRecordService, IMapper mapper, IHostEnvironment environment)
    {
        _authorizationRecordService = authorizationRecordService;
        _mapper = mapper;
    }
    


    /// <summary>
    /// 添加授权权限字典
    /// </summary>
    /// <param name="input">{Content : "" , Remark : ""}</param>
    /// <returns></returns>
    [HttpPost]
    [CustomResponseType(typeof(AuthorizationRecordDictDto))]
    public IActionResult AddAuthorizationDict(AuthorizationRecordDictInput input)
    {
        var dict =  _authorizationRecordService.AddAuthorizationRecordDict(input.Content, input.Remark);
        var result = _mapper.Map<AuthorizationRecordDictDto>(dict);
        return Ok(result.ToResultDto());
    }


    /// <summary>
    /// 修改授权权限字典
    /// </summary>
    /// <param name="id"></param>
    /// <param name="input">{Content : "" , Remark : ""}</param>
    /// <returns></returns>
    [HttpPut("{id}")]
    public IActionResult UpdateAuthorizationDict([Required] string id, [FromBody] AuthorizationRecordDictInput input)
    {
        _authorizationRecordService.UpdateAuthorizationRecordDict(id,input.Content,input.Remark);
        return Ok(true.ToResultDto());
    }
    
    /// <summary>
    /// 删除授权权限字典
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
    [HttpDelete("{Id}")]
    public IActionResult DeleteContentDict([Required] string Id)
    {
        _authorizationRecordService.DeleteAuthorizationRecordDict(Id);
        return Ok(true.ToResultDto());
    }
    
    /// <summary>
    /// 查询授权权限字典列表
    /// </summary>
    /// <param name="content">字典内容</param>
    /// <returns></returns>
    [HttpGet]
    [CustomResponseType(typeof(List<AuthorizationRecordDictDto>))]
    public IActionResult GetAuthorizeRecordDicts(string? content)
    {
        var dicts = _authorizationRecordService.GetAuthorizationRecordDicts(content);
        var result = _mapper.Map<List<AuthorizationRecordDictDto>>(dicts);
        return Ok(result.ToResultDto());
    }

    /// <summary>
    /// 添加授权记录
    /// </summary>
    /// <param name="equipmentId">设备id</param>
    /// <param name="input"></param>
    /// <remarks>
    /// input: {
    /// "AUTHORIZE_PERSON_ID": "string", //用户id PERSON_ID
    /// "AUTHORIZE_PERSON": "string", login_userno 授权用户
    /// "AUTHORIZED_PERSON": "string", 多个被授权用户
    /// "AUTHORIZED_ROLE": "string",  授权权限
    /// "AUTHORIZE_DATE": "2024-12-30"  授权时间
    /// }
    /// </remarks>
    /// <returns></returns>
    [HttpPost("{equipmentId}")]
    [CustomResponseType(typeof(EMS_AUTHORIZE_INFO))]
    public IActionResult AddAuthorizeRecord([Required]string equipmentId ,[FromBody] AuthorizationRecordDto input)
    {
        if (equipmentId.IsNullOrEmpty())
        {
            throw new BizException("请选中设备后添加");
        }
        var record =  _authorizationRecordService.AddAuthorizationRecord(equipmentId, input);

        var result = 
        _authorizationRecordService.AddAuthorizationRecordDict(input.AUTHORIZED_ROLE,"自动记录");
        
        return Ok(record.ToResultDto());
    }
    
    /// <summary>
    /// 修改授权记录
    /// </summary>
    /// <param name="id">授权记录id  AUTHORIZE_ID </param>
    /// <param name="input"></param>
    /// <remarks>
    /// input: {
    /// "AUTHORIZE_PERSON_ID": "string", //用户id PERSON_ID
    /// "AUTHORIZE_PERSON": "string", login_userno 授权用户
    /// "AUTHORIZED_PERSON": "string", 多个被授权用户
    /// "AUTHORIZED_ROLE": "string",  授权权限
    /// "AUTHORIZE_DATE": "2024-12-30"  授权时间
    /// }
    /// </remarks>
    /// <returns></returns>
    [HttpPut("{id}")]
    public IActionResult UpdateAuthorizeRecord([Required]string id ,[FromBody] AuthorizationRecordDto input)
    {
        if (id.IsNullOrEmpty())
        {
            throw new BizException("请选授权记录");
        }
        _authorizationRecordService.UpdateAuthorizationRecord(id, input);
        _authorizationRecordService.AddAuthorizationRecordDict(input.AUTHORIZED_ROLE,"自动记录");
        return Ok(true.ToResultDto());
    }


    /// <summary>
    /// 获取悬浮表格中的用户信息
    /// </summary>
    /// <param name="names"></param>
    /// <param name="ids"></param>
    /// <returns></returns>
    /// <exception cref="BizException"></exception>
    [HttpGet]
    [CustomResponseType(typeof(List<TableUserInfoDto>))]
    public IActionResult GetTableUsers(string names ,string ids)
    {
        var result = new  List<TableUserInfoDto>();
        
        if (ids.IsNotNullOrEmpty() && names.IsNotNullOrEmpty())
        {
            throw new BizException("参数错误");
        }

        if (names.IsNotNullOrEmpty())
        {
            var hisNames = names.Split(";").ToList();
            if (hisNames.Any())
            {
                result.AddRange(_authorizationRecordService.GetTableUsersByHisName(hisNames));  
            }
        }

        if (ids.IsNotNullOrEmpty())
        {
            var userNos = ids.Split(";").ToList();
            if (userNos.Any())
            {
                result.AddRange(_authorizationRecordService.GetTableUsersByIds(userNos));
            }
        }
        return Ok(result.ToResultDto());
    }
}