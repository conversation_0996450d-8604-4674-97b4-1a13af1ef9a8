﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using H.Utility.SqlSugarInfra;
using SqlSugar;

namespace XH.H82.Models.Entities.THS
{
    /// <summary>
    /// 设备监测点指标信息表
    /// </summary>
    [DBOwner("XH_SYS")]
    [Table("THS_EQUIPMENT_POINT_ITEM")]
    [SugarTable("THS_EQUIPMENT_POINT_ITEM")]
    public class THS_EQUIPMENT_POINT_ITEM
    {
        /// <summary>
        /// 监测点ID
        /// </summary>
        [Key]
        [SugarColumn(IsPrimaryKey = true)]
        public string? POINT_ID { get; set; }
        /// <summary>
        /// 指标ID
        /// </summary>
        [Key]
        [SugarColumn(IsPrimaryKey = true)]
        public string? ITEM_ID { get; set; }
        /// <summary>
        /// 指标名称
        /// </summary>
        public string? ITEM_NAME { get; set; }
        /// <summary>
        /// 排序号
        /// </summary>
        public string? ITEM_SORT { get; set; }
        /// <summary>
        /// 报警规则ID
        /// </summary>
        public string? RULE_ID { get; set; }
        /// <summary>
        /// 在线状态1在线2离线
        /// </summary>
        public string? ONLINE_ITEM_STATE { get; set; }
        /// <summary>
        /// 异常状态1正常2异常
        /// </summary>
        public string? ABNORMAL_ITEM_STATE { get; set; }
        /// <summary>
        /// 状态0禁用1在用
        /// </summary>
        public string? ITEM_STATE { get; set; }
        /// <summary>
        /// 首次登记人
        /// </summary>
        public string? FIRST_RPERSON { get; set; }
        /// <summary>
        /// 首次登记时间
        /// </summary>
        public DateTime? FIRST_RTIME { get; set; }
        /// <summary>
        /// 最后修改人员
        /// </summary>
        public string? LAST_MPERSON { get; set; }
        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime? LAST_MTIME { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string? REMARK { get; set; }
        /// <summary>
        /// 坐标参数
        /// </summary>
        public string? COORDINATE_PARAM { get; set; }
        /// <summary>
        /// 对应通道号
        /// </summary>
        public string? ITEM_CHANNEL { get; set; }
    }
}
