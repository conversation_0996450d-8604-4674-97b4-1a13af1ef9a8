﻿using H.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XH.H82.Models;
using XH.H82.Models.Dtos;
using XH.H82.Models.Entities;

namespace XH.H82.IServices
{
    public interface IWorkPlanService
    {
        //List<WorkPlanDto> GetWorkPlanList(string userNo, string hospitalId, string keyword, string mgroupId, string equipmentClass,string labId,string pgroupId, string areaId);
        ResultDto SaveWorkPlan(List<WorkPlanDto> record,string userName);
        ClassTreeDto GetClassList(string hospitalId, string userNo, string labId);
        //List<EMS_EQUIPMENT_INFO> GetWorkPlanByClass(string hospitalId, string labId, string userNo, string keyword, string equipmentClass);
        
        /// <summary>
        /// 统计需要进行年检的设备
        /// </summary>
        /// <returns></returns>
        List<CountWorkPlanDto> GetCountWorkPlan();


        List<EquipmentYearCheckCountDto> GetEquipmentYearCheckCount(string? hospitalId, string? labId);
        List<ConutEquipmentYearChectByMonth> GetEquipmentYearCheck(int year,string? hospitalId, string? labId);
        List<ConutReminderDto> ConutReminders(string smblLabId);

        /// <summary>
        /// 获取所有记录 按年、月份
        /// </summary>
        /// <param name="time"></param>
        /// <returns></returns>
        List<IRecord> GetAllRecordsByDate(DateTime? time);

        Dictionary<string, string> GetEquipmentNameDict(List<string> equipmentIds , string smblLabId);
    }
}
