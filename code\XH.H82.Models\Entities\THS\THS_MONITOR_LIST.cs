﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using H.Utility.SqlSugarInfra;
using SqlSugar;

namespace XH.H82.Models.Entities.THS
{
    /// <summary>
    /// 设备检测点监测记录表
    /// </summary>
    [DBOwner("XH_DATA")]
    [Table("THS_MONITOR_LIST")]
    [SugarTable("THS_MONITOR_LIST")]
    public class THS_MONITOR_LIST
    {
        /// <summary>
        /// 监测记录ID
        /// </summary>
        [Key]
        [SugarColumn(IsPrimaryKey = true)]
        public string? MONITOR_ID { get; set; }
        /// <summary>
        /// 设备ID
        /// </summary>
        public string? EQUIPMENT_ID { get; set; }
        /// <summary>
        /// 监测点ID
        /// </summary>
        public string? POINT_ID { get; set; }
        /// <summary>
        /// 管理单元ID
        /// </summary>
        public string? UNIT_ID { get; set; }
        /// <summary>
        /// 医疗机构ID
        /// </summary>
        public string? HOSPITAL_ID { get; set; }
        /// <summary>
        /// 记录日期
        /// </summary>
        public DateTime? MONITOR_DATE { get; set; }
        /// <summary>
        /// 监测时间点
        /// </summary>
        public string? TIME_POINT { get; set; }
        /// <summary>
        /// 记录人员
        /// </summary>
        public string? MONITOR_PERSON { get; set; }
        /// <summary>
        /// 记录时间
        /// </summary>
        public DateTime? MONITOR_TIME { get; set; }
        /// <summary>
        /// 记录电脑
        /// </summary>
        public string? MONITOR_COMPUTER { get; set; }
        /// <summary>
        /// 监测临时记录ID
        /// </summary>
        public string? MONITOR_TID { get; set; }
        /// <summary>
        /// 状态1正常2异常
        /// </summary>
        public string? MONITOR_STATE { get; set; }
        /// <summary>
        /// 首次登记人
        /// </summary>
        public string? FIRST_RPERSON { get; set; }
        /// <summary>
        /// 首次登记时间
        /// </summary>
        public DateTime? FIRST_RTIME { get; set; }
        /// <summary>
        /// 最后修改人员
        /// </summary>
        public string? LAST_MPERSON { get; set; }
        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime? LAST_MTIME { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string? REMARK { get; set; }
        /// <summary>
        /// 处理人员
        /// </summary>
        public string? OPER_PERSON { get; set; }
        /// <summary>
        /// 处理时间
        /// </summary>
        public DateTime? OPER_TIME { get; set; }
        /// <summary>
        /// 处理电脑
        /// </summary>
        public string? OPER_COMPUTER { get; set; }

        /// <summary>
        /// 关联的监测值结果
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToMany,nameof(THS_MONITOR_RESULT.MONITOR_ID))]
        public List<THS_MONITOR_RESULT> ThsMonitorResults { get; set; } 
    }
}
