﻿"restore":{"projectUniqueName":"C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.API\\XH.H82.API.csproj","projectName":"XH.H82.API","projectPath":"C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.API\\XH.H82.API.csproj","outputPath":"C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.API\\obj\\","projectStyle":"PackageReference","fallbackFolders":["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"],"originalTargetFrameworks":["net6.0"],"sources":{"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\":{},"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net6.0":{"targetAlias":"net6.0","projectReferences":{"C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Base\\XH.H82.Base.csproj":{"projectPath":"C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Base\\XH.H82.Base.csproj"},"C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.IServices\\XH.H82.IServices.csproj":{"projectPath":"C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.IServices\\XH.H82.IServices.csproj"},"C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Models\\XH.H82.Models.csproj":{"projectPath":"C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Models\\XH.H82.Models.csproj"},"C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Services\\XH.H82.Services.csproj":{"projectPath":"C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Services\\XH.H82.Services.csproj"}}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"},"SdkAnalysisLevel":"9.0.300"}"frameworks":{"net6.0":{"targetAlias":"net6.0","dependencies":{"Autofac.Extensions.DependencyInjection":{"target":"Package","version":"[8.0.0, )"},"Autofac.Extras.DynamicProxy":{"target":"Package","version":"[6.0.1, )"},"EPPlus":{"target":"Package","version":"[6.2.6, )"},"ExcelDataReader":{"target":"Package","version":"[3.7.0-develop00385, )"},"ExcelDataReader.DataSet":{"target":"Package","version":"[3.7.0-develop00385, )"},"Microsoft.Extensions.Hosting.WindowsServices":{"target":"Package","version":"[6.0.1, )"},"Microsoft.VisualStudio.Azure.Containers.Tools.Targets":{"target":"Package","version":"[1.21.0, )"},"NPOI":{"target":"Package","version":"[2.6.0, )"},"Namotion.Reflection":{"target":"Package","version":"[2.1.1, )"},"Npoi.Mapper":{"target":"Package","version":"[6.0.0, )"},"Yarp.ReverseProxy":{"target":"Package","version":"[2.1.0, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"frameworkReferences":{"Microsoft.AspNetCore.App":{"privateAssets":"none"},"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}