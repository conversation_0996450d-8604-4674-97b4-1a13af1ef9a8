using System;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using SqlSugar;
using H.Utility.SqlSugarInfra;

namespace XH.H82.Models.Entities.Common
{
    [DBOwner("XH_SYS")]
    [SugarTable("SYS6_BASE_DATA")]
    public class SYS6_BASE_DATA
    {
        [SugarColumn(IsPrimaryKey = true)]
        [Column("DATA_ID")]
        [Required(ErrorMessage = "不允许为空")]
        [StringLength(50, ErrorMessage = "DATA_ID长度不能超出50字符")]
        [Unicode(false)]
        public string DATA_ID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("HOSPITAL_ID")]
        [Required(ErrorMessage = "不允许为空")]
        [StringLength(20, ErrorMessage = "HOSPITAL_ID长度不能超出20字符")]
        [Unicode(false)]
        public string HOSPITAL_ID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("LAB_ID")]
        [Required(ErrorMessage = "不允许为空")]
        [StringLength(20, ErrorMessage = "LAB_ID长度不能超出20字符")]
        [Unicode(false)]
        public string LAB_ID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("CLASS_ID")]
        [SugarColumn(IsPrimaryKey = true)]
        [Required(ErrorMessage = "不允许为空")]
        [StringLength(50, ErrorMessage = "CLASS_ID长度不能超出50字符")]
        [Unicode(false)]
        public string CLASS_ID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("DATA_SORT")]
        [StringLength(20, ErrorMessage = "DATA_SORT长度不能超出20字符")]
        [Unicode(false)]
        public string? DATA_SORT { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("DATA_CNAME")]
        [StringLength(100, ErrorMessage = "DATA_CNAME长度不能超出100字符")]
        [Unicode(false)]
        public string? DATA_CNAME { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("DATA_ENAME")]
        [StringLength(50, ErrorMessage = "DATA_ENAME长度不能超出50字符")]
        [Unicode(false)]
        public string? DATA_ENAME { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("HIS_ID")]
        [StringLength(200, ErrorMessage = "HIS_ID长度不能超出200字符")]
        [Unicode(false)]
        public string? HIS_ID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("CUSTOM_CODE")]
        [StringLength(20, ErrorMessage = "CUSTOM_CODE长度不能超出20字符")]
        [Unicode(false)]
        public string? CUSTOM_CODE { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("SPELL_CODE")]
        [StringLength(20, ErrorMessage = "SPELL_CODE长度不能超出20字符")]
        [Unicode(false)]
        public string? SPELL_CODE { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("DATA_STATE")]
        [StringLength(20, ErrorMessage = "DATA_STATE长度不能超出20字符")]
        [Unicode(false)]
        public string? DATA_STATE { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("FIRST_RPERSON")]
        [StringLength(50, ErrorMessage = "FIRST_RPERSON长度不能超出50字符")]
        [Unicode(false)]
        public string? FIRST_RPERSON { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("FIRST_RTIME")]
        [Unicode(false)]
        public DateTime? FIRST_RTIME { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("LAST_MPERSON")]
        [StringLength(50, ErrorMessage = "LAST_MPERSON长度不能超出50字符")]
        [Unicode(false)]
        public string? LAST_MPERSON { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("LAST_MTIME")]
        [Unicode(false)]
        public DateTime? LAST_MTIME { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("REMARK")]
        [StringLength(200, ErrorMessage = "REMARK长度不能超出200字符")]
        [Unicode(false)]
        public string? REMARK { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("DATA_SNAME")]
        [StringLength(500, ErrorMessage = "DATA_SNAME长度不能超出500字符")]
        [Unicode(false)]
        public string? DATA_SNAME { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("DATA_SOURCE")]
        [StringLength(500, ErrorMessage = "DATA_SOURCE长度不能超出500字符")]
        [Unicode(false)]
        public string? DATA_SOURCE { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("ONE_CLASS")]
        [StringLength(50, ErrorMessage = "ONE_CLASS长度不能超出50字符")]
        [Unicode(false)]
        public string? ONE_CLASS { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("DATA_UNAME")]
        [StringLength(50, ErrorMessage = "DATA_UNAME长度不能超出50字符")]
        [Unicode(false)]
        public string? DATA_UNAME { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("IF_REPEAT")]
        [StringLength(20, ErrorMessage = "IF_REPEAT长度不能超出20字符")]
        [Unicode(false)]
        public string? IF_REPEAT { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("SYSTEM_ID")]
        [StringLength(20, ErrorMessage = "SYSTEM_ID长度不能超出20字符")]
        [Unicode(false)]
        public string? SYSTEM_ID { get; set; }


    }
}
