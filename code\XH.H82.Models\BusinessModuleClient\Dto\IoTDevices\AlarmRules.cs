using Newtonsoft.Json;

namespace XH.H82.Models.BusinessModuleClient.Dto.IoTDevices;

/// <summary>
/// 传感器告警规则
/// </summary>
public class AlarmRules
{
    /// <summary>
    /// 上限
    /// </summary>
    [JsonProperty("up")]
    public double Up { get; set; }
    /// <summary>
    /// 下限
    /// </summary>
    [JsonProperty("down")]
    public double Down { get; set; }

    /// <summary>
    /// 是否需要告警 1需要 0 不需要
    /// </summary>
    [JsonProperty("isNeedAlarm")]
    public int IsNeedAlarm { get; set; } = 1;
    /// <summary>
    /// 监测告警类型
    /// 100开关使用状态、101电压、102电流、103功率、104累计电量、200温度、201湿度、202噪声、203气压、205紫外线强度、206水压
    /// </summary>
    [JsonProperty("bioAlarmType")]
    public int BioAlarmType { get; set; }
    /// <summary>
    /// 数值单位
    /// </summary>
    [JsonProperty("unit")]
    public string unit { get; set; }
}