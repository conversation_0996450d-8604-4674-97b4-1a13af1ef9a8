﻿using EasyCaching.Core;
using H.Utility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using XH.H82.Base.Setup;
using XH.H82.IServices;
using XH.H82.Models.Dtos;

namespace XH.H82.API.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize]
    public class WorkPlanController : ControllerBase
    {
        
        private readonly IWorkPlanService _workPlanService;
        private readonly IConfiguration _configuration;
        private readonly IMemoryCache _easyCacheMemory;
        private string currentLabKey = "XH:LIS:H82:CURRENTLAB:UserSelectedLab:";
        private readonly string RedisModule;
        public WorkPlanController(IWorkPlanService workPlanService, IConfiguration configuration, IMemoryCache easyCahceFactory)
        {
            _workPlanService = workPlanService;
            _configuration = configuration;
            RedisModule = _configuration["RedisModule"];
            _easyCacheMemory = easyCahceFactory;
        }


        /// <summary>
        ///   保存工作计划
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult SaveWorkPlan([FromBody] List<WorkPlanDto> record)
        {
            var claims = User.ToClaimsDto();
            var userName = claims.HIS_NAME;
            var res = _workPlanService.SaveWorkPlan(record, userName);
            return Ok(res);
        }

        /// <summary>
        ///   设备类型树结构
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetClassList()
        {
            var claims = User.ToClaimsDto();
            var clientMac = GetLocalMac.GetMac();
            currentLabKey = currentLabKey + claims.USER_NO + "." + clientMac;
            var labId = _easyCacheMemory.Get<string>(currentLabKey);
            var res = _workPlanService.GetClassList(claims.HOSPITAL_ID, claims.USER_NO, labId);
            return Ok(res.ToResultDto());
        }
    }
}
