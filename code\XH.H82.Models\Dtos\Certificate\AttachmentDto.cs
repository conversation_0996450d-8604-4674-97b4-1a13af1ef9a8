using AutoMapper;
using AutoMapper.Configuration.Annotations;
using XH.H82.Models.Entities;

namespace XH.H82.Models.Dtos.Certificate;

/// <summary>
/// 附件Dto
/// </summary>
[AutoMap(typeof(EMS_DOC_INFO))]
public class AttachmentDto
{
    /// <summary>
    /// 附件id
    /// </summary>
    [SourceMember("DOC_ID")]
    public string UploadFileId { get; set; }
    /// <summary>
    /// 文件名
    /// </summary>
    [SourceMember("DOC_NAME")]
    public string FileName  { get; set; }
    /// <summary>
    /// 文件路径
    /// </summary>
    [SourceMember("DOC_PATH")]
    public string Path { get; set; }

    /// <summary>
    /// 预览路径
    /// </summary>
    [SourceMember("PDF_PREVIEW_PATH")]

    public string PreviewPath { get; set; }
    /// <summary>
    /// 文件名后缀
    /// </summary>
    [SourceMember("DOC_SUFFIX")]

    public string FileSuffix { get; set; }

    /// <summary>
    /// 文件类型
    /// </summary>
    [SourceMember("DOC_TYPE")]
    public string DocType { get; set; }

    /// <summary>
    /// 证书信息ID
    /// </summary>
    [SourceMember("EMS_INFO_ID")]
    public string CertificateId { get; set; }

}