using System.ComponentModel.DataAnnotations;
using H.Utility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Newtonsoft.Json;
using Serilog;
using XH.H82.API.Controllers.IoTDevice.Dto;
using XH.H82.API.Controllers.IoTDevice.Dtos;
using XH.H82.API.Extensions;
using XH.H82.IServices;
using XH.H82.IServices.IoTDevice;
using XH.H82.Models.BusinessModuleClient.Dto.IoTDevices;
using XH.H82.Models.Dtos;
using XH.H82.Models.Entities.THS;
using XH.H82.Models.Smbl;
using ClaimsDto = H.Utility.ClaimsDto;

namespace XH.H82.API.Controllers.IoTDevice;

[Route("api/[controller]/[action]")]
[ApiController]
[Authorize]
public  class IoTDeviceController : ControllerBase
{
    private readonly IHttpContextAccessor _httpContext;
    private readonly ClaimsDto _user;
    private readonly IIoTDeviceService _deviceService;
    private readonly IWorkPlanService _workPlanService;
    private readonly IMemoryCache _cache;



    public IoTDeviceController(IHttpContextAccessor httpContext, IIoTDeviceService deviceService, IWorkPlanService workPlanService, IMemoryCache cache)
    {
        _httpContext = httpContext;
        _deviceService = deviceService;
        _workPlanService = workPlanService;
        _cache = cache;
        _user = _httpContext.HttpContext.User.ToClaimsDto();
    }

    
    /// <summary>
    /// 智能插座监测记录测试
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [AllowAnonymous]
    public IActionResult EquipmentInit(string id)
    {
         _deviceService.InitEquipmentToThs(id);
        return Ok(); 
    }
    
    /// <summary>
    /// 智能插座监测记录测试
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [AllowAnonymous]
    public IActionResult EquipmentInit1111(string id)
    {
        var equipment = _deviceService.GetEquipmentInfo(id);
;       var result =  _deviceService.GetEquipmentIoTData(equipment);
        if (result is not  null)
        {
            _deviceService.AddObtainMonitoringData(equipment,result);
        }
        return Ok(result); 
    }
    
    
    /// <summary>
    /// 获取智能插座列表
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [AllowAnonymous]
    public IActionResult GetIoTDevieces( )
    {
        return Ok(_deviceService.GetIotDevice()); 
    }
    
    
    /// <summary>
    /// 获取传感器列表
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [AllowAnonymous]
    public IActionResult GetChuanqganqi()
    {
        return Ok(_deviceService.Chuanganqi()); 
    }
    
    
    /// <summary>
    /// 获取生安设备日历各种记录
    /// </summary>
    /// <param name="time">时间范围 yyyy-MM 精确到月</param>
    /// <param name="smblLabId">备案实验室id 选填</param>
    /// <returns></returns>
    [HttpGet("{time}")]
    [CustomResponseType(typeof(List<CalendarRecordsDto>))]
    public IActionResult GetCalendarRecords([Required]DateTime? time  , string? smblLabId)
    {
        if (!time.HasValue)
        {
            time = DateTime.Today;
        }

        var result = new List<CalendarRecordsDto>();
        var records = _workPlanService.GetAllRecordsByDate(time);
        var equipmentIds = records.Select(x => x.GetEquipmentId()).ToList();
        var equipmentDict = _workPlanService.GetEquipmentNameDict(equipmentIds,smblLabId);
        
        var calendarRecords = new List<CalendarRecordDto>();
        foreach (var record in records)
        {
            if (equipmentDict.TryGetValue(record.GetEquipmentId(), out var equipmentName))
            {
                var calendarRecordDto = new CalendarRecordDto();
                calendarRecordDto.Id = record.GetId();
                calendarRecordDto.Type = record.GetType().Type;
                calendarRecordDto.TypeName = record.GetType().TypeName;
                calendarRecordDto.Title = equipmentName;
                calendarRecordDto.BeginDate = record.GetRecordDate().Value.ToString("yyyy-MM-dd");
                calendarRecordDto.EndDate = record.GetRecordDate().Value.ToString("yyyy-MM-dd");
                calendarRecords.Add(calendarRecordDto);
            }
        }

        var types = calendarRecords.GroupBy(x => new {x.Type , x.TypeName});

        foreach (var type in types)
        {
            var item = new CalendarRecordsDto();
            item.Type = type.Key.Type;
            item.TypeName = type.Key.TypeName;
            item.RecordCalendars = type.ToList();
            result.Add(item);
        }
        return Ok(result.ToResultDto());
    }

    /// <summary>
    /// 生安监测设备-折现和状态矩形
    /// </summary>
    /// <param name="equipmentId"></param>
    /// <param name="date"></param>
    /// <param name="timeSpan"></param>
    /// <param name="smblClass">生安设备类型</param>
    /// <param name="ItemId">监测项</param>
    /// <returns></returns>
    [HttpGet("{equipmentId}")]
    [CustomResponseType(typeof(List<LineChartDto>))]
    public IActionResult GetDviceMonitorsStatisticsByLineChartAndMrak([Required] string equipmentId , DateTime? date ,double timeSpan = 30 ,string? smblClass = null, string? ItemId =null)
    {
        var result = new List<LineChartDto>();
        if (!date.HasValue)
        {
            date = DateTime.Now;
        }
        
        var monitoringProject = _deviceService.GetEquipmentIndicator(equipmentId);
        if (smblClass is not null  && ItemId is not null)
        {
            monitoringProject = (ItemId, smblClass);
        }
        var monitors = _deviceService.GetObtainMonitoringData(equipmentId,date)
            .OrderBy(x=>x.MONITOR_TIME)
            .Where(x=>x.ThsMonitorResults.FirstOrDefault(x=>x.ITEM_ID == monitoringProject.Project) is  not null)
            .Where(x => x.MONITOR_TIME.Value.Date == date.Value.Date  ||  x.MONITOR_TIME.Value.Date == date.Value.Date.AddDays(-1) )
            .ToList();
        if (!monitors.Any())
        {
            return Ok(result.ToResultDto());
        }
        
        var startTime = date.Value.Date;
        var endTime = date.Value.Date.AddDays(1);
        var range = (endTime - startTime).TotalMinutes/timeSpan;
        for (int i = 0; i < range; i++)
        {
            var timeStart = startTime.AddMinutes(i * timeSpan);
            var timeEnd = startTime.AddMinutes(timeSpan * (i + 1));
            if (timeStart > DateTime.Now.AddMinutes(-30))
            {
                break;
            }
            var rangeRecord = monitors
                .Where(x => x.MONITOR_TIME.Value >= timeStart.AddMinutes(-2))
                .Where(x => x.MONITOR_TIME.Value <= timeEnd.AddMinutes(2))
                .ToList();

            var itemValues = GetItenValueByTime(rangeRecord, monitoringProject.Project,monitoringProject.ProjectClass, timeStart, timeEnd);
            result.AddRange(itemValues);
            
            // var avg = rangeRecord.Sum(x => 
            //     double.Parse(x.ThsMonitorResults.FirstOrDefault(x=>
            //         x.ITEM_ID == monitoringProject.Project).ITEM_VALUE))/rangeRecord.Count();
            // if (avg is Double.NaN)
            // {
            //     avg = 0;
            // }
            // result.Add(new LineChartDto()
            // {
            //     Value = avg.ToString("F3"),
            //     Status = LineChartDto.GetStatus(avg,monitoringProject.Project,monitoringProject.ProjectClass)
            // });
        }
        
        return Ok(result.ToResultDto());
    }

    private List<LineChartDto> GetItenValueByTime(List<THS_MONITOR_LIST> rangeRecords , string itemId , string smblClass,  DateTime startTime , DateTime end)
    {
        var result = new List<LineChartDto>();
        var nowStatus = string.Empty;
        var nowStatusValue = double.NaN;
        if (rangeRecords.Count == 0)
        {
            result.Add(new LineChartDto()
            {
                Value = "0",
                Status = "离线"
            });
            return result;
        }
        foreach (var rangeRecord in rangeRecords)
        {
            var item = rangeRecord.ThsMonitorResults!.FirstOrDefault(x => x.ITEM_ID == itemId);
            var itemValue = Double.NaN;
            if (item is not  null)
            {
                itemValue = double.Parse(item.ITEM_VALUE!);
            }
            //第一次计算状态值
            if (nowStatus ==  string.Empty)
            {
                nowStatus = LineChartDto.GetStatus(itemValue, itemId, smblClass);
                nowStatusValue = itemValue;
            }
            //后续计算状态值
            else
            {
                if (nowStatus == LineChartDto.GetStatus(itemValue, itemId, smblClass))
                {
                    nowStatusValue = (nowStatusValue + itemValue)/2;
                }
                else
                {
                    result.Add(new LineChartDto()
                    {
                        Value = nowStatusValue.ToString("F3"),
                        Status = nowStatus
                    });
                    nowStatus = LineChartDto.GetStatus(itemValue, itemId, smblClass);
                    nowStatusValue = itemValue;
                }
            }
        }
        result.Add(new LineChartDto()
        {
            Value = nowStatusValue.ToString("F3"),
            Status = nowStatus
        });
        return result;
    }

    /// <summary>
    /// 重点监测设备的使用记录-日汇总  
    /// </summary>
    /// <param name="equipmentId">设备id</param>
    /// <param name="startDate">开始时间</param>
    /// <param name="endDate">结束时间</param>
    /// <param name="smblClass">生安设备类型</param>
    /// <param name="itemId">监测项id</param>
    /// <returns> 生物安全柜、水压、洗眼器  环境一体机 </returns>
    [HttpGet("{equipmentId}")]
    [CustomResponseType(typeof(List<IotDeviceUsingRecordDto>))]
    public IActionResult GetDeviceMonitorUseRecoreds([Required] string equipmentId ,DateTime startDate , DateTime endDate ,string? smblClass, string? itemId)
    {
        var result = new List<IotDeviceUsingRecordDto>();
        if (startDate.Date == endDate.Date)
        {
            endDate =  endDate.Date.AddDays(1).AddSeconds(-1);
        }

        var monitoringProject = _deviceService.GetEquipmentIndicator(equipmentId);

        if (smblClass is not null  && itemId is not null)
        {
            monitoringProject = (itemId, smblClass);
        }
        
        var monitors = _deviceService.GetObtainMonitoringDataByTime(equipmentId,startDate,endDate)
            .OrderBy(x=>x.MONITOR_TIME)
            .Where(x=>x.ThsMonitorResults.FirstOrDefault(x=>x.ITEM_ID == monitoringProject.Project) is  not null)
            .ToList();
        if (!monitors.Any())
        {
            return Ok(result.ToResultDto());
        }
        var groupedRecords = monitors.GroupBy(x => x.MONITOR_TIME.Value.Date);
        foreach (var group in groupedRecords)
        {
            var dayRecords = group.ToList();
            var dayUseRecords = dayRecords.Select(x => x.ThsMonitorResults.FirstOrDefault(x => x.ITEM_ID == monitoringProject.Project))
                .ToList();
            if (dayUseRecords.Any())
            {
                var dailyStat = AnalyzeRecordsByDay(group.Key,dayUseRecords,_deviceService.GetDevicesStatusValue(monitoringProject.ProjectClass , monitoringProject.Project));
                result.Add(
                    new IotDeviceUsingRecordDto
                    {
                        Date = group.Key.ToString("yyyy-MM-dd"),
                        EarliestStartTime = dailyStat.EarliestStartTime?.ToString("HH:mm"),
                        LatestShutdownTime = dailyStat.LatestShutdownTime?.ToString("HH:mm"),
                        TotalIdleTime = dailyStat.TotalIdleTime.Days == 1 ? "24小时" :  dailyStat.TotalIdleTime.Hours== 0 ? "0分" : dailyStat.TotalIdleTime.Hours == 0 ? $"{dailyStat.TotalIdleTime.Minutes.ToString("F0")}分" : $"{dailyStat.TotalIdleTime.Hours.ToString("F0")}时{dailyStat.TotalIdleTime.Minutes.ToString("F0")}分",
                        TotalUsageTime  = dailyStat.TotalUsageTime.Days == 1 ? "24小时" : dailyStat.TotalUsageTime.Hours== 0 ? "0分" : dailyStat.TotalUsageTime.Hours == 0 ? $"{dailyStat.TotalUsageTime.Minutes.ToString("F0")}分" : $"{dailyStat.TotalUsageTime.Hours.ToString("F0")}时{dailyStat.TotalUsageTime.Minutes.ToString("F0")}分",
                        TotalShutdownTime = dailyStat.TotalShutdownTime.Days == 1 ? "24小时" : dailyStat.TotalShutdownTime.Hours== 0 ? "0分" : dailyStat.TotalShutdownTime.Hours == 0 ? $"{dailyStat.TotalShutdownTime.Minutes.ToString("F0")}分" : $"{dailyStat.TotalShutdownTime.Hours.ToString("F0")}时{dailyStat.TotalShutdownTime.Minutes.ToString("F0")}分",
                    }
                );
            }
        }
        result = result.OrderByDescending(x=>x.Date).ToList();
        var no = 0;
        foreach (var item in result)
        {
            item.No = ++no;
        }
        return Ok(result.ToResultDto());
    }

    /// <summary>
    /// 统计使用状态的列表
    /// </summary>
    /// <param name="date"></param>
    /// <param name="records"></param>
    /// <param name="devicesStatusValue"></param>
    /// <returns></returns>
    private DailyStats AnalyzeRecordsByDay(DateTime date, List<THS_MONITOR_RESULT> records , DevicesStatusValue devicesStatusValue)
    {
        var result = new DailyStats()
        {
            Date = date,
            EarliestStartTime = null,
            LatestShutdownTime = null,
            TotalIdleTime = TimeSpan.Zero,
            TotalUsageTime = TimeSpan.Zero,
            TotalShutdownTime = TimeSpan.Zero
        };
        //记录连续关机状态下的第一次关机时间 不为空null 则不记录，状态从关机->其他状态 则清空
        DateTime? lastShutdownStart = null;
        for (int i = 0; i < records.Count; i++)
        {
            var currentRecord = records[i];

            // 确定最早使用时间
            if (devicesStatusValue.IsUse(double.Parse(currentRecord.ITEM_VALUE)) && result.EarliestStartTime == null)
            {
                result.EarliestStartTime = currentRecord.LAST_MTIME;
            }

            // 确定最迟关机时间
            if (devicesStatusValue.IsShutDown(double.Parse(currentRecord.ITEM_VALUE)))
            {
                if (result.LatestShutdownTime == null)
                {
                    // 第一次记录关机时间，设置为最迟关机时间
                    result.LatestShutdownTime = currentRecord.LAST_MTIME;
                }
                lastShutdownStart = currentRecord.LAST_MTIME;
            }
            else
            {
                // 如果状态从关机变为其他状态，则结束当前关机时长
                if (lastShutdownStart != null)
                {
                    lastShutdownStart = null;
                }
            }

            // 计算累 计待机时间和累计使用、关机时长时间
            if (i < records.Count - 1)
            {
                var nextRecord = records[i + 1];
                TimeSpan duration = nextRecord.LAST_MTIME.Value - currentRecord.LAST_MTIME.Value;
                if (i == 0)
                {
                    var timespanx = currentRecord.LAST_MTIME.Value - date.Date;
                    duration += timespanx;
                }
                
                if (i  == records.Count - 2)
                {
                    var timespany = date.Date.AddDays(1).Date - nextRecord.LAST_MTIME.Value;
                    if (date.Date == DateTime.Now.Date)
                    {
                        timespany = DateTime.Now - nextRecord.LAST_MTIME.Value;
                    }
                    duration += timespany;
                }
                var value = double.Parse(currentRecord.ITEM_VALUE);
                if (devicesStatusValue.IsUse(value))
                {
                    result.TotalUsageTime += duration;
                }

                if (devicesStatusValue.IsShutDown(value))
                {
                    result.TotalShutdownTime += duration;
                }

                if (devicesStatusValue.IsStandBy(value))
                {
                    result.TotalIdleTime += duration;
                }
                
            }
            
        }
        return result;
    }

    /// <summary>
    /// 统计生安重点设备类的总使用情况
    /// </summary>
    /// <param name="hosiptalId">机构id</param>
    /// <param name="labId">科室id</param>
    /// <param name="smblLabId">备案实验室id</param>
    /// <returns></returns>
    [HttpGet]
    [CustomResponseType(typeof(List<CountTheTotalUseEquipmentDto>))]
    public IActionResult CountTheTotalUseEquipmentCategories(string?  hosiptalId , string? labId , string? smblLabId )
    {
        var result = new List<CountTheTotalUseEquipmentDto>();
        var keyTypes =  new List<string>{ "1","2", "4", "5","9"};
        foreach (var keyType in keyTypes)
        {
            result.Add(new ()
            {
                SmblClass = keyType,
                SmblClassName = keyType switch
                {
                    "1"=>"生物安全柜",
                    "2"=>"高压灭菌器",
                    "4"=>"洗眼装置",
                    "5"=>"紫外灯",
                    "9"=>"冲淋装置",
                }
            });
        }
        
        var equipments = _deviceService.GetEquipmentsUseCase(hosiptalId,labId, smblLabId);
        var obtainMonitoringEquipments = _deviceService.GetObtainMonitoringEquipments();
        var now = DateTime.Now;
        DateTime firstDayOfMonth = new DateTime(DateTime.Now.Year, now.Month, 1);
        DateTime lastDayOfMonth = firstDayOfMonth.AddMonths(1);
        foreach (var equipment in equipments)
        {
            var CountTheTotalUseEquipmentCategorie = new CountTheTotalUseEquipmentCategoriesDto();
            var monitoringProject = _deviceService.GetEquipmentIndicatorBySmblClass(equipment.SMBL_CLASS);
            if (obtainMonitoringEquipments.Exists(x=>x.EQUIPMENT_ID == equipment.EQUIPMENT_ID))
            {
                // 获取当月前设备监测记录
                var monitors = _deviceService.GetObtainMonitoringDataByTime(equipment.EQUIPMENT_ID, firstDayOfMonth,lastDayOfMonth)
                    .OrderBy(x=>x.MONITOR_TIME)
                    .Where(x=>x.ThsMonitorResults.FirstOrDefault(x=>x.ITEM_ID == monitoringProject.Project) is  not null)
                    .ToList();
                //如果没有监测记录
                if (!monitors.Any())
                {
                    CountTheTotalUseEquipmentCategorie.DayUsageRate = 0;
                    CountTheTotalUseEquipmentCategorie.CurrentUseDayHours = 0;
                    CountTheTotalUseEquipmentCategorie.CurrentAvgUseMoonHours = 0;
                    CountTheTotalUseEquipmentCategorie.CurrentAvgUseMoonUseDay = 0;
                }
                else
                {
                    var moonDatas = new List<DailyStats>();
                    var groupedRecords = monitors.GroupBy(x => x.MONITOR_TIME.Value.Date);
                    foreach (var group in groupedRecords)
                    {
                        var dayRecords = group.ToList();
                        var dayUseRecords = dayRecords.Select(x => x.ThsMonitorResults.FirstOrDefault(x => x.ITEM_ID == monitoringProject.Project))
                            .ToList();
                        if (dayUseRecords.Any())
                        {
                            var dailyStat = AnalyzeRecordsByDay(group.Key,dayUseRecords,_deviceService.GetDevicesStatusValue(monitoringProject.ProjectClass, monitoringProject.Project));
                            moonDatas.Add(dailyStat);
                        }
                    }

                    var dayUsage = moonDatas.FirstOrDefault(x => x.Date.Date == now.Date);
                    if (dayUsage is null)
                    {
                        CountTheTotalUseEquipmentCategorie.DayUsageRate = 0;
                        CountTheTotalUseEquipmentCategorie.CurrentUseDayHours = 0;
                    }
                    else
                    {
                        CountTheTotalUseEquipmentCategorie.DayUsageRate =  Math.Ceiling((dayUsage.TotalUsageTime / TimeSpan.FromDays(1)) * 100);
                        CountTheTotalUseEquipmentCategorie.CurrentUseDayHours = Math.Ceiling(dayUsage.TotalUsageTime.TotalHours);
                    }
                    
                    CountTheTotalUseEquipmentCategorie.CurrentAvgUseMoonHours = moonDatas.Sum(x=>x.TotalUsageTime.TotalHours)/
                        (lastDayOfMonth - firstDayOfMonth).Days;

                    CountTheTotalUseEquipmentCategorie.CurrentAvgUseMoonHours =
                        Math.Ceiling(CountTheTotalUseEquipmentCategorie.CurrentAvgUseMoonHours);
                    CountTheTotalUseEquipmentCategorie.CurrentAvgUseMoonUseDay = moonDatas.Count(x=>x.TotalUsageTime > TimeSpan.Zero);

                }
                CountTheTotalUseEquipmentCategorie.SmblLabId = equipment.SMBL_LAB_ID;
                CountTheTotalUseEquipmentCategorie.SmblLabName = equipment.SMBL_LAB_NAME;
                CountTheTotalUseEquipmentCategorie.SmblClass = equipment.SMBL_CLASS;
                CountTheTotalUseEquipmentCategorie.EquipmentName = equipment.EQUIPMENT_NAME;
                CountTheTotalUseEquipmentCategorie.IsAccess = true;
            }
            else
            {
                CountTheTotalUseEquipmentCategorie.SmblLabId = equipment.SMBL_LAB_ID;
                CountTheTotalUseEquipmentCategorie.SmblLabName = equipment.SMBL_LAB_NAME;
                CountTheTotalUseEquipmentCategorie.SmblClass = equipment.SMBL_CLASS;
                CountTheTotalUseEquipmentCategorie.EquipmentName = equipment.EQUIPMENT_NAME;
                CountTheTotalUseEquipmentCategorie.DayUsageRate = 0;
                CountTheTotalUseEquipmentCategorie.CurrentUseDayHours = 0;
                CountTheTotalUseEquipmentCategorie.CurrentAvgUseMoonHours = 0;
                CountTheTotalUseEquipmentCategorie.CurrentAvgUseMoonUseDay = 0;
                CountTheTotalUseEquipmentCategorie.IsAccess = false;
            }
            var countTheTotalUseEquipment  = result.FirstOrDefault(x => x.SmblClass == CountTheTotalUseEquipmentCategorie.SmblClass);
            if (countTheTotalUseEquipment is  not null)
            {
                countTheTotalUseEquipment.Categories.Add(CountTheTotalUseEquipmentCategorie);
            }
        }

        foreach (var item in  result)
        {
            
            item.TotalEquipmentCount = equipments.Count(x => x.SMBL_CLASS == item.SmblClass);
            foreach (var categorie in item.Categories)
            {
                if (categorie.IsAccess)
                {
                    item.IsAccess = true;
                }
                if (categorie.CurrentUseDayHours > 0)
                {
                    item.CurrentEquipmentCount += 1;
                }
                item.CurrentUseDayHours += categorie.CurrentUseDayHours;
                item.AvgMoonthUseCount += categorie.CurrentAvgUseMoonUseDay;
                item.AvgMoonthUseHoursCount += categorie.CurrentAvgUseMoonHours;
            } 
            
            item.AvgMoonthUseCount /= item.TotalEquipmentCount;
            item.AvgMoonthUseHoursCount /= item.TotalEquipmentCount;
            item.AvgMoonthUseCount  =  item.AvgMoonthUseCount is Double.NaN  ? 0 :  Math.Ceiling(item.AvgMoonthUseCount) ;
            item.AvgMoonthUseHoursCount  =  item.AvgMoonthUseHoursCount is Double.NaN  ? 0 : Math.Ceiling(item.AvgMoonthUseHoursCount);
        }
        return Ok(result.ToResultDto());

    }

    /// <summary>
    /// 看板提醒事项
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [CustomResponseType(typeof(List<ConutReminderDto>))]
    public IActionResult ConutReminders(string smblLabId)
    {
        return Ok(_workPlanService.ConutReminders(smblLabId).ToResultDto());
    }


    /// <summary>
    /// 获取近两年的需要年检的设备数量
    /// </summary>
    /// <param name="inlet">入口类型  0机构 1科室 2备案实验室</param>
    /// <param name="id">入口的id  机构id 或者科室id 或者备案实验室id</param>
    /// <returns></returns>
    [HttpGet]
    [CustomResponseType(typeof(List<EquipmentYearCheckCountDto>))]
    public IActionResult GetEquipmentYearCheckCount(SmblInletEnum inlet , string id)
    {
        switch (inlet)
        {
            case SmblInletEnum.Hospital:
                return Ok(_workPlanService.GetEquipmentYearCheckCount(id,null).ToResultDto());
            case SmblInletEnum.Lab:
                return Ok(_workPlanService.GetEquipmentYearCheckCount(null,id).ToResultDto());
            case SmblInletEnum.RecordLab:
                return Ok(_workPlanService.GetEquipmentYearCheckCount(null,null).ToResultDto());
            default:
                return Ok(_workPlanService.GetEquipmentYearCheckCount(null,null).ToResultDto());
        }
    }

    /// <summary>
    /// 获取当前 年 的需要年检的设备数量  前端实现每个月的分组计算
    /// </summary>
    /// <param name="year">年份</param>
    /// <param name="inlet">入口类型  0机构 1科室 2备案实验室</param>
    /// <param name="id">入口的id  机构id 或者科室id 或者备案实验室id</param>
    /// <returns></returns>
    [HttpGet]
    [CustomResponseType(typeof(List<ConutEquipmentYearChectByMonth>))]
    public IActionResult GetEquipmentYearCheck(int? year,SmblInletEnum inlet , string id)
    {
        var nowYear = DateTime.Now.Year;
        if (year.HasValue)
        {
            nowYear = year.Value;
        }
        switch (inlet)
        {
            case SmblInletEnum.Hospital:
                return Ok(_workPlanService.GetEquipmentYearCheck(nowYear,id,id).ToResultDto());
            case SmblInletEnum.Lab:
                return Ok(_workPlanService.GetEquipmentYearCheck(nowYear,null,id).ToResultDto());
            case SmblInletEnum.RecordLab:
                return Ok(_workPlanService.GetEquipmentYearCheck(nowYear,null,null).ToResultDto());
            default:
                return Ok(_workPlanService.GetEquipmentYearCheck(nowYear,null,null).ToResultDto());
        }
    }

    /// <summary>
    /// 查询设备大概情况
    /// </summary>
    /// <param name="inlet">入口类型  0机构 1科室 2备案实验室</param>
    /// <param name="id">入口的id  机构id 或者科室id 或者备案实验室id</param>
    /// <returns></returns>
    [HttpGet]
    [CustomResponseType(typeof(EquipmentDistributionDto))]
    public IActionResult GetEquipmentsDistribution(SmblInletEnum  inlet , string id)
    {
        var  hasResult =  _cache.TryGetValue<EquipmentDistributionDto>($"GetEquipmentsDistribution_{inlet}_{id}" , out var result);
        if (hasResult)
        {
            return Ok(result.ToResultDto());
        }
        result = new EquipmentDistributionDto();
        result.AllNumList[0] = new DistributionDto
        {
            Num = 0,
            enumState = DistributionState.all
        };
        
        result.AllNumList[1] = new DistributionDto
        {
            Num = 0,
            enumState = DistributionState.focus
        };

        result.equipmentStateList[0] = new DistributionDto
        {
            Num = 0,
            enumState = DistributionState.active
        };
        result.equipmentStateList[1] = new DistributionDto
        {
            Num = 0,
            enumState = DistributionState.notNabled
        };
        result.equipmentStateList[2] = new DistributionDto
        {
            Num = 0,
            enumState = DistributionState.stopUse
        };
        result.equipmentStateList[3] = new DistributionDto
        {
            Num = 0,
            enumState = DistributionState.scrap
        };
        var equipments = inlet switch
        {
            SmblInletEnum.Hospital =>_deviceService.GetGetEquipmentsDistributionByHospital("smbl", id),
            SmblInletEnum.Lab => _deviceService.GetGetEquipmentsDistributionByLab("smbl",id),
            SmblInletEnum.RecordLab => _deviceService.GetGetEquipmentsDistributionBySmblLab(id),
            _ => throw new ArgumentOutOfRangeException()
        };
        
        result.AllNumList[0].Num = equipments.Count;
        if (inlet is SmblInletEnum.Hospital)
        {
          result.AllNumList[1].Num = _deviceService.GetEquipmentsUseCase(id , null,null).Count;
        }
        if (inlet is SmblInletEnum.Lab) 
        {
            result.AllNumList[1].Num = _deviceService.GetEquipmentsUseCase(null, id,null).Count;
        }
        if (inlet is SmblInletEnum.RecordLab)
        {
            result.AllNumList[1].Num = _deviceService.GetEquipmentsUseCase(null, null, id).Count;
        }
        
        var equipmentStateGroups = equipments.GroupBy(x => x.EQUIPMENT_STATE).OrderBy(x=>x.Key);

        foreach (var equipmentStateGroup in equipmentStateGroups)
        {
            switch (equipmentStateGroup.Key)
            {
                case "0" : 
                    result.equipmentStateList.First(x=>x.enumState is DistributionState.notNabled).Num += equipmentStateGroup.Count();
                    break;
                case "1" : 
                    result.equipmentStateList.First(x=>x.enumState is DistributionState.active).Num += equipmentStateGroup.Count();
                    break;
                case "2" : 
                    result.equipmentStateList.First(x=>x.enumState is DistributionState.stopUse).Num += equipmentStateGroup.Count();
                    break;
                case "3" : 
                    result.equipmentStateList.First(x=>x.enumState is DistributionState.scrap).Num += equipmentStateGroup.Count();
                    break;
            }
        }
        _cache.Set($"GetEquipmentsDistribution_{inlet}_{id}" , result , TimeSpan.FromMinutes(1));
        return Ok(result.ToResultDto());
    }

    /// <summary>
    /// 获取机构层级下的设备使用趋势
    /// </summary>
    /// <param name="hospitalId">机构id</param>
    /// <param name="labId">科室id</param>
    /// <param name="smblLabId">备案实验室id</param>
    /// <param name="smblClass">生安设备类型id</param>
    /// <param name="equipmentName">设备名称</param>
    /// <param name="isSamePeriod">是否同期  默认false</param>
    /// <returns></returns>
    [HttpGet]
    [CustomResponseType(typeof(List<ConutHosptailEquipmentDistributionDto>))]
    public IActionResult ConutHosptailEquipmentDistribution([Required]string hospitalId , string? labId, string? smblLabId ,string? smblClass , string? equipmentName, bool isSamePeriod = false)
    {
        var result = new  List<ConutHosptailEquipmentDistributionDto>();
        for (int i = 1; i <= 12; i++)
        {
            result.Add(new ConutHosptailEquipmentDistributionDto
            {
                Month = i,
                Duration =0
            });
        }
        var date = DateTime.Now.Year;
        var days = _deviceService.GetObtainMonitoringDataByYear(date, hospitalId,labId,smblLabId,smblClass,equipmentName);
        var monitorGroupByEquipments = days.GroupBy(x =>new
        {
             x.MONITOR_DATE!.Value.Month
        });
        foreach (var monitorGroupByEquipment in monitorGroupByEquipments)
        {
           var month =   result.First(x => x.Month == monitorGroupByEquipment.Key.Month);
           month.Duration = (int)Math.Ceiling(monitorGroupByEquipment.Sum(x => x.ACCUM_VALUE)/60.0);
        }
        
        return Ok(result.ToResultDto());
    }

    /// <summary>
    /// 设备待维护按生安设备类型统计
    /// </summary>
    /// <param name="smblLabId">备案实验室</param>
    /// <returns></returns>
    [HttpGet]
    [CustomResponseType(typeof(List<OverviewMaintainedDto>))]
    public IActionResult ConutOverviewMaintained(string smblLabId)
    {
        var result = new List<OverviewMaintainedDto>();
        var equipments = _deviceService.GetSmblToBeMaintainedEquipments(smblLabId);
        var equipmentGroups = equipments.GroupBy(x => new {x.SMBL_CLASS,x.SMBL_CLASS_NAME});
        foreach (var equipmentGroup in equipmentGroups)
        {
            if (equipmentGroup.Key.SMBL_CLASS.IsNullOrEmpty())
            {
                continue;
            }
            var preMaintained = equipmentGroup.Count(x =>
            {
                // 未填写环境要求
                if (x.eMS_ENVI_REQUIRE_INFO is null)
                {
                    return true;
                }
                // 未填写申购信息 商务信息
                if (x.eMS_PURCHASE_INFO is null)
                {
                    return true;
                }
                // 未填写保养记录
                if (x.eMS_MAINTAIN_INFO is null || !x.eMS_MAINTAIN_INFO.Any())
                {
                    return true;
                }
                // 未填写校准记录
                if (x.eMS_CORRECT_INFO is null || !x.eMS_CORRECT_INFO.Any())
                {
                    return true;
                }
                // 未填写比对记录
                if (x.eMS_COMPARISON_INFO is null || !x.eMS_COMPARISON_INFO.Any())
                {
                    return true;
                }
                // 未填写性能验证记录
                if (x.eMS_VERIFICATION_INFO is null || !x.eMS_VERIFICATION_INFO.Any())
                {
                    return true;
                }
                // 未填写安装信息
                if (x.eMS_INSTALL_INFO is null)
                {
                    return true;
                }
                // 未填写授权记录 
                if (x.eMS_AUTHORIZE_INFO is null || !x.eMS_AUTHORIZE_INFO.Any())
                {
                    return true;
                }
                // 未填写培训记录
                if (x.eMS_TRAIN_INFO is null || !x.eMS_TRAIN_INFO.Any())
                {
                    return true;
                }
                // 未填写联系人
                if (x.Contacts is null || !x.Contacts.Any())
                {
                    return true;
                }
                // 未填写其他附件-外观信息
                if (x.Documents is null || !x.Documents.Any(x=>x.DOC_CLASS == "外观信息"))
                {
                    return true;
                }
                // 未填写其他附件-验收报告
                if (x.Documents is null || !x.Documents.Any(x=>x.DOC_CLASS == "验收报告"))
                {
                    return true;
                }
                // 未填写其他附件-SOP档案
                if (x.Documents is null || !x.Documents.Any(x=>x.DOC_CLASS == "SOP档案"))
                {
                    return true;
                }
                // 未填写其他附件-设备说明书
                if (x.Documents is null || !x.Documents.Any(x=>x.DOC_CLASS == "设备说明书"))
                {
                    return true;
                }
                // 未填写其他附件-证书信息
                if (x.CertificateInfos is null || !x.CertificateInfos.Any())
                {
                    return true;
                }
                // 未填写供应商商信息
                if (x.DEALER_ID.IsNullOrEmpty() || x.MANUFACTURER_ID.IsNullOrEmpty())
                {
                    return true;
                }
                return false;
            });
            result.Add(new OverviewMaintainedDto()
            {
                SmblClassName = equipmentGroup.Key.SMBL_CLASS_NAME,
                SmblClass = equipmentGroup.Key.SMBL_CLASS,
                TotalMaintained = equipmentGroup.Count(),
                PreMaintained = preMaintained
            });
        }
        return Ok(result.ToResultDto());
    }
    
    /// <summary>
    /// 设备待维护按具体档案部分统计
    /// </summary>
    /// <param name="smblLabId"></param>
    /// <returns></returns>
    [HttpGet]
    [CustomResponseType(typeof(List<OverviewMaintainedDetailDto>))]
    public IActionResult ConutOverviewMaintainedDetal(string smblLabId)
    {
        var result = new List<OverviewMaintainedDetailDto>();
        var equipments = _deviceService.GetSmblToBeMaintainedEquipments(smblLabId);
        result.Add(new OverviewMaintainedDetailDto()
        {
            MaintainedName = "外观信息",
            MaintainedCount = equipments.Count(x=>x.Documents is null || !x.Documents.Any(x=>x.DOC_CLASS == "外观信息"))
        });
        result.Add(new OverviewMaintainedDetailDto()
        {
            MaintainedName = "供应商",
            MaintainedCount = equipments.Count(x=>x.DEALER_ID.IsNullOrEmpty() || x.MANUFACTURER_ID.IsNullOrEmpty())
        });
        result.Add(new OverviewMaintainedDetailDto()
        {
            MaintainedName = "商务信息",
            MaintainedCount = equipments.Count(x=>x.eMS_PURCHASE_INFO is null)
        });
        result.Add(new OverviewMaintainedDetailDto()
        {
            MaintainedName = "环境信息",
            MaintainedCount = equipments.Count(x=>x.eMS_ENVI_REQUIRE_INFO is null)
        });
        result.Add(new OverviewMaintainedDetailDto()
        {
            MaintainedName = "培训记录",
            MaintainedCount = equipments.Count(x=>x.eMS_TRAIN_INFO is null|| !x.eMS_TRAIN_INFO.Any())
        });
        
        result.Add(new OverviewMaintainedDetailDto()
        {
            MaintainedName = "安装记录",
            MaintainedCount = equipments.Count(x=>x.eMS_INSTALL_INFO is null)
        });
        
        result.Add(new OverviewMaintainedDetailDto()
        {
            MaintainedName = "授权记录",
            MaintainedCount = equipments.Count(x=>x.eMS_AUTHORIZE_INFO is null|| !x.eMS_AUTHORIZE_INFO.Any())
        });
        result.Add(new OverviewMaintainedDetailDto()
        {
            MaintainedName = "性能验证记录",
            MaintainedCount = equipments.Count(x=>x.eMS_VERIFICATION_INFO is null|| !x.eMS_VERIFICATION_INFO.Any())
        });
        
        result.Add(new OverviewMaintainedDetailDto()
        {
            MaintainedName = "验收报告",
            MaintainedCount = equipments.Count(x=>x.Documents is null || !x.Documents.Any(x=>x.DOC_CLASS == "验收报告"))
        });
        result.Add(new OverviewMaintainedDetailDto()
        {
            MaintainedName = "资质证书",
            MaintainedCount = equipments.Count(x=>x.CertificateInfos is null || !x.CertificateInfos.Any())
        });
        result.Add(new OverviewMaintainedDetailDto()
        {
            MaintainedName = "联系方式",
            MaintainedCount = equipments.Count(x=>x.Contacts is null || !x.Contacts.Any())
        });
        
        result.Add(new OverviewMaintainedDetailDto()
        {
            MaintainedName = "SOP档案",
            MaintainedCount = equipments.Count(x=>x.Documents is null || !x.Documents.Any(x=>x.DOC_CLASS == "SOP档案"))
        });
        result.Add(new OverviewMaintainedDetailDto()
        {
            MaintainedName = "校准记录",
            MaintainedCount = equipments.Count(x=>x.eMS_CORRECT_INFO is null || !x.eMS_CORRECT_INFO.Any())
        });
        result.Add(new OverviewMaintainedDetailDto()
        {
            MaintainedName = "保养记录",
            MaintainedCount = equipments.Count(x=>x.eMS_MAINTAIN_INFO is null || !x.eMS_MAINTAIN_INFO.Any())
        });
        result.Add(new OverviewMaintainedDetailDto()
        {
            MaintainedName = "说明书",
            MaintainedCount = equipments.Count(x=>x.Documents is null || !x.Documents.Any(x=>x.DOC_CLASS == "设备说明书"))
        });
        result.Add(new OverviewMaintainedDetailDto()
        {
            MaintainedName = "比对记录",
            MaintainedCount = equipments.Count(x=>x.eMS_COMPARISON_INFO is null || !x.eMS_COMPARISON_INFO.Any())
        });
        
        return Ok(result.ToResultDto());
    }


    /// <summary>
    /// 修改设备的sn码
    /// </summary>
    /// <param name="id"></param>
    /// <param name="sn"></param>
    /// <returns></returns>
    [HttpGet]
    public IActionResult ChangeThsEquipmentSn(string id , string sn)
    {

        _deviceService.ChangeThsEquipmentSn(id, sn);
         return Ok(true.ToResultDto());
    }

    /// <summary>
    /// 修改设备的did
    /// </summary>
    /// <param name="thsEquipmentId"></param>
    /// <param name="emsEquipmentId"></param>
    /// <returns></returns>
    [HttpGet]
    public IActionResult ChangeThsEquipmentDid(string thsEquipmentId, string emsEquipmentId)
    {
        _deviceService.ChangeThsEquipmentDid(thsEquipmentId, emsEquipmentId);
        
        return Ok(true.ToResultDto());
    }
    
    
    /// <summary>
    /// 电信门禁设备列表
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [AllowAnonymous]
    public IActionResult GetAccessControls()
    {
        return Ok(_deviceService.GetAccessControls());
    }

}