﻿using Newtonsoft.Json;

namespace XH.H82.Models.Flow;

public class ApproverProperty
{
    [Json<PERSON>roperty("approverType")]
    public string ApproverType { get; set; }

    [JsonProperty("deptApproveType")]
    public object DeptApproveType { get; set; }

    [JsonProperty("nullApproverAction")]
    public object NullApproverAction { get; set; }

    [JsonProperty("personList")]
    public object PersonList { get; set; }

    [JsonProperty("positionList")]
    public object PositionList { get; set; }
}