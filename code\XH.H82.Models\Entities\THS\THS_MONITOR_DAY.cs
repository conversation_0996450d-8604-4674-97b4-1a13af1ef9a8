﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using H.Utility.SqlSugarInfra;
using SqlSugar;

namespace XH.H82.Models.Entities.THS
{
    /// <summary>
    /// 设备检测点每日监测汇总表
    /// </summary>
    [DBOwner("XH_DATA")]
    [Table("THS_MONITOR_DAY")]
    [SugarTable("THS_MONITOR_DAY")]
    public class THS_MONITOR_DAY
    {
        /// <summary>
        /// 日监测记录ID
        /// </summary>
        [Key]
        [SugarColumn(IsPrimaryKey = true)]
        public string? MONITOR_DAYID { get; set; }
        /// <summary>
        /// 设备ID
        /// </summary>
        public string? EQUIPMENT_ID { get; set; }
        /// <summary>
        /// 监测点ID
        /// </summary>
        public string? POINT_ID { get; set; }
        /// <summary>
        /// 管理单元ID
        /// </summary>
        public string? UNIT_ID { get; set; }
        /// <summary>
        /// 医疗机构ID
        /// </summary>
        public string? HOSPITAL_ID { get; set; }
        /// <summary>
        /// 监测指标ID
        /// </summary>
        public string? ITEM_ID { get; set; }
        /// <summary>
        /// 记录日期
        /// </summary>
        public DateTime? MONITOR_DATE { get; set; }

        public int TOTAL_NUM { get; set; }
        /// <summary>
        /// 监测记录总数
        /// </summary>
        public string? TIME_POINT { get; set; }
        /// <summary>
        /// 正常记录数
        /// </summary>
        public int? NORMAL_NUM { get; set; }
        /// <summary>
        /// 异常记录数
        /// </summary>
        public int? ABNORMAL_NUM { get; set; }
        /// <summary>
        /// 最高值
        /// </summary>
        public string? MAX_VALUE { get; set; }
        /// <summary>
        /// 最低值
        /// </summary>
        public string? MIN_VALUE { get; set; }
        /// <summary>
        /// 平均值
        /// </summary>
        public string? AVG_VALUE { get; set; }

        /// <summary>
        /// 累计值
        /// </summary>
        public int ACCUM_VALUE { get; set; }
        /// <summary>
        /// 报警记录数
        /// </summary>
        public int? ALARM_NUM { get; set; }
        /// <summary>
        /// 处理人员
        /// </summary>
        public string? OPER_PERSON { get; set; }
        /// <summary>
        /// 处理时间
        /// </summary>
        public DateTime? OPER_TIME { get; set; }
        /// <summary>
        /// 处理电脑
        /// </summary>
        public string? OPER_COMPUTER { get; set; }
        /// <summary>
        /// 状态1未确认2已确认
        /// </summary>
        public string? MONITOR_DAY_STATE { get; set; }
        /// <summary>
        /// 首次登记人
        /// </summary>
        public string? FIRST_RPERSON { get; set; }
        /// <summary>
        /// 首次登记时间
        /// </summary>
        public DateTime? FIRST_RTIME { get; set; }
        /// <summary>
        /// 最后修改人员
        /// </summary>
        public string? LAST_MPERSON { get; set; }
        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime? LAST_MTIME { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string? REMARK { get; set; }
    }
}
