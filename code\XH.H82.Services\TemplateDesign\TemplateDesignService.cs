﻿using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using H.Utility.Helper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Npoi.Mapper;
using Serilog;
using XH.H82.Base.Helper;
using XH.H82.IServices;
using XH.H82.IServices.TemplateDesign;
using XH.H82.Models.BusinessModuleClient.H04;
using XH.H82.Models.EquipmengtClassNew;
using XH.H82.Models.SugarDbContext;
using XH.H82.Models.TemplateDesign;
using XH.LAB.UTILS.Models;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace XH.H82.Services.TemplateDesign;

public class TemplateDesignService : ITemplateDesignService
{
    private readonly ISqlSugarUow<SugarDbContext_Master> _dbContext;
    private readonly IConfiguration _configuration;
    private readonly IHttpContextAccessor _httpContext;
    private readonly IBaseDataServices _IBaseDataServices;

    private const string BasicFieldId = "********";
    public TemplateDesignService(ISqlSugarUow<SugarDbContext_Master> dbContext, IConfiguration configuration, IHttpContextAccessor httpContext, IBaseDataServices iBaseDataServices)
    {
      _dbContext = dbContext;
      _configuration = configuration;
      _httpContext = httpContext;
      _IBaseDataServices = iBaseDataServices;
    }

    public  PageSettingForm GetInitBasicField(string setUpId = BasicFieldId)
    {
        var result=  new PageSettingForm();
        result.form = new List<Form>();
        if (setUpId.IsNullOrEmpty())
        {
            List<PropertyInfoModel> propertyInfos = EntityMetadataGenerator.GenerateMetadataJson<Equipment>();
            foreach (var item in propertyInfos)
            {
                PageSettingForm PageSettingForm = new PageSettingForm();
                Form form = new Form();
                form.formCname = item.Description;
                form.formName = item.Description;
                form.formCode = item.PropertyName;
                form.ifShow = false;
                form.titleShow = true;
                form.titleColor = "#000000";
                form.titleSize = 13;
                form.titleStyle = "0";
                form.titleAlign = "3";
                form.contentLine = 1;
                form.contentMaxLine = 1;
                form.contentHeightClass = 1;
                form.contentHeightRatio = "";
                form.contentAlign = "1";
                form.contentFontSize = 13;
                form.contentStyle = "0";
                form.titleAndContentType = "1";
                form.contentEnlarge = false;
                //form.ifRequired = dto.ifRequired;
                form.replaceField = true;
                form.onlyRead = "1";
                form.dataType = item.DbType;
                //form.dataClass = "1";
                form.allowMaintainDropDownData = false;
                form.editeState = false;
                form.unitFlag = false;
                PageSettingForm.form.Add(form);
            }

            return result;
        }
        var formDict = _dbContext.Db.Queryable<SYS6_MODULE_FUNC_DICT>()
            .Where(p => p.SETUP_ID == setUpId)?.First();
        if (formDict is null)
        {
            throw new BizException("请到新工具箱配置复杂表单信息");
        }
        result.form = JsonConvert.DeserializeObject<List<Form>>(formDict.FORM_COL_JSON);
        return result;
    }

    
    /// <summary>
    /// 初始化「基本信息」公共库字段
    /// </summary>
    public ResultDto InitBaseInfoFieldDict()
    {
       var h04Client = new H04Client(_configuration, _httpContext);
        var result = new ResultDto();

        // 1. 取表单元数据
        var form = LoadFormJson("********");
        if (form.formJson == null || form.pageSettingForm == null) return result;
        
        // 2. 取组件属性清单
        var widgetPropsMap = LoadWidgetPropsMap();
        Random random = new Random();
        // 3. 把表单里每一个字段落地到公共库
        foreach (var dto in ExtractFieldDicts(form.formJson, form.pageSettingForm, widgetPropsMap))
        {
          if (dto.FIELD_ID.IsNullOrEmpty())
            dto.FIELD_ID = "H82" + _IBaseDataServices.GetTableMax("SYS6_FUNC_FIELD_DICT", "FIELD_ID", 1, 1, "XH_SYS").data.ToString().PadLeft(6, '0');
          if (dto.FIELD_CODE.IsNullOrEmpty() && dto.FIELD_NAME.IsNotNullOrEmpty())
          {
            dto.FIELD_CODE = new SpellAndWbCodeTookit().GetSpellCode(dto.FIELD_NAME) + random.Next(10000, 99999);
          }
          h04Client.SaveFieldDict(dto);
        }
        return result;
    }

    
    public bool CreateComplexForms(string classId , string layoutId = BasicFieldId)
    {
      var user = _httpContext.HttpContext.User.ToClaimsDto();
      var  classDict =  _dbContext.Db.Queryable<EMS_EQUIPMENT_CLASS_DICT>().First(x => x.ClassId == classId);
      if (classDict  is null)
      {
        return false;
      }
      var tagTemplateDto = new TagTemplateDto();
      tagTemplateDto.SETUP_ID = $"H82_{classDict.ClassId}";
      tagTemplateDto.FIELD_CLASS = "基本信息";
      tagTemplateDto.FUNC_ID = "H8253"; //设备档案信息页面id
      tagTemplateDto.HOSPITAL_ID = user.HOSPITAL_ID;
      tagTemplateDto.MODULE_ID = "H82";
      tagTemplateDto.SETUP_NAME = $"{classDict.ClassName}设备类型表单";
      tagTemplateDto.SETUP_CNAME = $"{classDict.ClassName}设备类型表单";
      tagTemplateDto.SETUP_SORT = classDict.ClassLevel;
      tagTemplateDto.SETUP_STATE = "1";
      tagTemplateDto.LAYOUT_ID = layoutId;
      try
      {
        var h04Client = new H04Client(_configuration, _httpContext);
        h04Client.ClearCache();
        h04Client.CreateComplexForms(tagTemplateDto);
      }
      catch (Exception ex)
      {
        return false;
      }

      return true;
    }

    public ComplexFormDto? GetComplexForms(string setUpId, string merge, string? qunitId)
    {
      var complexForms = _dbContext.Db.Queryable<SYS6_MODULE_FUNC_DICT>()
        .Where(x => x.SETUP_ID == $"H82_{setUpId}")
        .First();
      if (complexForms is null)
      {
        return null;
      }
      
      try
      {
        var h04Client = new H04Client(_configuration, _httpContext);
        var  result  = h04Client.GetComplexForms($"H82_{setUpId}", merge,qunitId);
        return result ;
      }
      catch (Exception ex)
      {
        Log.Error(ex.Message);
        return null;
      }
    }

    public List<object> GetFieldDicts(string filedClass)
    {
      var result= Array.Empty<object>().ToList();
      try
      {
        var h04Client = new H04Client(_configuration, _httpContext);
        var dict =  h04Client.GetFieldDicts(filedClass);
        result.AddRange(dict);
      }
      catch (BizException e)
      {
        Log.Error(e.Message);
      }
      return result;
    }

    public bool SaveFieldDic(FieldDictDto input)
    {
      var random = new Random();
      if (input.FIELD_ID.IsNullOrEmpty())
        input.FIELD_ID = "H82" + _IBaseDataServices.GetTableMax("SYS6_FUNC_FIELD_DICT", "FIELD_ID", 1, 1, "XH_SYS").data.ToString().PadLeft(6, '0');
      if (input.FIELD_CODE.IsNullOrEmpty() && input.FIELD_NAME.IsNotNullOrEmpty())
      {
        input.FIELD_CODE = new SpellAndWbCodeTookit().GetSpellCode(input.FIELD_NAME) + random.Next(10000, 99999);
      }
      var h04Client = new H04Client(_configuration, _httpContext);
      h04Client.SaveFieldDict( input);
      return true;
    }



    public ResultDto InsertPersonInfoSetting()
    {
        ResultDto resultDto = new ResultDto();
        SYS6_MODULE_FUNC_DICT formDict = _dbContext.Db.Queryable<SYS6_MODULE_FUNC_DICT>()
            .Where(p => p.SETUP_ID == "********")?.First();
        if (formDict != null)
        {
            // List<PropertyInfoModel> propertyInfos = EntityMetadataGenerator.GenerateMetadataJson<Equipment>();
            // PageSettingForm PageSettingForm = new PageSettingForm();
            // PageSettingForm.form = new List<Form>();
            // foreach (var item in propertyInfos)
            // {
            //     Form form = new Form();
            //     form.formCname = item.Description;
            //     form.formName = item.Description;
            //     form.formCode = item.PropertyName;
            //     form.ifShow = false;
            //     form.titleShow = true;
            //     form.titleColor = "#000000";
            //     form.titleSize = 13;
            //     form.titleStyle = "0";
            //     form.titleAlign = "3";
            //     form.contentLine = 1;
            //     form.contentMaxLine = 1;
            //     form.contentHeightClass = 1;
            //     form.contentHeightRatio = "";
            //     form.contentAlign = "1";
            //     form.contentFontSize = 13;
            //     form.contentStyle = "0";
            //     form.titleAndContentType = "1";
            //     form.contentEnlarge = false;
            //     //form.ifRequired = dto.ifRequired;
            //     form.replaceField = true;
            //     form.onlyRead = "1";
            //     form.dataType = item.DbType;
            //     //form.dataClass = "1";
            //     form.allowMaintainDropDownData = false;
            //     form.editeState = false;
            //     form.unitFlag = false;
            //     PageSettingForm.form.Add(form);
            // }
            var json = @"{""form"":[{""formName"":""所属专业组"",""formCname"":""所属专业组"",""formCode"":""UNIT_ID"",""ifNew"":null,""ifShow"":false,""titleShow"":true,""titleColor"":""#000000"",""titleSize"":13,""titleStyle"":""0"",""titleBackground"":null,""titleAlign"":""3"",""contentLine"":1,""contentMaxLine"":1,""contentHeightClass"":1,""contentHeightRatio"":"""",""contentAlign"":""1"",""contentColor"":null,""contentFontSize"":13,""contentStyle"":""0"",""contentBackground"":null,""titleAndContentType"":""1"",""contentEnlarge"":false,""ifRequired"":null,""replaceField"":true,""onlyRead"":""1"",""default"":null,""resetContent"":null,""dataType"":""1"",""dataClass"":null,""allowMaintainDropDownData"":false,""formDesc"":null,""suffix"":null,""sort"":null,""editeState"":false,""unitFlag"":false,""styleJson"":{""formName"":""所属专业组"",""dataClass"":""检验专业组"",""dataType"":""Select"",""labelHide"":false,""labelStyle"":{""color"":""#000000"",""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""right"",""justifyContent"":""right""},""rules"":[{""required"":null}],""widgetProps"":{""disabled"":false,""style"":{""color"":null,""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""left"",""justifyContent"":""left""},""defaultValue"":null}}},{""formName"":""所属医疗机构"",""formCname"":""所属医疗机构"",""formCode"":""HOSPITAL_ID"",""ifNew"":null,""ifShow"":false,""titleShow"":true,""titleColor"":""#000000"",""titleSize"":13,""titleStyle"":""0"",""titleBackground"":null,""titleAlign"":""3"",""contentLine"":1,""contentMaxLine"":1,""contentHeightClass"":1,""contentHeightRatio"":"""",""contentAlign"":""1"",""contentColor"":null,""contentFontSize"":13,""contentStyle"":""0"",""contentBackground"":null,""titleAndContentType"":""1"",""contentEnlarge"":false,""ifRequired"":null,""replaceField"":true,""onlyRead"":""1"",""default"":null,""resetContent"":null,""dataType"":""1"",""dataClass"":null,""allowMaintainDropDownData"":false,""formDesc"":null,""suffix"":null,""sort"":null,""editeState"":false,""unitFlag"":false,""styleJson"":{""formName"":""所属医疗机构"",""dataType"":""Input"",""labelHide"":false,""labelStyle"":{""color"":""#000000"",""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""right"",""justifyContent"":""right""},""rules"":[{""required"":null}],""widgetProps"":{""disabled"":true,""style"":{""color"":null,""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""left"",""justifyContent"":""left""}}}},{""formName"":""所属科室"",""formCname"":""所属科室"",""formCode"":""LAB_ID"",""ifNew"":null,""ifShow"":false,""titleShow"":true,""titleColor"":""#000000"",""titleSize"":13,""titleStyle"":""0"",""titleBackground"":null,""titleAlign"":""3"",""contentLine"":1,""contentMaxLine"":1,""contentHeightClass"":1,""contentHeightRatio"":"""",""contentAlign"":""1"",""contentColor"":null,""contentFontSize"":13,""contentStyle"":""0"",""contentBackground"":null,""titleAndContentType"":""1"",""contentEnlarge"":false,""ifRequired"":null,""replaceField"":true,""onlyRead"":""1"",""default"":null,""resetContent"":null,""dataType"":""1"",""dataClass"":null,""allowMaintainDropDownData"":false,""formDesc"":null,""suffix"":null,""sort"":null,""editeState"":false,""unitFlag"":false,""styleJson"":{""formName"":""所属科室"",""dataType"":""Input"",""labelHide"":false,""labelStyle"":{""color"":""#000000"",""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""right"",""justifyContent"":""right""},""rules"":[{""required"":null}],""widgetProps"":{""disabled"":true,""style"":{""color"":null,""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""left"",""justifyContent"":""left""}}}},{""formName"":""专业分类"",""formCname"":""专业分类"",""formCode"":""PROFESSIONAL_CLASS"",""ifNew"":null,""ifShow"":false,""titleShow"":true,""titleColor"":""#000000"",""titleSize"":13,""titleStyle"":""0"",""titleBackground"":null,""titleAlign"":""3"",""contentLine"":1,""contentMaxLine"":1,""contentHeightClass"":1,""contentHeightRatio"":"""",""contentAlign"":""1"",""contentColor"":null,""contentFontSize"":13,""contentStyle"":""0"",""contentBackground"":null,""titleAndContentType"":""1"",""contentEnlarge"":false,""ifRequired"":null,""replaceField"":true,""onlyRead"":""1"",""default"":null,""resetContent"":null,""dataType"":""1"",""dataClass"":null,""allowMaintainDropDownData"":false,""formDesc"":null,""suffix"":null,""sort"":null,""editeState"":false,""unitFlag"":false,""styleJson"":{""formName"":""专业分类"",""dataClass"":""设备分类"",""dataType"":""Select"",""labelHide"":false,""labelStyle"":{""color"":""#000000"",""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""right"",""justifyContent"":""right""},""rules"":[{""required"":null}],""widgetProps"":{""disabled"":false,""style"":{""color"":null,""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""left"",""justifyContent"":""left""},""defaultValue"":null}}},{""formName"":""设备序号"",""formCname"":""设备序号"",""formCode"":""EQUIPMENT_NUM"",""ifNew"":null,""ifShow"":false,""titleShow"":true,""titleColor"":""#000000"",""titleSize"":13,""titleStyle"":""0"",""titleBackground"":null,""titleAlign"":""3"",""contentLine"":1,""contentMaxLine"":1,""contentHeightClass"":1,""contentHeightRatio"":"""",""contentAlign"":""1"",""contentColor"":null,""contentFontSize"":13,""contentStyle"":""0"",""contentBackground"":null,""titleAndContentType"":""1"",""contentEnlarge"":false,""ifRequired"":null,""replaceField"":true,""onlyRead"":""1"",""default"":null,""resetContent"":null,""dataType"":""1"",""dataClass"":null,""allowMaintainDropDownData"":false,""formDesc"":null,""suffix"":null,""sort"":null,""editeState"":false,""unitFlag"":false,""styleJson"":{""formName"":""设备序号"",""dataType"":""Input"",""labelHide"":false,""labelStyle"":{""color"":""#000000"",""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""right"",""justifyContent"":""right""},""rules"":[{""required"":null}],""widgetProps"":{""disabled"":false,""style"":{""color"":null,""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""left"",""justifyContent"":""left""}}}},{""formName"":""设备名称"",""formCname"":""设备名称"",""formCode"":""EQUIPMENT_NAME"",""ifNew"":null,""ifShow"":false,""titleShow"":true,""titleColor"":""#000000"",""titleSize"":13,""titleStyle"":""0"",""titleBackground"":null,""titleAlign"":""3"",""contentLine"":1,""contentMaxLine"":1,""contentHeightClass"":1,""contentHeightRatio"":"""",""contentAlign"":""1"",""contentColor"":null,""contentFontSize"":13,""contentStyle"":""0"",""contentBackground"":null,""titleAndContentType"":""1"",""contentEnlarge"":false,""ifRequired"":null,""replaceField"":true,""onlyRead"":""1"",""default"":null,""resetContent"":null,""dataType"":""1"",""dataClass"":null,""allowMaintainDropDownData"":false,""formDesc"":null,""suffix"":null,""sort"":null,""editeState"":false,""unitFlag"":false,""styleJson"":{""formName"":""设备名称"",""dataType"":""Input"",""labelHide"":false,""labelStyle"":{""color"":""#000000"",""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""right"",""justifyContent"":""right""},""rules"":[{""required"":null}],""widgetProps"":{""disabled"":false,""style"":{""color"":null,""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""left"",""justifyContent"":""left""}}}},{""formName"":""科室设备编号"",""formCname"":""科室设备编号"",""formCode"":""DEPT_SECTION_NO"",""ifNew"":null,""ifShow"":false,""titleShow"":true,""titleColor"":""#000000"",""titleSize"":13,""titleStyle"":""0"",""titleBackground"":null,""titleAlign"":""3"",""contentLine"":1,""contentMaxLine"":1,""contentHeightClass"":1,""contentHeightRatio"":"""",""contentAlign"":""1"",""contentColor"":null,""contentFontSize"":13,""contentStyle"":""0"",""contentBackground"":null,""titleAndContentType"":""1"",""contentEnlarge"":false,""ifRequired"":null,""replaceField"":true,""onlyRead"":""1"",""default"":null,""resetContent"":null,""dataType"":""1"",""dataClass"":null,""allowMaintainDropDownData"":false,""formDesc"":null,""suffix"":null,""sort"":null,""editeState"":false,""unitFlag"":false,""styleJson"":{""formName"":""科室设备编号"",""dataType"":""Input"",""labelHide"":false,""labelStyle"":{""color"":""#000000"",""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""right"",""justifyContent"":""right""},""rules"":[{""required"":null}],""widgetProps"":{""disabled"":false,""style"":{""color"":null,""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""left"",""justifyContent"":""left""}}}},{""formName"":""设备英文名称"",""formCname"":""设备英文名称"",""formCode"":""EQUIPMENT_ENAME"",""ifNew"":null,""ifShow"":false,""titleShow"":true,""titleColor"":""#000000"",""titleSize"":13,""titleStyle"":""0"",""titleBackground"":null,""titleAlign"":""3"",""contentLine"":1,""contentMaxLine"":1,""contentHeightClass"":1,""contentHeightRatio"":"""",""contentAlign"":""1"",""contentColor"":null,""contentFontSize"":13,""contentStyle"":""0"",""contentBackground"":null,""titleAndContentType"":""1"",""contentEnlarge"":false,""ifRequired"":null,""replaceField"":true,""onlyRead"":""1"",""default"":null,""resetContent"":null,""dataType"":""1"",""dataClass"":null,""allowMaintainDropDownData"":false,""formDesc"":null,""suffix"":null,""sort"":null,""editeState"":false,""unitFlag"":false,""styleJson"":{""formName"":""设备英文名称"",""dataType"":""Input"",""labelHide"":false,""labelStyle"":{""color"":""#000000"",""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""right"",""justifyContent"":""right""},""rules"":[{""required"":null}],""widgetProps"":{""disabled"":false,""style"":{""color"":null,""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""left"",""justifyContent"":""left""}}}},{""formName"":""设备型号"",""formCname"":""设备型号"",""formCode"":""EQUIPMENT_MODEL"",""ifNew"":null,""ifShow"":false,""titleShow"":true,""titleColor"":""#000000"",""titleSize"":13,""titleStyle"":""0"",""titleBackground"":null,""titleAlign"":""3"",""contentLine"":1,""contentMaxLine"":1,""contentHeightClass"":1,""contentHeightRatio"":"""",""contentAlign"":""1"",""contentColor"":null,""contentFontSize"":13,""contentStyle"":""0"",""contentBackground"":null,""titleAndContentType"":""1"",""contentEnlarge"":false,""ifRequired"":null,""replaceField"":true,""onlyRead"":""1"",""default"":null,""resetContent"":null,""dataType"":""1"",""dataClass"":null,""allowMaintainDropDownData"":false,""formDesc"":null,""suffix"":null,""sort"":null,""editeState"":false,""unitFlag"":false,""styleJson"":{""formName"":""设备型号"",""dataType"":""Input"",""labelHide"":false,""labelStyle"":{""color"":""#000000"",""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""right"",""justifyContent"":""right""},""rules"":[{""required"":null}],""widgetProps"":{""disabled"":false,""style"":{""color"":null,""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""left"",""justifyContent"":""left""}}}},{""formName"":""所属流水线"",""formCname"":""所属流水线"",""formCode"":""VEST_PIPELINE"",""ifNew"":null,""ifShow"":false,""titleShow"":true,""titleColor"":""#000000"",""titleSize"":13,""titleStyle"":""0"",""titleBackground"":null,""titleAlign"":""3"",""contentLine"":1,""contentMaxLine"":1,""contentHeightClass"":1,""contentHeightRatio"":"""",""contentAlign"":""1"",""contentColor"":null,""contentFontSize"":13,""contentStyle"":""0"",""contentBackground"":null,""titleAndContentType"":""1"",""contentEnlarge"":false,""ifRequired"":null,""replaceField"":true,""onlyRead"":""1"",""default"":null,""resetContent"":null,""dataType"":""1"",""dataClass"":null,""allowMaintainDropDownData"":false,""formDesc"":null,""suffix"":null,""sort"":null,""editeState"":false,""unitFlag"":false,""styleJson"":{""formName"":""所属流水线"",""dataType"":""Input"",""labelHide"":false,""labelStyle"":{""color"":""#000000"",""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""right"",""justifyContent"":""right""},""rules"":[{""required"":null}],""widgetProps"":{""disabled"":false,""style"":{""color"":null,""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""left"",""justifyContent"":""left""}}}},{""formName"":""设备类型"",""formCname"":""设备类型"",""formCode"":""EQUIPMENT_CLASS"",""ifNew"":null,""ifShow"":false,""titleShow"":true,""titleColor"":""#000000"",""titleSize"":13,""titleStyle"":""0"",""titleBackground"":null,""titleAlign"":""3"",""contentLine"":1,""contentMaxLine"":1,""contentHeightClass"":1,""contentHeightRatio"":"""",""contentAlign"":""1"",""contentColor"":null,""contentFontSize"":13,""contentStyle"":""0"",""contentBackground"":null,""titleAndContentType"":""1"",""contentEnlarge"":false,""ifRequired"":null,""replaceField"":true,""onlyRead"":""1"",""default"":null,""resetContent"":null,""dataType"":""1"",""dataClass"":null,""allowMaintainDropDownData"":false,""formDesc"":null,""suffix"":null,""sort"":null,""editeState"":false,""unitFlag"":false,""styleJson"":{""formName"":""设备类型"",""dataClass"":""设备类型"",""dataType"":""Select"",""labelHide"":false,""labelStyle"":{""color"":""#000000"",""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""right"",""justifyContent"":""right""},""rules"":[{""required"":null}],""widgetProps"":{""disabled"":false,""style"":{""color"":null,""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""left"",""justifyContent"":""left""},""defaultValue"":null,""options"":[],""mode"":""""}}},{""formName"":""医院设备编号"",""formCname"":""医院设备编号"",""formCode"":""SECTION_NO"",""ifNew"":null,""ifShow"":false,""titleShow"":true,""titleColor"":""#000000"",""titleSize"":13,""titleStyle"":""0"",""titleBackground"":null,""titleAlign"":""3"",""contentLine"":1,""contentMaxLine"":1,""contentHeightClass"":1,""contentHeightRatio"":"""",""contentAlign"":""1"",""contentColor"":null,""contentFontSize"":13,""contentStyle"":""0"",""contentBackground"":null,""titleAndContentType"":""1"",""contentEnlarge"":false,""ifRequired"":null,""replaceField"":true,""onlyRead"":""1"",""default"":null,""resetContent"":null,""dataType"":""1"",""dataClass"":null,""allowMaintainDropDownData"":false,""formDesc"":null,""suffix"":null,""sort"":null,""editeState"":false,""unitFlag"":false,""styleJson"":{""formName"":""医院设备编号"",""dataType"":""Input"",""labelHide"":false,""labelStyle"":{""color"":""#000000"",""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""right"",""justifyContent"":""right""},""rules"":[{""required"":null}],""widgetProps"":{""disabled"":false,""style"":{""color"":null,""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""left"",""justifyContent"":""left""}}}},{""formName"":""设备价格"",""formCname"":""设备价格"",""formCode"":""SELL_PRICE"",""ifNew"":null,""ifShow"":false,""titleShow"":true,""titleColor"":""#000000"",""titleSize"":13,""titleStyle"":""0"",""titleBackground"":null,""titleAlign"":""3"",""contentLine"":1,""contentMaxLine"":1,""contentHeightClass"":1,""contentHeightRatio"":"""",""contentAlign"":""1"",""contentColor"":null,""contentFontSize"":13,""contentStyle"":""0"",""contentBackground"":null,""titleAndContentType"":""1"",""contentEnlarge"":false,""ifRequired"":null,""replaceField"":true,""onlyRead"":""1"",""default"":null,""resetContent"":null,""dataType"":""1"",""dataClass"":null,""allowMaintainDropDownData"":false,""formDesc"":null,""suffix"":null,""sort"":null,""editeState"":false,""unitFlag"":false,""styleJson"":{""formName"":""设备价格"",""dataType"":""InputNumber"",""labelHide"":false,""labelStyle"":{""color"":""#000000"",""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""right"",""justifyContent"":""right""},""rules"":[{""required"":null}],""widgetProps"":{""disabled"":false,""style"":{""color"":null,""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""left"",""justifyContent"":""left""},""defaultValue"":null}}},{""formName"":""设备责任人"",""formCname"":""设备责任人"",""formCode"":""KEEP_PERSON"",""ifNew"":null,""ifShow"":false,""titleShow"":true,""titleColor"":""#000000"",""titleSize"":13,""titleStyle"":""0"",""titleBackground"":null,""titleAlign"":""3"",""contentLine"":1,""contentMaxLine"":1,""contentHeightClass"":1,""contentHeightRatio"":"""",""contentAlign"":""1"",""contentColor"":null,""contentFontSize"":13,""contentStyle"":""0"",""contentBackground"":null,""titleAndContentType"":""1"",""contentEnlarge"":false,""ifRequired"":null,""replaceField"":true,""onlyRead"":""1"",""default"":null,""resetContent"":null,""dataType"":""1"",""dataClass"":null,""allowMaintainDropDownData"":false,""formDesc"":null,""suffix"":null,""sort"":null,""editeState"":false,""unitFlag"":false,""styleJson"":{""formName"":""设备责任人"",""dataClass"":""系统用户"",""dataType"":""Select"",""labelHide"":false,""labelStyle"":{""color"":""#000000"",""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""right"",""justifyContent"":""right""},""rules"":[{""required"":null}],""widgetProps"":{""disabled"":false,""style"":{""color"":null,""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""left"",""justifyContent"":""left""},""defaultValue"":null}}},{""formName"":""安装日期"",""formCname"":""安装日期"",""formCode"":""INSTALL_DATE"",""ifNew"":null,""ifShow"":false,""titleShow"":true,""titleColor"":""#000000"",""titleSize"":13,""titleStyle"":""0"",""titleBackground"":null,""titleAlign"":""3"",""contentLine"":1,""contentMaxLine"":1,""contentHeightClass"":1,""contentHeightRatio"":"""",""contentAlign"":""1"",""contentColor"":null,""contentFontSize"":13,""contentStyle"":""0"",""contentBackground"":null,""titleAndContentType"":""1"",""contentEnlarge"":false,""ifRequired"":null,""replaceField"":true,""onlyRead"":""1"",""default"":null,""resetContent"":null,""dataType"":""1"",""dataClass"":null,""allowMaintainDropDownData"":false,""formDesc"":null,""suffix"":null,""sort"":null,""editeState"":false,""unitFlag"":false,""styleJson"":{""formName"":""安装日期"",""dataType"":""DatePicker"",""labelHide"":false,""labelStyle"":{""color"":""#000000"",""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""right"",""justifyContent"":""right""},""rules"":[{""required"":null}],""widgetProps"":{""disabled"":false,""style"":{""color"":null,""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""left"",""justifyContent"":""left""},""defaultValue"":null}}},{""formName"":""安装位置"",""formCname"":""安装位置"",""formCode"":""INSTALL_AREA"",""ifNew"":null,""ifShow"":false,""titleShow"":true,""titleColor"":""#000000"",""titleSize"":13,""titleStyle"":""0"",""titleBackground"":null,""titleAlign"":""3"",""contentLine"":1,""contentMaxLine"":1,""contentHeightClass"":1,""contentHeightRatio"":"""",""contentAlign"":""1"",""contentColor"":null,""contentFontSize"":13,""contentStyle"":""0"",""contentBackground"":null,""titleAndContentType"":""1"",""contentEnlarge"":false,""ifRequired"":null,""replaceField"":true,""onlyRead"":""1"",""default"":null,""resetContent"":null,""dataType"":""1"",""dataClass"":null,""allowMaintainDropDownData"":false,""formDesc"":null,""suffix"":null,""sort"":null,""editeState"":false,""unitFlag"":false,""styleJson"":{""formName"":""安装位置"",""dataClass"":""检验科室"",""dataType"":""Select"",""labelHide"":false,""labelStyle"":{""color"":""#000000"",""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""right"",""justifyContent"":""right""},""rules"":[{""required"":null}],""widgetProps"":{""disabled"":false,""style"":{""color"":null,""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""left"",""justifyContent"":""left""},""defaultValue"":null}}},{""formName"":""折旧年限"",""formCname"":""折旧年限"",""formCode"":""DEPRECIATION_TIME"",""ifNew"":null,""ifShow"":false,""titleShow"":true,""titleColor"":""#000000"",""titleSize"":13,""titleStyle"":""0"",""titleBackground"":null,""titleAlign"":""3"",""contentLine"":1,""contentMaxLine"":1,""contentHeightClass"":1,""contentHeightRatio"":"""",""contentAlign"":""1"",""contentColor"":null,""contentFontSize"":13,""contentStyle"":""0"",""contentBackground"":null,""titleAndContentType"":""1"",""contentEnlarge"":false,""ifRequired"":null,""replaceField"":true,""onlyRead"":""1"",""default"":null,""resetContent"":null,""dataType"":""1"",""dataClass"":null,""allowMaintainDropDownData"":false,""formDesc"":null,""suffix"":null,""sort"":null,""editeState"":false,""unitFlag"":false,""styleJson"":{""formName"":""折旧年限"",""dataType"":""InputNumber"",""labelHide"":false,""labelStyle"":{""color"":""#000000"",""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""right"",""justifyContent"":""right""},""rules"":[{""required"":null}],""widgetProps"":{""disabled"":false,""style"":{""color"":null,""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""left"",""justifyContent"":""left""},""defaultValue"":null}}},{""formName"":""联系方式"",""formCname"":""联系方式"",""formCode"":""CONTACT_PHONE"",""ifNew"":null,""ifShow"":false,""titleShow"":true,""titleColor"":""#000000"",""titleSize"":13,""titleStyle"":""0"",""titleBackground"":null,""titleAlign"":""3"",""contentLine"":1,""contentMaxLine"":1,""contentHeightClass"":1,""contentHeightRatio"":"""",""contentAlign"":""1"",""contentColor"":null,""contentFontSize"":13,""contentStyle"":""0"",""contentBackground"":null,""titleAndContentType"":""1"",""contentEnlarge"":false,""ifRequired"":null,""replaceField"":true,""onlyRead"":""1"",""default"":null,""resetContent"":null,""dataType"":""1"",""dataClass"":null,""allowMaintainDropDownData"":false,""formDesc"":null,""suffix"":null,""sort"":null,""editeState"":false,""unitFlag"":false,""styleJson"":{""formName"":""联系方式"",""dataType"":""Input"",""labelHide"":false,""labelStyle"":{""color"":""#000000"",""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""right"",""justifyContent"":""right""},""rules"":[{""required"":null}],""widgetProps"":{""disabled"":false,""style"":{""color"":null,""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""left"",""justifyContent"":""left""}}}},{""formName"":""注册证号"",""formCname"":""注册证号"",""formCode"":""REGISTRATION_NUM"",""ifNew"":null,""ifShow"":false,""titleShow"":true,""titleColor"":""#000000"",""titleSize"":13,""titleStyle"":""0"",""titleBackground"":null,""titleAlign"":""3"",""contentLine"":1,""contentMaxLine"":1,""contentHeightClass"":1,""contentHeightRatio"":"""",""contentAlign"":""1"",""contentColor"":null,""contentFontSize"":13,""contentStyle"":""0"",""contentBackground"":null,""titleAndContentType"":""1"",""contentEnlarge"":false,""ifRequired"":null,""replaceField"":true,""onlyRead"":""1"",""default"":null,""resetContent"":null,""dataType"":""1"",""dataClass"":null,""allowMaintainDropDownData"":false,""formDesc"":null,""suffix"":null,""sort"":null,""editeState"":false,""unitFlag"":false,""styleJson"":{""formName"":""注册证号"",""dataType"":""Input"",""labelHide"":false,""labelStyle"":{""color"":""#000000"",""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""right"",""justifyContent"":""right""},""rules"":[{""required"":null}],""widgetProps"":{""disabled"":false,""style"":{""color"":null,""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""left"",""justifyContent"":""left""}}}},{""formName"":""英文注册证号"",""formCname"":""英文注册证号"",""formCode"":""REGISTRATION_ENUM"",""ifNew"":null,""ifShow"":false,""titleShow"":true,""titleColor"":""#000000"",""titleSize"":13,""titleStyle"":""0"",""titleBackground"":null,""titleAlign"":""3"",""contentLine"":1,""contentMaxLine"":1,""contentHeightClass"":1,""contentHeightRatio"":"""",""contentAlign"":""1"",""contentColor"":null,""contentFontSize"":13,""contentStyle"":""0"",""contentBackground"":null,""titleAndContentType"":""1"",""contentEnlarge"":false,""ifRequired"":null,""replaceField"":true,""onlyRead"":""1"",""default"":null,""resetContent"":null,""dataType"":""1"",""dataClass"":null,""allowMaintainDropDownData"":false,""formDesc"":null,""suffix"":null,""sort"":null,""editeState"":false,""unitFlag"":false,""styleJson"":{""formName"":""英文注册证号"",""dataType"":""Input"",""labelHide"":false,""labelStyle"":{""color"":""#000000"",""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""right"",""justifyContent"":""right""},""rules"":[{""required"":null}],""widgetProps"":{""disabled"":false,""style"":{""color"":null,""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""left"",""justifyContent"":""left""}}}},{""formName"":""设备状态"",""formCname"":""设备状态"",""formCode"":""EQUIPMENT_STATE"",""ifNew"":null,""ifShow"":false,""titleShow"":true,""titleColor"":""#000000"",""titleSize"":13,""titleStyle"":""0"",""titleBackground"":null,""titleAlign"":""3"",""contentLine"":1,""contentMaxLine"":1,""contentHeightClass"":1,""contentHeightRatio"":"""",""contentAlign"":""1"",""contentColor"":null,""contentFontSize"":13,""contentStyle"":""0"",""contentBackground"":null,""titleAndContentType"":""1"",""contentEnlarge"":false,""ifRequired"":null,""replaceField"":true,""onlyRead"":""1"",""default"":null,""resetContent"":null,""dataType"":""1"",""dataClass"":null,""allowMaintainDropDownData"":false,""formDesc"":null,""suffix"":null,""sort"":null,""editeState"":false,""unitFlag"":false,""styleJson"":{""formName"":""设备状态"",""dataType"":""Select"",""labelHide"":false,""labelStyle"":{""color"":""#000000"",""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""right"",""justifyContent"":""right""},""rules"":[{""required"":null}],""widgetProps"":{""disabled"":false,""style"":{""color"":null,""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""left"",""justifyContent"":""left""},""defaultValue"":null,""options"":[{""label"":""合格"",""value"":""1""},{""label"":""不合格"",""value"":""0""}]}}},{""formName"":""备注"",""formCname"":""备注"",""formCode"":""REMARK"",""ifNew"":null,""ifShow"":false,""titleShow"":true,""titleColor"":""#000000"",""titleSize"":13,""titleStyle"":""0"",""titleBackground"":null,""titleAlign"":""3"",""contentLine"":1,""contentMaxLine"":1,""contentHeightClass"":1,""contentHeightRatio"":"""",""contentAlign"":""1"",""contentColor"":null,""contentFontSize"":13,""contentStyle"":""0"",""contentBackground"":null,""titleAndContentType"":""1"",""contentEnlarge"":false,""ifRequired"":null,""replaceField"":true,""onlyRead"":""1"",""default"":null,""resetContent"":null,""dataType"":""1"",""dataClass"":null,""allowMaintainDropDownData"":false,""formDesc"":null,""suffix"":null,""sort"":null,""editeState"":false,""unitFlag"":false,""styleJson"":{""formName"":""备注"",""dataType"":""Input"",""labelHide"":false,""labelStyle"":{""color"":""#000000"",""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""right"",""justifyContent"":""right""},""rules"":[{""required"":null}],""widgetProps"":{""disabled"":false,""style"":{""color"":null,""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""left"",""justifyContent"":""left""}}}},{""formName"":""到货日期"",""formCname"":""到货日期"",""formCode"":""EQ_IN_TIME"",""ifNew"":null,""ifShow"":false,""titleShow"":true,""titleColor"":""#000000"",""titleSize"":13,""titleStyle"":""0"",""titleBackground"":null,""titleAlign"":""3"",""contentLine"":1,""contentMaxLine"":1,""contentHeightClass"":1,""contentHeightRatio"":"""",""contentAlign"":""1"",""contentColor"":null,""contentFontSize"":13,""contentStyle"":""0"",""contentBackground"":null,""titleAndContentType"":""1"",""contentEnlarge"":false,""ifRequired"":null,""replaceField"":true,""onlyRead"":""1"",""default"":null,""resetContent"":null,""dataType"":""1"",""dataClass"":null,""allowMaintainDropDownData"":false,""formDesc"":null,""suffix"":null,""sort"":null,""editeState"":false,""unitFlag"":false,""styleJson"":{""formName"":""到货日期"",""dataType"":""DatePicker"",""labelHide"":false,""labelStyle"":{""color"":""#000000"",""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""right"",""justifyContent"":""right""},""rules"":[{""required"":null}],""widgetProps"":{""disabled"":false,""style"":{""color"":null,""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""left"",""justifyContent"":""left""},""defaultValue"":null}}},{""formName"":""出厂日期"",""formCname"":""出厂日期"",""formCode"":""EQ_OUT_TIME"",""ifNew"":null,""ifShow"":false,""titleShow"":true,""titleColor"":""#000000"",""titleSize"":13,""titleStyle"":""0"",""titleBackground"":null,""titleAlign"":""3"",""contentLine"":1,""contentMaxLine"":1,""contentHeightClass"":1,""contentHeightRatio"":"""",""contentAlign"":""1"",""contentColor"":null,""contentFontSize"":13,""contentStyle"":""0"",""contentBackground"":null,""titleAndContentType"":""1"",""contentEnlarge"":false,""ifRequired"":null,""replaceField"":true,""onlyRead"":""1"",""default"":null,""resetContent"":null,""dataType"":""1"",""dataClass"":null,""allowMaintainDropDownData"":false,""formDesc"":null,""suffix"":null,""sort"":null,""editeState"":false,""unitFlag"":false,""styleJson"":{""formName"":""出厂日期"",""dataType"":""DatePicker"",""labelHide"":false,""labelStyle"":{""color"":""#000000"",""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""right"",""justifyContent"":""right""},""rules"":[{""required"":null}],""widgetProps"":{""disabled"":false,""style"":{""color"":null,""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""left"",""justifyContent"":""left""},""defaultValue"":null}}},{""formName"":""设备序列号"",""formCname"":""设备序列号"",""formCode"":""SERIAL_NUMBER"",""ifNew"":null,""ifShow"":false,""titleShow"":true,""titleColor"":""#000000"",""titleSize"":13,""titleStyle"":""0"",""titleBackground"":null,""titleAlign"":""3"",""contentLine"":1,""contentMaxLine"":1,""contentHeightClass"":1,""contentHeightRatio"":"""",""contentAlign"":""1"",""contentColor"":null,""contentFontSize"":13,""contentStyle"":""0"",""contentBackground"":null,""titleAndContentType"":""1"",""contentEnlarge"":false,""ifRequired"":null,""replaceField"":true,""onlyRead"":""1"",""default"":null,""resetContent"":null,""dataType"":""1"",""dataClass"":null,""allowMaintainDropDownData"":false,""formDesc"":null,""suffix"":null,""sort"":null,""editeState"":false,""unitFlag"":false,""styleJson"":{""formName"":""设备序列号"",""dataType"":""Input"",""labelHide"":false,""labelStyle"":{""color"":""#000000"",""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""right"",""justifyContent"":""right""},""rules"":[{""required"":null}],""widgetProps"":{""disabled"":false,""style"":{""color"":null,""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""left"",""justifyContent"":""left""}}}},{""formName"":""设备代号"",""formCname"":""设备代号"",""formCode"":""EQUIPMENT_CODE"",""ifNew"":null,""ifShow"":false,""titleShow"":true,""titleColor"":""#000000"",""titleSize"":13,""titleStyle"":""0"",""titleBackground"":null,""titleAlign"":""3"",""contentLine"":1,""contentMaxLine"":1,""contentHeightClass"":1,""contentHeightRatio"":"""",""contentAlign"":""1"",""contentColor"":null,""contentFontSize"":13,""contentStyle"":""0"",""contentBackground"":null,""titleAndContentType"":""1"",""contentEnlarge"":false,""ifRequired"":null,""replaceField"":true,""onlyRead"":""1"",""default"":null,""resetContent"":null,""dataType"":""1"",""dataClass"":null,""allowMaintainDropDownData"":false,""formDesc"":null,""suffix"":null,""sort"":null,""editeState"":false,""unitFlag"":false,""styleJson"":{""formName"":""设备代号"",""dataType"":""Input"",""labelHide"":false,""labelStyle"":{""color"":""#000000"",""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""right"",""justifyContent"":""right""},""rules"":[{""required"":null}],""widgetProps"":{""disabled"":false,""style"":{""color"":null,""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""left"",""justifyContent"":""left""}}}},{""formName"":""首次启用日期"",""formCname"":""首次启用日期"",""formCode"":""ENABLE_TIME"",""ifNew"":null,""ifShow"":false,""titleShow"":true,""titleColor"":""#000000"",""titleSize"":13,""titleStyle"":""0"",""titleBackground"":null,""titleAlign"":""3"",""contentLine"":1,""contentMaxLine"":1,""contentHeightClass"":1,""contentHeightRatio"":"""",""contentAlign"":""1"",""contentColor"":null,""contentFontSize"":13,""contentStyle"":""0"",""contentBackground"":null,""titleAndContentType"":""1"",""contentEnlarge"":false,""ifRequired"":null,""replaceField"":true,""onlyRead"":""1"",""default"":null,""resetContent"":null,""dataType"":""1"",""dataClass"":null,""allowMaintainDropDownData"":false,""formDesc"":null,""suffix"":null,""sort"":null,""editeState"":false,""unitFlag"":false,""styleJson"":{""formName"":""首次启用日期"",""dataType"":""DatePicker"",""labelHide"":false,""labelStyle"":{""color"":""#000000"",""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""right"",""justifyContent"":""right""},""rules"":[{""required"":null}],""widgetProps"":{""disabled"":false,""style"":{""color"":null,""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""left"",""justifyContent"":""left""},""defaultValue"":null}}},{""formName"":""使用年限"",""formCname"":""使用年限"",""formCode"":""EQ_SERVICE_LIFE"",""ifNew"":null,""ifShow"":false,""titleShow"":true,""titleColor"":""#000000"",""titleSize"":13,""titleStyle"":""0"",""titleBackground"":null,""titleAlign"":""3"",""contentLine"":1,""contentMaxLine"":1,""contentHeightClass"":1,""contentHeightRatio"":"""",""contentAlign"":""1"",""contentColor"":null,""contentFontSize"":13,""contentStyle"":""0"",""contentBackground"":null,""titleAndContentType"":""1"",""contentEnlarge"":false,""ifRequired"":null,""replaceField"":true,""onlyRead"":""1"",""default"":null,""resetContent"":null,""dataType"":""1"",""dataClass"":null,""allowMaintainDropDownData"":false,""formDesc"":null,""suffix"":null,""sort"":null,""editeState"":false,""unitFlag"":false,""styleJson"":{""formName"":""使用年限"",""dataType"":""InputNumber"",""labelHide"":false,""labelStyle"":{""color"":""#000000"",""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""right"",""justifyContent"":""right""},""rules"":[{""required"":null}],""widgetProps"":{""disabled"":false,""style"":{""color"":null,""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""left"",""justifyContent"":""left""},""defaultValue"":null}}},{""formName"":""生物安全"",""formCname"":""生物安全"",""formCode"":""SMBL_FLAG"",""ifNew"":null,""ifShow"":false,""titleShow"":true,""titleColor"":""#000000"",""titleSize"":13,""titleStyle"":""0"",""titleBackground"":null,""titleAlign"":""3"",""contentLine"":1,""contentMaxLine"":1,""contentHeightClass"":1,""contentHeightRatio"":"""",""contentAlign"":""1"",""contentColor"":null,""contentFontSize"":13,""contentStyle"":""0"",""contentBackground"":null,""titleAndContentType"":""1"",""contentEnlarge"":false,""ifRequired"":null,""replaceField"":true,""onlyRead"":""1"",""default"":null,""resetContent"":null,""dataType"":""1"",""dataClass"":null,""allowMaintainDropDownData"":false,""formDesc"":null,""suffix"":null,""sort"":null,""editeState"":false,""unitFlag"":false,""styleJson"":{""formName"":""生物安全"",""dataType"":""Radio"",""labelHide"":false,""labelStyle"":{""color"":""#000000"",""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""right"",""justifyContent"":""right""},""rules"":[{""required"":null}],""widgetProps"":{""disabled"":false,""style"":{""color"":null,""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""left"",""justifyContent"":""left""},""defaultValue"":null,""options"":[{""label"":""是"",""value"":""1""},{""label"":""否"",""value"":""0""}]}}},{""formName"":""备案实验室"",""formCname"":""备案实验室"",""formCode"":""SMBL_LAB_ID"",""ifNew"":null,""ifShow"":false,""titleShow"":true,""titleColor"":""#000000"",""titleSize"":13,""titleStyle"":""0"",""titleBackground"":null,""titleAlign"":""3"",""contentLine"":1,""contentMaxLine"":1,""contentHeightClass"":1,""contentHeightRatio"":"""",""contentAlign"":""1"",""contentColor"":null,""contentFontSize"":13,""contentStyle"":""0"",""contentBackground"":null,""titleAndContentType"":""1"",""contentEnlarge"":false,""ifRequired"":null,""replaceField"":true,""onlyRead"":""1"",""default"":null,""resetContent"":null,""dataType"":""1"",""dataClass"":null,""allowMaintainDropDownData"":false,""formDesc"":null,""suffix"":null,""sort"":null,""editeState"":false,""unitFlag"":false,""styleJson"":{""formName"":""备案实验室"",""dataClass"":""生安实验室"",""dataType"":""Select"",""labelHide"":false,""labelStyle"":{""color"":""#000000"",""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""right"",""justifyContent"":""right""},""rules"":[{""required"":null}],""widgetProps"":{""disabled"":false,""style"":{""color"":null,""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""left"",""justifyContent"":""left""},""defaultValue"":null}}},{""formName"":""设备自定义代号"",""formCname"":""设备自定义代号"",""formCode"":""EQUIPMENT_UCODE"",""ifNew"":null,""ifShow"":false,""titleShow"":true,""titleColor"":""#000000"",""titleSize"":13,""titleStyle"":""0"",""titleBackground"":null,""titleAlign"":""3"",""contentLine"":1,""contentMaxLine"":1,""contentHeightClass"":1,""contentHeightRatio"":"""",""contentAlign"":""1"",""contentColor"":null,""contentFontSize"":13,""contentStyle"":""0"",""contentBackground"":null,""titleAndContentType"":""1"",""contentEnlarge"":false,""ifRequired"":null,""replaceField"":true,""onlyRead"":""1"",""default"":null,""resetContent"":null,""dataType"":""1"",""dataClass"":null,""allowMaintainDropDownData"":false,""formDesc"":null,""suffix"":null,""sort"":null,""editeState"":false,""unitFlag"":false,""styleJson"":{""formName"":""设备自定义代号"",""dataType"":""Input"",""labelHide"":false,""labelStyle"":{""color"":""#000000"",""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""right"",""justifyContent"":""right""},""rules"":[{""required"":null}],""widgetProps"":{""disabled"":false,""style"":{""color"":null,""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""left"",""justifyContent"":""left""}}}},{""formName"":""生安设备类型"",""formCname"":""生安设备类型"",""formCode"":""SMBL_CLASS"",""ifNew"":null,""ifShow"":false,""titleShow"":true,""titleColor"":""#000000"",""titleSize"":13,""titleStyle"":""0"",""titleBackground"":null,""titleAlign"":""3"",""contentLine"":1,""contentMaxLine"":1,""contentHeightClass"":1,""contentHeightRatio"":"""",""contentAlign"":""1"",""contentColor"":null,""contentFontSize"":13,""contentStyle"":""0"",""contentBackground"":null,""titleAndContentType"":""1"",""contentEnlarge"":false,""ifRequired"":null,""replaceField"":true,""onlyRead"":""1"",""default"":null,""resetContent"":null,""dataType"":""1"",""dataClass"":null,""allowMaintainDropDownData"":false,""formDesc"":null,""suffix"":null,""sort"":null,""editeState"":false,""unitFlag"":false,""styleJson"":{}},{""formName"":""设备产出地"",""formCname"":""设备产出地"",""formCode"":""COUNTRY_ORIGIN"",""ifNew"":null,""ifShow"":false,""titleShow"":true,""titleColor"":""#000000"",""titleSize"":13,""titleStyle"":""0"",""titleBackground"":null,""titleAlign"":""3"",""contentLine"":1,""contentMaxLine"":1,""contentHeightClass"":1,""contentHeightRatio"":"""",""contentAlign"":""1"",""contentColor"":null,""contentFontSize"":13,""contentStyle"":""0"",""contentBackground"":null,""titleAndContentType"":""1"",""contentEnlarge"":false,""ifRequired"":null,""replaceField"":true,""onlyRead"":""1"",""default"":null,""resetContent"":null,""dataType"":""2"",""dataClass"":null,""allowMaintainDropDownData"":false,""formDesc"":null,""suffix"":null,""sort"":null,""editeState"":false,""unitFlag"":false,""styleJson"":{""formName"":""设备产出地"",""dataType"":""InputNumber"",""labelHide"":false,""labelStyle"":{""color"":""#000000"",""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""right"",""justifyContent"":""right""},""rules"":[{""required"":null}],""widgetProps"":{""disabled"":false,""style"":{""color"":null,""fontSize"":13,""fontWeight"":""normal"",""fontStyle"":""normal"",""background"":null,""textAlign"":""left"",""justifyContent"":""left""}}}}]}";
            //formDict.FORM_COL_JSON = JsonConvert.SerializeObject(PageSettingForm);
            formDict.FORM_COL_JSON = json;
            _dbContext.Db.Updateable(formDict).ExecuteCommand();
        }
        return resultDto;
    }
    
    /// <summary>
    /// 把表单所有字段转换成 FieldDictDto
    /// </summary>
    private static IEnumerable<FieldDictDto> ExtractFieldDicts(FormJsonDto form, PageSettingForm  pageSettingForm,
        Dictionary<string, List<string>> widgetPropsMap)
    {
      var result = Array.Empty<FieldDictDto>().ToList();
      form.rows.SelectMany(r => r.cols)
        .SelectMany(c => c.items).Where(i => !i.formCode.IsNullOrEmpty() && !i.formCname.IsNullOrEmpty())
        .ForEach((item) =>
        {
          var i = 0;
          var from = pageSettingForm.form.FirstOrDefault(f => f.formCode == item.formCode);
          if (from != null)
          { 
            result.Add(BuildFieldDict(item,from ,widgetPropsMap, i++));
          }
        });
      return result;
    }
    /// <summary>
    /// 单个字段 -> FieldDictDto
    /// </summary>
    private static FieldDictDto BuildFieldDict(Items item, Form from,
        Dictionary<string, List<string>> widgetPropsMap,
        int sort)
    {
        var dto = new FieldDictDto
        {
            FIELD_CLASS = "基本信息",
            FIELD_NAME  = from.formCname,
            FIELD_CODE  = from.formCode,
            HOSPITAL_ID = "33A001",
            MODULE_ID   = "H82",
            FIELD_STATE = "1",
            FIELD_DESC  = from.formDesc,
            FIELD_SORT  = sort.ToString()
        };
        // 样式 JSON
        dto.STYLE_JSON = JsonHelper.ToJson(from.styleJson);
        // 只读 JSON（属性清单）
        List<string>? props;
        widgetPropsMap.TryGetValue(from.styleJson.dataType, out props);
        dto.READONLY_JSON = props == null
            ? null
            : JsonSerializer.Serialize( props);

        return dto;
    }
    
    /// <summary>
    /// 根据 SETUP_ID 加载表单 JSON
    /// </summary>
    private (FormJsonDto? formJson, PageSettingForm? pageSettingForm) LoadFormJson(string setupId = "********")
    {
        var formDict = _dbContext.Db.Queryable<SYS6_MODULE_FUNC_DICT>()
            .First(p => p.SETUP_ID == setupId);
       var  formJson  = formDict == null
            ? null
            : JsonHelper.FromJson<FormJsonDto>(formDict.FORM_JSON);
         
       var pageSettingForm = formDict == null
            ? null
            : JsonHelper.FromJson<PageSettingForm>(formDict.FORM_COL_JSON);
       return (formJson, pageSettingForm);
    }
    
    /// <summary>
    /// 加载组件 -> 属性清单 映射
    /// </summary>
    private static Dictionary<string, List<string>> LoadWidgetPropsMap()
    {
     const string json = @"[
      {
        ""group"": ""布局"",
        ""formId"": ""col_1"",
        ""icon"": ""iconyilie"",
        ""formName"": ""一行一列"",
        ""dataType"": ""Col1""
      },
      {
        ""group"": ""布局"",
        ""formId"": ""col_2"",
        ""icon"": ""iconlianglie"",
        ""formName"": ""一行两列"",
        ""dataType"": ""Col2""
      },
      {
        ""group"": ""布局"",
        ""formId"": ""col_3"",
        ""icon"": ""iconsanlie"",
        ""formName"": ""一行三列"",
        ""dataType"": ""Col3""
      },
      {
        ""group"": ""布局"",
        ""formId"": ""col_4"",
        ""icon"": ""iconsilie"",
        ""formName"": ""一行四列"",
        ""dataType"": ""Col4""
      },
      {
        ""group"": ""基本"",
        ""formId"": ""title"",
        ""icon"": ""iconbiaotilan"",
        ""formName"": ""标题"",
        ""isForm"": false,
        ""dataType"": ""XTitle"",
        ""wigetProps"": {
          ""button"": true,
          ""groupKey"": """"
        },
        ""propslist"": [""button"", ""buttonSize"", ""groupKey""]
      },
      {
        ""group"": ""基本"",
        ""formId"": ""fengexian"",
        ""icon"": ""iconfengexian1"",
        ""formName"": ""分隔线"",
        ""isForm"": false,
        ""dataType"": ""Divider"",
        ""wigetProps"": {
          ""button"": true,
          ""groupKey"": """"
        },
        ""propslist"": [""button"", ""buttonSize"", ""groupKey""]
      },
      {
        ""group"": ""基本"",
        ""formId"": ""danhangwenben"",
        ""icon"": ""icondanhangwenben"",
        ""formName"": ""单行文本"",
        ""dataType"": ""Input"",
        ""isForm"": true,
        ""rules"": [{ ""required"": true }],
        ""wigetProps"": {
          ""placeholder"": ""请输入"",
          ""allowClear"": true,
          ""maxLength"": 200,
          ""disabled"": false
        },
        ""propslist"": [
          ""dataType"",
          ""required"",
          ""allowClear"",
          ""placeholder"",
          ""maxLength"",
          ""prefix"",
          ""suffix"",
          ""variant"",
          ""disabled"",
          ""showCount""
        ]
      },
      {
        ""group"": ""基本"",
        ""formId"": ""duohangwenben"",
        ""icon"": ""iconduohangwenben"",
        ""formName"": ""多行文本"",
        ""dataType"": ""TextArea"",
        ""isForm"": true,
        ""wigetProps"": {},
        ""propslist"": [
          ""dataType"",
          ""required"",
          ""allowClear"",
          ""placeholder"",
          ""maxLength"",
          ""minRows"",
          ""maxRows"",
          ""variant"",
          ""disabled"",
          ""showCount""
        ]
      },
      {
        ""group"": ""基本"",
        ""formId"": ""xialakuang"",
        ""icon"": ""iconxialakuang"",
        ""formName"": ""下拉框"",
        ""dataType"": ""Select"",
        ""isForm"": true,
        ""wigetProps"": {
          ""options"": [
            { ""value"": ""选项1"", ""label"": ""选项1"" },
            { ""value"": ""选项2"", ""label"": ""选项2"" },
            { ""value"": ""选项3"", ""label"": ""选项3"" }
          ]
        },
        ""propslist"": [
          ""dataType"",
          ""required"",
          ""allowClear"",
          ""prefix"",
          ""suffix"",
          ""variant"",
          ""placeholder"",
          ""disabled"",
          ""showSearch"",
          ""mode"",
          ""queryType"",
          ""dataClass"",
          ""defaultValue"",
          ""isDataTypeToPerson""
        ]
      },
      {
        ""group"": ""基本"",
        ""formId"": ""shuzhishurukuang"",
        ""icon"": ""iconshuzhishurukuang"",
        ""formName"": ""数值"",
        ""dataType"": ""InputNumber"",
        ""isForm"": true,
        ""wigetProps"": {
          ""controls"": true
        },
        ""propslist"": [
          ""dataType"",
          ""required"",
          ""allowClear"",
          ""placeholder"",
          ""maxLength"",
          ""min"",
          ""max"",
          ""prefix"",
          ""suffix"",
          ""variant"",
          ""disabled"",
          ""controls""
        ]
      },
      {
        ""group"": ""基本"",
        ""formId"": ""danxuankuang"",
        ""icon"": ""icondanxuan"",
        ""formName"": ""单选"",
        ""dataType"": ""Radio"",
        ""isForm"": true,
        ""wigetProps"": {
          ""options"": [
            { ""value"": ""选项1"", ""label"": ""选项1"" },
            { ""value"": ""选项2"", ""label"": ""选项2"" },
            { ""value"": ""选项3"", ""label"": ""选项3"" }
          ]
        },
        ""propslist"": [
          ""dataType"",
          ""required"",
          ""optionType"",
          ""styleFlex"",
          ""disabled"",
          ""queryType"",
          ""dataClass"",
          ""defaultValue"",
          ""isDataTypeToPerson""
        ]
      },
      {
        ""group"": ""基本"",
        ""formId"": ""fuxuankuang"",
        ""icon"": ""iconfuxuan"",
        ""formName"": ""复选"",
        ""dataType"": ""Checkbox"",
        ""isForm"": true,
        ""wigetProps"": {
          ""options"": [
            { ""value"": ""选项1"", ""label"": ""选项1"" },
            { ""value"": ""选项2"", ""label"": ""选项2"" },
            { ""value"": ""选项3"", ""label"": ""选项3"" }
          ]
        },
        ""propslist"": [
          ""dataType"",
          ""required"",
          ""styleFlex"",
          ""disabled"",
          ""queryType"",
          ""dataClass"",
          ""defaultValue"",
          ""isDataTypeToPerson""
        ]
      },
      {
        ""group"": ""基本"",
        ""formId"": ""pingfen"",
        ""icon"": ""iconpingfen"",
        ""formName"": ""评分"",
        ""dataType"": ""Rate"",
        ""isForm"": true,
        ""wigetProps"": {
          ""tooltips"": [""差"", ""较差"", ""一般"", ""好"", ""优秀""]
        },
        ""propslist"": [
          ""dataType"",
          ""required"",
          ""allowClear"",
          ""allowHalf"",
          ""disabled""
        ]
      },
      {
        ""group"": ""基本"",
        ""formId"": ""kaiguan"",
        ""icon"": ""iconkaiguan"",
        ""formName"": ""开关"",
        ""dataType"": ""Switch"",
        ""isForm"": true,
        ""wigetProps"": {},
        ""propslist"": [
          ""dataType"",
          ""required"",
          ""checkedChildren"",
          ""unCheckedChildren""
        ]
      },
      {
        ""group"": ""基本"",
        ""formId"": ""huadongshuru"",
        ""icon"": ""iconhuadongshuru"",
        ""formName"": ""滑动输入条"",
        ""dataType"": ""Slider"",
        ""isForm"": true,
        ""wigetProps"": {},
        ""propslist"": [
          ""dataType"",
          ""required"",
          ""disabled"",
          ""max"",
          ""min""
        ]
      },
      {
        ""group"": ""基本"",
        ""formId"": ""yansexuanze"",
        ""icon"": ""iconyanse"",
        ""formName"": ""颜色选择器"",
        ""dataType"": ""ColorPicker"",
        ""isForm"": true,
        ""wigetProps"": {
          ""showText"": true,
          ""allowClear"": true
        },
        ""propslist"": [
          ""dataType"",
          ""required"",
          ""disabled"",
          ""showText""
        ]
      },
      {
        ""group"": ""基本"",
        ""formId"": ""shijian"",
        ""icon"": ""iconshijian1"",
        ""formName"": ""时间"",
        ""dataType"": ""TimePicker"",
        ""isForm"": true,
        ""wigetProps"": {},
        ""propslist"": [
          ""dataType"",
          ""required"",
          ""disabled"",
          ""allowClear""
        ]
      },
      {
        ""group"": ""基本"",
        ""formId"": ""shijianfanwei"",
        ""icon"": ""iconshijianqujian"",
        ""formName"": ""时间区间"",
        ""dataType"": ""TimeRangePicker"",
        ""isForm"": true,
        ""wigetProps"": {},
        ""propslist"": [
          ""dataType"",
          ""required"",
          ""disabled"",
          ""allowClear""
        ]
      },
      {
        ""group"": ""基本"",
        ""formId"": ""riqi"",
        ""icon"": ""iconriqi"",
        ""formName"": ""日期"",
        ""dataType"": ""DatePicker"",
        ""isForm"": true,
        ""wigetProps"": {},
        ""propslist"": [
          ""dataType"",
          ""required"",
          ""disabled"",
          ""picker"",
          ""allowClear""
        ]
      },
      {
        ""group"": ""基本"",
        ""formId"": ""riqifanwei"",
        ""icon"": ""iconriqifanwei"",
        ""formName"": ""日期区间"",
        ""dataType"": ""RangePicker"",
        ""isForm"": true,
        ""wigetProps"": {},
        ""propslist"": [
          ""dataType"",
          ""required"",
          ""disabled"",
          ""picker"",
          ""allowClear""
        ]
      },
      {
        ""group"": ""基本"",
        ""formId"": ""tupianfujian"",
        ""icon"": ""icontouxiang"",
        ""formName"": ""图片"",
        ""formNameHide"": true,
        ""dataType"": ""ImgUpload"",
        ""isForm"": true,
        ""wigetProps"": {
          ""width"": 200,
          ""height"": 250,
          ""accept"": "".png,.jpg,.jpeg""
        },
        ""propslist"": [
          ""dataType"",
          ""required"",
          ""width"",
          ""height""
        ]
      },
      {
        ""group"": ""基本"",
        ""formId"": ""fujian"",
        ""icon"": ""iconfujian"",
        ""formName"": ""附件"",
        ""isForm"": true,
        ""dataType"": ""Upload"",
        ""wigetProps"": {
          ""listType"": ""picture-card"",
          ""multiple"": true,
          ""accept"": "".png,.jpg,.jpeg,.pdf,.xls,.xlsx,.doc,.docx"",
          ""showUploadList"": true
        },
        ""propslist"": [
          ""dataType"",
          ""required"",
          ""multiple"",
          ""listType"",
          ""accept""
        ]
      }
    ]";
        var list = JsonHelper.FromJson<List<PropslistJson>>(json);
        return list.ToDictionary(p => p.dataType, p => p.propslist);
    }
    
    
}