﻿using H.Utility;
using H.Utility.SqlSugarInfra;
using SqlSugar;
using System.ComponentModel.DataAnnotations.Schema;

namespace XH.H82.Models.Entities.InkScreen
{
    [DBOwner("XH_OA")]
    [Table("EMS_INKSCREEN_TEMPLATE")]
    public class EMS_INKSCREEN_TEMPLATE : BaseField
    {


        /// <summary>
        /// 模板id
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string TEMPLATE_ID { get; set; }
        /// <summary>
        /// 模板名称
        /// </summary>
        public string TEMPLATE_NAME { get; set; }
        /// <summary>
        /// 模板内容
        /// </summary>
        public string TEMPLATE_CONTENT { get; set; }
        /// <summary>
        /// 模板标题
        /// </summary>
        public string TEMPLATE_TITLE { get; set; }

        /// <summary>
        /// 异常状态亮灯
        /// </summary>
        public bool SET_ABNORMAL { get; set; } = true;
        /// <summary>
        /// 是否设置二维码
        /// </summary>
        public bool SET_QR_CODE { get; set; } = false;
        /// <summary>
        /// 是否设置表格线框
        /// </summary>
        public bool SET_WIREFRAME { get; set; } = false;
        /// <summary>
        /// 应用的专业组
        /// </summary>
        public string PGROUP_SID { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public string TEMPLATE_STATE { get; set; }


        private List<string> _GROUPS = new List<string>();

        public List<string> GetGroups()
        {
            if (PGROUP_SID.IsNotNullOrEmpty())
            {
                var groups = PGROUP_SID.Split(';');
                foreach (var group in groups)
                {
                    _GROUPS.Add(group);
                }
            }
            return _GROUPS;
        }


    }
}
