﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.Entities.Common
{
    [DBOwner("XH_SYS")]
    public class LIS6_PIPELINING_INSTRUMENT
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string PIPELINING_ID { get; set; }
        [SugarColumn(IsPrimaryKey = true)]
        public string INSTRUMENT_ID { get;set; }
        public string INSTRUMENT_SNUM { get; set; }
        public string INSTRUMENT_CODE { get; set; }
        public string INSTRUMENT_SORT { get; set; }
        public string FIRST_RPERSON { get; set; }
        public DateTime? FIRST_RTIME { get; set; }
        public string LAST_MPERSON { get ; set; }
        public DateTime? LAST_MTIME { get; set; }
        public string REMARK { get; set; }
    }
}
