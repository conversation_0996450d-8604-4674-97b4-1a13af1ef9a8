﻿using H.Utility.SqlSugarInfra;
using SqlSugar;

namespace XH.H82.Models.Entities
{
    [DBOwner("XH_OA")]
    public class EMS_SCRAP_LOG
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string SCRAP_PROCESS_ID { get; set; }
        public string H<PERSON><PERSON>TAL_ID { get; set; }
        public string SCRAP_ID { get; set; }
        public string PROCESS_STATE { get; set; }
        public string OPER_PERSON { get; set; }
        public DateTime? OPER_TIME { get; set; }
        public string PROCESS_CONTENT { get; set; }
        public string FIRST_RPERSON { get; set; }
        public DateTime? FIRST_RTIME { get; set; }
        public string LAST_MPERSON { get; set; }
        public DateTime? LAST_MTIME { get; set; }
        public string REMARK { get; set; }
        public string PROCESS_TYPE { get; set; }
    }
}
