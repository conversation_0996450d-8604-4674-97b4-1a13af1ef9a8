using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using SqlSugar;
using XH.H82.Base.Tree;
using XH.H82.IServices.Sbml;
using XH.H82.Models.DeviceRelevantInformation;
using XH.H82.Models.Dtos;
using XH.H82.Models.Dtos.Tim;
using XH.H82.Models.Entities;
using XH.H82.Models.Entities.Common;
using XH.H82.Models.Entities.OperationLog;
using XH.H82.Models.Entities.Tim;
using XH.H82.Models.SugarDbContext;
using XH.H82.Services.DeactivationScrapping;
using XH.H82.Services.DeviceDataRefresh;
using XH.LAB.UTILS.Helpers;
using XH.LAB.UTILS.Interface;
using XH.LAB.UTILS.Models;
using Log = NPOI.SS.Formula.Functions.Log;

namespace XH.H82.Services.Smbl;

public class SmblServicve: ISmblServicve
{
    private readonly ISqlSugarUow<SugarDbContext_Master> _dbContext;
    private AuthorityContext _authorityContext;
    private IAuthorityService2 _authorityService; 
    private readonly IHttpContextAccessor _httpContext;
    private const string SmblEquipmentClass = "生安设备类型";
    private List<SYS6_INSPECTION_PGROUP> _pgroups = new ();
    private List<SMBL_LAB> _smblLabs = new ();
    
    
    public SmblServicve(ISqlSugarUow<SugarDbContext_Master> dbContext, IAuthorityService2 authorityService, IHttpContextAccessor httpContext)
    {
        _dbContext = dbContext;
        _authorityService = authorityService;
        _httpContext = httpContext;
        _authorityContext = new AuthorityContext(_dbContext,_authorityService);
    }
    /// <summary>
    /// 机构入口-生安树基本结构
    /// </summary>
    /// <param name="hospitalId"></param>
    /// <returns></returns>
    public (RootNode rootNode, List<ITreeNode> nodes) GetSmblHospitalBaseTree(string hospitalId)
    {
        var nodes = new List<ITreeNode>();
        var root = new RootNode();
        var num = 1;
        var hospitalNode =  _authorityContext.AddSmblHospitalNode(root, hospitalId, false, ref num);
        nodes.Add(hospitalNode);
        var smblLabs = _authorityContext.GetUserSmbls();
        var labIds = smblLabs.Where(x=>x.LAB_ID!=null).Select(x => x.LAB_ID).Distinct();
        var labs = _dbContext.Db.Queryable<SYS6_INSPECTION_LAB>()
            .Where(x => labIds.Contains(x.LAB_ID))
            .Where(x => x.STATE_FLAG == "1")
            .ToList();
        Serilog.Log.Information($"数据埋点2:{JsonConvert.SerializeObject(smblLabs)}");
        Serilog.Log.Information($"数据埋点4:{JsonConvert.SerializeObject(labs)}");
        foreach (var lab in labs)
        {
           var labNode = _authorityContext.AddLabNode(hospitalNode, lab, false, ref num);
           nodes.Add(labNode);
        }
        Serilog.Log.Information($"数据埋点5:{JsonConvert.SerializeObject(root)}");
        foreach (var smblLab in smblLabs)
        {
            var labNode = nodes.FirstOrDefault(x => x.NODE_TYPE is TreeNodeTypeEnum.LAB && x.LAB_ID == smblLab.LAB_ID);
            if (labNode is not null)
            {
              var smblLabNode =  _authorityContext.AddSmblLabNode(labNode, smblLab, false, ref num);
              nodes.Add(smblLabNode);
            }
        }
        return (root,nodes);
    }
    /// <summary>
    /// 科室入口-生安树基本结构
    /// </summary>
    /// <param name="labId"></param>
    /// <returns></returns>
    public (RootNode rootNode, List<ITreeNode> nodes) GetLabBaseTree(string labId)
    {
        var nodes = new List<ITreeNode>();
        var root = new RootNode();
        var num = 1;
        var labNode =  _authorityContext.AddLabNode(root, labId, false, ref num);
        nodes.Add(labNode);
        var smblLabs = _authorityContext.GetUserSmbls()
            .Where(x => x.LAB_ID == labId);
        foreach (var smblLab in smblLabs)
        { 
            var smblLabNode =   _authorityContext.AddSmblLabNode(labNode, smblLab, false, ref num);
            nodes.Add(smblLabNode);
        }
        return (root,nodes);
    }
    /// <summary>
    /// 备案实验室入口-生安基本结构
    /// </summary>
    /// <param name="smblLabId"></param>
    /// <returns></returns>
    public (RootNode rootNode, List<ITreeNode> nodes) GetSmblLabBaseTree(string smblLabId)
    {
        var nodes = new List<ITreeNode>();
        var root = new RootNode();
        var num = 1;
        var smblLabNode  = _authorityContext.AddSmblLabNode(root, smblLabId, false, ref num);
        nodes.Add(smblLabNode);
        return (root, nodes);
    }

    /// <summary>
    /// 生安-备案实验室拉下数据   
    /// </summary>
    /// <param name="hospitalId"></param>
    /// <returns></returns>
    public (RootNode rootNode, List<ITreeNode> nodes) GetSmblLabPullTree(string hospitalId)
    {
        var nodes = new List<ITreeNode>();
        var root = new RootNode();
        var num = 1;
        var hospitalNode =  _authorityContext.AddSmblHospitalNode(root, hospitalId, false, ref num);
        if (hospitalNode is null)
        {
            return (root, nodes);
        }
        nodes.Add(hospitalNode);
        var smblLabs = _authorityContext.GetUserSmbls();
        foreach (var smblLab in smblLabs)
        {
            var smblLabNode =  _authorityContext.AddSmblLabNode(hospitalNode, smblLab, false, ref num);
            nodes.Add(smblLabNode);
        }

        return (root, nodes);

    }

    /// <summary>
    /// 查询生安设备类型
    /// </summary>
    /// <returns></returns>
    public List<SYS6_BASE_DATA> GetSmblEquipmentClassPull()
    {
        var result = _dbContext.Db.Queryable<SYS6_BASE_DATA>().Where(x => x.CLASS_ID == SmblEquipmentClass)
            .Where(x => x.DATA_STATE == "1")
            .ToList();
        if (!result.Any())
        {
            result.Add(new SYS6_BASE_DATA()
            {
                DATA_ID = "1",
                HOSPITAL_ID = "H0000",
                LAB_ID = "L000",
                CLASS_ID = "生安设备类型",
                DATA_SORT = "001",
                DATA_CNAME = "生物安全柜",
                DATA_ENAME = "生物安全柜",
                HIS_ID = null,
                CUSTOM_CODE = "smblEqClassInit",
                SPELL_CODE = null,
                DATA_STATE = "1",
                FIRST_RPERSON = "管理员",
                FIRST_RTIME = DateTime.Now,
                LAST_MPERSON = "初始化",
                LAST_MTIME = DateTime.Now,
                REMARK = null,
                DATA_SNAME = null,
                DATA_SOURCE = null,
                ONE_CLASS = "设备管理",
                DATA_UNAME = null,
                IF_REPEAT = null,
                SYSTEM_ID = "LIS",
            });
            
            result.Add(new SYS6_BASE_DATA()
                {
                    DATA_ID = "2",
                    HOSPITAL_ID = "H0000",
                    LAB_ID = "L000",
                    CLASS_ID="生安设备类型",
                    DATA_SORT = "002",
                    DATA_CNAME="高压灭菌器",
                    DATA_ENAME="高压灭菌器",
                    HIS_ID = null,
                    CUSTOM_CODE="smblEqClassInit",
                    SPELL_CODE = null,
                    DATA_STATE= "1",
                    FIRST_RPERSON = "管理员",
                    FIRST_RTIME = DateTime.Now,
                    LAST_MPERSON="初始化",
                    LAST_MTIME = DateTime.Now,
                    REMARK = null,
                    DATA_SNAME = null,
                    DATA_SOURCE=null,
                    ONE_CLASS = "设备管理",
                    DATA_UNAME=null,
                    IF_REPEAT= null,
                    SYSTEM_ID="LIS",
                });
            result.Add(new SYS6_BASE_DATA()
                {
                    DATA_ID = "3",
                    HOSPITAL_ID = "H0000",
                    LAB_ID = "L000",
                    CLASS_ID="生安设备类型",
                    DATA_SORT = "003",
                    DATA_CNAME="离心机",
                    DATA_ENAME="离心机",
                    HIS_ID = null,
                    CUSTOM_CODE="smblEqClassInit",
                    SPELL_CODE = null,
                    DATA_STATE= "1",
                    FIRST_RPERSON = "管理员",
                    FIRST_RTIME = DateTime.Now,
                    LAST_MPERSON="初始化",
                    LAST_MTIME = DateTime.Now,
                    REMARK = null,
                    DATA_SNAME = null,
                    DATA_SOURCE=null,
                    ONE_CLASS = "设备管理",
                    DATA_UNAME=null,
                    IF_REPEAT= null,
                    SYSTEM_ID="LIS",
                });
            result.Add(new SYS6_BASE_DATA()
            {
                DATA_ID = "4",
                HOSPITAL_ID = "H0000",
                LAB_ID = "L000",
                CLASS_ID = "生安设备类型",
                DATA_SORT = "004",
                DATA_CNAME = "洗眼装置",
                DATA_ENAME = "洗眼装置",
                HIS_ID = null,
                CUSTOM_CODE = "smblEqClassInit",
                SPELL_CODE = null,
                DATA_STATE = "1",
                FIRST_RPERSON = "管理员",
                FIRST_RTIME = DateTime.Now,
                LAST_MPERSON = "初始化",
                LAST_MTIME = DateTime.Now,
                REMARK = null,
                DATA_SNAME = null,
                DATA_SOURCE = null,
                ONE_CLASS = "设备管理",
                DATA_UNAME = null,
                IF_REPEAT = null,
                SYSTEM_ID = "LIS",
            });
            result.Add(new SYS6_BASE_DATA()
                {
                    DATA_ID = "5",
                    HOSPITAL_ID = "H0000",
                    LAB_ID = "L000",
                    CLASS_ID="生安设备类型",
                    DATA_SORT = "005",
                    DATA_CNAME="消毒装置",
                    DATA_ENAME="消毒装置",
                    HIS_ID = null,
                    CUSTOM_CODE="smblEqClassInit",
                    SPELL_CODE = null,
                    DATA_STATE= "1",
                    FIRST_RPERSON = "管理员",
                    FIRST_RTIME = DateTime.Now,
                    LAST_MPERSON="初始化",
                    LAST_MTIME = DateTime.Now,
                    REMARK = null,
                    DATA_SNAME = null,
                    DATA_SOURCE=null,
                    ONE_CLASS = "设备管理",
                    DATA_UNAME=null,
                    IF_REPEAT= null,
                    SYSTEM_ID="LIS",
                });
            result.Add(new SYS6_BASE_DATA()
            {
                DATA_ID = "6",
                HOSPITAL_ID = "H0000",
                LAB_ID = "L000",
                CLASS_ID="生安设备类型",
                DATA_SORT = "099",
                DATA_CNAME="其他",
                DATA_ENAME="其他",
                HIS_ID = null,
                CUSTOM_CODE="smblEqClassInit",
                SPELL_CODE = null,
                DATA_STATE= "1",
                FIRST_RPERSON = "管理员",
                FIRST_RTIME = DateTime.Now,
                LAST_MPERSON="初始化",
                LAST_MTIME = DateTime.Now,
                REMARK = null,
                DATA_SNAME = null,
                DATA_SOURCE=null,
                ONE_CLASS = "设备管理",
                DATA_UNAME=null,
                IF_REPEAT= null,
                SYSTEM_ID="LIS",
            });
            result.Add(new SYS6_BASE_DATA()
            {
                DATA_ID = "7",
                HOSPITAL_ID = "H0000",
                LAB_ID = "L000",
                CLASS_ID="生安设备类型",
                DATA_SORT = "007",
                DATA_CNAME="环境监测设备",
                DATA_ENAME="环境监测设备",
                HIS_ID = null,
                CUSTOM_CODE="smblEqClassInit",
                SPELL_CODE = null,
                DATA_STATE= "1",
                FIRST_RPERSON = "管理员",
                FIRST_RTIME = DateTime.Now,
                LAST_MPERSON="初始化",
                LAST_MTIME = DateTime.Now,
                REMARK = null,
                DATA_SNAME = null,
                DATA_SOURCE=null,
                ONE_CLASS = "设备管理",
                DATA_UNAME=null,
                IF_REPEAT= null,
                SYSTEM_ID="LIS",
            });
        }
        if (!result.Any(x=>x.DATA_ID == "7"))
        {
            result.Add(new SYS6_BASE_DATA()
            {
                DATA_ID = "7",
                HOSPITAL_ID = "H0000",
                LAB_ID = "L000",
                CLASS_ID = "生安设备类型",
                DATA_SORT = "007",
                DATA_CNAME = "环境监测设备",
                DATA_ENAME = "环境监测设备",
                HIS_ID = null,
                CUSTOM_CODE = "smblEqClassInit",
                SPELL_CODE = null,
                DATA_STATE = "1",
                FIRST_RPERSON = "管理员",
                FIRST_RTIME = DateTime.Now,
                LAST_MPERSON = "初始化",
                LAST_MTIME = DateTime.Now,
                REMARK = null,
                DATA_SNAME = null,
                DATA_SOURCE = null,
                ONE_CLASS = "设备管理",
                DATA_UNAME = null,
                IF_REPEAT = null,
                SYSTEM_ID = "LIS",
            });
        }

        if (!result.Any(x=>x.DATA_ID == "8"))
        {
            result.Add(new SYS6_BASE_DATA()
            {
                DATA_ID = "8",
                HOSPITAL_ID = "H0000",
                LAB_ID = "L000",
                CLASS_ID = "生安设备类型",
                DATA_SORT = "008",
                DATA_CNAME = "门禁设备",
                DATA_ENAME = "门禁",
                HIS_ID = null,
                CUSTOM_CODE = "smblEqClassInit",
                SPELL_CODE = null,
                DATA_STATE = "1",
                FIRST_RPERSON = "管理员",
                FIRST_RTIME = DateTime.Now,
                LAST_MPERSON = "初始化",
                LAST_MTIME = DateTime.Now,
                REMARK = null,
                DATA_SNAME = null,
                DATA_SOURCE = null,
                ONE_CLASS = "设备管理",
                DATA_UNAME = null,
                IF_REPEAT = null,
                SYSTEM_ID = "LIS",
            });
        }
        
        return result;
    }

    
    public void UpdateSmblLabTime(string smblLabId)
    {
        try
        {
            _dbContext.Db.Updateable<XH.H82.Models.Entities.SMBL.SMBL_LAB>()
                .SetColumns(x => x.UPDATE_TIME == SqlFunc.GetDate())
                .Where(x=>x.SMBL_LAB_ID == smblLabId)
                .ExecuteCommand();
        }
        catch (Exception e)
        {
            Serilog.Log.Error("更新smblLabTime: " + e);
        }
    }

    /// <summary>
    /// 生安-申购记录查询
    /// </summary>
    /// <param name="startTime"> 开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="state">状态</param>
    /// <param name="search">模糊查询关键字</param>
    /// <returns></returns>
    public List<EMS_SUBSCRIBE_INFO> GetSubscrs(DateTime startTime, DateTime endTime,string state, string search)
    {
        #region smbl
        var equipmentContext = new EquipmentContext(_dbContext);
        var result = _dbContext.Db.Queryable<EMS_SUBSCRIBE_INFO>()
            .Where(p=>_smblLabs.Select(x=>x.SMBL_LAB_ID).Contains(p.SMBL_LAB_ID))
            .Where(p => p.SUBSCRIBE_DATE <= endTime && p.SUBSCRIBE_DATE >= startTime)
            .WhereIF(state.IsNotNullOrEmpty(), p => p.SUBSCRIBE_STATE == state)
            .WhereIF(search.IsNotNullOrEmpty(), p => p.SUBSCRIBE_NAME.ToLower().Contains(search.ToLower())
                                                     || p.SUBSCRIBE_PERSON.ToLower().Contains(search.ToLower()))
            .ToList();
        #endregion
        
        var equipments = new List<EMS_EQUIPMENT_INFO>();
        _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>()
            .Includes(i => i.eMS_INSTALL_INFO)
            .Includes(i => i.eMS_PURCHASE_INFO)
            .ForEach(i => equipments.Add(i));
        foreach (var item in result)
        {
            item.MGROUP_NAME = equipmentContext.ExchangeProfessionalGroupName(item.MGROUP_ID);
            if (item.EQUIPMENT_ID.IsNullOrEmpty())
            {
                continue;
            }
            //将申购的设备ID字段整理成数组
            var arrEquipmentId = item.EQUIPMENT_ID.TrimStart(',').TrimEnd(',').Split(',');
            equipments.ForEach(equipment =>
            {
                if (arrEquipmentId.Contains(equipment.EQUIPMENT_ID))
                {
                    item.EQUIPMENT_LIST += equipment.EQUIPMENT_CODE + ";";
                    if(item.SUBSCRIBE_STATE == "合同已签订" && item.SUBSCRIBE_STATE == "通过" || item.SUBSCRIBE_STATE == "仪器已安装")
                    {
                        if (equipment.eMS_INSTALL_INFO != null)
                        {
                            _dbContext.Db.Updateable<EMS_SUBSCRIBE_INFO>()
                                .SetColumns(p => new EMS_SUBSCRIBE_INFO
                                {
                                    SUBSCRIBE_STATE = "仪器已安装"
                                }).Where(p => p.SUBSCRIBE_ID == item.SUBSCRIBE_ID)
                                .ExecuteCommand();

                        }
                        else if (equipment.eMS_PURCHASE_INFO != null)
                        {
                            _dbContext.Db.Updateable<EMS_SUBSCRIBE_INFO>().SetColumns(p => new EMS_SUBSCRIBE_INFO
                            {
                                SUBSCRIBE_STATE = "合同已签订"
                            }).Where(p => p.SUBSCRIBE_ID == item.SUBSCRIBE_ID).ExecuteCommand();

                        }
                    }
                }
            });
        }
        return result.OrderByDescending(i => i.SUBSCRIBE_DATE).ToList();
    }

    /// <summary>
    /// 确定生安设备数据范围
    /// </summary>
    /// <param name="labId"></param>
    /// <param name="smblLabId"></param>
    /// <returns></returns>
    public void AsSmblforPgroups(string? labId ,string? smblLabId)
    {
        var pgids = new List<string>();
        var smblLabs = _authorityContext.GetUserSmbls()
            .Where(x=>x.LAB_ID != null)
            .WhereIF(labId.IsNotNullOrEmpty(),x=>x.LAB_ID  == labId)
            .WhereIF(smblLabId.IsNotNullOrEmpty(),x=>x.SMBL_LAB_ID == smblLabId)
            .Where(x => x.SMBL_LAB_STATE == "1")
            .ToList();
        _smblLabs.AddRange(smblLabs);
        foreach (var smblLab in smblLabs)
        {
            if (smblLab.PGROUP_SID.IsNotNullOrEmpty())
            {
                pgids.AddRange(smblLab.PGROUP_SID.Split(","));
            }
        }
        pgids = pgids.Distinct().ToList();
        if (!pgids.Any())
        {
            return;
        }
        var pgroups = _dbContext.Db.Queryable<SYS6_INSPECTION_PGROUP>()
            .Where(x=>pgids.Contains(x.PGROUP_ID))
            .Where(x => x.PGROUP_STATE == "1").ToList();
        _pgroups.AddRange(pgroups);
    }


    /// <summary>
    /// 查询生安设备档案列表
    /// </summary>
    /// <param name="state">设备状态</param>
    /// <param name="type">设备类型</param>
    /// <param name="keyWord">查询关键字</param>
    /// <param name="smblEquipmentClass">生安设备类型</param>
    /// <returns></returns>
    public List<EquipmentInfoDto> Getequipments(string state, string type, string keyWord ,string smblEquipmentClass)
    {
            var equimentContext = new EquipmentContext(_dbContext);
            var equipments = new List<EMS_EQUIPMENT_INFO>();
            _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>()
                            .Where(x=>x.UNIT_ID != null)
                            .Where(x=>x.EQUIPMENT_CODE != null)
                            .Where(p=>_smblLabs.Select(x=>x.SMBL_LAB_ID).Contains(p.SMBL_LAB_ID))
                            .WhereIF(state.IsNotNullOrEmpty(), x => x.EQUIPMENT_STATE == state)
                            .WhereIF(type.IsNotNullOrEmpty(), x => x.EQUIPMENT_CLASS == type)
                            .WhereIF(keyWord.IsNotNullOrEmpty(), x => x.EQUIPMENT_NAME.ToLower().Contains(keyWord.ToLower()) 
                                                                      || x.EQUIPMENT_NUM.ToLower().Contains(keyWord.ToLower()) 
                                                                      || x.EQUIPMENT_CODE.ToLower().Contains(keyWord.ToLower()) 
                                                                      || x.MANUFACTURER.ToLower().Contains(keyWord.ToLower()) 
                                                                      || x.DEALER.ToLower().Contains(keyWord.ToLower()))
                            .Includes(i => i.eMS_WORK_PLAN)
                            .Includes(i => i.eMS_MAINTAIN_INFO)
                            .Includes(i => i.eMS_CORRECT_INFO)
                            .Includes(i => i.eMS_COMPARISON_INFO)
                            .Includes(i => i.eMS_VERIFICATION_INFO)
                            .ForEach(it => equipments.Add(it));
            
            if (smblEquipmentClass.IsNotNullOrEmpty())
            {
                if (smblEquipmentClass == "0")
                {
                    smblEquipmentClass = null;
                }
                equipments = equipments.Where(x => x.SMBL_CLASS == smblEquipmentClass).ToList();

            }
            equimentContext.Injection(equipments);
            var result = equimentContext.equipments.Select(equipment => new EquipmentInfoDto
            {
                EQUIPMENT_CLASS_NAME = equimentContext.ExchangeEquipmentClass(equipment.EQUIPMENT_CLASS, equipment.EQUIPMENT_TYPE),
                EQUIPMENT_STATE_VALUE = equimentContext.ExchangeEquipmentState(equipment.EQUIPMENT_STATE),
                EQUIPMENT_STATE = equipment.EQUIPMENT_STATE,
                EQUIPMENT_ID = equipment.EQUIPMENT_ID,
                EQUIPMENT_NAME = equipment.EQUIPMENT_NAME,
                EQUIPMENT_ENAME = equipment.EQUIPMENT_ENAME,
                HOSPITAL_NAME = equimentContext.ExchangeHosptailName(equipment.HOSPITAL_ID),
                LAB_NAME = equimentContext.ExchangeLabName(equipment.LAB_ID),
                EQUIPMENT_MODEL = equipment.EQUIPMENT_MODEL,
                DEPT_SECTION_NO = equipment.DEPT_SECTION_NO,
                REGISTRATION_NUM = equipment.REGISTRATION_NUM,
                REGISTRATION_ENUM = equipment.REGISTRATION_ENUM,
                EQUIPMENT_CODE = equipment.EQUIPMENT_CODE,
                KEEP_PERSON = equipment.KEEP_PERSON,
                SECTION_NO = equipment.SECTION_NO,
                INSTALL_AREA = equipment.INSTALL_AREA,
                SERIAL_NUMBER = equipment.SERIAL_NUMBER,
                VEST_PIPELINE = equipments.Where(x => x.EQUIPMENT_ID == equipment.VEST_PIPELINE).FirstOrDefault()?.EQUIPMENT_CODE,
                EQUIPMENT_NUM = equipment.EQUIPMENT_NUM,
                EQUIPMENT_CLASS = equimentContext.ExchangeEquipmentClass(equipment.EQUIPMENT_CLASS, equipment.EQUIPMENT_TYPE),
                CONTACT_PHONE = equipment.CONTACT_PHONE,
                MGROUP_NAME = equimentContext.ExchangeProfessionalGroupName(equipment.UNIT_ID),
                MANUFACTURER = equipment.MANUFACTURER,
                DEALER = equipment.DEALER,
                ENABLE_TIME = equipment.ENABLE_TIME != null ? equipment.ENABLE_TIME.Value.ToString("yyyy-MM-dd") : "",
                INSTALL_DATE = equipment.INSTALL_DATE.IsNotNullOrEmpty() ? DateTime.Parse(equipment.INSTALL_DATE).ToString("yyyy-MM-dd") : "",
                MAINTAIN_DATE = equipment.LAST_MAINTAIN_DATE,
                NEXT_MAINTAIN_DATE = equipment.NEXT_MAINTAIN_DATE,
                COMPARISON_DATE = equipment.LAST_COMPARISON_DATE,
                NEXT_COMPARISON_DATE = equipment.NEXT_COMPARISON_DATE,
                VERIFICATION_DATE = equipment.LAST_VERIFICATION_DATE,
                NEXT_VERIFICATION_DATE = equipment.NEXT_VERIFICATION_DATE,
                CORRECT_DATE = equipment.LAST_CORRECT_DATE,
                NEXT_CORRECT_DATE = equipment.NEXT_CORRECT_DATE,
                MAINTAIN_INTERVALS = equipment.MAINTAIN_INTERVALS,
                CORRECT_INTERVALS = equipment.CORRECT_INTERVALS,
                COMPARISON_INTERVALS = equipment.COMPARISON_INTERVALS,
                VERIFICATION_INTERVALS = equipment.VERIFICATION_INTERVALS,
                IS_HIDE = equipment.IS_HIDE == "1" ? "1" : "0",
                REMARK = equipment.REMARK,
                EQ_SERVICE_LIFE = equipment.EQ_SERVICE_LIFE,
                SMBL_STATE = equipment.SMBL_STATE,
                SMBL_CLASS = equimentContext.ExchangeEquipmentSmblClass(equipment.SMBL_CLASS),
                SMBL_FLAG = equipment.SMBL_FLAG =="1" ? "1": "0",
                SMBL_LAB_ID = equipment.SMBL_LAB_ID,
                SMBL_LAB_NAME = equimentContext.ExchangeSmblLabName(equipment.SMBL_LAB_ID),
                UNIT_ID = equipment.UNIT_ID,
                LAB_ID = equipment.LAB_ID,
                EQUIPMENT_STAT_SBML = $"{(equipment.SMBL_FLAG =="1" ? "1": "")}{equipment.EQUIPMENT_STATE}"
            }).OrderBy(x => x.EQUIPMENT_CLASS)
                .ThenBy(x => x.EQUIPMENT_CODE)
                .ToList();
            
            
            return result;
    }

    /// <summary>
    /// 查询生安设备工作计划列表
    /// </summary>
    /// <param name="keyword"></param>
    /// <param name="equipmentClass">设备类型</param>
    /// <param name="smblEquipmentClass">生安设备类型</param>
    /// <returns></returns>
    public List<EmsWorkPlanDto> GetWorkPlanDtos(string keyword, string equipmentClass ,string smblEquipmentClass)
    {
        var transactions = _dbContext.Db.Queryable<TIM_FORM_MAIN_INFO>()
                .InnerJoin<TIM_WORK_FORM>((main, form) => main.FORM_ID == form.FORM_ID)
                .InnerJoin<TIM_WORK_FORM_VER>((main, form, formVer) => form.FORM_ID == formVer.FORM_ID)
                .Where((main, form, formVer) => form.FORM_STATE == "1")
                .Where((main, form, formVer) => formVer.FORM_VER_STATE == "6")
                .Select<TransactionForm>()
                .ToList();
            var transactionFormItmes = _dbContext.Db.Queryable<TIM_WORK_INFO>().Select<TransactionItem>().ToList();
            
            foreach (var form in transactions)
            {
                form.CLASS_NAME = form.CLASS_ID switch
                {
                    "1" => "使用",
                    "2" => "保养",
                    "3" => "维修",
                    "4" => "校准",
                    _ => "其他"
                };

                var equipmentTransactionFormItmes =
                    transactionFormItmes.Where(x => x.FORM_VER_ID == form.FORM_VER_ID).ToList();
                foreach (var equipmentTransactionFormItme in equipmentTransactionFormItmes)
                {
                    equipmentTransactionFormItme.WORK_NAME = equipmentTransactionFormItme.WORK_NAME.Replace("\r\n", "").Trim();
                    
                    equipmentTransactionFormItme.WORK_PLAN_TYPE = equipmentTransactionFormItme.WORK_PLAN_TYPE switch
                    {
                        "1" => "日保养",
                        "2" => "月保养",
                        "3" => "季度保养",
                        "4" => "年保养",
                        _ => ""
                    };
                    equipmentTransactionFormItme.TRANSACTION_ITEM_CLASS =  equipmentTransactionFormItme.WORK_PLAN_TYPE.IsNullOrEmpty()? $"【{form.CLASS_NAME}】"  : $"【{equipmentTransactionFormItme.WORK_PLAN_TYPE}】"  ;
                }
                form.TransactionItems.AddRange(equipmentTransactionFormItmes);
            } 
            var equipments = _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>()
                    //.Where(e=>_pgroups.Select(x=>x.PGROUP_ID).Contains(e.UNIT_ID))
                    .Where(e=>_smblLabs.Select(x=>x.SMBL_LAB_ID).Contains(e.SMBL_LAB_ID))
                    .Where(e => e.EQUIPMENT_STATE == "1")  //报废和停用的不查询
                    .Where(e => e.IS_HIDE == null || e.IS_HIDE != "1")//隐藏的不查
                    .WhereIF(equipmentClass.IsNotNullOrEmpty(), e => e.EQUIPMENT_CLASS == equipmentClass)
                    //.WhereIF(smblEquipmentClass.IsNotNullOrEmpty(), e => e.SMBL_CLASS == smblEquipmentClass)
                    .WhereIF(keyword.IsNotNullOrEmpty(), e => e.EQUIPMENT_CODE.ToLower().Contains(keyword.ToLower())
                                                              || e.EQUIPMENT_NAME.ToLower().Contains(keyword.ToLower())
                                                              || e.EQUIPMENT_MODEL.ToLower().Contains(keyword.ToLower()))
                    .Includes(e => e.eMS_WORK_PLAN)
                    .Includes(e => e.eMS_MAINTAIN_INFO)
                    .Includes(e => e.eMS_CORRECT_INFO)
                    .Includes(e => e.eMS_COMPARISON_INFO)
                    .Includes(e => e.eMS_VERIFICATION_INFO)
                    
                    .OrderBy(x => x.EQUIPMENT_CODE)
                    .ToList();
                var equipmentContext = new EquipmentContext(_dbContext);
                if (smblEquipmentClass.IsNotNullOrEmpty())
                {
                    if (smblEquipmentClass == "0")
                    {
                        smblEquipmentClass = null;
                    }
                    equipments = equipments.Where(x => x.SMBL_CLASS == smblEquipmentClass).ToList();

                }
                equipmentContext.Injection(equipments);
                var result = equipmentContext.equipments.Where(x => x.eMS_WORK_PLAN != null)
                    .Select(x => new EmsWorkPlanDto()
                    {
                        WORK_PLAN_ID = x.eMS_WORK_PLAN.WORK_PLAN_ID,
                        EQUIPMENT_ID = x.EQUIPMENT_ID,
                        CURRENT_STATE = x.eMS_WORK_PLAN.CURRENT_STATE,
                        CURRENT_STATE_NAME = EnumUtils.FromID<OperationStateEnum>(x.eMS_WORK_PLAN.CURRENT_STATE).Value.ToDesc(),
                        EQUIPMENT_NAME = x.EQUIPMENT_NAME,
                        EQUIPMENT_MODEL = x.EQUIPMENT_MODEL,
                        EQUIPMENT_CODE = x.EQUIPMENT_CODE,
                        MAINTAIN_INTERVALS = x.eMS_WORK_PLAN.MAINTAIN_INTERVALS,
                        MAINTAIN_WARN_INTERVALS = x.eMS_WORK_PLAN.MAINTAIN_WARN_INTERVALS,
                        LAST_MAINTAIN_DATE = null,
                        MONTHLY_MAINTAIN = x.eMS_WORK_PLAN.MONTHLY_MAINTAIN,
                        MONTHLY_MAINTAIN_WARN = x.eMS_WORK_PLAN.MONTHLY_MAINTAIN_WARN,
                        LAST_MONTHLY_MAINTAIN_DATE = null,
                        QUARTERLY_MAINTAIN = x.eMS_WORK_PLAN.QUARTERLY_MAINTAIN,
                        QUARTERLY_MAINTAIN_WARN = x.eMS_WORK_PLAN.QUARTERLY_MAINTAIN_WARN,
                        LAST_QUARTERLY_MAINTAIN_DATE = null,
                        YEARLY_MAINTAIN = x.eMS_WORK_PLAN.YEARLY_MAINTAIN,
                        YEARLY_MAINTAIN_WARN = x.eMS_WORK_PLAN.YEARLY_MAINTAIN_WARN,
                        LAST_YEARLY_MAINTAIN_DATE = null,
                        CORRECT_INTERVALS = x.eMS_WORK_PLAN.CORRECT_INTERVALS,
                        CORRECT_WARN_INTERVALS = x.eMS_WORK_PLAN.CORRECT_WARN_INTERVALS,
                        LAST_CORRECT_INTERVALS_DATE = x.LAST_CORRECT_DATE,
                        NEXT_CORRECT_INTERVALS_DATE = x.NEXT_CORRECT_DATE,
                        CORRECT_UNIT = x.LAST_CORRECT_DEPT,
                        EQ_IN_PERSON = x.KEEP_PERSON,
                        COMPARISON_INTERVALS = x.eMS_WORK_PLAN.COMPARISON_INTERVALS,
                        COMPARISON_WARN_INTERVALS = x.eMS_WORK_PLAN.COMPARISON_WARN_INTERVALS,
                        LAST_COMPARISON_INTERVALS_DATE = x.LAST_COMPARISON_DATE,
                        NEXT_COMPARISON_INTERVALS_DATE = x.NEXT_COMPARISON_DATE,
                        OPER_PERSON = x.eMS_WORK_PLAN.OPER_PERSON,
                        OPER_TIME = x.eMS_WORK_PLAN.OPER_TIME,
                        AUDITOR_USER_NAME = "",
                        AUDITOR_TIME = null,
                        AUDITOR_CONTEXT = "",
                        REGISTRATION_NUM = x.REGISTRATION_NUM,
                        REGISTRATION_NAME = x.MANUFACTURER,
                        SMBL_FLAG = x.SMBL_FLAG == "1" ? "1" : "0"
                    }).ToList();

                CalculateMaintainNextTime(equipmentContext, result);
                GetAuditRecode(result);

                foreach (var item in result)
                {
                    item.IS_ASSOCIATED = transactions.Count(x => x.WORK_MAINID == item.EQUIPMENT_ID) > 0;
                    item.TRANSANCTIO_FORMS.AddRange(transactions.Where(x => x.WORK_MAINID == item.EQUIPMENT_ID).ToList());
                }    
                
                return result;
    }


    /// <summary>
    ///   获取报废停用列表
    /// </summary>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="smblEquipmentClass">生安设备类型</param>
    /// <param name="equipmentClass">设备类型</param>
    /// <param name="state">状态</param>
    /// <param name="applyClass">申请类型</param>
    /// <param name="equipmentKey">设备检索</param>
    /// <param name="personKey">人物检索</param>
    /// <param name="userNo">前用户处理</param>
    /// <returns></returns>
    public List<ScrapStopListDto> GetScrapStopList(DateTime startTime, DateTime endTime, string smblEquipmentClass , string equipmentClass, string state, string applyClass,
        string equipmentKey, string personKey, string? userNo)
    {
        var equipmentContext = new EquipmentContext(_dbContext);
            //设备停用列表
            var q = _dbContext.Db.Queryable<EMS_SCRAP_INFO>()
                .InnerJoin<EMS_EQUIPMENT_INFO>((a, b) => a.EQUIPMENT_ID == b.EQUIPMENT_ID)
                .Where((a, b) =>  a.SCRAP_STATE == "1" && a.OPER_TIME <= endTime && a.OPER_TIME >= startTime)
                .Where((a,b)=>_smblLabs.Select(x=>x.SMBL_LAB_ID).Contains(b.SMBL_LAB_ID))
                .WhereIF(equipmentClass.IsNotNullOrEmpty(), (a, b) => b.EQUIPMENT_CLASS == equipmentClass)
                .WhereIF(applyClass.IsNotNullOrEmpty(), (a, b) => a.APPLY_TYPE == applyClass)
                .WhereIF(equipmentKey.IsNotNullOrEmpty(), (a, b) => b.EQUIPMENT_ENAME.ToLower().Contains(equipmentKey.ToLower())
                || b.EQUIPMENT_MODEL.ToLower().Contains(equipmentKey.ToLower())
                || b.EQUIPMENT_CODE.ToLower().Contains(equipmentKey.ToLower()))
                .WhereIF(personKey.IsNotNullOrEmpty(), (a, b) => a.OPER_PERSON.ToLower().Contains(personKey.ToLower())
                || a.APPROVE_PERSON.ToLower().Contains(personKey.ToLower())
                || a.EXAMINE_PERSON.ToLower().Contains(personKey.ToLower()))
                .Select((a, b) => new
                {
                    a.SCRAP_ID,
                    a.APPLY_STATE,
                    a.APPLY_TYPE,
                    b.LAB_ID,
                    b.UNIT_ID,
                    b.EQUIPMENT_ID,
                    b.EQUIPMENT_NAME,
                    b.EQUIPMENT_MODEL,
                    b.EQUIPMENT_CODE,
                    b.EQUIPMENT_CLASS,
                    b.MANUFACTURER,
                    b.DEALER,
                    a.OPER_PERSON,
                    a.OPER_PERSON_ID,
                    a.OPER_TIME,
                    a.SCRAP_CAUSE,
                    a.EXAMINE_PERSON,
                    a.EXAMINE_PERSON_ID,
                    a.EXAMINE_DATE,
                    a.EXAMINE_OPINION,
                    a.APPROVE_PERSON,
                    a.APPROVE_PERSON_ID,
                    a.APPROVE_OPINION,
                    a.APPROVE_DATE,
                    a.IF_RE_APPLY,
                    b.EQUIPMENT_STATE,
                    a.FILE_PATH,
                    a.SCRAP_DATE,
                    b.SMBL_CLASS,
                    b.SMBL_FLAG,
                    b.EQUIPMENT_TYPE,
                    b.SMBL_LAB_ID
                }).MergeTable();
            
            if (smblEquipmentClass.IsNotNullOrEmpty())
            {
                if (smblEquipmentClass == "0")
                {
                    smblEquipmentClass = null;
                }
                q = q.Where(p => p.SMBL_CLASS == smblEquipmentClass);
            }
            
            //若申请状态不为空
            if (state.IsNotNullOrEmpty())
            {
                if (state == "4")
                {
                    q = q.Where(p => p.APPLY_STATE == "4" || p.APPLY_STATE == "5");
                }
                else
                {
                    q = q.Where(p => p.APPLY_STATE == state);
                }
            }
           
            if (userNo.IsNotNullOrEmpty())
            {
                q = q.Where(x => userNo == (x.APPLY_STATE == "0" || x.APPLY_STATE == "4" || x.APPLY_STATE == "5"
                    ? x.OPER_PERSON_ID : x.APPLY_STATE == "1"
                        ? x.EXAMINE_PERSON_ID : x.APPLY_STATE == "2"
                            ? x.APPROVE_PERSON_ID : null));
            }
            //存储报废停用列表dto
            var scrapStopList = new List<ScrapStopListDto>();
            //关联专业组表
            var res = q.ToList();
            res.ForEach(item =>
            {
                scrapStopList.Add(new ScrapStopListDto
                {
                    ScrapStop_ID = item.SCRAP_ID,
                    STATE = item.APPLY_STATE,
                    APPLY_CLASS = item.APPLY_TYPE,
                    MGROUP_ID = item.UNIT_ID,
                    MGROUP_NAME = equipmentContext.ExchangeProfessionalGroupName(item.UNIT_ID),
                    EQUIPMENT_CLASS = equipmentContext.ExchangeEquipmentClass(item.EQUIPMENT_CLASS,item.EQUIPMENT_TYPE),
                    EQUIPMENT_ID = item.EQUIPMENT_ID,
                    EQUIPMENT_NAME = item.EQUIPMENT_NAME,
                    EQUIPMENT_MODAL = item.EQUIPMENT_MODEL,
                    EQUIPMENT_CODE = item.EQUIPMENT_CODE,
                    MANUFACTURER = item.MANUFACTURER,
                    DEALER = item.DEALER,
                    APPLY_PERSON = item.OPER_PERSON,
                    APPLY_PERSON_ID = item.OPER_PERSON_ID,
                    APPLY_DATE = item.OPER_TIME,
                    APPLY_REASON = item.SCRAP_CAUSE,
                    EXAMINE_PERSON = item.EXAMINE_PERSON,
                    EXAMINE_PERSON_ID = item.EXAMINE_PERSON_ID,
                    EXAMINE_DATE = item.EXAMINE_DATE,
                    EXAMINE_OPINION = item.EXAMINE_OPINION,
                    APPROVE_PERSON = item.APPROVE_PERSON,
                    APPROVE_PERSON_ID = item.APPROVE_PERSON_ID,
                    APPROVE_OPINION = item.APPROVE_OPINION,
                    APPROVE_DATE = item.APPROVE_DATE,
                    IF_RE_APPLY = item.IF_RE_APPLY,
                    DEAL_PERSON_ID = item.APPLY_STATE == "0" ? item.OPER_PERSON_ID :
                    item.APPLY_STATE == "1" ? item.EXAMINE_PERSON_ID :
                    item.APPLY_STATE == "2" ? item.APPROVE_PERSON_ID :
                    (item.APPLY_STATE == "4" || item.APPLY_STATE == "5") ?
                    item.OPER_PERSON_ID : "",
                    REVOKE_PERSON_ID = item.APPLY_STATE == "1" ? item.OPER_PERSON_ID :
                    (item.APPLY_STATE == "2" || item.APPLY_STATE == "4") ? item.EXAMINE_PERSON_ID :
                    (item.APPLY_STATE == "3" || item.APPLY_STATE == "5") ? item.APPROVE_PERSON_ID :
                    "",
                    EQUIPMENT_STATE = item.EQUIPMENT_STATE,
                    FILE_PATH = item.FILE_PATH + ".pdf",
                    SCRAP_DATE = item.SCRAP_DATE,
                    SMBL_FLAG = item.SMBL_FLAG == "1" ? "1":"0" ,
                    SMBL_LAB_ID = item.SMBL_LAB_ID
                });
            });
            return scrapStopList.OrderByDescending(P => P.APPLY_DATE).ToList();
    }


    /// <summary>
    /// 获取生安待报废停用的设备列表
    /// </summary>
    /// <param name="equipmentClass">设备类型</param>
    /// <param name="keyword">查询</param>
    /// <param name="smblFlag">生安标识</param>
    /// <returns></returns>
    public List<EMS_EQUIPMENT_INFO> GetEquipmentApplyList(string equipmentClass, string keyword)
        {
            var equipmentContext = new EquipmentContext(_dbContext);
            var deactivationScrappingContext = new DeactivationScrappingContext(_dbContext);
            deactivationScrappingContext.AsEquipment();
            var equipments = deactivationScrappingContext.GetEquipments();

            equipments = equipments
                    //.Where(e=>_pgroups.Select(x=>x.PGROUP_ID).Contains(e.UNIT_ID))
                    .Where(p=>_smblLabs.Select(x=>x.SMBL_LAB_ID).Contains(p.SMBL_LAB_ID))
                    .WhereIF(equipmentClass.IsNotNullOrEmpty(), e => e.EQUIPMENT_CLASS == equipmentClass)
                    .Where(e => e.eMS_SCRAP_INFO.Count() == 0 || e.eMS_SCRAP_INFO.Count(x => x.APPLY_STATE == "3") > 0)
                    .ToList();
            
            foreach (var equipment in equipments)
            {
                equipment.EQUIPMENT_CLASS = equipmentContext.ExchangeEquipmentClass(equipment.EQUIPMENT_CLASS, equipment.EQUIPMENT_TYPE);
                equipment.MGROUP_NAME = equipmentContext.ExchangeProfessionalGroupName(equipment.UNIT_ID);
            }
            
            if (keyword.IsNotNullOrEmpty())
            {
                equipments = equipments
                .Where(x => x.EQUIPMENT_NAME is not null)
                .Where(x => x.EQUIPMENT_MODEL is not null)
                .Where(x => x.EQUIPMENT_CODE is not null)
                .WhereIF(keyword.IsNotNullOrEmpty(), p => p.EQUIPMENT_NAME.ToLower().Contains(keyword.ToLower())
                || p.EQUIPMENT_MODEL.ToLower().Contains(keyword.ToLower())
                || p.EQUIPMENT_CODE.ToLower().Contains(keyword.ToLower()))
                .ToList();
            }

            return equipments;

        }
    
    /// <summary>
    /// 计算下一次保养时间
    /// </summary>
    /// <param name="emsWorkPlan"></param>
    private void CalculateMaintainNextTime(EquipmentContext equipmentContext, List<EmsWorkPlanDto> plans)
    {

        foreach (var plan in plans)
        {
            var equipment = equipmentContext.GetEquipment(plan.EQUIPMENT_ID);
            var maintenanceRecodes = equipment.eMS_MAINTAIN_INFO;
            var week = maintenanceRecodes.Where(x => x.MAINTAIN_CYCLE.Contains("周"))
           .OrderByDescending(x => x.MAINTAIN_DATE)
           .FirstOrDefault(x => x.EQUIPMENT_ID == plan.EQUIPMENT_ID);

            var month = maintenanceRecodes.Where(x => x.MAINTAIN_CYCLE.Contains("月"))
                .OrderByDescending(x => x.MAINTAIN_DATE).FirstOrDefault(x => x.EQUIPMENT_ID == plan.EQUIPMENT_ID);

            var quarter = maintenanceRecodes.Where(x => x.MAINTAIN_CYCLE.Contains("季度"))
                .OrderByDescending(x => x.MAINTAIN_DATE).FirstOrDefault(x => x.EQUIPMENT_ID == plan.EQUIPMENT_ID);

            var year = maintenanceRecodes.Where(x => x.MAINTAIN_CYCLE.Contains("年"))
                .OrderByDescending(x => x.MAINTAIN_DATE).FirstOrDefault(x => x.EQUIPMENT_ID == plan.EQUIPMENT_ID);

            //最近一次保养时间
            plan.LAST_MAINTAIN_DATE = week is not null ? week.MAINTAIN_DATE : null;
            plan.LAST_MONTHLY_MAINTAIN_DATE = month is not null ? month.MAINTAIN_DATE : null;
            plan.LAST_QUARTERLY_MAINTAIN_DATE = quarter is not null ? quarter.MAINTAIN_DATE : null;
            plan.LAST_YEARLY_MAINTAIN_DATE = year is not null ? year.MAINTAIN_DATE : null;

            //下一次保养时间
            plan.NEXT_MAINTAIN_DATE = week is not null ? week.MAINTAIN_DATE.Value.AddDays(Convert.ToDouble(plan.MAINTAIN_INTERVALS)) : null;
            plan.NEXT_MONTHLY_MAINTAIN_DATE = month is not null ? month.MAINTAIN_DATE.Value.AddDays(Convert.ToDouble(plan.MONTHLY_MAINTAIN)) : null;
            plan.NEXT_QUARTERLY_MAINTAIN_DATE = quarter is not null ? quarter.MAINTAIN_DATE.Value.AddDays(Convert.ToDouble(plan.QUARTERLY_MAINTAIN)) : null;
            plan.NEXT_YEARLY_MAINTAIN_DATE = year is not null ? year.MAINTAIN_DATE.Value.AddDays(Convert.ToDouble(plan.YEARLY_MAINTAIN)) : null;
        }
    }
    /// <summary>
    /// 查询工作计划审核记录
    /// </summary>
    /// <param name="plans"></param>
    private void GetAuditRecode(List<EmsWorkPlanDto> plans)
    {

        var workPlanIds = plans.Select(x => x.WORK_PLAN_ID);

        var recodes = _dbContext.Db.Queryable<EMS_OPER_LOG>()
            .Where(x => workPlanIds.Contains(x.OPER_MAIN_ID))
            .Where(x => x.OPER_STATE == OperationStateEnum.Audited)
            .ToList();

        foreach (var plan in plans)
        {
            var recode = recodes
                .Where(x => x.OPER_MAIN_ID == plan.WORK_PLAN_ID)
                .OrderByDescending(x => x.LAST_MTIME)
                .FirstOrDefault();
            if (recode is not null)
            {
                plan.AUDITOR_USER_NAME = recode.OPER_PERSON;
                plan.AUDITOR_TIME = recode.FIRST_RTIME;
                plan.AUDITOR_CONTEXT = recode.OPER_CONTENT;
            }
        }

    }

}