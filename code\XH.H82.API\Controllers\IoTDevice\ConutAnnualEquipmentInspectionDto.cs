﻿using XH.H82.Models.Dtos;

namespace XH.H82.API.Controllers.IoTDevice;

public class ConutAnnualEquipmentInspectionDto
{

    /// <summary>
    /// 月份
    /// </summary>
    public int Month { get; set; }
    /// <summary>
    /// 数量
    /// </summary>
    public int Count { get; set; }
    /// <summary>
    /// 需要年检的设备数量
    /// </summary>
    public List<CountWorkPlanDto>  CountWorkPlans  = new List<CountWorkPlanDto>();
}