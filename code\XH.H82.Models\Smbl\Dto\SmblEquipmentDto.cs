namespace XH.H82.Models.Smbl.Dto;

/// <summary>
/// 生安设备列表
/// </summary>
public class SmblEquipmentDto
{
        /// <summary>
        /// 设备id
        /// </summary>
        public string EQUIPMENT_ID { get; set; }
        /// <summary>
        /// 设备名称
        /// </summary>
        public string EQUIPMENT_NAME { get; set; }
        /// <summary>
        /// 设备代号
        /// </summary>
        public string EQUIPMENT_CODE { get; set; }
        /// <summary>
        /// 是否隐藏
        /// </summary>
        public string IS_HIDE { get; set; } = "0";
        /// <summary>
        /// 是否属于生安设备
        /// </summary>
        public string SMBL_FLAG { get; set; } = "0";
        /// <summary>
        /// 备案实验室id
        /// </summary>
        public string SMBL_LAB_ID { get; set; }
        /// <summary>
        /// 备案实验室名称
        /// </summary>
        public string SMBL_LAB_NAME { get; set; }
        /// <summary>
        /// 生安设备类型
        /// </summary>
        public string SMBL_CLASS { get; set; }
        /// <summary>
        /// 生安设备状态
        /// </summary>
        public string SMBL_STATE { get; set; }
}