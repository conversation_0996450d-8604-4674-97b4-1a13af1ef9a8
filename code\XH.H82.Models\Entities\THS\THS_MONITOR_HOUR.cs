﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using XH.H82.Models.Enums;

namespace XH.H82.Models.Entities.THS;

[DBOwner("XH_DATA")]
    [SugarTable("THS_MONITOR_HOUR", TableDescription = "设备检测点阶段监测汇总表")]
    public class THS_MONITOR_HOUR : BaseField
    {
        /// <summary>
        /// 阶段监测记录ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true,ColumnName = "MONITOR_HOURID")]
        public string MONITOR_HOURID{ get; set; }
        
        /// <summary>
        /// 设备ID
        /// </summary>
        [SugarColumn(ColumnName = "EQUIPMENT_ID")]
        public string EQUIPMENT_ID{ get; set; }
        
        /// <summary>
        /// 监测点ID
        /// </summary>
        [SugarColumn(ColumnName = "POINT_ID")]
        public string POINT_ID{ get; set; }
        
        /// <summary>
        /// 管理单元ID
        /// </summary>
        [SugarColumn(ColumnName = "UNIT_ID")]
        public string UNIT_ID{ get; set; }
        
        /// <summary>
        /// 医疗机构ID
        /// </summary>
        [SugarColumn(ColumnName = "HOSPITAL_ID")]
        public string HOSPITAL_ID{ get; set; }
        
        /// <summary>
        /// 监测指标ID
        /// </summary>
        [SugarColumn(ColumnName = "ITEM_ID")]
        public string ITEM_ID{ get; set; }
        
        /// <summary>
        /// 监测类型
        /// </summary>
        [SugarColumn(ColumnName = "MONITOR_TYPE")]
        public MonitorTypeEnum MONITOR_TYPE{ get; set; }
        
        /// <summary>
        /// 记录日期
        /// </summary>
        [SugarColumn(ColumnName = "MONITOR_DATE")]
        public DateTime? MONITOR_DATE{ get; set; }
        
        /// <summary>
        /// 监测记录小时范围
        /// </summary>
        [SugarColumn(ColumnName = "TIME_POINT_HOUR")]
        public string? TIME_POINT_HOUR{ get; set; }
        
        /// <summary>
        /// 总记录数
        /// </summary>
        [SugarColumn(ColumnName = "TOTAL_NUM")]
        public int TOTAL_NUM{ get; set; }
        
        /// <summary>
        /// 正常记录数
        /// </summary>
        [SugarColumn(ColumnName = "NORMAL_NUM")]
        public int NORMAL_NUM{ get; set; }
        
        /// <summary>
        /// 异常记录数
        /// </summary>
        [SugarColumn(ColumnName = "ABNORMAL_NUM")]
        public int ABNORMAL_NUM{ get; set; }
        
        /// <summary>
        /// 最高值
        /// </summary>
        [SugarColumn(ColumnName = "MAX_VALUE")]
        public double? MAX_VALUE{ get; set; }
        
        /// <summary>
        /// 最低值
        /// </summary>
        [SugarColumn(ColumnName = "MIN_VALUE")]
        public double? MIN_VALUE{ get; set; }
        
        /// <summary>
        /// 平均值
        /// </summary>
        [SugarColumn(ColumnName = "AVG_VALUE")]
        public double? AVG_VALUE{ get; set; }
        
        /// <summary>
        /// 累计时长
        /// </summary>
        [SugarColumn(ColumnName = "ACCUM_VALUE")]
        public int? ACCUM_VALUE{ get; set; }
        
        /// <summary>
        /// 报警记录数
        /// </summary>
        [SugarColumn(ColumnName = "ALARM_NUM")]
        public int ALARM_NUM{ get; set; }
        
        /// <summary>
        /// 处理人员
        /// </summary>
        [SugarColumn(ColumnName = "OPER_PERSON")]
        public string? OPER_PERSON{ get; set; }
        
        /// <summary>
        /// 处理时间
        /// </summary>
        [SugarColumn(ColumnName = "OPER_TIME")]
        public DateTime? OPER_TIME{ get; set; }
        
        /// <summary>
        /// 处理电脑
        /// </summary>
        [SugarColumn(ColumnName = "OPER_COMPUTER")]
        public string? OPER_COMPUTER{ get; set; }

        /// <summary>
        /// 状态;1未确认 2已确认
        /// </summary>
        [SugarColumn(ColumnName = "MONITOR_HOUR_STATE")]
        public string MONITOR_HOUR_STATE { get; set; } = "1";
        

        
    }