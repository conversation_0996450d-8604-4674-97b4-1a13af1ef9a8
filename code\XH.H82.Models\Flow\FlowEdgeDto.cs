﻿using Newtonsoft.Json;

namespace XH.H82.Models.Flow;

public class FlowEdgeDto
{
    [JsonProperty("branchId")]
    public string? BranchId { get; set; }

    [JsonProperty("branchSort")]
    public long? BranchSort { get; set; }

    [JsonProperty("edgeName")]
    public string EdgeName { get; set; }

    [JsonProperty("endFlowNodeId")]
    public string EndFlowNodeId { get; set; }

    [JsonProperty("startFlowNodeId")]
    public string StartFlowNodeId { get; set; }
}