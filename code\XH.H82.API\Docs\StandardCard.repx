﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="********" Ref="1" ControlType="DevExpress.XtraReports.UI.XtraReport, DevExpress.XtraReports.v22.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Name="Report3" Margins="0, 0, 161.20001, 0" PaperKind="Custom" PageWidth="278" PageHeight="180" Version="22.2" DataMember="EMS_EQUIPMENT_INFO" DataSource="#Ref-0" BackColor="255,153,180,209" BorderWidth="0">
  <Parameters>
    <Item1 Ref="3" Description="LAST_CORRECT_DATE" Name="LAST_CORRECT_DATE" />
    <Item2 Ref="4" Description="NEXT_CORRECT_DATE" Name="NEXT_CORRECT_DATE" />
  </Parameters>
  <CalculatedFields>
    <Item1 Ref="5" Name="SERIAL_NUMBER" DataMember="EMS_EQUIPMENT_INFO" />
    <Item2 Ref="6" Name="DEPT_SECTION_NO" DataMember="EMS_EQUIPMENT_INFO" />
    <Item3 Ref="7" Name="LAST_CORRECT" DisplayName="LAST_CORRECT" DataMember="EMS_EQUIPMENT_INFO" />
    <Item4 Ref="8" Name="NEXT_CORRECT" DataMember="EMS_EQUIPMENT_INFO" />
  </CalculatedFields>
  <Bands>
    <Item1 Ref="9" ControlType="TopMarginBand" Name="TopMargin" HeightF="161.20001" ForeColor="Black" BackColor="White" BorderColor="Silver">
      <Controls>
        <Item1 Ref="10" ControlType="XRLabel" Name="label21" TextAlignment="TopCenter" SizeF="72.96875,12.580002" LocationFloat="188.22998,146.53667" Font="微软雅黑, 5pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="11" EventName="BeforePrint" PropertyName="Text" Expression="[KEEP_PERSON]" />
          </ExpressionBindings>
          <StylePriority Ref="12" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="13" ControlType="XRLabel" Name="label20" TextAlignment="TopCenter" SizeF="72.96875,12.580002" LocationFloat="188.23007,121.35664" Font="微软雅黑, 5pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="14" EventName="BeforePrint" PropertyName="Text" Expression="Iif(&#xA;[EQUIPMENT_STATE] == '0', '未启用',&#xA;[EQUIPMENT_STATE] == '1', '启用',&#xA; [EQUIPMENT_STATE] == '2', '停用', &#xA;[EQUIPMENT_STATE] == '3', '报废' ,&#xA;[EQUIPMENT_STATE])&#xA;" />
          </ExpressionBindings>
          <StylePriority Ref="15" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="16" ControlType="XRLine" Name="line10" SizeF="72.96875,2.083332" LocationFloat="188.23007,159.11667" ForeColor="Black" BackColor="255,188,188,188" BorderColor="LightGray">
          <StylePriority Ref="17" UseForeColor="false" UseBackColor="false" UseBorderColor="false" />
        </Item3>
        <Item4 Ref="18" ControlType="XRLine" Name="line9" SizeF="72.96875,2.083332" LocationFloat="188.23007,133.93666" ForeColor="Black" BackColor="255,188,188,188" BorderColor="LightGray">
          <StylePriority Ref="19" UseForeColor="false" UseBackColor="false" UseBorderColor="false" />
        </Item4>
        <Item5 Ref="20" ControlType="XRLabel" Name="label19" TextAlignment="TopCenter" SizeF="72.96875,12.580002" LocationFloat="188.23006,96.17667" Font="微软雅黑, 5pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="21" EventName="BeforePrint" PropertyName="Text" Expression="[NEXT_CORRECT]" />
          </ExpressionBindings>
          <StylePriority Ref="22" UseFont="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="23" ControlType="XRLine" Name="line8" SizeF="72.96875,2.083332" LocationFloat="188.23004,108.75667" ForeColor="Black" BackColor="255,188,188,188" BorderColor="LightGray">
          <StylePriority Ref="24" UseForeColor="false" UseBackColor="false" UseBorderColor="false" />
        </Item6>
        <Item7 Ref="25" ControlType="XRLine" Name="line7" SizeF="72.96875,2.083332" LocationFloat="188.23004,83.57238" ForeColor="Black" BackColor="255,188,188,188" BorderColor="LightGray">
          <StylePriority Ref="26" UseForeColor="false" UseBackColor="false" UseBorderColor="false" />
        </Item7>
        <Item8 Ref="27" ControlType="XRLabel" Name="label17" TextAlignment="TopCenter" SizeF="72.96875,12.580002" LocationFloat="188.23003,70.99238" Font="微软雅黑, 5pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="28" EventName="BeforePrint" PropertyName="Text" Expression="[LAST_CORRECT]" />
          </ExpressionBindings>
          <StylePriority Ref="29" UseFont="false" UseTextAlignment="false" />
        </Item8>
        <Item9 Ref="30" ControlType="XRLabel" Name="label16" TextAlignment="TopCenter" SizeF="72.96875,12.580002" LocationFloat="188.23001,45.821518" Font="微软雅黑, 5pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="31" EventName="BeforePrint" PropertyName="Text" Expression="[EQUIPMENT_NAME]" />
          </ExpressionBindings>
          <StylePriority Ref="32" UseFont="false" UseTextAlignment="false" />
        </Item9>
        <Item10 Ref="33" ControlType="XRLine" Name="line6" SizeF="72.96875,2.083332" LocationFloat="188.23003,58.40152" ForeColor="Black" BackColor="255,188,188,188" BorderColor="LightGray">
          <StylePriority Ref="34" UseForeColor="false" UseBackColor="false" UseBorderColor="false" />
        </Item10>
        <Item11 Ref="35" ControlType="XRLabel" Name="label15" TextAlignment="TopCenter" SizeF="72.96875,12.580002" LocationFloat="48.00002,146.53667" Font="微软雅黑, 5pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="36" EventName="BeforePrint" PropertyName="Text" Expression="[DEPT_SECTION_NO]" />
          </ExpressionBindings>
          <StylePriority Ref="37" UseFont="false" UseTextAlignment="false" />
        </Item11>
        <Item12 Ref="38" ControlType="XRLabel" Name="label14" TextAlignment="TopCenter" SizeF="72.96875,12.580002" LocationFloat="47.999996,121.35668" Font="微软雅黑, 5pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="39" EventName="BeforePrint" PropertyName="Text" Expression="[ENABLE_TIME]" />
          </ExpressionBindings>
          <StylePriority Ref="40" UseFont="false" UseTextAlignment="false" />
        </Item12>
        <Item13 Ref="41" ControlType="XRLabel" Name="label13" TextTrimming="None" Text="责任人" TextAlignment="TopCenter" SizeF="36.197952,10.500008" LocationFloat="150.23216,150.7" Font="微软雅黑, 5pt" ForeColor="Black" BackColor="Transparent" Padding="2,2,0,0,100" BorderColor="IndianRed" BorderWidth="0">
          <StylePriority Ref="42" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorderWidth="false" UseTextAlignment="false" />
        </Item13>
        <Item14 Ref="43" ControlType="XRLabel" Name="label12" TextTrimming="None" Text="运行状态" TextAlignment="TopCenter" SizeF="36.197952,10.500008" LocationFloat="150.23216,125.51999" Font="微软雅黑, 5pt" ForeColor="Black" BackColor="Transparent" Padding="2,2,0,0,100" BorderColor="IndianRed" BorderWidth="0">
          <StylePriority Ref="44" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorderWidth="false" UseTextAlignment="false" />
        </Item14>
        <Item15 Ref="45" ControlType="XRLabel" Name="label11" TextTrimming="None" Text="下次校准" TextAlignment="TopCenter" SizeF="36.197952,10.500008" LocationFloat="150.23216,100.34" Font="微软雅黑, 5pt" ForeColor="Black" BackColor="Transparent" Padding="2,2,0,0,100" BorderColor="IndianRed" BorderWidth="0">
          <StylePriority Ref="46" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorderWidth="false" UseTextAlignment="false" />
        </Item15>
        <Item16 Ref="47" ControlType="XRLabel" Name="label10" TextTrimming="None" Text="校准日期" TextAlignment="TopCenter" SizeF="36.197952,10.500008" LocationFloat="150.23216,75.1557" Font="微软雅黑, 5pt" ForeColor="Black" BackColor="Transparent" Padding="2,2,0,0,100" BorderColor="IndianRed" BorderWidth="0">
          <StylePriority Ref="48" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorderWidth="false" UseTextAlignment="false" />
        </Item16>
        <Item17 Ref="49" ControlType="XRLabel" Name="label9" TextTrimming="None" Text="设备名称" TextAlignment="TopCenter" SizeF="36.197952,10.500008" LocationFloat="150.23216,49.98484" Font="微软雅黑, 5pt" ForeColor="Black" BackColor="Transparent" Padding="2,2,0,0,100" BorderColor="IndianRed" BorderWidth="0">
          <StylePriority Ref="50" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorderWidth="false" UseTextAlignment="false" />
        </Item17>
        <Item18 Ref="51" ControlType="XRLabel" Name="label7" TextAlignment="TopCenter" SizeF="72.96875,12.580002" LocationFloat="47.999996,96.17667" Font="微软雅黑, 5pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="52" EventName="BeforePrint" PropertyName="Text" Expression="[MANUFACTURER]" />
          </ExpressionBindings>
          <StylePriority Ref="53" UseFont="false" UseTextAlignment="false" />
        </Item18>
        <Item19 Ref="54" ControlType="XRLine" Name="line5" SizeF="72.96875,2.083332" LocationFloat="48.00002,159.11667" ForeColor="Black" BackColor="255,188,188,188" BorderColor="LightGray">
          <StylePriority Ref="55" UseForeColor="false" UseBackColor="false" UseBorderColor="false" />
        </Item19>
        <Item20 Ref="56" ControlType="XRLine" Name="line4" SizeF="72.96875,2.083332" LocationFloat="48.00002,133.93668" ForeColor="Black" BackColor="255,188,188,188" BorderColor="LightGray">
          <StylePriority Ref="57" UseForeColor="false" UseBackColor="false" UseBorderColor="false" />
        </Item20>
        <Item21 Ref="58" ControlType="XRLine" Name="line1" SizeF="72.96875,2.083332" LocationFloat="48.00001,108.75667" ForeColor="Black" BackColor="255,188,188,188" BorderColor="LightGray">
          <StylePriority Ref="59" UseForeColor="false" UseBackColor="false" UseBorderColor="false" />
        </Item21>
        <Item22 Ref="60" ControlType="XRLine" Name="line2" SizeF="72.96875,2.083332" LocationFloat="48.00001,83.57238" ForeColor="Black" BackColor="255,188,188,188" BorderColor="LightGray">
          <StylePriority Ref="61" UseForeColor="false" UseBackColor="false" UseBorderColor="false" />
        </Item22>
        <Item23 Ref="62" ControlType="XRLabel" Name="label8" TextTrimming="None" Text="实验室编号" TextAlignment="TopCenter" SizeF="46.197952,10.500015" LocationFloat="0,150.7" Font="微软雅黑, 5pt" ForeColor="Black" BackColor="Transparent" Padding="2,2,0,0,100" BorderColor="IndianRed" BorderWidth="0">
          <StylePriority Ref="63" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorderWidth="false" UseTextAlignment="false" />
        </Item23>
        <Item24 Ref="64" ControlType="XRLabel" Name="label6" TextTrimming="None" Text="启用日期" TextAlignment="TopCenter" SizeF="36.197952,10.500008" LocationFloat="10,125.52" Font="微软雅黑, 5pt" ForeColor="Black" BackColor="Transparent" Padding="2,2,0,0,100" BorderColor="IndianRed" BorderWidth="0">
          <StylePriority Ref="65" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorderWidth="false" UseTextAlignment="false" />
        </Item24>
        <Item25 Ref="66" ControlType="XRLabel" Name="label5" TextTrimming="None" Text="制造商" TextAlignment="TopCenter" SizeF="36.197952,10.500008" LocationFloat="10,100.34" Font="微软雅黑, 5pt" ForeColor="Black" BackColor="Transparent" Padding="2,2,0,0,100" BorderColor="IndianRed" BorderWidth="0">
          <StylePriority Ref="67" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorderWidth="false" UseTextAlignment="false" />
        </Item25>
        <Item26 Ref="68" ControlType="XRLabel" Name="label4" TextAlignment="TopCenter" SizeF="72.96875,12.580002" LocationFloat="48.000004,70.99238" Font="微软雅黑, 5pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="69" EventName="BeforePrint" PropertyName="Text" Expression="[DEALER]" />
          </ExpressionBindings>
          <StylePriority Ref="70" UseFont="false" UseTextAlignment="false" />
        </Item26>
        <Item27 Ref="71" ControlType="XRLabel" Name="label2" TextTrimming="None" Text="经销商" TextAlignment="TopCenter" SizeF="36.197952,10.500008" LocationFloat="9.999993,75.1557" Font="微软雅黑, 5pt" ForeColor="Black" BackColor="Transparent" Padding="2,2,0,0,100" BorderColor="IndianRed" BorderWidth="0">
          <StylePriority Ref="72" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorderWidth="false" UseTextAlignment="false" />
        </Item27>
        <Item28 Ref="73" ControlType="XRLabel" Name="label18" Text="label18" TextAlignment="TopCenter" SizeF="72.96875,12.580002" LocationFloat="48.000004,45.821518" Font="微软雅黑, 5pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="74" EventName="BeforePrint" PropertyName="Text" Expression="[EQUIPMENT_MODEL]" />
          </ExpressionBindings>
          <StylePriority Ref="75" UseFont="false" UseTextAlignment="false" />
        </Item28>
        <Item29 Ref="76" ControlType="XRLabel" Name="label3" TextTrimming="None" Text="设备型号" TextAlignment="TopCenter" SizeF="36.197952,10.500008" LocationFloat="9.999997,49.984844" Font="微软雅黑, 5pt" ForeColor="Black" BackColor="Transparent" Padding="2,2,0,0,100" BorderColor="IndianRed" BorderWidth="0">
          <StylePriority Ref="77" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorderWidth="false" UseTextAlignment="false" />
        </Item29>
        <Item30 Ref="78" ControlType="XRLine" Name="line3" SizeF="72.96875,2.083332" LocationFloat="47.999996,58.40152" ForeColor="Black" BackColor="255,188,188,188" BorderColor="LightGray">
          <StylePriority Ref="79" UseForeColor="false" UseBackColor="false" UseBorderColor="false" />
        </Item30>
        <Item31 Ref="80" ControlType="XRLabel" Name="label1" Text="设备运行状态及校准标识" TextAlignment="TopCenter" SizeF="243.47313,27.484856" LocationFloat="9.999996,9.999996" Font="微软雅黑, 8pt, style=Bold" ForeColor="DimGray" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="81" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
        </Item31>
      </Controls>
      <StylePriority Ref="82" UseForeColor="false" UseBackColor="false" UseBorderColor="false" />
    </Item1>
    <Item2 Ref="83" ControlType="DetailBand" Name="Detail" DrillDownExpanded="false" HeightF="0" BackColor="PaleTurquoise">
      <StylePriority Ref="84" UseBackColor="false" />
    </Item2>
    <Item3 Ref="85" ControlType="BottomMarginBand" Name="BottomMargin" HeightF="0" />
  </Bands>
  <ComponentStorage>
    <Item1 Ref="0" ObjectType="DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.v22.2" Name="sqlDataSource1" Base64="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" />
  </ComponentStorage>
</XtraReportsLayoutSerializer>