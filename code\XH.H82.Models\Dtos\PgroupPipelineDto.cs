﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.Dtos
{
    public class PgroupPipelineDto
    {
        /// <summary>
        /// 检验专业组ID
        /// </summary>
        public string PGROUP_ID { get; set; }
        /// <summary>
        /// 检验专业组名称
        /// </summary>
        public string PGROUP_NAME { get; set; }
        public List<PipelineDto> PipelineDto { get; set; }
    }
    public class PipelineDto
    {
        /// <summary>
        /// 流水线ID
        /// </summary>
        public string PIPELINE_ID { get; set; }
        /// <summary>
        /// 流水线名称
        /// </summary>
        public string PIPELINE_NAME { get; set; }
    }
}
