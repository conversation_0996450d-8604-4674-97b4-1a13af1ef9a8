﻿using Microsoft.AspNetCore.Http;

namespace XH.H82.Models.Dtos
{
    public class UploadFileDto
    {
        /// <summary>
        /// 设备文档分类(如：申购信息)
        /// </summary>
        public string DOC_CLASS { get; set; }
        /// <summary>
        /// 设备信息ID（如：申购ID）
        /// </summary>
        public string DOC_INFO_ID { get; set; }
        /// <summary>
        /// 文档名称
        /// </summary>
        public string DOC_NAME { get; set; }
        /// <summary>
        /// 文档后缀
        /// </summary>
        public string DOC_SUFFIX { get; set; }
        /// <summary>
        /// multipart/form-data 文件上传信息
        /// </summary>
        public IFormFile FILE { get; set; }
    }


    public class UploadStagingAreaFileDto
    {
        /// <summary>
        /// 设备文档分类(如：申购信息)
        /// </summary>
        public string DOC_CLASS { get; set; }
        /// <summary>
        /// 文档名称
        /// </summary>
        public string DOC_NAME { get; set; }
        /// <summary>
        /// 文档后缀
        /// </summary>
        public string DOC_SUFFIX { get; set; }
        /// <summary>
        /// multipart/form-data 文件上传信息
        /// </summary>
        public IFormFile FILE { get; set; }
    }
}
