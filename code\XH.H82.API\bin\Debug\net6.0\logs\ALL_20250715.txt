2025-07-15 00:00:13.739 +08:00 [INF] 【SQL执行耗时:565.6239ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-15 02:26:27.617 +08:00 [INF] Redis connection error restored
2025-07-15 02:26:27.918 +08:00 [INF] Redis connection error restored
2025-07-15 02:26:28.010 +08:00 [INF] Redis connection error restored
2025-07-15 02:26:28.010 +08:00 [INF] Redis connection error restored
2025-07-15 02:26:28.051 +08:00 [INF] Redis connection error restored
2025-07-15 02:26:28.223 +08:00 [INF] Redis connection error restored
2025-07-15 02:26:28.223 +08:00 [INF] Redis connection error restored
2025-07-15 02:26:26.352 +08:00 [ERR] Redis connection error SocketFailure
StackExchange.Redis.RedisConnectionException: SocketFailure (ReadSocketError/ConnectionReset, last-recv: 636) on **************:10090/Interactive, Idle/Faulted, last: INFO, origin: ReadFromPipe, outstanding: 1, last-read: 60s ago, last-write: 0s ago, unanswered-write: 0s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 21 of 32 available, in: 0, in-pipe: 0, out-pipe: 0, last-heartbeat: 0s ago, last-mbeat: 0s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionResetException: 远程主机强迫关闭了一个现有的连接。
 ---> System.Net.Sockets.SocketException (10054): 远程主机强迫关闭了一个现有的连接。
   at Pipelines.Sockets.Unofficial.Internal.Throw.Socket(Int32 errorCode) in /_/src/Pipelines.Sockets.Unofficial/Internal/Throw.cs:line 59
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.<GetResult>g__ThrowSocketException|10_0(SocketError e) in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 86
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.GetResult() in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 79
   at Pipelines.Sockets.Unofficial.SocketConnection.DoReceiveAsync() in /_/src/Pipelines.Sockets.Unofficial/SocketConnection.Receive.cs:line 64
   --- End of inner exception stack trace ---
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
2025-07-15 02:26:26.750 +08:00 [ERR] Redis connection error SocketFailure
StackExchange.Redis.RedisConnectionException: SocketFailure (ReadSocketError/ConnectionReset, last-recv: 20) on **************:10090/Subscription, Idle/Faulted, last: PING, origin: ReadFromPipe, outstanding: 1, last-read: 61s ago, last-write: 1s ago, unanswered-write: 1s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 13 of 32 available, in: 0, in-pipe: 0, out-pipe: 0, last-heartbeat: 1s ago, last-mbeat: 0s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionResetException: 远程主机强迫关闭了一个现有的连接。
 ---> System.Net.Sockets.SocketException (10054): 远程主机强迫关闭了一个现有的连接。
   at Pipelines.Sockets.Unofficial.Internal.Throw.Socket(Int32 errorCode) in /_/src/Pipelines.Sockets.Unofficial/Internal/Throw.cs:line 59
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.<GetResult>g__ThrowSocketException|10_0(SocketError e) in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 86
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.GetResult() in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 79
   at Pipelines.Sockets.Unofficial.SocketConnection.DoReceiveAsync() in /_/src/Pipelines.Sockets.Unofficial/SocketConnection.Receive.cs:line 64
   --- End of inner exception stack trace ---
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
2025-07-15 02:26:26.352 +08:00 [ERR] Redis connection error SocketFailure
StackExchange.Redis.RedisConnectionException: SocketFailure (ReadSocketError/ConnectionReset, last-recv: 20) on **************:10088/Subscription, Idle/Faulted, last: PING, origin: ReadFromPipe, outstanding: 1, last-read: 60s ago, last-write: 0s ago, unanswered-write: 0s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 21 of 32 available, in: 0, in-pipe: 0, out-pipe: 0, last-heartbeat: 0s ago, last-mbeat: 0s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionResetException: 远程主机强迫关闭了一个现有的连接。
 ---> System.Net.Sockets.SocketException (10054): 远程主机强迫关闭了一个现有的连接。
   at Pipelines.Sockets.Unofficial.Internal.Throw.Socket(Int32 errorCode) in /_/src/Pipelines.Sockets.Unofficial/Internal/Throw.cs:line 59
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.<GetResult>g__ThrowSocketException|10_0(SocketError e) in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 86
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.GetResult() in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 79
   at Pipelines.Sockets.Unofficial.SocketConnection.DoReceiveAsync() in /_/src/Pipelines.Sockets.Unofficial/SocketConnection.Receive.cs:line 64
   --- End of inner exception stack trace ---
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
2025-07-15 02:26:26.352 +08:00 [ERR] Redis connection error SocketFailure
StackExchange.Redis.RedisConnectionException: SocketFailure (ReadSocketError/ConnectionReset, last-recv: 636) on **************:10089/Interactive, Idle/Faulted, last: INFO, origin: ReadFromPipe, outstanding: 1, last-read: 60s ago, last-write: 0s ago, unanswered-write: 0s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 21 of 32 available, in: 0, in-pipe: 0, out-pipe: 0, last-heartbeat: 0s ago, last-mbeat: 0s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionResetException: 远程主机强迫关闭了一个现有的连接。
 ---> System.Net.Sockets.SocketException (10054): 远程主机强迫关闭了一个现有的连接。
   at Pipelines.Sockets.Unofficial.Internal.Throw.Socket(Int32 errorCode) in /_/src/Pipelines.Sockets.Unofficial/Internal/Throw.cs:line 59
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.<GetResult>g__ThrowSocketException|10_0(SocketError e) in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 86
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.GetResult() in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 79
   at Pipelines.Sockets.Unofficial.SocketConnection.DoReceiveAsync() in /_/src/Pipelines.Sockets.Unofficial/SocketConnection.Receive.cs:line 64
   --- End of inner exception stack trace ---
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
2025-07-15 02:26:28.009 +08:00 [ERR] Redis connection error SocketFailure
StackExchange.Redis.RedisConnectionException: SocketFailure (ReadSocketError/ConnectionReset, last-recv: 322) on d02.lis-china.com:10088/Interactive, Idle/Faulted, last: INFO, origin: ReadFromPipe, outstanding: 1, last-read: 66s ago, last-write: 6s ago, unanswered-write: 6s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 30 of 32 available, in: 0, in-pipe: 0, out-pipe: 0, last-heartbeat: 1s ago, last-mbeat: 1s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionResetException: 远程主机强迫关闭了一个现有的连接。
 ---> System.Net.Sockets.SocketException (10054): 远程主机强迫关闭了一个现有的连接。
   at Pipelines.Sockets.Unofficial.Internal.Throw.Socket(Int32 errorCode) in /_/src/Pipelines.Sockets.Unofficial/Internal/Throw.cs:line 59
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.<GetResult>g__ThrowSocketException|10_0(SocketError e) in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 86
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.GetResult() in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 79
   at Pipelines.Sockets.Unofficial.SocketConnection.DoReceiveAsync() in /_/src/Pipelines.Sockets.Unofficial/SocketConnection.Receive.cs:line 64
   --- End of inner exception stack trace ---
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
2025-07-15 02:26:27.714 +08:00 [ERR] Redis connection error SocketFailure
StackExchange.Redis.RedisConnectionException: SocketFailure (ReadSocketError/ConnectionReset, last-recv: 20) on d02.lis-china.com:10088/Subscription, Idle/Faulted, last: PING, origin: ReadFromPipe, outstanding: 1, last-read: 66s ago, last-write: 6s ago, unanswered-write: 6s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 30 of 32 available, in: 0, in-pipe: 0, out-pipe: 0, last-heartbeat: 1s ago, last-mbeat: 0s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionResetException: 远程主机强迫关闭了一个现有的连接。
 ---> System.Net.Sockets.SocketException (10054): 远程主机强迫关闭了一个现有的连接。
   at Pipelines.Sockets.Unofficial.Internal.Throw.Socket(Int32 errorCode) in /_/src/Pipelines.Sockets.Unofficial/Internal/Throw.cs:line 59
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.<GetResult>g__ThrowSocketException|10_0(SocketError e) in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 86
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.GetResult() in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 79
   at Pipelines.Sockets.Unofficial.SocketConnection.DoReceiveAsync() in /_/src/Pipelines.Sockets.Unofficial/SocketConnection.Receive.cs:line 64
   --- End of inner exception stack trace ---
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
2025-07-15 02:26:27.576 +08:00 [ERR] Redis connection error SocketFailure
StackExchange.Redis.RedisConnectionException: SocketFailure (ReadSocketError/ConnectionReset, last-recv: 20) on **************:10089/Subscription, Idle/Faulted, last: PING, origin: ReadFromPipe, outstanding: 1, last-read: 62s ago, last-write: 2s ago, unanswered-write: 2s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 23 of 32 available, in: 0, in-pipe: 0, out-pipe: 0, last-heartbeat: 0s ago, last-mbeat: 0s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionResetException: 远程主机强迫关闭了一个现有的连接。
 ---> System.Net.Sockets.SocketException (10054): 远程主机强迫关闭了一个现有的连接。
   at Pipelines.Sockets.Unofficial.Internal.Throw.Socket(Int32 errorCode) in /_/src/Pipelines.Sockets.Unofficial/Internal/Throw.cs:line 59
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.<GetResult>g__ThrowSocketException|10_0(SocketError e) in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 86
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.GetResult() in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 79
   at Pipelines.Sockets.Unofficial.SocketConnection.DoReceiveAsync() in /_/src/Pipelines.Sockets.Unofficial/SocketConnection.Receive.cs:line 64
   --- End of inner exception stack trace ---
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
2025-07-15 02:26:27.576 +08:00 [ERR] Redis connection error SocketFailure
StackExchange.Redis.RedisConnectionException: SocketFailure (ReadSocketError/ConnectionReset, last-recv: 542) on **************:10088/Interactive, Idle/Faulted, last: PING, origin: ReadFromPipe, outstanding: 2, last-read: 62s ago, last-write: 1s ago, unanswered-write: 2s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 17 of 32 available, in: 0, in-pipe: 0, out-pipe: 0, last-heartbeat: 0s ago, last-mbeat: 0s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionResetException: 远程主机强迫关闭了一个现有的连接。
 ---> System.Net.Sockets.SocketException (10054): 远程主机强迫关闭了一个现有的连接。
   at Pipelines.Sockets.Unofficial.Internal.Throw.Socket(Int32 errorCode) in /_/src/Pipelines.Sockets.Unofficial/Internal/Throw.cs:line 59
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.<GetResult>g__ThrowSocketException|10_0(SocketError e) in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 86
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.GetResult() in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 79
   at Pipelines.Sockets.Unofficial.SocketConnection.DoReceiveAsync() in /_/src/Pipelines.Sockets.Unofficial/SocketConnection.Receive.cs:line 64
   --- End of inner exception stack trace ---
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
   at StackExchange.Redis.ServerEndPoint.<>c__DisplayClass43_0.<<OnConnectedAsync>g__IfConnectedAsync|0>d.MoveNext() in /_/src/StackExchange.Redis/ServerEndPoint.cs:line 126
--- End of stack trace from previous location ---
   at StackExchange.Redis.TaskExtensions.TimeoutAfter(Task task, Int32 timeoutMs) in /_/src/StackExchange.Redis/TaskExtensions.cs:line 50
   at StackExchange.Redis.ConnectionMultiplexer.WaitAllIgnoreErrorsAsync(String name, Task[] tasks, Int32 timeoutMilliseconds, ILogger log, String caller, Int32 callerLineNumber) in /_/src/StackExchange.Redis/ConnectionMultiplexer.cs:line 516
2025-07-15 02:26:28.293 +08:00 [INF] Redis connection error restored
2025-07-15 02:27:06.778 +08:00 [ERR] Redis connection error SocketFailure
StackExchange.Redis.RedisConnectionException: SocketFailure (ReadSocketError/ConnectionReset, last-recv: 542) on **************:10088/Interactive, Idle/Faulted, last: INFO, origin: ReadFromPipe, outstanding: 1, last-read: 60s ago, last-write: 0s ago, unanswered-write: 0s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 26 of 32 available, in: 0, in-pipe: 0, out-pipe: 0, last-heartbeat: 0s ago, last-mbeat: 0s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionResetException: 远程主机强迫关闭了一个现有的连接。
 ---> System.Net.Sockets.SocketException (10054): 远程主机强迫关闭了一个现有的连接。
   at Pipelines.Sockets.Unofficial.Internal.Throw.Socket(Int32 errorCode) in /_/src/Pipelines.Sockets.Unofficial/Internal/Throw.cs:line 59
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.<GetResult>g__ThrowSocketException|10_0(SocketError e) in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 86
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.GetResult() in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 79
   at Pipelines.Sockets.Unofficial.SocketConnection.DoReceiveAsync() in /_/src/Pipelines.Sockets.Unofficial/SocketConnection.Receive.cs:line 64
   --- End of inner exception stack trace ---
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
2025-07-15 02:27:06.778 +08:00 [ERR] Redis connection error SocketFailure
StackExchange.Redis.RedisConnectionException: SocketFailure (ReadSocketError/ConnectionReset, last-recv: 542) on **************:10088/Interactive, Idle/Faulted, last: INFO, origin: ReadFromPipe, outstanding: 1, last-read: 60s ago, last-write: 0s ago, unanswered-write: 0s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 22 of 32 available, in: 0, in-pipe: 0, out-pipe: 0, last-heartbeat: 0s ago, last-mbeat: 0s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionResetException: 远程主机强迫关闭了一个现有的连接。
 ---> System.Net.Sockets.SocketException (10054): 远程主机强迫关闭了一个现有的连接。
   at Pipelines.Sockets.Unofficial.Internal.Throw.Socket(Int32 errorCode) in /_/src/Pipelines.Sockets.Unofficial/Internal/Throw.cs:line 59
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.<GetResult>g__ThrowSocketException|10_0(SocketError e) in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 86
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.GetResult() in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 79
   at Pipelines.Sockets.Unofficial.SocketConnection.DoReceiveAsync() in /_/src/Pipelines.Sockets.Unofficial/SocketConnection.Receive.cs:line 64
   --- End of inner exception stack trace ---
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
2025-07-15 02:27:06.995 +08:00 [ERR] Redis connection error SocketFailure
StackExchange.Redis.RedisConnectionException: SocketFailure (ReadSocketError/ConnectionReset, last-recv: 20) on **************:10088/Subscription, Idle/Faulted, last: PING, origin: ReadFromPipe, outstanding: 1, last-read: 60s ago, last-write: 0s ago, unanswered-write: 0s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 25 of 32 available, in: 0, in-pipe: 0, out-pipe: 0, last-heartbeat: 0s ago, last-mbeat: 0s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionResetException: 远程主机强迫关闭了一个现有的连接。
 ---> System.Net.Sockets.SocketException (10054): 远程主机强迫关闭了一个现有的连接。
   at Pipelines.Sockets.Unofficial.Internal.Throw.Socket(Int32 errorCode) in /_/src/Pipelines.Sockets.Unofficial/Internal/Throw.cs:line 59
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.<GetResult>g__ThrowSocketException|10_0(SocketError e) in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 86
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.GetResult() in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 79
   at Pipelines.Sockets.Unofficial.SocketConnection.DoReceiveAsync() in /_/src/Pipelines.Sockets.Unofficial/SocketConnection.Receive.cs:line 64
   --- End of inner exception stack trace ---
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
2025-07-15 02:27:07.219 +08:00 [ERR] Redis connection error SocketFailure
StackExchange.Redis.RedisConnectionException: SocketFailure (ReadSocketError/ConnectionReset, last-recv: 20) on **************:10090/Subscription, Idle/Faulted, last: PING, origin: ReadFromPipe, outstanding: 1, last-read: 60s ago, last-write: 0s ago, unanswered-write: 0s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 24 of 32 available, in: 0, in-pipe: 0, out-pipe: 0, last-heartbeat: 0s ago, last-mbeat: 0s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionResetException: 远程主机强迫关闭了一个现有的连接。
 ---> System.Net.Sockets.SocketException (10054): 远程主机强迫关闭了一个现有的连接。
   at Pipelines.Sockets.Unofficial.Internal.Throw.Socket(Int32 errorCode) in /_/src/Pipelines.Sockets.Unofficial/Internal/Throw.cs:line 59
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.<GetResult>g__ThrowSocketException|10_0(SocketError e) in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 86
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.GetResult() in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 79
   at Pipelines.Sockets.Unofficial.SocketConnection.DoReceiveAsync() in /_/src/Pipelines.Sockets.Unofficial/SocketConnection.Receive.cs:line 64
   --- End of inner exception stack trace ---
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
2025-07-15 02:27:07.220 +08:00 [ERR] Redis connection error SocketFailure
StackExchange.Redis.RedisConnectionException: SocketFailure (ReadSocketError/ConnectionReset, last-recv: 636) on **************:10090/Interactive, Idle/Faulted, last: PING, origin: ReadFromPipe, outstanding: 2, last-read: 60s ago, last-write: 0s ago, unanswered-write: 0s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 24 of 32 available, in: 0, in-pipe: 0, out-pipe: 14, last-heartbeat: 0s ago, last-mbeat: 0s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionResetException: 远程主机强迫关闭了一个现有的连接。
 ---> System.Net.Sockets.SocketException (10054): 远程主机强迫关闭了一个现有的连接。
   at Pipelines.Sockets.Unofficial.Internal.Throw.Socket(Int32 errorCode) in /_/src/Pipelines.Sockets.Unofficial/Internal/Throw.cs:line 59
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.<GetResult>g__ThrowSocketException|10_0(SocketError e) in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 86
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.GetResult() in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 79
   at Pipelines.Sockets.Unofficial.SocketConnection.DoReceiveAsync() in /_/src/Pipelines.Sockets.Unofficial/SocketConnection.Receive.cs:line 64
   --- End of inner exception stack trace ---
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
   at StackExchange.Redis.ServerEndPoint.<>c__DisplayClass43_0.<<OnConnectedAsync>g__IfConnectedAsync|0>d.MoveNext() in /_/src/StackExchange.Redis/ServerEndPoint.cs:line 126
2025-07-15 02:27:07.423 +08:00 [ERR] Redis connection error SocketFailure
StackExchange.Redis.RedisConnectionException: SocketFailure (ReadSocketError/ConnectionReset, last-recv: 636) on **************:10089/Interactive, Idle/Faulted, last: PING, origin: ReadFromPipe, outstanding: 2, last-read: 60s ago, last-write: 0s ago, unanswered-write: 1s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 22 of 32 available, in: 0, in-pipe: 0, out-pipe: 14, last-heartbeat: 1s ago, last-mbeat: 1s ago, global: 1s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionResetException: 远程主机强迫关闭了一个现有的连接。
 ---> System.Net.Sockets.SocketException (10054): 远程主机强迫关闭了一个现有的连接。
   at Pipelines.Sockets.Unofficial.Internal.Throw.Socket(Int32 errorCode) in /_/src/Pipelines.Sockets.Unofficial/Internal/Throw.cs:line 59
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.<GetResult>g__ThrowSocketException|10_0(SocketError e) in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 86
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.GetResult() in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 79
   at Pipelines.Sockets.Unofficial.SocketConnection.DoReceiveAsync() in /_/src/Pipelines.Sockets.Unofficial/SocketConnection.Receive.cs:line 64
   --- End of inner exception stack trace ---
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
   at StackExchange.Redis.ServerEndPoint.<>c__DisplayClass43_0.<<OnConnectedAsync>g__IfConnectedAsync|0>d.MoveNext() in /_/src/StackExchange.Redis/ServerEndPoint.cs:line 126
2025-07-15 02:27:07.574 +08:00 [ERR] Redis connection error SocketFailure
StackExchange.Redis.RedisConnectionException: SocketFailure (ReadSocketError/ConnectionReset, last-recv: 20) on **************:10089/Subscription, Idle/Faulted, last: PING, origin: ReadFromPipe, outstanding: 1, last-read: 61s ago, last-write: 1s ago, unanswered-write: 1s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 20 of 32 available, in: 0, in-pipe: 0, out-pipe: 0, last-heartbeat: 0s ago, last-mbeat: 0s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionResetException: 远程主机强迫关闭了一个现有的连接。
 ---> System.Net.Sockets.SocketException (10054): 远程主机强迫关闭了一个现有的连接。
   at Pipelines.Sockets.Unofficial.Internal.Throw.Socket(Int32 errorCode) in /_/src/Pipelines.Sockets.Unofficial/Internal/Throw.cs:line 59
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.<GetResult>g__ThrowSocketException|10_0(SocketError e) in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 86
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.GetResult() in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 79
   at Pipelines.Sockets.Unofficial.SocketConnection.DoReceiveAsync() in /_/src/Pipelines.Sockets.Unofficial/SocketConnection.Receive.cs:line 64
   --- End of inner exception stack trace ---
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
2025-07-15 02:27:07.574 +08:00 [ERR] Redis connection error SocketFailure
StackExchange.Redis.RedisConnectionException: SocketFailure (ReadSocketError/ConnectionReset, last-recv: 20) on **************:10090/Subscription, Idle/Faulted, last: PING, origin: ReadFromPipe, outstanding: 1, last-read: 61s ago, last-write: 1s ago, unanswered-write: 1s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 20 of 32 available, in: 0, in-pipe: 0, out-pipe: 0, last-heartbeat: 0s ago, last-mbeat: 0s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionResetException: 远程主机强迫关闭了一个现有的连接。
 ---> System.Net.Sockets.SocketException (10054): 远程主机强迫关闭了一个现有的连接。
   at Pipelines.Sockets.Unofficial.Internal.Throw.Socket(Int32 errorCode) in /_/src/Pipelines.Sockets.Unofficial/Internal/Throw.cs:line 59
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.<GetResult>g__ThrowSocketException|10_0(SocketError e) in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 86
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.GetResult() in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 79
   at Pipelines.Sockets.Unofficial.SocketConnection.DoReceiveAsync() in /_/src/Pipelines.Sockets.Unofficial/SocketConnection.Receive.cs:line 64
   --- End of inner exception stack trace ---
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
2025-07-15 02:27:07.575 +08:00 [ERR] Redis connection error SocketFailure
StackExchange.Redis.RedisConnectionException: SocketFailure (ReadSocketError/ConnectionReset, last-recv: 20) on **************:10088/Subscription, Idle/Faulted, last: PING, origin: ReadFromPipe, outstanding: 1, last-read: 61s ago, last-write: 1s ago, unanswered-write: 1s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 20 of 32 available, in: 0, in-pipe: 0, out-pipe: 0, last-heartbeat: 1s ago, last-mbeat: 0s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionResetException: 远程主机强迫关闭了一个现有的连接。
 ---> System.Net.Sockets.SocketException (10054): 远程主机强迫关闭了一个现有的连接。
   at Pipelines.Sockets.Unofficial.Internal.Throw.Socket(Int32 errorCode) in /_/src/Pipelines.Sockets.Unofficial/Internal/Throw.cs:line 59
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.<GetResult>g__ThrowSocketException|10_0(SocketError e) in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 86
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.GetResult() in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 79
   at Pipelines.Sockets.Unofficial.SocketConnection.DoReceiveAsync() in /_/src/Pipelines.Sockets.Unofficial/SocketConnection.Receive.cs:line 64
   --- End of inner exception stack trace ---
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
2025-07-15 02:27:07.702 +08:00 [INF] Redis connection error restored
2025-07-15 02:27:07.829 +08:00 [ERR] Redis connection error SocketFailure
StackExchange.Redis.RedisConnectionException: SocketFailure (ReadSocketError/ConnectionReset, last-recv: 20) on **************:10088/Subscription, Idle/Faulted, last: PING, origin: ReadFromPipe, outstanding: 1, last-read: 61s ago, last-write: 1s ago, unanswered-write: 1s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 16 of 32 available, in: 0, in-pipe: 0, out-pipe: 0, last-heartbeat: 0s ago, last-mbeat: 0s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionResetException: 远程主机强迫关闭了一个现有的连接。
 ---> System.Net.Sockets.SocketException (10054): 远程主机强迫关闭了一个现有的连接。
   at Pipelines.Sockets.Unofficial.Internal.Throw.Socket(Int32 errorCode) in /_/src/Pipelines.Sockets.Unofficial/Internal/Throw.cs:line 59
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.<GetResult>g__ThrowSocketException|10_0(SocketError e) in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 86
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.GetResult() in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 79
   at Pipelines.Sockets.Unofficial.SocketConnection.DoReceiveAsync() in /_/src/Pipelines.Sockets.Unofficial/SocketConnection.Receive.cs:line 64
   --- End of inner exception stack trace ---
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
2025-07-15 02:27:07.859 +08:00 [ERR] Redis connection error SocketFailure
StackExchange.Redis.RedisConnectionException: SocketFailure (ReadSocketError/ConnectionReset, last-recv: 542) on **************:10088/Interactive, Idle/Faulted, last: PING, origin: ReadFromPipe, outstanding: 2, last-read: 61s ago, last-write: 0s ago, unanswered-write: 1s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 15 of 32 available, in: 0, in-pipe: 0, out-pipe: 14, last-heartbeat: 0s ago, last-mbeat: 0s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionResetException: 远程主机强迫关闭了一个现有的连接。
 ---> System.Net.Sockets.SocketException (10054): 远程主机强迫关闭了一个现有的连接。
   at Pipelines.Sockets.Unofficial.Internal.Throw.Socket(Int32 errorCode) in /_/src/Pipelines.Sockets.Unofficial/Internal/Throw.cs:line 59
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.<GetResult>g__ThrowSocketException|10_0(SocketError e) in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 86
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.GetResult() in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 79
   at Pipelines.Sockets.Unofficial.SocketConnection.DoReceiveAsync() in /_/src/Pipelines.Sockets.Unofficial/SocketConnection.Receive.cs:line 64
   --- End of inner exception stack trace ---
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
2025-07-15 02:27:07.944 +08:00 [ERR] Redis connection error SocketFailure
StackExchange.Redis.RedisConnectionException: SocketFailure (ReadSocketError/ConnectionReset, last-recv: 20) on **************:10089/Subscription, Idle/Faulted, last: PING, origin: ReadFromPipe, outstanding: 1, last-read: 61s ago, last-write: 1s ago, unanswered-write: 1s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 15 of 32 available, in: 0, in-pipe: 0, out-pipe: 0, last-heartbeat: 0s ago, last-mbeat: 0s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionResetException: 远程主机强迫关闭了一个现有的连接。
 ---> System.Net.Sockets.SocketException (10054): 远程主机强迫关闭了一个现有的连接。
   at Pipelines.Sockets.Unofficial.Internal.Throw.Socket(Int32 errorCode) in /_/src/Pipelines.Sockets.Unofficial/Internal/Throw.cs:line 59
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.<GetResult>g__ThrowSocketException|10_0(SocketError e) in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 86
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.GetResult() in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 79
   at Pipelines.Sockets.Unofficial.SocketConnection.DoReceiveAsync() in /_/src/Pipelines.Sockets.Unofficial/SocketConnection.Receive.cs:line 64
   --- End of inner exception stack trace ---
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
2025-07-15 02:27:08.065 +08:00 [ERR] Redis connection error SocketFailure
StackExchange.Redis.RedisConnectionException: SocketFailure (ReadSocketError/ConnectionReset, last-recv: 636) on **************:10089/Interactive, Idle/Faulted, last: PING, origin: ReadFromPipe, outstanding: 2, last-read: 61s ago, last-write: 0s ago, unanswered-write: 1s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 14 of 32 available, in: 0, in-pipe: 0, out-pipe: 0, last-heartbeat: 0s ago, last-mbeat: 0s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionResetException: 远程主机强迫关闭了一个现有的连接。
 ---> System.Net.Sockets.SocketException (10054): 远程主机强迫关闭了一个现有的连接。
   at Pipelines.Sockets.Unofficial.Internal.Throw.Socket(Int32 errorCode) in /_/src/Pipelines.Sockets.Unofficial/Internal/Throw.cs:line 59
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.<GetResult>g__ThrowSocketException|10_0(SocketError e) in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 86
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.GetResult() in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 79
   at Pipelines.Sockets.Unofficial.SocketConnection.DoReceiveAsync() in /_/src/Pipelines.Sockets.Unofficial/SocketConnection.Receive.cs:line 64
   --- End of inner exception stack trace ---
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
   at StackExchange.Redis.ServerEndPoint.<>c__DisplayClass43_0.<<OnConnectedAsync>g__IfConnectedAsync|0>d.MoveNext() in /_/src/StackExchange.Redis/ServerEndPoint.cs:line 126
2025-07-15 02:27:08.253 +08:00 [ERR] Redis connection error SocketFailure
StackExchange.Redis.RedisConnectionException: SocketFailure (ReadSocketError/ConnectionReset, last-recv: 636) on **************:10089/Interactive, Idle/Faulted, last: INFO, origin: ReadFromPipe, outstanding: 1, last-read: 61s ago, last-write: 1s ago, unanswered-write: 1s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 10 of 32 available, in: 0, in-pipe: 0, out-pipe: 0, last-heartbeat: 0s ago, last-mbeat: 0s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionResetException: 远程主机强迫关闭了一个现有的连接。
 ---> System.Net.Sockets.SocketException (10054): 远程主机强迫关闭了一个现有的连接。
   at Pipelines.Sockets.Unofficial.Internal.Throw.Socket(Int32 errorCode) in /_/src/Pipelines.Sockets.Unofficial/Internal/Throw.cs:line 59
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.<GetResult>g__ThrowSocketException|10_0(SocketError e) in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 86
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.GetResult() in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 79
   at Pipelines.Sockets.Unofficial.SocketConnection.DoReceiveAsync() in /_/src/Pipelines.Sockets.Unofficial/SocketConnection.Receive.cs:line 64
   --- End of inner exception stack trace ---
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
2025-07-15 02:27:08.428 +08:00 [INF] Redis connection error restored
2025-07-15 02:27:08.461 +08:00 [ERR] Redis connection error SocketFailure
StackExchange.Redis.RedisConnectionException: SocketFailure (ReadSocketError/ConnectionReset, last-recv: 636) on **************:10090/Interactive, Idle/Faulted, last: PING, origin: ReadFromPipe, outstanding: 2, last-read: 61s ago, last-write: 1s ago, unanswered-write: 1s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 18 of 32 available, in: 0, in-pipe: 0, out-pipe: 0, last-heartbeat: 0s ago, last-mbeat: 0s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionResetException: 远程主机强迫关闭了一个现有的连接。
 ---> System.Net.Sockets.SocketException (10054): 远程主机强迫关闭了一个现有的连接。
   at Pipelines.Sockets.Unofficial.Internal.Throw.Socket(Int32 errorCode) in /_/src/Pipelines.Sockets.Unofficial/Internal/Throw.cs:line 59
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.<GetResult>g__ThrowSocketException|10_0(SocketError e) in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 86
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.GetResult() in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 79
   at Pipelines.Sockets.Unofficial.SocketConnection.DoReceiveAsync() in /_/src/Pipelines.Sockets.Unofficial/SocketConnection.Receive.cs:line 64
   --- End of inner exception stack trace ---
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
   at StackExchange.Redis.ServerEndPoint.<>c__DisplayClass43_0.<<OnConnectedAsync>g__IfConnectedAsync|0>d.MoveNext() in /_/src/StackExchange.Redis/ServerEndPoint.cs:line 126
2025-07-15 02:27:09.205 +08:00 [ERR] Redis connection error SocketFailure
StackExchange.Redis.RedisConnectionException: SocketFailure (ReadSocketError/ConnectionReset, last-recv: 20) on **************:10089/Subscription, Idle/Faulted, last: PING, origin: ReadFromPipe, outstanding: 1, last-read: 62s ago, last-write: 2s ago, unanswered-write: 2s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 20 of 32 available, in: 0, in-pipe: 0, out-pipe: 0, last-heartbeat: 1s ago, last-mbeat: 1s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionResetException: 远程主机强迫关闭了一个现有的连接。
 ---> System.Net.Sockets.SocketException (10054): 远程主机强迫关闭了一个现有的连接。
   at Pipelines.Sockets.Unofficial.Internal.Throw.Socket(Int32 errorCode) in /_/src/Pipelines.Sockets.Unofficial/Internal/Throw.cs:line 59
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.<GetResult>g__ThrowSocketException|10_0(SocketError e) in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 86
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.GetResult() in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 79
   at Pipelines.Sockets.Unofficial.SocketConnection.DoReceiveAsync() in /_/src/Pipelines.Sockets.Unofficial/SocketConnection.Receive.cs:line 64
   --- End of inner exception stack trace ---
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
2025-07-15 02:27:09.205 +08:00 [ERR] Redis connection error SocketFailure
StackExchange.Redis.RedisConnectionException: SocketFailure (ReadSocketError/ConnectionReset, last-recv: 636) on **************:10090/Interactive, Idle/Faulted, last: PING, origin: ReadFromPipe, outstanding: 2, last-read: 61s ago, last-write: 0s ago, unanswered-write: 1s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 5 of 32 available, in: 0, in-pipe: 0, out-pipe: 14, last-heartbeat: 0s ago, last-mbeat: 0s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionResetException: 远程主机强迫关闭了一个现有的连接。
 ---> System.Net.Sockets.SocketException (10054): 远程主机强迫关闭了一个现有的连接。
   at Pipelines.Sockets.Unofficial.Internal.Throw.Socket(Int32 errorCode) in /_/src/Pipelines.Sockets.Unofficial/Internal/Throw.cs:line 59
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.<GetResult>g__ThrowSocketException|10_0(SocketError e) in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 86
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.GetResult() in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 79
   at Pipelines.Sockets.Unofficial.SocketConnection.DoReceiveAsync() in /_/src/Pipelines.Sockets.Unofficial/SocketConnection.Receive.cs:line 64
   --- End of inner exception stack trace ---
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
   at StackExchange.Redis.ServerEndPoint.<>c__DisplayClass43_0.<<OnConnectedAsync>g__IfConnectedAsync|0>d.MoveNext() in /_/src/StackExchange.Redis/ServerEndPoint.cs:line 126
2025-07-15 02:27:09.329 +08:00 [INF] Redis connection error restored
2025-07-15 02:27:09.422 +08:00 [INF] Redis connection error restored
2025-07-15 02:27:09.422 +08:00 [ERR] Redis connection error SocketFailure
StackExchange.Redis.RedisConnectionException: SocketFailure (ReadSocketError/ConnectionReset, last-recv: 20) on **************:10090/Subscription, Idle/Faulted, last: PING, origin: ReadFromPipe, outstanding: 1, last-read: 62s ago, last-write: 2s ago, unanswered-write: 2s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 22 of 32 available, in: 0, in-pipe: 0, out-pipe: 0, last-heartbeat: 1s ago, last-mbeat: 1s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionResetException: 远程主机强迫关闭了一个现有的连接。
 ---> System.Net.Sockets.SocketException (10054): 远程主机强迫关闭了一个现有的连接。
   at Pipelines.Sockets.Unofficial.Internal.Throw.Socket(Int32 errorCode) in /_/src/Pipelines.Sockets.Unofficial/Internal/Throw.cs:line 59
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.<GetResult>g__ThrowSocketException|10_0(SocketError e) in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 86
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.GetResult() in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 79
   at Pipelines.Sockets.Unofficial.SocketConnection.DoReceiveAsync() in /_/src/Pipelines.Sockets.Unofficial/SocketConnection.Receive.cs:line 64
   --- End of inner exception stack trace ---
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
2025-07-15 02:27:09.497 +08:00 [INF] Redis connection error restored
2025-07-15 02:27:09.823 +08:00 [INF] Redis connection error restored
2025-07-15 02:27:09.823 +08:00 [INF] Redis connection error restored
2025-07-15 02:27:09.823 +08:00 [INF] Redis connection error restored
2025-07-15 02:27:09.823 +08:00 [INF] Redis connection error restored
2025-07-15 02:27:09.823 +08:00 [INF] Redis connection error restored
2025-07-15 02:27:09.879 +08:00 [INF] Redis connection error restored
2025-07-15 02:27:09.879 +08:00 [INF] Redis connection error restored
2025-07-15 02:27:10.014 +08:00 [INF] Redis connection error restored
2025-07-15 02:27:10.057 +08:00 [INF] Redis connection error restored
2025-07-15 02:27:10.057 +08:00 [INF] Redis connection error restored
2025-07-15 02:27:10.057 +08:00 [INF] Redis connection error restored
2025-07-15 02:27:10.057 +08:00 [INF] Redis connection error restored
2025-07-15 02:27:10.161 +08:00 [INF] Redis connection error restored
2025-07-15 02:27:12.527 +08:00 [ERR] Redis connection error SocketFailure
StackExchange.Redis.RedisConnectionException: SocketFailure (ReadSocketError/ConnectionReset, last-recv: 20) on d02.lis-china.com:10088/Subscription, Idle/Faulted, last: PING, origin: ReadFromPipe, outstanding: 1, last-read: 61s ago, last-write: 0s ago, unanswered-write: 0s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 30 of 32 available, in: 0, in-pipe: 0, out-pipe: 0, last-heartbeat: 0s ago, last-mbeat: 0s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionResetException: 远程主机强迫关闭了一个现有的连接。
 ---> System.Net.Sockets.SocketException (10054): 远程主机强迫关闭了一个现有的连接。
   at Pipelines.Sockets.Unofficial.Internal.Throw.Socket(Int32 errorCode) in /_/src/Pipelines.Sockets.Unofficial/Internal/Throw.cs:line 59
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.<GetResult>g__ThrowSocketException|10_0(SocketError e) in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 86
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.GetResult() in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 79
   at Pipelines.Sockets.Unofficial.SocketConnection.DoReceiveAsync() in /_/src/Pipelines.Sockets.Unofficial/SocketConnection.Receive.cs:line 64
   --- End of inner exception stack trace ---
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
2025-07-15 02:27:12.527 +08:00 [ERR] Redis connection error SocketFailure
StackExchange.Redis.RedisConnectionException: SocketFailure (ReadSocketError/ConnectionReset, last-recv: 322) on d02.lis-china.com:10088/Interactive, Idle/Faulted, last: INFO, origin: ReadFromPipe, outstanding: 1, last-read: 61s ago, last-write: 0s ago, unanswered-write: 0s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 30 of 32 available, in: 0, in-pipe: 0, out-pipe: 0, last-heartbeat: 0s ago, last-mbeat: 0s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionResetException: 远程主机强迫关闭了一个现有的连接。
 ---> System.Net.Sockets.SocketException (10054): 远程主机强迫关闭了一个现有的连接。
   at Pipelines.Sockets.Unofficial.Internal.Throw.Socket(Int32 errorCode) in /_/src/Pipelines.Sockets.Unofficial/Internal/Throw.cs:line 59
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.<GetResult>g__ThrowSocketException|10_0(SocketError e) in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 86
   at Pipelines.Sockets.Unofficial.SocketAwaitableEventArgs.GetResult() in /_/src/Pipelines.Sockets.Unofficial/SocketAwaitableEventArgs.cs:line 79
   at Pipelines.Sockets.Unofficial.SocketConnection.DoReceiveAsync() in /_/src/Pipelines.Sockets.Unofficial/SocketConnection.Receive.cs:line 64
   --- End of inner exception stack trace ---
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
2025-07-15 02:27:12.628 +08:00 [INF] Redis connection error restored
2025-07-15 02:27:12.630 +08:00 [INF] Redis connection error restored
2025-07-15 09:20:03.740 +08:00 [INF] HTTP GET /favicon.ico responded 404 in 8.2156 ms
2025-07-15 16:43:33.148 +08:00 [INF] ==>App Start..2025-07-15 16:43:33
2025-07-15 16:43:33.454 +08:00 [INF] ==>当前模块:H82,6.25.329
2025-07-15 16:43:33.460 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-15 16:43:35.919 +08:00 [INF] ==>基础连接请求完成.
2025-07-15 16:43:36.341 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-15 16:43:36.851 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-15 16:43:37.165 +08:00 [INF] ==>S01版本写入成功:6.25.329
2025-07-15 16:43:40.010 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-15 16:43:40.529 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-15 16:43:41.185 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-15 16:43:41.185 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-15 16:43:42.282 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-15 16:43:44.610 +08:00 [INF] ==>初始化完成..
2025-07-15 16:43:44.693 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-15 16:43:44.694 +08:00 [INF] 设备启用任务
2025-07-15 16:43:44.694 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-15 16:43:45.131 +08:00 [INF] 【SQL执行耗时:400.5026ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-15 16:43:45.323 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-15 16:43:45.342 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-15 16:43:45.344 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 16:43:45.345 +08:00 [INF] Hosting environment: Development
2025-07-15 16:43:45.345 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-15 16:44:56.688 +08:00 [INF] HTTP GET /api/SetUp/GetBaseDatas responded 401 in 227.3987 ms
2025-07-15 16:45:14.022 +08:00 [INF] 【SQL执行耗时:507.1789ms】

[Sql]:SELECT "DATA_CLASS_ID","HOSPITAL_ID","CLASS_ID","CLASS_NAME","CLASS_SORT","DATA_TABLE","DATA_FIELD","CLASS_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","ONE_CLASS" FROM "XH_SYS"."SYS6_BASE_DATA_CLASS"  WHERE ( "CLASS_STATE" = :CLASS_STATE0 ) 
[Pars]:
[Name]::CLASS_STATE0 [Value]:1 [Type]:String    

2025-07-15 16:45:14.670 +08:00 [INF] 【SQL执行耗时:375.2711ms】

[Sql]:SELECT "X"."DATA_ID","X"."HOSPITAL_ID","X"."LAB_ID","X"."CLASS_ID","X"."DATA_SORT","X"."DATA_CNAME","X"."DATA_ENAME","X"."HIS_ID","X"."CUSTOM_CODE","X"."SPELL_CODE","X"."DATA_STATE","X"."FIRST_RPERSON","X"."FIRST_RTIME","X"."LAST_MPERSON","X"."LAST_MTIME","X"."REMARK","X"."DATA_SNAME","X"."DATA_SOURCE","X"."ONE_CLASS","X"."DATA_UNAME","X"."IF_REPEAT","X"."SYSTEM_ID" FROM "XH_SYS"."SYS6_BASE_DATA" "X" Inner JOIN "XH_SYS"."SYS6_BASE_DATA_CLASS" "Y" ON ( "X"."CLASS_ID" = "Y"."CLASS_ID" )   WHERE ( "X"."DATA_STATE" = :DATA_STATE0 ) 
[Pars]:
[Name]::DATA_STATE0 [Value]:1 [Type]:String    

2025-07-15 16:45:16.581 +08:00 [INF] 【SQL执行耗时:346.6476ms】

[Sql]:SELECT "DATA_ID","HOSPITAL_ID","MODULE_ID","FATHER_ID","CLASS_ID","DATA_SORT","DATA_NAME","DATA_SNAME","DATA_ENAME","STANDART_ID","CUSTOM_CODE","SPELL_CODE","STATE_FLAG","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","ADDN_CONFIG_JSON" FROM "XH_OA"."OA_BASE_DATA"  WHERE ( "HOSPITAL_ID" = :HOSPITAL_ID0 )  AND ( "STATE_FLAG" <> :STATE_FLAG1 ) 
[Pars]:
[Name]::HOSPITAL_ID0 [Value]:33A001 [Type]:String    
[Name]::STATE_FLAG1 [Value]:2 [Type]:String    

2025-07-15 16:45:17.283 +08:00 [INF] 【SQL执行耗时:368.2464ms】

[Sql]:SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON" FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT" 

2025-07-15 16:45:17.386 +08:00 [INF] HTTP GET /api/SetUp/GetBaseDatas responded 200 in 7279.6983 ms
2025-07-15 16:45:17.388 +08:00 [INF] 【接口超时阀值预警】 [a9bb1a2c60035c3ea1e6af528e9851ca]接口/api/SetUp/GetBaseDatas,耗时:[7280]毫秒
2025-07-15 16:45:33.246 +08:00 [INF] 【SQL执行耗时:378.798ms】

[Sql]:SELECT "DATA_CLASS_ID","HOSPITAL_ID","CLASS_ID","CLASS_NAME","CLASS_SORT","DATA_TABLE","DATA_FIELD","CLASS_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","ONE_CLASS" FROM "XH_SYS"."SYS6_BASE_DATA_CLASS"  WHERE ( "CLASS_STATE" = :CLASS_STATE0 ) 
[Pars]:
[Name]::CLASS_STATE0 [Value]:1 [Type]:String    

2025-07-15 16:46:17.850 +08:00 [INF] 【SQL执行耗时:420.6624ms】

[Sql]:SELECT "X"."DATA_ID","X"."HOSPITAL_ID","X"."LAB_ID","X"."CLASS_ID","X"."DATA_SORT","X"."DATA_CNAME","X"."DATA_ENAME","X"."HIS_ID","X"."CUSTOM_CODE","X"."SPELL_CODE","X"."DATA_STATE","X"."FIRST_RPERSON","X"."FIRST_RTIME","X"."LAST_MPERSON","X"."LAST_MTIME","X"."REMARK","X"."DATA_SNAME","X"."DATA_SOURCE","X"."ONE_CLASS","X"."DATA_UNAME","X"."IF_REPEAT","X"."SYSTEM_ID" FROM "XH_SYS"."SYS6_BASE_DATA" "X" Inner JOIN "XH_SYS"."SYS6_BASE_DATA_CLASS" "Y" ON ( "X"."CLASS_ID" = "Y"."CLASS_ID" )   WHERE ( "X"."DATA_STATE" = :DATA_STATE0 ) 
[Pars]:
[Name]::DATA_STATE0 [Value]:1 [Type]:String    

2025-07-15 16:46:21.496 +08:00 [INF] 【SQL执行耗时:377.0639ms】

[Sql]:SELECT "DATA_ID","HOSPITAL_ID","MODULE_ID","FATHER_ID","CLASS_ID","DATA_SORT","DATA_NAME","DATA_SNAME","DATA_ENAME","STANDART_ID","CUSTOM_CODE","SPELL_CODE","STATE_FLAG","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","ADDN_CONFIG_JSON" FROM "XH_OA"."OA_BASE_DATA"  WHERE ( "HOSPITAL_ID" = :HOSPITAL_ID0 )  AND ( "STATE_FLAG" <> :STATE_FLAG1 ) 
[Pars]:
[Name]::HOSPITAL_ID0 [Value]:33A001 [Type]:String    
[Name]::STATE_FLAG1 [Value]:2 [Type]:String    

2025-07-15 16:46:23.398 +08:00 [INF] 【SQL执行耗时:353.0947ms】

[Sql]:SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON" FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT" 

2025-07-15 16:46:26.030 +08:00 [INF] HTTP GET /api/SetUp/GetBaseDatas responded 200 in 56993.7804 ms
2025-07-15 16:46:26.031 +08:00 [INF] 【接口超时阀值预警】 [124ce17db3005d06bf9bd29038d3271f]接口/api/SetUp/GetBaseDatas,耗时:[56994]毫秒
2025-07-15 16:47:53.613 +08:00 [INF] HTTP GET /api/SetUp/GetBaseDatas responded 401 in 6.5519 ms
2025-07-15 16:47:55.047 +08:00 [INF] HTTP GET /api/SetUp/GetBaseDatas responded 401 in 0.9131 ms
2025-07-15 16:48:06.212 +08:00 [INF] 【SQL执行耗时:374.7527ms】

[Sql]:SELECT "DATA_CLASS_ID","HOSPITAL_ID","CLASS_ID","CLASS_NAME","CLASS_SORT","DATA_TABLE","DATA_FIELD","CLASS_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","ONE_CLASS" FROM "XH_SYS"."SYS6_BASE_DATA_CLASS"  WHERE ( "CLASS_STATE" = :CLASS_STATE0 ) 
[Pars]:
[Name]::CLASS_STATE0 [Value]:1 [Type]:String    

2025-07-15 16:48:09.576 +08:00 [INF] 【SQL执行耗时:748.8066ms】

[Sql]:SELECT "X"."DATA_ID","X"."HOSPITAL_ID","X"."LAB_ID","X"."CLASS_ID","X"."DATA_SORT","X"."DATA_CNAME","X"."DATA_ENAME","X"."HIS_ID","X"."CUSTOM_CODE","X"."SPELL_CODE","X"."DATA_STATE","X"."FIRST_RPERSON","X"."FIRST_RTIME","X"."LAST_MPERSON","X"."LAST_MTIME","X"."REMARK","X"."DATA_SNAME","X"."DATA_SOURCE","X"."ONE_CLASS","X"."DATA_UNAME","X"."IF_REPEAT","X"."SYSTEM_ID" FROM "XH_SYS"."SYS6_BASE_DATA" "X" Inner JOIN "XH_SYS"."SYS6_BASE_DATA_CLASS" "Y" ON ( "X"."CLASS_ID" = "Y"."CLASS_ID" )   WHERE ( "X"."DATA_STATE" = :DATA_STATE0 ) 
[Pars]:
[Name]::DATA_STATE0 [Value]:1 [Type]:String    

2025-07-15 16:48:13.059 +08:00 [INF] 【SQL执行耗时:834.7409ms】

[Sql]:SELECT "DATA_ID","HOSPITAL_ID","MODULE_ID","FATHER_ID","CLASS_ID","DATA_SORT","DATA_NAME","DATA_SNAME","DATA_ENAME","STANDART_ID","CUSTOM_CODE","SPELL_CODE","STATE_FLAG","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","ADDN_CONFIG_JSON" FROM "XH_OA"."OA_BASE_DATA"  WHERE ( "HOSPITAL_ID" = :HOSPITAL_ID0 )  AND ( "STATE_FLAG" <> :STATE_FLAG1 ) 
[Pars]:
[Name]::HOSPITAL_ID0 [Value]:33A001 [Type]:String    
[Name]::STATE_FLAG1 [Value]:2 [Type]:String    

2025-07-15 16:48:13.998 +08:00 [INF] 【SQL执行耗时:346.5142ms】

[Sql]:SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON" FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT" 

2025-07-15 16:48:16.841 +08:00 [INF] HTTP GET /api/SetUp/GetBaseDatas responded 200 in 17552.2769 ms
2025-07-15 16:48:16.841 +08:00 [INF] 【接口超时阀值预警】 [62542b0f9fde592d1d06298ff8871f19]接口/api/SetUp/GetBaseDatas,耗时:[17553]毫秒
2025-07-15 16:54:12.477 +08:00 [INF] 【SQL执行耗时:428.1989ms】

[Sql]:SELECT "DATA_CLASS_ID","HOSPITAL_ID","CLASS_ID","CLASS_NAME","CLASS_SORT","DATA_TABLE","DATA_FIELD","CLASS_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","ONE_CLASS" FROM "XH_SYS"."SYS6_BASE_DATA_CLASS"  WHERE ( "CLASS_STATE" = :CLASS_STATE0 ) 
[Pars]:
[Name]::CLASS_STATE0 [Value]:1 [Type]:String    

2025-07-15 16:54:13.077 +08:00 [INF] 【SQL执行耗时:361.1819ms】

[Sql]:SELECT "X"."DATA_ID","X"."HOSPITAL_ID","X"."LAB_ID","X"."CLASS_ID","X"."DATA_SORT","X"."DATA_CNAME","X"."DATA_ENAME","X"."HIS_ID","X"."CUSTOM_CODE","X"."SPELL_CODE","X"."DATA_STATE","X"."FIRST_RPERSON","X"."FIRST_RTIME","X"."LAST_MPERSON","X"."LAST_MTIME","X"."REMARK","X"."DATA_SNAME","X"."DATA_SOURCE","X"."ONE_CLASS","X"."DATA_UNAME","X"."IF_REPEAT","X"."SYSTEM_ID" FROM "XH_SYS"."SYS6_BASE_DATA" "X" Inner JOIN "XH_SYS"."SYS6_BASE_DATA_CLASS" "Y" ON ( "X"."CLASS_ID" = "Y"."CLASS_ID" )   WHERE ( "X"."DATA_STATE" = :DATA_STATE0 ) 
[Pars]:
[Name]::DATA_STATE0 [Value]:1 [Type]:String    

2025-07-15 16:54:14.955 +08:00 [INF] 【SQL执行耗时:357.6867ms】

[Sql]:SELECT "DATA_ID","HOSPITAL_ID","MODULE_ID","FATHER_ID","CLASS_ID","DATA_SORT","DATA_NAME","DATA_SNAME","DATA_ENAME","STANDART_ID","CUSTOM_CODE","SPELL_CODE","STATE_FLAG","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","ADDN_CONFIG_JSON" FROM "XH_OA"."OA_BASE_DATA"  WHERE ( "HOSPITAL_ID" = :HOSPITAL_ID0 )  AND ( "STATE_FLAG" <> :STATE_FLAG1 ) 
[Pars]:
[Name]::HOSPITAL_ID0 [Value]:33A001 [Type]:String    
[Name]::STATE_FLAG1 [Value]:2 [Type]:String    

2025-07-15 16:54:15.627 +08:00 [INF] 【SQL执行耗时:352.8408ms】

[Sql]:SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON" FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT" 

2025-07-15 16:54:15.892 +08:00 [INF] HTTP GET /api/SetUp/GetBaseDatas responded 200 in 3891.5189 ms
2025-07-15 16:54:15.893 +08:00 [INF] 【接口超时阀值预警】 [3c27814f6027d83c014e519d4080f1bf]接口/api/SetUp/GetBaseDatas,耗时:[3891]毫秒
2025-07-15 17:09:12.111 +08:00 [INF] ==>App Start..2025-07-15 17:09:12
2025-07-15 17:09:12.427 +08:00 [INF] ==>当前模块:H82,6.25.329
2025-07-15 17:09:12.436 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-15 17:09:14.154 +08:00 [INF] ==>基础连接请求完成.
2025-07-15 17:09:14.564 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-15 17:09:15.016 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-15 17:09:15.429 +08:00 [INF] ==>S01版本写入成功:6.25.329
2025-07-15 17:09:16.405 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-15 17:09:16.547 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-15 17:09:17.034 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-15 17:09:17.034 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-15 17:09:18.252 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-15 17:09:20.480 +08:00 [INF] ==>初始化完成..
2025-07-15 17:09:20.501 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-15 17:09:20.504 +08:00 [INF] 设备启用任务
2025-07-15 17:09:20.505 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-15 17:09:20.907 +08:00 [INF] 【SQL执行耗时:378.7836ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-15 17:09:21.056 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-15 17:09:21.071 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-15 17:09:21.072 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 17:09:21.072 +08:00 [INF] Hosting environment: Development
2025-07-15 17:09:21.073 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-15 17:09:57.963 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetToolBoxUrl responded 200 in 3820.2834 ms
2025-07-15 17:09:57.983 +08:00 [INF] 【接口超时阀值预警】 [af3261f7d3bdea113a71cc4701ecf23a]接口/api/EquipmentClassNew/GetToolBoxUrl,耗时:[3848]毫秒
2025-07-15 17:10:31.154 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetToolBoxUrl responded 204 in 49.4228 ms
2025-07-15 17:10:32.602 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetToolBoxUrl responded 204 in 36.7471 ms
2025-07-15 17:10:33.056 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetToolBoxUrl responded 204 in 36.8476 ms
2025-07-15 17:10:33.228 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetToolBoxUrl responded 204 in 38.6499 ms
2025-07-15 17:10:33.382 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetToolBoxUrl responded 204 in 35.9233 ms
2025-07-15 17:10:33.534 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetToolBoxUrl responded 204 in 36.3693 ms
2025-07-15 17:10:33.695 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetToolBoxUrl responded 204 in 45.5162 ms
2025-07-15 17:10:33.826 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetToolBoxUrl responded 204 in 35.8174 ms
2025-07-15 17:11:07.775 +08:00 [INF] ==>App Start..2025-07-15 17:11:07
2025-07-15 17:11:07.968 +08:00 [INF] ==>当前模块:H82,6.25.329
2025-07-15 17:11:07.973 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-15 17:11:09.488 +08:00 [INF] ==>基础连接请求完成.
2025-07-15 17:11:09.878 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-15 17:11:10.451 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-15 17:11:10.781 +08:00 [INF] ==>S01版本写入成功:6.25.329
2025-07-15 17:11:11.838 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-15 17:11:11.962 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-15 17:11:12.522 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-15 17:11:12.523 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-15 17:11:13.462 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-15 17:11:18.692 +08:00 [INF] ==>初始化完成..
2025-07-15 17:11:18.714 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-15 17:11:18.715 +08:00 [INF] 设备启用任务
2025-07-15 17:11:18.715 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-15 17:11:19.125 +08:00 [INF] 【SQL执行耗时:386.6496ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-15 17:11:19.267 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-15 17:11:19.286 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-15 17:11:19.287 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 17:11:19.288 +08:00 [INF] Hosting environment: Development
2025-07-15 17:11:19.288 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-15 17:11:30.895 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetToolBoxUrl responded 200 in 4379.5631 ms
2025-07-15 17:11:30.922 +08:00 [INF] 【接口超时阀值预警】 [ca849dd888b74ce34c65923d8addc905]接口/api/EquipmentClassNew/GetToolBoxUrl,耗时:[4413]毫秒
2025-07-15 17:11:33.659 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetToolBoxUrl responded 200 in 44.8588 ms
2025-07-15 17:13:28.651 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetToolBoxUrl responded 200 in 44.2056 ms
2025-07-15 17:14:19.234 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetToolBoxUrl responded 200 in 53.5092 ms
2025-07-15 17:17:07.637 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetToolBoxUrl responded 200 in 36.7453 ms
2025-07-15 17:36:22.098 +08:00 [INF] ==>App Start..2025-07-15 17:36:22
2025-07-15 17:36:22.309 +08:00 [INF] ==>当前模块:H82,6.25.329
2025-07-15 17:36:22.313 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-15 17:36:23.963 +08:00 [INF] ==>基础连接请求完成.
2025-07-15 17:36:24.386 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-15 17:36:24.959 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-15 17:36:25.388 +08:00 [INF] ==>S01版本写入成功:6.25.329
2025-07-15 17:36:26.628 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-15 17:36:26.762 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-15 17:36:27.361 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-15 17:36:27.362 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-15 17:36:28.386 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-15 17:36:30.753 +08:00 [INF] ==>初始化完成..
2025-07-15 17:36:30.785 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-15 17:36:30.789 +08:00 [INF] 设备启用任务
2025-07-15 17:36:30.790 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-15 17:36:31.250 +08:00 [INF] 【SQL执行耗时:428.9962ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-15 17:36:31.428 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-15 17:36:31.443 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-15 17:36:31.444 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 17:36:31.445 +08:00 [INF] Hosting environment: Development
2025-07-15 17:36:31.445 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-15 17:36:41.471 +08:00 [INF] 【SQL执行耗时:935.4195ms】

[Sql]: SELECT "SETUP_ID","FUNC_ID","HOSPITAL_ID","FIRST_RPERSON","SKIN_ID","LAST_MTIME","IF_DOUBLE_CLICK","FORM_QUERY_JSON","SETUP_CNAME","SETUP_SORT","SETUP_STATE","SETUP_DESC","FORM_COL_JSON","MODULE_ID","REMARK","FIRST_RTIME","LAST_MPERSON","FORM_JSON","IF_QUERY","SETUP_NAME","SETUP_CLASS"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ( "SETUP_ID" = :SETUP_ID0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::SETUP_ID0 [Value]:******** [Type]:AnsiString    

2025-07-15 17:37:46.045 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:292ms
2025-07-15 17:37:54.746 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:230ms
2025-07-15 17:37:54.751 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-15 17:39:03.162 +08:00 [INF] HTTP POST /api/EquipmentClassNew/InitBaseInfoFieldDict responded 200 in 148577.8893 ms
2025-07-15 17:39:03.167 +08:00 [INF] 【接口超时阀值预警】 [a61648fca92fec93fe668bfff3421b3e]接口/api/EquipmentClassNew/InitBaseInfoFieldDict,耗时:[148587]毫秒
2025-07-15 17:40:07.311 +08:00 [INF] 【SQL执行耗时:532.6603ms】

[Sql]: SELECT "SETUP_ID","FUNC_ID","HOSPITAL_ID","FIRST_RPERSON","SKIN_ID","LAST_MTIME","IF_DOUBLE_CLICK","FORM_QUERY_JSON","SETUP_CNAME","SETUP_SORT","SETUP_STATE","SETUP_DESC","FORM_COL_JSON","MODULE_ID","REMARK","FIRST_RTIME","LAST_MPERSON","FORM_JSON","IF_QUERY","SETUP_NAME","SETUP_CLASS"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ( "SETUP_ID" = :SETUP_ID0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::SETUP_ID0 [Value]:******** [Type]:AnsiString    

2025-07-15 17:44:32.072 +08:00 [INF] ==>App Start..2025-07-15 17:44:32
2025-07-15 17:44:32.349 +08:00 [INF] ==>当前模块:H82,6.25.329
2025-07-15 17:44:32.354 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-15 17:44:34.051 +08:00 [INF] ==>基础连接请求完成.
2025-07-15 17:44:34.508 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-15 17:44:35.036 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-15 17:44:35.411 +08:00 [INF] ==>S01版本写入成功:6.25.329
2025-07-15 17:44:36.605 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-15 17:44:36.733 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-15 17:44:37.285 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-15 17:44:37.286 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-15 17:44:38.342 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-15 17:44:40.686 +08:00 [INF] ==>初始化完成..
2025-07-15 17:44:40.710 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-15 17:44:40.714 +08:00 [INF] 设备启用任务
2025-07-15 17:44:40.714 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-15 17:44:41.848 +08:00 [INF] 【SQL执行耗时:1110.1791ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-15 17:44:42.136 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-15 17:44:42.154 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-15 17:44:42.156 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 17:44:42.157 +08:00 [INF] Hosting environment: Development
2025-07-15 17:44:42.157 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-15 17:44:46.977 +08:00 [INF] 【SQL执行耗时:378.1677ms】

[Sql]: SELECT "SETUP_ID","FUNC_ID","HOSPITAL_ID","FIRST_RPERSON","SKIN_ID","LAST_MTIME","IF_DOUBLE_CLICK","FORM_QUERY_JSON","SETUP_CNAME","SETUP_SORT","SETUP_STATE","SETUP_DESC","FORM_COL_JSON","MODULE_ID","REMARK","FIRST_RTIME","LAST_MPERSON","FORM_JSON","IF_QUERY","SETUP_NAME","SETUP_CLASS"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ( "SETUP_ID" = :SETUP_ID0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::SETUP_ID0 [Value]:******** [Type]:AnsiString    

2025-07-15 17:47:24.175 +08:00 [ERR] 未处理的异常::System.ArgumentNullException: Value cannot be null. (Parameter 'key')
   at System.Collections.Generic.Dictionary`2.FindValue(TKey key)
   at System.Collections.Generic.Dictionary`2.TryGetValue(TKey key, TValue& value)
   at XH.H82.Services.TemplateDesign.TemplateDesignService.BuildFieldDict(Items item, Dictionary`2 widgetPropsMap, Int32 sort)
   at XH.H82.Services.TemplateDesign.TemplateDesignService.<>c__DisplayClass13_0.<ExtractFieldDicts>b__3(Items item, Int32 idx)
   at System.Linq.Enumerable.SelectIterator[TSource,TResult](IEnumerable`1 source, Func`3 selector)+MoveNext()
   at XH.H82.Services.TemplateDesign.TemplateDesignService.InitBaseInfoFieldDict()
   at Castle.Proxies.Invocations.ITemplateDesignService_InitBaseInfoFieldDict.InvokeMethodOnTarget()
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.UseCacheWhenFailedAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.CacheAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at Castle.Proxies.ITemplateDesignServiceProxy.InitBaseInfoFieldDict()
   at XH.H82.API.Controllers.EquipmentClassNew.EquipmentClassNewController.InitBaseInfoFieldDict() in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\Controllers\EquipmentClassNew\EquipmentClassNewController.cs:line 205
   at lambda_method918(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-07-15 17:47:24.189 +08:00 [ERR] HTTP POST /api/EquipmentClassNew/InitBaseInfoFieldDict responded 500 in 160423.7766 ms
2025-07-15 17:47:24.193 +08:00 [INF] 【接口超时阀值预警】 [5b5493062d4d06419e248d56fe8cf2d9]接口/api/EquipmentClassNew/InitBaseInfoFieldDict,耗时:[160436]毫秒
2025-07-15 17:47:27.916 +08:00 [INF] 【SQL执行耗时:1521.3578ms】

[Sql]: SELECT "SETUP_ID","FUNC_ID","HOSPITAL_ID","FIRST_RPERSON","SKIN_ID","LAST_MTIME","IF_DOUBLE_CLICK","FORM_QUERY_JSON","SETUP_CNAME","SETUP_SORT","SETUP_STATE","SETUP_DESC","FORM_COL_JSON","MODULE_ID","REMARK","FIRST_RTIME","LAST_MPERSON","FORM_JSON","IF_QUERY","SETUP_NAME","SETUP_CLASS"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ( "SETUP_ID" = :SETUP_ID0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::SETUP_ID0 [Value]:******** [Type]:AnsiString    

2025-07-15 17:47:38.851 +08:00 [ERR] 未处理的异常::System.ArgumentNullException: Value cannot be null. (Parameter 'key')
   at System.Collections.Generic.Dictionary`2.FindValue(TKey key)
   at System.Collections.Generic.Dictionary`2.TryGetValue(TKey key, TValue& value)
   at XH.H82.Services.TemplateDesign.TemplateDesignService.BuildFieldDict(Items item, Dictionary`2 widgetPropsMap, Int32 sort)
   at XH.H82.Services.TemplateDesign.TemplateDesignService.<>c__DisplayClass13_0.<ExtractFieldDicts>b__3(Items item, Int32 idx)
   at System.Linq.Enumerable.SelectIterator[TSource,TResult](IEnumerable`1 source, Func`3 selector)+MoveNext()
   at XH.H82.Services.TemplateDesign.TemplateDesignService.InitBaseInfoFieldDict()
   at Castle.Proxies.Invocations.ITemplateDesignService_InitBaseInfoFieldDict.InvokeMethodOnTarget()
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.UseCacheWhenFailedAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.CacheAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at Castle.Proxies.ITemplateDesignServiceProxy.InitBaseInfoFieldDict()
   at XH.H82.API.Controllers.EquipmentClassNew.EquipmentClassNewController.InitBaseInfoFieldDict() in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\Controllers\EquipmentClassNew\EquipmentClassNewController.cs:line 205
   at lambda_method918(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-07-15 17:47:38.853 +08:00 [ERR] HTTP POST /api/EquipmentClassNew/InitBaseInfoFieldDict responded 500 in 12511.9434 ms
2025-07-15 17:47:38.854 +08:00 [INF] 【接口超时阀值预警】 [d60eb08e8feec8b7fbc9c58f4e72c3bd]接口/api/EquipmentClassNew/InitBaseInfoFieldDict,耗时:[12513]毫秒
2025-07-15 17:47:59.385 +08:00 [INF] 【SQL执行耗时:354.5598ms】

[Sql]: SELECT "SETUP_ID","FUNC_ID","HOSPITAL_ID","FIRST_RPERSON","SKIN_ID","LAST_MTIME","IF_DOUBLE_CLICK","FORM_QUERY_JSON","SETUP_CNAME","SETUP_SORT","SETUP_STATE","SETUP_DESC","FORM_COL_JSON","MODULE_ID","REMARK","FIRST_RTIME","LAST_MPERSON","FORM_JSON","IF_QUERY","SETUP_NAME","SETUP_CLASS"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ( "SETUP_ID" = :SETUP_ID0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::SETUP_ID0 [Value]:******** [Type]:AnsiString    

2025-07-15 17:49:09.288 +08:00 [ERR] 未处理的异常::System.ArgumentNullException: Value cannot be null. (Parameter 'key')
   at System.Collections.Generic.Dictionary`2.FindValue(TKey key)
   at System.Collections.Generic.Dictionary`2.TryGetValue(TKey key, TValue& value)
   at XH.H82.Services.TemplateDesign.TemplateDesignService.BuildFieldDict(Items item, Dictionary`2 widgetPropsMap, Int32 sort)
   at XH.H82.Services.TemplateDesign.TemplateDesignService.<>c__DisplayClass13_0.<ExtractFieldDicts>b__3(Items item, Int32 idx)
   at System.Linq.Enumerable.SelectIterator[TSource,TResult](IEnumerable`1 source, Func`3 selector)+MoveNext()
   at XH.H82.Services.TemplateDesign.TemplateDesignService.InitBaseInfoFieldDict()
   at Castle.Proxies.Invocations.ITemplateDesignService_InitBaseInfoFieldDict.InvokeMethodOnTarget()
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.UseCacheWhenFailedAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.CacheAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at Castle.Proxies.ITemplateDesignServiceProxy.InitBaseInfoFieldDict()
   at XH.H82.API.Controllers.EquipmentClassNew.EquipmentClassNewController.InitBaseInfoFieldDict() in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\Controllers\EquipmentClassNew\EquipmentClassNewController.cs:line 205
   at lambda_method918(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-07-15 17:49:09.290 +08:00 [ERR] HTTP POST /api/EquipmentClassNew/InitBaseInfoFieldDict responded 500 in 70302.1359 ms
2025-07-15 17:49:09.290 +08:00 [INF] 【接口超时阀值预警】 [79af076cfc5a8f70449035d12ec3f1a5]接口/api/EquipmentClassNew/InitBaseInfoFieldDict,耗时:[70302]毫秒
2025-07-15 17:49:12.251 +08:00 [INF] 【SQL执行耗时:430.0209ms】

[Sql]: SELECT "SETUP_ID","FUNC_ID","HOSPITAL_ID","FIRST_RPERSON","SKIN_ID","LAST_MTIME","IF_DOUBLE_CLICK","FORM_QUERY_JSON","SETUP_CNAME","SETUP_SORT","SETUP_STATE","SETUP_DESC","FORM_COL_JSON","MODULE_ID","REMARK","FIRST_RTIME","LAST_MPERSON","FORM_JSON","IF_QUERY","SETUP_NAME","SETUP_CLASS"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ( "SETUP_ID" = :SETUP_ID0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::SETUP_ID0 [Value]:******** [Type]:AnsiString    

2025-07-15 17:59:03.434 +08:00 [ERR] 未处理的异常::System.ArgumentNullException: Value cannot be null. (Parameter 'key')
   at System.Collections.Generic.Dictionary`2.FindValue(TKey key)
   at System.Collections.Generic.Dictionary`2.TryGetValue(TKey key, TValue& value)
   at XH.H82.Services.TemplateDesign.TemplateDesignService.BuildFieldDict(Items item, Dictionary`2 widgetPropsMap, Int32 sort)
   at XH.H82.Services.TemplateDesign.TemplateDesignService.<>c__DisplayClass13_0.<ExtractFieldDicts>b__3(Items item, Int32 idx)
   at System.Linq.Enumerable.SelectIterator[TSource,TResult](IEnumerable`1 source, Func`3 selector)+MoveNext()
   at XH.H82.Services.TemplateDesign.TemplateDesignService.InitBaseInfoFieldDict()
   at Castle.Proxies.Invocations.ITemplateDesignService_InitBaseInfoFieldDict.InvokeMethodOnTarget()
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.UseCacheWhenFailedAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.CacheAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at Castle.Proxies.ITemplateDesignServiceProxy.InitBaseInfoFieldDict()
   at XH.H82.API.Controllers.EquipmentClassNew.EquipmentClassNewController.InitBaseInfoFieldDict() in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\Controllers\EquipmentClassNew\EquipmentClassNewController.cs:line 205
   at lambda_method918(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-07-15 17:59:03.437 +08:00 [ERR] HTTP POST /api/EquipmentClassNew/InitBaseInfoFieldDict responded 500 in 591698.8696 ms
2025-07-15 17:59:03.437 +08:00 [INF] 【接口超时阀值预警】 [7d05b7bae8ea4692c15447e01b67df53]接口/api/EquipmentClassNew/InitBaseInfoFieldDict,耗时:[591699]毫秒
2025-07-15 17:59:13.882 +08:00 [INF] 【SQL执行耗时:343.0102ms】

[Sql]: SELECT "SETUP_ID","FUNC_ID","HOSPITAL_ID","FIRST_RPERSON","SKIN_ID","LAST_MTIME","IF_DOUBLE_CLICK","FORM_QUERY_JSON","SETUP_CNAME","SETUP_SORT","SETUP_STATE","SETUP_DESC","FORM_COL_JSON","MODULE_ID","REMARK","FIRST_RTIME","LAST_MPERSON","FORM_JSON","IF_QUERY","SETUP_NAME","SETUP_CLASS"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ( "SETUP_ID" = :SETUP_ID0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::SETUP_ID0 [Value]:******** [Type]:AnsiString    

