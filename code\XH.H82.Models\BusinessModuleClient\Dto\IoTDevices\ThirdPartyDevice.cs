using Newtonsoft.Json;

namespace XH.H82.Models.BusinessModuleClient.Dto.IoTDevices;

public class ThirdPartyDevice
{
    /// <summary>
    /// ID
    /// </summary>
    [JsonProperty("id")]
    public string Id { get; set; }
    /// <summary>
    /// 医疗设备名称
    /// </summary>
    [JsonProperty("name")]
    public string Name { get; set; }
    /// <summary>
    /// SN码 唯一
    /// </summary>
    [JsonProperty("sn")]
    public string Sn { get; set; }
    /// <summary>
    /// 设备序列号
    /// </summary>
    [JsonProperty("model")]
    public string Model { get; set; }
    /// <summary>
    /// 医疗设备类型
    /// </summary>
    [JsonProperty("type")]
    public int Type { get; set; }
    
    /// <summary>
    /// 医疗设备所在房间id
    /// </summary>
    [JsonProperty("roomId")]
    public long? RoomId { get; set; }
    /// <summary>
    /// 医疗设备所在房间名称
    /// </summary>
    [JsonProperty("roomName")]
    public string? RoomName { get; set; }
    /// <summary>
    /// 医疗设备所在房间监测点id
    /// </summary>
    [JsonProperty("checkpointId")]
    public string? CheckpointId { get; set; }
    /// <summary>
    /// 医疗设备所在监测点名称
    /// </summary>
    [JsonProperty("checkpointName")]
    public string? CheckpointName { get; set; }
    /// <summary>
    /// 医疗设备所属实验室id
    /// </summary>
    [JsonProperty("labId")]
    public long? LabId { get; set; }
    /// <summary>
    /// 医疗设备所属实验室名称
    /// </summary>
    [JsonProperty("labName")]
    public string LabName { get; set; } = "";
    /// <summary>
    /// 医疗设备是否在线 1在线  0离线
    /// </summary>
    [JsonProperty("isOnline")]
    public int IsOnline { get; set; } 
    /// <summary>
    /// 医疗设备开关状态 0离线/关机 1待机,2运行,201生物安全柜紫外灯运行,3过载
    /// </summary>
    [JsonProperty("switchStatus")]
    public int SwitchStatus { get; set; } 
    
    [JsonProperty("sensors")]
    public List<Sensor> Sensors { get; set; } = new ();
    
}