﻿namespace XH.H82.Models.EquipmengtClassNew;

/// <summary>
/// 设备分类Dto
/// </summary>
public class EquipmentClassDto
{
    /// <summary>
    /// 设备分类id
    /// </summary>
    public string ClassId{ get; set; }
    
    /// <summary>
    /// 设备分类名称
    /// </summary>
    public string ClassName{ get; set; }

    /// <summary>
    /// 父级设备分类id;父级节点为固定大类的固定id
    /// </summary>
    public string? ParentClassId{ get; set; }
    
    /// <summary>
    /// 父级设备分类名称
    /// </summary>
    public string? ParentClassName{ get; set; }
    
    /// <summary>
    /// 设备分类简称
    /// </summary>
    public string? ClassSname{ get; set; }
    
    /// <summary>
    /// 设备分类标签;0 iso 1生物安全  2POCT 3高等级
    /// </summary>
    public string? ClassTag{ get; set; }
    
    /// <summary>
    /// 设备分样式;用于存储图案颜色
    /// </summary>
    public string? ClassStyle{ get; set; }
    
    /// <summary>
    /// 设备分优先级;根节点为0 逐级递增1  结合自定义代号作模板选择权重
    /// </summary>
    public string ClassLevel{ get; set; }
    
    /// <summary>
    /// 设备分类状态;0 禁用   1在用  2删除
    /// </summary>
    public string ClassState{ get; set; }
    
    /// <summary>
    /// 设备分类状态;0 禁用   1在用  2删除
    /// </summary>
    public string ClassStateName=> ClassState switch
    {
        "0" => "禁用",
        "1" => "在用",
        "2" => "删除",
        _ => "未知"
    };
    
    /// <summary>
    /// 最后修改时间
    /// </summary>
    public DateTime? LAST_MTIME{ get; set; }
    
    /// <summary>
    /// 最后修改人员
    /// </summary>
    public string? LAST_MPERSON{ get; set; }
    
    /// <summary>
    /// 首次登记时间
    /// </summary>
    public DateTime? FIRST_RTIME{ get; set; }
    
    /// <summary>
    /// 首次登记人
    /// </summary>
    public string? FIRST_RPERSON{ get; set; }
    
    /// <summary>
    /// 档案记录
    /// </summary>
    public string? ClassArchivesName { get; set; }
    
}


/// <summary>
/// 添加设备类型模型
/// </summary>
public class AddEquipmentClassDto
{
    /// <summary>
    /// 父级设备分类id;父级节点为固定大类的固定id  
    /// </summary>
    public string ParentClassId{ get; set; }
    
    /// <summary>
    /// 设备类型名称
    /// </summary>
    public string ClassName { get; set; }
    
    /// <summary>
    /// 设备分类简称
    /// </summary>
    public string? ClassSname{ get; set; }
    
    /// <summary>
    /// 设备分类标签;0 iso 1生物安全  2POCT 3高等级
    /// </summary>
    public string? ClassTag{ get; set; }
    
    /// <summary>
    /// 设备分样式;用于存储图案颜色
    /// </summary>
    public string? ClassStyle{ get; set; }
    
    /// <summary>
    /// 缺省上一层级标签内容
    /// </summary>
    public bool IsUseParentRecord { get; set; } = true;
    
}



