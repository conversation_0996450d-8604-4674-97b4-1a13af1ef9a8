﻿using System.Net;
using System.Text.RegularExpressions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;

namespace XH.H82.API.Extensions;

/// <inheritdoc />
public class IpRestrictionFilter : IActionFilter
{
    private readonly IConfiguration _configuration;

    /// <inheritdoc />
    public IpRestrictionFilter( IConfiguration configuration)
    {
        _configuration = configuration;
    }
    public void OnActionExecuting(ActionExecutingContext context)
    {
        var clientIp = GetClientIpAddress(context.HttpContext);
        var allowedIps = _configuration.GetSection("AllowedIps").Get<List<string>>();

        if (allowedIps == null || allowedIps.Count == 0)
        {
            context.Result = new ContentResult
            {
                Content = "No allowed IP addresses are configured.",
                StatusCode = (int)HttpStatusCode.Forbidden
            };
            return;
        }

        var isAllowed = false;
        foreach (var allowedIp in allowedIps)
        {
            if (IsIpAllowed(clientIp, allowedIp))
            {
                isAllowed = true;
                break;
            }
        }

        if (!isAllowed)
        {
            context.Result = new ContentResult
            {
                Content = $"IP address {clientIp} is not allowed to access this resource.",
                StatusCode = (int)HttpStatusCode.Forbidden
            };
        }
    }

    public void OnActionExecuted(ActionExecutedContext context)
    {
        Console.WriteLine("IP Restriction Filter executed.");
    }

    private bool IsIpInRange(string clientIp, string allowedIpRange)
    {
        var parts = allowedIpRange.Split('/');
        if (parts.Length != 2)
        {
            throw new ArgumentException("Invalid IP range format.");
        }

        var networkAddress = parts[0];
        var subnetMask = Convert.ToInt32(parts[1]);

        var clientIpBytes = IPAddress.Parse(clientIp).GetAddressBytes();
        var networkAddressBytes = IPAddress.Parse(networkAddress).GetAddressBytes();

        if (clientIpBytes.Length != networkAddressBytes.Length)
        {
            return false;
        }

        var maxBit = clientIpBytes.Length * 8;
        if (subnetMask < 0 || subnetMask > maxBit)
        {
            throw new ArgumentException("Invalid subnet mask.");
        }

        for (var i = 0; i < clientIpBytes.Length; i++)
        {
            var bitOffset = i * 8;
            var bitLength = Math.Min(subnetMask - bitOffset, 8);

            if (bitLength <= 0)
            {
                break;
            }

            var mask = (byte)(0xFF << (8 - bitLength));
            if ((clientIpBytes[i] & mask) != (networkAddressBytes[i] & mask))
            {
                return false;
            }
        }

        return true;
    }
    private bool IsSingleIp(string ip)
    {
        return Regex.IsMatch(ip, @"^(\d{1,3}\.){3}\d{1,3}$");
    }
    private bool IsIpAllowed(string clientIp, string allowedIp)
    {
        if (IsSingleIp(allowedIp))
        {
            return clientIp == allowedIp;
        }
        else
        {
            return IsIpInRange(clientIp, allowedIp);
        }
    }
    
    private string GetClientIpAddress(HttpContext context)
    {
        var clientIp = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
        if (string.IsNullOrEmpty(clientIp))
        {
            clientIp = context.Connection.RemoteIpAddress?.ToString();
            if (clientIp == "::1")
            {
                clientIp = "0.0.0.0";
            }
        }

        if (string.IsNullOrEmpty(clientIp))
        {
            clientIp = context.Request.Headers["REMOTE_ADDR"].FirstOrDefault();
        }

        return clientIp ?? "0.0.0.0";
    }
}