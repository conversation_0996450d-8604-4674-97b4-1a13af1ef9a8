﻿using System.Net;
using AutoMapper;
using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RestSharp;
using Spire.Doc;
using SqlSugar;
using XH.H82.Base.Helper;
using XH.H82.Base.Setup;
using XH.H82.IServices;
using XH.H82.Models.Dtos;
using XH.H82.Models.Dtos.Base;
using XH.H82.Models.Dtos.SysDocDtos;
using XH.H82.Models.Entities;
using XH.H82.Models.SugarDbContext;
using XH.LAB.UTILS.Interface;
using XH.LAB.UTILS.Models;

namespace XH.H82.Services.OtherSystem
{
    public class FileManageService : IFileManageService
    {
        private readonly string file_preview_address;
        private readonly IConfiguration _configuration;
        private readonly IBaseDataServices _baseDataServices;
        private readonly ISqlSugarUow<SugarDbContext_Master> _dbContext;
        private readonly ILogger<FileManageService> _logger;
        private readonly IHttpContextAccessor _httpContext;
        private readonly IUploadFileService _uploadFileService;
        private readonly IMapper _mapper;
        private string _accessToken { get; set; }
        private RestClient _clientH91 { get; set; }

        public FileManageService(IConfiguration configuration, ISqlSugarUow<SugarDbContext_Master> dbContext, IBaseDataServices baseDataServices, IHttpContextAccessor httpContext, IMapper mapper, ILogger<FileManageService> logger, IUploadFileService uploadFileService)
        {
            _dbContext = dbContext;
            _configuration = configuration;
            _configuration = configuration;
            file_preview_address = _configuration["S54"];
            _baseDataServices = baseDataServices;
            _httpContext = httpContext;
            _mapper = mapper;

            _clientH91 = new RestClient(new RestClientOptions
            {
                RemoteCertificateValidationCallback = (Sender, certificate, chain, SslPolicyErrors) => true,
                BaseUrl = new Uri(_configuration["H91"]),
                ThrowOnAnyError = true
            });
            _accessToken = _httpContext.HttpContext.Request.Headers.Authorization;
            _logger = logger;
            _uploadFileService = uploadFileService;
        }

        public List<DocumentSystemMenu> GetFileTree(string labId, bool isSmbl = false , string meanId = "")
        { ///string meanId = "H9101"
            var url = $"/api/Dmis/GetDmisMenuTreeInfo?lab_id={labId}&selectPage=1";
            if (isSmbl)
            {
                url = $"/api/Dmis/GetDmisMenuTreeInfo?lab_id={labId}&selectPage=2";
            }
            RestRequest request = new RestRequest(url);
            request.AddHeader("Authorization", _accessToken);
            var rsp = _clientH91.ExecuteGet<ResultDto>(request);

            var documentSystemMenu = JsonConvert.DeserializeObject<List<DocumentSystemMenu>>(rsp.Data.data.ToString());
            var systemDocuments = documentSystemMenu!
                .WhereIF(meanId.IsNotNullOrEmpty(), x => x.FirstmenuId == meanId)
                .ToList();

            var treeData = new List<TreeData>();
            documentSystemMenu.ForEach(x =>
            {
                treeData.Add(x.ToTreeData());
            });
            Console.WriteLine(JsonConvert.SerializeObject(treeData,Formatting.Indented));
            if (systemDocuments is null)
            {
                return new List<DocumentSystemMenu>();
            }
            else
            {
                return systemDocuments;
            }
        }

        public List<TreeData> GetFileTree1(string labId, string meanId = "")
        { ///string meanId = "H9101"
            var result = new List<TreeData>();
            var url = $"/api/Dmis/GetDmisMenuTreeInfo?lab_id={labId}";
            RestRequest request = new RestRequest(url);
            request.AddHeader("Authorization", _accessToken);
            var rsp = _clientH91.ExecuteGet<ResultDto>(request);
            var documentSystemMenu = JsonConvert.DeserializeObject<List<DocumentSystemMenu>>(rsp.Data.data.ToString());
            if (documentSystemMenu is null)
            {
                return result;
            }
            var systemDocuments = documentSystemMenu!
                .WhereIF(meanId.IsNotNullOrEmpty(), x => x.FirstmenuId == meanId)
                .ToList();
            systemDocuments.ForEach(x =>
            {
                result.Add(x.ToTreeData());
            });
            Console.WriteLine(JsonConvert.SerializeObject(result,Formatting.Indented));
            return result;
        }
        
        
        public List<MgroupPullDto> GetPgroupList(string labId)
        {
            string url = $"/api/Dmis/GetLabMgroupDropDown?lab_id={labId}";
            RestRequest request = new RestRequest(url);
            request.AddHeader("Authorization", _accessToken);
            var rsp = _clientH91.ExecuteGet<ResultDto>(request).Data.data;
            var nodes = JsonConvert.DeserializeObject<List<MgroupNode>>(rsp.ToString());
            var result = _mapper.Map<List<MgroupPullDto>>(nodes);
            return result;
        }

        public List<DocInfoDto> GetDocInfoList(string classId, string pgroupId, string docType, string keyword, string equipmentId)
        {
            var res = _dbContext.Db.Queryable<DMIS_SYS_DOC>()
                .Where(p => p.CLASS_ID == classId && (p.DOC_STATE == "1" || p.DOC_STATE == "9"))
                .WhereIF(pgroupId.IsNotNullOrEmpty(), p => p.PGROUP_ID == pgroupId)
                .WhereIF(docType.IsNotNullOrEmpty(), p => p.DOC_CLASS_ID == docType)
                .WhereIF(keyword.IsNotNullOrEmpty(), p => p.DOC_NAME.ToLower().Contains(keyword.ToLower()) || p.DOC_KEYWORD.ToLower().Contains(keyword.ToLower()))
                .ToList();
            var dmisFileList = _dbContext.Db.Queryable<DMIS_SYS_FILE>().ToList();
            var filePathList = _dbContext.Db.Queryable<EMS_DOC_INFO>()
                .Where(p => p.DOC_INFO_ID == equipmentId && p.DOC_CLASS == docType && p.DOC_STATE == "1")
                .Select(i => i.DOC_PATH.Replace(".pdf", ""))
                .ToList();
            var pgroupList = _dbContext.Db.Queryable<SYS6_INSPECTION_PGROUP>().ToList();
            var result = new List<DocInfoDto>();
            res.ForEach(item =>
            {
                bool isEqual = false;
                if (filePathList.Count == 0)
                {
                    isEqual = false;
                }
                else
                {
                    dmisFileList
                    .Where(p => p.DOC_ID == item.DOC_ID)
                    .Select(i => i.FILE_PATH)
                    .ToList()
                    .ForEach(o =>
                    {
                        if (filePathList.Contains(o))
                        {
                            isEqual |= true;
                        }
                    });
                }
                result.Add(new DocInfoDto
                {
                    DOC_ID = item.DOC_ID,
                    PGROUP_ID = item.PGROUP_ID,
                    PGROUP_NAME = pgroupList.Where(p => p.PGROUP_ID == item.PGROUP_ID).FirstOrDefault()?.PGROUP_NAME,
                    DOC_NAME = item.DOC_NAME,
                    DOC_CODE = item.DOC_CODE,
                    DOC_TYPE = item.DOC_TYPE,
                    DOC_KEYWORD = item.DOC_KEYWORD,
                    DOC_VERSION = item.DOC_VERSION,
                    IF_SELECT = isEqual == false ? "0" : "1"
                });
            });
            return result;
        }

        public List<string> GetDocFile(string docId, string file_upload_address)
        {
            var res = _dbContext.Db.Queryable<DMIS_SYS_FILE>()
                .Where(p => p.DOC_ID == docId && p.FILE_STATE == "1" && p.REVISION_STATE == "1")
                .ToList();
            var result = new List<string>();
            res.ForEach(item =>
            {
                if (item.FILE_TYPE == "WORD")
                {
                    result.Add(item.FILE_PATH + ".pdf");
                }
                else
                {
                    result.Add(item.FILE_PATH + item.FILE_SUFFIX);
                }
            });
            return result;
        }


        public ResultDto UploadDocFile(List<DocFileDto> filePath, string? docInfoId, string docClass, string equipmentId, string userName, string hospitalId)
        {
            ResultDto result = new ResultDto();
            if (docClass == "SOP档案")
            {
                AddEquipmentSopFiles(equipmentId, filePath);
                result.success = true;
            }
            else
            {
                try
                {
                    //忽略证书
                    ServicePointManager.ServerCertificateValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true;
                    UploadDocFiles(filePath, docInfoId, docClass, equipmentId, userName, hospitalId);
                    result.success = true;
                }
                catch (Exception ex)
                {
                    result.success = false;
                    result.msg = "操作失败";
                    _logger.LogError("操作失败:\n" + ex.Message);
                }
            }

          
            return result;
        }

        public DocumentSystemDto FindDoccumentNodesAndDocuments(string labId, string equipmentId, string firstmenukey, string docClass, string? classId, string? docType, string? pgroupId, string? docName, SOPType? docClassId)
        {

            var emsfiles = new List<EMS_DOC_INFO>();
            if (docClass == "SOP档案")
            {
                emsfiles =  GetEquipmentSopFils(equipmentId);
            }
            else
            {
                _dbContext.Db.Queryable<EMS_DOC_INFO>()
                    .Where(x => x.DOC_STATE == "1")
                    .Where(x => x.DOC_CLASS == docClass)
                    .Where(x => x.EQUIPMENT_ID == equipmentId)
                    .ForEach(x=>emsfiles.Add(x));
            }
            var nodes = FindDocumentSystemExitsDocuments(labId, firstmenukey, classId, docType, null, docName, docClassId);

            var mGroupId = string.Empty;
            var pGroupId = string.Empty;
            var docTypeId = string.Empty;
            if (pgroupId.IsNotNullOrEmpty())
            {
                if (pgroupId.Contains("M") || pgroupId.Contains("m") || pgroupId.Contains("l") || pgroupId.Contains("L"))
                {
                    mGroupId = pgroupId;
                }
                else if (pgroupId.IsNotNullOrEmpty() && pgroupId.Contains("P") || pgroupId.Contains("p") || pgroupId == "0")
                {
                    pGroupId = pgroupId;
                }
                else
                {
                    docTypeId = pgroupId;
                }
            }
            var files = _mapper.Map<List<DocInfoDto>>(SeparateFiles(nodes, docTypeId, mGroupId, pGroupId));
            foreach (var file in files)
            {
                if (file.FilePreviewNginx is null)
                {
                    continue;
                }
                file.IF_SELECT = emsfiles
                .Where(x => file.DOC_ID == x.DOC_FILE)
                .Count() > 0 ? "1" : "0";
                
            }
            var result = new DocumentSystemDto(nodes, files);
            return result;
        }
        
        private List<SopClass> GetThirdMenuDrop(string labId,string classId)
        {
            string url = $"/api/Dmis/GetThirdMenuDrop?lab_id={labId}&class_id={classId}";
            RestRequest request = new RestRequest(url);
            request.AddHeader("Authorization", _accessToken);
            var response = _clientH91.ExecuteGet<ResultDto>(request);
            Console.WriteLine(response.Data.data.ToString());
            var sopClass = JsonConvert.DeserializeObject<List<SopClass>>(response.Data.data.ToString());
            return sopClass;
        }

        public class SopClass
        {
            public string key { get; set; }
            public string value { get; set; }
        }


        public List<EMS_DOC_INFO> GetEquipmentSopFils(string equipmentId)
        {
            var result = new List<EMS_DOC_INFO>();
            var oldEquipmentSopFiles = _dbContext.Db.Queryable<EMS_DOC_INFO>()
                .Where(p => p.DOC_CLASS == "SOP档案")
                .Where(p => p.DOC_STATE == "1")
                .Where(x => x.EQUIPMENT_ID == equipmentId || x.DOC_INFO_ID == equipmentId)
                .ToList();
            if (oldEquipmentSopFiles.Any(x=>x.DOC_INFO_ID == equipmentId))
            {
                result.AddRange(oldEquipmentSopFiles.Where(x=>x.DOC_INFO_ID == equipmentId));
            }
            var likns = _uploadFileService.GetEquipmentSopFiles(_dbContext, equipmentId);
            var docids = likns.Select(x => x.DOC_ID);
            if (oldEquipmentSopFiles.Any())
            {
                var oldDocFileIds = oldEquipmentSopFiles.Where(x => x.DOC_FILE.IsNotNullOrEmpty())
                    .Select(x => x.DOC_FILE)
                    .Distinct()
                    .ToList();
                var oldDocFileIds2 = oldEquipmentSopFiles.Where(x => x.DOC_INFO_ID.IsNotNullOrEmpty())
                    .Where(x => x.DOC_INFO_ID != equipmentId).Select(x => x.DOC_INFO_ID)
                    .Distinct()
                    .ToList();
                var alloldDocFileIdsHash = new HashSet<string>(oldDocFileIds);
                alloldDocFileIdsHash.UnionWith(oldDocFileIds2);
                var alloldDocFileIds = alloldDocFileIdsHash.ToList();
                foreach (var oldDocFileId in alloldDocFileIds)
                {
                    if (!docids.Contains(oldDocFileId))
                    {
                        _uploadFileService.AddEquipmentSopFiles(_dbContext, equipmentId, new List<string>{oldDocFileId});
                    }
                }
            }
            var docs = _dbContext.Db.Queryable<DMIS_SYS_DOC>()
                .InnerJoin<DMIS_SYS_FILE>((doc,file) => doc.DOC_ID == file.DOC_ID)
                .Where(doc => docids.Contains(doc.DOC_ID))
                .Where((doc,file)=>file.REVISION_STATE == "1" && file.FILE_STATE == "1" && file.FILE_ORIGIN != "2")
                .Select((doc,file)=>file)
                .ToList();
            foreach (var doc in docs)
            {
                result.Add(new EMS_DOC_INFO
                {
                    DOC_ID = doc.DOC_ID,
                    DOC_FILE = doc.DOC_ID,
                    DOC_INFO_ID = null,
                    DOC_NAME = doc.FILE_CNAME,
                    DOC_PATH = $"{doc.FILE_PATH}{(doc.FILE_TYPE.ToUpper() == "IMG" ? doc.FILE_TYPE : ".pdf")}",
                    DOC_SUFFIX = doc.FILE_TYPE.ToUpper() == "IMG" ? doc.FILE_TYPE : ".pdf",
                    DOC_TYPE = doc.FILE_TYPE.ToUpper() == "IMG" ? doc.FILE_TYPE : "PDF",
                    DOC_STATE = doc.FILE_STATE,
                    PDF_PREVIEW_PATH = "",
                    EQUIPMENT_ID = equipmentId
                });
            }
            return result;
        }

        /// <summary>
        /// 添加设备sop证书
        /// </summary>
        /// <param name="equipmentId">设备id</param>
        /// <param name="files">文件系统证书模型</param>
        public void AddEquipmentSopFiles(string equipmentId,List<DocFileDto> files)
        {
            var  docids =  files.Select(x => x.DOC_ID).ToList();
            var likns =  _uploadFileService.AddEquipmentSopFiles(_dbContext, equipmentId, docids);
            #if DEBUG
            Console.WriteLine(JsonConvert.SerializeObject(likns,  Formatting.Indented));
            #endif
        }

        /// <summary>
        /// 查询文件系统中的文件
        /// </summary>
        /// <param name="classId">文档系统中的文档分类的id</param>
        /// <param name="pgroupId">检验专业组id</param>
        /// <param name="docType">文档分类</param>
        /// <param name="docName">文档名称</param>
        /// <returns></returns>
        private List<LaboratoryOrManagementProfessionalGroupNode> FindDocumentSystemExitsDocuments(string labId, string firstmenukey, string? classId, string? docType, string? pgroupId, string? docName, SOPType? docClassId)
        {
            var sop = "";
            if (docClassId.HasValue)
            {
                var sopclasses = GetThirdMenuDrop(labId,classId);
                foreach (var sopclass in sopclasses)
                {
                    if (docClassId == SOPType.管理SOP)
                    {
                        if (sopclass.value == "管理SOP")
                        {
                            sop = sopclass.key;
                        }
                    }
                    if (docClassId == SOPType.仪器SOP)
                    {
                        if (sopclass.value == "仪器SOP")
                        {
                            sop = sopclass.key;
                        }
                    }
                    if (docClassId == SOPType.项目SOP)
                    {
                        if (sopclass.value == "项目SOP")
                        {
                            sop = sopclass.key;
                        }
                    }
                }
            }

            string url = $"/api/Dmis/GetLabGroupTypeDocTree?firstmenukey={firstmenukey}&class_id={classId}&doc_type{docType}=&lab_id={labId}&pgroup_id={pgroupId}&doc_name={docName}&sort_mode=1&sort_type=1&doc_state=1&load_mode=1&doc_class_id={sop}";
            RestRequest request = new RestRequest(url);
            request.AddHeader("Authorization", _accessToken);
            var response = _clientH91.ExecuteGet<ResultDto>(request);
            Console.WriteLine(response.Data.data.ToString());
            var nodes = JsonConvert.DeserializeObject<List<LaboratoryOrManagementProfessionalGroupNode>>(response.Data.data.ToString());
            return nodes;
        }

        private List<FileSystemFileInfo> SeparateFiles(List<LaboratoryOrManagementProfessionalGroupNode> nodes, string doctype = null, string mgroupId = null, string pgroupId = null)
        {
            var result = new List<FileSystemFileInfo>();
            if (nodes.Count > 0)
            {
                foreach (var node in nodes)
                {
                    if (mgroupId.IsNullOrEmpty() || mgroupId == node.MgroupId)
                    {
                        foreach (var professionalGroupNodeNode in node.ProfessionalGroupNodes)
                        {
                            if (pgroupId.IsNullOrEmpty() || pgroupId == professionalGroupNodeNode.ClassCid)
                            {
                                foreach (var classNode in professionalGroupNodeNode.DocumentClasses)
                                {
                                    if (doctype.IsNullOrEmpty() || doctype == classNode.DataId)
                                    {
                                        foreach (var file in classNode.FileInfos)
                                        {
                                            if (professionalGroupNodeNode.ClassCid == "0"/*公共分类*/)
                                            {
                                                file.PgroupId = node.MgroupId;
                                                file.PgroupName = node.MgroupName;
                                                file.DocType = classNode.DataId;
                                                file.FilePath = file.FilePreviewNginx.IsNotNullOrEmpty() ? file.FilePreviewNginx.Replace("/H91pdf/api/", "/") : null;
                                            }
                                            else
                                            {
                                                file.PgroupId = professionalGroupNodeNode.ClassCid;
                                                file.PgroupName = professionalGroupNodeNode.ClassCname;
                                                file.DocType = classNode.DataId;
                                                file.FilePath = file.FilePreviewNginx.IsNotNullOrEmpty() ? file.FilePreviewNginx.Replace("/H91pdf/api/", "/") : null;
                                            }
                                        }
                                        result.AddRange(classNode.FileInfos);
                                    }
                                }
                            }
                        }
                    }
                }

                foreach (var node in nodes)
                {
                    foreach (var professionalGroupNodeNode in node.ProfessionalGroupNodes)
                    {
                        foreach (var classNode in professionalGroupNodeNode.DocumentClasses)
                        {
                            classNode.FileInfos.Clear();
                        }
                    }
                }
            }
            return result;
        }

        private void UploadDocFiles(List<DocFileDto> files, string? docInfoId, string docClass, string equipmentId, string userName, string hospitalId)
        {
            var fileInfo = _dbContext.Db.Queryable<EMS_DOC_INFO>()
                        .Where(p => p.EQUIPMENT_ID == equipmentId)
                        .Where(x => x.DOC_STATE == "1")
                        .Where(x => x.DOC_CLASS == docClass)
                        .ToList();

            var addDocInfos = new List<EMS_DOC_INFO>();

            var updateDocInfos = new List<EMS_DOC_INFO>();
            foreach (var file in files)
            {
                var doc = fileInfo.FirstOrDefault(x => x.DOC_FILE == file.DOC_ID);
                if (doc is null)
                {
                    var id = IDGenHelper.CreateGuid().ToString();
                    var suffixIndex = file.FILE_PATH.LastIndexOf(".");
                    var suffix = file.FILE_PATH.Substring(suffixIndex);
                    var docType = FileTypeUnit.ReturnFileType(suffix);
                    var docInfo = new EMS_DOC_INFO()
                    {
                        DOC_ID = id,
                        DOC_TYPE = docType,
                        HOSPITAL_ID = hospitalId,
                        DOC_INFO_ID = docInfoId,
                        DOC_FILE = file.DOC_ID,
                        EQUIPMENT_ID = equipmentId,
                        DOC_CLASS = docClass,
                        DOC_NAME = file.FILE_NAME,
                        DOC_PATH = file.FILE_PATH,
                        UPLOAD_PERSON = userName,
                        UPLOAD_TIME = DateTime.Now,
                        DOC_SUFFIX = suffix,
                        DOC_STATE = "1",
                        FIRST_RPERSON = userName,
                        FIRST_RTIME = DateTime.Now,
                        LAST_MPERSON = userName,
                        LAST_MTIME = DateTime.Now,
                        PDF_PREVIEW_PATH = ""
                    };
                    addDocInfos.Add(docInfo);
                }
                else
                {
                    var suffixIndex = file.FILE_PATH.LastIndexOf(".");
                    var suffix = file.FILE_PATH.Substring(suffixIndex);
                    var docType = FileTypeUnit.ReturnFileType(suffix);
                    var docInfo = new EMS_DOC_INFO()
                    {
                        DOC_ID = doc.DOC_ID,
                        DOC_TYPE = docType,
                        HOSPITAL_ID = hospitalId,
                        DOC_INFO_ID = docInfoId,
                        DOC_FILE = file.DOC_ID,
                        EQUIPMENT_ID = equipmentId,
                        DOC_CLASS = docClass,
                        DOC_NAME = file.FILE_NAME,
                        DOC_PATH = file.FILE_PATH,
                        DOC_SUFFIX = suffix,
                        DOC_STATE = "1",
                        UPLOAD_PERSON = doc.UPLOAD_PERSON,
                        UPLOAD_TIME = doc.UPLOAD_TIME,
                        FIRST_RPERSON = userName,
                        FIRST_RTIME = DateTime.Now,
                        LAST_MPERSON = userName,
                        LAST_MTIME = DateTime.Now,
                        PDF_PREVIEW_PATH = ""
                    };
                    updateDocInfos.Add(docInfo);
                }
            }
            foreach (var doc in addDocInfos)
            {
                UploadDocFilePreview(doc);
            }
            foreach (var doc in updateDocInfos)
            {
                UploadDocFilePreview(doc);
            }
            using (_dbContext.Begin())
            {
                _dbContext.Db.Insertable(addDocInfos).ExecuteCommand();
                _dbContext.Db.Updateable(addDocInfos).IgnoreColumns().ExecuteCommand();
                _dbContext.SaveChanges();
            }
        }

        private void UploadDocFilePreview(EMS_DOC_INFO docInfo)
        {
            var filePath = file_preview_address + docInfo.DOC_PATH;
            WebClient mywebclient = new WebClient();
            byte[] Bytes = mywebclient.DownloadData(filePath);

            if (docInfo.DOC_SUFFIX == ".docx")
            {
                using (MemoryStream pdfStream = new MemoryStream())
                {
                    var document = new Document(new MemoryStream(Bytes), FileFormat.Docx);
                    document.SaveToStream(pdfStream, FileFormat.PDF);
                    Bytes = pdfStream.ToArray();
                }

                docInfo.DOC_PATH = docInfo.DOC_NAME.Replace(docInfo.DOC_SUFFIX, ".pdf");
                docInfo.DOC_TYPE = "PDF";
                docInfo.DOC_SUFFIX = ".pdf";
            }
            if (docInfo.DOC_SUFFIX == ".doc")
            {
                using (MemoryStream pdfStream = new MemoryStream())
                {
                    var document = new Document(new MemoryStream(Bytes), FileFormat.Doc);
                    document.SaveToStream(pdfStream, FileFormat.PDF);
                    Bytes = pdfStream.ToArray();
                }

                docInfo.DOC_PATH = docInfo.DOC_NAME.Replace(docInfo.DOC_SUFFIX, ".pdf");
                docInfo.DOC_TYPE = "PDF";
                docInfo.DOC_SUFFIX = ".pdf";
            }

            var previewByte = PdfPreview.PDFPreview_Byte(Bytes);
            var doc_name = $"{docInfo.DOC_NAME}_{Guid.NewGuid().ToString("N")}预览.jpg";
            if (docInfo.DOC_SUFFIX == ".png" || docInfo.DOC_SUFFIX == ".jpg")
            {
                previewByte = Bytes;
                doc_name = $"{docInfo.DOC_NAME}_{Guid.NewGuid().ToString("N")}预览{docInfo.DOC_SUFFIX}";
            }
            var doc_folder = "EMS/datafile";
            var if_cover = "true";

            ResultDto rsp = _baseDataServices.UploadPathFormDataFile(doc_folder, if_cover, doc_name, previewByte);

            if (rsp != null && rsp.success)
            {
                JArray jarray1 = JArray.Parse(rsp.data.ToString()); // 将JSON字符串转换为JArray对象
                docInfo.PDF_PREVIEW_PATH = jarray1[0]["UploadPath"].ToString();
            }
            else
            {
                throw new BizException($"{rsp.msg}");
            }
        }
    }
}