﻿using Microsoft.Extensions.Configuration;
using XH.H82.Base.Helper.InitDataOption.DeploymentConfig.Models;

namespace XH.H82.Base.Helper.InitDataOption.DeploymentConfig.Provider
{
    public class TextConfigProvider
    {
        IConfiguration _configuration;

        public TextConfigProvider(IConfiguration configuration) => _configuration = configuration;


        private async Task<TextConfiguration> FindPahtInAppDomain(string fileName)
        {
            var appRunPath = AppDomain.CurrentDomain.BaseDirectory;
            var filePath = $"{appRunPath}\\{fileName}";
            var fileText = "";
            if (File.Exists(filePath))
            {
                fileText = await File.ReadAllTextAsync(filePath);
            }
            return new TextConfiguration(fileName, filePath, fileText);
        }

        /// <summary>
        /// 默认查找模块id 和 医院id 限定
        /// {moduleId}.conf 和 {moduleId}.service 
        /// {moduleId}-z{hospitalId}.conf 和 {moduleId}-{hospitalId}.service
        /// </summary>
        /// <param name="moduleId"></param>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        public async Task<List<TextConfiguration>> FindCommonConfiguration(string moduleId, string hospitalId = null)
        {
            var result = new List<TextConfiguration>();
            var fileNames = new List<string>();
            if (hospitalId is null)
            {
                var confName = $"{moduleId}.conf";
                var serviceName = $"{moduleId}.service";
                fileNames.Add(confName);
                fileNames.Add(serviceName);
            }
            else
            {
                var confName = $"{moduleId}-z{hospitalId}.conf";
                var serviceName = $"{moduleId}-{hospitalId}.service";
                fileNames.Add(confName);
                fileNames.Add(serviceName);

            }
            foreach (var fileName in fileNames)
            {
                var textConfiguration = await FindPahtInAppDomain(fileName);
                result.Add(textConfiguration);
            }
            return result;
        }


        /// <summary>
        /// 查找程序运行目录下的文本文件
        /// </summary>
        /// <param name="fileName">文件名（需要拓展名）</param>
        /// <returns></returns>
        public async Task<TextConfiguration> FindInAppDomainOtherTextConfiguration(string fileName)
        {
            var result = await FindPahtInAppDomain(fileName);
            return result;
        }
    }
}
