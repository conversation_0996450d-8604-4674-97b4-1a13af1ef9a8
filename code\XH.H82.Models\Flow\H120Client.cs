﻿using System.Diagnostics;
using H.Utility;
using H.Utility.Helper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using RestSharp;
using Serilog;
using XH.H82.Models.Flow;

namespace XH.H82.Models;

public class H120Client
{
    
    private readonly IHttpContextAccessor _httpContext;
    private readonly IConfiguration _configuration;
    private RestClient _clientH120;
    private RestClient ClientH120
    {
        get
        {
            if (_clientH120 == null)
            {
                try
                {
                    var addressH120 = _configuration["H120"];
                    _clientH120 = new RestClient(new RestClientOptions()
                    {
                        RemoteCertificateValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true,
                        BaseUrl = new Uri(addressH120),
                        ThrowOnAnyError = true
                    });
                }
                catch (Exception ex)
                {
                    Log.Error(ex.ToString());
                    throw ex;
                }
            }
            return _clientH120;
        }
    }

    public H120Client(IHttpContextAccessor httpContext, IConfiguration configuration)
    {
        _httpContext = httpContext;
        _configuration = configuration;
    }

    /// <summary>
    /// 调用H120方法
    /// </summary>
    /// <param name="url">api接口</param>
    /// <param name="method">请求方法</param>
    /// <param name="body">请求体</param>
    /// <typeparam name="T">出差模型</typeparam>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    private T H120Request<T>(string url  ,Method method , object? body = null) where T : class
    { 
        Stopwatch sw = new Stopwatch();
        sw.Start();
        var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();
         var request = new RestRequest(url);
        request.AddHeader("Authorization", token);
        if (body is not null)
        {
            request.AddBody(body);
        }
        var response =  method switch
        {
            Method.Get => ClientH120.ExecuteGet<ResultDto<T>>(request),
            Method.Post => ClientH120.ExecutePost<ResultDto<T>>(request),
            Method.Put => ClientH120.ExecutePut<ResultDto<T>>(request),
            Method.Delete => ClientH120.ExecuteDelete<ResultDto<T>>(request),
            _ => throw new BizException("不支持的请求方法")
        };
        sw.Stop();
        Log.ForContext("elapsed", sw.ElapsedMilliseconds)
            .Information($"调用H120模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");

        if (response.IsSuccessful)
        {
            if (response.Data.success)
            {
                Log.Information($"H120模块{url}请求完成,返回了数据:" + JsonConvert.SerializeObject(response.Data.data , Formatting.Indented));
                //return JsonHelper.FromJson<T>(response.Data.data.ToString());
                return response.Data.data;
            }
            else
            {
                Log.Error($"调用H120模块[{url}]请求完成,但是返回了错误:" + response.Data.msg);
                throw new Exception($"调用H120模块[{url}]请求完成,但是返回了错误:" + response.Data.msg);
            }
        }
        else
        {
            Log.Error($"调用H120模块[{url}]发生错误:{response.ErrorException}");
            throw new Exception($"调用H120模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
        }
    }

    /// <summary>
    /// 初始化流程
    /// </summary>
    /// <param name="flowCode">流程唯一标识</param>
    /// <param name="userNos">审批人（多个userNo）</param>
    /// <returns></returns>
    public string StartFlowInstanceByCode(string flowCode , List<string> userNos)
    {
        var  url = $"/api/flow-instance/code?flowCode={flowCode}";
        
        var requestBody = new
        {
            assignPerson = userNos
        };
        var rsp = H120Request<StartFlowInstanceByCodeDto>(url, Method.Post, requestBody);
        return rsp.flowInstanceId;
    }

    /// <summary>
    /// 获取实例日志
    /// </summary>
    /// <param name="flowInstanceId">流程实例id</param>
    /// <returns></returns>
    public FlowInstanceDto  GetFlowInstanceDetail(string flowInstanceId)
    {
        var  url = $"/api/flow-instance/detail?flowInstanceId={flowInstanceId}";
        return H120Request<FlowInstanceDto>(url, Method.Get);
    }
    
    /// <summary>
    /// 获取最新流程定义
    /// </summary>
    /// <param name="flowCode"></param>
    /// <returns></returns>
    public  FlowDto GetFlowDetailByCode(string flowCode)
    {
        var  url = $"/api/flow?flowCode={flowCode}";
        return H120Request<FlowDto>(url, Method.Get);
    }

    /// <summary>
    /// 进入下一个审批流程
    /// </summary>
    /// <param name="flowInstanceId"></param>
    /// <param name="flowInstanceNodeId"></param>
    /// <param name="approvalInput"><see cref="ApprovalInput"/></param>
    /// <returns></returns>
    public bool NextFlow(string flowInstanceId, string flowInstanceNodeId, ApprovalInput approvalInput)
    {
        var url = $"/api/flow-instance/init?flowInstanceId={flowInstanceId}&flowInstanceNodeId={flowInstanceNodeId}";
        var result  =  H120Request<ResultDto<object>>(url, Method.Put, approvalInput);
        return true;
    }
}