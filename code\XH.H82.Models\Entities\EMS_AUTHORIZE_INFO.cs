﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace XH.H82.Models.Entities
{
    [DBOwner("XH_OA")]
    public class EMS_AUTHORIZE_INFO
    {
        /// <summary>
        /// pk
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string AUTHORIZE_ID { get; set; }
        /// <summary>
        /// 机构id
        /// </summary>
        public string HOSPITAL_ID { get; set; }
        /// <summary>
        /// 设备id
        /// </summary>
        public string EQUIPMENT_ID { get; set; }
        /// <summary>
        /// 授权人
        /// </summary>
        public string AUTHORIZE_PERSON { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string AUTHORIZE_PERSON_ID { get; set; }
        /// <summary>
        /// 被授权人（多个）
        /// </summary>
        public string AUTHORIZED_PERSON { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string AUTHORIZED_PERSON_IDS { get; set; }
        /// <summary>
        /// 授权权限
        /// </summary>
        public string AUTHORIZED_ROLE { get; set; }
        /// <summary>
        /// 授权人职务
        /// </summary>
        public string AUTHORIZED_PERSON_POST { get; set; }
        /// <summary>
        /// 授权时间
        /// </summary>
        public DateTime? AUTHORIZE_DATE { get; set; }
        /// <summary>
        /// 数据状态 1 在用  2 删除  0 禁用（应该没有禁用）
        /// </summary>
        public string AUTHORIZE_STATE { get; set; }
        public string FIRST_RPERSON { get; set; }
        public DateTime? FIRST_RTIME { get; set; }
        public string LAST_MPERSON { get; set; }
        public DateTime? LAST_MTIME { get; set; }
        public string REMARK { get; set; }

    }
}
