﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.Entities.FileTemplate
{
    /// <summary>
    /// 设备档案信息
    /// </summary>
    public class EquipmentArchivesInfo
    {
        public int NO { get; set; } = 1;
        public string? EQUIPMENT_ID { get; set; } = "";

        public string? EQUIPMENT_STATE { get; set; } = "";
        /// <summary>
        /// 档案名称 
        /// </summary>
        public string? EQUIPMENT_ARCHIVE { get; set; } = "";

        /// <summary>
        /// 设备名称(中文)
        /// </summary>
        public string? EQUIPMENT_NAME { get; set; } = "";
        /// <summary>
        /// 设备序号
        /// </summary>
        public string? EQUIPMENT_NUM { get; set; } = "";

        /// <summary>
        /// 所属医疗机构
        /// </summary>
        public string? HOSPITAL_NAME { get; set; } = "";

        /// <summary>
        /// 设备名称(英文)
        /// </summary>
        public string? EQUIPMENT_ENAME { get; set; } = "";

        /// <summary>
        /// 医院设备编号
        /// </summary>
        public string? SECTION_NO { get; set; } = "";

        /// <summary>
        /// 所属科室
        /// </summary>
        public string? LAB_NAME { get; set; } = "";

        /// <summary>
        /// 设备型号
        /// </summary>
        public string? EQUIPMENT_MODEL { get; set; } = "";

        /// <summary>
        /// 科室设备编号
        /// </summary>
        public string? DEPT_SECTION_NO { get; set; } = "";

        /// <summary>
        /// 所属专业组
        /// </summary>
        public string? UNIT_ID { get; set; } = "";

        /// <summary>
        /// 设备代号
        /// </summary>
        public string? EQUIPMENT_CODE { get; set; } = "";

        /// <summary>
        /// 注册证号(中文)
        /// </summary>
        public string? REGISTRATION_NUM { get; set; } = "";

        /// <summary>
        /// 设备负责人
        /// </summary>
        public string? KEEP_PERSON { get; set; } = "";

        /// <summary>
        /// 设备类型
        /// </summary>

        public string? EQUIPMENT_CLASS { get; set; } = "";

        /// <summary>
        /// 注册证号(英文)
        /// </summary>
        public string? REGISTRATION_ENUM { get; set; } = "";

        /// <summary>
        /// 联系方式
        /// </summary>
        public string? CONTACT_PHONE { get; set; } = "";

        /// <summary>
        /// 专业分类 
        /// </summary>
        public string? PROFESSIONAL_CLASS { get; set; } = "";

        /// <summary>
        /// 设备序列号
        /// </summary>
        public string? SERIAL_NUMBER { get; set; } = "";
        /// <summary>
        /// 安装位置
        /// </summary>
        public string? INSTALL_AREA { get; set; } = "";

        /// <summary>
        /// 出厂日期
        /// </summary>
        public DateTime? EQ_OUT_TIME { get; set; }

        /// <summary>
        /// 到货日期
        /// </summary>
        public DateTime? EQ_IN_TIME { get; set; }

        /// <summary>
        /// 安装日期
        /// </summary>
        public DateTime? INSTALL_DATE { get; set; }

        /// <summary>
        /// 首次启用日期
        /// </summary>
        public DateTime? ENABLE_TIME { get; set; }

        /// <summary>
        /// 折旧年限(年)
        /// </summary>
        public string? DEPRECIATION_TIME { get; set; } = "";

        /// <summary>
        /// 设备价格(元)
        /// </summary>
        public double? SELL_PRICE { get; set; }

        /// <summary>
        /// 报废日期
        /// </summary>
        public DateTime? EQ_SCRAP_TIME { get; set; }

        /// <summary>
        /// 上次保养日期
        /// </summary>
        public DateTime? LAST_MAINTAIN_DATE { get; set; }

        /// <summary>
        /// 保养类型
        /// </summary>
        public string? MAINTAIN_TYPE { get; set; } = "";

        /// <summary>
        /// 下次保养日期
        /// </summary>
        public DateTime? NEXT_MAINTAIN_DATE { get; set; }

        /// <summary>
        /// 上次校准日期
        /// </summary>

        public DateTime? LAST_CORRECT_DATE { get; set; }

        /// <summary>
        /// 校准有效期
        /// </summary>
        public string? CORRECT_INTERVALS { get; set; } = "";

        /// <summary>
        /// 下次校准日期
        /// </summary>
        public DateTime? NEXT_CORRECT_DATE { get; set; }

        /// <summary>
        /// 上次对比日期 
        /// </summary>
        public DateTime? LAST_COMPARISON_DATE { get; set; }

        /// <summary>
        /// 比对有效期
        /// </summary>
        public string? COMPARISON_INTERVALS { get; set; } = "";
        /// <summary>
        /// 下次比对日期
        /// </summary>
        public DateTime? NEXT_COMPARISON_DATE { get; set; }

        /// <summary>
        /// 上次验证日期
        /// </summary>
        public DateTime? LAST_VERIFICATION_DATE { get; set; }

        /// <summary>
        /// 验证有效期
        /// </summary>
        public string? VERIFICATION_INTERVALS { get; set; } = "";

        /// <summary>
        /// 下次验证日期
        /// </summary>
        public DateTime? NEXT_VERIFICATION_DATE { get; set; }

        public string? REMARK { get; set; } = "";

        /// <summary>
        /// 经销商(中文)
        /// </summary>
        public string DEALER { get; set; } = "";
        /// <summary>
        /// 经销商(英文)
        /// </summary>
        public string DEALER_ENAME { get; set; } = "";


        public List<EquipmentDealerContact> DealerContact { get; set; } = null;
        /// <summary>
        /// 制造商（中文）
        /// </summary>
        public string MANUFACTURER { get; set; } = "";
        /// <summary>
        /// 制造商（英文）
        /// </summary>
        public string MANUFACTURER_ENAME { get; set; } = "";

        public List<EquipmentManufacturerContact> ManufacturerContact { get; set; } = null;

        /// <summary>
        /// 环境信息
        /// </summary>
        public EnvironmentInfo EnvironmentInfo { get; set; } = null;
    }
    /// <summary>
    /// 经销商联系人信息
    /// </summary>
    public class EquipmentDealerContact
    {
        /// <summary>
        /// 人员No.(经销商)
        /// </summary>
        public int NO { get; set; } = 1;
        /// <summary>
        /// 人员类型(经销商)
        /// </summary>
        public string CONTACT_TYPE { get; set; } = "";
        /// <summary>
        /// 职务/岗位(经销商)
        /// </summary>
        public string CONTACT_POST { get; set; } = "";
        /// <summary>
        /// 联系人姓名(经销商)
        /// </summary>
        public string CONTACT_NAME { get; set; } = "";
        /// <summary>
        /// 联系方式(经销商)
        /// </summary>
        public string PHONE_NO { get; set; } = "";
        /// <summary>
        /// 微信(经销商)
        /// </summary>
        public string CONTACT_WX { get; set; } = "";
        /// <summary>
        /// 邮箱(经销商)
        /// </summary>
        public string E_MAIL { get; set; } = "";
        /// <summary>
        /// 备注(经销商)
        /// </summary>
        public string REMARK { get; set; } = "";

    }

    /// <summary>
    /// 制造商联系人信息
    /// </summary>
    public class EquipmentManufacturerContact
    {

        public int NO { get; set; } = 1;
        public string CONTACT_TYPE { get; set; } = "";
        public string CONTACT_POST { get; set; } = "";
        public string CONTACT_NAME { get; set; } = "";
        public string PHONE_NO { get; set; } = "";
        public string CONTACT_WX { get; set; } = "";
        public string E_MAIL { get; set; } = "";
        public string REMARK { get; set; } = "";

    }


    /// <summary>
    /// 环境要求信息
    /// </summary>
    public class EnvironmentInfo
    {
        /// <summary>
        /// 长(尺寸(mm))
        /// </summary>
        public string? LENGTH { get; set; } = "";

        /// <summary>
        /// 宽(尺寸(mm))
        /// </summary>
        public string? WIDTH { get; set; } = "";
        /// <summary>
        /// 高(尺寸(mm))
        /// </summary>
        public string? HEIGHT { get; set; } = "";
        /// <summary>
        /// 重量(kg)
        /// </summary>
        public string? EQUIPMENT_WEIGHT { get; set; } = "";
        /// <summary>
        /// 承重要求(kg)
        /// </summary>
        public string? BEARING_REQUIRE { get; set; } = "";
        /// <summary>
        /// 空间(m3)
        /// </summary>
        public string? SPACE_REQUIRE { get; set; } = "";
        /// <summary>
        /// 空间洁净度(级)
        /// </summary>
        public string? AIR_REQUIRE { get; set; } = "";
        /// <summary>
        /// 水质要求(Ω)
        /// </summary>
        public string? WATER_REQUIRE { get; set; } = "";
        /// <summary>
        /// 最低温度(℃)
        /// </summary>
        public string? TEMP_MIN { get; set; } = "";
        /// <summary>
        /// 最高温度(℃)
        /// </summary>
        public string? TEMP_MAX { get; set; } = "";
        /// <summary>
        /// 低湿度(%)
        /// </summary>
        public string? HUMI_MIN { get; set; } = "";
        /// <summary>
        /// 最高湿度(%)
        /// </summary>
        public string? HUMI_MAX { get; set; } = "";
        /// <summary>
        /// 气压(Pa)
        /// </summary>
        public string? AIR_PRESSURE_REQUIRE { get; set; } = "";
        /// <summary>
        /// 功率(W)
        /// </summary>
        public string? POWER_REQUIRE { get; set; } = "";
        /// <summary>
        /// 电压(V)
        /// </summary>
        public string? VOLTAGE_REQUIRE { get; set; } = "";
        /// <summary>
        /// 电流(A)
        /// </summary>
        public string? ELECTRICITY_REQUIRE { get; set; } = "";
        /// <summary>
        /// 其他
        /// </summary>
        public string? OTHER_REQUIRE { get; set; } = "";

    }

}
