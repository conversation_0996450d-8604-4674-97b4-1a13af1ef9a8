﻿using H.Utility;
using System.Collections.Generic;
using XH.H82.Models.Dtos.FileTemplate;
using XH.H82.Models.Entities;
using XH.H82.Models.Entities.FileTemplate;

namespace XH.H82.IServices.EquipmentOnlyOffice
{
    public interface IEquipmentOnlyOfficeService
    {
        /// <summary>
        /// 调用H115返回导出模板文件
        /// </summary>
        /// <param name="templateFillDataDto"></param>
        /// <returns></returns>
        public byte[] ExportStyleFile(StyleTemplateFillDataDto templateFillDataDto);

        /// <summary>
        /// 调用H115返回导出模板文件
        /// </summary>
        /// <param name="templateFillDataDto"></param>
        /// <returns></returns>
        public byte[] ExportStylePDFFile(StyleTemplateFillDataDto templateFillDataDto);

        /// <summary>
        /// 调用H115返回导出模板文件
        /// </summary>
        /// <param name="templateFillDataDto"></param>
        /// <returns></returns>
        public byte[] PreviewFile(StyleTemplateFillDataDto templateFillDataDto);

        ResultDto LoadExcelData(OaExcelFillDataDto requestBody);


        StyleTemplateFillDataDto FillEquipmentInfos(string styleId, List<EquipmentInfoData> datas);

        StyleTemplateFillDataDto FillEquipmentInfo(string styleId, EquipmentInfoData data);

        StyleTemplateFillDataDto FillEquipmentTextAndRecords(string styleId, EquipmentInfoData data);
        
        List<EquipmentInfoData> FillEquipmentInfoDatas(string styleId, List<string> ids , bool isExcel = false);


        OaExcelFillDataDto FillExcelModelEquipments(string styleId, List<EquipmentInfoData> datas);

    }
}
