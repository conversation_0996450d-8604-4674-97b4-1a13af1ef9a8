﻿using H.BASE;

namespace XH.H82.Models;

public class OaBaseDataContent
{
    public string? DataId { get; set; }
    public string? DataName { get; set; }
    public string? DataSname { get; set; }
    public string? DataEname { get; set; }
    public string? Sort { get; set; }
    public string? Remark { get; set; }
    public string? CustomCode { get; set; }
    public string? SpellCode { get; set; }
    public string? StandartId { get; set; }


    public static string CreatDataId(string classId)
    {
        
        
        return $"{AppSettingsProvider.CurrModuleId}{classId}{DateTime.Now.ToString("yyyyMMddHHmmssfff")}";
    }
}