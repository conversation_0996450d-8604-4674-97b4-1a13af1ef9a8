﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using SqlSugar;
using H.Utility.SqlSugarInfra;
using XH.H82.Models.Entities.Certificate;
using XH.H82.Models.Entities.WarnRecord;
using XH.H82.Models.Entities.InkScreen;

namespace XH.H82.Models.Entities
{
    [DBOwner("XH_OA")]
    public class EMS_EQUIPMENT_INFO
    {
        /// <summary>
        /// 单元名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string UNIT_NAME  { get; set; }

        /// <summary>
        
        /// 院区名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string? AREA_NAME { get; set; }
        /// <summary>
        /// pk
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string EQUIPMENT_ID { get; set; }
        /// <summary>
        /// 检验单元id|检验专业组id
        /// </summary>
        public string UNIT_ID { get; set; }
        /// <summary>
        /// 机构id
        /// </summary>
        public string HOSPITAL_ID { get; set; }
        /// <summary>
        /// 实验室id
        /// </summary>
        public string LAB_ID { get; set; }
        /// <summary>
        /// 专业类型 
        /// </summary>
        public string PROFESSIONAL_CLASS { get; set; }
        /// <summary>
        /// 仪器id
        /// </summary>
        public string INSTRUMENT_ID { get; set; }
        /// <summary>
        /// 系列id
        /// </summary>
        public string ESERIES_ID { get; set; }
        /// <summary>
        /// 设备序号
        /// </summary>
        public string EQUIPMENT_NUM { get; set; }
        /// <summary>
        /// 设备名称
        /// </summary>
        public string EQUIPMENT_NAME { get; set; }
        /// <summary>
        ///  科室设备编号
        /// </summary>
        public string DEPT_SECTION_NO { get; set; }
        /// <summary>
        /// 设备英文名称
        /// </summary>
        public string EQUIPMENT_ENAME { get; set; }
        /// <summary>
        /// 设备型号
        /// </summary>
        public string EQUIPMENT_MODEL { get; set; }
        /// <summary>
        /// 流水线所属（对应设备id）
        /// </summary>
        public string VEST_PIPELINE { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string PIPELINE_NAME { get; set; }
        /// <summary>
        /// 设备分类
        /// </summary>
        public string EQUIPMENT_CLASS { get; set; }
        /// <summary>
        /// 所属部门
        /// </summary>
        public string DEPT_NAME { get; set; }
        /// <summary>
        /// 设备科室编号
        /// </summary>
        public string SECTION_NO { get; set; }
        /// <summary>
        /// 排序号
        /// </summary>
        public string EQUIPMENT_SORT { get; set; }
        /// <summary>
        /// 出厂编号
        /// </summary>
        public string FACTORY_NUM { get; set; }
        /// <summary>
        /// 设备参数
        /// </summary>
        public string EQUIPMENT_FEATURE { get; set; }
        /// <summary>
        /// 购买日期
        /// </summary>
        public string BUY_DATE { get; set; }
        public string? SELL_PRICE { get; set; }
        public string KEEP_PERSON { get; set; }
        public string INSTALL_DATE { get; set; }
        public string INSTALL_AREA { get; set; }
        public string DEPRECIATION_TIME { get; set; }
        public string ANNUAL_SURVEY_DATE { get; set; }
        public string MANUFACTURER { get; set; }
        public string DEALER { get; set; }
        public string REPAIR_COMPANY { get; set; }
        public string APPLY_STATE { get; set; }
        public string CERTIFICATE_STATE { get; set; }
        public string ACCEPT_REPORT_STATE { get; set; }
        public string EQUIPMENT_GRAPH_STATE { get; set; }
        public string MANUAL_STATE { get; set; }
        public string REPAIR_PERSON { get; set; }
        public string REPAIR_PERSON_STATE { get; set; }
        public string CONTACT_PHONE { get; set; }
        public string REGISTER_PERSON { get; set; }
        public DateTime? REGISTER_TIME { get; set; }
        public string REGISTRATION_NUM { get; set; }
        public string REGISTRATION_ENUM { get; set; }

        /// <summary>
        /// 设备状态：0-未启用；1-启用；2-停用 3-报废 
        /// </summary>
        public string EQUIPMENT_STATE { get; set; }
        public string FIRST_RPERSON { get; set; }
        public DateTime? FIRST_RTIME { get; set; }
        public string LAST_MPERSON { get; set; }
        public DateTime? LAST_MTIME { get; set; }
        public string REMARK { get; set; }
        public string MANUFACTURER_ID { get; set; }
        public string DEALER_ID { get; set; }
        public string EQUIPMENT_SIZE { get; set; }
        public string EQUIPMENT_POWER { get; set; }
        public string EQUIPMENT_VOLTAGE { get; set; }
        public string EQUIPMENT_TEMP { get; set; }
        public string EQUIPMENT_TEMP_RANGE { get; set; }

        public string EQ_IN_PERSON { get; set; }
        public DateTime? EQ_IN_TIME { get; set; }
        public string EQ_OUT_PERSON { get; set; }
        public DateTime? EQ_OUT_TIME { get; set; }
        public string EQ_SCRAP_PERSON { get; set; }
        public DateTime? EQ_SCRAP_TIME { get; set; }
        public string SERIAL_NUMBER { get; set; }
        public string EQUIPMENT_CODE { get; set; }
        public string DEALER_ENAME { get; set; }
        public string MANUFACTURER_ENAME { get; set; }
        public string EQUIPMENT_TYPE { get; set; }
        public DateTime? ENABLE_TIME { get; set; }
        public string? IS_HIDE { get; set; }
        public string? EQ_SERVICE_LIFE { get; set; }
        public string? SMBL_FLAG { get; set; } = "0";
        public string? SMBL_LAB_ID { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string? SMBL_LAB_NAME { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string? EQUIPMENT_STAT_SBML { get; set; }
        /// <summary>
        /// 服务商id
        /// </summary>

        public string? PROVIDER_ID { get; set; }
        
        /// <summary>
        /// 服务商名称
        /// </summary>
        public string? PROVIDER { get; set; }

        /// <summary>
        /// 设备自身的自定义代号
        /// </summary>
        public string? EQUIPMENT_UCODE { get; set; }
        public string? SMBL_CLASS { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string? SMBL_CLASS_NAME { get; set; }
        public string? SMBL_STATE { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string MAINTAIN_INTERVALS { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string MAINTAIN_WARN_INTERVALS { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string COMPARISON_INTERVALS { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string COMPARISON_WARN_INTERVALS { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string VERIFICATION_INTERVALS { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string VERIFICATION_WARN_INTERVALS { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string CORRECT_INTERVALS { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string CORRECT_WARN_INTERVALS { get; set; }
        [SugarColumn(IsIgnore = true)]
        public DateTime? FIRST_START_TIME { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string LAB_NAME { get; set; }
        [SugarColumn(IsIgnore = true)]
        public DateTime? LAST_MAINTAIN_DATE { get; set; }
        [SugarColumn(IsIgnore = true)]
        public DateTime? LAST_COMPARISON_DATE { get; set; }
        [SugarColumn(IsIgnore = true)]
        public DateTime? LAST_VERIFICATION_DATE { get; set; }
        [SugarColumn(IsIgnore = true)]
        public DateTime? LAST_CORRECT_DATE { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string LAST_CORRECT_DEPT { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string LAST_CORRECT_RESULT { get; set; }
        [SugarColumn(IsIgnore = true)]
        public DateTime? NEXT_MAINTAIN_DATE { get; set; }
        [SugarColumn(IsIgnore = true)]
        public DateTime? NEXT_COMPARISON_DATE { get; set; }
        [SugarColumn(IsIgnore = true)]
        public DateTime? NEXT_VERIFICATION_DATE { get; set; }
        [SugarColumn(IsIgnore = true)]
        public DateTime? NEXT_CORRECT_DATE { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string MGROUP_NAME { get; set; }
        [SugarColumn(IsIgnore = true)]
        public DateTime? EARLIEST_ENABLE_DATE { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string HOSPITAL_NAME { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string MAINTAIN_TYPE { get; set; }
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToOne, nameof(EQUIPMENT_ID), nameof(EMS_INSTALL_INFO.EQUIPMENT_ID))]
        public EMS_INSTALL_INFO eMS_INSTALL_INFO { get; set; }
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToOne, nameof(EQUIPMENT_ID), nameof(EMS_PURCHASE_INFO.EQUIPMENT_ID))]
        public EMS_PURCHASE_INFO eMS_PURCHASE_INFO { get; set; }
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToOne, nameof(EQUIPMENT_ID), nameof(EMS_WORK_PLAN.EQUIPMENT_ID))]
        public EMS_WORK_PLAN eMS_WORK_PLAN { get; set; }
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToMany, nameof(EMS_MAINTAIN_INFO.EQUIPMENT_ID))]
        public List<EMS_MAINTAIN_INFO> eMS_MAINTAIN_INFO { get; set; }
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToMany, nameof(EMS_CORRECT_INFO.EQUIPMENT_ID))]
        public List<EMS_CORRECT_INFO> eMS_CORRECT_INFO { get; set; }
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToMany, nameof(EMS_COMPARISON_INFO.EQUIPMENT_ID))]
        public List<EMS_COMPARISON_INFO> eMS_COMPARISON_INFO { get; set; }
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToMany, nameof(EMS_VERIFICATION_INFO.EQUIPMENT_ID))]
        public List<EMS_VERIFICATION_INFO> eMS_VERIFICATION_INFO { get; set; }
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToMany, nameof(EMS_AUTHORIZE_INFO.EQUIPMENT_ID))]
        public List<EMS_AUTHORIZE_INFO> eMS_AUTHORIZE_INFO { get; set; }
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToMany, nameof(EMS_CHANGE_INFO.EQUIPMENT_ID))]
        public List<EMS_CHANGE_INFO> eMS_CHANGE_INFO { get; set; }
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToMany, nameof(EMS_DEBUG_INFO.EQUIPMENT_ID))]
        public List<EMS_DEBUG_INFO> eMS_DEBUG_INFO { get; set; }
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToMany, nameof(EMS_PARTS_INFO.EQUIPMENT_ID))]
        public List<EMS_PARTS_INFO> eMS_PARTS_INFO { get; set; }
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToMany, nameof(EMS_REPAIR_INFO.EQUIPMENT_ID))]
        public List<EMS_REPAIR_INFO> eMS_REPAIR_INFO { get; set; }
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToMany, nameof(EMS_SCRAP_INFO.EQUIPMENT_ID))]
        public List<EMS_SCRAP_INFO> eMS_SCRAP_INFO { get; set; }
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToMany, nameof(EMS_START_STOP.EQUIPMENT_ID))]
        public List<EMS_START_STOP> eMS_START_STOP { get; set; }
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToMany, nameof(EMS_STARTUP_INFO.EQUIPMENT_ID))]
        public List<EMS_STARTUP_INFO> eMS_STARTUP_INFO { get; set; }
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToMany, nameof(EMS_TRAIN_INFO.EQUIPMENT_ID))]
        public List<EMS_TRAIN_INFO> eMS_TRAIN_INFO { get; set; }
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToMany, nameof(EMS_UNPACK_INFO.EQUIPMENT_ID))]
        public List<EMS_UNPACK_INFO> eMS_UNPACK_INFO { get; set; }
    
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToOne, nameof(EQUIPMENT_ID), nameof(EMS_ENVI_REQUIRE_INFO.EQUIPMENT_ID))]
        public EMS_ENVI_REQUIRE_INFO eMS_ENVI_REQUIRE_INFO { get; set; }

        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToMany, nameof(EMS_EQUIPMENT_WARN.EQUIPMENT_ID))]
        public List<EMS_EQUIPMENT_WARN> eMS_EQUIPMENT_WARN { get; set; }
        /// <summary>
        /// 设备联系人
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToMany, nameof(EMS_EQUIPMENT_CONTACT.EQUIPMENT_ID))]
        public List<EMS_EQUIPMENT_CONTACT> Contacts { get; set; } 
        /// <summary>
        /// 设备相关的警告记录
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToMany, nameof(EMS_EQUIPMENT_WARN.EQUIPMENT_ID))]
        public List<EMS_EQUIPMENT_WARN> WarnRecords { get; set; }

        /// <summary>
        /// 设备绑定的水墨屏设备
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToOne, nameof(EQUIPMENT_ID), nameof(EMS_INKSCREEN_INFO.EQUIPMENT_ID))]
        public EMS_INKSCREEN_INFO Inkscreen { get; set; }
        
        
        /// <summary>
        /// 设备其他附件信息
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToMany,  nameof(EMS_DOC_INFO.DOC_INFO_ID))]
        public List<EMS_DOC_INFO>  Documents { get; set; } 

        /// <summary>
        /// 证书附件信息/外部链接文件
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToMany,  nameof(EMS_DOC_INFO.EQUIPMENT_ID))]
        public List<EMS_CERTIFICATE_INFO>  CertificateInfos { get; set; }


        
        /// <summary>
        /// 监测在线状态
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string?  SWITCH_STATUS { get; set; }


        /// <summary>
        /// 是否被生安监测
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public bool IS_MONITOR { get; set; } = false;
        
        public CountryOriginEnum COUNTRY_ORIGIN { get; set; }
        /// <summary>
        /// 房间位置id
        /// </summary>
        public string? POSITION_ID { get; set; }
        /// <summary>
        /// 拓展字段
        /// </summary>
        public string? EQUIPMENT_JSON { get; set; }
    }


    /// <summary>
    /// 设备产出地 （国产，进口）
    /// </summary>
    public enum CountryOriginEnum
    {
        /// <summary>
        /// 国产
        /// </summary>
        [Description("国产")]
        Domestic = 1,
        /// <summary>
        /// 进口
        /// </summary>
        [Description("进口")]
        Abroad = 2,
    }
}
