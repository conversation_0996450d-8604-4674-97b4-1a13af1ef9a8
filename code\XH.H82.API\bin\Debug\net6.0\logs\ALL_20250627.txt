2025-06-27 10:26:39.683 +08:00 [INF] ==>App Start..2025-06-27 10:26:39
2025-06-27 10:26:39.923 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-06-27 10:26:39.927 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-06-27 10:26:42.291 +08:00 [INF] ==>基础连接请求完成.
2025-06-27 10:26:42.709 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-06-27 10:26:43.340 +08:00 [INF]  正在通过S01写入版本信息...
2025-06-27 10:26:43.672 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-06-27 10:26:43.697 +08:00 [INF]  正在通过H04写入版本信息...
2025-06-27 10:26:44.047 +08:00 [INF] ==>版本写入成功:6.25.300
2025-06-27 10:26:46.108 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-06-27 10:26:46.591 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-06-27 10:26:47.156 +08:00 [INF] 正在检查旧数据是否完整...
2025-06-27 10:26:47.157 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-06-27 10:26:48.121 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-06-27 10:26:50.453 +08:00 [INF] ==>初始化完成..
2025-06-27 10:26:50.523 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-06-27 10:26:50.527 +08:00 [INF] 设备启用任务
2025-06-27 10:26:50.528 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-06-27 10:26:50.949 +08:00 [INF] 【SQL执行耗时:387.1908ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-06-27 10:26:51.188 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-06-27 10:26:51.203 +08:00 [INF] Now listening on: https://[::]:18482
2025-06-27 10:26:51.205 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-27 10:26:51.206 +08:00 [INF] Hosting environment: Development
2025-06-27 10:26:51.206 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-06-27 10:26:52.593 +08:00 [INF] HTTP GET /favicon.ico responded 404 in 283.7956 ms
2025-06-27 10:43:14.187 +08:00 [INF] HTTP GET /api/Base/GetMenuInfo responded 401 in 177.1136 ms
2025-06-27 10:43:21.171 +08:00 [INF] 【SQL执行耗时:548.5861ms】

[Sql]: SELECT "SETUP_ID","FUNC_ID","HOSPITAL_ID","FIRST_RPERSON","SKIN_ID","LAST_MTIME","IF_DOUBLE_CLICK","FORM_QUERY_JSON","SETUP_CNAME","SETUP_SORT","SETUP_STATE","SETUP_DESC","FORM_COL_JSON","MODULE_ID","REMARK","FIRST_RTIME","LAST_MPERSON","FORM_JSON","IF_QUERY","SETUP_NAME","SETUP_CLASS"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ((( "MODULE_ID" = :MODULE_ID0 ) AND ( "HOSPITAL_ID" = :HOSPITAL_ID1 )) AND ( "SETUP_NAME" = :SETUP_NAME2 )) AND   ROWNUM = 1  
[Pars]:
[Name]::MODULE_ID0 [Value]:H82 [Type]:AnsiString    
[Name]::HOSPITAL_ID1 [Value]:33A001 [Type]:AnsiString    
[Name]::SETUP_NAME2 [Value]:导航栏 [Type]:AnsiString    

2025-06-27 10:43:21.520 +08:00 [ERR] GetMenuInfo:H.Utility.BizException: 请求Header里，需按登录入口选择的单元添加参数"Xh-lab-labid"，不能为空！
   at XH.LAB.UTILS.Implement.AuthorityService2.GetUserMenuList(Object soa, String moduleId, MenuClassEnum menuClassStr, String parentMenuId)
   at Castle.Proxies.Invocations.IAuthorityService2_GetUserMenuList.InvokeMethodOnTarget()
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.UseCacheWhenFailedAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.CacheAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at Castle.Proxies.IAuthorityService2Proxy.GetUserMenuList(Object soa, String moduleId, MenuClassEnum menuClassStr, String parentMenuId)
   at XH.H82.Services.BaseService.GetMenuInfo(String hospitalId, String moduleId, String userNo) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\BaseService.cs:line 986
2025-06-27 10:43:21.535 +08:00 [INF] HTTP GET /api/Base/GetMenuInfo responded 200 in 5667.3856 ms
2025-06-27 10:43:21.537 +08:00 [INF] 【接口超时阀值预警】 [982361f9834edea4b6f621d1283601ff]接口/api/Base/GetMenuInfo,耗时:[5667]毫秒
2025-06-27 10:44:02.530 +08:00 [INF] 【SQL执行耗时:342.3793ms】

[Sql]: SELECT "SETUP_ID","FUNC_ID","HOSPITAL_ID","FIRST_RPERSON","SKIN_ID","LAST_MTIME","IF_DOUBLE_CLICK","FORM_QUERY_JSON","SETUP_CNAME","SETUP_SORT","SETUP_STATE","SETUP_DESC","FORM_COL_JSON","MODULE_ID","REMARK","FIRST_RTIME","LAST_MPERSON","FORM_JSON","IF_QUERY","SETUP_NAME","SETUP_CLASS"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ((( "MODULE_ID" = :MODULE_ID0 ) AND ( "HOSPITAL_ID" = :HOSPITAL_ID1 )) AND ( "SETUP_NAME" = :SETUP_NAME2 )) AND   ROWNUM = 1  
[Pars]:
[Name]::MODULE_ID0 [Value]:H82 [Type]:AnsiString    
[Name]::HOSPITAL_ID1 [Value]:33A001 [Type]:AnsiString    
[Name]::SETUP_NAME2 [Value]:导航栏 [Type]:AnsiString    

2025-06-27 10:44:03.687 +08:00 [INF] 调用H07模块[/api/External/GetUserMenuInfoPost?moduleId=H82&unitClass=2&unitId=PG006&specialJson=[{"unitClass":"2","unitId":"PG005"},{"unitClass":"2","unitId":"PG095"},{"unitClass":"2","unitId":"PG013"},{"unitClass":"2","unitId":"PG055"},{"unitClass":"2","unitId":"PG054"},{"unitClass":"2","unitId":"PG001"},{"unitClass":"11","unitId":"L001"},{"unitClass":"19","unitId":"MG014"},{"unitClass":"19","unitId":"MG012"},{"unitClass":"19","unitId":"MG013"},{"unitClass":"19","unitId":"MG025"},{"unitClass":"19","unitId":"MG002"},{"unitClass":"19","unitId":"MG034"},{"unitClass":"19","unitId":"MG026"},{"unitClass":"19","unitId":"MG033"},{"unitClass":"19","unitId":"MG031"}]],耗时:933ms
2025-06-27 10:44:03.792 +08:00 [INF] HTTP GET /api/Base/GetMenuInfo responded 200 in 1651.7133 ms
2025-06-27 10:44:03.792 +08:00 [INF] 【接口超时阀值预警】 [d7ca895be575daa444c6c6b928f7b9e7]接口/api/Base/GetMenuInfo,耗时:[1652]毫秒
2025-06-27 10:44:19.511 +08:00 [INF] 【SQL执行耗时:360.7657ms】

[Sql]: SELECT "SETUP_ID","FUNC_ID","HOSPITAL_ID","FIRST_RPERSON","SKIN_ID","LAST_MTIME","IF_DOUBLE_CLICK","FORM_QUERY_JSON","SETUP_CNAME","SETUP_SORT","SETUP_STATE","SETUP_DESC","FORM_COL_JSON","MODULE_ID","REMARK","FIRST_RTIME","LAST_MPERSON","FORM_JSON","IF_QUERY","SETUP_NAME","SETUP_CLASS"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ((( "MODULE_ID" = :MODULE_ID0 ) AND ( "HOSPITAL_ID" = :HOSPITAL_ID1 )) AND ( "SETUP_NAME" = :SETUP_NAME2 )) AND   ROWNUM = 1  
[Pars]:
[Name]::MODULE_ID0 [Value]:H82 [Type]:AnsiString    
[Name]::HOSPITAL_ID1 [Value]:33A001 [Type]:AnsiString    
[Name]::SETUP_NAME2 [Value]:导航栏 [Type]:AnsiString    

2025-06-27 10:45:43.534 +08:00 [INF] HTTP GET /api/Base/GetMenuInfo responded 200 in 84419.6890 ms
2025-06-27 10:45:43.535 +08:00 [INF] 【接口超时阀值预警】 [8c242220ba2179f15ea497e3d0101afb]接口/api/Base/GetMenuInfo,耗时:[84420]毫秒
2025-06-27 10:46:12.561 +08:00 [INF] 【SQL执行耗时:366.5273ms】

[Sql]: SELECT "SETUP_ID","FUNC_ID","HOSPITAL_ID","FIRST_RPERSON","SKIN_ID","LAST_MTIME","IF_DOUBLE_CLICK","FORM_QUERY_JSON","SETUP_CNAME","SETUP_SORT","SETUP_STATE","SETUP_DESC","FORM_COL_JSON","MODULE_ID","REMARK","FIRST_RTIME","LAST_MPERSON","FORM_JSON","IF_QUERY","SETUP_NAME","SETUP_CLASS"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ((( "MODULE_ID" = :MODULE_ID0 ) AND ( "HOSPITAL_ID" = :HOSPITAL_ID1 )) AND ( "SETUP_NAME" = :SETUP_NAME2 )) AND   ROWNUM = 1  
[Pars]:
[Name]::MODULE_ID0 [Value]:H82 [Type]:AnsiString    
[Name]::HOSPITAL_ID1 [Value]:33A001 [Type]:AnsiString    
[Name]::SETUP_NAME2 [Value]:导航栏 [Type]:AnsiString    

2025-06-27 10:57:33.391 +08:00 [INF] ==>App Start..2025-06-27 10:57:33
2025-06-27 10:57:33.581 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-06-27 10:57:33.585 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-06-27 10:57:38.243 +08:00 [INF] ==>基础连接请求完成.
2025-06-27 10:57:38.622 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-06-27 10:57:39.032 +08:00 [INF]  正在通过S01写入版本信息...
2025-06-27 10:57:39.380 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-06-27 10:57:39.389 +08:00 [INF]  正在通过H04写入版本信息...
2025-06-27 10:57:39.743 +08:00 [INF] ==>版本写入成功:6.25.300
2025-06-27 10:57:40.257 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-06-27 10:57:40.368 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-06-27 10:57:40.834 +08:00 [INF] 正在检查旧数据是否完整...
2025-06-27 10:57:40.835 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-06-27 10:57:42.053 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-06-27 10:57:49.227 +08:00 [INF] ==>初始化完成..
2025-06-27 10:57:49.270 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-06-27 10:57:49.271 +08:00 [INF] 设备启用任务
2025-06-27 10:57:49.272 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-06-27 10:57:49.652 +08:00 [INF] 【SQL执行耗时:359.665ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-06-27 10:57:49.787 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-06-27 10:57:49.799 +08:00 [INF] Now listening on: https://[::]:18482
2025-06-27 10:57:49.801 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-27 10:57:49.801 +08:00 [INF] Hosting environment: Development
2025-06-27 10:57:49.801 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-06-27 10:58:48.058 +08:00 [INF] 【SQL执行耗时:680.7954ms】

[Sql]: SELECT "SETUP_ID","FUNC_ID","HOSPITAL_ID","FIRST_RPERSON","SKIN_ID","LAST_MTIME","IF_DOUBLE_CLICK","FORM_QUERY_JSON","SETUP_CNAME","SETUP_SORT","SETUP_STATE","SETUP_DESC","FORM_COL_JSON","MODULE_ID","REMARK","FIRST_RTIME","LAST_MPERSON","FORM_JSON","IF_QUERY","SETUP_NAME","SETUP_CLASS"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ((( "MODULE_ID" = :MODULE_ID0 ) AND ( "HOSPITAL_ID" = :HOSPITAL_ID1 )) AND ( "SETUP_NAME" = :SETUP_NAME2 )) AND   ROWNUM = 1  
[Pars]:
[Name]::MODULE_ID0 [Value]:H82 [Type]:AnsiString    
[Name]::HOSPITAL_ID1 [Value]:33A001 [Type]:AnsiString    
[Name]::SETUP_NAME2 [Value]:导航栏 [Type]:AnsiString    

2025-06-27 10:58:48.899 +08:00 [INF] 【SQL执行耗时:357.8853ms】

[Sql]: SELECT "SETUP_ID","FUNC_ID","HOSPITAL_ID","FIRST_RPERSON","SKIN_ID","LAST_MTIME","IF_DOUBLE_CLICK","FORM_QUERY_JSON","SETUP_CNAME","SETUP_SORT","SETUP_STATE","SETUP_DESC","FORM_COL_JSON","MODULE_ID","REMARK","FIRST_RTIME","LAST_MPERSON","FORM_JSON","IF_QUERY","SETUP_NAME","SETUP_CLASS"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ((( "MODULE_ID" = :MODULE_ID0 ) AND ( "HOSPITAL_ID" = :HOSPITAL_ID1 )) AND ( "SETUP_NAME" = :SETUP_NAME2 )) AND   ROWNUM = 1  
[Pars]:
[Name]::MODULE_ID0 [Value]:H82 [Type]:AnsiString    
[Name]::HOSPITAL_ID1 [Value]:33A001 [Type]:AnsiString    
[Name]::SETUP_NAME2 [Value]:导航栏 [Type]:AnsiString    

2025-06-27 11:00:12.852 +08:00 [INF] 【SQL执行耗时:474.9083ms】

[Sql]: SELECT "SETUP_ID","FUNC_ID","HOSPITAL_ID","FIRST_RPERSON","SKIN_ID","LAST_MTIME","IF_DOUBLE_CLICK","FORM_QUERY_JSON","SETUP_CNAME","SETUP_SORT","SETUP_STATE","SETUP_DESC","FORM_COL_JSON","MODULE_ID","REMARK","FIRST_RTIME","LAST_MPERSON","FORM_JSON","IF_QUERY","SETUP_NAME","SETUP_CLASS"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ((( "MODULE_ID" = :MODULE_ID0 ) AND ( "HOSPITAL_ID" = :HOSPITAL_ID1 )) AND ( "SETUP_NAME" = :SETUP_NAME2 )) AND   ROWNUM = 1  
[Pars]:
[Name]::MODULE_ID0 [Value]:H81 [Type]:AnsiString    
[Name]::HOSPITAL_ID1 [Value]:33A001 [Type]:AnsiString    
[Name]::SETUP_NAME2 [Value]:导航栏 [Type]:AnsiString    

2025-06-27 11:00:31.349 +08:00 [INF] 【SQL执行耗时:356.6616ms】

[Sql]: SELECT "SETUP_ID","FUNC_ID","HOSPITAL_ID","FIRST_RPERSON","SKIN_ID","LAST_MTIME","IF_DOUBLE_CLICK","FORM_QUERY_JSON","SETUP_CNAME","SETUP_SORT","SETUP_STATE","SETUP_DESC","FORM_COL_JSON","MODULE_ID","REMARK","FIRST_RTIME","LAST_MPERSON","FORM_JSON","IF_QUERY","SETUP_NAME","SETUP_CLASS"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ((( "MODULE_ID" = :MODULE_ID0 ) AND ( "HOSPITAL_ID" = :HOSPITAL_ID1 )) AND ( "SETUP_NAME" = :SETUP_NAME2 )) AND   ROWNUM = 1  
[Pars]:
[Name]::MODULE_ID0 [Value]:H92 [Type]:AnsiString    
[Name]::HOSPITAL_ID1 [Value]:33A001 [Type]:AnsiString    
[Name]::SETUP_NAME2 [Value]:导航栏 [Type]:AnsiString    

2025-06-27 11:45:03.940 +08:00 [INF] ==>App Start..2025-06-27 11:45:03
2025-06-27 11:45:04.124 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-06-27 11:45:04.127 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-06-27 11:45:06.068 +08:00 [INF] ==>基础连接请求完成.
2025-06-27 11:45:06.433 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-06-27 11:45:06.842 +08:00 [INF]  正在通过S01写入版本信息...
2025-06-27 11:45:07.145 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-06-27 11:45:07.148 +08:00 [INF]  正在通过H04写入版本信息...
2025-06-27 11:45:07.480 +08:00 [INF] ==>版本写入成功:6.25.300
2025-06-27 11:45:09.254 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-06-27 11:45:09.685 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-06-27 11:45:10.206 +08:00 [INF] 正在检查旧数据是否完整...
2025-06-27 11:45:10.206 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-06-27 11:45:11.256 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-06-27 11:45:13.516 +08:00 [INF] ==>初始化完成..
2025-06-27 11:45:13.569 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-06-27 11:45:13.572 +08:00 [INF] 设备启用任务
2025-06-27 11:45:13.573 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-06-27 11:45:13.969 +08:00 [INF] 【SQL执行耗时:373.4666ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-06-27 11:45:14.126 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-06-27 11:45:14.140 +08:00 [INF] Now listening on: https://[::]:18482
2025-06-27 11:45:14.142 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-27 11:45:14.142 +08:00 [INF] Hosting environment: Development
2025-06-27 11:45:14.143 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-06-27 11:46:03.790 +08:00 [INF] 【SQL执行耗时:368.0298ms】

[Sql]: SELECT "FUNC_ID"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ((( "MODULE_ID" = :MODULE_ID0 ) AND ( "HOSPITAL_ID" = :HOSPITAL_ID1 )) AND ( "SETUP_NAME" = :SETUP_NAME2 )) AND   ROWNUM = 1  
[Pars]:
[Name]::MODULE_ID0 [Value]:H82 [Type]:AnsiString    
[Name]::HOSPITAL_ID1 [Value]:33A001 [Type]:AnsiString    
[Name]::SETUP_NAME2 [Value]:导航栏 [Type]:AnsiString    

2025-06-27 11:46:15.082 +08:00 [ERR] 未处理的异常::H.Utility.BizException: 请求Header里，需按登录入口选择的单元添加参数"Xh-lab-labid"，不能为空！
   at XH.LAB.UTILS.Implement.AuthorityService2.GetUserMenuList(Object soa, String moduleId, MenuClassEnum menuClassStr, String parentMenuId)
   at Castle.Proxies.Invocations.IAuthorityService2_GetUserMenuList.InvokeMethodOnTarget()
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.UseCacheWhenFailedAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.CacheAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at Castle.Proxies.IAuthorityService2Proxy.GetUserMenuList(Object soa, String moduleId, MenuClassEnum menuClassStr, String parentMenuId)
   at XH.H82.Services.BaseService.GetMenuInfoByPermission() in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\BaseService.cs:line 1085
   at Castle.Proxies.Invocations.IBaseService_GetMenuInfoByPermission.InvokeMethodOnTarget()
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.UseCacheWhenFailedAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.CacheAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at Castle.Proxies.IBaseServiceProxy.GetMenuInfoByPermission()
   at XH.H82.API.Controllers.BaseController.GetMenuInfo() in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\Controllers\BaseController.cs:line 282
   at lambda_method908(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-06-27 11:46:15.093 +08:00 [INF] HTTP GET /api/Base/GetMenuInfo responded 200 in 16233.6547 ms
2025-06-27 11:46:15.095 +08:00 [INF] 【接口超时阀值预警】 [ea13f0d28078bcf06c34ec8e2f980390]接口/api/Base/GetMenuInfo,耗时:[16243]毫秒
2025-06-27 11:46:22.430 +08:00 [INF] HTTP GET /api/Base/GetMenuInfo responded 401 in 14.9949 ms
2025-06-27 11:46:25.686 +08:00 [INF] 【SQL执行耗时:367.549ms】

[Sql]: SELECT "FUNC_ID"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ((( "MODULE_ID" = :MODULE_ID0 ) AND ( "HOSPITAL_ID" = :HOSPITAL_ID1 )) AND ( "SETUP_NAME" = :SETUP_NAME2 )) AND   ROWNUM = 1  
[Pars]:
[Name]::MODULE_ID0 [Value]:H82 [Type]:AnsiString    
[Name]::HOSPITAL_ID1 [Value]:33A001 [Type]:AnsiString    
[Name]::SETUP_NAME2 [Value]:导航栏 [Type]:AnsiString    

2025-06-27 11:46:26.316 +08:00 [INF] 调用H07模块[/api/External/GetUserUnitInfo?moduleId=H82],耗时:555ms
2025-06-27 11:46:27.265 +08:00 [INF] 调用H07模块[/api/External/GetUserMenuInfoPost?moduleId=H82&unitClass=2&unitId=PG006&specialJson=[{"unitClass":"2","unitId":"PG005"},{"unitClass":"2","unitId":"PG095"},{"unitClass":"2","unitId":"PG013"},{"unitClass":"2","unitId":"PG055"},{"unitClass":"2","unitId":"PG054"},{"unitClass":"2","unitId":"PG001"},{"unitClass":"11","unitId":"L001"},{"unitClass":"19","unitId":"MG014"},{"unitClass":"19","unitId":"MG012"},{"unitClass":"19","unitId":"MG013"},{"unitClass":"19","unitId":"MG025"},{"unitClass":"19","unitId":"MG002"},{"unitClass":"19","unitId":"MG034"},{"unitClass":"19","unitId":"MG026"},{"unitClass":"19","unitId":"MG033"},{"unitClass":"19","unitId":"MG031"}]],耗时:864ms
2025-06-27 11:46:41.146 +08:00 [ERR] 未处理的异常::System.NullReferenceException: Object reference not set to an instance of an object.
   at XH.H82.Services.BaseService.GetMenuInfoByPermission() in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\BaseService.cs:line 1111
   at Castle.Proxies.Invocations.IBaseService_GetMenuInfoByPermission.InvokeMethodOnTarget()
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.UseCacheWhenFailedAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.CacheAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at Castle.Proxies.IBaseServiceProxy.GetMenuInfoByPermission()
   at XH.H82.API.Controllers.BaseController.GetMenuInfo() in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\Controllers\BaseController.cs:line 282
   at lambda_method908(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-06-27 11:46:41.147 +08:00 [ERR] HTTP GET /api/Base/GetMenuInfo responded 500 in 15917.5313 ms
2025-06-27 11:46:41.148 +08:00 [INF] 【接口超时阀值预警】 [67d683d44615db25f56a9de1c71589fd]接口/api/Base/GetMenuInfo,耗时:[15918]毫秒
2025-06-27 11:46:45.101 +08:00 [INF] 【SQL执行耗时:366.6791ms】

[Sql]: SELECT "FUNC_ID"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ((( "MODULE_ID" = :MODULE_ID0 ) AND ( "HOSPITAL_ID" = :HOSPITAL_ID1 )) AND ( "SETUP_NAME" = :SETUP_NAME2 )) AND   ROWNUM = 1  
[Pars]:
[Name]::MODULE_ID0 [Value]:H82 [Type]:AnsiString    
[Name]::HOSPITAL_ID1 [Value]:33A001 [Type]:AnsiString    
[Name]::SETUP_NAME2 [Value]:导航栏 [Type]:AnsiString    

2025-06-27 11:50:17.966 +08:00 [INF] ==>App Start..2025-06-27 11:50:17
2025-06-27 11:50:18.155 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-06-27 11:50:18.159 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-06-27 11:50:19.659 +08:00 [INF] ==>基础连接请求完成.
2025-06-27 11:50:20.034 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-06-27 11:50:20.419 +08:00 [INF]  正在通过S01写入版本信息...
2025-06-27 11:50:20.739 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-06-27 11:50:20.741 +08:00 [INF]  正在通过H04写入版本信息...
2025-06-27 11:50:21.234 +08:00 [INF] ==>版本写入成功:6.25.300
2025-06-27 11:50:21.561 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-06-27 11:50:21.655 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-06-27 11:50:22.066 +08:00 [INF] 正在检查旧数据是否完整...
2025-06-27 11:50:22.066 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-06-27 11:50:23.026 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-06-27 11:50:25.325 +08:00 [INF] ==>初始化完成..
2025-06-27 11:50:25.346 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-06-27 11:50:25.349 +08:00 [INF] 设备启用任务
2025-06-27 11:50:25.350 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-06-27 11:50:25.765 +08:00 [INF] 【SQL执行耗时:394.6582ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-06-27 11:50:25.910 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-06-27 11:50:25.926 +08:00 [INF] Now listening on: https://[::]:18482
2025-06-27 11:50:25.928 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-27 11:50:25.928 +08:00 [INF] Hosting environment: Development
2025-06-27 11:50:25.929 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-06-27 11:50:32.256 +08:00 [INF] 【SQL执行耗时:363.3011ms】

[Sql]: SELECT "FUNC_ID"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ((( "MODULE_ID" = :MODULE_ID0 ) AND ( "HOSPITAL_ID" = :HOSPITAL_ID1 )) AND ( "SETUP_NAME" = :SETUP_NAME2 )) AND   ROWNUM = 1  
[Pars]:
[Name]::MODULE_ID0 [Value]:H82 [Type]:AnsiString    
[Name]::HOSPITAL_ID1 [Value]:33A001 [Type]:AnsiString    
[Name]::SETUP_NAME2 [Value]:导航栏 [Type]:AnsiString    

2025-06-27 11:50:32.545 +08:00 [INF] HTTP GET /api/Base/GetMenuInfo responded 200 in 5493.8895 ms
2025-06-27 11:50:32.548 +08:00 [INF] 【接口超时阀值预警】 [1accc13856a2af8adc5c2ceb91fa3b94]接口/api/Base/GetMenuInfo,耗时:[5502]毫秒
2025-06-27 11:50:43.851 +08:00 [INF] 【SQL执行耗时:350.6937ms】

[Sql]: SELECT "FUNC_ID"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ((( "MODULE_ID" = :MODULE_ID0 ) AND ( "HOSPITAL_ID" = :HOSPITAL_ID1 )) AND ( "SETUP_NAME" = :SETUP_NAME2 )) AND   ROWNUM = 1  
[Pars]:
[Name]::MODULE_ID0 [Value]:H82 [Type]:AnsiString    
[Name]::HOSPITAL_ID1 [Value]:33A001 [Type]:AnsiString    
[Name]::SETUP_NAME2 [Value]:导航栏 [Type]:AnsiString    

2025-06-27 11:51:40.670 +08:00 [INF] HTTP GET /api/Base/GetMenuInfo responded 200 in 57219.2288 ms
2025-06-27 11:51:40.671 +08:00 [INF] 【接口超时阀值预警】 [9fc75845b11c5045740f2f57c55e1ebc]接口/api/Base/GetMenuInfo,耗时:[57219]毫秒
2025-06-27 11:52:47.051 +08:00 [INF] 【SQL执行耗时:341.5064ms】

[Sql]: SELECT "FUNC_ID"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ((( "MODULE_ID" = :MODULE_ID0 ) AND ( "HOSPITAL_ID" = :HOSPITAL_ID1 )) AND ( "SETUP_NAME" = :SETUP_NAME2 )) AND   ROWNUM = 1  
[Pars]:
[Name]::MODULE_ID0 [Value]:H84 [Type]:AnsiString    
[Name]::HOSPITAL_ID1 [Value]:33A001 [Type]:AnsiString    
[Name]::SETUP_NAME2 [Value]:导航栏 [Type]:AnsiString    

2025-06-27 11:52:47.251 +08:00 [INF] HTTP GET /api/Base/GetMenuInfo responded 200 in 584.8796 ms
2025-06-27 11:52:47.252 +08:00 [INF] 【接口超时阀值预警】 [87fd47369cbdb0948550f0135ac46079]接口/api/Base/GetMenuInfo,耗时:[585]毫秒
2025-06-27 11:53:01.474 +08:00 [INF] 【SQL执行耗时:360.1209ms】

[Sql]: SELECT "FUNC_ID"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ((( "MODULE_ID" = :MODULE_ID0 ) AND ( "HOSPITAL_ID" = :HOSPITAL_ID1 )) AND ( "SETUP_NAME" = :SETUP_NAME2 )) AND   ROWNUM = 1  
[Pars]:
[Name]::MODULE_ID0 [Value]:H84 [Type]:AnsiString    
[Name]::HOSPITAL_ID1 [Value]:33A001 [Type]:AnsiString    
[Name]::SETUP_NAME2 [Value]:导航栏 [Type]:AnsiString    

2025-06-27 11:53:27.336 +08:00 [INF] HTTP GET /api/Base/GetMenuInfo responded 200 in 28064.7397 ms
2025-06-27 11:53:27.337 +08:00 [INF] 【接口超时阀值预警】 [ce43d400453446866a4a64ca05d2d3c4]接口/api/Base/GetMenuInfo,耗时:[28065]毫秒
2025-06-27 11:53:40.942 +08:00 [INF] 【SQL执行耗时:379.2751ms】

[Sql]: SELECT "FUNC_ID"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ((( "MODULE_ID" = :MODULE_ID0 ) AND ( "HOSPITAL_ID" = :HOSPITAL_ID1 )) AND ( "SETUP_NAME" = :SETUP_NAME2 )) AND   ROWNUM = 1  
[Pars]:
[Name]::MODULE_ID0 [Value]:H84 [Type]:AnsiString    
[Name]::HOSPITAL_ID1 [Value]:33A001 [Type]:AnsiString    
[Name]::SETUP_NAME2 [Value]:导航栏 [Type]:AnsiString    

2025-06-27 11:54:19.456 +08:00 [INF] HTTP GET /api/Base/GetMenuInfo responded 200 in 41190.9460 ms
2025-06-27 11:54:19.515 +08:00 [INF] 【接口超时阀值预警】 [2dcda6eceb2400d20a03aa90ced9ad4d]接口/api/Base/GetMenuInfo,耗时:[41249]毫秒
2025-06-27 11:54:23.703 +08:00 [INF] 【SQL执行耗时:360.9934ms】

[Sql]: SELECT "FUNC_ID"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ((( "MODULE_ID" = :MODULE_ID0 ) AND ( "HOSPITAL_ID" = :HOSPITAL_ID1 )) AND ( "SETUP_NAME" = :SETUP_NAME2 )) AND   ROWNUM = 1  
[Pars]:
[Name]::MODULE_ID0 [Value]:H84 [Type]:AnsiString    
[Name]::HOSPITAL_ID1 [Value]:33A001 [Type]:AnsiString    
[Name]::SETUP_NAME2 [Value]:导航栏 [Type]:AnsiString    

2025-06-27 11:54:24.392 +08:00 [INF] 调用H07模块[/api/External/GetUserUnitInfo?moduleId=H84],耗时:616ms
2025-06-27 11:54:25.956 +08:00 [INF] 调用H07模块[/api/External/GetUserMenuInfoPost?moduleId=H84&unitClass=2&unitId=PG004&specialJson=[{"unitClass":"2","unitId":"PG001"},{"unitClass":"2","unitId":"LIS1006"},{"unitClass":"2","unitId":"PG055"},{"unitClass":"2","unitId":"PG052"},{"unitClass":"2","unitId":"PG005"},{"unitClass":"2","unitId":"PG020"},{"unitClass":"2","unitId":"PG092"},{"unitClass":"2","unitId":"PG095"},{"unitClass":"2","unitId":"PG032"},{"unitClass":"2","unitId":"PG114"},{"unitClass":"2","unitId":"PG119"},{"unitClass":"2","unitId":"PG053"},{"unitClass":"2","unitId":"PG054"},{"unitClass":"2","unitId":"PG003"},{"unitClass":"2","unitId":"PG006"},{"unitClass":"2","unitId":"PG008"},{"unitClass":"2","unitId":"PG013"},{"unitClass":"2","unitId":"PG081"},{"unitClass":"2","unitId":"PG082"},{"unitClass":"11","unitId":"L001"},{"unitClass":"19","unitId":"MG014"},{"unitClass":"19","unitId":"MG034"},{"unitClass":"19","unitId":"MG012"},{"unitClass":"19","unitId":"MG013"},{"unitClass":"19","unitId":"MG025"},{"unitClass":"19","unitId":"MG026"},{"unitClass":"19","unitId":"MG033"},{"unitClass":"19","unitId":"MG002"},{"unitClass":"19","unitId":"MG031"}]],耗时:1448ms
2025-06-27 11:54:27.740 +08:00 [INF] HTTP GET /api/Base/GetMenuInfo responded 200 in 5511.7055 ms
2025-06-27 11:54:27.741 +08:00 [INF] 【接口超时阀值预警】 [2c52d306267c69c66ab73358a2ad8864]接口/api/Base/GetMenuInfo,耗时:[5512]毫秒
