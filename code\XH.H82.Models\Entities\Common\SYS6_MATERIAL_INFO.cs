﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Security.Cryptography;
using XH.H82.Models.ViewDtos;
using SqlSugar;
using H.Utility.SqlSugarInfra;

namespace XH.H82.Models.Entities.Common
{
    [DBOwner("XH_SYS")]
    [SugarTable("SYS6_MATERIAL_INFO")]
    public class SYS6_MATERIAL_INFO
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string MATERIAL_ID { get; set; }

        public string HOSPITAL_ID { get; set; }

        public string? MATERIAL_CODE { get; set; }

        public string? MATERIAL_SORT { get; set; }

        public string? MATERIAL_CNAME { get; set; }

        public string? MATERIAL_SCNAME { get; set; }

        public string? MATERIAL_ENAME { get; set; }

        public string? MATERIAL_SENAME { get; set; }

        public string? MATERIAL_TYPE { get; set; }

        public string? MATERIAL_PROPERTY { get; set; }

        public string? MATERIAL_SPEC { get; set; }

        public string? IF_BATCH { get; set; }

        public string? IN_PACK { get; set; }

        public string? CONVET_RATIO { get; set; }

        public string? MATERIAL_PACK { get; set; }

        public float? MATERIAL_PRICE { get; set; }

        public int? PROMPT_TERM { get; set; }

        public string? FACTURER_ID { get; set; }

        public string? SUPPLIER_ID { get; set; }

        public string? DEPOT_ID { get; set; }

        public string? SPELL_CODE { get; set; }

        public string? MATERIAL_BAR { get; set; }

        public string? MATERIAL_IDENT { get; set; }

        public string? CATALOG_ID { get; set; }

        public string? MATERIAL_NETID { get; set; }

        public string? HIS_ID { get; set; }

        public int? BARCODE_NUM { get; set; }

        public int? OPEN_PERIOD { get; set; }

        public float? BARCODE_TYPE { get; set; }

        public int? SEARCH_LABEL { get; set; }

        public int? LOAD_CHANGE_RATE { get; set; }

        public string? LOAD_UNIT { get; set; }

        public string? STORAGE_LOCATION { get; set; }

        public int? YZ_AMOUNT_UPPER { get; set; }

        public string? MATERIAL_YZTYPE { get; set; }

        public string? MATERIAL_CLASS { get; set; }

        public string? MATERIAL_STATE { get; set; }

        public string? USE_STATE { get; set; }

        public string? FIRST_RPERSON { get; set; }

        public DateTime? FIRST_RTIME { get; set; }

        public string? LAST_MPERSON { get; set; }

        public DateTime? LAST_MTIME { get; set; }

        public string? REMARK { get; set; }

    }
}
