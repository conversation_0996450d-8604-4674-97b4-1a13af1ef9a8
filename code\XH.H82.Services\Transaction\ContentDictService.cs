﻿using AutoMapper;
using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using Microsoft.AspNetCore.Http;
using XH.H82.IServices.Transaction;
using XH.H82.Models.BusinessModuleClient;
using XH.H82.Models.Dtos.Transaction;
using XH.H82.Models.Entities.Transaction;
using XH.H82.Models.SugarDbContext;

namespace XH.H82.Services.Transaction
{
    public class ContentDictService : IContentDictService
    {

        private readonly ISqlSugarUow<SugarDbContext_Master> _dbContext;
        private readonly IHttpContextAccessor _httpContext;
        private readonly IMapper _mapper;
        private readonly ClaimsDto _user;

        public ContentDictService(ISqlSugarUow<SugarDbContext_Master> dbContext, IHttpContextAccessor httpContext, IMapper mapper)
        {
            _dbContext = dbContext;
            _httpContext = httpContext;
            _user = _httpContext.HttpContext.User.ToClaimsDto();
            _mapper = mapper;
            _dbContext.SetCreateTimeAndCreatePersonData(_user);
        }

        public void AddContentDict(string classId, string content, string? remark)
        {
            var exitsDict = _dbContext.Db.Queryable<OA_BASE_DATA>()
                   .Where(x => x.FATHER_ID == RecordContentDict.RECORD_CONTENT_DICT)
                   .Where(x => x.STATE_FLAG == "1")
                   .Where(x => x.CLASS_ID == classId)
                   .Where(x => x.DATA_NAME == content)
                   .First();
            if (exitsDict is not null)
            {
                return;
            }
            var dict = RecordContentDict.CreateRecordDict(_user.HOSPITAL_ID, _user.HIS_NAME, classId, content, remark);
            _dbContext.Db.Insertable(dict).ExecuteCommand();
        }
        public void DeleteContentDict(string id)
        {
            var dict = _dbContext.Db.Queryable<OA_BASE_DATA>()
                .Where(x => x.DATA_ID == id && x.FATHER_ID == RecordContentDict.RECORD_CONTENT_DICT && x.STATE_FLAG == "1")
                .First();

            if (dict is not null)
            {
                dict.STATE_FLAG = "2";
                _dbContext.Db.Updateable<OA_BASE_DATA>(dict).IgnoreColumns(ignoreAllNullColumns:true).ExecuteCommand();
            }
        }
        public List<ContentDictDto> GetContentDicts(string? classId, string? content)
        {

            var dicts = _dbContext.Db.Queryable<OA_BASE_DATA>()
                .OrderByDescending(x => x.FIRST_RTIME)
                  .Where(x => x.FATHER_ID == RecordContentDict.RECORD_CONTENT_DICT)
                  .Where(x => x.STATE_FLAG == "1")
                  .WhereIF(classId.IsNotNullOrEmpty(), x => x.CLASS_ID == classId)
                  .WhereIF(content.IsNotNullOrEmpty(), x => x.DATA_NAME.Contains(content))
                  .ToList();

            if (dicts is null)
            {
                return new List<ContentDictDto>();
            }
            else
            {
                var result = _mapper.Map<List<ContentDictDto>>(dicts);
                return result;
            }


        }
        public void UpdateContentDict(string id, string content, string? remark)
        {
            var dict = _dbContext.Db.Queryable<OA_BASE_DATA>()
                .Where(x => x.DATA_ID == id && x.FATHER_ID == RecordContentDict.RECORD_CONTENT_DICT && x.STATE_FLAG == "1").First();
            if (dict is not null)
            {
                dict.DATA_NAME = content;
                dict.REMARK = remark;
                _dbContext.Db.Updateable<OA_BASE_DATA>(dict).IgnoreColumns(ignoreAllNullColumns:true).ExecuteCommand();
            }

        }
    }
}
