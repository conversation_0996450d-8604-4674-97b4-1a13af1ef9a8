﻿using System.Data;
using OfficeOpenXml;

namespace XH.H82.Base.Helper;

public static class ExcelHelper
{
    public static MemoryStream EpplusExportExcelStream(DataSet ds, string tabNames)
    {
        string[] strName = tabNames.Split(',');
        int count = 0;
        MemoryStream memoryStream = new MemoryStream();
        //指定EPPlus使用非商业证书
        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        using (ExcelPackage package = new ExcelPackage(memoryStream))
        {
            if (ds != null)
            {
                foreach (DataTable dt in ds.Tables)
                {
                    if (dt != null && dt.Rows.Count > 0)
                    {
                        string tabName = dt.TableName;
                        if (count <= strName.Length)
                        {
                            tabName = strName[count];
                        }
                        ExcelWorksheet worksheet = package.Workbook.Worksheets.Add(tabName);
                        for (int i = 0; i < dt.Rows.Count; i++)
                        {
                            for (int c = 0; c < dt.Columns.Count; c++)
                            {
                                worksheet.Cells[i + 1, c + 1].Value = dt.Rows[i][c].ToString();
                            }
                        }
                    }
                    count++;
                }
                package.Save();
            }                
        }
        memoryStream.Position = 0;
        return memoryStream;
    }
    
    // 将数据集写入到 Excel 模板文件并返回文件流
    public static MemoryStream WriteDataSetToExcelTemplate(DataSet dataSet, string templateFilePath)
    {
        if (!File.Exists(templateFilePath))
        {
            throw new FileNotFoundException("Excel 模板文件不存在", templateFilePath);
        }
        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        using (ExcelPackage package = new ExcelPackage(new FileInfo(templateFilePath)))
        {
            try
            {
                foreach (DataTable table in dataSet.Tables)
                {
                    string sheetName = table.TableName; // 假设工作表名称与数据表名称对应
                    ExcelWorksheet worksheet = package.Workbook.Worksheets[sheetName];

                    if (worksheet == null)
                    {
                         //如果工作表不存在，可以创建一个新工作表
                            worksheet = package.Workbook.Worksheets.Add(sheetName);
                    }
                    // 将 DataTable 数据写入工作表
                    worksheet.Cells["A1"].LoadFromDataTable(table, false);
                    
                }
                // 将 ExcelPackage 内容写入内存流
                MemoryStream memoryStream = new MemoryStream();
                package.SaveAs(memoryStream);
                memoryStream.Position = 0; // 重置流位置，以便读取
                return memoryStream;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"写入 Excel 模板时发生错误: {ex.Message}");
                throw;
            }
        }
    }
}