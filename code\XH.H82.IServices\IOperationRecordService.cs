﻿using H.Utility;
using XH.H82.Models;
using XH.H82.Models.Dtos;
using XH.H82.Models.Dtos.Tim;
using XH.H82.Models.Entities;
using XH.H82.Models.Entities.Transaction;

namespace XH.H82.IServices
{
    public interface IOperationRecordService
    {
        List<WorkSequentialDto> GetWorkSequentialList(string equipmentId, string year);
        List<EMS_MAINTAIN_INFO> GetMaintainList(string equipmentId);
        EMS_MAINTAIN_INFO SaveMaintainInfo(EMS_MAINTAIN_INFO record);
        ResultDto DeleteMaintainInfo(string maintainId, string userName);
        List<EMS_REPAIR_INFO> GetRepairList(string equipmentId);
        EMS_REPAIR_INFO SaveRepairInfo(EMS_REPAIR_INFO record);
        ResultDto DeleteRepairInfo(string repairId, string userName);
        public List<EMS_CORRECT_INFO> GetCorrectList(string equipmentId);
        EMS_CORRECT_INFO SaveCorrectInfo(EMS_CORRECT_INFO record);
        ResultDto DeleteCorrectInfo(string correctId, string userName);
        List<EMS_COMPARISON_INFO> GetComparisonList(string equipmentId);
        EMS_COMPARISON_INFO SaveComparisonInfo(EMS_COMPARISON_INFO record);
        ResultDto DeleteComparisonInfo(string comparisonId, string userName);
        List<EMS_VERIFICATION_INFO> GetVerificationList(string equipmentId);
        EMS_VERIFICATION_INFO SaveVerificationInfo(EMS_VERIFICATION_INFO record);
        ResultDto DeleteVerificationInfo(string verificationInfoId, string userName);
        List<EMS_CHANGE_INFO> GetChangeList(string equipmentId);
        EMS_CHANGE_INFO SaveChangeInfo(EMS_CHANGE_INFO record);
        RelationEventDto GetRelationEventInfo(string relationNo);
        ResultDto DeleteChangeInfo(string changeId, string userName);
        List<EMS_DOC_INFO> GetOperFiles(string module, string operId);
        IssueFileDto GetUsingFile(string equipment, int year, int? month);
        List<MaintainFormDto> GetMaintainFile(string equipmentId, int year, int month);
        List<RepairArchivingDto> GetRepairFile(string equipmentId, int year);
        List<CorrectArchivingDto> GetCorrectFile(string equipmentId, int year);
        void EditImplement(EMS_IMPLEMENT_INFO implement);
        void DeleteImplement(string id);
        List<EMS_IMPLEMENT_INFO> GetImplements(string equipmentId, DateTime? startTime, DateTime? endTime, string context);
        EMS_IMPLEMENT_INFO CreateImplement(EMS_IMPLEMENT_INFO implement);
        List<EMS_DOC_INFO> GetImplementDocs(string implementId);
        List<EMS_DOC_INFO> GetMaintainDocs(string maintainId);
        List<EMS_DOC_INFO> GetRepairDocs(string repairId);
        List<EMS_DOC_INFO> GetCorrectDocs(string correctId);
        List<EMS_DOC_INFO> GetComparisonDocs(string comparisonId);
        List<EMS_DOC_INFO> GetVerificationDocs(string verificationId);
        List<EMS_DOC_INFO> GetChangeDocs(string changeId);
        List<ExecRecord> GetTransactionFillingRecords(string equipmentId, string transactionClass);

        List<TransactionForm> GetTransactions(string equipmentId);

        /// <summary>
        /// 查询设备保养记录
        /// </summary>
        /// <param name="equipmentId">设备id</param>
        /// <param name="maintainCycle">保养类型</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="content">保养内容</param>
        /// <returns></returns>
        List<EMS_MAINTAIN_INFO> GetMaintains(string equipmentId, string maintainCycle, DateTime? startTime,
            DateTime? endTime,
            string content);
        
        /// <summary>
        ///  查询设备维修记录
        /// </summary>
        /// <param name="equipmentId">设备id </param>
        /// <param name="content">维修内容 </param>
        /// <param name="startTime">维修时间开始</param>
        /// <param name="endTime">维修时间结束</param>
        /// <returns></returns>
        List<EMS_REPAIR_INFO> GetRepairs(string equipmentId , string content , DateTime? startTime, DateTime? endTime);

        List<EMS_CORRECT_INFO> GetCorrects(string equipmentId ,DateTime? startTime, DateTime? endTime);


        /// <summary>
        /// 查询设备比对记录
        /// </summary>
        /// <param name="equipmentId">设备id</param>
        /// <returns></returns>
        List<EMS_COMPARISON_INFO> GetComparisons(string equipmentId);
        
        /// <summary>
        /// 获取  IRecord的记录信息
        /// </summary>
        /// <param name="id">设备XX记录id</param>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        T GetRecordById<T>(string id) where T :  class,IRecord,new();

        /// <summary>
        /// 获取设备校准记录有效期推断
        /// </summary>
        /// <param name="equipmentId"></param>
        /// <param name="records"></param>
        void GetCorrectRecordValidityPeriod(string equipmentId, List<EMS_CORRECT_INFO> records);

        List<DecontaminationFileDto> GetDecontaminationFile(string equipmentId, int year);
    }
}
