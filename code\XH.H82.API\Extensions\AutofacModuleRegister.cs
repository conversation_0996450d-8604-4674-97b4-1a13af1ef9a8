﻿using Autofac;
using Autofac.Extras.DynamicProxy;
using H.Utility.AOP;
using H.Utility.CommonService;
using XH.H82.IServices;
using XH.H82.IServices._demo;
using XH.H82.IServices.AuthorizationRecord;
using XH.H82.IServices.Certificate;
using XH.H82.IServices.DeviceDataRefresh;
using XH.H82.IServices.EquipmentClassNew;
using XH.H82.IServices.EquipmentCodeCustom;
using XH.H82.IServices.EquipmentOnlyOffice;
using XH.H82.IServices.IoTDevice;
using XH.H82.IServices.Sbml;
using XH.H82.IServices.TemplateDesign;
using XH.H82.IServices.Transaction;
using XH.H82.Services;
using XH.H82.Services._demo;
using XH.H82.Services.AuthorizationRecord;
using XH.H82.Services.Certificate;
using XH.H82.Services.DeviceDataRefresh;
using XH.H82.Services.EquipmentClassNew;
using XH.H82.Services.EquipmentCodeCustom;
using XH.H82.Services.EquipmentOnlyOffice;
using XH.H82.Services.InkScreen;
using XH.H82.Services.IoTDevice;
using XH.H82.Services.OtherSystem;
using XH.H82.Services.Smbl;
using XH.H82.Services.TemplateDesign;
using XH.H82.Services.Transaction;
using XH.H82.Services.EquipmentAdditionalRecord;
using XH.LAB.UTILS.Implement;
using XH.LAB.UTILS.Interface;

namespace XH.H82.API.Extensions
{
    public class AutofacModuleRegister : Module
    {
        protected override void Load(ContainerBuilder builder)
        {
            //缓存AOP
            builder.RegisterType<CacheAOP>();
            builder.RegisterType<UseCacheWhenFailedAOP>();
            //拦截器列表
            var interceptType = new List<Type>();
            //缓存拦截器
            interceptType.Add(typeof(CacheAOP));
            //访问失败返回缓存数据拦截器
            interceptType.Add(typeof(UseCacheWhenFailedAOP));
            /////////////////
            //系统服务
            builder.RegisterType<SystemService>().As<ISystemService>().InstancePerLifetimeScope()
                .EnableInterfaceInterceptors()//启用拦截器
                .InterceptedBy(interceptType.ToArray());
            //基础数据服务
            builder.RegisterType<BaseDataServices>().As<IBaseDataServices>().InstancePerLifetimeScope()
                .EnableInterfaceInterceptors()//启用拦截器
                .InterceptedBy(interceptType.ToArray());
            builder.RegisterType<HttpContextAccessor>().As<IHttpContextAccessor>().SingleInstance();
            /////////////////


            //业务服义注册 IDemoServices 可能有多个实现,根据实际需要注册指定的实现类
            builder.RegisterType<DemoServices>().As<IDemoServices>().InstancePerLifetimeScope()
                .EnableInterfaceInterceptors()//启用拦截器
                .InterceptedBy(interceptType.ToArray());
            builder.RegisterType<ModuleLabGroupService>().As<IModuleLabGroupService>().InstancePerLifetimeScope()
                .EnableInterfaceInterceptors()//启用拦截器
                .InterceptedBy(interceptType.ToArray());
            builder.RegisterType<EquipmentDocService>().As<IEquipmentDocService>().InstancePerLifetimeScope()
                .EnableInterfaceInterceptors()//启用拦截器
                .InterceptedBy(interceptType.ToArray());
            builder.RegisterType<XhEquipmentDocService>().As<IXhEquipmentDocService>().InstancePerLifetimeScope()
                .EnableInterfaceInterceptors()//启用拦截器
                .InterceptedBy(interceptType.ToArray());
            builder.RegisterType<BaseService>().As<IBaseService>().InstancePerLifetimeScope()
                .EnableInterfaceInterceptors()//启用拦截器
                .InterceptedBy(interceptType.ToArray());
            builder.RegisterType<CurrencyService>().As<ICurrencyService>().InstancePerLifetimeScope()
                .EnableInterfaceInterceptors()//启用拦截器
                .InterceptedBy(interceptType.ToArray());
            builder.RegisterType<FileManageService>().As<IFileManageService>().InstancePerLifetimeScope()
                .EnableInterfaceInterceptors()//启用拦截器
                .InterceptedBy(interceptType.ToArray());

            builder.RegisterType<OperationRecordService>().As<IOperationRecordService>().InstancePerLifetimeScope()
              .EnableInterfaceInterceptors()//启用拦截器
              .InterceptedBy(interceptType.ToArray());
            builder.RegisterType<ReagentInfoService>().As<IReagentInfoService>().InstancePerLifetimeScope()
            .EnableInterfaceInterceptors()//启用拦截器
            .InterceptedBy(interceptType.ToArray());

            builder.RegisterType<OnlyOfficeService>().As<IOnlyOfficeService>().InstancePerLifetimeScope()
            .EnableInterfaceInterceptors()//启用拦截器
            .InterceptedBy(interceptType.ToArray());

            builder.RegisterType<ScrapStopService>().As<IScrapStopService>().InstancePerLifetimeScope()
            .EnableInterfaceInterceptors()//启用拦截器
            .InterceptedBy(interceptType.ToArray());

            builder.RegisterType<SetUpService>().As<ISetUpService>().InstancePerLifetimeScope()
           .EnableInterfaceInterceptors()//启用拦截器
           .InterceptedBy(interceptType.ToArray());
            builder.RegisterType<SubscribeService>().As<ISubscribeService>().InstancePerLifetimeScope()
           .EnableInterfaceInterceptors()//启用拦截器
           .InterceptedBy(interceptType.ToArray());
            builder.RegisterType<WorkPlanService>().As<IWorkPlanService>().InstancePerLifetimeScope()
           .EnableInterfaceInterceptors()//启用拦截器
           .InterceptedBy(interceptType.ToArray());
            builder.RegisterType<XHLISCommonService>().As<IXHLISCommonService>().InstancePerLifetimeScope()
           .EnableInterfaceInterceptors()//启用拦截器
           .InterceptedBy(interceptType.ToArray());
            builder.RegisterType<XHLISPrintService>().As<IXHLISPrintService>().InstancePerLifetimeScope()
           .EnableInterfaceInterceptors()//启用拦截器
           .InterceptedBy(interceptType.ToArray());
            builder.RegisterType<XhCurrencyService>().As<IXhCurrencyService>().InstancePerLifetimeScope()
           .EnableInterfaceInterceptors()//启用拦截器
           .InterceptedBy(interceptType.ToArray());
            builder.RegisterType<XhScrapStopService>().As<IXhScrapStopService>().InstancePerLifetimeScope()
           .EnableInterfaceInterceptors()//启用拦截器
           .InterceptedBy(interceptType.ToArray());
            builder.RegisterType<EquipmentOnlyOfficeService>().As<IEquipmentOnlyOfficeService>().InstancePerLifetimeScope()
            .EnableInterfaceInterceptors()//启用拦截器
            .InterceptedBy(interceptType.ToArray());

            builder.RegisterType<H115OnlyOfficeService>().As<IH115OnlyOfficeService>().InstancePerLifetimeScope()
            .EnableInterfaceInterceptors()//启用拦截器
            .InterceptedBy(interceptType.ToArray());

            builder.RegisterType<LearningRecordService>().As<ILearningRecordService>().InstancePerLifetimeScope()
            .EnableInterfaceInterceptors()//启用拦截器
            .InterceptedBy(interceptType.ToArray());

            builder.RegisterType<EquipmentTransactionInformationService>().As<IDeviceRefreshService>().InstancePerLifetimeScope()
            .EnableInterfaceInterceptors()//启用拦截器
            .InterceptedBy(interceptType.ToArray());


            builder.RegisterType<InkScreenTemplateService>().As<IInkScreenTemplateService>().InstancePerLifetimeScope()
            .InterceptedBy(interceptType.ToArray());

            //墨水屏设计上下文
            builder.RegisterType<InkScreenTemplateContext>();

            // builder.RegisterType<SqlSugarUow<SugarDbContext_Master>>().As<ISqlSugarUow<SugarDbContext_Master>>().InstancePerLifetimeScope()
            //.EnableInterfaceInterceptors()//启用拦截器
            //.InterceptedBy(interceptType.ToArray());

            //以下为新创建的服务
            //文件上传
            builder.RegisterType<UploadFileService>().As<IUploadFileService>().InstancePerLifetimeScope()
            .EnableInterfaceInterceptors()//启用拦截器
            .InterceptedBy(interceptType.ToArray());
            //权限服务
            builder.RegisterType<AuthorityService>().As<IAuthorityService>().InstancePerLifetimeScope()
                .EnableInterfaceInterceptors()//启用拦截器
                .InterceptedBy(interceptType.ToArray());
            
            //权限服务
            builder.RegisterType<AuthorityService2>().As<IAuthorityService2>().InstancePerLifetimeScope()
                .EnableInterfaceInterceptors()//启用拦截器
                .InterceptedBy(interceptType.ToArray());

            builder.RegisterType<XhWorkPlanService>().As<IXhWorkPlanService>().InstancePerLifetimeScope()
           .EnableInterfaceInterceptors()//启用拦截器
           .InterceptedBy(interceptType.ToArray());


            builder.RegisterType<InkScreenService>().As<IInkScreenService>().InstancePerLifetimeScope()
            .EnableInterfaceInterceptors()//启用拦截器
            .InterceptedBy(interceptType.ToArray());

            builder.RegisterType<CertificateService>().As<ICertificateService>().InstancePerLifetimeScope()
                .EnableInterfaceInterceptors()//启用拦截器
                .InterceptedBy(interceptType.ToArray());
            
            builder.RegisterType<InfraServices>().InstancePerLifetimeScope();
            builder.RegisterType<EquipmentTreeContext>().InstancePerLifetimeScope();
            builder.RegisterType<SmblServicve>().As<ISmblServicve>().InstancePerLifetimeScope()
                .EnableInterfaceInterceptors()//启用拦截器
                .InterceptedBy(interceptType.ToArray());

            builder.RegisterType<ContentDictService>().As<IContentDictService>().InstancePerLifetimeScope()
            .EnableInterfaceInterceptors()//启用拦截器
            .InterceptedBy(interceptType.ToArray());
            builder.RegisterType<AuthorizationRecordService>().As<IAuthorizationRecordService>().InstancePerLifetimeScope()
                .EnableInterfaceInterceptors()//启用拦截器
                .InterceptedBy(interceptType.ToArray());
            
            builder.RegisterType<IoTDeviceService>().As<IIoTDeviceService>().InstancePerLifetimeScope()
                .EnableInterfaceInterceptors()//启用拦截器
                .InterceptedBy(interceptType.ToArray());
            
            builder.RegisterType<TemplateDesignService>().As<ITemplateDesignService>().InstancePerLifetimeScope()
                .EnableInterfaceInterceptors()//启用拦截器
                .InterceptedBy(interceptType.ToArray());
            
            builder.RegisterType<MessageService>().As<IMessageService>().InstancePerLifetimeScope()
                .EnableInterfaceInterceptors()//启用拦截器
                .InterceptedBy(interceptType.ToArray());
            
            builder.RegisterType<CustomCodeService>().As<ICustomCodeService>().InstancePerLifetimeScope()
                .EnableInterfaceInterceptors()//启用拦截器
                .InterceptedBy(interceptType.ToArray());
            
            builder.RegisterType<EquipmentClassService>().As<IEquipmentClassService>().InstancePerLifetimeScope()
                .EnableInterfaceInterceptors()//启用拦截器
                .InterceptedBy(interceptType.ToArray());
            builder.RegisterType<EquipmentClassService>().As<IEquipomentArchivesService>().InstancePerLifetimeScope()
                .EnableInterfaceInterceptors()//启用拦截器
                .InterceptedBy(interceptType.ToArray());

            builder.RegisterType<EquipmentAdditionalRecordService>().As<IEquipmentAdditionalRecordService>().InstancePerLifetimeScope()
                .EnableInterfaceInterceptors()//启用拦截器
                .InterceptedBy(interceptType.ToArray());
        }
    }
}
