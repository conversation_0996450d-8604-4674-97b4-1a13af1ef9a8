﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XH.H82.Models.Entities;

namespace XH.H82.Models.Dtos
{
    public class WorkSequentialDto
    {
        public DateTime WORK_DATE { get; set; }
        public List<SECOND_SEQUENTIAL_NODE> SECOND_SEQUENTIAL_NODE { get; set; }
    }
    public class SECOND_SEQUENTIAL_NODE
    {
        public string ID { get; set; }
        public string TYPE { get; set; }
        public string OPER_PERSON { get; set; }
        public string WORK_NO { get; set; }
        public string WORK_MAIN_CONTENT { get; set; }
        public string WORK_LESS_CONTENT { get; set; }
        public string RELATED_EVENTS { get; set; }
        public List<EMS_DOC_INFO> EMS_DOC_INFO { get; set; }
    }
}
