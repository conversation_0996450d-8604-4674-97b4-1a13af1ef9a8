namespace XH.H82.API.Controllers.IoTDevice.Dtos;

public class OverviewMaintainedDto
{
    /// <summary>
    /// 生安设备类型id
    /// </summary>
    public string SmblClass  { get; set; }
    /// <summary>
    /// 生安设备类型名称
    /// </summary>
    public string SmblClassName { get; set; }
    /// <summary>
    /// 带维护数量
    /// </summary>
    public int PreMaintained { get; set; }
    /// <summary>
    /// 设备数量总数
    /// </summary>
    public int TotalMaintained { get; set; }
}