2025-07-04 11:03:08.376 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-04 11:05:53.087 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-04 11:05:53.219 +08:00 [ERR] 调用H07检测版本依赖时发生错误:Index and length must refer to a location within the string. (Parameter 'length')
System.ArgumentOutOfRangeException: Index and length must refer to a location within the string. (Parameter 'length')
   at System.String.Substring(Int32 startIndex, Int32 length)
   at Spectre.Console.Rendering.Segment.SplitOverflow(Segment segment, Nullable`1 overflow, Int32 maxWidth) in /_/src/Spectre.Console/Rendering/Segment.cs:line 351
   at Spectre.Console.Paragraph.SplitLines(Int32 maxWidth) in /_/src/Spectre.Console/Widgets/Paragraph.cs:line 242
   at Spectre.Console.Paragraph.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Widgets/Paragraph.cs:line 144
   at Spectre.Console.Rendering.Renderable.Spectre.Console.Rendering.IRenderable.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Rendering/Renderable.cs:line 19
   at Spectre.Console.Markup.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Widgets/Markup.cs:line 54
   at Spectre.Console.Rendering.Renderable.Spectre.Console.Rendering.IRenderable.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Rendering/Renderable.cs:line 19
   at Spectre.Console.TableRenderer.Render(TableRendererContext context, List`1 columnWidths) in /_/src/Spectre.Console/Widgets/Table/TableRenderer.cs:line 31
   at Spectre.Console.Table.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Widgets/Table/Table.cs:line 141
   at Spectre.Console.Rendering.Renderable.Spectre.Console.Rendering.IRenderable.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Rendering/Renderable.cs:line 19
   at Spectre.Console.RenderableExtensions.GetSegments(IAnsiConsole console, RenderContext options, IEnumerable`1 renderables) in /_/src/Spectre.Console/Extensions/RenderableExtensions.cs:line 37
   at Spectre.Console.RenderableExtensions.GetSegments(IRenderable renderable, IAnsiConsole console) in /_/src/Spectre.Console/Extensions/RenderableExtensions.cs:line 29
   at Spectre.Console.AnsiBuilder.Build(IAnsiConsole console, IRenderable renderable) in /_/src/Spectre.Console/Internal/Backends/Ansi/AnsiBuilder.cs:line 17
   at Spectre.Console.AnsiConsoleBackend.Write(IRenderable renderable) in /_/src/Spectre.Console/Internal/Backends/Ansi/AnsiConsoleBackend.cs:line 30
   at Spectre.Console.AnsiConsoleFacade.Write(IRenderable renderable) in /_/src/Spectre.Console/Internal/Backends/AnsiConsoleFacade.cs:line 40
   at Spectre.Console.AnsiConsole.Write(IRenderable renderable) in /_/src/Spectre.Console/AnsiConsole.Rendering.cs:line 29
   at H.BASE.AppInit.<RegisterInfraServer>b__22_0()
2025-07-04 11:09:24.556 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-04 11:09:24.605 +08:00 [ERR] 调用H07检测版本依赖时发生错误:Index and length must refer to a location within the string. (Parameter 'length')
System.ArgumentOutOfRangeException: Index and length must refer to a location within the string. (Parameter 'length')
   at System.String.Substring(Int32 startIndex, Int32 length)
   at Spectre.Console.Rendering.Segment.SplitOverflow(Segment segment, Nullable`1 overflow, Int32 maxWidth) in /_/src/Spectre.Console/Rendering/Segment.cs:line 351
   at Spectre.Console.Paragraph.SplitLines(Int32 maxWidth) in /_/src/Spectre.Console/Widgets/Paragraph.cs:line 242
   at Spectre.Console.Paragraph.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Widgets/Paragraph.cs:line 144
   at Spectre.Console.Rendering.Renderable.Spectre.Console.Rendering.IRenderable.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Rendering/Renderable.cs:line 19
   at Spectre.Console.Markup.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Widgets/Markup.cs:line 54
   at Spectre.Console.Rendering.Renderable.Spectre.Console.Rendering.IRenderable.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Rendering/Renderable.cs:line 19
   at Spectre.Console.TableRenderer.Render(TableRendererContext context, List`1 columnWidths) in /_/src/Spectre.Console/Widgets/Table/TableRenderer.cs:line 31
   at Spectre.Console.Table.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Widgets/Table/Table.cs:line 141
   at Spectre.Console.Rendering.Renderable.Spectre.Console.Rendering.IRenderable.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Rendering/Renderable.cs:line 19
   at Spectre.Console.RenderableExtensions.GetSegments(IAnsiConsole console, RenderContext options, IEnumerable`1 renderables) in /_/src/Spectre.Console/Extensions/RenderableExtensions.cs:line 37
   at Spectre.Console.RenderableExtensions.GetSegments(IRenderable renderable, IAnsiConsole console) in /_/src/Spectre.Console/Extensions/RenderableExtensions.cs:line 29
   at Spectre.Console.AnsiBuilder.Build(IAnsiConsole console, IRenderable renderable) in /_/src/Spectre.Console/Internal/Backends/Ansi/AnsiBuilder.cs:line 17
   at Spectre.Console.AnsiConsoleBackend.Write(IRenderable renderable) in /_/src/Spectre.Console/Internal/Backends/Ansi/AnsiConsoleBackend.cs:line 30
   at Spectre.Console.AnsiConsoleFacade.Write(IRenderable renderable) in /_/src/Spectre.Console/Internal/Backends/AnsiConsoleFacade.cs:line 40
   at Spectre.Console.AnsiConsole.Write(IRenderable renderable) in /_/src/Spectre.Console/AnsiConsole.Rendering.cs:line 29
   at H.BASE.AppInit.<RegisterInfraServer>b__22_0()
2025-07-04 11:12:18.380 +08:00 [ERR] 调用H07检测版本依赖时发生错误:Index and length must refer to a location within the string. (Parameter 'length')
System.ArgumentOutOfRangeException: Index and length must refer to a location within the string. (Parameter 'length')
   at System.String.Substring(Int32 startIndex, Int32 length)
   at Spectre.Console.Rendering.Segment.SplitOverflow(Segment segment, Nullable`1 overflow, Int32 maxWidth) in /_/src/Spectre.Console/Rendering/Segment.cs:line 351
   at Spectre.Console.Paragraph.SplitLines(Int32 maxWidth) in /_/src/Spectre.Console/Widgets/Paragraph.cs:line 242
   at Spectre.Console.Paragraph.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Widgets/Paragraph.cs:line 144
   at Spectre.Console.Rendering.Renderable.Spectre.Console.Rendering.IRenderable.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Rendering/Renderable.cs:line 19
   at Spectre.Console.Markup.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Widgets/Markup.cs:line 54
   at Spectre.Console.Rendering.Renderable.Spectre.Console.Rendering.IRenderable.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Rendering/Renderable.cs:line 19
   at Spectre.Console.TableRenderer.Render(TableRendererContext context, List`1 columnWidths) in /_/src/Spectre.Console/Widgets/Table/TableRenderer.cs:line 31
   at Spectre.Console.Table.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Widgets/Table/Table.cs:line 141
   at Spectre.Console.Rendering.Renderable.Spectre.Console.Rendering.IRenderable.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Rendering/Renderable.cs:line 19
   at Spectre.Console.RenderableExtensions.GetSegments(IAnsiConsole console, RenderContext options, IEnumerable`1 renderables) in /_/src/Spectre.Console/Extensions/RenderableExtensions.cs:line 37
   at Spectre.Console.RenderableExtensions.GetSegments(IRenderable renderable, IAnsiConsole console) in /_/src/Spectre.Console/Extensions/RenderableExtensions.cs:line 29
   at Spectre.Console.AnsiBuilder.Build(IAnsiConsole console, IRenderable renderable) in /_/src/Spectre.Console/Internal/Backends/Ansi/AnsiBuilder.cs:line 17
   at Spectre.Console.AnsiConsoleBackend.Write(IRenderable renderable) in /_/src/Spectre.Console/Internal/Backends/Ansi/AnsiConsoleBackend.cs:line 30
   at Spectre.Console.AnsiConsoleFacade.Write(IRenderable renderable) in /_/src/Spectre.Console/Internal/Backends/AnsiConsoleFacade.cs:line 40
   at Spectre.Console.AnsiConsole.Write(IRenderable renderable) in /_/src/Spectre.Console/AnsiConsole.Rendering.cs:line 29
   at H.BASE.AppInit.<RegisterInfraServer>b__22_0()
2025-07-04 11:12:18.381 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-04 11:18:53.747 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-04 11:18:53.804 +08:00 [ERR] 调用H07检测版本依赖时发生错误:Index and length must refer to a location within the string. (Parameter 'length')
System.ArgumentOutOfRangeException: Index and length must refer to a location within the string. (Parameter 'length')
   at System.String.Substring(Int32 startIndex, Int32 length)
   at Spectre.Console.Rendering.Segment.SplitOverflow(Segment segment, Nullable`1 overflow, Int32 maxWidth) in /_/src/Spectre.Console/Rendering/Segment.cs:line 351
   at Spectre.Console.Paragraph.SplitLines(Int32 maxWidth) in /_/src/Spectre.Console/Widgets/Paragraph.cs:line 242
   at Spectre.Console.Paragraph.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Widgets/Paragraph.cs:line 144
   at Spectre.Console.Rendering.Renderable.Spectre.Console.Rendering.IRenderable.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Rendering/Renderable.cs:line 19
   at Spectre.Console.Markup.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Widgets/Markup.cs:line 54
   at Spectre.Console.Rendering.Renderable.Spectre.Console.Rendering.IRenderable.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Rendering/Renderable.cs:line 19
   at Spectre.Console.TableRenderer.Render(TableRendererContext context, List`1 columnWidths) in /_/src/Spectre.Console/Widgets/Table/TableRenderer.cs:line 31
   at Spectre.Console.Table.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Widgets/Table/Table.cs:line 141
   at Spectre.Console.Rendering.Renderable.Spectre.Console.Rendering.IRenderable.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Rendering/Renderable.cs:line 19
   at Spectre.Console.RenderableExtensions.GetSegments(IAnsiConsole console, RenderContext options, IEnumerable`1 renderables) in /_/src/Spectre.Console/Extensions/RenderableExtensions.cs:line 37
   at Spectre.Console.RenderableExtensions.GetSegments(IRenderable renderable, IAnsiConsole console) in /_/src/Spectre.Console/Extensions/RenderableExtensions.cs:line 29
   at Spectre.Console.AnsiBuilder.Build(IAnsiConsole console, IRenderable renderable) in /_/src/Spectre.Console/Internal/Backends/Ansi/AnsiBuilder.cs:line 17
   at Spectre.Console.AnsiConsoleBackend.Write(IRenderable renderable) in /_/src/Spectre.Console/Internal/Backends/Ansi/AnsiConsoleBackend.cs:line 30
   at Spectre.Console.AnsiConsoleFacade.Write(IRenderable renderable) in /_/src/Spectre.Console/Internal/Backends/AnsiConsoleFacade.cs:line 40
   at Spectre.Console.AnsiConsole.Write(IRenderable renderable) in /_/src/Spectre.Console/AnsiConsole.Rendering.cs:line 29
   at H.BASE.AppInit.<RegisterInfraServer>b__22_0()
2025-07-04 14:09:01.939 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-04 14:44:22.766 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-04 14:45:08.026 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-04 14:50:19.610 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-04 15:14:07.295 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-04 15:55:22.986 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
