﻿using H.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XH.H82.Models.Dtos;
using XH.H82.Models.Entities;

namespace XH.H82.IServices
{
    public interface ISubscribeService
    {
        List<EMS_SUBSCRIBE_INFO> GetSubscribeInfoList(DateTime startTime,DateTime endTime, string userNo, string hospitalId, string mgroupId,string state,string search, string labId, string pgroupId,string areaId);
        EMS_SUBSCRIBE_INFO SaveSubscribeInfo(EMS_SUBSCRIBE_INFO record);
        ResultDto DeleteSubscribeInfo(string subscribeId, string userName);
        SubscribeProcessDto GetSubscribeProcess(string subscribeId);
    }
}
