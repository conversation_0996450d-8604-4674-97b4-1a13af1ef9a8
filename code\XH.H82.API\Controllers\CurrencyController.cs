﻿using EasyCaching.Core;
using H.Utility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.Extensions.Caching.Memory;
using XH.H82.Base.Setup;
using XH.H82.IServices;

namespace XH.H82.API.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize]
    public class CurrencyController : ControllerBase
    {
        private readonly ICurrencyService _currencyService;
        private readonly IXhCurrencyService _xhCurrencyService;
        private readonly IModuleLabGroupService _IModuleLabGroupService;
        private readonly IConfiguration _configuration;
        private readonly IMemoryCache _easyCacheMemory;
        private string currentLabKey = "XH:LIS:H82:CURRENTLAB:UserSelectedLab:";
        private readonly string RedisModule;
        public CurrencyController(ICurrencyService currencyService, IXhCurrencyService xhCurrencyService, IConfiguration configuration, IModuleLabGroupService iModuleLabGroupService, IMemoryCache easyCahceFactory)
        {
            _currencyService = currencyService;
            _xhCurrencyService = xhCurrencyService;
            _IModuleLabGroupService = iModuleLabGroupService;
            _configuration = configuration;
            RedisModule = _configuration["RedisModule"];
            _easyCacheMemory = easyCahceFactory;
        }

        /// <summary>
        ///   获取人事列表
        /// </summary>
        /// <param name="areaId">院区ID</param>
        /// <param name="mgroupId">专业组ID</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetPersonList([BindRequired] string areaId, string mgroupId)
        {
            var claims = User.ToClaimsDto();
            var clientMac = GetLocalMac.GetMac();
            currentLabKey = currentLabKey + claims.USER_NO + "." + clientMac;
            var labId = _easyCacheMemory.Get<string>(currentLabKey);
            var res = _currencyService.GetPersonList(mgroupId, claims.USER_NO, claims.HOSPITAL_ID, labId, areaId);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   获取人事列表
        /// </summary>
        /// <param name="areaId">院区ID</param>
        /// <param name="mgroupId">专业组ID</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetScrapStopPersonList([BindRequired] string areaId)
        {
            var claims = User.ToClaimsDto();
            var clientMac = GetLocalMac.GetMac();
            currentLabKey = currentLabKey + claims.USER_NO + "." + clientMac;
            var labId = _easyCacheMemory.Get<string>(currentLabKey);
            var res = _xhCurrencyService.GetScrapStopPersonList(areaId, claims.HOSPITAL_ID);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   获取科室列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetLabList([BindRequired] string areaId)
        {
            var claims = User.ToClaimsDto();
            var res = _currencyService.GetLabList(claims.HOSPITAL_ID, areaId);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   获取人员类型列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetPersonTypeList()
        {
            var claims = User.ToClaimsDto();
            var res = _currencyService.GetPersonTypeList();
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   获取人员职务列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetPostList()
        {
            var claims = User.ToClaimsDto();
            var res = _currencyService.GetPostList();
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   获取专业分类列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetProfessionalClass()
        {
            var res = _currencyService.GetProfessionalClass();
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   添加职务岗位信息
        /// </summary>
        /// <param name="input">输入</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult AddPostInfo([BindRequired] string input)
        {
            var claims = User.ToClaimsDto();
            var clientMac = GetLocalMac.GetMac();
            currentLabKey = currentLabKey + claims.USER_NO + "." + clientMac;
            var labId = _easyCacheMemory.Get<string>(currentLabKey);
            var userName = claims.HIS_NAME;
            var res = _currencyService.AddPostInfo(input, labId, claims.HOSPITAL_ID, userName);
            return Ok(res);
        }

        /// <summary>
        ///   删除职务岗位信息
        /// </summary>
        /// <param name="BasciId">ID</param>
        /// <returns></returns>
        [HttpDelete]
        public IActionResult DeletePostInfo(string BasciId)
        {
            var res = _currencyService.DeletePostInfo(BasciId);
            return Ok(res);
        }

        /// <summary>
        ///   添加人员类型信息
        /// </summary>
        /// <param name="input">输入</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult AddPersonTypeInfo([BindRequired] string input)
        {
            var claims = User.ToClaimsDto();
            var clientMac = GetLocalMac.GetMac();
            currentLabKey = currentLabKey + claims.USER_NO + "." + clientMac;
            var labId = _easyCacheMemory.Get<string>(currentLabKey);
            var userName = claims.HIS_NAME;
            var res = _currencyService.AddPersonTypeInfo(input, labId, claims.HOSPITAL_ID, userName);
            return Ok(res);
        }

        /// <summary>
        ///   删除人员类型信息
        /// </summary>
        /// <param name="BasciId">ID</param>
        /// <returns></returns>
        [HttpDelete]
        public IActionResult DeletePersonTypeInfo(string BasciId)
        {
            var res = _currencyService.DeletePersonTypeInfo(BasciId);
            return Ok(res);
        }

        /// <summary>
        /// 获取实验室下拉
        /// </summary>
        /// <param name="areaId"></param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetPgroupPullList(string areaId)
        {
            var claims = User.ToClaimsDto();
            var clientMac = GetLocalMac.GetMac();
            currentLabKey = currentLabKey + claims.USER_NO + "." + clientMac;
            var labId = _easyCacheMemory.Get<string>(currentLabKey);
            var res = _currencyService.GetPgroupPullList(areaId, claims.USER_NO, labId);
            return Ok(res.ToResultDto());
        }
    }
}
