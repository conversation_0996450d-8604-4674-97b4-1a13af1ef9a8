﻿using System.ComponentModel.DataAnnotations;
using H.Utility.SqlSugarInfra;
using SqlSugar;

namespace XH.H82.Models.Entities
{
    [SugarTable("EMS_START_STOP")]
    [DBOwner("XH_OA")]
    public class EMS_START_STOP
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string START_STOP_ID { get; set; }
        public string HOSPITAL_ID { get; set; }
        [Required(ErrorMessage = "设备ID不能为空")]
        public string EQUIPMENT_ID { get; set; }
        public DateTime? START_DATE { get; set; }
        public string START_CAUSE { get; set; }
        public string OPER_PERSON { get; set; }
        public DateTime? OPER_TIME { get; set; }
        public string START_STOP_STATE { get; set; }
        public string START_STOP_TYPE { get; set; }
        public string FIRST_RPERSON { get; set; }
        public DateTime? FIRST_RTIME { get; set; }
        public string LAST_MPERSON { get; set; }
        public DateTime? LAST_MTIME { get; set; }
        public string REMARK { get; set; }

    }
}
