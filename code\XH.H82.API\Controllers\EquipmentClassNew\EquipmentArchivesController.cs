﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using XH.H82.API.Extensions;
using XH.H82.IServices.EquipmentClassNew;
using XH.H82.Models.EquipmengtClassNew;
namespace XH.H82.API.Controllers.EquipmentClassNew;

/// <summary>
/// 档案记录
/// </summary>
public class EquipmentArchivesController : SuperController
{
    
    private readonly IEquipmentClassService _equipmentClassNewService;
    private readonly IEquipomentArchivesService _equipomentArchivesService;
    
    /// <summary>
    /// 构造方法
    /// </summary>
    /// <param name="equipmentClassNewService"></param>
    /// <param name="equipomentArchivesService"></param>
    public EquipmentArchivesController(IEquipmentClassService equipmentClassNewService, IEquipomentArchivesService equipomentArchivesService)
    {
        _equipmentClassNewService = equipmentClassNewService;
        _equipomentArchivesService = equipomentArchivesService;
    }
    
    /// <summary>
    /// 查询档案记录字典
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [CustomResponseType(typeof(List<EqpArchivesDto>))]
    public IActionResult GetEquipmentArchives()
    {
        var result = Array.Empty<EqpArchivesDto>().ToList();
        var archives = _equipmentClassNewService.GetEquipmentArchives();
        var classes = _equipmentClassNewService.GetEquipmentClassDict();
        foreach (var archivesDict in archives)
        {
            var extModule = JsonConvert.DeserializeObject<EqpArchivesExt>(archivesDict.EqpArchivesJson);
            var classIds = archivesDict.ClassArchives.Select(x => x.EqpClassId);
            var classDict = classes.Where(x => classIds.Contains(x.ClassId)).ToList();
            var names = classDict.Select(x => x.ClassName);
            var dto = new EqpArchivesDto()
            {
                EqpArchivesId = archivesDict.EqpArchivesId,                  
                EqpArchivesName = archivesDict.EqpArchivesPid is  "0" ? archivesDict.EqpArchivesPName : archivesDict.EqpArchivesName ,
                EqpArchivesPid = archivesDict.EqpArchivesPid,
                EqpArchivesPidName = archivesDict.EqpArchivesPName,
                EqpArchivesSort = archivesDict.EqpArchivesSort,
                EqpArchivesState = archivesDict.EqpArchivesState,
                EqpArchivesType = archivesDict.EqpArchivesType,
                IsUpload = extModule.IsUpload,
                ApplyEquipmentClassNames = String.Join(",",names),
                FIRST_RPERSON = archivesDict.FIRST_RPERSON,
                FIRST_RTIME = archivesDict.FIRST_RTIME,
                LAST_MPERSON = archivesDict.LAST_MPERSON,
                LAST_MTIME = archivesDict.LAST_MTIME,
                Remark = archivesDict.Remark,
                IsSubdivision = archivesDict.EqpArchivesPid is  not "0"
            };
            result.Add(dto);
        }
        return Ok(result.Success());
    }
    
    
    /// <summary>
    /// 新增设备档案记录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    ///
    [HttpPost]
    [CustomResponseType(typeof(EMS_EQP_ARCHIVES_DICT))]
    public IActionResult AddEquipmentArchives(AddEquipmentArchivesDto input)
    {
        var result =  _equipomentArchivesService.AddEquipmentArchives(input);
        return Ok(result.Success());
    }
    
    /// <summary>
    /// 更新设备档案记录
    /// </summary>
    /// <param name="eqpArchivesId">档案记录id</param>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPut("{eqpArchivesId}")]
    [CustomResponseType(typeof(EMS_EQP_ARCHIVES_DICT))]
    public IActionResult UpdateEquipmentArchives(string eqpArchivesId, AddEquipmentArchivesDto input)
    {
        var result =  _equipomentArchivesService.UpdateEquipmentArchives(eqpArchivesId,input);
        return Ok(result.Success());
    }


    /// <summary>
    /// 删除档案记录
    /// </summary>
    /// <param name="EqpArchivesId">档案记录id</param>
    /// <returns></returns>
    [HttpDelete("{EqpArchivesId}")] 
    [CustomResponseType(typeof(bool))]
    public IActionResult DeleteEquipmentArchives(string EqpArchivesId)
    {
        var result = _equipomentArchivesService.DeleteEquipmentArchives(EqpArchivesId);
        return Ok(result.Success());
    }
    
    
    
    /// <summary>
    /// 停用或启用设备档案记录
    /// </summary>
    /// <param name="EqpArchivesId">档案记录id</param>
    /// <returns></returns>
    [HttpPost("{EqpArchivesId}")] 
    [CustomResponseType(typeof(bool))]
    public IActionResult EnableOrDisableEquipmentArchives(string EqpArchivesId)
    {
        var result = _equipomentArchivesService.EnableOrDisableEquipmentArchives(EqpArchivesId);
        return Ok(result.Success());
    }
}