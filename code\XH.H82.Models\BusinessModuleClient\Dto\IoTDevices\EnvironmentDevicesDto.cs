﻿using Newtonsoft.Json;

namespace XH.H82.Models.BusinessModuleClient.Dto.IoTDevices;

public class EnvironmentDevicesDto
{

        /// <summary>
        /// 电信设备唯一标识
        /// </summary>
        [JsonProperty("id")]
        public string Id { get; set; }
        /// <summary>
        /// 测点名称
        /// </summary>
        [JsonProperty("name")]
        public string Name { get; set; }
        /// <summary>
        /// 设备sn
        /// </summary>
        [JsonProperty("sn")]
        public string Sn { get; set; }
        /// <summary>
        /// 实验室id
        /// </summary>
        [JsonProperty("labId")]
        public long LabId { get; set; }
        /// <summary>
        /// 房间id
        /// </summary>
        [JsonProperty("roomId")]
        public long RoomId { get; set; }
        /// <summary>
        /// 测点id
        /// </summary>
        [JsonProperty("checkpointId")]
        public long CheckpointId { get; set; }
        /// <summary>
        /// 物理机json数据
        /// </summary>
        [JsonProperty("data")]
        public string Data { get; set; }
        /// <summary>
        /// 监测类型
        /// </summary>
        [JsonProperty("bioAlarmType")]
        public BioAlarmTypeEnum BioAlarmType { get; set; }
        /// <summary>
        /// 指标是否正常   0异常  1正常
        /// </summary>
        [JsonProperty("status")]
        public long Status { get; set; }
        /// <summary>
        /// 监测值
        /// </summary>
        [JsonProperty("value")]
        public double Value { get; set; }
        [JsonProperty("createTime")]
        public string? CreateTime { get; set; }
        // [JsonProperty("airPressure")]
        // public double AirPressure { get; set; }
        // [JsonProperty("airPressureAlarmId")]
        // public object AirPressureAlarmId { get; set; }
        // [JsonProperty("temperature")]
        // public double Temperature { get; set; }
        // [JsonProperty("temperatureAlarmId")]
        // public object TemperatureAlarmId { get; set; }
        // [JsonProperty("noiseLevel")]
        // public double NoiseLevel { get; set; }
        // [JsonProperty("noiseLevelAlarmId")]
        // public object NoiseLevelAlarmId { get; set; }
        // [JsonProperty("humidity")]
        // public double Humidity { get; set; }
        // [JsonProperty("humidityAlarmId")]
        // public long HumidityAlarmId { get; set; }
}