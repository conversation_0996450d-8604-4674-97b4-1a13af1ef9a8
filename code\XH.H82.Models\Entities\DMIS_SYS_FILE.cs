﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.Entities
{
    [DBOwner("XH_OA")]
    public class DMIS_SYS_FILE
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string FILE_ID { get;set; }
        public string CLASS_ID { get; set; }
        public string HOSPITAL_ID { get; set; }
        public string DOC_ID { get; set; }
        public string FILE_NUM { get; set; }
        public string FILE_CNAME { get; set; }
        public string FILE_SORT { get; set; }
        public string FILE_NAME { get; set; }
        public string FILE_PATH { get; set; }
        public string FILE_SUFFIX { get; set; }
        public string FILE_TYPE { get; set; }
        public int? REVISION_NUM { get; set; }
        public string REVISION_PERSON { get; set; }
        public DateTime? REVISION_TIME { get; set; }
        public string REVISION_STATE { get; set; }
        public string FILE_STATE { get; set; }
        public string FIRST_RPERSON { get; set; }
        public DateTime? FIRST_RTIME { get; set; }
        public string LAST_MPERSON { get; set; }
        public DateTime? LAST_MTIME { get; set; }
        public string REMARK { get; set; }
        public string FORM_VER_ID { get; set; }
        public string  FILE_ORIGIN { get; set; }
    }
}
