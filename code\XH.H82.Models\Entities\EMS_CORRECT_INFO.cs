﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace XH.H82.Models.Entities
{
    [DBOwner("XH_OA")]
    public class EMS_CORRECT_INFO : IRecordEvent , IBeRecordEvent
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string CORRECT_ID { get; set; }
        public string HOSPITAL_ID { get; set; }
        [Required(ErrorMessage = "设备id不能为空")]
        public string EQUIPMENT_ID { get; set; }
        public string CORRECT_NO { get; set; }
        public DateTime? CORRECT_DATE { get; set; }
        public string CORRECT_DEPT { get; set; }
        public string CORRECT_PERSON { get; set; }
        public string CORRECT_RESULT { get; set; }
        public string CORRECT_STATE { get; set; }
        public string OCCUR_EVENT { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string IF_CORRECT { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string IF_COMPARISON { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string IF_VERIFICATION { get; set; }
        public string GetNo()
        {
            return CORRECT_NO;
        }
        /// <summary>
        /// 校准有效期
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public DateTime? DURATION_CORRECT_DATE { get; set; }
        public string RELATION_EVENT { get; set; }
        public string FIRST_RPERSON { get; set; }
        public DateTime? FIRST_RTIME { get; set; }
        public string LAST_MPERSON { get; set; }
        public DateTime? LAST_MTIME { get; set; }
        public string REMARK { get; set; }
        public string STATE { get; set; }

        public string GetId()
        {
            return CORRECT_ID;
        }

        public (string Type, string TypeName) GetType()
        {
            return ("3", "校准");
        }
        public string GetEquipmentId()
        {
            return EQUIPMENT_ID;
        }
        public DateTime? GetRecordDate()
        {
            return CORRECT_DATE;
        }
    }
}
