2025-07-03 12:47:35.217 +08:00 [INF] ==>App Start..2025-07-03 12:47:35
2025-07-03 12:47:35.408 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-03 12:47:35.412 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-03 12:47:37.743 +08:00 [INF] ==>基础连接请求完成.
2025-07-03 12:47:38.138 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-03 12:47:38.541 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-03 12:47:38.877 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-03 12:47:38.881 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-03 12:47:39.254 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-03 12:47:41.331 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-03 12:47:41.735 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-03 12:47:42.339 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-03 12:47:42.339 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-03 12:47:42.866 +08:00 [ERR] 查询出错:中文提示 :  连接数据库过程中发生错误，检查服务器是否正常连接字符串是否正确，错误信息：ORA-12545: Network Transport: Unable to resolve connect hostnameDbType="Oracle";ConfigId="".
English Message : Connection open error . ORA-12545: Network Transport: Unable to resolve connect hostnameDbType="Oracle";ConfigId="" :
SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON" FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "LAST_MTIME" IS NULL )
2025-07-03 12:47:55.578 +08:00 [INF] ==>App Start..2025-07-03 12:47:55
2025-07-03 12:47:55.745 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-03 12:47:55.749 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-03 12:47:57.151 +08:00 [INF] ==>基础连接请求完成.
2025-07-03 12:47:57.513 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-03 12:47:57.832 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-03 12:47:58.142 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-03 12:47:58.148 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-03 12:47:58.480 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-03 12:47:58.891 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-03 12:47:58.983 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-03 12:47:59.416 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-03 12:47:59.416 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-03 12:48:00.311 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-03 12:48:02.608 +08:00 [INF] ==>初始化完成..
2025-07-03 12:48:02.682 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-03 12:48:02.685 +08:00 [INF] 设备启用任务
2025-07-03 12:48:02.686 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-03 12:48:03.084 +08:00 [INF] 【SQL执行耗时:367.9667ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-03 12:48:03.257 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-03 12:48:03.272 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-03 12:48:03.274 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-03 12:48:03.274 +08:00 [INF] Hosting environment: Development
2025-07-03 12:48:03.275 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-03 12:48:04.502 +08:00 [INF] HTTP GET /favicon.ico responded 404 in 277.0153 ms
2025-07-03 12:48:41.579 +08:00 [INF] HTTP GET /api/CodeCustom/GetCustomDictCode responded 200 in 3146.2509 ms
2025-07-03 12:48:41.582 +08:00 [INF] 【接口超时阀值预警】 [94578089924c3197b116059a2aa8c4bb]接口/api/CodeCustom/GetCustomDictCode,耗时:[3147]毫秒
2025-07-03 12:48:46.430 +08:00 [INF] 【SQL执行耗时:338.8159ms】

[Sql]:SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" <> :EqpNoState0 ) 
[Pars]:
[Name]::EqpNoState0 [Value]:2 [Type]:String    

2025-07-03 12:48:46.930 +08:00 [INF] 【SQL执行耗时:353.7803ms】

[Sql]:SELECT "DATA_ID","HOSPITAL_ID","LAB_ID","CLASS_ID","DATA_SORT","DATA_CNAME","DATA_ENAME","HIS_ID","CUSTOM_CODE","SPELL_CODE","DATA_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","DATA_SNAME","DATA_SOURCE","ONE_CLASS","DATA_UNAME","IF_REPEAT","SYSTEM_ID" FROM "XH_SYS"."SYS6_BASE_DATA"  WHERE ((( "CLASS_ID" = :CLASS_ID0 ) OR ( "CLASS_ID" = :CLASS_ID1 )) AND ( "DATA_STATE" = :DATA_STATE2 )) 
[Pars]:
[Name]::CLASS_ID0 [Value]:设备分类 [Type]:String    
[Name]::CLASS_ID1 [Value]:设备类型 [Type]:String    
[Name]::DATA_STATE2 [Value]:1 [Type]:String    

2025-07-03 12:48:47.333 +08:00 [INF] 【SQL执行耗时:348.7942ms】

[Sql]:SELECT "DATA_ID","HOSPITAL_ID","LAB_ID","CLASS_ID","DATA_SORT","DATA_CNAME","DATA_ENAME","HIS_ID","CUSTOM_CODE","SPELL_CODE","DATA_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","DATA_SNAME","DATA_SOURCE","ONE_CLASS","DATA_UNAME","IF_REPEAT","SYSTEM_ID" FROM "XH_SYS"."SYS6_BASE_DATA"  WHERE (( "CLASS_ID" = :CLASS_ID0 ) AND ( "DATA_STATE" = :DATA_STATE1 )) 
[Pars]:
[Name]::CLASS_ID0 [Value]:专业分类 [Type]:String    
[Name]::DATA_STATE1 [Value]:1 [Type]:String    

2025-07-03 12:48:47.729 +08:00 [INF] 【SQL执行耗时:349.2015ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_CLASS" = :EQUIPMENT_CLASS0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_CLASS0 [Value]:0 [Type]:String    

2025-07-03 12:48:48.150 +08:00 [INF] 【SQL执行耗时:349.8781ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 12:48:48.598 +08:00 [INF] 【SQL执行耗时:372.0342ms】

[Sql]:SELECT "HOSPITAL_ID","HOSPITAL_LOGIN","HOSPITAL_CNAME","HOSPITAL_SNAME","HOSPITAL_TYPE","HOSPITAL_LEVEL","LEVEL1_CODE","LEVEL2_CODE","LEVEL3_CODE","HOSPITAL_BARCODE","HOSPITAL_PROPERTY","HOSPITAL_PERSON","HOSPITAL_ADDRESS","HOSPITAL_TEL","HOSPITAL_STATE","FIRST_PERSON","FIRST_RTIME","LAST_MPRESON","LAST_MTIME","REMARK","HOSPITAL_BCODE","HOSPITAL_RANGE","BARCODE_TYPE","BARCODE_RANGE","CHARGE_PROVIDE_TYPE","DATADB_IP","WECHAT_NAME","LAST_MPERSON","FIRST_RPERSON","UPDATE_FLAG","INSERFACE_SERVICE","SAMELEVEL_HOSPITAL","HOSPITAL_CLASS","HOSPITAL_URL","REGISTER_CODE","PACK_BAR_PREFIX","IF_ITEM_MANAGE","IF_TEMPLATE_HOS","MEDICAL_CONGENER","MEDICAL_CONGENER_ROLE","VEST_IN_DC","INSTANCE_ID","HOSPITAL_ATTRIBUTE","DATASYNC_TYPE","HGROUP_ID","CITEM_MANAGE" FROM "XH_SYS"."SYS6_HOSPITAL_INFO" 

2025-07-03 12:48:49.059 +08:00 [INF] 【SQL执行耗时:375.837ms】

[Sql]:SELECT "LAB_ID","FIRST_RPERSON","LAB_NAME","LAB_ADDRESS","LAB_CODE","LAST_MTIME","LAB_SORT","LAB_RNAME","LAB_CLASS","LAB_PERSON","LINKMAN_TEL","REGISTER_CODE","LAB_LINKMAN","STATE_FLAG","REMARK","LAST_MPERSON","FIRST_RTIME","SPELL_CODE","LAB_TEL","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_LAB" 

2025-07-03 12:48:49.481 +08:00 [INF] 【SQL执行耗时:339.2468ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-03 12:48:49.908 +08:00 [INF] 【SQL执行耗时:345.7206ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP" 

2025-07-03 12:48:50.317 +08:00 [INF] 【SQL执行耗时:332.8944ms】

[Sql]:SELECT "AREA_ID","AREA_CODE","AREA_SORT","AREA_NAME","AREA_CLASS","AREA_PERSON","AREA_ADDRESS","AREA_TEL","SPELL_CODE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","STATE_FLAG","REMARK","HOSPITAL_ID","TAG_SIGN","AREA_ANAME","PACK_BAR_PREFIX" FROM "XH_SYS"."SYS6_INSPECTION_AREA" 

2025-07-03 12:48:50.398 +08:00 [INF] HTTP GET /api/CodeCustom/GetEquipmentCodeCustomDict responded 200 in 4396.6645 ms
2025-07-03 12:48:50.399 +08:00 [INF] 【接口超时阀值预警】 [f8fdc7b4b67e155f9dafe36b007a9acf]接口/api/CodeCustom/GetEquipmentCodeCustomDict,耗时:[4397]毫秒
2025-07-03 12:50:31.235 +08:00 [INF] 【SQL执行耗时:356.6858ms】

[Sql]:SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" <> :EqpNoState0 ) 
[Pars]:
[Name]::EqpNoState0 [Value]:2 [Type]:String    

2025-07-03 12:50:31.718 +08:00 [INF] 【SQL执行耗时:374.1017ms】

[Sql]:SELECT "DATA_ID","HOSPITAL_ID","LAB_ID","CLASS_ID","DATA_SORT","DATA_CNAME","DATA_ENAME","HIS_ID","CUSTOM_CODE","SPELL_CODE","DATA_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","DATA_SNAME","DATA_SOURCE","ONE_CLASS","DATA_UNAME","IF_REPEAT","SYSTEM_ID" FROM "XH_SYS"."SYS6_BASE_DATA"  WHERE (( "CLASS_ID" = :CLASS_ID0 ) AND ( "DATA_STATE" = :DATA_STATE1 )) 
[Pars]:
[Name]::CLASS_ID0 [Value]:专业分类 [Type]:String    
[Name]::DATA_STATE1 [Value]:1 [Type]:String    

2025-07-03 12:50:32.103 +08:00 [INF] 【SQL执行耗时:341.7963ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_CLASS" = :EQUIPMENT_CLASS0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_CLASS0 [Value]:0 [Type]:String    

2025-07-03 12:50:32.527 +08:00 [INF] 【SQL执行耗时:352.3801ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 12:50:32.951 +08:00 [INF] 【SQL执行耗时:350.7045ms】

[Sql]:SELECT "HOSPITAL_ID","HOSPITAL_LOGIN","HOSPITAL_CNAME","HOSPITAL_SNAME","HOSPITAL_TYPE","HOSPITAL_LEVEL","LEVEL1_CODE","LEVEL2_CODE","LEVEL3_CODE","HOSPITAL_BARCODE","HOSPITAL_PROPERTY","HOSPITAL_PERSON","HOSPITAL_ADDRESS","HOSPITAL_TEL","HOSPITAL_STATE","FIRST_PERSON","FIRST_RTIME","LAST_MPRESON","LAST_MTIME","REMARK","HOSPITAL_BCODE","HOSPITAL_RANGE","BARCODE_TYPE","BARCODE_RANGE","CHARGE_PROVIDE_TYPE","DATADB_IP","WECHAT_NAME","LAST_MPERSON","FIRST_RPERSON","UPDATE_FLAG","INSERFACE_SERVICE","SAMELEVEL_HOSPITAL","HOSPITAL_CLASS","HOSPITAL_URL","REGISTER_CODE","PACK_BAR_PREFIX","IF_ITEM_MANAGE","IF_TEMPLATE_HOS","MEDICAL_CONGENER","MEDICAL_CONGENER_ROLE","VEST_IN_DC","INSTANCE_ID","HOSPITAL_ATTRIBUTE","DATASYNC_TYPE","HGROUP_ID","CITEM_MANAGE" FROM "XH_SYS"."SYS6_HOSPITAL_INFO" 

2025-07-03 12:50:33.461 +08:00 [INF] 【SQL执行耗时:436.3215ms】

[Sql]:SELECT "LAB_ID","FIRST_RPERSON","LAB_NAME","LAB_ADDRESS","LAB_CODE","LAST_MTIME","LAB_SORT","LAB_RNAME","LAB_CLASS","LAB_PERSON","LINKMAN_TEL","REGISTER_CODE","LAB_LINKMAN","STATE_FLAG","REMARK","LAST_MPERSON","FIRST_RTIME","SPELL_CODE","LAB_TEL","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_LAB" 

2025-07-03 12:50:33.877 +08:00 [INF] 【SQL执行耗时:345.3574ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-03 12:50:34.298 +08:00 [INF] 【SQL执行耗时:350.113ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP" 

2025-07-03 12:50:34.710 +08:00 [INF] 【SQL执行耗时:340.2669ms】

[Sql]:SELECT "AREA_ID","AREA_CODE","AREA_SORT","AREA_NAME","AREA_CLASS","AREA_PERSON","AREA_ADDRESS","AREA_TEL","SPELL_CODE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","STATE_FLAG","REMARK","HOSPITAL_ID","TAG_SIGN","AREA_ANAME","PACK_BAR_PREFIX" FROM "XH_SYS"."SYS6_INSPECTION_AREA" 

2025-07-03 12:50:34.781 +08:00 [INF] HTTP GET /api/CodeCustom/GetEquipmentCodeCustomDict responded 200 in 3949.6871 ms
2025-07-03 12:50:34.781 +08:00 [INF] 【接口超时阀值预警】 [9e924579a0082cd3f9d444e9c594e09a]接口/api/CodeCustom/GetEquipmentCodeCustomDict,耗时:[3950]毫秒
2025-07-03 12:52:52.417 +08:00 [INF] ==>App Start..2025-07-03 12:52:52
2025-07-03 12:52:52.584 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-03 12:52:52.587 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-03 12:52:54.090 +08:00 [INF] ==>基础连接请求完成.
2025-07-03 12:52:54.459 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-03 12:52:54.816 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-03 12:52:55.145 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-03 12:52:55.152 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-03 12:52:55.509 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-03 12:52:55.914 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-03 12:52:56.004 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-03 12:52:56.409 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-03 12:52:56.410 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-03 12:52:57.348 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-03 12:52:59.593 +08:00 [INF] ==>初始化完成..
2025-07-03 12:52:59.613 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-03 12:52:59.615 +08:00 [INF] 设备启用任务
2025-07-03 12:52:59.616 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-03 12:53:00.014 +08:00 [INF] 【SQL执行耗时:376.0625ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-03 12:53:00.146 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-03 12:53:00.158 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-03 12:53:00.160 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-03 12:53:00.161 +08:00 [INF] Hosting environment: Development
2025-07-03 12:53:00.161 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-03 12:53:04.945 +08:00 [INF] 【SQL执行耗时:357.5395ms】

[Sql]:SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" <> :EqpNoState0 ) 
[Pars]:
[Name]::EqpNoState0 [Value]:2 [Type]:String    

2025-07-03 12:53:05.444 +08:00 [INF] 【SQL执行耗时:348.0286ms】

[Sql]:SELECT "DATA_ID","HOSPITAL_ID","LAB_ID","CLASS_ID","DATA_SORT","DATA_CNAME","DATA_ENAME","HIS_ID","CUSTOM_CODE","SPELL_CODE","DATA_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","DATA_SNAME","DATA_SOURCE","ONE_CLASS","DATA_UNAME","IF_REPEAT","SYSTEM_ID" FROM "XH_SYS"."SYS6_BASE_DATA"  WHERE (( "CLASS_ID" = :CLASS_ID0 ) AND ( "DATA_STATE" = :DATA_STATE1 )) 
[Pars]:
[Name]::CLASS_ID0 [Value]:专业分类 [Type]:String    
[Name]::DATA_STATE1 [Value]:1 [Type]:String    

2025-07-03 12:53:05.839 +08:00 [INF] 【SQL执行耗时:343.782ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_CLASS" = :EQUIPMENT_CLASS0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_CLASS0 [Value]:0 [Type]:String    

2025-07-03 12:53:06.270 +08:00 [INF] 【SQL执行耗时:359.4744ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 12:53:06.699 +08:00 [INF] 【SQL执行耗时:348.7974ms】

[Sql]:SELECT "HOSPITAL_ID","HOSPITAL_LOGIN","HOSPITAL_CNAME","HOSPITAL_SNAME","HOSPITAL_TYPE","HOSPITAL_LEVEL","LEVEL1_CODE","LEVEL2_CODE","LEVEL3_CODE","HOSPITAL_BARCODE","HOSPITAL_PROPERTY","HOSPITAL_PERSON","HOSPITAL_ADDRESS","HOSPITAL_TEL","HOSPITAL_STATE","FIRST_PERSON","FIRST_RTIME","LAST_MPRESON","LAST_MTIME","REMARK","HOSPITAL_BCODE","HOSPITAL_RANGE","BARCODE_TYPE","BARCODE_RANGE","CHARGE_PROVIDE_TYPE","DATADB_IP","WECHAT_NAME","LAST_MPERSON","FIRST_RPERSON","UPDATE_FLAG","INSERFACE_SERVICE","SAMELEVEL_HOSPITAL","HOSPITAL_CLASS","HOSPITAL_URL","REGISTER_CODE","PACK_BAR_PREFIX","IF_ITEM_MANAGE","IF_TEMPLATE_HOS","MEDICAL_CONGENER","MEDICAL_CONGENER_ROLE","VEST_IN_DC","INSTANCE_ID","HOSPITAL_ATTRIBUTE","DATASYNC_TYPE","HGROUP_ID","CITEM_MANAGE" FROM "XH_SYS"."SYS6_HOSPITAL_INFO" 

2025-07-03 12:53:07.317 +08:00 [INF] 【SQL执行耗时:535.9003ms】

[Sql]:SELECT "LAB_ID","FIRST_RPERSON","LAB_NAME","LAB_ADDRESS","LAB_CODE","LAST_MTIME","LAB_SORT","LAB_RNAME","LAB_CLASS","LAB_PERSON","LINKMAN_TEL","REGISTER_CODE","LAB_LINKMAN","STATE_FLAG","REMARK","LAST_MPERSON","FIRST_RTIME","SPELL_CODE","LAB_TEL","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_LAB" 

2025-07-03 12:53:07.748 +08:00 [INF] 【SQL执行耗时:355.5895ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-03 12:53:08.183 +08:00 [INF] 【SQL执行耗时:354.3952ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP" 

2025-07-03 12:53:08.608 +08:00 [INF] 【SQL执行耗时:346.2227ms】

[Sql]:SELECT "AREA_ID","AREA_CODE","AREA_SORT","AREA_NAME","AREA_CLASS","AREA_PERSON","AREA_ADDRESS","AREA_TEL","SPELL_CODE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","STATE_FLAG","REMARK","HOSPITAL_ID","TAG_SIGN","AREA_ANAME","PACK_BAR_PREFIX" FROM "XH_SYS"."SYS6_INSPECTION_AREA" 

2025-07-03 12:53:08.708 +08:00 [INF] HTTP GET /api/CodeCustom/GetEquipmentCodeCustomDict responded 200 in 6432.5443 ms
2025-07-03 12:53:08.710 +08:00 [INF] 【接口超时阀值预警】 [1e59549b0b22a3d5939bbb508e5e6445]接口/api/CodeCustom/GetEquipmentCodeCustomDict,耗时:[6439]毫秒
2025-07-03 12:56:16.914 +08:00 [INF] ==>App Start..2025-07-03 12:56:16
2025-07-03 12:56:17.085 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-03 12:56:17.089 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-03 12:56:18.539 +08:00 [INF] ==>基础连接请求完成.
2025-07-03 12:56:18.896 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-03 12:56:19.215 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-03 12:56:19.547 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-03 12:56:19.555 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-03 12:56:19.894 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-03 12:56:20.307 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-03 12:56:20.404 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-03 12:56:20.825 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-03 12:56:20.826 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-03 12:56:21.697 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-03 12:56:23.882 +08:00 [INF] ==>初始化完成..
2025-07-03 12:56:23.902 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-03 12:56:23.905 +08:00 [INF] 设备启用任务
2025-07-03 12:56:23.905 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-03 12:56:24.307 +08:00 [INF] 【SQL执行耗时:379.5382ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-03 12:56:24.447 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-03 12:56:24.462 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-03 12:56:24.463 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-03 12:56:24.463 +08:00 [INF] Hosting environment: Development
2025-07-03 12:56:24.463 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-03 12:56:50.615 +08:00 [INF] HTTP POST /api/CodeCustom/GetEquipmentUCodePreview/0 responded 200 in 2362.3776 ms
2025-07-03 12:56:50.631 +08:00 [INF] 【接口超时阀值预警】 [1da4ef27a34129be87f0b5fc2bed01d3]接口/api/CodeCustom/GetEquipmentUCodePreview/0,耗时:[2383]毫秒
2025-07-03 12:56:56.911 +08:00 [INF] 【SQL执行耗时:361.0684ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_CLASS" = :EQUIPMENT_CLASS0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_CLASS0 [Value]:0 [Type]:String    

2025-07-03 12:56:57.340 +08:00 [INF] 【SQL执行耗时:350.4142ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 12:56:57.752 +08:00 [INF] 【SQL执行耗时:336.1144ms】

[Sql]:SELECT "HOSPITAL_ID","HOSPITAL_LOGIN","HOSPITAL_CNAME","HOSPITAL_SNAME","HOSPITAL_TYPE","HOSPITAL_LEVEL","LEVEL1_CODE","LEVEL2_CODE","LEVEL3_CODE","HOSPITAL_BARCODE","HOSPITAL_PROPERTY","HOSPITAL_PERSON","HOSPITAL_ADDRESS","HOSPITAL_TEL","HOSPITAL_STATE","FIRST_PERSON","FIRST_RTIME","LAST_MPRESON","LAST_MTIME","REMARK","HOSPITAL_BCODE","HOSPITAL_RANGE","BARCODE_TYPE","BARCODE_RANGE","CHARGE_PROVIDE_TYPE","DATADB_IP","WECHAT_NAME","LAST_MPERSON","FIRST_RPERSON","UPDATE_FLAG","INSERFACE_SERVICE","SAMELEVEL_HOSPITAL","HOSPITAL_CLASS","HOSPITAL_URL","REGISTER_CODE","PACK_BAR_PREFIX","IF_ITEM_MANAGE","IF_TEMPLATE_HOS","MEDICAL_CONGENER","MEDICAL_CONGENER_ROLE","VEST_IN_DC","INSTANCE_ID","HOSPITAL_ATTRIBUTE","DATASYNC_TYPE","HGROUP_ID","CITEM_MANAGE" FROM "XH_SYS"."SYS6_HOSPITAL_INFO" 

2025-07-03 12:56:58.198 +08:00 [INF] 【SQL执行耗时:368.5447ms】

[Sql]:SELECT "LAB_ID","FIRST_RPERSON","LAB_NAME","LAB_ADDRESS","LAB_CODE","LAST_MTIME","LAB_SORT","LAB_RNAME","LAB_CLASS","LAB_PERSON","LINKMAN_TEL","REGISTER_CODE","LAB_LINKMAN","STATE_FLAG","REMARK","LAST_MPERSON","FIRST_RTIME","SPELL_CODE","LAB_TEL","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_LAB" 

2025-07-03 12:56:58.638 +08:00 [INF] 【SQL执行耗时:357.1476ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-03 12:56:59.058 +08:00 [INF] 【SQL执行耗时:342.4186ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP" 

2025-07-03 12:56:59.484 +08:00 [INF] 【SQL执行耗时:349.168ms】

[Sql]:SELECT "AREA_ID","AREA_CODE","AREA_SORT","AREA_NAME","AREA_CLASS","AREA_PERSON","AREA_ADDRESS","AREA_TEL","SPELL_CODE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","STATE_FLAG","REMARK","HOSPITAL_ID","TAG_SIGN","AREA_ANAME","PACK_BAR_PREFIX" FROM "XH_SYS"."SYS6_INSPECTION_AREA" 

2025-07-03 12:56:59.993 +08:00 [INF] 【SQL执行耗时:382.8093ms】

[Sql]:UPDATE "XH_OA"."EMS_EQUIPMENT_INFO"  SET
           "UNIT_ID"=:UNIT_ID,"HOSPITAL_ID"=:HOSPITAL_ID,"LAB_ID"=:LAB_ID,"PROFESSIONAL_CLASS"=:PROFESSIONAL_CLASS,"INSTRUMENT_ID"=:INSTRUMENT_ID,"ESERIES_ID"=:ESERIES_ID,"EQUIPMENT_NUM"=:EQUIPMENT_NUM,"EQUIPMENT_NAME"=:EQUIPMENT_NAME,"DEPT_SECTION_NO"=:DEPT_SECTION_NO,"EQUIPMENT_ENAME"=:EQUIPMENT_ENAME,"EQUIPMENT_MODEL"=:EQUIPMENT_MODEL,"VEST_PIPELINE"=:VEST_PIPELINE,"EQUIPMENT_CLASS"=:EQUIPMENT_CLASS,"DEPT_NAME"=:DEPT_NAME,"SECTION_NO"=:SECTION_NO,"EQUIPMENT_SORT"=:EQUIPMENT_SORT,"FACTORY_NUM"=:FACTORY_NUM,"EQUIPMENT_FEATURE"=:EQUIPMENT_FEATURE,"BUY_DATE"=:BUY_DATE,"SELL_PRICE"=:SELL_PRICE,"KEEP_PERSON"=:KEEP_PERSON,"INSTALL_DATE"=:INSTALL_DATE,"INSTALL_AREA"=:INSTALL_AREA,"DEPRECIATION_TIME"=:DEPRECIATION_TIME,"ANNUAL_SURVEY_DATE"=:ANNUAL_SURVEY_DATE,"MANUFACTURER"=:MANUFACTURER,"DEALER"=:DEALER,"REPAIR_COMPANY"=:REPAIR_COMPANY,"APPLY_STATE"=:APPLY_STATE,"CERTIFICATE_STATE"=:CERTIFICATE_STATE,"ACCEPT_REPORT_STATE"=:ACCEPT_REPORT_STATE,"EQUIPMENT_GRAPH_STATE"=:EQUIPMENT_GRAPH_STATE,"MANUAL_STATE"=:MANUAL_STATE,"REPAIR_PERSON"=:REPAIR_PERSON,"REPAIR_PERSON_STATE"=:REPAIR_PERSON_STATE,"CONTACT_PHONE"=:CONTACT_PHONE,"REGISTER_PERSON"=:REGISTER_PERSON,"REGISTER_TIME"=:REGISTER_TIME,"REGISTRATION_NUM"=:REGISTRATION_NUM,"REGISTRATION_ENUM"=:REGISTRATION_ENUM,"EQUIPMENT_STATE"=:EQUIPMENT_STATE,"FIRST_RPERSON"=:FIRST_RPERSON,"FIRST_RTIME"=:FIRST_RTIME,"LAST_MPERSON"=:LAST_MPERSON,"LAST_MTIME"=:LAST_MTIME,"REMARK"=:REMARK,"MANUFACTURER_ID"=:MANUFACTURER_ID,"DEALER_ID"=:DEALER_ID,"EQUIPMENT_SIZE"=:EQUIPMENT_SIZE,"EQUIPMENT_POWER"=:EQUIPMENT_POWER,"EQUIPMENT_VOLTAGE"=:EQUIPMENT_VOLTAGE,"EQUIPMENT_TEMP"=:EQUIPMENT_TEMP,"EQUIPMENT_TEMP_RANGE"=:EQUIPMENT_TEMP_RANGE,"EQ_IN_PERSON"=:EQ_IN_PERSON,"EQ_IN_TIME"=:EQ_IN_TIME,"EQ_OUT_PERSON"=:EQ_OUT_PERSON,"EQ_OUT_TIME"=:EQ_OUT_TIME,"EQ_SCRAP_PERSON"=:EQ_SCRAP_PERSON,"EQ_SCRAP_TIME"=:EQ_SCRAP_TIME,"SERIAL_NUMBER"=:SERIAL_NUMBER,"EQUIPMENT_CODE"=:EQUIPMENT_CODE,"DEALER_ENAME"=:DEALER_ENAME,"MANUFACTURER_ENAME"=:MANUFACTURER_ENAME,"EQUIPMENT_TYPE"=:EQUIPMENT_TYPE,"ENABLE_TIME"=:ENABLE_TIME,"IS_HIDE"=:IS_HIDE,"EQ_SERVICE_LIFE"=:EQ_SERVICE_LIFE,"SMBL_FLAG"=:SMBL_FLAG,"SMBL_LAB_ID"=:SMBL_LAB_ID,"PROVIDER_ID"=:PROVIDER_ID,"PROVIDER"=:PROVIDER,"EQUIPMENT_UCODE"=:EQUIPMENT_UCODE,"SMBL_CLASS"=:SMBL_CLASS,"SMBL_STATE"=:SMBL_STATE,"COUNTRY_ORIGIN"=:COUNTRY_ORIGIN,"POSITION_ID"=:POSITION_ID,"EQUIPMENT_JSON"=:EQUIPMENT_JSON  WHERE "EQUIPMENT_ID"=:EQUIPMENT_ID 
[Pars]:
[Name]::EQUIPMENT_ID [Value]:1721769948730232832 [Type]:String    
[Name]::UNIT_ID [Value]:PG001 [Type]:String    
[Name]::HOSPITAL_ID [Value]:33A001 [Type]:String    
[Name]::LAB_ID [Value]:L001 [Type]:String    
[Name]::PROFESSIONAL_CLASS [Value]: [Type]:String    
[Name]::INSTRUMENT_ID [Value]:27 [Type]:String    
[Name]::ESERIES_ID [Value]: [Type]:String    
[Name]::EQUIPMENT_NUM [Value]:双开门冷藏箱-15 [Type]:String    
[Name]::EQUIPMENT_NAME [Value]:双开门冷藏箱-15 [Type]:String    
[Name]::DEPT_SECTION_NO [Value]: [Type]:String    
[Name]::EQUIPMENT_ENAME [Value]: [Type]:String    
[Name]::EQUIPMENT_MODEL [Value]:SINSTRUMENT_ID [Type]:String    
[Name]::VEST_PIPELINE [Value]: [Type]:String    
[Name]::EQUIPMENT_CLASS [Value]:1 [Type]:String    
[Name]::DEPT_NAME [Value]: [Type]:String    
[Name]::SECTION_NO [Value]: [Type]:String    
[Name]::EQUIPMENT_SORT [Value]: [Type]:String    
[Name]::FACTORY_NUM [Value]: [Type]:String    
[Name]::EQUIPMENT_FEATURE [Value]: [Type]:String    
[Name]::BUY_DATE [Value]: [Type]:String    
[Name]::SELL_PRICE [Value]: [Type]:String    
[Name]::KEEP_PERSON [Value]: [Type]:String    
[Name]::INSTALL_DATE [Value]: [Type]:String    
[Name]::INSTALL_AREA [Value]: [Type]:String    
[Name]::DEPRECIATION_TIME [Value]: [Type]:String    
[Name]::ANNUAL_SURVEY_DATE [Value]: [Type]:String    
[Name]::MANUFACTURER [Value]: [Type]:String    
[Name]::DEALER [Value]: [Type]:String    
[Name]::REPAIR_COMPANY [Value]: [Type]:String    
[Name]::APPLY_STATE [Value]: [Type]:String    
[Name]::CERTIFICATE_STATE [Value]: [Type]:String    
[Name]::ACCEPT_REPORT_STATE [Value]: [Type]:String    
[Name]::EQUIPMENT_GRAPH_STATE [Value]: [Type]:String    
[Name]::MANUAL_STATE [Value]: [Type]:String    
[Name]::REPAIR_PERSON [Value]: [Type]:String    
[Name]::REPAIR_PERSON_STATE [Value]: [Type]:String    
[Name]::CONTACT_PHONE [Value]: [Type]:String    
[Name]::REGISTER_PERSON [Value]: [Type]:String    
[Name]::REGISTER_TIME [Value]: [Type]:DateTime    
[Name]::REGISTRATION_NUM [Value]: [Type]:String    
[Name]::REGISTRATION_ENUM [Value]: [Type]:String    
[Name]::EQUIPMENT_STATE [Value]:3 [Type]:String    
[Name]::FIRST_RPERSON [Value]:检测仪器同步数据 [Type]:String    
[Name]::FIRST_RTIME [Value]:2023/11/7 14:01:59 [Type]:DateTime    
[Name]::LAST_MPERSON [Value]:gz1_广测重 [Type]:String    
[Name]::LAST_MTIME [Value]:2025/2/22 17:51:38 [Type]:DateTime    
[Name]::REMARK [Value]: [Type]:String    
[Name]::MANUFACTURER_ID [Value]:M105 [Type]:String    
[Name]::DEALER_ID [Value]: [Type]:String    
[Name]::EQUIPMENT_SIZE [Value]: [Type]:String    
[Name]::EQUIPMENT_POWER [Value]: [Type]:String    
[Name]::EQUIPMENT_VOLTAGE [Value]: [Type]:String    
[Name]::EQUIPMENT_TEMP [Value]: [Type]:String    
[Name]::EQUIPMENT_TEMP_RANGE [Value]: [Type]:String    
[Name]::EQ_IN_PERSON [Value]: [Type]:String    
[Name]::EQ_IN_TIME [Value]: [Type]:DateTime    
[Name]::EQ_OUT_PERSON [Value]: [Type]:String    
[Name]::EQ_OUT_TIME [Value]: [Type]:DateTime    
[Name]::EQ_SCRAP_PERSON [Value]:gz1_广测重 [Type]:String    
[Name]::EQ_SCRAP_TIME [Value]:2024/8/1 18:56:07 [Type]:DateTime    
[Name]::SERIAL_NUMBER [Value]: [Type]:String    
[Name]::EQUIPMENT_CODE [Value]:双开门冷藏箱-15 [Type]:String    
[Name]::DEALER_ENAME [Value]: [Type]:String    
[Name]::MANUFACTURER_ENAME [Value]: [Type]:String    
[Name]::EQUIPMENT_TYPE [Value]: [Type]:String    
[Name]::ENABLE_TIME [Value]: [Type]:DateTime    
[Name]::IS_HIDE [Value]: [Type]:String    
[Name]::EQ_SERVICE_LIFE [Value]: [Type]:String    
[Name]::SMBL_FLAG [Value]:1 [Type]:String    
[Name]::SMBL_LAB_ID [Value]:33A001L0007 [Type]:String    
[Name]::PROVIDER_ID [Value]: [Type]:String    
[Name]::PROVIDER [Value]: [Type]:String    
[Name]::EQUIPMENT_UCODE [Value]:双开门冷藏箱-15_双开门冷藏箱-15 [Type]:String    
[Name]::SMBL_CLASS [Value]:1 [Type]:String    
[Name]::SMBL_STATE [Value]: [Type]:String    
[Name]::COUNTRY_ORIGIN [Value]:1 [Type]:Int64    
[Name]::POSITION_ID [Value]: [Type]:String    
[Name]::EQUIPMENT_JSON [Value]: [Type]:String    

2025-07-03 12:57:00.043 +08:00 [INF] HTTP POST /api/CodeCustom/GetEquipmentUCodePreview/0 responded 200 in 3576.7392 ms
2025-07-03 12:57:00.043 +08:00 [INF] 【接口超时阀值预警】 [3eefd070783ad19a5252bbede119b1e6]接口/api/CodeCustom/GetEquipmentUCodePreview/0,耗时:[3577]毫秒
2025-07-03 12:57:11.089 +08:00 [INF] 【SQL执行耗时:340.7928ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_CLASS" = :EQUIPMENT_CLASS0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_CLASS0 [Value]:1 [Type]:String    

2025-07-03 12:57:11.517 +08:00 [INF] 【SQL执行耗时:356.1894ms】

[Sql]:SELECT "HOSPITAL_ID","HOSPITAL_LOGIN","HOSPITAL_CNAME","HOSPITAL_SNAME","HOSPITAL_TYPE","HOSPITAL_LEVEL","LEVEL1_CODE","LEVEL2_CODE","LEVEL3_CODE","HOSPITAL_BARCODE","HOSPITAL_PROPERTY","HOSPITAL_PERSON","HOSPITAL_ADDRESS","HOSPITAL_TEL","HOSPITAL_STATE","FIRST_PERSON","FIRST_RTIME","LAST_MPRESON","LAST_MTIME","REMARK","HOSPITAL_BCODE","HOSPITAL_RANGE","BARCODE_TYPE","BARCODE_RANGE","CHARGE_PROVIDE_TYPE","DATADB_IP","WECHAT_NAME","LAST_MPERSON","FIRST_RPERSON","UPDATE_FLAG","INSERFACE_SERVICE","SAMELEVEL_HOSPITAL","HOSPITAL_CLASS","HOSPITAL_URL","REGISTER_CODE","PACK_BAR_PREFIX","IF_ITEM_MANAGE","IF_TEMPLATE_HOS","MEDICAL_CONGENER","MEDICAL_CONGENER_ROLE","VEST_IN_DC","INSTANCE_ID","HOSPITAL_ATTRIBUTE","DATASYNC_TYPE","HGROUP_ID","CITEM_MANAGE" FROM "XH_SYS"."SYS6_HOSPITAL_INFO" 

2025-07-03 12:57:11.959 +08:00 [INF] 【SQL执行耗时:368.6996ms】

[Sql]:SELECT "LAB_ID","FIRST_RPERSON","LAB_NAME","LAB_ADDRESS","LAB_CODE","LAST_MTIME","LAB_SORT","LAB_RNAME","LAB_CLASS","LAB_PERSON","LINKMAN_TEL","REGISTER_CODE","LAB_LINKMAN","STATE_FLAG","REMARK","LAST_MPERSON","FIRST_RTIME","SPELL_CODE","LAB_TEL","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_LAB" 

2025-07-03 12:57:12.385 +08:00 [INF] 【SQL执行耗时:350.1888ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-03 12:57:12.831 +08:00 [INF] 【SQL执行耗时:372.7478ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP" 

2025-07-03 12:57:13.293 +08:00 [INF] 【SQL执行耗时:385.556ms】

[Sql]:SELECT "AREA_ID","AREA_CODE","AREA_SORT","AREA_NAME","AREA_CLASS","AREA_PERSON","AREA_ADDRESS","AREA_TEL","SPELL_CODE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","STATE_FLAG","REMARK","HOSPITAL_ID","TAG_SIGN","AREA_ANAME","PACK_BAR_PREFIX" FROM "XH_SYS"."SYS6_INSPECTION_AREA" 

2025-07-03 12:57:13.701 +08:00 [INF] 【SQL执行耗时:331.6364ms】

[Sql]:UPDATE "XH_OA"."EMS_EQUIPMENT_INFO"  SET
           "UNIT_ID"=:UNIT_ID,"HOSPITAL_ID"=:HOSPITAL_ID,"LAB_ID"=:LAB_ID,"PROFESSIONAL_CLASS"=:PROFESSIONAL_CLASS,"INSTRUMENT_ID"=:INSTRUMENT_ID,"ESERIES_ID"=:ESERIES_ID,"EQUIPMENT_NUM"=:EQUIPMENT_NUM,"EQUIPMENT_NAME"=:EQUIPMENT_NAME,"DEPT_SECTION_NO"=:DEPT_SECTION_NO,"EQUIPMENT_ENAME"=:EQUIPMENT_ENAME,"EQUIPMENT_MODEL"=:EQUIPMENT_MODEL,"VEST_PIPELINE"=:VEST_PIPELINE,"EQUIPMENT_CLASS"=:EQUIPMENT_CLASS,"DEPT_NAME"=:DEPT_NAME,"SECTION_NO"=:SECTION_NO,"EQUIPMENT_SORT"=:EQUIPMENT_SORT,"FACTORY_NUM"=:FACTORY_NUM,"EQUIPMENT_FEATURE"=:EQUIPMENT_FEATURE,"BUY_DATE"=:BUY_DATE,"SELL_PRICE"=:SELL_PRICE,"KEEP_PERSON"=:KEEP_PERSON,"INSTALL_DATE"=:INSTALL_DATE,"INSTALL_AREA"=:INSTALL_AREA,"DEPRECIATION_TIME"=:DEPRECIATION_TIME,"ANNUAL_SURVEY_DATE"=:ANNUAL_SURVEY_DATE,"MANUFACTURER"=:MANUFACTURER,"DEALER"=:DEALER,"REPAIR_COMPANY"=:REPAIR_COMPANY,"APPLY_STATE"=:APPLY_STATE,"CERTIFICATE_STATE"=:CERTIFICATE_STATE,"ACCEPT_REPORT_STATE"=:ACCEPT_REPORT_STATE,"EQUIPMENT_GRAPH_STATE"=:EQUIPMENT_GRAPH_STATE,"MANUAL_STATE"=:MANUAL_STATE,"REPAIR_PERSON"=:REPAIR_PERSON,"REPAIR_PERSON_STATE"=:REPAIR_PERSON_STATE,"CONTACT_PHONE"=:CONTACT_PHONE,"REGISTER_PERSON"=:REGISTER_PERSON,"REGISTER_TIME"=:REGISTER_TIME,"REGISTRATION_NUM"=:REGISTRATION_NUM,"REGISTRATION_ENUM"=:REGISTRATION_ENUM,"EQUIPMENT_STATE"=:EQUIPMENT_STATE,"FIRST_RPERSON"=:FIRST_RPERSON,"FIRST_RTIME"=:FIRST_RTIME,"LAST_MPERSON"=:LAST_MPERSON,"LAST_MTIME"=:LAST_MTIME,"REMARK"=:REMARK,"MANUFACTURER_ID"=:MANUFACTURER_ID,"DEALER_ID"=:DEALER_ID,"EQUIPMENT_SIZE"=:EQUIPMENT_SIZE,"EQUIPMENT_POWER"=:EQUIPMENT_POWER,"EQUIPMENT_VOLTAGE"=:EQUIPMENT_VOLTAGE,"EQUIPMENT_TEMP"=:EQUIPMENT_TEMP,"EQUIPMENT_TEMP_RANGE"=:EQUIPMENT_TEMP_RANGE,"EQ_IN_PERSON"=:EQ_IN_PERSON,"EQ_IN_TIME"=:EQ_IN_TIME,"EQ_OUT_PERSON"=:EQ_OUT_PERSON,"EQ_OUT_TIME"=:EQ_OUT_TIME,"EQ_SCRAP_PERSON"=:EQ_SCRAP_PERSON,"EQ_SCRAP_TIME"=:EQ_SCRAP_TIME,"SERIAL_NUMBER"=:SERIAL_NUMBER,"EQUIPMENT_CODE"=:EQUIPMENT_CODE,"DEALER_ENAME"=:DEALER_ENAME,"MANUFACTURER_ENAME"=:MANUFACTURER_ENAME,"EQUIPMENT_TYPE"=:EQUIPMENT_TYPE,"ENABLE_TIME"=:ENABLE_TIME,"IS_HIDE"=:IS_HIDE,"EQ_SERVICE_LIFE"=:EQ_SERVICE_LIFE,"SMBL_FLAG"=:SMBL_FLAG,"SMBL_LAB_ID"=:SMBL_LAB_ID,"PROVIDER_ID"=:PROVIDER_ID,"PROVIDER"=:PROVIDER,"EQUIPMENT_UCODE"=:EQUIPMENT_UCODE,"SMBL_CLASS"=:SMBL_CLASS,"SMBL_STATE"=:SMBL_STATE,"COUNTRY_ORIGIN"=:COUNTRY_ORIGIN,"POSITION_ID"=:POSITION_ID,"EQUIPMENT_JSON"=:EQUIPMENT_JSON  WHERE "EQUIPMENT_ID"=:EQUIPMENT_ID 
[Pars]:
[Name]::EQUIPMENT_ID [Value]:1721769948730232832 [Type]:String    
[Name]::UNIT_ID [Value]:PG001 [Type]:String    
[Name]::HOSPITAL_ID [Value]:33A001 [Type]:String    
[Name]::LAB_ID [Value]:L001 [Type]:String    
[Name]::PROFESSIONAL_CLASS [Value]: [Type]:String    
[Name]::INSTRUMENT_ID [Value]:27 [Type]:String    
[Name]::ESERIES_ID [Value]: [Type]:String    
[Name]::EQUIPMENT_NUM [Value]:双开门冷藏箱-15 [Type]:String    
[Name]::EQUIPMENT_NAME [Value]:双开门冷藏箱-15 [Type]:String    
[Name]::DEPT_SECTION_NO [Value]: [Type]:String    
[Name]::EQUIPMENT_ENAME [Value]: [Type]:String    
[Name]::EQUIPMENT_MODEL [Value]:SINSTRUMENT_ID [Type]:String    
[Name]::VEST_PIPELINE [Value]: [Type]:String    
[Name]::EQUIPMENT_CLASS [Value]:1 [Type]:String    
[Name]::DEPT_NAME [Value]: [Type]:String    
[Name]::SECTION_NO [Value]: [Type]:String    
[Name]::EQUIPMENT_SORT [Value]: [Type]:String    
[Name]::FACTORY_NUM [Value]: [Type]:String    
[Name]::EQUIPMENT_FEATURE [Value]: [Type]:String    
[Name]::BUY_DATE [Value]: [Type]:String    
[Name]::SELL_PRICE [Value]: [Type]:String    
[Name]::KEEP_PERSON [Value]: [Type]:String    
[Name]::INSTALL_DATE [Value]: [Type]:String    
[Name]::INSTALL_AREA [Value]: [Type]:String    
[Name]::DEPRECIATION_TIME [Value]: [Type]:String    
[Name]::ANNUAL_SURVEY_DATE [Value]: [Type]:String    
[Name]::MANUFACTURER [Value]: [Type]:String    
[Name]::DEALER [Value]: [Type]:String    
[Name]::REPAIR_COMPANY [Value]: [Type]:String    
[Name]::APPLY_STATE [Value]: [Type]:String    
[Name]::CERTIFICATE_STATE [Value]: [Type]:String    
[Name]::ACCEPT_REPORT_STATE [Value]: [Type]:String    
[Name]::EQUIPMENT_GRAPH_STATE [Value]: [Type]:String    
[Name]::MANUAL_STATE [Value]: [Type]:String    
[Name]::REPAIR_PERSON [Value]: [Type]:String    
[Name]::REPAIR_PERSON_STATE [Value]: [Type]:String    
[Name]::CONTACT_PHONE [Value]: [Type]:String    
[Name]::REGISTER_PERSON [Value]: [Type]:String    
[Name]::REGISTER_TIME [Value]: [Type]:DateTime    
[Name]::REGISTRATION_NUM [Value]: [Type]:String    
[Name]::REGISTRATION_ENUM [Value]: [Type]:String    
[Name]::EQUIPMENT_STATE [Value]:3 [Type]:String    
[Name]::FIRST_RPERSON [Value]:检测仪器同步数据 [Type]:String    
[Name]::FIRST_RTIME [Value]:2023/11/7 14:01:59 [Type]:DateTime    
[Name]::LAST_MPERSON [Value]:gz1_广测重 [Type]:String    
[Name]::LAST_MTIME [Value]:2025/2/22 17:51:38 [Type]:DateTime    
[Name]::REMARK [Value]: [Type]:String    
[Name]::MANUFACTURER_ID [Value]:M105 [Type]:String    
[Name]::DEALER_ID [Value]: [Type]:String    
[Name]::EQUIPMENT_SIZE [Value]: [Type]:String    
[Name]::EQUIPMENT_POWER [Value]: [Type]:String    
[Name]::EQUIPMENT_VOLTAGE [Value]: [Type]:String    
[Name]::EQUIPMENT_TEMP [Value]: [Type]:String    
[Name]::EQUIPMENT_TEMP_RANGE [Value]: [Type]:String    
[Name]::EQ_IN_PERSON [Value]: [Type]:String    
[Name]::EQ_IN_TIME [Value]: [Type]:DateTime    
[Name]::EQ_OUT_PERSON [Value]: [Type]:String    
[Name]::EQ_OUT_TIME [Value]: [Type]:DateTime    
[Name]::EQ_SCRAP_PERSON [Value]:gz1_广测重 [Type]:String    
[Name]::EQ_SCRAP_TIME [Value]:2024/8/1 18:56:07 [Type]:DateTime    
[Name]::SERIAL_NUMBER [Value]: [Type]:String    
[Name]::EQUIPMENT_CODE [Value]:双开门冷藏箱-15 [Type]:String    
[Name]::DEALER_ENAME [Value]: [Type]:String    
[Name]::MANUFACTURER_ENAME [Value]: [Type]:String    
[Name]::EQUIPMENT_TYPE [Value]: [Type]:String    
[Name]::ENABLE_TIME [Value]: [Type]:DateTime    
[Name]::IS_HIDE [Value]: [Type]:String    
[Name]::EQ_SERVICE_LIFE [Value]: [Type]:String    
[Name]::SMBL_FLAG [Value]:1 [Type]:String    
[Name]::SMBL_LAB_ID [Value]:33A001L0007 [Type]:String    
[Name]::PROVIDER_ID [Value]: [Type]:String    
[Name]::PROVIDER [Value]: [Type]:String    
[Name]::EQUIPMENT_UCODE [Value]:双开门冷藏箱-15_双开门冷藏箱-15 [Type]:String    
[Name]::SMBL_CLASS [Value]:1 [Type]:String    
[Name]::SMBL_STATE [Value]: [Type]:String    
[Name]::COUNTRY_ORIGIN [Value]:1 [Type]:Int64    
[Name]::POSITION_ID [Value]: [Type]:String    
[Name]::EQUIPMENT_JSON [Value]: [Type]:String    

2025-07-03 12:57:13.740 +08:00 [INF] HTTP POST /api/CodeCustom/GetEquipmentUCodePreview/1 responded 200 in 3032.7209 ms
2025-07-03 12:57:13.740 +08:00 [INF] 【接口超时阀值预警】 [12550d0bb134f4178b86660a447eb2a9]接口/api/CodeCustom/GetEquipmentUCodePreview/1,耗时:[3033]毫秒
2025-07-03 12:57:16.994 +08:00 [INF] 【SQL执行耗时:349.023ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_CLASS" = :EQUIPMENT_CLASS0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_CLASS0 [Value]:2 [Type]:String    

2025-07-03 12:57:17.425 +08:00 [INF] 【SQL执行耗时:357.9449ms】

[Sql]:SELECT "HOSPITAL_ID","HOSPITAL_LOGIN","HOSPITAL_CNAME","HOSPITAL_SNAME","HOSPITAL_TYPE","HOSPITAL_LEVEL","LEVEL1_CODE","LEVEL2_CODE","LEVEL3_CODE","HOSPITAL_BARCODE","HOSPITAL_PROPERTY","HOSPITAL_PERSON","HOSPITAL_ADDRESS","HOSPITAL_TEL","HOSPITAL_STATE","FIRST_PERSON","FIRST_RTIME","LAST_MPRESON","LAST_MTIME","REMARK","HOSPITAL_BCODE","HOSPITAL_RANGE","BARCODE_TYPE","BARCODE_RANGE","CHARGE_PROVIDE_TYPE","DATADB_IP","WECHAT_NAME","LAST_MPERSON","FIRST_RPERSON","UPDATE_FLAG","INSERFACE_SERVICE","SAMELEVEL_HOSPITAL","HOSPITAL_CLASS","HOSPITAL_URL","REGISTER_CODE","PACK_BAR_PREFIX","IF_ITEM_MANAGE","IF_TEMPLATE_HOS","MEDICAL_CONGENER","MEDICAL_CONGENER_ROLE","VEST_IN_DC","INSTANCE_ID","HOSPITAL_ATTRIBUTE","DATASYNC_TYPE","HGROUP_ID","CITEM_MANAGE" FROM "XH_SYS"."SYS6_HOSPITAL_INFO" 

2025-07-03 12:57:17.848 +08:00 [INF] 【SQL执行耗时:344.4477ms】

[Sql]:SELECT "LAB_ID","FIRST_RPERSON","LAB_NAME","LAB_ADDRESS","LAB_CODE","LAST_MTIME","LAB_SORT","LAB_RNAME","LAB_CLASS","LAB_PERSON","LINKMAN_TEL","REGISTER_CODE","LAB_LINKMAN","STATE_FLAG","REMARK","LAST_MPERSON","FIRST_RTIME","SPELL_CODE","LAB_TEL","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_LAB" 

2025-07-03 12:57:18.267 +08:00 [INF] 【SQL执行耗时:349.4966ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-03 12:57:18.677 +08:00 [INF] 【SQL执行耗时:341.0776ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP" 

2025-07-03 12:57:19.166 +08:00 [INF] 【SQL执行耗时:418.4992ms】

[Sql]:SELECT "AREA_ID","AREA_CODE","AREA_SORT","AREA_NAME","AREA_CLASS","AREA_PERSON","AREA_ADDRESS","AREA_TEL","SPELL_CODE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","STATE_FLAG","REMARK","HOSPITAL_ID","TAG_SIGN","AREA_ANAME","PACK_BAR_PREFIX" FROM "XH_SYS"."SYS6_INSPECTION_AREA" 

2025-07-03 12:57:19.607 +08:00 [INF] 【SQL执行耗时:364.6091ms】

[Sql]:UPDATE "XH_OA"."EMS_EQUIPMENT_INFO"  SET
           "UNIT_ID"=:UNIT_ID,"HOSPITAL_ID"=:HOSPITAL_ID,"LAB_ID"=:LAB_ID,"PROFESSIONAL_CLASS"=:PROFESSIONAL_CLASS,"INSTRUMENT_ID"=:INSTRUMENT_ID,"ESERIES_ID"=:ESERIES_ID,"EQUIPMENT_NUM"=:EQUIPMENT_NUM,"EQUIPMENT_NAME"=:EQUIPMENT_NAME,"DEPT_SECTION_NO"=:DEPT_SECTION_NO,"EQUIPMENT_ENAME"=:EQUIPMENT_ENAME,"EQUIPMENT_MODEL"=:EQUIPMENT_MODEL,"VEST_PIPELINE"=:VEST_PIPELINE,"EQUIPMENT_CLASS"=:EQUIPMENT_CLASS,"DEPT_NAME"=:DEPT_NAME,"SECTION_NO"=:SECTION_NO,"EQUIPMENT_SORT"=:EQUIPMENT_SORT,"FACTORY_NUM"=:FACTORY_NUM,"EQUIPMENT_FEATURE"=:EQUIPMENT_FEATURE,"BUY_DATE"=:BUY_DATE,"SELL_PRICE"=:SELL_PRICE,"KEEP_PERSON"=:KEEP_PERSON,"INSTALL_DATE"=:INSTALL_DATE,"INSTALL_AREA"=:INSTALL_AREA,"DEPRECIATION_TIME"=:DEPRECIATION_TIME,"ANNUAL_SURVEY_DATE"=:ANNUAL_SURVEY_DATE,"MANUFACTURER"=:MANUFACTURER,"DEALER"=:DEALER,"REPAIR_COMPANY"=:REPAIR_COMPANY,"APPLY_STATE"=:APPLY_STATE,"CERTIFICATE_STATE"=:CERTIFICATE_STATE,"ACCEPT_REPORT_STATE"=:ACCEPT_REPORT_STATE,"EQUIPMENT_GRAPH_STATE"=:EQUIPMENT_GRAPH_STATE,"MANUAL_STATE"=:MANUAL_STATE,"REPAIR_PERSON"=:REPAIR_PERSON,"REPAIR_PERSON_STATE"=:REPAIR_PERSON_STATE,"CONTACT_PHONE"=:CONTACT_PHONE,"REGISTER_PERSON"=:REGISTER_PERSON,"REGISTER_TIME"=:REGISTER_TIME,"REGISTRATION_NUM"=:REGISTRATION_NUM,"REGISTRATION_ENUM"=:REGISTRATION_ENUM,"EQUIPMENT_STATE"=:EQUIPMENT_STATE,"FIRST_RPERSON"=:FIRST_RPERSON,"FIRST_RTIME"=:FIRST_RTIME,"LAST_MPERSON"=:LAST_MPERSON,"LAST_MTIME"=:LAST_MTIME,"REMARK"=:REMARK,"MANUFACTURER_ID"=:MANUFACTURER_ID,"DEALER_ID"=:DEALER_ID,"EQUIPMENT_SIZE"=:EQUIPMENT_SIZE,"EQUIPMENT_POWER"=:EQUIPMENT_POWER,"EQUIPMENT_VOLTAGE"=:EQUIPMENT_VOLTAGE,"EQUIPMENT_TEMP"=:EQUIPMENT_TEMP,"EQUIPMENT_TEMP_RANGE"=:EQUIPMENT_TEMP_RANGE,"EQ_IN_PERSON"=:EQ_IN_PERSON,"EQ_IN_TIME"=:EQ_IN_TIME,"EQ_OUT_PERSON"=:EQ_OUT_PERSON,"EQ_OUT_TIME"=:EQ_OUT_TIME,"EQ_SCRAP_PERSON"=:EQ_SCRAP_PERSON,"EQ_SCRAP_TIME"=:EQ_SCRAP_TIME,"SERIAL_NUMBER"=:SERIAL_NUMBER,"EQUIPMENT_CODE"=:EQUIPMENT_CODE,"DEALER_ENAME"=:DEALER_ENAME,"MANUFACTURER_ENAME"=:MANUFACTURER_ENAME,"EQUIPMENT_TYPE"=:EQUIPMENT_TYPE,"ENABLE_TIME"=:ENABLE_TIME,"IS_HIDE"=:IS_HIDE,"EQ_SERVICE_LIFE"=:EQ_SERVICE_LIFE,"SMBL_FLAG"=:SMBL_FLAG,"SMBL_LAB_ID"=:SMBL_LAB_ID,"PROVIDER_ID"=:PROVIDER_ID,"PROVIDER"=:PROVIDER,"EQUIPMENT_UCODE"=:EQUIPMENT_UCODE,"SMBL_CLASS"=:SMBL_CLASS,"SMBL_STATE"=:SMBL_STATE,"COUNTRY_ORIGIN"=:COUNTRY_ORIGIN,"POSITION_ID"=:POSITION_ID,"EQUIPMENT_JSON"=:EQUIPMENT_JSON  WHERE "EQUIPMENT_ID"=:EQUIPMENT_ID 
[Pars]:
[Name]::EQUIPMENT_ID [Value]:399FA3AE95CF4A6F86A728B5E65EFB07 [Type]:String    
[Name]::UNIT_ID [Value]:PG065 [Type]:String    
[Name]::HOSPITAL_ID [Value]:33A001 [Type]:String    
[Name]::LAB_ID [Value]:LAB001 [Type]:String    
[Name]::PROFESSIONAL_CLASS [Value]: [Type]:String    
[Name]::INSTRUMENT_ID [Value]: [Type]:String    
[Name]::ESERIES_ID [Value]: [Type]:String    
[Name]::EQUIPMENT_NUM [Value]:nihao [Type]:String    
[Name]::EQUIPMENT_NAME [Value]:你好 [Type]:String    
[Name]::DEPT_SECTION_NO [Value]: [Type]:String    
[Name]::EQUIPMENT_ENAME [Value]: [Type]:String    
[Name]::EQUIPMENT_MODEL [Value]:nihao [Type]:String    
[Name]::VEST_PIPELINE [Value]: [Type]:String    
[Name]::EQUIPMENT_CLASS [Value]:2 [Type]:String    
[Name]::DEPT_NAME [Value]: [Type]:String    
[Name]::SECTION_NO [Value]: [Type]:String    
[Name]::EQUIPMENT_SORT [Value]: [Type]:String    
[Name]::FACTORY_NUM [Value]: [Type]:String    
[Name]::EQUIPMENT_FEATURE [Value]: [Type]:String    
[Name]::BUY_DATE [Value]: [Type]:String    
[Name]::SELL_PRICE [Value]: [Type]:String    
[Name]::KEEP_PERSON [Value]: [Type]:String    
[Name]::INSTALL_DATE [Value]: [Type]:String    
[Name]::INSTALL_AREA [Value]: [Type]:String    
[Name]::DEPRECIATION_TIME [Value]: [Type]:String    
[Name]::ANNUAL_SURVEY_DATE [Value]: [Type]:String    
[Name]::MANUFACTURER [Value]: [Type]:String    
[Name]::DEALER [Value]: [Type]:String    
[Name]::REPAIR_COMPANY [Value]: [Type]:String    
[Name]::APPLY_STATE [Value]: [Type]:String    
[Name]::CERTIFICATE_STATE [Value]: [Type]:String    
[Name]::ACCEPT_REPORT_STATE [Value]: [Type]:String    
[Name]::EQUIPMENT_GRAPH_STATE [Value]: [Type]:String    
[Name]::MANUAL_STATE [Value]: [Type]:String    
[Name]::REPAIR_PERSON [Value]: [Type]:String    
[Name]::REPAIR_PERSON_STATE [Value]: [Type]:String    
[Name]::CONTACT_PHONE [Value]: [Type]:String    
[Name]::REGISTER_PERSON [Value]: [Type]:String    
[Name]::REGISTER_TIME [Value]: [Type]:DateTime    
[Name]::REGISTRATION_NUM [Value]: [Type]:String    
[Name]::REGISTRATION_ENUM [Value]: [Type]:String    
[Name]::EQUIPMENT_STATE [Value]:1 [Type]:String    
[Name]::FIRST_RPERSON [Value]:gz1_广测重 [Type]:String    
[Name]::FIRST_RTIME [Value]:2024/12/20 9:20:25 [Type]:DateTime    
[Name]::LAST_MPERSON [Value]:gz1_广测重 [Type]:String    
[Name]::LAST_MTIME [Value]:2024/12/20 9:20:50 [Type]:DateTime    
[Name]::REMARK [Value]: [Type]:String    
[Name]::MANUFACTURER_ID [Value]: [Type]:String    
[Name]::DEALER_ID [Value]: [Type]:String    
[Name]::EQUIPMENT_SIZE [Value]: [Type]:String    
[Name]::EQUIPMENT_POWER [Value]: [Type]:String    
[Name]::EQUIPMENT_VOLTAGE [Value]: [Type]:String    
[Name]::EQUIPMENT_TEMP [Value]: [Type]:String    
[Name]::EQUIPMENT_TEMP_RANGE [Value]: [Type]:String    
[Name]::EQ_IN_PERSON [Value]: [Type]:String    
[Name]::EQ_IN_TIME [Value]: [Type]:DateTime    
[Name]::EQ_OUT_PERSON [Value]: [Type]:String    
[Name]::EQ_OUT_TIME [Value]: [Type]:DateTime    
[Name]::EQ_SCRAP_PERSON [Value]: [Type]:String    
[Name]::EQ_SCRAP_TIME [Value]: [Type]:DateTime    
[Name]::SERIAL_NUMBER [Value]: [Type]:String    
[Name]::EQUIPMENT_CODE [Value]:nihao [Type]:String    
[Name]::DEALER_ENAME [Value]: [Type]:String    
[Name]::MANUFACTURER_ENAME [Value]: [Type]:String    
[Name]::EQUIPMENT_TYPE [Value]: [Type]:String    
[Name]::ENABLE_TIME [Value]:2024/12/20 0:00:00 [Type]:DateTime    
[Name]::IS_HIDE [Value]: [Type]:String    
[Name]::EQ_SERVICE_LIFE [Value]: [Type]:String    
[Name]::SMBL_FLAG [Value]:0 [Type]:String    
[Name]::SMBL_LAB_ID [Value]: [Type]:String    
[Name]::PROVIDER_ID [Value]: [Type]:String    
[Name]::PROVIDER [Value]: [Type]:String    
[Name]::EQUIPMENT_UCODE [Value]:nihao_你好 [Type]:String    
[Name]::SMBL_CLASS [Value]: [Type]:String    
[Name]::SMBL_STATE [Value]: [Type]:String    
[Name]::COUNTRY_ORIGIN [Value]:1 [Type]:Int64    
[Name]::POSITION_ID [Value]: [Type]:String    
[Name]::EQUIPMENT_JSON [Value]: [Type]:String    

2025-07-03 12:57:19.648 +08:00 [INF] HTTP POST /api/CodeCustom/GetEquipmentUCodePreview/2 responded 200 in 3041.1346 ms
2025-07-03 12:57:19.648 +08:00 [INF] 【接口超时阀值预警】 [c7dca406af58bb20eb064be48888742c]接口/api/CodeCustom/GetEquipmentUCodePreview/2,耗时:[3041]毫秒
2025-07-03 12:57:23.084 +08:00 [INF] 【SQL执行耗时:347.9001ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_CLASS" = :EQUIPMENT_CLASS0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_CLASS0 [Value]:3 [Type]:String    

2025-07-03 12:57:23.522 +08:00 [INF] 【SQL执行耗时:365.6779ms】

[Sql]:SELECT "HOSPITAL_ID","HOSPITAL_LOGIN","HOSPITAL_CNAME","HOSPITAL_SNAME","HOSPITAL_TYPE","HOSPITAL_LEVEL","LEVEL1_CODE","LEVEL2_CODE","LEVEL3_CODE","HOSPITAL_BARCODE","HOSPITAL_PROPERTY","HOSPITAL_PERSON","HOSPITAL_ADDRESS","HOSPITAL_TEL","HOSPITAL_STATE","FIRST_PERSON","FIRST_RTIME","LAST_MPRESON","LAST_MTIME","REMARK","HOSPITAL_BCODE","HOSPITAL_RANGE","BARCODE_TYPE","BARCODE_RANGE","CHARGE_PROVIDE_TYPE","DATADB_IP","WECHAT_NAME","LAST_MPERSON","FIRST_RPERSON","UPDATE_FLAG","INSERFACE_SERVICE","SAMELEVEL_HOSPITAL","HOSPITAL_CLASS","HOSPITAL_URL","REGISTER_CODE","PACK_BAR_PREFIX","IF_ITEM_MANAGE","IF_TEMPLATE_HOS","MEDICAL_CONGENER","MEDICAL_CONGENER_ROLE","VEST_IN_DC","INSTANCE_ID","HOSPITAL_ATTRIBUTE","DATASYNC_TYPE","HGROUP_ID","CITEM_MANAGE" FROM "XH_SYS"."SYS6_HOSPITAL_INFO" 

2025-07-03 12:57:23.943 +08:00 [INF] 【SQL执行耗时:345.335ms】

[Sql]:SELECT "LAB_ID","FIRST_RPERSON","LAB_NAME","LAB_ADDRESS","LAB_CODE","LAST_MTIME","LAB_SORT","LAB_RNAME","LAB_CLASS","LAB_PERSON","LINKMAN_TEL","REGISTER_CODE","LAB_LINKMAN","STATE_FLAG","REMARK","LAST_MPERSON","FIRST_RTIME","SPELL_CODE","LAB_TEL","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_LAB" 

2025-07-03 12:57:24.362 +08:00 [INF] 【SQL执行耗时:348.3021ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-03 12:57:24.810 +08:00 [INF] 【SQL执行耗时:376.0915ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP" 

2025-07-03 12:57:25.264 +08:00 [INF] 【SQL执行耗时:371.7718ms】

[Sql]:SELECT "AREA_ID","AREA_CODE","AREA_SORT","AREA_NAME","AREA_CLASS","AREA_PERSON","AREA_ADDRESS","AREA_TEL","SPELL_CODE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","STATE_FLAG","REMARK","HOSPITAL_ID","TAG_SIGN","AREA_ANAME","PACK_BAR_PREFIX" FROM "XH_SYS"."SYS6_INSPECTION_AREA" 

2025-07-03 12:57:25.681 +08:00 [INF] 【SQL执行耗时:342.629ms】

[Sql]:UPDATE "XH_OA"."EMS_EQUIPMENT_INFO"  SET
           "UNIT_ID"=:UNIT_ID,"HOSPITAL_ID"=:HOSPITAL_ID,"LAB_ID"=:LAB_ID,"PROFESSIONAL_CLASS"=:PROFESSIONAL_CLASS,"INSTRUMENT_ID"=:INSTRUMENT_ID,"ESERIES_ID"=:ESERIES_ID,"EQUIPMENT_NUM"=:EQUIPMENT_NUM,"EQUIPMENT_NAME"=:EQUIPMENT_NAME,"DEPT_SECTION_NO"=:DEPT_SECTION_NO,"EQUIPMENT_ENAME"=:EQUIPMENT_ENAME,"EQUIPMENT_MODEL"=:EQUIPMENT_MODEL,"VEST_PIPELINE"=:VEST_PIPELINE,"EQUIPMENT_CLASS"=:EQUIPMENT_CLASS,"DEPT_NAME"=:DEPT_NAME,"SECTION_NO"=:SECTION_NO,"EQUIPMENT_SORT"=:EQUIPMENT_SORT,"FACTORY_NUM"=:FACTORY_NUM,"EQUIPMENT_FEATURE"=:EQUIPMENT_FEATURE,"BUY_DATE"=:BUY_DATE,"SELL_PRICE"=:SELL_PRICE,"KEEP_PERSON"=:KEEP_PERSON,"INSTALL_DATE"=:INSTALL_DATE,"INSTALL_AREA"=:INSTALL_AREA,"DEPRECIATION_TIME"=:DEPRECIATION_TIME,"ANNUAL_SURVEY_DATE"=:ANNUAL_SURVEY_DATE,"MANUFACTURER"=:MANUFACTURER,"DEALER"=:DEALER,"REPAIR_COMPANY"=:REPAIR_COMPANY,"APPLY_STATE"=:APPLY_STATE,"CERTIFICATE_STATE"=:CERTIFICATE_STATE,"ACCEPT_REPORT_STATE"=:ACCEPT_REPORT_STATE,"EQUIPMENT_GRAPH_STATE"=:EQUIPMENT_GRAPH_STATE,"MANUAL_STATE"=:MANUAL_STATE,"REPAIR_PERSON"=:REPAIR_PERSON,"REPAIR_PERSON_STATE"=:REPAIR_PERSON_STATE,"CONTACT_PHONE"=:CONTACT_PHONE,"REGISTER_PERSON"=:REGISTER_PERSON,"REGISTER_TIME"=:REGISTER_TIME,"REGISTRATION_NUM"=:REGISTRATION_NUM,"REGISTRATION_ENUM"=:REGISTRATION_ENUM,"EQUIPMENT_STATE"=:EQUIPMENT_STATE,"FIRST_RPERSON"=:FIRST_RPERSON,"FIRST_RTIME"=:FIRST_RTIME,"LAST_MPERSON"=:LAST_MPERSON,"LAST_MTIME"=:LAST_MTIME,"REMARK"=:REMARK,"MANUFACTURER_ID"=:MANUFACTURER_ID,"DEALER_ID"=:DEALER_ID,"EQUIPMENT_SIZE"=:EQUIPMENT_SIZE,"EQUIPMENT_POWER"=:EQUIPMENT_POWER,"EQUIPMENT_VOLTAGE"=:EQUIPMENT_VOLTAGE,"EQUIPMENT_TEMP"=:EQUIPMENT_TEMP,"EQUIPMENT_TEMP_RANGE"=:EQUIPMENT_TEMP_RANGE,"EQ_IN_PERSON"=:EQ_IN_PERSON,"EQ_IN_TIME"=:EQ_IN_TIME,"EQ_OUT_PERSON"=:EQ_OUT_PERSON,"EQ_OUT_TIME"=:EQ_OUT_TIME,"EQ_SCRAP_PERSON"=:EQ_SCRAP_PERSON,"EQ_SCRAP_TIME"=:EQ_SCRAP_TIME,"SERIAL_NUMBER"=:SERIAL_NUMBER,"EQUIPMENT_CODE"=:EQUIPMENT_CODE,"DEALER_ENAME"=:DEALER_ENAME,"MANUFACTURER_ENAME"=:MANUFACTURER_ENAME,"EQUIPMENT_TYPE"=:EQUIPMENT_TYPE,"ENABLE_TIME"=:ENABLE_TIME,"IS_HIDE"=:IS_HIDE,"EQ_SERVICE_LIFE"=:EQ_SERVICE_LIFE,"SMBL_FLAG"=:SMBL_FLAG,"SMBL_LAB_ID"=:SMBL_LAB_ID,"PROVIDER_ID"=:PROVIDER_ID,"PROVIDER"=:PROVIDER,"EQUIPMENT_UCODE"=:EQUIPMENT_UCODE,"SMBL_CLASS"=:SMBL_CLASS,"SMBL_STATE"=:SMBL_STATE,"COUNTRY_ORIGIN"=:COUNTRY_ORIGIN,"POSITION_ID"=:POSITION_ID,"EQUIPMENT_JSON"=:EQUIPMENT_JSON  WHERE "EQUIPMENT_ID"=:EQUIPMENT_ID 
[Pars]:
[Name]::EQUIPMENT_ID [Value]:1724383226295881728 [Type]:String    
[Name]::UNIT_ID [Value]:PG002 [Type]:String    
[Name]::HOSPITAL_ID [Value]:33A001 [Type]:String    
[Name]::LAB_ID [Value]:L001 [Type]:String    
[Name]::PROFESSIONAL_CLASS [Value]:血液类 [Type]:String    
[Name]::INSTRUMENT_ID [Value]: [Type]:String    
[Name]::ESERIES_ID [Value]: [Type]:String    
[Name]::EQUIPMENT_NUM [Value]:测试1 [Type]:String    
[Name]::EQUIPMENT_NAME [Value]:测试1 [Type]:String    
[Name]::DEPT_SECTION_NO [Value]: [Type]:String    
[Name]::EQUIPMENT_ENAME [Value]: [Type]:String    
[Name]::EQUIPMENT_MODEL [Value]:测试1 [Type]:String    
[Name]::VEST_PIPELINE [Value]: [Type]:String    
[Name]::EQUIPMENT_CLASS [Value]:3 [Type]:String    
[Name]::DEPT_NAME [Value]: [Type]:String    
[Name]::SECTION_NO [Value]: [Type]:String    
[Name]::EQUIPMENT_SORT [Value]: [Type]:String    
[Name]::FACTORY_NUM [Value]: [Type]:String    
[Name]::EQUIPMENT_FEATURE [Value]: [Type]:String    
[Name]::BUY_DATE [Value]: [Type]:String    
[Name]::SELL_PRICE [Value]: [Type]:String    
[Name]::KEEP_PERSON [Value]: [Type]:String    
[Name]::INSTALL_DATE [Value]:2024-02-21T16:00:00.000Z [Type]:String    
[Name]::INSTALL_AREA [Value]: [Type]:String    
[Name]::DEPRECIATION_TIME [Value]: [Type]:String    
[Name]::ANNUAL_SURVEY_DATE [Value]: [Type]:String    
[Name]::MANUFACTURER [Value]:美国ABI [Type]:String    
[Name]::DEALER [Value]:LabTide [Type]:String    
[Name]::REPAIR_COMPANY [Value]: [Type]:String    
[Name]::APPLY_STATE [Value]: [Type]:String    
[Name]::CERTIFICATE_STATE [Value]: [Type]:String    
[Name]::ACCEPT_REPORT_STATE [Value]: [Type]:String    
[Name]::EQUIPMENT_GRAPH_STATE [Value]: [Type]:String    
[Name]::MANUAL_STATE [Value]: [Type]:String    
[Name]::REPAIR_PERSON [Value]: [Type]:String    
[Name]::REPAIR_PERSON_STATE [Value]: [Type]:String    
[Name]::CONTACT_PHONE [Value]: [Type]:String    
[Name]::REGISTER_PERSON [Value]: [Type]:String    
[Name]::REGISTER_TIME [Value]: [Type]:DateTime    
[Name]::REGISTRATION_NUM [Value]: [Type]:String    
[Name]::REGISTRATION_ENUM [Value]: [Type]:String    
[Name]::EQUIPMENT_STATE [Value]:1 [Type]:String    
[Name]::FIRST_RPERSON [Value]:fr_李影 [Type]:String    
[Name]::FIRST_RTIME [Value]:2023/11/14 19:06:13 [Type]:DateTime    
[Name]::LAST_MPERSON [Value]:fr_李影 [Type]:String    
[Name]::LAST_MTIME [Value]:2024/8/17 14:39:43 [Type]:DateTime    
[Name]::REMARK [Value]: [Type]:String    
[Name]::MANUFACTURER_ID [Value]:1784488170637975568 [Type]:String    
[Name]::DEALER_ID [Value]:c6 [Type]:String    
[Name]::EQUIPMENT_SIZE [Value]: [Type]:String    
[Name]::EQUIPMENT_POWER [Value]: [Type]:String    
[Name]::EQUIPMENT_VOLTAGE [Value]: [Type]:String    
[Name]::EQUIPMENT_TEMP [Value]: [Type]:String    
[Name]::EQUIPMENT_TEMP_RANGE [Value]: [Type]:String    
[Name]::EQ_IN_PERSON [Value]: [Type]:String    
[Name]::EQ_IN_TIME [Value]:2024/2/21 9:12:37 [Type]:DateTime    
[Name]::EQ_OUT_PERSON [Value]: [Type]:String    
[Name]::EQ_OUT_TIME [Value]: [Type]:DateTime    
[Name]::EQ_SCRAP_PERSON [Value]:gz1_广测重 [Type]:String    
[Name]::EQ_SCRAP_TIME [Value]:2024/7/19 19:56:47 [Type]:DateTime    
[Name]::SERIAL_NUMBER [Value]: [Type]:String    
[Name]::EQUIPMENT_CODE [Value]:测试12 [Type]:String    
[Name]::DEALER_ENAME [Value]: [Type]:String    
[Name]::MANUFACTURER_ENAME [Value]: [Type]:String    
[Name]::EQUIPMENT_TYPE [Value]: [Type]:String    
[Name]::ENABLE_TIME [Value]:2023/11/14 15:46:26 [Type]:DateTime    
[Name]::IS_HIDE [Value]: [Type]:String    
[Name]::EQ_SERVICE_LIFE [Value]: [Type]:String    
[Name]::SMBL_FLAG [Value]:0 [Type]:String    
[Name]::SMBL_LAB_ID [Value]: [Type]:String    
[Name]::PROVIDER_ID [Value]: [Type]:String    
[Name]::PROVIDER [Value]: [Type]:String    
[Name]::EQUIPMENT_UCODE [Value]:测试12_测试1 [Type]:String    
[Name]::SMBL_CLASS [Value]: [Type]:String    
[Name]::SMBL_STATE [Value]: [Type]:String    
[Name]::COUNTRY_ORIGIN [Value]:1 [Type]:Int64    
[Name]::POSITION_ID [Value]: [Type]:String    
[Name]::EQUIPMENT_JSON [Value]: [Type]:String    

2025-07-03 12:57:25.719 +08:00 [INF] HTTP POST /api/CodeCustom/GetEquipmentUCodePreview/3 responded 200 in 3022.1601 ms
2025-07-03 12:57:25.720 +08:00 [INF] 【接口超时阀值预警】 [f40ba152c2ade8e8cb09a7e0adf60676]接口/api/CodeCustom/GetEquipmentUCodePreview/3,耗时:[3022]毫秒
2025-07-03 12:57:29.876 +08:00 [INF] 【SQL执行耗时:351.6857ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_CLASS" = :EQUIPMENT_CLASS0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_CLASS0 [Value]:0 [Type]:String    

2025-07-03 12:57:30.303 +08:00 [INF] 【SQL执行耗时:352.4657ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 12:57:30.749 +08:00 [INF] 【SQL执行耗时:373.9941ms】

[Sql]:SELECT "HOSPITAL_ID","HOSPITAL_LOGIN","HOSPITAL_CNAME","HOSPITAL_SNAME","HOSPITAL_TYPE","HOSPITAL_LEVEL","LEVEL1_CODE","LEVEL2_CODE","LEVEL3_CODE","HOSPITAL_BARCODE","HOSPITAL_PROPERTY","HOSPITAL_PERSON","HOSPITAL_ADDRESS","HOSPITAL_TEL","HOSPITAL_STATE","FIRST_PERSON","FIRST_RTIME","LAST_MPRESON","LAST_MTIME","REMARK","HOSPITAL_BCODE","HOSPITAL_RANGE","BARCODE_TYPE","BARCODE_RANGE","CHARGE_PROVIDE_TYPE","DATADB_IP","WECHAT_NAME","LAST_MPERSON","FIRST_RPERSON","UPDATE_FLAG","INSERFACE_SERVICE","SAMELEVEL_HOSPITAL","HOSPITAL_CLASS","HOSPITAL_URL","REGISTER_CODE","PACK_BAR_PREFIX","IF_ITEM_MANAGE","IF_TEMPLATE_HOS","MEDICAL_CONGENER","MEDICAL_CONGENER_ROLE","VEST_IN_DC","INSTANCE_ID","HOSPITAL_ATTRIBUTE","DATASYNC_TYPE","HGROUP_ID","CITEM_MANAGE" FROM "XH_SYS"."SYS6_HOSPITAL_INFO" 

2025-07-03 12:57:31.197 +08:00 [INF] 【SQL执行耗时:370.5858ms】

[Sql]:SELECT "LAB_ID","FIRST_RPERSON","LAB_NAME","LAB_ADDRESS","LAB_CODE","LAST_MTIME","LAB_SORT","LAB_RNAME","LAB_CLASS","LAB_PERSON","LINKMAN_TEL","REGISTER_CODE","LAB_LINKMAN","STATE_FLAG","REMARK","LAST_MPERSON","FIRST_RTIME","SPELL_CODE","LAB_TEL","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_LAB" 

2025-07-03 12:57:31.616 +08:00 [INF] 【SQL执行耗时:338.6268ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-03 12:57:32.027 +08:00 [INF] 【SQL执行耗时:341.6608ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP" 

2025-07-03 12:57:32.439 +08:00 [INF] 【SQL执行耗时:342.6009ms】

[Sql]:SELECT "AREA_ID","AREA_CODE","AREA_SORT","AREA_NAME","AREA_CLASS","AREA_PERSON","AREA_ADDRESS","AREA_TEL","SPELL_CODE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","STATE_FLAG","REMARK","HOSPITAL_ID","TAG_SIGN","AREA_ANAME","PACK_BAR_PREFIX" FROM "XH_SYS"."SYS6_INSPECTION_AREA" 

2025-07-03 12:57:32.878 +08:00 [INF] 【SQL执行耗时:365.1958ms】

[Sql]:UPDATE "XH_OA"."EMS_EQUIPMENT_INFO"  SET
           "UNIT_ID"=:UNIT_ID,"HOSPITAL_ID"=:HOSPITAL_ID,"LAB_ID"=:LAB_ID,"PROFESSIONAL_CLASS"=:PROFESSIONAL_CLASS,"INSTRUMENT_ID"=:INSTRUMENT_ID,"ESERIES_ID"=:ESERIES_ID,"EQUIPMENT_NUM"=:EQUIPMENT_NUM,"EQUIPMENT_NAME"=:EQUIPMENT_NAME,"DEPT_SECTION_NO"=:DEPT_SECTION_NO,"EQUIPMENT_ENAME"=:EQUIPMENT_ENAME,"EQUIPMENT_MODEL"=:EQUIPMENT_MODEL,"VEST_PIPELINE"=:VEST_PIPELINE,"EQUIPMENT_CLASS"=:EQUIPMENT_CLASS,"DEPT_NAME"=:DEPT_NAME,"SECTION_NO"=:SECTION_NO,"EQUIPMENT_SORT"=:EQUIPMENT_SORT,"FACTORY_NUM"=:FACTORY_NUM,"EQUIPMENT_FEATURE"=:EQUIPMENT_FEATURE,"BUY_DATE"=:BUY_DATE,"SELL_PRICE"=:SELL_PRICE,"KEEP_PERSON"=:KEEP_PERSON,"INSTALL_DATE"=:INSTALL_DATE,"INSTALL_AREA"=:INSTALL_AREA,"DEPRECIATION_TIME"=:DEPRECIATION_TIME,"ANNUAL_SURVEY_DATE"=:ANNUAL_SURVEY_DATE,"MANUFACTURER"=:MANUFACTURER,"DEALER"=:DEALER,"REPAIR_COMPANY"=:REPAIR_COMPANY,"APPLY_STATE"=:APPLY_STATE,"CERTIFICATE_STATE"=:CERTIFICATE_STATE,"ACCEPT_REPORT_STATE"=:ACCEPT_REPORT_STATE,"EQUIPMENT_GRAPH_STATE"=:EQUIPMENT_GRAPH_STATE,"MANUAL_STATE"=:MANUAL_STATE,"REPAIR_PERSON"=:REPAIR_PERSON,"REPAIR_PERSON_STATE"=:REPAIR_PERSON_STATE,"CONTACT_PHONE"=:CONTACT_PHONE,"REGISTER_PERSON"=:REGISTER_PERSON,"REGISTER_TIME"=:REGISTER_TIME,"REGISTRATION_NUM"=:REGISTRATION_NUM,"REGISTRATION_ENUM"=:REGISTRATION_ENUM,"EQUIPMENT_STATE"=:EQUIPMENT_STATE,"FIRST_RPERSON"=:FIRST_RPERSON,"FIRST_RTIME"=:FIRST_RTIME,"LAST_MPERSON"=:LAST_MPERSON,"LAST_MTIME"=:LAST_MTIME,"REMARK"=:REMARK,"MANUFACTURER_ID"=:MANUFACTURER_ID,"DEALER_ID"=:DEALER_ID,"EQUIPMENT_SIZE"=:EQUIPMENT_SIZE,"EQUIPMENT_POWER"=:EQUIPMENT_POWER,"EQUIPMENT_VOLTAGE"=:EQUIPMENT_VOLTAGE,"EQUIPMENT_TEMP"=:EQUIPMENT_TEMP,"EQUIPMENT_TEMP_RANGE"=:EQUIPMENT_TEMP_RANGE,"EQ_IN_PERSON"=:EQ_IN_PERSON,"EQ_IN_TIME"=:EQ_IN_TIME,"EQ_OUT_PERSON"=:EQ_OUT_PERSON,"EQ_OUT_TIME"=:EQ_OUT_TIME,"EQ_SCRAP_PERSON"=:EQ_SCRAP_PERSON,"EQ_SCRAP_TIME"=:EQ_SCRAP_TIME,"SERIAL_NUMBER"=:SERIAL_NUMBER,"EQUIPMENT_CODE"=:EQUIPMENT_CODE,"DEALER_ENAME"=:DEALER_ENAME,"MANUFACTURER_ENAME"=:MANUFACTURER_ENAME,"EQUIPMENT_TYPE"=:EQUIPMENT_TYPE,"ENABLE_TIME"=:ENABLE_TIME,"IS_HIDE"=:IS_HIDE,"EQ_SERVICE_LIFE"=:EQ_SERVICE_LIFE,"SMBL_FLAG"=:SMBL_FLAG,"SMBL_LAB_ID"=:SMBL_LAB_ID,"PROVIDER_ID"=:PROVIDER_ID,"PROVIDER"=:PROVIDER,"EQUIPMENT_UCODE"=:EQUIPMENT_UCODE,"SMBL_CLASS"=:SMBL_CLASS,"SMBL_STATE"=:SMBL_STATE,"COUNTRY_ORIGIN"=:COUNTRY_ORIGIN,"POSITION_ID"=:POSITION_ID,"EQUIPMENT_JSON"=:EQUIPMENT_JSON  WHERE "EQUIPMENT_ID"=:EQUIPMENT_ID 
[Pars]:
[Name]::EQUIPMENT_ID [Value]:1721769948730232832 [Type]:String    
[Name]::UNIT_ID [Value]:PG001 [Type]:String    
[Name]::HOSPITAL_ID [Value]:33A001 [Type]:String    
[Name]::LAB_ID [Value]:L001 [Type]:String    
[Name]::PROFESSIONAL_CLASS [Value]: [Type]:String    
[Name]::INSTRUMENT_ID [Value]:27 [Type]:String    
[Name]::ESERIES_ID [Value]: [Type]:String    
[Name]::EQUIPMENT_NUM [Value]:双开门冷藏箱-15 [Type]:String    
[Name]::EQUIPMENT_NAME [Value]:双开门冷藏箱-15 [Type]:String    
[Name]::DEPT_SECTION_NO [Value]: [Type]:String    
[Name]::EQUIPMENT_ENAME [Value]: [Type]:String    
[Name]::EQUIPMENT_MODEL [Value]:SINSTRUMENT_ID [Type]:String    
[Name]::VEST_PIPELINE [Value]: [Type]:String    
[Name]::EQUIPMENT_CLASS [Value]:1 [Type]:String    
[Name]::DEPT_NAME [Value]: [Type]:String    
[Name]::SECTION_NO [Value]: [Type]:String    
[Name]::EQUIPMENT_SORT [Value]: [Type]:String    
[Name]::FACTORY_NUM [Value]: [Type]:String    
[Name]::EQUIPMENT_FEATURE [Value]: [Type]:String    
[Name]::BUY_DATE [Value]: [Type]:String    
[Name]::SELL_PRICE [Value]: [Type]:String    
[Name]::KEEP_PERSON [Value]: [Type]:String    
[Name]::INSTALL_DATE [Value]: [Type]:String    
[Name]::INSTALL_AREA [Value]: [Type]:String    
[Name]::DEPRECIATION_TIME [Value]: [Type]:String    
[Name]::ANNUAL_SURVEY_DATE [Value]: [Type]:String    
[Name]::MANUFACTURER [Value]: [Type]:String    
[Name]::DEALER [Value]: [Type]:String    
[Name]::REPAIR_COMPANY [Value]: [Type]:String    
[Name]::APPLY_STATE [Value]: [Type]:String    
[Name]::CERTIFICATE_STATE [Value]: [Type]:String    
[Name]::ACCEPT_REPORT_STATE [Value]: [Type]:String    
[Name]::EQUIPMENT_GRAPH_STATE [Value]: [Type]:String    
[Name]::MANUAL_STATE [Value]: [Type]:String    
[Name]::REPAIR_PERSON [Value]: [Type]:String    
[Name]::REPAIR_PERSON_STATE [Value]: [Type]:String    
[Name]::CONTACT_PHONE [Value]: [Type]:String    
[Name]::REGISTER_PERSON [Value]: [Type]:String    
[Name]::REGISTER_TIME [Value]: [Type]:DateTime    
[Name]::REGISTRATION_NUM [Value]: [Type]:String    
[Name]::REGISTRATION_ENUM [Value]: [Type]:String    
[Name]::EQUIPMENT_STATE [Value]:3 [Type]:String    
[Name]::FIRST_RPERSON [Value]:检测仪器同步数据 [Type]:String    
[Name]::FIRST_RTIME [Value]:2023/11/7 14:01:59 [Type]:DateTime    
[Name]::LAST_MPERSON [Value]:gz1_广测重 [Type]:String    
[Name]::LAST_MTIME [Value]:2025/2/22 17:51:38 [Type]:DateTime    
[Name]::REMARK [Value]: [Type]:String    
[Name]::MANUFACTURER_ID [Value]:M105 [Type]:String    
[Name]::DEALER_ID [Value]: [Type]:String    
[Name]::EQUIPMENT_SIZE [Value]: [Type]:String    
[Name]::EQUIPMENT_POWER [Value]: [Type]:String    
[Name]::EQUIPMENT_VOLTAGE [Value]: [Type]:String    
[Name]::EQUIPMENT_TEMP [Value]: [Type]:String    
[Name]::EQUIPMENT_TEMP_RANGE [Value]: [Type]:String    
[Name]::EQ_IN_PERSON [Value]: [Type]:String    
[Name]::EQ_IN_TIME [Value]: [Type]:DateTime    
[Name]::EQ_OUT_PERSON [Value]: [Type]:String    
[Name]::EQ_OUT_TIME [Value]: [Type]:DateTime    
[Name]::EQ_SCRAP_PERSON [Value]:gz1_广测重 [Type]:String    
[Name]::EQ_SCRAP_TIME [Value]:2024/8/1 18:56:07 [Type]:DateTime    
[Name]::SERIAL_NUMBER [Value]: [Type]:String    
[Name]::EQUIPMENT_CODE [Value]:双开门冷藏箱-15 [Type]:String    
[Name]::DEALER_ENAME [Value]: [Type]:String    
[Name]::MANUFACTURER_ENAME [Value]: [Type]:String    
[Name]::EQUIPMENT_TYPE [Value]: [Type]:String    
[Name]::ENABLE_TIME [Value]: [Type]:DateTime    
[Name]::IS_HIDE [Value]: [Type]:String    
[Name]::EQ_SERVICE_LIFE [Value]: [Type]:String    
[Name]::SMBL_FLAG [Value]:1 [Type]:String    
[Name]::SMBL_LAB_ID [Value]:33A001L0007 [Type]:String    
[Name]::PROVIDER_ID [Value]: [Type]:String    
[Name]::PROVIDER [Value]: [Type]:String    
[Name]::EQUIPMENT_UCODE [Value]:双开门冷藏箱-15_双开门冷藏箱-15 [Type]:String    
[Name]::SMBL_CLASS [Value]:1 [Type]:String    
[Name]::SMBL_STATE [Value]: [Type]:String    
[Name]::COUNTRY_ORIGIN [Value]:1 [Type]:Int64    
[Name]::POSITION_ID [Value]: [Type]:String    
[Name]::EQUIPMENT_JSON [Value]: [Type]:String    

2025-07-03 12:57:32.918 +08:00 [INF] HTTP POST /api/CodeCustom/GetEquipmentUCodePreview/0 responded 200 in 3432.4000 ms
2025-07-03 12:57:32.919 +08:00 [INF] 【接口超时阀值预警】 [c3d4cb22c9dfe12618995893257be3c8]接口/api/CodeCustom/GetEquipmentUCodePreview/0,耗时:[3432]毫秒
2025-07-03 12:57:52.270 +08:00 [INF] 【SQL执行耗时:338.4042ms】

[Sql]:SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" <> :EqpNoState0 ) 
[Pars]:
[Name]::EqpNoState0 [Value]:2 [Type]:String    

2025-07-03 12:57:52.732 +08:00 [INF] 【SQL执行耗时:331.005ms】

[Sql]:SELECT "DATA_ID","HOSPITAL_ID","LAB_ID","CLASS_ID","DATA_SORT","DATA_CNAME","DATA_ENAME","HIS_ID","CUSTOM_CODE","SPELL_CODE","DATA_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","DATA_SNAME","DATA_SOURCE","ONE_CLASS","DATA_UNAME","IF_REPEAT","SYSTEM_ID" FROM "XH_SYS"."SYS6_BASE_DATA"  WHERE (( "CLASS_ID" = :CLASS_ID0 ) AND ( "DATA_STATE" = :DATA_STATE1 )) 
[Pars]:
[Name]::CLASS_ID0 [Value]:专业分类 [Type]:String    
[Name]::DATA_STATE1 [Value]:1 [Type]:String    

2025-07-03 12:57:53.107 +08:00 [INF] 【SQL执行耗时:336.1023ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_CLASS" = :EQUIPMENT_CLASS0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_CLASS0 [Value]:0 [Type]:String    

2025-07-03 12:57:53.525 +08:00 [INF] 【SQL执行耗时:347.0851ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 12:57:53.949 +08:00 [INF] 【SQL执行耗时:353.5795ms】

[Sql]:SELECT "HOSPITAL_ID","HOSPITAL_LOGIN","HOSPITAL_CNAME","HOSPITAL_SNAME","HOSPITAL_TYPE","HOSPITAL_LEVEL","LEVEL1_CODE","LEVEL2_CODE","LEVEL3_CODE","HOSPITAL_BARCODE","HOSPITAL_PROPERTY","HOSPITAL_PERSON","HOSPITAL_ADDRESS","HOSPITAL_TEL","HOSPITAL_STATE","FIRST_PERSON","FIRST_RTIME","LAST_MPRESON","LAST_MTIME","REMARK","HOSPITAL_BCODE","HOSPITAL_RANGE","BARCODE_TYPE","BARCODE_RANGE","CHARGE_PROVIDE_TYPE","DATADB_IP","WECHAT_NAME","LAST_MPERSON","FIRST_RPERSON","UPDATE_FLAG","INSERFACE_SERVICE","SAMELEVEL_HOSPITAL","HOSPITAL_CLASS","HOSPITAL_URL","REGISTER_CODE","PACK_BAR_PREFIX","IF_ITEM_MANAGE","IF_TEMPLATE_HOS","MEDICAL_CONGENER","MEDICAL_CONGENER_ROLE","VEST_IN_DC","INSTANCE_ID","HOSPITAL_ATTRIBUTE","DATASYNC_TYPE","HGROUP_ID","CITEM_MANAGE" FROM "XH_SYS"."SYS6_HOSPITAL_INFO" 

2025-07-03 12:57:54.366 +08:00 [INF] 【SQL执行耗时:344.3877ms】

[Sql]:SELECT "LAB_ID","FIRST_RPERSON","LAB_NAME","LAB_ADDRESS","LAB_CODE","LAST_MTIME","LAB_SORT","LAB_RNAME","LAB_CLASS","LAB_PERSON","LINKMAN_TEL","REGISTER_CODE","LAB_LINKMAN","STATE_FLAG","REMARK","LAST_MPERSON","FIRST_RTIME","SPELL_CODE","LAB_TEL","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_LAB" 

2025-07-03 12:57:54.813 +08:00 [INF] 【SQL执行耗时:376.3969ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-03 12:57:55.262 +08:00 [INF] 【SQL执行耗时:370.7894ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP" 

2025-07-03 12:57:55.713 +08:00 [INF] 【SQL执行耗时:375.224ms】

[Sql]:SELECT "AREA_ID","AREA_CODE","AREA_SORT","AREA_NAME","AREA_CLASS","AREA_PERSON","AREA_ADDRESS","AREA_TEL","SPELL_CODE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","STATE_FLAG","REMARK","HOSPITAL_ID","TAG_SIGN","AREA_ANAME","PACK_BAR_PREFIX" FROM "XH_SYS"."SYS6_INSPECTION_AREA" 

2025-07-03 12:57:55.797 +08:00 [INF] HTTP GET /api/CodeCustom/GetEquipmentCodeCustomDict responded 200 in 3912.1432 ms
2025-07-03 12:57:55.797 +08:00 [INF] 【接口超时阀值预警】 [01cc611cc52ebcc5ba8696ebc4a35d14]接口/api/CodeCustom/GetEquipmentCodeCustomDict,耗时:[3912]毫秒
2025-07-03 13:00:24.062 +08:00 [INF] HTTP POST /api/CodeCustom/AddEquipmentCodeCustomDict responded 200 in 46.8286 ms
2025-07-03 13:00:35.311 +08:00 [INF] HTTP POST /api/CodeCustom/AddEquipmentCodeCustomDict responded 200 in 40.1692 ms
2025-07-03 13:00:54.443 +08:00 [INF] 【SQL执行耗时:541.2548ms】

[Sql]:INSERT INTO "XH_OA"."EMS_EQPNO_FORMAT_DICT"  
           ("EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK")
     VALUES
           (:EQP_NO_ID,:HOSPITAL_ID,:EQP_NO_NAME,:EQP_NO_LEVEL,:EQP_NO_CLASS,:EQP_DISPLAY_JSON,:EQP_NO_APPLYS,:EQP_NO_STATE,:FIRST_RPERSON,:FIRST_RTIME,:LAST_MPERSON,:LAST_MTIME,:REMARK)  
[Pars]:
[Name]::EQP_NO_ID [Value]:EFAE5C71F41C4DE99E2E97FCC3C60B54 [Type]:String    
[Name]::HOSPITAL_ID [Value]:33A001 [Type]:String    
[Name]::EQP_NO_NAME [Value]:测试2 [Type]:String    
[Name]::EQP_NO_LEVEL [Value]:0 [Type]:String    
[Name]::EQP_NO_CLASS [Value]:0 [Type]:String    
[Name]::EQP_DISPLAY_JSON [Value]:{"DisplayContentString":"{机构}_{院区}_{设备编号}_{设备名称}","DisplayContentCode":"{HOSPITAL_NAME}_{AREA_NAME}_{EQUIPMENT_CODE}_{EQUIPMENT_NAME}","FixedFieldDisplays":[{"Key":"{HOSPITAL_NAME}","Value":"A"},{"Key":"{AREA_NAME}","Value":"B"},{"Key":"{LAB_NAME}","Value":""},{"Key":"{MGROUP_NAME}","Value":""},{"Key":"{UNIT_NAME}","Value":""},{"Key":"{SMBL_LAB_NAME}","Value":""}]} [Type]:String    
[Name]::EQP_NO_APPLYS [Value]:PG001 [Type]:String    
[Name]::EQP_NO_STATE [Value]:1 [Type]:String    
[Name]::FIRST_RPERSON [Value]:gz1_广测重 [Type]:String    
[Name]::FIRST_RTIME [Value]:2025/7/3 13:00:53 [Type]:DateTime    
[Name]::LAST_MPERSON [Value]:gz1_广测重 [Type]:String    
[Name]::LAST_MTIME [Value]:2025/7/3 13:00:53 [Type]:DateTime    
[Name]::REMARK [Value]: [Type]:String    

2025-07-03 13:00:54.482 +08:00 [INF] HTTP POST /api/CodeCustom/AddEquipmentCodeCustomDict responded 200 in 645.4766 ms
2025-07-03 13:00:54.482 +08:00 [INF] 【接口超时阀值预警】 [086c2a8bda91fdda4a08792566b3efb1]接口/api/CodeCustom/AddEquipmentCodeCustomDict,耗时:[645]毫秒
2025-07-03 13:03:07.423 +08:00 [INF] 【SQL执行耗时:367.89ms】

[Sql]:INSERT INTO "XH_OA"."EMS_EQPNO_FORMAT_DICT"  
           ("EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK")
     VALUES
           (:EQP_NO_ID,:HOSPITAL_ID,:EQP_NO_NAME,:EQP_NO_LEVEL,:EQP_NO_CLASS,:EQP_DISPLAY_JSON,:EQP_NO_APPLYS,:EQP_NO_STATE,:FIRST_RPERSON,:FIRST_RTIME,:LAST_MPERSON,:LAST_MTIME,:REMARK)  
[Pars]:
[Name]::EQP_NO_ID [Value]:4F2351A29D444EA3BD2202FCFA992BD1 [Type]:String    
[Name]::HOSPITAL_ID [Value]:33A001 [Type]:String    
[Name]::EQP_NO_NAME [Value]:测试2 [Type]:String    
[Name]::EQP_NO_LEVEL [Value]:0 [Type]:String    
[Name]::EQP_NO_CLASS [Value]:0 [Type]:String    
[Name]::EQP_DISPLAY_JSON [Value]:{"DisplayContentString":"{机构}_{院区}_{设备编号}_{设备名称}","DisplayContentCode":"{HOSPITAL_NAME}_{AREA_NAME}_{EQUIPMENT_CODE}_{EQUIPMENT_NAME}","FixedFieldDisplays":[{"Key":"{HOSPITAL_NAME}","Value":"A"},{"Key":"{AREA_NAME}","Value":"B"},{"Key":"{LAB_NAME}","Value":""},{"Key":"{MGROUP_NAME}","Value":""},{"Key":"{UNIT_NAME}","Value":""},{"Key":"{SMBL_LAB_NAME}","Value":""}]} [Type]:String    
[Name]::EQP_NO_APPLYS [Value]:PG001 [Type]:String    
[Name]::EQP_NO_STATE [Value]:1 [Type]:String    
[Name]::FIRST_RPERSON [Value]:gz1_广测重 [Type]:String    
[Name]::FIRST_RTIME [Value]:2025/7/3 13:03:00 [Type]:DateTime    
[Name]::LAST_MPERSON [Value]:gz1_广测重 [Type]:String    
[Name]::LAST_MTIME [Value]:2025/7/3 13:03:02 [Type]:DateTime    
[Name]::REMARK [Value]: [Type]:String    

2025-07-03 13:03:25.408 +08:00 [INF] HTTP POST /api/CodeCustom/AddEquipmentCodeCustomDict responded 200 in 76713.5439 ms
2025-07-03 13:03:25.409 +08:00 [INF] 【接口超时阀值预警】 [53356592dd44eb365edbcd5a44441048]接口/api/CodeCustom/AddEquipmentCodeCustomDict,耗时:[76713]毫秒
2025-07-03 13:05:13.149 +08:00 [INF] ==>App Start..2025-07-03 13:05:13
2025-07-03 13:05:13.311 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-03 13:05:13.314 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-03 13:05:18.742 +08:00 [INF] ==>基础连接请求完成.
2025-07-03 13:05:19.106 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-03 13:05:19.438 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-03 13:05:19.737 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-03 13:05:19.740 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-03 13:05:20.066 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-03 13:05:20.478 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-03 13:05:20.581 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-03 13:05:21.023 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-03 13:05:21.024 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-03 13:05:21.889 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-03 13:05:24.091 +08:00 [INF] ==>初始化完成..
2025-07-03 13:05:24.111 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-03 13:05:24.114 +08:00 [INF] 设备启用任务
2025-07-03 13:05:24.114 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-03 13:05:24.548 +08:00 [INF] 【SQL执行耗时:412.2895ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-03 13:05:24.687 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-03 13:05:24.701 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-03 13:05:24.703 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-03 13:05:24.703 +08:00 [INF] Hosting environment: Development
2025-07-03 13:05:24.704 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-03 13:05:30.999 +08:00 [INF] 【SQL执行耗时:357.5401ms】

[Sql]:INSERT INTO "XH_OA"."EMS_EQPNO_FORMAT_DICT"  
           ("EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK")
     VALUES
           (:EQP_NO_ID,:HOSPITAL_ID,:EQP_NO_NAME,:EQP_NO_LEVEL,:EQP_NO_CLASS,:EQP_DISPLAY_JSON,:EQP_NO_APPLYS,:EQP_NO_STATE,:FIRST_RPERSON,:FIRST_RTIME,:LAST_MPERSON,:LAST_MTIME,:REMARK)  
[Pars]:
[Name]::EQP_NO_ID [Value]:CA923FF8810741078947C55F2E71A372 [Type]:String    
[Name]::HOSPITAL_ID [Value]:33A001 [Type]:String    
[Name]::EQP_NO_NAME [Value]:测试32 [Type]:String    
[Name]::EQP_NO_LEVEL [Value]:0 [Type]:String    
[Name]::EQP_NO_CLASS [Value]:0 [Type]:String    
[Name]::EQP_DISPLAY_JSON [Value]:{"DisplayContentString":"{机构}_{院区}_{设备编号}_{设备名称}","DisplayContentCode":"{HOSPITAL_NAME}_{AREA_NAME}_{EQUIPMENT_CODE}_{EQUIPMENT_NAME}","FixedFieldDisplays":[{"Key":"{HOSPITAL_NAME}","Value":"A"},{"Key":"{AREA_NAME}","Value":"B"},{"Key":"{LAB_NAME}","Value":""},{"Key":"{MGROUP_NAME}","Value":""},{"Key":"{UNIT_NAME}","Value":""},{"Key":"{SMBL_LAB_NAME}","Value":""}]} [Type]:String    
[Name]::EQP_NO_APPLYS [Value]:PG001 [Type]:String    
[Name]::EQP_NO_STATE [Value]:1 [Type]:String    
[Name]::FIRST_RPERSON [Value]:gz1_广测重 [Type]:String    
[Name]::FIRST_RTIME [Value]:2025/7/3 13:05:30 [Type]:DateTime    
[Name]::LAST_MPERSON [Value]:gz1_广测重 [Type]:String    
[Name]::LAST_MTIME [Value]:2025/7/3 13:05:30 [Type]:DateTime    
[Name]::REMARK [Value]: [Type]:String    

2025-07-03 13:05:31.088 +08:00 [INF] HTTP POST /api/CodeCustom/AddEquipmentCodeCustomDict responded 200 in 6166.6284 ms
2025-07-03 13:05:31.090 +08:00 [INF] 【接口超时阀值预警】 [0211d01c38bcef55634ec1a3d89ae6f5]接口/api/CodeCustom/AddEquipmentCodeCustomDict,耗时:[6174]毫秒
2025-07-03 13:06:48.245 +08:00 [INF] 【SQL执行耗时:442.3086ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_CLASS" = :EQUIPMENT_CLASS0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_CLASS0 [Value]:0 [Type]:String    

2025-07-03 13:06:48.665 +08:00 [INF] 【SQL执行耗时:341.4713ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 13:06:49.163 +08:00 [INF] 【SQL执行耗时:423.5812ms】

[Sql]:SELECT "HOSPITAL_ID","HOSPITAL_LOGIN","HOSPITAL_CNAME","HOSPITAL_SNAME","HOSPITAL_TYPE","HOSPITAL_LEVEL","LEVEL1_CODE","LEVEL2_CODE","LEVEL3_CODE","HOSPITAL_BARCODE","HOSPITAL_PROPERTY","HOSPITAL_PERSON","HOSPITAL_ADDRESS","HOSPITAL_TEL","HOSPITAL_STATE","FIRST_PERSON","FIRST_RTIME","LAST_MPRESON","LAST_MTIME","REMARK","HOSPITAL_BCODE","HOSPITAL_RANGE","BARCODE_TYPE","BARCODE_RANGE","CHARGE_PROVIDE_TYPE","DATADB_IP","WECHAT_NAME","LAST_MPERSON","FIRST_RPERSON","UPDATE_FLAG","INSERFACE_SERVICE","SAMELEVEL_HOSPITAL","HOSPITAL_CLASS","HOSPITAL_URL","REGISTER_CODE","PACK_BAR_PREFIX","IF_ITEM_MANAGE","IF_TEMPLATE_HOS","MEDICAL_CONGENER","MEDICAL_CONGENER_ROLE","VEST_IN_DC","INSTANCE_ID","HOSPITAL_ATTRIBUTE","DATASYNC_TYPE","HGROUP_ID","CITEM_MANAGE" FROM "XH_SYS"."SYS6_HOSPITAL_INFO" 

2025-07-03 13:06:49.628 +08:00 [INF] 【SQL执行耗时:379.3202ms】

[Sql]:SELECT "LAB_ID","FIRST_RPERSON","LAB_NAME","LAB_ADDRESS","LAB_CODE","LAST_MTIME","LAB_SORT","LAB_RNAME","LAB_CLASS","LAB_PERSON","LINKMAN_TEL","REGISTER_CODE","LAB_LINKMAN","STATE_FLAG","REMARK","LAST_MPERSON","FIRST_RTIME","SPELL_CODE","LAB_TEL","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_LAB" 

2025-07-03 13:06:50.087 +08:00 [INF] 【SQL执行耗时:362.8815ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-03 13:06:50.508 +08:00 [INF] 【SQL执行耗时:340.8884ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP" 

2025-07-03 13:06:50.935 +08:00 [INF] 【SQL执行耗时:351.5336ms】

[Sql]:SELECT "AREA_ID","AREA_CODE","AREA_SORT","AREA_NAME","AREA_CLASS","AREA_PERSON","AREA_ADDRESS","AREA_TEL","SPELL_CODE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","STATE_FLAG","REMARK","HOSPITAL_ID","TAG_SIGN","AREA_ANAME","PACK_BAR_PREFIX" FROM "XH_SYS"."SYS6_INSPECTION_AREA" 

2025-07-03 13:06:51.414 +08:00 [INF] 【SQL执行耗时:364.5594ms】

[Sql]:UPDATE "XH_OA"."EMS_EQUIPMENT_INFO"  SET
           "UNIT_ID"=:UNIT_ID,"HOSPITAL_ID"=:HOSPITAL_ID,"LAB_ID"=:LAB_ID,"PROFESSIONAL_CLASS"=:PROFESSIONAL_CLASS,"INSTRUMENT_ID"=:INSTRUMENT_ID,"ESERIES_ID"=:ESERIES_ID,"EQUIPMENT_NUM"=:EQUIPMENT_NUM,"EQUIPMENT_NAME"=:EQUIPMENT_NAME,"DEPT_SECTION_NO"=:DEPT_SECTION_NO,"EQUIPMENT_ENAME"=:EQUIPMENT_ENAME,"EQUIPMENT_MODEL"=:EQUIPMENT_MODEL,"VEST_PIPELINE"=:VEST_PIPELINE,"EQUIPMENT_CLASS"=:EQUIPMENT_CLASS,"DEPT_NAME"=:DEPT_NAME,"SECTION_NO"=:SECTION_NO,"EQUIPMENT_SORT"=:EQUIPMENT_SORT,"FACTORY_NUM"=:FACTORY_NUM,"EQUIPMENT_FEATURE"=:EQUIPMENT_FEATURE,"BUY_DATE"=:BUY_DATE,"SELL_PRICE"=:SELL_PRICE,"KEEP_PERSON"=:KEEP_PERSON,"INSTALL_DATE"=:INSTALL_DATE,"INSTALL_AREA"=:INSTALL_AREA,"DEPRECIATION_TIME"=:DEPRECIATION_TIME,"ANNUAL_SURVEY_DATE"=:ANNUAL_SURVEY_DATE,"MANUFACTURER"=:MANUFACTURER,"DEALER"=:DEALER,"REPAIR_COMPANY"=:REPAIR_COMPANY,"APPLY_STATE"=:APPLY_STATE,"CERTIFICATE_STATE"=:CERTIFICATE_STATE,"ACCEPT_REPORT_STATE"=:ACCEPT_REPORT_STATE,"EQUIPMENT_GRAPH_STATE"=:EQUIPMENT_GRAPH_STATE,"MANUAL_STATE"=:MANUAL_STATE,"REPAIR_PERSON"=:REPAIR_PERSON,"REPAIR_PERSON_STATE"=:REPAIR_PERSON_STATE,"CONTACT_PHONE"=:CONTACT_PHONE,"REGISTER_PERSON"=:REGISTER_PERSON,"REGISTER_TIME"=:REGISTER_TIME,"REGISTRATION_NUM"=:REGISTRATION_NUM,"REGISTRATION_ENUM"=:REGISTRATION_ENUM,"EQUIPMENT_STATE"=:EQUIPMENT_STATE,"FIRST_RPERSON"=:FIRST_RPERSON,"FIRST_RTIME"=:FIRST_RTIME,"LAST_MPERSON"=:LAST_MPERSON,"LAST_MTIME"=:LAST_MTIME,"REMARK"=:REMARK,"MANUFACTURER_ID"=:MANUFACTURER_ID,"DEALER_ID"=:DEALER_ID,"EQUIPMENT_SIZE"=:EQUIPMENT_SIZE,"EQUIPMENT_POWER"=:EQUIPMENT_POWER,"EQUIPMENT_VOLTAGE"=:EQUIPMENT_VOLTAGE,"EQUIPMENT_TEMP"=:EQUIPMENT_TEMP,"EQUIPMENT_TEMP_RANGE"=:EQUIPMENT_TEMP_RANGE,"EQ_IN_PERSON"=:EQ_IN_PERSON,"EQ_IN_TIME"=:EQ_IN_TIME,"EQ_OUT_PERSON"=:EQ_OUT_PERSON,"EQ_OUT_TIME"=:EQ_OUT_TIME,"EQ_SCRAP_PERSON"=:EQ_SCRAP_PERSON,"EQ_SCRAP_TIME"=:EQ_SCRAP_TIME,"SERIAL_NUMBER"=:SERIAL_NUMBER,"EQUIPMENT_CODE"=:EQUIPMENT_CODE,"DEALER_ENAME"=:DEALER_ENAME,"MANUFACTURER_ENAME"=:MANUFACTURER_ENAME,"EQUIPMENT_TYPE"=:EQUIPMENT_TYPE,"ENABLE_TIME"=:ENABLE_TIME,"IS_HIDE"=:IS_HIDE,"EQ_SERVICE_LIFE"=:EQ_SERVICE_LIFE,"SMBL_FLAG"=:SMBL_FLAG,"SMBL_LAB_ID"=:SMBL_LAB_ID,"PROVIDER_ID"=:PROVIDER_ID,"PROVIDER"=:PROVIDER,"EQUIPMENT_UCODE"=:EQUIPMENT_UCODE,"SMBL_CLASS"=:SMBL_CLASS,"SMBL_STATE"=:SMBL_STATE,"COUNTRY_ORIGIN"=:COUNTRY_ORIGIN,"POSITION_ID"=:POSITION_ID,"EQUIPMENT_JSON"=:EQUIPMENT_JSON  WHERE "EQUIPMENT_ID"=:EQUIPMENT_ID 
[Pars]:
[Name]::EQUIPMENT_ID [Value]:1721769948730232832 [Type]:String    
[Name]::UNIT_ID [Value]:PG001 [Type]:String    
[Name]::HOSPITAL_ID [Value]:33A001 [Type]:String    
[Name]::LAB_ID [Value]:L001 [Type]:String    
[Name]::PROFESSIONAL_CLASS [Value]: [Type]:String    
[Name]::INSTRUMENT_ID [Value]:27 [Type]:String    
[Name]::ESERIES_ID [Value]: [Type]:String    
[Name]::EQUIPMENT_NUM [Value]:双开门冷藏箱-15 [Type]:String    
[Name]::EQUIPMENT_NAME [Value]:双开门冷藏箱-15 [Type]:String    
[Name]::DEPT_SECTION_NO [Value]: [Type]:String    
[Name]::EQUIPMENT_ENAME [Value]: [Type]:String    
[Name]::EQUIPMENT_MODEL [Value]:SINSTRUMENT_ID [Type]:String    
[Name]::VEST_PIPELINE [Value]: [Type]:String    
[Name]::EQUIPMENT_CLASS [Value]:1 [Type]:String    
[Name]::DEPT_NAME [Value]: [Type]:String    
[Name]::SECTION_NO [Value]: [Type]:String    
[Name]::EQUIPMENT_SORT [Value]: [Type]:String    
[Name]::FACTORY_NUM [Value]: [Type]:String    
[Name]::EQUIPMENT_FEATURE [Value]: [Type]:String    
[Name]::BUY_DATE [Value]: [Type]:String    
[Name]::SELL_PRICE [Value]: [Type]:String    
[Name]::KEEP_PERSON [Value]: [Type]:String    
[Name]::INSTALL_DATE [Value]: [Type]:String    
[Name]::INSTALL_AREA [Value]: [Type]:String    
[Name]::DEPRECIATION_TIME [Value]: [Type]:String    
[Name]::ANNUAL_SURVEY_DATE [Value]: [Type]:String    
[Name]::MANUFACTURER [Value]: [Type]:String    
[Name]::DEALER [Value]: [Type]:String    
[Name]::REPAIR_COMPANY [Value]: [Type]:String    
[Name]::APPLY_STATE [Value]: [Type]:String    
[Name]::CERTIFICATE_STATE [Value]: [Type]:String    
[Name]::ACCEPT_REPORT_STATE [Value]: [Type]:String    
[Name]::EQUIPMENT_GRAPH_STATE [Value]: [Type]:String    
[Name]::MANUAL_STATE [Value]: [Type]:String    
[Name]::REPAIR_PERSON [Value]: [Type]:String    
[Name]::REPAIR_PERSON_STATE [Value]: [Type]:String    
[Name]::CONTACT_PHONE [Value]: [Type]:String    
[Name]::REGISTER_PERSON [Value]: [Type]:String    
[Name]::REGISTER_TIME [Value]: [Type]:DateTime    
[Name]::REGISTRATION_NUM [Value]: [Type]:String    
[Name]::REGISTRATION_ENUM [Value]: [Type]:String    
[Name]::EQUIPMENT_STATE [Value]:3 [Type]:String    
[Name]::FIRST_RPERSON [Value]:检测仪器同步数据 [Type]:String    
[Name]::FIRST_RTIME [Value]:2023/11/7 14:01:59 [Type]:DateTime    
[Name]::LAST_MPERSON [Value]:gz1_广测重 [Type]:String    
[Name]::LAST_MTIME [Value]:2025/2/22 17:51:38 [Type]:DateTime    
[Name]::REMARK [Value]: [Type]:String    
[Name]::MANUFACTURER_ID [Value]:M105 [Type]:String    
[Name]::DEALER_ID [Value]: [Type]:String    
[Name]::EQUIPMENT_SIZE [Value]: [Type]:String    
[Name]::EQUIPMENT_POWER [Value]: [Type]:String    
[Name]::EQUIPMENT_VOLTAGE [Value]: [Type]:String    
[Name]::EQUIPMENT_TEMP [Value]: [Type]:String    
[Name]::EQUIPMENT_TEMP_RANGE [Value]: [Type]:String    
[Name]::EQ_IN_PERSON [Value]: [Type]:String    
[Name]::EQ_IN_TIME [Value]: [Type]:DateTime    
[Name]::EQ_OUT_PERSON [Value]: [Type]:String    
[Name]::EQ_OUT_TIME [Value]: [Type]:DateTime    
[Name]::EQ_SCRAP_PERSON [Value]:gz1_广测重 [Type]:String    
[Name]::EQ_SCRAP_TIME [Value]:2024/8/1 18:56:07 [Type]:DateTime    
[Name]::SERIAL_NUMBER [Value]: [Type]:String    
[Name]::EQUIPMENT_CODE [Value]:双开门冷藏箱-15 [Type]:String    
[Name]::DEALER_ENAME [Value]: [Type]:String    
[Name]::MANUFACTURER_ENAME [Value]: [Type]:String    
[Name]::EQUIPMENT_TYPE [Value]: [Type]:String    
[Name]::ENABLE_TIME [Value]: [Type]:DateTime    
[Name]::IS_HIDE [Value]: [Type]:String    
[Name]::EQ_SERVICE_LIFE [Value]: [Type]:String    
[Name]::SMBL_FLAG [Value]:1 [Type]:String    
[Name]::SMBL_LAB_ID [Value]:33A001L0007 [Type]:String    
[Name]::PROVIDER_ID [Value]: [Type]:String    
[Name]::PROVIDER [Value]: [Type]:String    
[Name]::EQUIPMENT_UCODE [Value]:A_B_双开门冷藏箱-15_双开门冷藏箱-15 [Type]:String    
[Name]::SMBL_CLASS [Value]:1 [Type]:String    
[Name]::SMBL_STATE [Value]: [Type]:String    
[Name]::COUNTRY_ORIGIN [Value]:1 [Type]:Int64    
[Name]::POSITION_ID [Value]: [Type]:String    
[Name]::EQUIPMENT_JSON [Value]: [Type]:String    

2025-07-03 13:06:51.457 +08:00 [INF] HTTP POST /api/CodeCustom/GetEquipmentUCodePreview/0 responded 200 in 3716.6902 ms
2025-07-03 13:06:51.458 +08:00 [INF] 【接口超时阀值预警】 [5d61a5d66bbb5928b9a17fa1cee5c804]接口/api/CodeCustom/GetEquipmentUCodePreview/0,耗时:[3717]毫秒
2025-07-03 13:07:11.239 +08:00 [INF] HTTP GET /api/CodeCustom/GetCustomDictCode responded 200 in 46.2575 ms
2025-07-03 13:07:17.179 +08:00 [INF] 【SQL执行耗时:352.4857ms】

[Sql]:SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" <> :EqpNoState0 ) 
[Pars]:
[Name]::EqpNoState0 [Value]:2 [Type]:String    

2025-07-03 13:07:17.773 +08:00 [INF] 【SQL执行耗时:353.1756ms】

[Sql]:SELECT "DATA_ID","HOSPITAL_ID","LAB_ID","CLASS_ID","DATA_SORT","DATA_CNAME","DATA_ENAME","HIS_ID","CUSTOM_CODE","SPELL_CODE","DATA_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","DATA_SNAME","DATA_SOURCE","ONE_CLASS","DATA_UNAME","IF_REPEAT","SYSTEM_ID" FROM "XH_SYS"."SYS6_BASE_DATA"  WHERE (( "CLASS_ID" = :CLASS_ID0 ) AND ( "DATA_STATE" = :DATA_STATE1 )) 
[Pars]:
[Name]::CLASS_ID0 [Value]:专业分类 [Type]:String    
[Name]::DATA_STATE1 [Value]:1 [Type]:String    

2025-07-03 13:07:18.181 +08:00 [INF] 【SQL执行耗时:366.5659ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_CLASS" = :EQUIPMENT_CLASS0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_CLASS0 [Value]:0 [Type]:String    

2025-07-03 13:07:18.635 +08:00 [INF] 【SQL执行耗时:373.707ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 13:07:19.055 +08:00 [INF] 【SQL执行耗时:345.3751ms】

[Sql]:SELECT "HOSPITAL_ID","HOSPITAL_LOGIN","HOSPITAL_CNAME","HOSPITAL_SNAME","HOSPITAL_TYPE","HOSPITAL_LEVEL","LEVEL1_CODE","LEVEL2_CODE","LEVEL3_CODE","HOSPITAL_BARCODE","HOSPITAL_PROPERTY","HOSPITAL_PERSON","HOSPITAL_ADDRESS","HOSPITAL_TEL","HOSPITAL_STATE","FIRST_PERSON","FIRST_RTIME","LAST_MPRESON","LAST_MTIME","REMARK","HOSPITAL_BCODE","HOSPITAL_RANGE","BARCODE_TYPE","BARCODE_RANGE","CHARGE_PROVIDE_TYPE","DATADB_IP","WECHAT_NAME","LAST_MPERSON","FIRST_RPERSON","UPDATE_FLAG","INSERFACE_SERVICE","SAMELEVEL_HOSPITAL","HOSPITAL_CLASS","HOSPITAL_URL","REGISTER_CODE","PACK_BAR_PREFIX","IF_ITEM_MANAGE","IF_TEMPLATE_HOS","MEDICAL_CONGENER","MEDICAL_CONGENER_ROLE","VEST_IN_DC","INSTANCE_ID","HOSPITAL_ATTRIBUTE","DATASYNC_TYPE","HGROUP_ID","CITEM_MANAGE" FROM "XH_SYS"."SYS6_HOSPITAL_INFO" 

2025-07-03 13:07:19.505 +08:00 [INF] 【SQL执行耗时:378.0513ms】

[Sql]:SELECT "LAB_ID","FIRST_RPERSON","LAB_NAME","LAB_ADDRESS","LAB_CODE","LAST_MTIME","LAB_SORT","LAB_RNAME","LAB_CLASS","LAB_PERSON","LINKMAN_TEL","REGISTER_CODE","LAB_LINKMAN","STATE_FLAG","REMARK","LAST_MPERSON","FIRST_RTIME","SPELL_CODE","LAB_TEL","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_LAB" 

2025-07-03 13:07:19.939 +08:00 [INF] 【SQL执行耗时:355.469ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-03 13:07:20.368 +08:00 [INF] 【SQL执行耗时:355.2281ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP" 

2025-07-03 13:07:20.788 +08:00 [INF] 【SQL执行耗时:344.6777ms】

[Sql]:SELECT "AREA_ID","AREA_CODE","AREA_SORT","AREA_NAME","AREA_CLASS","AREA_PERSON","AREA_ADDRESS","AREA_TEL","SPELL_CODE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","STATE_FLAG","REMARK","HOSPITAL_ID","TAG_SIGN","AREA_ANAME","PACK_BAR_PREFIX" FROM "XH_SYS"."SYS6_INSPECTION_AREA" 

2025-07-03 13:07:21.212 +08:00 [INF] 【SQL执行耗时:351.7632ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_CLASS" = :EQUIPMENT_CLASS0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_CLASS0 [Value]:0 [Type]:String    

2025-07-03 13:07:21.638 +08:00 [INF] 【SQL执行耗时:352.9632ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 13:07:22.066 +08:00 [INF] 【SQL执行耗时:345.6138ms】

[Sql]:SELECT "HOSPITAL_ID","HOSPITAL_LOGIN","HOSPITAL_CNAME","HOSPITAL_SNAME","HOSPITAL_TYPE","HOSPITAL_LEVEL","LEVEL1_CODE","LEVEL2_CODE","LEVEL3_CODE","HOSPITAL_BARCODE","HOSPITAL_PROPERTY","HOSPITAL_PERSON","HOSPITAL_ADDRESS","HOSPITAL_TEL","HOSPITAL_STATE","FIRST_PERSON","FIRST_RTIME","LAST_MPRESON","LAST_MTIME","REMARK","HOSPITAL_BCODE","HOSPITAL_RANGE","BARCODE_TYPE","BARCODE_RANGE","CHARGE_PROVIDE_TYPE","DATADB_IP","WECHAT_NAME","LAST_MPERSON","FIRST_RPERSON","UPDATE_FLAG","INSERFACE_SERVICE","SAMELEVEL_HOSPITAL","HOSPITAL_CLASS","HOSPITAL_URL","REGISTER_CODE","PACK_BAR_PREFIX","IF_ITEM_MANAGE","IF_TEMPLATE_HOS","MEDICAL_CONGENER","MEDICAL_CONGENER_ROLE","VEST_IN_DC","INSTANCE_ID","HOSPITAL_ATTRIBUTE","DATASYNC_TYPE","HGROUP_ID","CITEM_MANAGE" FROM "XH_SYS"."SYS6_HOSPITAL_INFO" 

2025-07-03 13:07:22.485 +08:00 [INF] 【SQL执行耗时:351.4581ms】

[Sql]:SELECT "LAB_ID","FIRST_RPERSON","LAB_NAME","LAB_ADDRESS","LAB_CODE","LAST_MTIME","LAB_SORT","LAB_RNAME","LAB_CLASS","LAB_PERSON","LINKMAN_TEL","REGISTER_CODE","LAB_LINKMAN","STATE_FLAG","REMARK","LAST_MPERSON","FIRST_RTIME","SPELL_CODE","LAB_TEL","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_LAB" 

2025-07-03 13:07:22.902 +08:00 [INF] 【SQL执行耗时:343.8757ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-03 13:07:23.330 +08:00 [INF] 【SQL执行耗时:357.6942ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP" 

2025-07-03 13:07:23.732 +08:00 [INF] 【SQL执行耗时:328.7702ms】

[Sql]:SELECT "AREA_ID","AREA_CODE","AREA_SORT","AREA_NAME","AREA_CLASS","AREA_PERSON","AREA_ADDRESS","AREA_TEL","SPELL_CODE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","STATE_FLAG","REMARK","HOSPITAL_ID","TAG_SIGN","AREA_ANAME","PACK_BAR_PREFIX" FROM "XH_SYS"."SYS6_INSPECTION_AREA" 

2025-07-03 13:07:24.148 +08:00 [INF] 【SQL执行耗时:346.8053ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_CLASS" = :EQUIPMENT_CLASS0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_CLASS0 [Value]:0 [Type]:String    

2025-07-03 13:07:24.574 +08:00 [INF] 【SQL执行耗时:354.9997ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 13:07:24.996 +08:00 [INF] 【SQL执行耗时:346.8778ms】

[Sql]:SELECT "HOSPITAL_ID","HOSPITAL_LOGIN","HOSPITAL_CNAME","HOSPITAL_SNAME","HOSPITAL_TYPE","HOSPITAL_LEVEL","LEVEL1_CODE","LEVEL2_CODE","LEVEL3_CODE","HOSPITAL_BARCODE","HOSPITAL_PROPERTY","HOSPITAL_PERSON","HOSPITAL_ADDRESS","HOSPITAL_TEL","HOSPITAL_STATE","FIRST_PERSON","FIRST_RTIME","LAST_MPRESON","LAST_MTIME","REMARK","HOSPITAL_BCODE","HOSPITAL_RANGE","BARCODE_TYPE","BARCODE_RANGE","CHARGE_PROVIDE_TYPE","DATADB_IP","WECHAT_NAME","LAST_MPERSON","FIRST_RPERSON","UPDATE_FLAG","INSERFACE_SERVICE","SAMELEVEL_HOSPITAL","HOSPITAL_CLASS","HOSPITAL_URL","REGISTER_CODE","PACK_BAR_PREFIX","IF_ITEM_MANAGE","IF_TEMPLATE_HOS","MEDICAL_CONGENER","MEDICAL_CONGENER_ROLE","VEST_IN_DC","INSTANCE_ID","HOSPITAL_ATTRIBUTE","DATASYNC_TYPE","HGROUP_ID","CITEM_MANAGE" FROM "XH_SYS"."SYS6_HOSPITAL_INFO" 

2025-07-03 13:07:25.415 +08:00 [INF] 【SQL执行耗时:334.7585ms】

[Sql]:SELECT "LAB_ID","FIRST_RPERSON","LAB_NAME","LAB_ADDRESS","LAB_CODE","LAST_MTIME","LAB_SORT","LAB_RNAME","LAB_CLASS","LAB_PERSON","LINKMAN_TEL","REGISTER_CODE","LAB_LINKMAN","STATE_FLAG","REMARK","LAST_MPERSON","FIRST_RTIME","SPELL_CODE","LAB_TEL","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_LAB" 

2025-07-03 13:07:25.853 +08:00 [INF] 【SQL执行耗时:368.3337ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-03 13:07:26.291 +08:00 [INF] 【SQL执行耗时:362.9526ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP" 

2025-07-03 13:07:26.699 +08:00 [INF] 【SQL执行耗时:333.8455ms】

[Sql]:SELECT "AREA_ID","AREA_CODE","AREA_SORT","AREA_NAME","AREA_CLASS","AREA_PERSON","AREA_ADDRESS","AREA_TEL","SPELL_CODE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","STATE_FLAG","REMARK","HOSPITAL_ID","TAG_SIGN","AREA_ANAME","PACK_BAR_PREFIX" FROM "XH_SYS"."SYS6_INSPECTION_AREA" 

2025-07-03 13:07:27.163 +08:00 [INF] 【SQL执行耗时:366.3751ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_CLASS" = :EQUIPMENT_CLASS0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_CLASS0 [Value]:0 [Type]:String    

2025-07-03 13:07:27.602 +08:00 [INF] 【SQL执行耗时:361.6387ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 13:07:28.025 +08:00 [INF] 【SQL执行耗时:347.0889ms】

[Sql]:SELECT "HOSPITAL_ID","HOSPITAL_LOGIN","HOSPITAL_CNAME","HOSPITAL_SNAME","HOSPITAL_TYPE","HOSPITAL_LEVEL","LEVEL1_CODE","LEVEL2_CODE","LEVEL3_CODE","HOSPITAL_BARCODE","HOSPITAL_PROPERTY","HOSPITAL_PERSON","HOSPITAL_ADDRESS","HOSPITAL_TEL","HOSPITAL_STATE","FIRST_PERSON","FIRST_RTIME","LAST_MPRESON","LAST_MTIME","REMARK","HOSPITAL_BCODE","HOSPITAL_RANGE","BARCODE_TYPE","BARCODE_RANGE","CHARGE_PROVIDE_TYPE","DATADB_IP","WECHAT_NAME","LAST_MPERSON","FIRST_RPERSON","UPDATE_FLAG","INSERFACE_SERVICE","SAMELEVEL_HOSPITAL","HOSPITAL_CLASS","HOSPITAL_URL","REGISTER_CODE","PACK_BAR_PREFIX","IF_ITEM_MANAGE","IF_TEMPLATE_HOS","MEDICAL_CONGENER","MEDICAL_CONGENER_ROLE","VEST_IN_DC","INSTANCE_ID","HOSPITAL_ATTRIBUTE","DATASYNC_TYPE","HGROUP_ID","CITEM_MANAGE" FROM "XH_SYS"."SYS6_HOSPITAL_INFO" 

2025-07-03 13:07:28.450 +08:00 [INF] 【SQL执行耗时:353.839ms】

[Sql]:SELECT "LAB_ID","FIRST_RPERSON","LAB_NAME","LAB_ADDRESS","LAB_CODE","LAST_MTIME","LAB_SORT","LAB_RNAME","LAB_CLASS","LAB_PERSON","LINKMAN_TEL","REGISTER_CODE","LAB_LINKMAN","STATE_FLAG","REMARK","LAST_MPERSON","FIRST_RTIME","SPELL_CODE","LAB_TEL","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_LAB" 

2025-07-03 13:07:28.864 +08:00 [INF] 【SQL执行耗时:340.2734ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-03 13:07:29.281 +08:00 [INF] 【SQL执行耗时:347.4782ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP" 

2025-07-03 13:07:29.694 +08:00 [INF] 【SQL执行耗时:341.7893ms】

[Sql]:SELECT "AREA_ID","AREA_CODE","AREA_SORT","AREA_NAME","AREA_CLASS","AREA_PERSON","AREA_ADDRESS","AREA_TEL","SPELL_CODE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","STATE_FLAG","REMARK","HOSPITAL_ID","TAG_SIGN","AREA_ANAME","PACK_BAR_PREFIX" FROM "XH_SYS"."SYS6_INSPECTION_AREA" 

2025-07-03 13:07:29.767 +08:00 [INF] HTTP GET /api/CodeCustom/GetEquipmentCodeCustomDict responded 200 in 12980.7593 ms
2025-07-03 13:07:29.767 +08:00 [INF] 【接口超时阀值预警】 [d4ae6e1454ce13d7bc713f1c5508d022]接口/api/CodeCustom/GetEquipmentCodeCustomDict,耗时:[12981]毫秒
2025-07-03 13:16:20.332 +08:00 [ERR] 未处理的异常::System.MissingMethodException: Method not found: 'System.Collections.Generic.List`1<XH.H82.Models.Dtos.KeyValueDto> XH.H82.Models.EquipmentCodeCustom.DisplayContent.CreatFixedFieldDisplays()'.
   at XH.H82.API.Controllers.EquipmentCodeCustom.CodeCustomController.AddEquipmentCodeCustomDict(AddEquipmentCodeCustomDictDto input)
   at lambda_method909(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-07-03 13:16:20.336 +08:00 [ERR] HTTP POST /api/CodeCustom/AddEquipmentCodeCustomDict responded 500 in 96.9597 ms
2025-07-03 13:16:43.943 +08:00 [INF] ==>App Start..2025-07-03 13:16:43
2025-07-03 13:16:44.110 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-03 13:16:44.114 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-03 13:16:45.521 +08:00 [INF] ==>基础连接请求完成.
2025-07-03 13:16:45.886 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-03 13:16:46.209 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-03 13:16:46.520 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-03 13:16:46.537 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-03 13:16:46.866 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-03 13:16:47.374 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-03 13:16:47.487 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-03 13:16:47.930 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-03 13:16:47.931 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-03 13:16:48.932 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-03 13:16:51.145 +08:00 [INF] ==>初始化完成..
2025-07-03 13:16:51.166 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-03 13:16:51.169 +08:00 [INF] 设备启用任务
2025-07-03 13:16:51.170 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-03 13:16:51.561 +08:00 [INF] 【SQL执行耗时:368.2126ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-03 13:16:51.701 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-03 13:16:51.714 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-03 13:16:51.716 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-03 13:16:51.716 +08:00 [INF] Hosting environment: Development
2025-07-03 13:16:51.717 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-03 13:17:01.549 +08:00 [INF] 【SQL执行耗时:366.829ms】

[Sql]:INSERT INTO "XH_OA"."EMS_EQPNO_FORMAT_DICT"  
           ("EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK")
     VALUES
           (:EQP_NO_ID,:HOSPITAL_ID,:EQP_NO_NAME,:EQP_NO_LEVEL,:EQP_NO_CLASS,:EQP_DISPLAY_JSON,:EQP_NO_APPLYS,:EQP_NO_STATE,:FIRST_RPERSON,:FIRST_RTIME,:LAST_MPERSON,:LAST_MTIME,:REMARK)  
[Pars]:
[Name]::EQP_NO_ID [Value]:F9E424F1673741CE90A1967C2518B68C [Type]:String    
[Name]::HOSPITAL_ID [Value]:33A001 [Type]:String    
[Name]::EQP_NO_NAME [Value]:测试33 [Type]:String    
[Name]::EQP_NO_LEVEL [Value]:0 [Type]:String    
[Name]::EQP_NO_CLASS [Value]:0 [Type]:String    
[Name]::EQP_DISPLAY_JSON [Value]:{"DisplayContentString":"{机构}_{院区}_{设备编号}_{设备名称}","DisplayContentCode":"{HOSPITAL_NAME}_{AREA_NAME}_{EQUIPMENT_CODE}_{EQUIPMENT_NAME}","FixedFieldDisplays":[{"Key":"{HOSPITAL_NAME}","Value":"A"}]} [Type]:String    
[Name]::EQP_NO_APPLYS [Value]:PG001 [Type]:String    
[Name]::EQP_NO_STATE [Value]:1 [Type]:String    
[Name]::FIRST_RPERSON [Value]:gz1_广测重 [Type]:String    
[Name]::FIRST_RTIME [Value]:2025/7/3 13:17:01 [Type]:DateTime    
[Name]::LAST_MPERSON [Value]:gz1_广测重 [Type]:String    
[Name]::LAST_MTIME [Value]:2025/7/3 13:17:01 [Type]:DateTime    
[Name]::REMARK [Value]: [Type]:String    

2025-07-03 13:17:01.639 +08:00 [INF] HTTP POST /api/CodeCustom/AddEquipmentCodeCustomDict responded 200 in 9599.0547 ms
2025-07-03 13:17:01.641 +08:00 [INF] 【接口超时阀值预警】 [71361c18ef1c018c00192d400dd99e45]接口/api/CodeCustom/AddEquipmentCodeCustomDict,耗时:[9606]毫秒
2025-07-03 13:17:54.929 +08:00 [INF] 【SQL执行耗时:362.1285ms】

[Sql]:INSERT INTO "XH_OA"."EMS_EQPNO_FORMAT_DICT"  
           ("EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK")
     VALUES
           (:EQP_NO_ID,:HOSPITAL_ID,:EQP_NO_NAME,:EQP_NO_LEVEL,:EQP_NO_CLASS,:EQP_DISPLAY_JSON,:EQP_NO_APPLYS,:EQP_NO_STATE,:FIRST_RPERSON,:FIRST_RTIME,:LAST_MPERSON,:LAST_MTIME,:REMARK)  
[Pars]:
[Name]::EQP_NO_ID [Value]:72D5A4A5D5134B33855D536D77D0DBCE [Type]:String    
[Name]::HOSPITAL_ID [Value]:33A001 [Type]:String    
[Name]::EQP_NO_NAME [Value]:测试34 [Type]:String    
[Name]::EQP_NO_LEVEL [Value]:0 [Type]:String    
[Name]::EQP_NO_CLASS [Value]:0 [Type]:String    
[Name]::EQP_DISPLAY_JSON [Value]:{"DisplayContentString":"{机构}_{院区}_{设备编号}_{设备名称}","DisplayContentCode":"{HOSPITAL_NAME}_{AREA_NAME}_{EQUIPMENT_CODE}_{EQUIPMENT_NAME}","FixedFieldDisplays":[{"Key":"{HOSPITAL_NAME}","Value":""},{"Key":"{AREA_NAME}","Value":""},{"Key":"{LAB_NAME}","Value":""},{"Key":"{MGROUP_NAME}","Value":""},{"Key":"{UNIT_NAME}","Value":""},{"Key":"{SMBL_LAB_NAME}","Value":""}]} [Type]:String    
[Name]::EQP_NO_APPLYS [Value]:PG001 [Type]:String    
[Name]::EQP_NO_STATE [Value]:1 [Type]:String    
[Name]::FIRST_RPERSON [Value]:gz1_广测重 [Type]:String    
[Name]::FIRST_RTIME [Value]:2025/7/3 13:17:54 [Type]:DateTime    
[Name]::LAST_MPERSON [Value]:gz1_广测重 [Type]:String    
[Name]::LAST_MTIME [Value]:2025/7/3 13:17:54 [Type]:DateTime    
[Name]::REMARK [Value]: [Type]:String    

2025-07-03 13:17:54.967 +08:00 [INF] HTTP POST /api/CodeCustom/AddEquipmentCodeCustomDict responded 200 in 444.7252 ms
2025-07-03 13:18:41.825 +08:00 [INF] ==>App Start..2025-07-03 13:18:41
2025-07-03 13:18:42.010 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-03 13:18:42.014 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-03 13:18:43.423 +08:00 [INF] ==>基础连接请求完成.
2025-07-03 13:18:43.789 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-03 13:18:44.118 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-03 13:18:44.449 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-03 13:18:44.455 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-03 13:18:44.781 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-03 13:18:45.181 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-03 13:18:45.270 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-03 13:18:45.678 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-03 13:18:45.678 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-03 13:18:46.533 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-03 13:18:48.767 +08:00 [INF] ==>初始化完成..
2025-07-03 13:18:48.789 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-03 13:18:48.792 +08:00 [INF] 设备启用任务
2025-07-03 13:18:48.793 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-03 13:18:49.188 +08:00 [INF] 【SQL执行耗时:373.5385ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-03 13:18:49.332 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-03 13:18:49.348 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-03 13:18:49.350 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-03 13:18:49.351 +08:00 [INF] Hosting environment: Development
2025-07-03 13:18:49.351 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-03 13:19:31.515 +08:00 [INF] 【SQL执行耗时:357.3311ms】

[Sql]:INSERT INTO "XH_OA"."EMS_EQPNO_FORMAT_DICT"  
           ("EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK")
     VALUES
           (:EQP_NO_ID,:HOSPITAL_ID,:EQP_NO_NAME,:EQP_NO_LEVEL,:EQP_NO_CLASS,:EQP_DISPLAY_JSON,:EQP_NO_APPLYS,:EQP_NO_STATE,:FIRST_RPERSON,:FIRST_RTIME,:LAST_MPERSON,:LAST_MTIME,:REMARK)  
[Pars]:
[Name]::EQP_NO_ID [Value]:2CF767D9166E4884A1D6C5BC8CFAD14F [Type]:String    
[Name]::HOSPITAL_ID [Value]:33A001 [Type]:String    
[Name]::EQP_NO_NAME [Value]:测试34 [Type]:String    
[Name]::EQP_NO_LEVEL [Value]:0 [Type]:String    
[Name]::EQP_NO_CLASS [Value]:0 [Type]:String    
[Name]::EQP_DISPLAY_JSON [Value]:{"DisplayContentString":"{机构}_{院区}_{设备编号}_{设备名称}","DisplayContentCode":"{HOSPITAL_NAME}_{AREA_NAME}_{EQUIPMENT_CODE}_{EQUIPMENT_NAME}","FixedFieldDisplays":[{"Key":"{HOSPITAL_NAME}","Value":"A"},{"Key":"{AREA_NAME}","Value":""},{"Key":"{LAB_NAME}","Value":""},{"Key":"{MGROUP_NAME}","Value":""},{"Key":"{UNIT_NAME}","Value":""},{"Key":"{SMBL_LAB_NAME}","Value":""}]} [Type]:String    
[Name]::EQP_NO_APPLYS [Value]:PG001 [Type]:String    
[Name]::EQP_NO_STATE [Value]:1 [Type]:String    
[Name]::FIRST_RPERSON [Value]:gz1_广测重 [Type]:String    
[Name]::FIRST_RTIME [Value]:2025/7/3 13:19:16 [Type]:DateTime    
[Name]::LAST_MPERSON [Value]:gz1_广测重 [Type]:String    
[Name]::LAST_MTIME [Value]:2025/7/3 13:19:30 [Type]:DateTime    
[Name]::REMARK [Value]: [Type]:String    

2025-07-03 13:19:42.209 +08:00 [INF] HTTP POST /api/CodeCustom/AddEquipmentCodeCustomDict responded 200 in 52655.2590 ms
2025-07-03 13:19:42.211 +08:00 [INF] 【接口超时阀值预警】 [d9fff21283a38850b86005db414bc1e0]接口/api/CodeCustom/AddEquipmentCodeCustomDict,耗时:[52662]毫秒
2025-07-03 13:20:57.615 +08:00 [INF] ==>App Start..2025-07-03 13:20:57
2025-07-03 13:20:57.777 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-03 13:20:57.780 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-03 13:20:59.178 +08:00 [INF] ==>基础连接请求完成.
2025-07-03 13:20:59.539 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-03 13:20:59.884 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-03 13:21:00.188 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-03 13:21:00.195 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-03 13:21:00.536 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-03 13:21:00.964 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-03 13:21:01.049 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-03 13:21:01.467 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-03 13:21:01.467 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-03 13:21:02.371 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-03 13:21:04.501 +08:00 [INF] ==>初始化完成..
2025-07-03 13:21:04.521 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-03 13:21:04.523 +08:00 [INF] 设备启用任务
2025-07-03 13:21:04.524 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-03 13:21:04.904 +08:00 [INF] 【SQL执行耗时:359.3319ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-03 13:21:05.034 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-03 13:21:05.046 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-03 13:21:05.047 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-03 13:21:05.047 +08:00 [INF] Hosting environment: Development
2025-07-03 13:21:05.047 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-03 13:21:29.052 +08:00 [INF] 【SQL执行耗时:357.9389ms】

[Sql]:INSERT INTO "XH_OA"."EMS_EQPNO_FORMAT_DICT"  
           ("EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK")
     VALUES
           (:EQP_NO_ID,:HOSPITAL_ID,:EQP_NO_NAME,:EQP_NO_LEVEL,:EQP_NO_CLASS,:EQP_DISPLAY_JSON,:EQP_NO_APPLYS,:EQP_NO_STATE,:FIRST_RPERSON,:FIRST_RTIME,:LAST_MPERSON,:LAST_MTIME,:REMARK)  
[Pars]:
[Name]::EQP_NO_ID [Value]:60449FBFDA044C0ABD4B26AE54C3C62C [Type]:String    
[Name]::HOSPITAL_ID [Value]:33A001 [Type]:String    
[Name]::EQP_NO_NAME [Value]:测试34 [Type]:String    
[Name]::EQP_NO_LEVEL [Value]:0 [Type]:String    
[Name]::EQP_NO_CLASS [Value]:0 [Type]:String    
[Name]::EQP_DISPLAY_JSON [Value]:{"DisplayContentString":"{机构}_{院区}_{设备编号}_{设备名称}","DisplayContentCode":"{HOSPITAL_NAME}_{AREA_NAME}_{EQUIPMENT_CODE}_{EQUIPMENT_NAME}","FixedFieldDisplays":[{"Key":"{HOSPITAL_NAME}","Value":"A"},{"Key":"{AREA_NAME}","Value":""},{"Key":"{LAB_NAME}","Value":""},{"Key":"{MGROUP_NAME}","Value":""},{"Key":"{UNIT_NAME}","Value":""},{"Key":"{SMBL_LAB_NAME}","Value":""}]} [Type]:String    
[Name]::EQP_NO_APPLYS [Value]:PG001 [Type]:String    
[Name]::EQP_NO_STATE [Value]:1 [Type]:String    
[Name]::FIRST_RPERSON [Value]:gz1_广测重 [Type]:String    
[Name]::FIRST_RTIME [Value]:2025/7/3 13:21:28 [Type]:DateTime    
[Name]::LAST_MPERSON [Value]:gz1_广测重 [Type]:String    
[Name]::LAST_MTIME [Value]:2025/7/3 13:21:28 [Type]:DateTime    
[Name]::REMARK [Value]: [Type]:String    

2025-07-03 13:21:29.148 +08:00 [INF] HTTP POST /api/CodeCustom/AddEquipmentCodeCustomDict responded 200 in 6242.7990 ms
2025-07-03 13:21:29.151 +08:00 [INF] 【接口超时阀值预警】 [cdcf7b77296492d6d500eba417d3d5df]接口/api/CodeCustom/AddEquipmentCodeCustomDict,耗时:[6251]毫秒
2025-07-03 13:22:28.916 +08:00 [INF] HTTP GET /api/CodeCustom/GetCustomDictCode responded 200 in 56.0904 ms
2025-07-03 13:23:10.714 +08:00 [INF] ==>App Start..2025-07-03 13:23:10
2025-07-03 13:23:10.876 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-03 13:23:10.879 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-03 13:23:12.403 +08:00 [INF] ==>基础连接请求完成.
2025-07-03 13:23:12.771 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-03 13:23:13.166 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-03 13:23:13.473 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-03 13:23:13.475 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-03 13:23:13.808 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-03 13:23:14.245 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-03 13:23:14.341 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-03 13:23:14.770 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-03 13:23:14.770 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-03 13:23:15.783 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-03 13:23:17.914 +08:00 [INF] ==>初始化完成..
2025-07-03 13:23:17.935 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-03 13:23:17.937 +08:00 [INF] 设备启用任务
2025-07-03 13:23:17.938 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-03 13:23:18.310 +08:00 [INF] 【SQL执行耗时:352.35ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-03 13:23:18.438 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-03 13:23:18.450 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-03 13:23:18.451 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-03 13:23:18.451 +08:00 [INF] Hosting environment: Development
2025-07-03 13:23:18.452 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-03 13:23:33.337 +08:00 [INF] HTTP GET /api/CodeCustom/GetCustomDictCode responded 401 in 253.8131 ms
2025-07-03 13:23:57.249 +08:00 [INF] HTTP GET /api/CodeCustom/GetCustomDictCode responded 200 in 8365.7702 ms
2025-07-03 13:23:57.250 +08:00 [INF] 【接口超时阀值预警】 [8a0c4a3733bf4f1d496e2acc299c0886]接口/api/CodeCustom/GetCustomDictCode,耗时:[8366]毫秒
2025-07-03 13:24:12.442 +08:00 [INF] HTTP GET /api/CodeCustom/GetCustomDictCode responded 200 in 47.0899 ms
2025-07-03 13:39:10.292 +08:00 [INF] HTTP GET /api/CodeCustom/GetCustomDictCode responded 200 in 43.7921 ms
2025-07-03 13:39:11.371 +08:00 [INF] HTTP GET /api/CodeCustom/GetCustomDictCode responded 200 in 39.9728 ms
2025-07-03 13:39:11.743 +08:00 [INF] HTTP GET /api/CodeCustom/GetCustomDictCode responded 200 in 39.9951 ms
2025-07-03 13:39:12.042 +08:00 [INF] HTTP GET /api/CodeCustom/GetCustomDictCode responded 200 in 40.0751 ms
2025-07-03 13:39:12.344 +08:00 [INF] HTTP GET /api/CodeCustom/GetCustomDictCode responded 200 in 40.1094 ms
2025-07-03 13:39:12.647 +08:00 [INF] HTTP GET /api/CodeCustom/GetCustomDictCode responded 200 in 40.8762 ms
2025-07-03 13:39:19.712 +08:00 [INF] 【SQL执行耗时:367.8438ms】

[Sql]:SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" <> :EqpNoState0 ) 
[Pars]:
[Name]::EqpNoState0 [Value]:2 [Type]:String    

2025-07-03 13:39:20.464 +08:00 [INF] 【SQL执行耗时:367.7442ms】

[Sql]:SELECT "DATA_ID","HOSPITAL_ID","LAB_ID","CLASS_ID","DATA_SORT","DATA_CNAME","DATA_ENAME","HIS_ID","CUSTOM_CODE","SPELL_CODE","DATA_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","DATA_SNAME","DATA_SOURCE","ONE_CLASS","DATA_UNAME","IF_REPEAT","SYSTEM_ID" FROM "XH_SYS"."SYS6_BASE_DATA"  WHERE (( "CLASS_ID" = :CLASS_ID0 ) AND ( "DATA_STATE" = :DATA_STATE1 )) 
[Pars]:
[Name]::CLASS_ID0 [Value]:专业分类 [Type]:String    
[Name]::DATA_STATE1 [Value]:1 [Type]:String    

2025-07-03 13:39:20.859 +08:00 [INF] 【SQL执行耗时:349.5904ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_CLASS" = :EQUIPMENT_CLASS0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_CLASS0 [Value]:0 [Type]:String    

2025-07-03 13:39:21.285 +08:00 [INF] 【SQL执行耗时:350.168ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 13:39:21.741 +08:00 [INF] 【SQL执行耗时:371.3503ms】

[Sql]:SELECT "HOSPITAL_ID","HOSPITAL_LOGIN","HOSPITAL_CNAME","HOSPITAL_SNAME","HOSPITAL_TYPE","HOSPITAL_LEVEL","LEVEL1_CODE","LEVEL2_CODE","LEVEL3_CODE","HOSPITAL_BARCODE","HOSPITAL_PROPERTY","HOSPITAL_PERSON","HOSPITAL_ADDRESS","HOSPITAL_TEL","HOSPITAL_STATE","FIRST_PERSON","FIRST_RTIME","LAST_MPRESON","LAST_MTIME","REMARK","HOSPITAL_BCODE","HOSPITAL_RANGE","BARCODE_TYPE","BARCODE_RANGE","CHARGE_PROVIDE_TYPE","DATADB_IP","WECHAT_NAME","LAST_MPERSON","FIRST_RPERSON","UPDATE_FLAG","INSERFACE_SERVICE","SAMELEVEL_HOSPITAL","HOSPITAL_CLASS","HOSPITAL_URL","REGISTER_CODE","PACK_BAR_PREFIX","IF_ITEM_MANAGE","IF_TEMPLATE_HOS","MEDICAL_CONGENER","MEDICAL_CONGENER_ROLE","VEST_IN_DC","INSTANCE_ID","HOSPITAL_ATTRIBUTE","DATASYNC_TYPE","HGROUP_ID","CITEM_MANAGE" FROM "XH_SYS"."SYS6_HOSPITAL_INFO" 

2025-07-03 13:39:22.184 +08:00 [INF] 【SQL执行耗时:357.0396ms】

[Sql]:SELECT "LAB_ID","FIRST_RPERSON","LAB_NAME","LAB_ADDRESS","LAB_CODE","LAST_MTIME","LAB_SORT","LAB_RNAME","LAB_CLASS","LAB_PERSON","LINKMAN_TEL","REGISTER_CODE","LAB_LINKMAN","STATE_FLAG","REMARK","LAST_MPERSON","FIRST_RTIME","SPELL_CODE","LAB_TEL","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_LAB" 

2025-07-03 13:39:22.638 +08:00 [INF] 【SQL执行耗时:371.7757ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-03 13:39:23.060 +08:00 [INF] 【SQL执行耗时:341.8948ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP" 

2025-07-03 13:39:23.482 +08:00 [INF] 【SQL执行耗时:347.6503ms】

[Sql]:SELECT "AREA_ID","AREA_CODE","AREA_SORT","AREA_NAME","AREA_CLASS","AREA_PERSON","AREA_ADDRESS","AREA_TEL","SPELL_CODE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","STATE_FLAG","REMARK","HOSPITAL_ID","TAG_SIGN","AREA_ANAME","PACK_BAR_PREFIX" FROM "XH_SYS"."SYS6_INSPECTION_AREA" 

2025-07-03 13:39:23.917 +08:00 [INF] 【SQL执行耗时:355.388ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_CLASS" = :EQUIPMENT_CLASS0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_CLASS0 [Value]:0 [Type]:String    

2025-07-03 13:39:24.347 +08:00 [INF] 【SQL执行耗时:355.4121ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 13:39:24.808 +08:00 [INF] 【SQL执行耗时:384.4983ms】

[Sql]:SELECT "HOSPITAL_ID","HOSPITAL_LOGIN","HOSPITAL_CNAME","HOSPITAL_SNAME","HOSPITAL_TYPE","HOSPITAL_LEVEL","LEVEL1_CODE","LEVEL2_CODE","LEVEL3_CODE","HOSPITAL_BARCODE","HOSPITAL_PROPERTY","HOSPITAL_PERSON","HOSPITAL_ADDRESS","HOSPITAL_TEL","HOSPITAL_STATE","FIRST_PERSON","FIRST_RTIME","LAST_MPRESON","LAST_MTIME","REMARK","HOSPITAL_BCODE","HOSPITAL_RANGE","BARCODE_TYPE","BARCODE_RANGE","CHARGE_PROVIDE_TYPE","DATADB_IP","WECHAT_NAME","LAST_MPERSON","FIRST_RPERSON","UPDATE_FLAG","INSERFACE_SERVICE","SAMELEVEL_HOSPITAL","HOSPITAL_CLASS","HOSPITAL_URL","REGISTER_CODE","PACK_BAR_PREFIX","IF_ITEM_MANAGE","IF_TEMPLATE_HOS","MEDICAL_CONGENER","MEDICAL_CONGENER_ROLE","VEST_IN_DC","INSTANCE_ID","HOSPITAL_ATTRIBUTE","DATASYNC_TYPE","HGROUP_ID","CITEM_MANAGE" FROM "XH_SYS"."SYS6_HOSPITAL_INFO" 

2025-07-03 13:39:25.243 +08:00 [INF] 【SQL执行耗时:359.7106ms】

[Sql]:SELECT "LAB_ID","FIRST_RPERSON","LAB_NAME","LAB_ADDRESS","LAB_CODE","LAST_MTIME","LAB_SORT","LAB_RNAME","LAB_CLASS","LAB_PERSON","LINKMAN_TEL","REGISTER_CODE","LAB_LINKMAN","STATE_FLAG","REMARK","LAST_MPERSON","FIRST_RTIME","SPELL_CODE","LAB_TEL","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_LAB" 

2025-07-03 13:39:25.663 +08:00 [INF] 【SQL执行耗时:347.0504ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-03 13:39:26.088 +08:00 [INF] 【SQL执行耗时:352.0167ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP" 

2025-07-03 13:39:26.499 +08:00 [INF] 【SQL执行耗时:339.3566ms】

[Sql]:SELECT "AREA_ID","AREA_CODE","AREA_SORT","AREA_NAME","AREA_CLASS","AREA_PERSON","AREA_ADDRESS","AREA_TEL","SPELL_CODE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","STATE_FLAG","REMARK","HOSPITAL_ID","TAG_SIGN","AREA_ANAME","PACK_BAR_PREFIX" FROM "XH_SYS"."SYS6_INSPECTION_AREA" 

2025-07-03 13:39:26.932 +08:00 [INF] 【SQL执行耗时:363.3824ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_CLASS" = :EQUIPMENT_CLASS0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_CLASS0 [Value]:0 [Type]:String    

2025-07-03 13:39:27.368 +08:00 [INF] 【SQL执行耗时:355.2153ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 13:39:27.836 +08:00 [INF] 【SQL执行耗时:376.1319ms】

[Sql]:SELECT "HOSPITAL_ID","HOSPITAL_LOGIN","HOSPITAL_CNAME","HOSPITAL_SNAME","HOSPITAL_TYPE","HOSPITAL_LEVEL","LEVEL1_CODE","LEVEL2_CODE","LEVEL3_CODE","HOSPITAL_BARCODE","HOSPITAL_PROPERTY","HOSPITAL_PERSON","HOSPITAL_ADDRESS","HOSPITAL_TEL","HOSPITAL_STATE","FIRST_PERSON","FIRST_RTIME","LAST_MPRESON","LAST_MTIME","REMARK","HOSPITAL_BCODE","HOSPITAL_RANGE","BARCODE_TYPE","BARCODE_RANGE","CHARGE_PROVIDE_TYPE","DATADB_IP","WECHAT_NAME","LAST_MPERSON","FIRST_RPERSON","UPDATE_FLAG","INSERFACE_SERVICE","SAMELEVEL_HOSPITAL","HOSPITAL_CLASS","HOSPITAL_URL","REGISTER_CODE","PACK_BAR_PREFIX","IF_ITEM_MANAGE","IF_TEMPLATE_HOS","MEDICAL_CONGENER","MEDICAL_CONGENER_ROLE","VEST_IN_DC","INSTANCE_ID","HOSPITAL_ATTRIBUTE","DATASYNC_TYPE","HGROUP_ID","CITEM_MANAGE" FROM "XH_SYS"."SYS6_HOSPITAL_INFO" 

2025-07-03 13:39:28.259 +08:00 [INF] 【SQL执行耗时:348.9869ms】

[Sql]:SELECT "LAB_ID","FIRST_RPERSON","LAB_NAME","LAB_ADDRESS","LAB_CODE","LAST_MTIME","LAB_SORT","LAB_RNAME","LAB_CLASS","LAB_PERSON","LINKMAN_TEL","REGISTER_CODE","LAB_LINKMAN","STATE_FLAG","REMARK","LAST_MPERSON","FIRST_RTIME","SPELL_CODE","LAB_TEL","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_LAB" 

2025-07-03 13:39:28.682 +08:00 [INF] 【SQL执行耗时:351.6173ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-03 13:39:29.180 +08:00 [INF] 【SQL执行耗时:425.8277ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP" 

2025-07-03 13:39:29.633 +08:00 [INF] 【SQL执行耗时:376.3777ms】

[Sql]:SELECT "AREA_ID","AREA_CODE","AREA_SORT","AREA_NAME","AREA_CLASS","AREA_PERSON","AREA_ADDRESS","AREA_TEL","SPELL_CODE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","STATE_FLAG","REMARK","HOSPITAL_ID","TAG_SIGN","AREA_ANAME","PACK_BAR_PREFIX" FROM "XH_SYS"."SYS6_INSPECTION_AREA" 

2025-07-03 13:39:30.059 +08:00 [INF] 【SQL执行耗时:357.0499ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_CLASS" = :EQUIPMENT_CLASS0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_CLASS0 [Value]:0 [Type]:String    

2025-07-03 13:39:30.471 +08:00 [INF] 【SQL执行耗时:340.9369ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 13:39:30.881 +08:00 [INF] 【SQL执行耗时:338.8685ms】

[Sql]:SELECT "HOSPITAL_ID","HOSPITAL_LOGIN","HOSPITAL_CNAME","HOSPITAL_SNAME","HOSPITAL_TYPE","HOSPITAL_LEVEL","LEVEL1_CODE","LEVEL2_CODE","LEVEL3_CODE","HOSPITAL_BARCODE","HOSPITAL_PROPERTY","HOSPITAL_PERSON","HOSPITAL_ADDRESS","HOSPITAL_TEL","HOSPITAL_STATE","FIRST_PERSON","FIRST_RTIME","LAST_MPRESON","LAST_MTIME","REMARK","HOSPITAL_BCODE","HOSPITAL_RANGE","BARCODE_TYPE","BARCODE_RANGE","CHARGE_PROVIDE_TYPE","DATADB_IP","WECHAT_NAME","LAST_MPERSON","FIRST_RPERSON","UPDATE_FLAG","INSERFACE_SERVICE","SAMELEVEL_HOSPITAL","HOSPITAL_CLASS","HOSPITAL_URL","REGISTER_CODE","PACK_BAR_PREFIX","IF_ITEM_MANAGE","IF_TEMPLATE_HOS","MEDICAL_CONGENER","MEDICAL_CONGENER_ROLE","VEST_IN_DC","INSTANCE_ID","HOSPITAL_ATTRIBUTE","DATASYNC_TYPE","HGROUP_ID","CITEM_MANAGE" FROM "XH_SYS"."SYS6_HOSPITAL_INFO" 

2025-07-03 13:39:31.322 +08:00 [INF] 【SQL执行耗时:370.8575ms】

[Sql]:SELECT "LAB_ID","FIRST_RPERSON","LAB_NAME","LAB_ADDRESS","LAB_CODE","LAST_MTIME","LAB_SORT","LAB_RNAME","LAB_CLASS","LAB_PERSON","LINKMAN_TEL","REGISTER_CODE","LAB_LINKMAN","STATE_FLAG","REMARK","LAST_MPERSON","FIRST_RTIME","SPELL_CODE","LAB_TEL","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_LAB" 

2025-07-03 13:39:31.762 +08:00 [INF] 【SQL执行耗时:362.7ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-03 13:39:32.186 +08:00 [INF] 【SQL执行耗时:347.478ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP" 

2025-07-03 13:39:32.631 +08:00 [INF] 【SQL执行耗时:374.3323ms】

[Sql]:SELECT "AREA_ID","AREA_CODE","AREA_SORT","AREA_NAME","AREA_CLASS","AREA_PERSON","AREA_ADDRESS","AREA_TEL","SPELL_CODE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","STATE_FLAG","REMARK","HOSPITAL_ID","TAG_SIGN","AREA_ANAME","PACK_BAR_PREFIX" FROM "XH_SYS"."SYS6_INSPECTION_AREA" 

2025-07-03 13:39:33.085 +08:00 [INF] 【SQL执行耗时:378.353ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_CLASS" = :EQUIPMENT_CLASS0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_CLASS0 [Value]:0 [Type]:String    

2025-07-03 13:39:33.533 +08:00 [INF] 【SQL执行耗时:368.4604ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 13:39:33.954 +08:00 [INF] 【SQL执行耗时:346.8333ms】

[Sql]:SELECT "HOSPITAL_ID","HOSPITAL_LOGIN","HOSPITAL_CNAME","HOSPITAL_SNAME","HOSPITAL_TYPE","HOSPITAL_LEVEL","LEVEL1_CODE","LEVEL2_CODE","LEVEL3_CODE","HOSPITAL_BARCODE","HOSPITAL_PROPERTY","HOSPITAL_PERSON","HOSPITAL_ADDRESS","HOSPITAL_TEL","HOSPITAL_STATE","FIRST_PERSON","FIRST_RTIME","LAST_MPRESON","LAST_MTIME","REMARK","HOSPITAL_BCODE","HOSPITAL_RANGE","BARCODE_TYPE","BARCODE_RANGE","CHARGE_PROVIDE_TYPE","DATADB_IP","WECHAT_NAME","LAST_MPERSON","FIRST_RPERSON","UPDATE_FLAG","INSERFACE_SERVICE","SAMELEVEL_HOSPITAL","HOSPITAL_CLASS","HOSPITAL_URL","REGISTER_CODE","PACK_BAR_PREFIX","IF_ITEM_MANAGE","IF_TEMPLATE_HOS","MEDICAL_CONGENER","MEDICAL_CONGENER_ROLE","VEST_IN_DC","INSTANCE_ID","HOSPITAL_ATTRIBUTE","DATASYNC_TYPE","HGROUP_ID","CITEM_MANAGE" FROM "XH_SYS"."SYS6_HOSPITAL_INFO" 

2025-07-03 13:39:34.368 +08:00 [INF] 【SQL执行耗时:343.4849ms】

[Sql]:SELECT "LAB_ID","FIRST_RPERSON","LAB_NAME","LAB_ADDRESS","LAB_CODE","LAST_MTIME","LAB_SORT","LAB_RNAME","LAB_CLASS","LAB_PERSON","LINKMAN_TEL","REGISTER_CODE","LAB_LINKMAN","STATE_FLAG","REMARK","LAST_MPERSON","FIRST_RTIME","SPELL_CODE","LAB_TEL","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_LAB" 

2025-07-03 13:39:34.772 +08:00 [INF] 【SQL执行耗时:332.6379ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-03 13:39:35.258 +08:00 [INF] 【SQL执行耗时:418.4597ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP" 

2025-07-03 13:39:35.698 +08:00 [INF] 【SQL执行耗时:360.6878ms】

[Sql]:SELECT "AREA_ID","AREA_CODE","AREA_SORT","AREA_NAME","AREA_CLASS","AREA_PERSON","AREA_ADDRESS","AREA_TEL","SPELL_CODE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","STATE_FLAG","REMARK","HOSPITAL_ID","TAG_SIGN","AREA_ANAME","PACK_BAR_PREFIX" FROM "XH_SYS"."SYS6_INSPECTION_AREA" 

2025-07-03 13:39:36.146 +08:00 [INF] 【SQL执行耗时:374.0394ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_CLASS" = :EQUIPMENT_CLASS0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_CLASS0 [Value]:0 [Type]:String    

2025-07-03 13:39:36.577 +08:00 [INF] 【SQL执行耗时:352.3903ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 13:39:37.006 +08:00 [INF] 【SQL执行耗时:355.4061ms】

[Sql]:SELECT "HOSPITAL_ID","HOSPITAL_LOGIN","HOSPITAL_CNAME","HOSPITAL_SNAME","HOSPITAL_TYPE","HOSPITAL_LEVEL","LEVEL1_CODE","LEVEL2_CODE","LEVEL3_CODE","HOSPITAL_BARCODE","HOSPITAL_PROPERTY","HOSPITAL_PERSON","HOSPITAL_ADDRESS","HOSPITAL_TEL","HOSPITAL_STATE","FIRST_PERSON","FIRST_RTIME","LAST_MPRESON","LAST_MTIME","REMARK","HOSPITAL_BCODE","HOSPITAL_RANGE","BARCODE_TYPE","BARCODE_RANGE","CHARGE_PROVIDE_TYPE","DATADB_IP","WECHAT_NAME","LAST_MPERSON","FIRST_RPERSON","UPDATE_FLAG","INSERFACE_SERVICE","SAMELEVEL_HOSPITAL","HOSPITAL_CLASS","HOSPITAL_URL","REGISTER_CODE","PACK_BAR_PREFIX","IF_ITEM_MANAGE","IF_TEMPLATE_HOS","MEDICAL_CONGENER","MEDICAL_CONGENER_ROLE","VEST_IN_DC","INSTANCE_ID","HOSPITAL_ATTRIBUTE","DATASYNC_TYPE","HGROUP_ID","CITEM_MANAGE" FROM "XH_SYS"."SYS6_HOSPITAL_INFO" 

2025-07-03 13:39:37.442 +08:00 [INF] 【SQL执行耗时:365.3837ms】

[Sql]:SELECT "LAB_ID","FIRST_RPERSON","LAB_NAME","LAB_ADDRESS","LAB_CODE","LAST_MTIME","LAB_SORT","LAB_RNAME","LAB_CLASS","LAB_PERSON","LINKMAN_TEL","REGISTER_CODE","LAB_LINKMAN","STATE_FLAG","REMARK","LAST_MPERSON","FIRST_RTIME","SPELL_CODE","LAB_TEL","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_LAB" 

2025-07-03 13:39:37.874 +08:00 [INF] 【SQL执行耗时:351.0959ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-03 13:39:38.285 +08:00 [INF] 【SQL执行耗时:340.2155ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP" 

2025-07-03 13:39:38.741 +08:00 [INF] 【SQL执行耗时:373.1889ms】

[Sql]:SELECT "AREA_ID","AREA_CODE","AREA_SORT","AREA_NAME","AREA_CLASS","AREA_PERSON","AREA_ADDRESS","AREA_TEL","SPELL_CODE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","STATE_FLAG","REMARK","HOSPITAL_ID","TAG_SIGN","AREA_ANAME","PACK_BAR_PREFIX" FROM "XH_SYS"."SYS6_INSPECTION_AREA" 

2025-07-03 13:39:39.174 +08:00 [INF] 【SQL执行耗时:351.9622ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_CLASS" = :EQUIPMENT_CLASS0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_CLASS0 [Value]:0 [Type]:String    

2025-07-03 13:39:39.600 +08:00 [INF] 【SQL执行耗时:352.7253ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 13:39:40.118 +08:00 [INF] 【SQL执行耗时:446.439ms】

[Sql]:SELECT "HOSPITAL_ID","HOSPITAL_LOGIN","HOSPITAL_CNAME","HOSPITAL_SNAME","HOSPITAL_TYPE","HOSPITAL_LEVEL","LEVEL1_CODE","LEVEL2_CODE","LEVEL3_CODE","HOSPITAL_BARCODE","HOSPITAL_PROPERTY","HOSPITAL_PERSON","HOSPITAL_ADDRESS","HOSPITAL_TEL","HOSPITAL_STATE","FIRST_PERSON","FIRST_RTIME","LAST_MPRESON","LAST_MTIME","REMARK","HOSPITAL_BCODE","HOSPITAL_RANGE","BARCODE_TYPE","BARCODE_RANGE","CHARGE_PROVIDE_TYPE","DATADB_IP","WECHAT_NAME","LAST_MPERSON","FIRST_RPERSON","UPDATE_FLAG","INSERFACE_SERVICE","SAMELEVEL_HOSPITAL","HOSPITAL_CLASS","HOSPITAL_URL","REGISTER_CODE","PACK_BAR_PREFIX","IF_ITEM_MANAGE","IF_TEMPLATE_HOS","MEDICAL_CONGENER","MEDICAL_CONGENER_ROLE","VEST_IN_DC","INSTANCE_ID","HOSPITAL_ATTRIBUTE","DATASYNC_TYPE","HGROUP_ID","CITEM_MANAGE" FROM "XH_SYS"."SYS6_HOSPITAL_INFO" 

2025-07-03 13:39:40.697 +08:00 [INF] 【SQL执行耗时:363.8483ms】

[Sql]:SELECT "LAB_ID","FIRST_RPERSON","LAB_NAME","LAB_ADDRESS","LAB_CODE","LAST_MTIME","LAB_SORT","LAB_RNAME","LAB_CLASS","LAB_PERSON","LINKMAN_TEL","REGISTER_CODE","LAB_LINKMAN","STATE_FLAG","REMARK","LAST_MPERSON","FIRST_RTIME","SPELL_CODE","LAB_TEL","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_LAB" 

2025-07-03 13:39:41.125 +08:00 [INF] 【SQL执行耗时:353.1212ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-03 13:39:41.582 +08:00 [INF] 【SQL执行耗时:385.5684ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP" 

2025-07-03 13:39:42.007 +08:00 [INF] 【SQL执行耗时:347.783ms】

[Sql]:SELECT "AREA_ID","AREA_CODE","AREA_SORT","AREA_NAME","AREA_CLASS","AREA_PERSON","AREA_ADDRESS","AREA_TEL","SPELL_CODE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","STATE_FLAG","REMARK","HOSPITAL_ID","TAG_SIGN","AREA_ANAME","PACK_BAR_PREFIX" FROM "XH_SYS"."SYS6_INSPECTION_AREA" 

2025-07-03 13:39:42.462 +08:00 [INF] 【SQL执行耗时:383.364ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_CLASS" = :EQUIPMENT_CLASS0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_CLASS0 [Value]:0 [Type]:String    

2025-07-03 13:39:42.907 +08:00 [INF] 【SQL执行耗时:367.0565ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 13:39:43.335 +08:00 [INF] 【SQL执行耗时:353.4725ms】

[Sql]:SELECT "HOSPITAL_ID","HOSPITAL_LOGIN","HOSPITAL_CNAME","HOSPITAL_SNAME","HOSPITAL_TYPE","HOSPITAL_LEVEL","LEVEL1_CODE","LEVEL2_CODE","LEVEL3_CODE","HOSPITAL_BARCODE","HOSPITAL_PROPERTY","HOSPITAL_PERSON","HOSPITAL_ADDRESS","HOSPITAL_TEL","HOSPITAL_STATE","FIRST_PERSON","FIRST_RTIME","LAST_MPRESON","LAST_MTIME","REMARK","HOSPITAL_BCODE","HOSPITAL_RANGE","BARCODE_TYPE","BARCODE_RANGE","CHARGE_PROVIDE_TYPE","DATADB_IP","WECHAT_NAME","LAST_MPERSON","FIRST_RPERSON","UPDATE_FLAG","INSERFACE_SERVICE","SAMELEVEL_HOSPITAL","HOSPITAL_CLASS","HOSPITAL_URL","REGISTER_CODE","PACK_BAR_PREFIX","IF_ITEM_MANAGE","IF_TEMPLATE_HOS","MEDICAL_CONGENER","MEDICAL_CONGENER_ROLE","VEST_IN_DC","INSTANCE_ID","HOSPITAL_ATTRIBUTE","DATASYNC_TYPE","HGROUP_ID","CITEM_MANAGE" FROM "XH_SYS"."SYS6_HOSPITAL_INFO" 

2025-07-03 13:39:43.765 +08:00 [INF] 【SQL执行耗时:358.6276ms】

[Sql]:SELECT "LAB_ID","FIRST_RPERSON","LAB_NAME","LAB_ADDRESS","LAB_CODE","LAST_MTIME","LAB_SORT","LAB_RNAME","LAB_CLASS","LAB_PERSON","LINKMAN_TEL","REGISTER_CODE","LAB_LINKMAN","STATE_FLAG","REMARK","LAST_MPERSON","FIRST_RTIME","SPELL_CODE","LAB_TEL","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_LAB" 

2025-07-03 13:39:44.310 +08:00 [INF] 【SQL执行耗时:473.5551ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-03 13:39:44.763 +08:00 [INF] 【SQL执行耗时:347.6293ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP" 

2025-07-03 13:39:45.284 +08:00 [INF] 【SQL执行耗时:450.4472ms】

[Sql]:SELECT "AREA_ID","AREA_CODE","AREA_SORT","AREA_NAME","AREA_CLASS","AREA_PERSON","AREA_ADDRESS","AREA_TEL","SPELL_CODE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","STATE_FLAG","REMARK","HOSPITAL_ID","TAG_SIGN","AREA_ANAME","PACK_BAR_PREFIX" FROM "XH_SYS"."SYS6_INSPECTION_AREA" 

2025-07-03 13:39:45.362 +08:00 [INF] HTTP GET /api/CodeCustom/GetEquipmentCodeCustomDict responded 200 in 26095.3457 ms
2025-07-03 13:39:45.363 +08:00 [INF] 【接口超时阀值预警】 [205ed5699299794580e7585515f69932]接口/api/CodeCustom/GetEquipmentCodeCustomDict,耗时:[26095]毫秒
2025-07-03 13:40:50.280 +08:00 [INF] 【SQL执行耗时:658.8478ms】

[Sql]:SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" <> :EqpNoState0 ) 
[Pars]:
[Name]::EqpNoState0 [Value]:2 [Type]:String    

2025-07-03 13:41:05.223 +08:00 [INF] 【SQL执行耗时:652.8234ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-03 13:41:10.867 +08:00 [INF] 【SQL执行耗时:362.7026ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_CLASS" = :EQUIPMENT_CLASS0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_CLASS0 [Value]:0 [Type]:String    

2025-07-03 13:41:11.280 +08:00 [INF] 【SQL执行耗时:336.196ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 13:41:11.711 +08:00 [INF] 【SQL执行耗时:360.1513ms】

[Sql]:SELECT "HOSPITAL_ID","HOSPITAL_LOGIN","HOSPITAL_CNAME","HOSPITAL_SNAME","HOSPITAL_TYPE","HOSPITAL_LEVEL","LEVEL1_CODE","LEVEL2_CODE","LEVEL3_CODE","HOSPITAL_BARCODE","HOSPITAL_PROPERTY","HOSPITAL_PERSON","HOSPITAL_ADDRESS","HOSPITAL_TEL","HOSPITAL_STATE","FIRST_PERSON","FIRST_RTIME","LAST_MPRESON","LAST_MTIME","REMARK","HOSPITAL_BCODE","HOSPITAL_RANGE","BARCODE_TYPE","BARCODE_RANGE","CHARGE_PROVIDE_TYPE","DATADB_IP","WECHAT_NAME","LAST_MPERSON","FIRST_RPERSON","UPDATE_FLAG","INSERFACE_SERVICE","SAMELEVEL_HOSPITAL","HOSPITAL_CLASS","HOSPITAL_URL","REGISTER_CODE","PACK_BAR_PREFIX","IF_ITEM_MANAGE","IF_TEMPLATE_HOS","MEDICAL_CONGENER","MEDICAL_CONGENER_ROLE","VEST_IN_DC","INSTANCE_ID","HOSPITAL_ATTRIBUTE","DATASYNC_TYPE","HGROUP_ID","CITEM_MANAGE" FROM "XH_SYS"."SYS6_HOSPITAL_INFO" 

2025-07-03 13:41:12.120 +08:00 [INF] 【SQL执行耗时:336.6177ms】

[Sql]:SELECT "LAB_ID","FIRST_RPERSON","LAB_NAME","LAB_ADDRESS","LAB_CODE","LAST_MTIME","LAB_SORT","LAB_RNAME","LAB_CLASS","LAB_PERSON","LINKMAN_TEL","REGISTER_CODE","LAB_LINKMAN","STATE_FLAG","REMARK","LAST_MPERSON","FIRST_RTIME","SPELL_CODE","LAB_TEL","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_LAB" 

2025-07-03 13:41:12.531 +08:00 [INF] 【SQL执行耗时:340.6464ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-03 13:41:12.971 +08:00 [INF] 【SQL执行耗时:370.2017ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP" 

2025-07-03 13:41:13.407 +08:00 [INF] 【SQL执行耗时:359.9204ms】

[Sql]:SELECT "AREA_ID","AREA_CODE","AREA_SORT","AREA_NAME","AREA_CLASS","AREA_PERSON","AREA_ADDRESS","AREA_TEL","SPELL_CODE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","STATE_FLAG","REMARK","HOSPITAL_ID","TAG_SIGN","AREA_ANAME","PACK_BAR_PREFIX" FROM "XH_SYS"."SYS6_INSPECTION_AREA" 

2025-07-03 13:49:57.132 +08:00 [INF] ==>App Start..2025-07-03 13:49:57
2025-07-03 13:49:57.301 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-03 13:49:57.305 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-03 13:49:58.789 +08:00 [INF] ==>基础连接请求完成.
2025-07-03 13:49:59.201 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-03 13:49:59.544 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-03 13:49:59.956 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-03 13:49:59.966 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-03 13:50:00.336 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-03 13:50:00.869 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-03 13:50:00.948 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-03 13:50:01.416 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-03 13:50:01.416 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-03 13:50:02.094 +08:00 [ERR] 查询出错:中文提示 :  连接数据库过程中发生错误，检查服务器是否正常连接字符串是否正确，错误信息：ORA-12545: Network Transport: Unable to resolve connect hostnameDbType="Oracle";ConfigId="".
English Message : Connection open error . ORA-12545: Network Transport: Unable to resolve connect hostnameDbType="Oracle";ConfigId="" :
SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON" FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "LAST_MTIME" IS NULL )
2025-07-03 13:50:17.923 +08:00 [INF] ==>App Start..2025-07-03 13:50:17
2025-07-03 13:50:18.088 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-03 13:50:18.091 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-03 13:50:19.497 +08:00 [INF] ==>基础连接请求完成.
2025-07-03 13:50:19.868 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-03 13:50:20.186 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-03 13:50:20.512 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-03 13:50:20.515 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-03 13:50:20.857 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-03 13:50:21.261 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-03 13:50:21.362 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-03 13:50:21.775 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-03 13:50:21.776 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-03 13:50:22.779 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-03 13:50:26.251 +08:00 [INF] ==>初始化完成..
2025-07-03 13:50:26.271 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-03 13:50:26.272 +08:00 [INF] 设备启用任务
2025-07-03 13:50:26.272 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-03 13:50:26.699 +08:00 [INF] 【SQL执行耗时:406.7242ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-03 13:50:26.829 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-03 13:50:26.842 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-03 13:50:26.843 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-03 13:50:26.843 +08:00 [INF] Hosting environment: Development
2025-07-03 13:50:26.844 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-03 13:50:32.567 +08:00 [INF] 【SQL执行耗时:353.8082ms】

[Sql]:SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" <> :EqpNoState0 ) 
[Pars]:
[Name]::EqpNoState0 [Value]:2 [Type]:String    

2025-07-03 13:50:33.284 +08:00 [INF] 【SQL执行耗时:352.1335ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-03 13:50:33.725 +08:00 [INF] 【SQL执行耗时:358.713ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_CLASS" = :EQUIPMENT_CLASS0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_CLASS0 [Value]:0 [Type]:String    

2025-07-03 13:50:34.169 +08:00 [INF] 【SQL执行耗时:370.6905ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 13:50:34.621 +08:00 [INF] 【SQL执行耗时:368.393ms】

[Sql]:SELECT "HOSPITAL_ID","HOSPITAL_LOGIN","HOSPITAL_CNAME","HOSPITAL_SNAME","HOSPITAL_TYPE","HOSPITAL_LEVEL","LEVEL1_CODE","LEVEL2_CODE","LEVEL3_CODE","HOSPITAL_BARCODE","HOSPITAL_PROPERTY","HOSPITAL_PERSON","HOSPITAL_ADDRESS","HOSPITAL_TEL","HOSPITAL_STATE","FIRST_PERSON","FIRST_RTIME","LAST_MPRESON","LAST_MTIME","REMARK","HOSPITAL_BCODE","HOSPITAL_RANGE","BARCODE_TYPE","BARCODE_RANGE","CHARGE_PROVIDE_TYPE","DATADB_IP","WECHAT_NAME","LAST_MPERSON","FIRST_RPERSON","UPDATE_FLAG","INSERFACE_SERVICE","SAMELEVEL_HOSPITAL","HOSPITAL_CLASS","HOSPITAL_URL","REGISTER_CODE","PACK_BAR_PREFIX","IF_ITEM_MANAGE","IF_TEMPLATE_HOS","MEDICAL_CONGENER","MEDICAL_CONGENER_ROLE","VEST_IN_DC","INSTANCE_ID","HOSPITAL_ATTRIBUTE","DATASYNC_TYPE","HGROUP_ID","CITEM_MANAGE" FROM "XH_SYS"."SYS6_HOSPITAL_INFO" 

2025-07-03 13:50:35.050 +08:00 [INF] 【SQL执行耗时:344.7538ms】

[Sql]:SELECT "LAB_ID","FIRST_RPERSON","LAB_NAME","LAB_ADDRESS","LAB_CODE","LAST_MTIME","LAB_SORT","LAB_RNAME","LAB_CLASS","LAB_PERSON","LINKMAN_TEL","REGISTER_CODE","LAB_LINKMAN","STATE_FLAG","REMARK","LAST_MPERSON","FIRST_RTIME","SPELL_CODE","LAB_TEL","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_LAB" 

2025-07-03 13:50:35.484 +08:00 [INF] 【SQL执行耗时:357.3984ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP" 

2025-07-03 13:50:35.994 +08:00 [INF] 【SQL执行耗时:395.3177ms】

[Sql]:SELECT "AREA_ID","AREA_CODE","AREA_SORT","AREA_NAME","AREA_CLASS","AREA_PERSON","AREA_ADDRESS","AREA_TEL","SPELL_CODE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","STATE_FLAG","REMARK","HOSPITAL_ID","TAG_SIGN","AREA_ANAME","PACK_BAR_PREFIX" FROM "XH_SYS"."SYS6_INSPECTION_AREA" 

2025-07-03 13:50:36.422 +08:00 [INF] 【SQL执行耗时:352.4892ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_CLASS" = :EQUIPMENT_CLASS0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_CLASS0 [Value]:0 [Type]:String    

2025-07-03 13:50:36.855 +08:00 [INF] 【SQL执行耗时:358.2299ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 13:50:37.288 +08:00 [INF] 【SQL执行耗时:357.2836ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_CLASS" = :EQUIPMENT_CLASS0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_CLASS0 [Value]:0 [Type]:String    

2025-07-03 13:50:37.718 +08:00 [INF] 【SQL执行耗时:355.79ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 13:50:38.133 +08:00 [INF] 【SQL执行耗时:340.8283ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_CLASS" = :EQUIPMENT_CLASS0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_CLASS0 [Value]:0 [Type]:String    

2025-07-03 13:50:38.555 +08:00 [INF] 【SQL执行耗时:350.5363ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 13:50:38.967 +08:00 [INF] 【SQL执行耗时:338.038ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_CLASS" = :EQUIPMENT_CLASS0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_CLASS0 [Value]:0 [Type]:String    

2025-07-03 13:50:39.408 +08:00 [INF] 【SQL执行耗时:369.7679ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 13:50:39.837 +08:00 [INF] 【SQL执行耗时:346.1359ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_CLASS" = :EQUIPMENT_CLASS0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_CLASS0 [Value]:0 [Type]:String    

2025-07-03 13:50:40.671 +08:00 [INF] 【SQL执行耗时:761.6523ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 13:50:41.097 +08:00 [INF] 【SQL执行耗时:353.9325ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_CLASS" = :EQUIPMENT_CLASS0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_CLASS0 [Value]:0 [Type]:String    

2025-07-03 13:50:41.575 +08:00 [INF] 【SQL执行耗时:403.9383ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 13:50:42.023 +08:00 [INF] 【SQL执行耗时:346.5872ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_CLASS" = :EQUIPMENT_CLASS0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_CLASS0 [Value]:0 [Type]:String    

2025-07-03 13:50:42.464 +08:00 [INF] 【SQL执行耗时:368.1453ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 13:50:42.567 +08:00 [INF] HTTP GET /api/CodeCustom/GetEquipmentCodeCustomDict responded 200 in 12890.4749 ms
2025-07-03 13:50:42.569 +08:00 [INF] 【接口超时阀值预警】 [5999f7457399d1110377a56a4944f2c7]接口/api/CodeCustom/GetEquipmentCodeCustomDict,耗时:[12897]毫秒
2025-07-03 13:50:49.711 +08:00 [INF] 【SQL执行耗时:341.2864ms】

[Sql]:SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" <> :EqpNoState0 ) 
[Pars]:
[Name]::EqpNoState0 [Value]:2 [Type]:String    

2025-07-03 13:50:50.410 +08:00 [INF] 【SQL执行耗时:358.4939ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-03 13:50:50.892 +08:00 [INF] 【SQL执行耗时:407.2262ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_CLASS" = :EQUIPMENT_CLASS0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_CLASS0 [Value]:0 [Type]:String    

2025-07-03 13:50:51.328 +08:00 [INF] 【SQL执行耗时:350.764ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 13:50:51.775 +08:00 [INF] 【SQL执行耗时:375.8373ms】

[Sql]:SELECT "HOSPITAL_ID","HOSPITAL_LOGIN","HOSPITAL_CNAME","HOSPITAL_SNAME","HOSPITAL_TYPE","HOSPITAL_LEVEL","LEVEL1_CODE","LEVEL2_CODE","LEVEL3_CODE","HOSPITAL_BARCODE","HOSPITAL_PROPERTY","HOSPITAL_PERSON","HOSPITAL_ADDRESS","HOSPITAL_TEL","HOSPITAL_STATE","FIRST_PERSON","FIRST_RTIME","LAST_MPRESON","LAST_MTIME","REMARK","HOSPITAL_BCODE","HOSPITAL_RANGE","BARCODE_TYPE","BARCODE_RANGE","CHARGE_PROVIDE_TYPE","DATADB_IP","WECHAT_NAME","LAST_MPERSON","FIRST_RPERSON","UPDATE_FLAG","INSERFACE_SERVICE","SAMELEVEL_HOSPITAL","HOSPITAL_CLASS","HOSPITAL_URL","REGISTER_CODE","PACK_BAR_PREFIX","IF_ITEM_MANAGE","IF_TEMPLATE_HOS","MEDICAL_CONGENER","MEDICAL_CONGENER_ROLE","VEST_IN_DC","INSTANCE_ID","HOSPITAL_ATTRIBUTE","DATASYNC_TYPE","HGROUP_ID","CITEM_MANAGE" FROM "XH_SYS"."SYS6_HOSPITAL_INFO" 

2025-07-03 13:50:52.228 +08:00 [INF] 【SQL执行耗时:376.1198ms】

[Sql]:SELECT "LAB_ID","FIRST_RPERSON","LAB_NAME","LAB_ADDRESS","LAB_CODE","LAST_MTIME","LAB_SORT","LAB_RNAME","LAB_CLASS","LAB_PERSON","LINKMAN_TEL","REGISTER_CODE","LAB_LINKMAN","STATE_FLAG","REMARK","LAST_MPERSON","FIRST_RTIME","SPELL_CODE","LAB_TEL","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_LAB" 

2025-07-03 13:50:52.665 +08:00 [INF] 【SQL执行耗时:365.224ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP" 

2025-07-03 13:50:53.096 +08:00 [INF] 【SQL执行耗时:358.4881ms】

[Sql]:SELECT "AREA_ID","AREA_CODE","AREA_SORT","AREA_NAME","AREA_CLASS","AREA_PERSON","AREA_ADDRESS","AREA_TEL","SPELL_CODE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","STATE_FLAG","REMARK","HOSPITAL_ID","TAG_SIGN","AREA_ANAME","PACK_BAR_PREFIX" FROM "XH_SYS"."SYS6_INSPECTION_AREA" 

2025-07-03 13:50:53.515 +08:00 [INF] 【SQL执行耗时:343.4929ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_CLASS" = :EQUIPMENT_CLASS0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_CLASS0 [Value]:0 [Type]:String    

2025-07-03 13:50:53.943 +08:00 [INF] 【SQL执行耗时:356.3589ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 13:50:54.363 +08:00 [INF] 【SQL执行耗时:344.7713ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_CLASS" = :EQUIPMENT_CLASS0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_CLASS0 [Value]:0 [Type]:String    

2025-07-03 13:50:54.776 +08:00 [INF] 【SQL执行耗时:341.0432ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 13:50:55.188 +08:00 [INF] 【SQL执行耗时:339.399ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_CLASS" = :EQUIPMENT_CLASS0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_CLASS0 [Value]:0 [Type]:String    

2025-07-03 13:50:55.601 +08:00 [INF] 【SQL执行耗时:341.3377ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 13:50:56.008 +08:00 [INF] 【SQL执行耗时:336.3512ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_CLASS" = :EQUIPMENT_CLASS0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_CLASS0 [Value]:0 [Type]:String    

2025-07-03 13:50:56.451 +08:00 [INF] 【SQL执行耗时:357.9578ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 13:50:56.888 +08:00 [INF] 【SQL执行耗时:362.4619ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_CLASS" = :EQUIPMENT_CLASS0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_CLASS0 [Value]:0 [Type]:String    

2025-07-03 13:50:57.313 +08:00 [INF] 【SQL执行耗时:348.6202ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 13:50:57.739 +08:00 [INF] 【SQL执行耗时:345.2196ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_CLASS" = :EQUIPMENT_CLASS0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_CLASS0 [Value]:0 [Type]:String    

2025-07-03 13:50:58.182 +08:00 [INF] 【SQL执行耗时:361.0058ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 13:50:58.615 +08:00 [INF] 【SQL执行耗时:355.5252ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_CLASS" = :EQUIPMENT_CLASS0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_CLASS0 [Value]:0 [Type]:String    

2025-07-03 13:50:59.049 +08:00 [INF] 【SQL执行耗时:358.9914ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 13:50:59.124 +08:00 [INF] HTTP GET /api/CodeCustom/GetEquipmentCodeCustomDict responded 200 in 9798.1489 ms
2025-07-03 13:50:59.124 +08:00 [INF] 【接口超时阀值预警】 [0d551561de4db5e3d5e6560502da6838]接口/api/CodeCustom/GetEquipmentCodeCustomDict,耗时:[9798]毫秒
2025-07-03 13:55:20.287 +08:00 [INF] 【SQL执行耗时:585.3498ms】

[Sql]:SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" <> :EqpNoState0 ) 
[Pars]:
[Name]::EqpNoState0 [Value]:2 [Type]:String    

2025-07-03 13:55:21.362 +08:00 [INF] 【SQL执行耗时:677.0737ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-03 13:55:21.828 +08:00 [INF] 【SQL执行耗时:368.4102ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 13:55:22.251 +08:00 [INF] 【SQL执行耗时:347.6983ms】

[Sql]:SELECT "HOSPITAL_ID","HOSPITAL_LOGIN","HOSPITAL_CNAME","HOSPITAL_SNAME","HOSPITAL_TYPE","HOSPITAL_LEVEL","LEVEL1_CODE","LEVEL2_CODE","LEVEL3_CODE","HOSPITAL_BARCODE","HOSPITAL_PROPERTY","HOSPITAL_PERSON","HOSPITAL_ADDRESS","HOSPITAL_TEL","HOSPITAL_STATE","FIRST_PERSON","FIRST_RTIME","LAST_MPRESON","LAST_MTIME","REMARK","HOSPITAL_BCODE","HOSPITAL_RANGE","BARCODE_TYPE","BARCODE_RANGE","CHARGE_PROVIDE_TYPE","DATADB_IP","WECHAT_NAME","LAST_MPERSON","FIRST_RPERSON","UPDATE_FLAG","INSERFACE_SERVICE","SAMELEVEL_HOSPITAL","HOSPITAL_CLASS","HOSPITAL_URL","REGISTER_CODE","PACK_BAR_PREFIX","IF_ITEM_MANAGE","IF_TEMPLATE_HOS","MEDICAL_CONGENER","MEDICAL_CONGENER_ROLE","VEST_IN_DC","INSTANCE_ID","HOSPITAL_ATTRIBUTE","DATASYNC_TYPE","HGROUP_ID","CITEM_MANAGE" FROM "XH_SYS"."SYS6_HOSPITAL_INFO" 

2025-07-03 13:55:22.669 +08:00 [INF] 【SQL执行耗时:345.8633ms】

[Sql]:SELECT "LAB_ID","FIRST_RPERSON","LAB_NAME","LAB_ADDRESS","LAB_CODE","LAST_MTIME","LAB_SORT","LAB_RNAME","LAB_CLASS","LAB_PERSON","LINKMAN_TEL","REGISTER_CODE","LAB_LINKMAN","STATE_FLAG","REMARK","LAST_MPERSON","FIRST_RTIME","SPELL_CODE","LAB_TEL","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_LAB" 

2025-07-03 13:55:23.388 +08:00 [INF] 【SQL执行耗时:647.6571ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP" 

2025-07-03 13:55:23.876 +08:00 [INF] 【SQL执行耗时:351.8248ms】

[Sql]:SELECT "AREA_ID","AREA_CODE","AREA_SORT","AREA_NAME","AREA_CLASS","AREA_PERSON","AREA_ADDRESS","AREA_TEL","SPELL_CODE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","STATE_FLAG","REMARK","HOSPITAL_ID","TAG_SIGN","AREA_ANAME","PACK_BAR_PREFIX" FROM "XH_SYS"."SYS6_INSPECTION_AREA" 

2025-07-03 13:55:24.303 +08:00 [INF] 【SQL执行耗时:351.9705ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 13:55:24.732 +08:00 [INF] 【SQL执行耗时:354.153ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 13:55:25.154 +08:00 [INF] 【SQL执行耗时:349.3394ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 13:55:25.603 +08:00 [INF] 【SQL执行耗时:369.1652ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 13:55:26.214 +08:00 [INF] 【SQL执行耗时:533.3488ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 13:55:26.707 +08:00 [INF] 【SQL执行耗时:362.2867ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 13:55:27.134 +08:00 [INF] 【SQL执行耗时:351.6422ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 13:55:27.208 +08:00 [INF] HTTP GET /api/CodeCustom/GetEquipmentCodeCustomDict responded 200 in 7548.7833 ms
2025-07-03 13:55:27.208 +08:00 [INF] 【接口超时阀值预警】 [bcdde14db3b68ba341f3fa1fbbea7571]接口/api/CodeCustom/GetEquipmentCodeCustomDict,耗时:[7549]毫秒
2025-07-03 13:55:30.141 +08:00 [INF] 【SQL执行耗时:369.5001ms】

[Sql]:SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" <> :EqpNoState0 ) 
[Pars]:
[Name]::EqpNoState0 [Value]:2 [Type]:String    

2025-07-03 13:55:30.904 +08:00 [INF] 【SQL执行耗时:381.6961ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-03 13:55:31.321 +08:00 [INF] 【SQL执行耗时:340.1293ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 13:55:31.750 +08:00 [INF] 【SQL执行耗时:357.8219ms】

[Sql]:SELECT "HOSPITAL_ID","HOSPITAL_LOGIN","HOSPITAL_CNAME","HOSPITAL_SNAME","HOSPITAL_TYPE","HOSPITAL_LEVEL","LEVEL1_CODE","LEVEL2_CODE","LEVEL3_CODE","HOSPITAL_BARCODE","HOSPITAL_PROPERTY","HOSPITAL_PERSON","HOSPITAL_ADDRESS","HOSPITAL_TEL","HOSPITAL_STATE","FIRST_PERSON","FIRST_RTIME","LAST_MPRESON","LAST_MTIME","REMARK","HOSPITAL_BCODE","HOSPITAL_RANGE","BARCODE_TYPE","BARCODE_RANGE","CHARGE_PROVIDE_TYPE","DATADB_IP","WECHAT_NAME","LAST_MPERSON","FIRST_RPERSON","UPDATE_FLAG","INSERFACE_SERVICE","SAMELEVEL_HOSPITAL","HOSPITAL_CLASS","HOSPITAL_URL","REGISTER_CODE","PACK_BAR_PREFIX","IF_ITEM_MANAGE","IF_TEMPLATE_HOS","MEDICAL_CONGENER","MEDICAL_CONGENER_ROLE","VEST_IN_DC","INSTANCE_ID","HOSPITAL_ATTRIBUTE","DATASYNC_TYPE","HGROUP_ID","CITEM_MANAGE" FROM "XH_SYS"."SYS6_HOSPITAL_INFO" 

2025-07-03 13:55:32.190 +08:00 [INF] 【SQL执行耗时:370.8168ms】

[Sql]:SELECT "LAB_ID","FIRST_RPERSON","LAB_NAME","LAB_ADDRESS","LAB_CODE","LAST_MTIME","LAB_SORT","LAB_RNAME","LAB_CLASS","LAB_PERSON","LINKMAN_TEL","REGISTER_CODE","LAB_LINKMAN","STATE_FLAG","REMARK","LAST_MPERSON","FIRST_RTIME","SPELL_CODE","LAB_TEL","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_LAB" 

2025-07-03 13:55:32.619 +08:00 [INF] 【SQL执行耗时:352.6337ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP" 

2025-07-03 13:55:33.034 +08:00 [INF] 【SQL执行耗时:343.9457ms】

[Sql]:SELECT "AREA_ID","AREA_CODE","AREA_SORT","AREA_NAME","AREA_CLASS","AREA_PERSON","AREA_ADDRESS","AREA_TEL","SPELL_CODE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","STATE_FLAG","REMARK","HOSPITAL_ID","TAG_SIGN","AREA_ANAME","PACK_BAR_PREFIX" FROM "XH_SYS"."SYS6_INSPECTION_AREA" 

2025-07-03 13:55:33.465 +08:00 [INF] 【SQL执行耗时:358.6313ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 13:55:33.899 +08:00 [INF] 【SQL执行耗时:360.4367ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 13:55:34.337 +08:00 [INF] 【SQL执行耗时:363.4104ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 13:55:34.775 +08:00 [INF] 【SQL执行耗时:355.9586ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 13:55:35.210 +08:00 [INF] 【SQL执行耗时:357.8005ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 13:55:35.638 +08:00 [INF] 【SQL执行耗时:350.3084ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 13:55:36.208 +08:00 [INF] 【SQL执行耗时:496.2549ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 13:55:36.349 +08:00 [INF] HTTP GET /api/CodeCustom/GetEquipmentCodeCustomDict responded 200 in 6615.5866 ms
2025-07-03 13:55:36.350 +08:00 [INF] 【接口超时阀值预警】 [67dc9a4fc75df19a2fc132f1d028a612]接口/api/CodeCustom/GetEquipmentCodeCustomDict,耗时:[6616]毫秒
2025-07-03 13:59:32.574 +08:00 [INF] ==>App Start..2025-07-03 13:59:32
2025-07-03 13:59:32.746 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-03 13:59:32.750 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-03 13:59:34.292 +08:00 [INF] ==>基础连接请求完成.
2025-07-03 13:59:34.667 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-03 13:59:35.047 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-03 13:59:35.373 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-03 13:59:35.375 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-03 13:59:35.412 +08:00 [ERR] 调用H07检测版本依赖时发生错误:Index and length must refer to a location within the string. (Parameter 'length')
System.ArgumentOutOfRangeException: Index and length must refer to a location within the string. (Parameter 'length')
   at System.String.Substring(Int32 startIndex, Int32 length)
   at Spectre.Console.Rendering.Segment.SplitOverflow(Segment segment, Nullable`1 overflow, Int32 maxWidth) in /_/src/Spectre.Console/Rendering/Segment.cs:line 351
   at Spectre.Console.Paragraph.SplitLines(Int32 maxWidth) in /_/src/Spectre.Console/Widgets/Paragraph.cs:line 242
   at Spectre.Console.Paragraph.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Widgets/Paragraph.cs:line 144
   at Spectre.Console.Rendering.Renderable.Spectre.Console.Rendering.IRenderable.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Rendering/Renderable.cs:line 19
   at Spectre.Console.Markup.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Widgets/Markup.cs:line 54
   at Spectre.Console.Rendering.Renderable.Spectre.Console.Rendering.IRenderable.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Rendering/Renderable.cs:line 19
   at Spectre.Console.TableRenderer.Render(TableRendererContext context, List`1 columnWidths) in /_/src/Spectre.Console/Widgets/Table/TableRenderer.cs:line 31
   at Spectre.Console.Table.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Widgets/Table/Table.cs:line 141
   at Spectre.Console.Rendering.Renderable.Spectre.Console.Rendering.IRenderable.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Rendering/Renderable.cs:line 19
   at Spectre.Console.RenderableExtensions.GetSegments(IAnsiConsole console, RenderContext options, IEnumerable`1 renderables) in /_/src/Spectre.Console/Extensions/RenderableExtensions.cs:line 37
   at Spectre.Console.RenderableExtensions.GetSegments(IRenderable renderable, IAnsiConsole console) in /_/src/Spectre.Console/Extensions/RenderableExtensions.cs:line 29
   at Spectre.Console.AnsiBuilder.Build(IAnsiConsole console, IRenderable renderable) in /_/src/Spectre.Console/Internal/Backends/Ansi/AnsiBuilder.cs:line 17
   at Spectre.Console.AnsiConsoleBackend.Write(IRenderable renderable) in /_/src/Spectre.Console/Internal/Backends/Ansi/AnsiConsoleBackend.cs:line 30
   at Spectre.Console.AnsiConsoleFacade.Write(IRenderable renderable) in /_/src/Spectre.Console/Internal/Backends/AnsiConsoleFacade.cs:line 40
   at Spectre.Console.AnsiConsole.Write(IRenderable renderable) in /_/src/Spectre.Console/AnsiConsole.Rendering.cs:line 29
   at H.BASE.AppInit.<RegisterInfraServer>b__22_0()
2025-07-03 13:59:35.729 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-03 13:59:36.205 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-03 13:59:36.318 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-03 13:59:36.755 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-03 13:59:36.755 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-03 13:59:37.654 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-03 13:59:39.897 +08:00 [INF] ==>初始化完成..
2025-07-03 13:59:39.918 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-03 13:59:39.922 +08:00 [INF] 设备启用任务
2025-07-03 13:59:39.922 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-03 13:59:40.315 +08:00 [INF] 【SQL执行耗时:370.5448ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-03 13:59:40.453 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-03 13:59:40.465 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-03 13:59:40.466 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-03 13:59:40.466 +08:00 [INF] Hosting environment: Development
2025-07-03 13:59:40.466 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-03 13:59:40.984 +08:00 [INF] HTTP GET /api/CodeCustom/GetEquipmentCodeCustomDict responded 401 in 273.1561 ms
2025-07-03 13:59:54.652 +08:00 [INF] HTTP GET /api/CodeCustom/GetEquipmentCodeCustomDict responded 401 in 7.8732 ms
2025-07-03 13:59:56.305 +08:00 [INF] HTTP GET /api/CodeCustom/GetEquipmentCodeCustomDict responded 401 in 0.8066 ms
2025-07-03 13:59:56.618 +08:00 [INF] HTTP GET /api/CodeCustom/GetEquipmentCodeCustomDict responded 401 in 2.0501 ms
2025-07-03 13:59:56.768 +08:00 [INF] HTTP GET /api/CodeCustom/GetEquipmentCodeCustomDict responded 401 in 0.8146 ms
2025-07-03 14:00:10.393 +08:00 [INF] HTTP GET /api/CodeCustom/GetCustomDictCode responded 401 in 0.7972 ms
2025-07-03 14:00:24.556 +08:00 [INF] HTTP GET /api/CodeCustom/GetCustomDictCode responded 200 in 3209.0965 ms
2025-07-03 14:00:24.559 +08:00 [INF] 【接口超时阀值预警】 [da885bbaf79b4bbe31d313ad9a1d0067]接口/api/CodeCustom/GetCustomDictCode,耗时:[3210]毫秒
2025-07-03 14:00:26.218 +08:00 [INF] HTTP GET /api/CodeCustom/GetCustomDictCode responded 200 in 39.8122 ms
2025-07-03 14:00:30.986 +08:00 [INF] 【SQL执行耗时:425.2148ms】

[Sql]:SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" <> :EqpNoState0 ) 
[Pars]:
[Name]::EqpNoState0 [Value]:2 [Type]:String    

2025-07-03 14:00:31.678 +08:00 [INF] 【SQL执行耗时:331.3825ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-03 14:00:32.131 +08:00 [INF] 【SQL执行耗时:376.0152ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 14:00:32.570 +08:00 [INF] 【SQL执行耗时:350.4928ms】

[Sql]:SELECT "HOSPITAL_ID","HOSPITAL_LOGIN","HOSPITAL_CNAME","HOSPITAL_SNAME","HOSPITAL_TYPE","HOSPITAL_LEVEL","LEVEL1_CODE","LEVEL2_CODE","LEVEL3_CODE","HOSPITAL_BARCODE","HOSPITAL_PROPERTY","HOSPITAL_PERSON","HOSPITAL_ADDRESS","HOSPITAL_TEL","HOSPITAL_STATE","FIRST_PERSON","FIRST_RTIME","LAST_MPRESON","LAST_MTIME","REMARK","HOSPITAL_BCODE","HOSPITAL_RANGE","BARCODE_TYPE","BARCODE_RANGE","CHARGE_PROVIDE_TYPE","DATADB_IP","WECHAT_NAME","LAST_MPERSON","FIRST_RPERSON","UPDATE_FLAG","INSERFACE_SERVICE","SAMELEVEL_HOSPITAL","HOSPITAL_CLASS","HOSPITAL_URL","REGISTER_CODE","PACK_BAR_PREFIX","IF_ITEM_MANAGE","IF_TEMPLATE_HOS","MEDICAL_CONGENER","MEDICAL_CONGENER_ROLE","VEST_IN_DC","INSTANCE_ID","HOSPITAL_ATTRIBUTE","DATASYNC_TYPE","HGROUP_ID","CITEM_MANAGE" FROM "XH_SYS"."SYS6_HOSPITAL_INFO" 

2025-07-03 14:00:33.700 +08:00 [INF] 【SQL执行耗时:1049.8477ms】

[Sql]:SELECT "LAB_ID","FIRST_RPERSON","LAB_NAME","LAB_ADDRESS","LAB_CODE","LAST_MTIME","LAB_SORT","LAB_RNAME","LAB_CLASS","LAB_PERSON","LINKMAN_TEL","REGISTER_CODE","LAB_LINKMAN","STATE_FLAG","REMARK","LAST_MPERSON","FIRST_RTIME","SPELL_CODE","LAB_TEL","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_LAB" 

2025-07-03 14:00:34.283 +08:00 [INF] 【SQL执行耗时:358.5665ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP" 

2025-07-03 14:00:34.724 +08:00 [INF] 【SQL执行耗时:360.6296ms】

[Sql]:SELECT "AREA_ID","AREA_CODE","AREA_SORT","AREA_NAME","AREA_CLASS","AREA_PERSON","AREA_ADDRESS","AREA_TEL","SPELL_CODE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","STATE_FLAG","REMARK","HOSPITAL_ID","TAG_SIGN","AREA_ANAME","PACK_BAR_PREFIX" FROM "XH_SYS"."SYS6_INSPECTION_AREA" 

2025-07-03 14:00:35.236 +08:00 [INF] 【SQL执行耗时:433.6759ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 14:00:35.698 +08:00 [INF] 【SQL执行耗时:366.2432ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 14:00:36.116 +08:00 [INF] 【SQL执行耗时:340.3544ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 14:00:36.520 +08:00 [INF] 【SQL执行耗时:333.8305ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 14:00:36.965 +08:00 [INF] 【SQL执行耗时:373.9268ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 14:00:37.385 +08:00 [INF] 【SQL执行耗时:342.0316ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 14:00:37.847 +08:00 [INF] 【SQL执行耗时:390.2038ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 14:00:37.932 +08:00 [INF] HTTP GET /api/CodeCustom/GetEquipmentCodeCustomDict responded 200 in 7441.0482 ms
2025-07-03 14:00:37.932 +08:00 [INF] 【接口超时阀值预警】 [4a8e3f339d74ad831f119497dd266140]接口/api/CodeCustom/GetEquipmentCodeCustomDict,耗时:[7441]毫秒
2025-07-03 14:00:40.331 +08:00 [INF] 【SQL执行耗时:444.3846ms】

[Sql]:SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" <> :EqpNoState0 ) 
[Pars]:
[Name]::EqpNoState0 [Value]:2 [Type]:String    

2025-07-03 14:00:41.074 +08:00 [INF] 【SQL执行耗时:365.3421ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-03 14:00:41.516 +08:00 [INF] 【SQL执行耗时:365.0893ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 14:00:42.120 +08:00 [INF] 【SQL执行耗时:528.1013ms】

[Sql]:SELECT "HOSPITAL_ID","HOSPITAL_LOGIN","HOSPITAL_CNAME","HOSPITAL_SNAME","HOSPITAL_TYPE","HOSPITAL_LEVEL","LEVEL1_CODE","LEVEL2_CODE","LEVEL3_CODE","HOSPITAL_BARCODE","HOSPITAL_PROPERTY","HOSPITAL_PERSON","HOSPITAL_ADDRESS","HOSPITAL_TEL","HOSPITAL_STATE","FIRST_PERSON","FIRST_RTIME","LAST_MPRESON","LAST_MTIME","REMARK","HOSPITAL_BCODE","HOSPITAL_RANGE","BARCODE_TYPE","BARCODE_RANGE","CHARGE_PROVIDE_TYPE","DATADB_IP","WECHAT_NAME","LAST_MPERSON","FIRST_RPERSON","UPDATE_FLAG","INSERFACE_SERVICE","SAMELEVEL_HOSPITAL","HOSPITAL_CLASS","HOSPITAL_URL","REGISTER_CODE","PACK_BAR_PREFIX","IF_ITEM_MANAGE","IF_TEMPLATE_HOS","MEDICAL_CONGENER","MEDICAL_CONGENER_ROLE","VEST_IN_DC","INSTANCE_ID","HOSPITAL_ATTRIBUTE","DATASYNC_TYPE","HGROUP_ID","CITEM_MANAGE" FROM "XH_SYS"."SYS6_HOSPITAL_INFO" 

2025-07-03 14:00:43.002 +08:00 [INF] 【SQL执行耗时:768.0983ms】

[Sql]:SELECT "LAB_ID","FIRST_RPERSON","LAB_NAME","LAB_ADDRESS","LAB_CODE","LAST_MTIME","LAB_SORT","LAB_RNAME","LAB_CLASS","LAB_PERSON","LINKMAN_TEL","REGISTER_CODE","LAB_LINKMAN","STATE_FLAG","REMARK","LAST_MPERSON","FIRST_RTIME","SPELL_CODE","LAB_TEL","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_LAB" 

2025-07-03 14:00:43.459 +08:00 [INF] 【SQL执行耗时:371.0135ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP" 

2025-07-03 14:00:43.923 +08:00 [INF] 【SQL执行耗时:360.6068ms】

[Sql]:SELECT "AREA_ID","AREA_CODE","AREA_SORT","AREA_NAME","AREA_CLASS","AREA_PERSON","AREA_ADDRESS","AREA_TEL","SPELL_CODE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","STATE_FLAG","REMARK","HOSPITAL_ID","TAG_SIGN","AREA_ANAME","PACK_BAR_PREFIX" FROM "XH_SYS"."SYS6_INSPECTION_AREA" 

2025-07-03 14:01:01.217 +08:00 [INF] 【SQL执行耗时:354.6926ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 14:01:04.934 +08:00 [INF] HTTP GET /api/CodeCustom/GetEquipmentCodeCustomDict responded 200 in 25085.2587 ms
2025-07-03 14:01:04.935 +08:00 [INF] 【接口超时阀值预警】 [6ecf802fbde2f91914388188a21dfd6a]接口/api/CodeCustom/GetEquipmentCodeCustomDict,耗时:[25085]毫秒
2025-07-03 14:01:07.507 +08:00 [INF] 【SQL执行耗时:442.9752ms】

[Sql]:SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" <> :EqpNoState0 ) 
[Pars]:
[Name]::EqpNoState0 [Value]:2 [Type]:String    

2025-07-03 14:01:08.207 +08:00 [INF] 【SQL执行耗时:334.9096ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-03 14:01:08.276 +08:00 [INF] HTTP GET /api/CodeCustom/GetEquipmentCodeCustomDict responded 200 in 1251.8894 ms
2025-07-03 14:01:08.277 +08:00 [INF] 【接口超时阀值预警】 [86debc65b00e314bd2c1361c3f9df995]接口/api/CodeCustom/GetEquipmentCodeCustomDict,耗时:[1252]毫秒
2025-07-03 14:01:18.707 +08:00 [INF] 【SQL执行耗时:337.7053ms】

[Sql]:SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" <> :EqpNoState0 ) 
[Pars]:
[Name]::EqpNoState0 [Value]:2 [Type]:String    

2025-07-03 14:01:19.386 +08:00 [INF] 【SQL执行耗时:340.2075ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-03 14:01:19.456 +08:00 [INF] HTTP GET /api/CodeCustom/GetEquipmentCodeCustomDict responded 200 in 1123.4385 ms
2025-07-03 14:01:19.457 +08:00 [INF] 【接口超时阀值预警】 [7bae5c152b19dc9166730819b7ad232c]接口/api/CodeCustom/GetEquipmentCodeCustomDict,耗时:[1123]毫秒
2025-07-03 14:01:28.002 +08:00 [INF] 【SQL执行耗时:366.1822ms】

[Sql]:SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" <> :EqpNoState0 ) 
[Pars]:
[Name]::EqpNoState0 [Value]:2 [Type]:String    

2025-07-03 14:01:28.727 +08:00 [INF] 【SQL执行耗时:354.9802ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-03 14:01:28.800 +08:00 [INF] HTTP GET /api/CodeCustom/GetEquipmentCodeCustomDict responded 200 in 1201.3045 ms
2025-07-03 14:01:28.800 +08:00 [INF] 【接口超时阀值预警】 [ccf72cc901dae1ec94c1bd041747fc5a]接口/api/CodeCustom/GetEquipmentCodeCustomDict,耗时:[1201]毫秒
2025-07-03 14:01:49.604 +08:00 [INF] 【SQL执行耗时:342.5568ms】

[Sql]:SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" <> :EqpNoState0 ) 
[Pars]:
[Name]::EqpNoState0 [Value]:2 [Type]:String    

2025-07-03 14:01:50.297 +08:00 [INF] 【SQL执行耗时:347.851ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-03 14:01:50.380 +08:00 [INF] HTTP GET /api/CodeCustom/GetEquipmentCodeCustomDict responded 200 in 1161.9324 ms
2025-07-03 14:01:50.380 +08:00 [INF] 【接口超时阀值预警】 [3408c28664beba9a8e7971b8792f2ae6]接口/api/CodeCustom/GetEquipmentCodeCustomDict,耗时:[1162]毫秒
2025-07-03 14:10:33.267 +08:00 [INF] ==>App Start..2025-07-03 14:10:33
2025-07-03 14:10:33.432 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-03 14:10:33.436 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-03 14:10:34.832 +08:00 [INF] ==>基础连接请求完成.
2025-07-03 14:10:35.201 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-03 14:10:35.539 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-03 14:10:35.855 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-03 14:10:35.862 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-03 14:10:36.227 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-03 14:10:36.628 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-03 14:10:36.724 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-03 14:10:37.136 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-03 14:10:37.136 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-03 14:10:38.439 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-03 14:10:40.644 +08:00 [INF] ==>初始化完成..
2025-07-03 14:10:40.665 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-03 14:10:40.667 +08:00 [INF] 设备启用任务
2025-07-03 14:10:40.668 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-03 14:10:41.064 +08:00 [INF] 【SQL执行耗时:375.1159ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-03 14:10:41.208 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-03 14:10:41.224 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-03 14:10:41.225 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-03 14:10:41.226 +08:00 [INF] Hosting environment: Development
2025-07-03 14:10:41.226 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-03 14:11:02.566 +08:00 [INF] ==>App Start..2025-07-03 14:11:02
2025-07-03 14:11:02.746 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-03 14:11:02.750 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-03 14:11:04.207 +08:00 [INF] ==>基础连接请求完成.
2025-07-03 14:11:04.592 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-03 14:11:04.923 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-03 14:11:05.325 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-03 14:11:05.327 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-03 14:11:05.680 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-03 14:11:06.003 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-03 14:11:06.058 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-03 14:11:06.467 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-03 14:11:06.467 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-03 14:11:07.352 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-03 14:11:09.610 +08:00 [INF] ==>初始化完成..
2025-07-03 14:11:09.629 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-03 14:11:09.631 +08:00 [INF] 设备启用任务
2025-07-03 14:11:09.632 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-03 14:11:10.023 +08:00 [INF] 【SQL执行耗时:370.519ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-03 14:11:10.171 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-03 14:11:10.184 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-03 14:11:10.186 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-03 14:11:10.186 +08:00 [INF] Hosting environment: Development
2025-07-03 14:11:10.186 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-03 14:43:53.316 +08:00 [INF] ==>App Start..2025-07-03 14:43:53
2025-07-03 14:43:53.487 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-03 14:43:53.490 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-03 14:43:56.595 +08:00 [INF] ==>基础连接请求完成.
2025-07-03 14:43:56.971 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-03 14:43:57.304 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-03 14:43:57.895 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-03 14:43:57.906 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-03 14:43:58.268 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-03 14:43:58.539 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-03 14:43:58.616 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-03 14:43:59.071 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-03 14:43:59.071 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-03 14:44:00.849 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-03 14:44:04.318 +08:00 [INF] ==>初始化完成..
2025-07-03 14:44:04.337 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-03 14:44:04.338 +08:00 [INF] 设备启用任务
2025-07-03 14:44:04.339 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-03 14:44:04.741 +08:00 [INF] 【SQL执行耗时:380.3198ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-03 14:44:04.882 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-03 14:44:04.894 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-03 14:44:04.895 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-03 14:44:04.896 +08:00 [INF] Hosting environment: Development
2025-07-03 14:44:04.896 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-03 14:44:11.709 +08:00 [INF] 【SQL执行耗时:444.5789ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-03 14:44:12.435 +08:00 [INF] 【SQL执行耗时:581.2009ms】

[Sql]:SELECT "HOSPITAL_ID","HOSPITAL_LOGIN","HOSPITAL_CNAME","HOSPITAL_SNAME","HOSPITAL_TYPE","HOSPITAL_LEVEL","LEVEL1_CODE","LEVEL2_CODE","LEVEL3_CODE","HOSPITAL_BARCODE","HOSPITAL_PROPERTY","HOSPITAL_PERSON","HOSPITAL_ADDRESS","HOSPITAL_TEL","HOSPITAL_STATE","FIRST_PERSON","FIRST_RTIME","LAST_MPRESON","LAST_MTIME","REMARK","HOSPITAL_BCODE","HOSPITAL_RANGE","BARCODE_TYPE","BARCODE_RANGE","CHARGE_PROVIDE_TYPE","DATADB_IP","WECHAT_NAME","LAST_MPERSON","FIRST_RPERSON","UPDATE_FLAG","INSERFACE_SERVICE","SAMELEVEL_HOSPITAL","HOSPITAL_CLASS","HOSPITAL_URL","REGISTER_CODE","PACK_BAR_PREFIX","IF_ITEM_MANAGE","IF_TEMPLATE_HOS","MEDICAL_CONGENER","MEDICAL_CONGENER_ROLE","VEST_IN_DC","INSTANCE_ID","HOSPITAL_ATTRIBUTE","DATASYNC_TYPE","HGROUP_ID","CITEM_MANAGE" FROM "XH_SYS"."SYS6_HOSPITAL_INFO" 

2025-07-03 14:44:13.141 +08:00 [INF] 【SQL执行耗时:624.5468ms】

[Sql]:SELECT "LAB_ID","FIRST_RPERSON","LAB_NAME","LAB_ADDRESS","LAB_CODE","LAST_MTIME","LAB_SORT","LAB_RNAME","LAB_CLASS","LAB_PERSON","LINKMAN_TEL","REGISTER_CODE","LAB_LINKMAN","STATE_FLAG","REMARK","LAST_MPERSON","FIRST_RTIME","SPELL_CODE","LAB_TEL","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_LAB" 

2025-07-03 14:44:13.702 +08:00 [INF] 【SQL执行耗时:438.8992ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-03 14:44:14.390 +08:00 [INF] 【SQL执行耗时:588.6986ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP" 

2025-07-03 14:44:14.827 +08:00 [INF] 【SQL执行耗时:359.0528ms】

[Sql]:SELECT "AREA_ID","AREA_CODE","AREA_SORT","AREA_NAME","AREA_CLASS","AREA_PERSON","AREA_ADDRESS","AREA_TEL","SPELL_CODE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","STATE_FLAG","REMARK","HOSPITAL_ID","TAG_SIGN","AREA_ANAME","PACK_BAR_PREFIX" FROM "XH_SYS"."SYS6_INSPECTION_AREA" 

2025-07-03 14:44:14.933 +08:00 [INF] HTTP POST /api/CodeCustom/GetEquipmentUCodePreview/0 responded 200 in 8641.2304 ms
2025-07-03 14:44:14.935 +08:00 [INF] 【接口超时阀值预警】 [f0208c9d7c0764e3f2e7d2aacd39ef1d]接口/api/CodeCustom/GetEquipmentUCodePreview/0,耗时:[8647]毫秒
