﻿using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using XH.H82.Models.Entities.OperationLog;
using XH.H82.Models.SugarDbContext;
using XH.LAB.UTILS.Models;

namespace XH.H82.Services.OperationLog
{
    /// <summary>
    /// 操作记录上下文
    /// </summary>
    public class DataRecordCentext
    {
        /// <summary>
        /// 数据主键
        /// </summary>IDGenHelper.GenUniqIDBySnowFlake();
        private string _dataId { get; set; }
        /// <summary>
        /// 当前操作人
        /// </summary>
        private ClaimsDto _user { get; set; }

        private readonly ISqlSugarUow<SugarDbContext_Master> _dbContext;

        /// <summary>
        /// 当前数据主键的最近记录的上一次记录ID
        /// </summary>
        private string _prevOperId { get; set; } = "0";

        /// <summary>
        /// 上一次的操作记录
        /// </summary>
        private EMS_OPER_LOG _preCirculationRecord { get; set; }

        public DataRecordCentext(ClaimsDto user, string dataId, ISqlSugarUow<SugarDbContext_Master> dbContext)
        {
            _dataId = dataId;
            _dbContext = dbContext;
            _user = user;
            _dbContext.SetCreateTimeAndCreatePersonData(user);
            FindPrevOperId(dataId);
        }


        /// <summary>
        /// 未提交的操作记录
        /// </summary>
        /// <param name="nextUserID"></param>
        /// <param name="nextUserName"></param>
        /// <param name="content"></param>
        public void CreateNotSubmitedRecode(string nextUserID, string nextUserName, string? content)
        {
            CheckRecodeState(OperationStateEnum.NotSubmitted);
            CreateRecode(nextUserID, nextUserName, "未提交", OperationStateEnum.NotSubmitted, content);
        }

        /// <summary>
        /// 提交的操作记录
        /// </summary>
        /// <param name="nextUserID"></param>
        /// <param name="nextUserName"></param>
        /// <param name="content"></param>
        public void CreateSubmitedRecode(string nextUserID, string nextUserName, string? content)
        {
            CheckRecodeState(OperationStateEnum.Submitted);
            if (_dbContext.Db.Queryable<EMS_OPER_LOG>().Where(x => x.OPER_MAIN_ID == _dataId).Where(x => x.OPER_STATE == OperationStateEnum.Overruled).Count() > 0)
            {
                CreateRecode(nextUserID, nextUserName, "重新提交", OperationStateEnum.Submitted, content);
            }
            else
            {
                CreateRecode(nextUserID, nextUserName, "提交", OperationStateEnum.Submitted, null);
            }
        }

        /// <summary>
        /// 驳回操作  （流程指派给上一个流程的负责人）
        /// </summary>
        /// <param name="content"></param>
        public void CreateOverruledRecode(string? content)
        {
            var nextUserID = _preCirculationRecord.OPER_PERSON_ID;
            var nextUserName = _preCirculationRecord.OPER_PERSON;
            CreateOverruledRecode(nextUserID, nextUserName, content);
        }


        /// <summary>
        /// 驳回的操作 (流程指派给其他人)
        /// </summary>
        /// <param name="nextUserID"></param>
        /// <param name="nextUserName"></param>
        /// <param name="content"></param>
        public void CreateOverruledRecode(string nextUserID, string nextUserName, string? content)
        {
            CheckRecodeState(OperationStateEnum.Overruled);
            CreateRecode(nextUserID, nextUserName, "已驳回", OperationStateEnum.Overruled, content);
        }

        /// <summary>
        /// 审核操作
        /// </summary>
        /// <param name="nextUserID"></param>
        /// <param name="nextUserName"></param>
        /// <param name="content"></param>
        public void CreateAuditdRecode(string nextUserID, string nextUserName, string? content)
        {
            CheckRecodeState(OperationStateEnum.Audited);
            CreateRecode(nextUserID, nextUserName, "已审核", OperationStateEnum.Audited, content);
        }




        /// <summary>
        /// 状态变化控制检查
        /// </summary>
        /// <param name="nextState"></param>
        private void CheckRecodeState(OperationStateEnum nextState)
        {
            CheckRecodeOperator(nextState);
            switch (nextState)
            {
                case OperationStateEnum.NotSubmitted:
                    break;
                case OperationStateEnum.Overruled:
                    if (_preCirculationRecord.OPER_STATE == OperationStateEnum.Submitted || _preCirculationRecord.OPER_STATE == OperationStateEnum.Audited)
                    {
                        break;
                    }
                    else
                    {
                        throw new BizException("当前状态不可以被驳回");
                    }
                case OperationStateEnum.Submitted:
                    if (_preCirculationRecord.OPER_STATE == OperationStateEnum.NotSubmitted || _preCirculationRecord.OPER_STATE == OperationStateEnum.Overruled)
                    {
                        break;
                    }
                    else
                    {
                        throw new BizException("当前状态不可以被提交");
                    }
                case OperationStateEnum.Audited:
                    if (_preCirculationRecord.OPER_STATE == OperationStateEnum.Submitted)
                    {
                        break;
                    }
                    else
                    {
                        throw new BizException("当前状态不可以被审核");
                    }
                default:
                    break;
            }

        }

        /// <summary>
        /// 检查下一个指向
        /// </summary>
        /// <exception cref="Exception"></exception>
        private void CheckRecodeOperator(OperationStateEnum nextState)
        {
            if (nextState is OperationStateEnum.Overruled)
            {
                if (!(_preCirculationRecord.NEXT_OPER_PERSON_ID == _user.USER_NO || _preCirculationRecord.OPER_PERSON_ID == _user.USER_NO))
                {
                    throw new BizException("当前用户无权限操作此数据");
                }
            }

            else if  (nextState is OperationStateEnum.Submitted)
            {
                if (_preCirculationRecord.OPER_STATE is not OperationStateEnum.Overruled)
                {
                    if (!(_preCirculationRecord.NEXT_OPER_PERSON_ID == _user.USER_NO))
                    {
                        throw new BizException("当前用户无权限操作此数据");
                    }
                }
            }
            else
            {
                if (!(_preCirculationRecord.NEXT_OPER_PERSON_ID == _user.USER_NO))
                {
                    throw new BizException("当前用户无权限操作此数据");
                }

            }
        }

        /// <summary>
        /// 查询数据的上次操作记录
        /// </summary>
        /// <param name="dataId"></param>
        private void FindPrevOperId(string dataId)
        {
            var recodes = _dbContext.Db.Queryable<EMS_OPER_LOG>().Where(x => x.OPER_MAIN_ID == dataId).OrderByDescending(x => x.LAST_MTIME).ToList();
            if (recodes.Count() > 0)
            {
                _prevOperId = recodes[0].OPER_LOGID;
                _preCirculationRecord = recodes[0];
            }
            else
            {
                _preCirculationRecord = new EMS_OPER_LOG();
                _preCirculationRecord.OPER_LOGID = _prevOperId;
                _preCirculationRecord.OPER_STATE = OperationStateEnum.NotSubmitted;
                _preCirculationRecord.NEXT_PER_PERSON = _user.USER_NAME;
                _preCirculationRecord.NEXT_OPER_PERSON_ID = _user.USER_NO;
            }
        }

        /// <summary>
        /// 基础操作方法
        /// </summary>
        /// <param name="nextUserID">下一个操作人ID</param>
        /// <param name="nextUserName">下一个操作任命</param>
        /// <param name="operationName">操作名称</param>
        /// <param name="operationState">操作状态</param>
        /// <param name="content">操作建议</param>
        /// <returns></returns>
        private EMS_OPER_LOG CreateRecode(string nextUserID, string nextUserName, string operationName, OperationStateEnum operationState, string? content)
        {
            var recode = new EMS_OPER_LOG();
            recode.OPER_LOGID = IDGenHelper.CreateGuid().ToString();
            recode.OPER_STATE = operationState;
            recode.OPER_MAIN_ID = _dataId;
            recode.OPER_PERSON_ID = _user.USER_NO;
            recode.OPER_PERSON = _user.USER_NAME;
            recode.OPER_NAME = operationName;
            recode.NEXT_OPER_PERSON_ID = nextUserID;
            recode.NEXT_PER_PERSON = nextUserName;
            recode.OPER_CONTENT = content;
            recode.PREV_OPER_ID = _prevOperId;

            using (_dbContext.Begin())
            {
                _dbContext.Context.CirculationRecords.Insert(recode);
                _dbContext.SaveChanges();
            }

            return recode;
        }


    }




}
