﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using H.Utility.SqlSugarInfra;
using SqlSugar;

namespace XH.H82.Models.Entities
{
    [DBOwner("XH_SYS")]
    [SugarTable("SYS6_COMPANY_INFO")]
    public class SYS6_COMPANY_INFO
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string COMPANY_ID { get; set; }
        public string INSTANCE_ID { get; set; }
        public string COMPANY_NAME { get; set; }
        public string COMPANY_NO { get; set; }
        public string COMPANY_SNAME { get; set; }
        //public string COMPANY_ENAME { get; set; }
        public string COMPANY_CLASS { get; set; }
        public string SERVICE_SCOPE { get; set; }
        public string COMPANY_SORT { get; set; }
        public string COMPANY_ADDRESS { get; set; }
        public string COMPANY_POSTALCODE { get; set; }
        public string PHONE_NO { get; set; }
        public string COMPANY_QQ { get; set; }
        public string COMPANY_WX { get; set; }
        public string COMPANY_MANAGER { get; set; }
        public string COMPANY_CONTACTER { get; set; }
        public string LEVEL1_CODE { get; set; }
        public string LEVEL2_CODE { get; set; }
        public string LEVEL3_CODE { get; set; }
        public string MANUFACTURER_NO { get; set; }
        public string SPELL_CODE { get; set; }
        public string COMPANY_STATE { get; set; }
        public DateTime? FIRST_RTIME { get; set; }
        public string LAST_MPERSON { get; set; }
        public DateTime? LAST_MTIME { get; set; }
        public string REMARK { get; set; }
        //public string CONPANY_TYPE { get; set; }
        //public string SERVICE_AREA { get; set; }
        //public string NATIONALITY { get; set; }
        //public string COMPANY_CODE { get; set; }

    }
}
