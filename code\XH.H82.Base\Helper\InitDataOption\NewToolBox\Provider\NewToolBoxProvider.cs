﻿using H.Utility;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using RestSharp;

namespace XH.H82.Base.Helper.InitDataOption.NewToolBox.Provider
{
    public class NewToolBoxProvider
    {
        const string _newToolConfigKey = "H04-13";

        IConfiguration _configuration;
        string _moduleId = "";
        string _hospitalId = "";
        string _newToolUrl = "";

        public NewToolBoxProvider(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public void SetModuleAndHospitalContext(string moduleId, string hospitalId, string NewToolConfigKey = _newToolConfigKey)
        {
            _newToolUrl = _configuration[NewToolConfigKey] ?? "https://114.55.38.185:18013";
            _moduleId = moduleId;
            _hospitalId = hospitalId;
            CheckOptions();
        }

        /// <summary>
        /// 可续写检查规则
        /// </summary>
        /// <exception cref="ArgumentNullException"></exception>
        private void CheckOptions()
        {
            if (string.IsNullOrEmpty(_newToolUrl))
            {
                throw new ArgumentNullException($"请在配置文件添加{_newToolConfigKey}的配置或在作为SetModuleAndHospitalContext传参传入NewToolConfigKey的值");
            }
            if (string.IsNullOrEmpty(_moduleId))
            {
                throw new ArgumentNullException($"请传入正确的moduleId的值");
            }
            if (string.IsNullOrEmpty(_hospitalId))
            {
                throw new ArgumentNullException($"请传入正确的hospitalId的值");
            }
        }

        private RestClient GetRestClient()
        {
            CheckOptions();
            var toolClientOption = new RestClientOptions()
            {
                RemoteCertificateValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true,
                BaseUrl = new Uri(_newToolUrl),
                ThrowOnAnyError = true
            };
            var toolClient = new RestClient(toolClientOption);
            return toolClient;
        }

        public async Task<ResultDto> InitMeanConfigByExcelFileAsync(IFormFile? excelFile, string accessToken)
        {
            try
            {
                var menuPageConfig = await UploadExcelToNewToolBox(excelFile, accessToken);
                var result = await ConfirmToNewToolBox(menuPageConfig, accessToken);
                return result;
            }
            catch (Exception e)
            {
                throw new BizException(e.Message, e.InnerException);
            }
        }
        private async Task<MenuPageConfig> UploadExcelToNewToolBox(IFormFile? excelFile, string accessToken)
        {
            CheckOptions();
            var uploadApi = $"/api/FileService/UploadProductConfigExcel?hospitalId={_hospitalId}&moduleId={_moduleId}";
            var client = GetRestClient();

            var fileBytes = GetfileBytes(ref excelFile);
            RestRequest uploadApirRequest = new RestRequest($"{uploadApi}");
            uploadApirRequest.RequestFormat = DataFormat.Json;
            uploadApirRequest.AddHeader("Authorization", $"{accessToken}");
            uploadApirRequest.AddHeader("Content-Type", "multipart/form-data");
            uploadApirRequest.AddFile("file", fileBytes, excelFile.FileName);
            RestResponse<ResultDto>? uploadResult = await client.ExecutePostAsync<ResultDto>(uploadApirRequest);
            if (!uploadResult.IsSuccessful)
            {
                throw new Exception($"文件上传失败{uploadResult.ErrorMessage}");
            }
            var menuPageConfig = JsonConvert.DeserializeObject<MenuPageConfig>(uploadResult.Data.data.ToString());

            return menuPageConfig;
        }

        private async Task<ResultDto> ConfirmToNewToolBox(MenuPageConfig menuPageConfig, string accessToken)
        {
            var confirmApi = $"/api/SysMenuInfoDict/ResetOriginalMenuData?hospitalId={_hospitalId}&moduleId={_moduleId}";
            var client = GetRestClient();
            RestRequest confirmApiRequest = new RestRequest($"{confirmApi}");
            confirmApiRequest.AddHeader("Authorization", $"{accessToken}");
            confirmApiRequest.AddHeader("Content-Type", ContentType.Json);
            confirmApiRequest.AddJsonBody(menuPageConfig.sysWmenus);
            var confirmResult = await client.ExecutePostAsync<ResultDto>(confirmApiRequest);
            if (!confirmResult.IsSuccessful)
            {
                throw new Exception($"菜单文件上传成功，但确认失败{confirmResult.ErrorMessage}");
            }
            return confirmResult.Data!;
        }

        private byte[] GetfileBytes(ref IFormFile? excelFile)
        {
            if (excelFile is null)
            {
                var appRunPath = AppDomain.CurrentDomain.BaseDirectory;
                var meanFileName = $"{_moduleId}-{_hospitalId}-全部页面设置.xlsx";
                var filePath = $"{appRunPath}\\{meanFileName}";

                if (File.Exists(filePath))
                {
                    var stream = File.OpenRead(filePath);
                    excelFile = new FormFile(stream, 0, stream.Length, "file", meanFileName);
                }
            }
            var fileBytes = new byte[excelFile.Length];

            using (var excelStream = excelFile.OpenReadStream())
            {
                excelStream.Read(fileBytes);
            }

            return fileBytes;
        }
    }
}
