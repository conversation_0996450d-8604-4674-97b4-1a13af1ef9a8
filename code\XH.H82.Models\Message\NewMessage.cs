﻿using XH.LAB.UTILS.H05Message;
using XH.LAB.UTILS.Helpers;

namespace XH.H82.Models.Message;

public class NewMessage : BaseMessage
{
    public NewMessage(string crrrId , string title , string msgType ) 
        : base("",0,"","",DateTime.Now,"M13",
            "", crrrId,MsgDisposeType.NewMessage,
            "","","1","1",
            0,title,msgType,DateTime.Now.AddDays(2),
            "","","个人消息",
            "",DateTime.Now,  "",null,
            null)
    {
        SEND_COMPUTER = GetMacLoacltion.GetMacAddress();
    }
    
    public void SetReceiver(string hospitalId, string labId, string areaId ,string moduleId, string receiveUserNo, string receiveName)
    {
        HOSPITAL_ID = hospitalId;
        LAB_ID = labId;
        AREA_ID = areaId;
        MODULE_ID = moduleId;
        RECEIVE_UNIT_ID = receiveUserNo;
        RECEIVE_UNIT_NAME = receiveName;
    }
    
    public void SetSender(string sendUserNo, string sendUserName, string content,DateTime? warningTime = null , DateTime? AlarmTime= null ,long delay = 0, long overTime = 0)
    {
        SEND_UNIT_ID = sendUserNo;
        SEND_PERSON = sendUserName;
        MSG_CONTENT = content;
        MSG_WARNING_TIME = warningTime;
        MSG_ALARM_TIME = AlarmTime;
        DELAY_TIME = delay;
        MSG_OVERTIME = overTime;
    }

}