﻿namespace XH.H82.Models.BusinessModuleClient.Dto.IoTDevices;

/// <summary>
/// 状态值计算模型
/// </summary>
public class DevicesStatusValue
{
    /// <summary>
    /// 是否使用中
    /// </summary>
    public Func<double, bool> UseFunc { get; set; }
    /// <summary>
    /// 是否关机
    /// </summary>
    public Func<double, bool> ShutDownFunc { get; set; }
    /// <summary>
    /// 是否待机
    /// </summary>
    public Func<double, bool> StandByFunc { get; set; }
    public Func<double, bool> DisinfectionFunc { get; set; }
    public bool IsUse(double value)
    {
       return UseFunc.Invoke(value);
    }
    public bool IsShutDown(double value)
    {
        return ShutDownFunc.Invoke(value);
    }
    public bool IsStandBy(double value)
    {
      return StandByFunc.Invoke(value);
    }
    public bool IsDisinfection(double value)
    {
        return DisinfectionFunc.Invoke(value);
    }
}