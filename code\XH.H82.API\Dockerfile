﻿FROM mcr.microsoft.com/dotnet/aspnet:6.0  AS runtime
ADD sources.list /etc/apt/
WORKDIR /
RUN mkdir -p /usr/share/fonts
RUN mkdir -p /app
COPY ./ChineseFont/* /usr/share/fonts
RUN apt-get update && \
    apt-get upgrade -y
RUN    apt-get install -y fontconfig
RUN    apt-get install -y xfonts-utils
RUN    apt-get clean
#ENV FONTCONFIG_FILE=/etc/fonts/fonts.conf
#RUN echo "<fontconfig><dir>/usr/share/fonts</dir></fontconfig>" > /etc/fonts/local.conf
ENV LANG en_US.utf8
ENV TZ=Asia/Shanghai
RUN fc-cache -fv
COPY XH.H82.API /app/ 
EXPOSE 18482
WORKDIR /app
ENTRYPOINT ["dotnet","XH.H82.API.dll"]
