﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using H.Utility.SqlSugarInfra;
using SqlSugar;

namespace XH.H82.Models.Entities.THS
{
    /// <summary>
    /// 设备监测指标表
    /// </summary>
    [DBOwner("XH_SYS")]
    [Table("THS_MONITOR_ITEM")]
    [SugarTable("THS_MONITOR_ITEM")]
    public class THS_MONITOR_ITEM
    {
        /// <summary>
        /// 监测指标ID
        /// </summary>
        [Key]
        [SugarColumn(IsPrimaryKey = true)]
        public string? ITEM_ID { get; set; }
        /// <summary>
        /// 医疗机构ID
        /// </summary>
        public string? HOSPITAL_ID { get; set; }
        /// <summary>
        /// 监测指标名称
        /// </summary>
        public string? ITEM_NAME { get; set; }
        /// <summary>
        /// 排序号
        /// </summary>
        public string? ITEM_SORT { get; set; }
        /// <summary>
        /// 监测指标单位
        /// </summary>
        public string? ITEM_UNIT { get; set; }
        /// <summary>
        /// 监测指标精度(0,1,2,3,4)
        /// </summary>
        public string? ITEM_PRECISION { get; set; }
        /// <summary>
        /// 状态0禁用1在用
        /// </summary>
        public string? ITEM_STATE { get; set; }
        /// <summary>
        /// 首次登记人
        /// </summary>
        public string? FIRST_RPERSON { get; set; }
        /// <summary>
        /// 首次登记时间
        /// </summary>
        public DateTime? FIRST_RTIME { get; set; }

        /// <summary>
        /// 最后修改人员
        /// </summary>
        public string? LAST_MPERSON { get; set; }
        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime? LAST_MTIME { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string? REMARK { get; set; }
    }
}
