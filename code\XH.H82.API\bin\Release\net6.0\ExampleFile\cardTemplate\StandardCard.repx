﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="********" Ref="1" ControlType="DevExpress.XtraReports.UI.XtraReport, DevExpress.XtraReports.v17.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Name="Report3" Margins="0, 0, 161, 0" PaperKind="Custom" PageWidth="278" PageHeight="180" Version="17.1" DataMember="EMS_EQUIPMENT_INFO" DataSource="#Ref-0" BackColor="ActiveCaption" BorderWidth="0">
  <Parameters>
    <Item1 Ref="3" Description="LAST_CORRECT_DATE" Name="LAST_CORRECT_DATE" />
    <Item2 Ref="4" Description="NEXT_CORRECT_DATE" Name="NEXT_CORRECT_DATE" />
  </Parameters>
  <CalculatedFields>
    <Item1 Ref="5" Name="SERIAL_NUMBER" DataMember="EMS_EQUIPMENT_INFO" />
    <Item2 Ref="6" Name="DEPT_SECTION_NO" DataMember="EMS_EQUIPMENT_INFO" />
    <Item3 Ref="7" Name="LAST_CORRECT" DisplayName="LAST_CORRECT" DataMember="EMS_EQUIPMENT_INFO" />
    <Item4 Ref="8" Name="NEXT_CORRECT" DataMember="EMS_EQUIPMENT_INFO" />
  </CalculatedFields>
  <Bands>
    <Item1 Ref="9" ControlType="TopMarginBand" Name="TopMargin" HeightF="161.200012" ForeColor="Black" BackColor="White" BorderColor="Silver">
      <Controls>
        <Item1 Ref="10" ControlType="XRLabel" Name="label21" TextAlignment="TopCenter" SizeF="72.96875,12.5800018" LocationFloat="188.22998, 146.536667" Font="微软雅黑, 5pt" Padding="2,2,0,0,100">
          <DataBindings>
            <Item1 Ref="11" PropertyName="Text" DataMember="EMS_EQUIPMENT_INFO.KEEP_PERSON" />
          </DataBindings>
          <StylePriority Ref="12" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="13" ControlType="XRLabel" Name="label20" TextAlignment="TopCenter" SizeF="72.96875,12.5800018" LocationFloat="188.230072, 121.356644" Font="微软雅黑, 5pt" Padding="2,2,0,0,100">
          <DataBindings>
            <Item1 Ref="14" PropertyName="Text" DataMember="EMS_EQUIPMENT_INFO.EQUIPMENT_STATE" />
          </DataBindings>
          <StylePriority Ref="15" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="16" ControlType="XRLine" Name="line10" SizeF="72.96875,2.083332" LocationFloat="188.230072, 159.116669" ForeColor="Black" BackColor="255,188,188,188" BorderColor="LightGray">
          <StylePriority Ref="17" UseForeColor="false" UseBackColor="false" UseBorderColor="false" />
        </Item3>
        <Item4 Ref="18" ControlType="XRLine" Name="line9" SizeF="72.96875,2.083332" LocationFloat="188.230072, 133.936661" ForeColor="Black" BackColor="255,188,188,188" BorderColor="LightGray">
          <StylePriority Ref="19" UseForeColor="false" UseBackColor="false" UseBorderColor="false" />
        </Item4>
        <Item5 Ref="20" ControlType="XRLabel" Name="label19" TextAlignment="TopCenter" SizeF="72.96875,12.5800018" LocationFloat="188.230057, 96.17667" Font="微软雅黑, 5pt" Padding="2,2,0,0,100">
          <DataBindings>
            <Item1 Ref="21" PropertyName="Text" DataMember="EMS_EQUIPMENT_INFO.NEXT_CORRECT" />
          </DataBindings>
          <StylePriority Ref="22" UseFont="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="23" ControlType="XRLine" Name="line8" SizeF="72.96875,2.083332" LocationFloat="188.230042, 108.756668" ForeColor="Black" BackColor="255,188,188,188" BorderColor="LightGray">
          <StylePriority Ref="24" UseForeColor="false" UseBackColor="false" UseBorderColor="false" />
        </Item6>
        <Item7 Ref="25" ControlType="XRLine" Name="line7" SizeF="72.96875,2.083332" LocationFloat="188.230042, 83.57238" ForeColor="Black" BackColor="255,188,188,188" BorderColor="LightGray">
          <StylePriority Ref="26" UseForeColor="false" UseBackColor="false" UseBorderColor="false" />
        </Item7>
        <Item8 Ref="27" ControlType="XRLabel" Name="label17" TextAlignment="TopCenter" SizeF="72.96875,12.5800018" LocationFloat="188.230026, 70.99238" Font="微软雅黑, 5pt" Padding="2,2,0,0,100">
          <DataBindings>
            <Item1 Ref="28" PropertyName="Text" DataMember="EMS_EQUIPMENT_INFO.LAST_CORRECT" />
          </DataBindings>
          <StylePriority Ref="29" UseFont="false" UseTextAlignment="false" />
        </Item8>
        <Item9 Ref="30" ControlType="XRLabel" Name="label16" TextAlignment="TopCenter" SizeF="72.96875,12.5800018" LocationFloat="188.230011, 45.8215179" Font="微软雅黑, 5pt" Padding="2,2,0,0,100">
          <DataBindings>
            <Item1 Ref="31" PropertyName="Text" DataMember="EMS_EQUIPMENT_INFO.EQUIPMENT_NAME" />
          </DataBindings>
          <StylePriority Ref="32" UseFont="false" UseTextAlignment="false" />
        </Item9>
        <Item10 Ref="33" ControlType="XRLine" Name="line6" SizeF="72.96875,2.083332" LocationFloat="188.230026, 58.40152" ForeColor="Black" BackColor="255,188,188,188" BorderColor="LightGray">
          <StylePriority Ref="34" UseForeColor="false" UseBackColor="false" UseBorderColor="false" />
        </Item10>
        <Item11 Ref="35" ControlType="XRLabel" Name="label15" TextAlignment="TopCenter" SizeF="72.96875,12.5800018" LocationFloat="48.00002, 146.536667" Font="微软雅黑, 5pt" Padding="2,2,0,0,100">
          <DataBindings>
            <Item1 Ref="36" PropertyName="Text" DataMember="EMS_EQUIPMENT_INFO.DEPT_SECTION_NO" />
          </DataBindings>
          <StylePriority Ref="37" UseFont="false" UseTextAlignment="false" />
        </Item11>
        <Item12 Ref="38" ControlType="XRLabel" Name="label14" TextAlignment="TopCenter" SizeF="72.96875,12.5800018" LocationFloat="47.9999962, 121.356682" Font="微软雅黑, 5pt" Padding="2,2,0,0,100">
          <DataBindings>
            <Item1 Ref="39" PropertyName="Text" DataMember="EMS_EQUIPMENT_INFO.ENABLE_TIME" />
          </DataBindings>
          <StylePriority Ref="40" UseFont="false" UseTextAlignment="false" />
        </Item12>
        <Item13 Ref="41" ControlType="XRLabel" Name="label13" TextTrimming="None" Text="责任人" TextAlignment="TopCenter" SizeF="36.1979523,10.5000076" LocationFloat="150.232162, 150.7" Font="微软雅黑, 5pt" ForeColor="Black" BackColor="Transparent" Padding="2,2,0,0,100" BorderColor="IndianRed" BorderWidth="0">
          <StylePriority Ref="42" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorderWidth="false" UseTextAlignment="false" />
        </Item13>
        <Item14 Ref="43" ControlType="XRLabel" Name="label12" TextTrimming="None" Text="运行状态" TextAlignment="TopCenter" SizeF="36.1979523,10.5000076" LocationFloat="150.232162, 125.519989" Font="微软雅黑, 5pt" ForeColor="Black" BackColor="Transparent" Padding="2,2,0,0,100" BorderColor="IndianRed" BorderWidth="0">
          <StylePriority Ref="44" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorderWidth="false" UseTextAlignment="false" />
        </Item14>
        <Item15 Ref="45" ControlType="XRLabel" Name="label11" TextTrimming="None" Text="下次校准" TextAlignment="TopCenter" SizeF="36.1979523,10.5000076" LocationFloat="150.232162, 100.34" Font="微软雅黑, 5pt" ForeColor="Black" BackColor="Transparent" Padding="2,2,0,0,100" BorderColor="IndianRed" BorderWidth="0">
          <StylePriority Ref="46" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorderWidth="false" UseTextAlignment="false" />
        </Item15>
        <Item16 Ref="47" ControlType="XRLabel" Name="label10" TextTrimming="None" Text="校准日期" TextAlignment="TopCenter" SizeF="36.1979523,10.5000076" LocationFloat="150.232162, 75.1557" Font="微软雅黑, 5pt" ForeColor="Black" BackColor="Transparent" Padding="2,2,0,0,100" BorderColor="IndianRed" BorderWidth="0">
          <StylePriority Ref="48" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorderWidth="false" UseTextAlignment="false" />
        </Item16>
        <Item17 Ref="49" ControlType="XRLabel" Name="label9" TextTrimming="None" Text="设备名称" TextAlignment="TopCenter" SizeF="36.1979523,10.5000076" LocationFloat="150.232162, 49.98484" Font="微软雅黑, 5pt" ForeColor="Black" BackColor="Transparent" Padding="2,2,0,0,100" BorderColor="IndianRed" BorderWidth="0">
          <StylePriority Ref="50" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorderWidth="false" UseTextAlignment="false" />
        </Item17>
        <Item18 Ref="51" ControlType="XRLabel" Name="label7" TextAlignment="TopCenter" SizeF="72.96875,12.5800018" LocationFloat="47.9999962, 96.17667" Font="微软雅黑, 5pt" Padding="2,2,0,0,100">
          <DataBindings>
            <Item1 Ref="52" PropertyName="Text" DataMember="EMS_EQUIPMENT_INFO.MANUFACTURER" />
          </DataBindings>
          <StylePriority Ref="53" UseFont="false" UseTextAlignment="false" />
        </Item18>
        <Item19 Ref="54" ControlType="XRLine" Name="line5" SizeF="72.96875,2.083332" LocationFloat="48.00002, 159.116669" ForeColor="Black" BackColor="255,188,188,188" BorderColor="LightGray">
          <StylePriority Ref="55" UseForeColor="false" UseBackColor="false" UseBorderColor="false" />
        </Item19>
        <Item20 Ref="56" ControlType="XRLine" Name="line4" SizeF="72.96875,2.083332" LocationFloat="48.00002, 133.936676" ForeColor="Black" BackColor="255,188,188,188" BorderColor="LightGray">
          <StylePriority Ref="57" UseForeColor="false" UseBackColor="false" UseBorderColor="false" />
        </Item20>
        <Item21 Ref="58" ControlType="XRLine" Name="line1" SizeF="72.96875,2.083332" LocationFloat="48.00001, 108.756668" ForeColor="Black" BackColor="255,188,188,188" BorderColor="LightGray">
          <StylePriority Ref="59" UseForeColor="false" UseBackColor="false" UseBorderColor="false" />
        </Item21>
        <Item22 Ref="60" ControlType="XRLine" Name="line2" SizeF="72.96875,2.083332" LocationFloat="48.00001, 83.57238" ForeColor="Black" BackColor="255,188,188,188" BorderColor="LightGray">
          <StylePriority Ref="61" UseForeColor="false" UseBackColor="false" UseBorderColor="false" />
        </Item22>
        <Item23 Ref="62" ControlType="XRLabel" Name="label8" TextTrimming="None" Text="实验室编号" TextAlignment="TopCenter" SizeF="46.1979523,10.5000153" LocationFloat="0, 150.7" Font="微软雅黑, 5pt" ForeColor="Black" BackColor="Transparent" Padding="2,2,0,0,100" BorderColor="IndianRed" BorderWidth="0">
          <StylePriority Ref="63" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorderWidth="false" UseTextAlignment="false" />
        </Item23>
        <Item24 Ref="64" ControlType="XRLabel" Name="label6" TextTrimming="None" Text="启用日期" TextAlignment="TopCenter" SizeF="36.1979523,10.5000076" LocationFloat="10, 125.52" Font="微软雅黑, 5pt" ForeColor="Black" BackColor="Transparent" Padding="2,2,0,0,100" BorderColor="IndianRed" BorderWidth="0">
          <StylePriority Ref="65" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorderWidth="false" UseTextAlignment="false" />
        </Item24>
        <Item25 Ref="66" ControlType="XRLabel" Name="label5" TextTrimming="None" Text="制造商" TextAlignment="TopCenter" SizeF="36.1979523,10.5000076" LocationFloat="10, 100.34" Font="微软雅黑, 5pt" ForeColor="Black" BackColor="Transparent" Padding="2,2,0,0,100" BorderColor="IndianRed" BorderWidth="0">
          <StylePriority Ref="67" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorderWidth="false" UseTextAlignment="false" />
        </Item25>
        <Item26 Ref="68" ControlType="XRLabel" Name="label4" TextAlignment="TopCenter" SizeF="72.96875,12.5800018" LocationFloat="48.0000038, 70.99238" Font="微软雅黑, 5pt" Padding="2,2,0,0,100">
          <DataBindings>
            <Item1 Ref="69" PropertyName="Text" DataMember="EMS_EQUIPMENT_INFO.DEALER" />
          </DataBindings>
          <StylePriority Ref="70" UseFont="false" UseTextAlignment="false" />
        </Item26>
        <Item27 Ref="71" ControlType="XRLabel" Name="label2" TextTrimming="None" Text="经销商" TextAlignment="TopCenter" SizeF="36.1979523,10.5000076" LocationFloat="9.999993, 75.1557" Font="微软雅黑, 5pt" ForeColor="Black" BackColor="Transparent" Padding="2,2,0,0,100" BorderColor="IndianRed" BorderWidth="0">
          <StylePriority Ref="72" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorderWidth="false" UseTextAlignment="false" />
        </Item27>
        <Item28 Ref="73" ControlType="XRLabel" Name="label18" Text="label18" TextAlignment="TopCenter" SizeF="72.96875,12.5800018" LocationFloat="48.0000038, 45.8215179" Font="微软雅黑, 5pt" Padding="2,2,0,0,100">
          <DataBindings>
            <Item1 Ref="74" PropertyName="Text" DataMember="EQUIPMENT_MODEL" />
          </DataBindings>
          <StylePriority Ref="75" UseFont="false" UseTextAlignment="false" />
        </Item28>
        <Item29 Ref="76" ControlType="XRLabel" Name="label3" TextTrimming="None" Text="设备型号" TextAlignment="TopCenter" SizeF="36.1979523,10.5000076" LocationFloat="9.999997, 49.9848442" Font="微软雅黑, 5pt" ForeColor="Black" BackColor="Transparent" Padding="2,2,0,0,100" BorderColor="IndianRed" BorderWidth="0">
          <StylePriority Ref="77" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorderWidth="false" UseTextAlignment="false" />
        </Item29>
        <Item30 Ref="78" ControlType="XRLine" Name="line3" SizeF="72.96875,2.083332" LocationFloat="47.9999962, 58.40152" ForeColor="Black" BackColor="255,188,188,188" BorderColor="LightGray">
          <StylePriority Ref="79" UseForeColor="false" UseBackColor="false" UseBorderColor="false" />
        </Item30>
        <Item31 Ref="80" ControlType="XRLabel" Name="label1" Text="设备运行状态及校准标识" TextAlignment="TopCenter" SizeF="243.473129,27.4848557" LocationFloat="9.999996, 9.999996" Font="微软雅黑, 8pt, style=Bold" ForeColor="DimGray" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="81" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
        </Item31>
      </Controls>
      <StylePriority Ref="82" UseForeColor="false" UseBackColor="false" UseBorderColor="false" />
    </Item1>
    <Item2 Ref="83" ControlType="DetailBand" Name="Detail" DrillDownExpanded="false" HeightF="0" BackColor="PaleTurquoise">
      <StylePriority Ref="84" UseBackColor="false" />
    </Item2>
    <Item3 Ref="85" ControlType="BottomMarginBand" Name="BottomMargin" HeightF="0" />
  </Bands>
  <ComponentStorage>
    <Item1 Ref="0" ObjectType="DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.v17.1" Name="sqlDataSource1" Base64="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" />
  </ComponentStorage>
</XtraReportsLayoutSerializer>