﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.Dtos.FileTemplate
{
    public class OaExcelFillDataDto
    {
        public string OPER_UUID { get; set; } = Guid.NewGuid().ToString();
        public string STYLE_ID { get; set; }
        public string KEY_COLUMN { get; set; }

        public List<StyleTemplateClassExcelDataDto> ALL_CLASS_DATAS { get; set; }

        public OaExcelFillDataDto(string styleId, string keyColumn)
        {
            STYLE_ID = styleId;
            KEY_COLUMN = keyColumn;
            ALL_CLASS_DATAS = new List<StyleTemplateClassExcelDataDto>();
        }
    }
}
