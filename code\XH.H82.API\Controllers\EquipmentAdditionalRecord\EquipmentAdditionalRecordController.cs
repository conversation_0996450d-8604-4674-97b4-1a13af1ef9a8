using AutoMapper;
using H.Utility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using XH.H82.API.Extensions;
using XH.H82.IServices;
using XH.H82.Models.Dtos.EquipmentAdditionalRecord;
using XH.H82.Models.EquipmengtClassNew;

namespace XH.H82.API.Controllers.EquipmentAdditionalRecord
{
    /// <summary>
    /// 设备扩展记录控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class EquipmentAdditionalRecordController : ControllerBase
    {
        private readonly IEquipmentAdditionalRecordService _equipmentAdditionalRecordService;
        private readonly IMapper _mapper;

        public EquipmentAdditionalRecordController(
            IEquipmentAdditionalRecordService equipmentAdditionalRecordService,
            IMapper mapper)
        {
            _equipmentAdditionalRecordService = equipmentAdditionalRecordService;
            _mapper = mapper;
        }

        /// <summary>
        /// 根据设备ID和档案记录ID获取设备扩展记录
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <param name="eqpArchivesId">档案记录ID</param>
        /// <returns>设备扩展记录</returns>
        [HttpGet("GetAdditionalRecord")]
        [CustomResponseType(typeof(EMS_EQP_ADDN_RECORD))]
        public IActionResult GetEquipmentAdditionalRecord(string equipmentId, string eqpArchivesId)
        {
            try
            {
                var result = _equipmentAdditionalRecordService.GetEquipmentAdditionalRecord(equipmentId, eqpArchivesId);
                return Ok(result.ToResultDto());
            }
            catch (Exception ex)
            {
                return Ok(new ResultDto { success = false, msg = ex.Message });
            }
        }

        /// <summary>
        /// 根据设备ID获取所有扩展记录
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <returns>设备扩展记录列表</returns>
        [HttpGet("GetAdditionalRecords")]
        [CustomResponseType(typeof(List<EMS_EQP_ADDN_RECORD>))]
        public IActionResult GetEquipmentAdditionalRecords(string equipmentId)
        {
            try
            {
                var result = _equipmentAdditionalRecordService.GetEquipmentAdditionalRecords(equipmentId);
                return Ok(result.ToResultDto());
            }
            catch (Exception ex)
            {
                return Ok(new ResultDto { success = false, msg = ex.Message });
            }
        }

        /// <summary>
        /// 获取设备的档案记录详细信息（包含扩展字段）
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <param name="eqpArchivesId">档案记录ID</param>
        /// <returns>档案记录详细信息</returns>
        [HttpGet("GetArchiveDetail")]
        [CustomResponseType(typeof(EquipmentArchiveDetailDto))]
        public IActionResult GetEquipmentArchiveDetail(string equipmentId, string eqpArchivesId)
        {
            try
            {
                var result = _equipmentAdditionalRecordService.GetEquipmentArchiveDetail(equipmentId, eqpArchivesId);
                return Ok(result.ToResultDto());
            }
            catch (Exception ex)
            {
                return Ok(new ResultDto { success = false, msg = ex.Message });
            }
        }

        /// <summary>
        /// 获取设备的所有档案记录详细信息列表
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <returns>档案记录详细信息列表</returns>
        [HttpGet("GetArchiveDetails")]
        [CustomResponseType(typeof(List<EquipmentArchiveDetailDto>))]
        public IActionResult GetEquipmentArchiveDetails(string equipmentId)
        {
            try
            {
                var result = _equipmentAdditionalRecordService.GetEquipmentArchiveDetails(equipmentId);
                return Ok(result.ToResultDto());
            }
            catch (Exception ex)
            {
                return Ok(new ResultDto { success = false, msg = ex.Message });
            }
        }

        /// <summary>
        /// 保存或更新设备扩展记录
        /// </summary>
        /// <param name="dto">设备扩展记录DTO</param>
        /// <returns>操作结果</returns>
        [HttpPost("SaveAdditionalRecord")]
        [CustomResponseType(typeof(ResultDto))]
        public IActionResult SaveEquipmentAdditionalRecord([FromBody] SaveAdditionalRecordDto dto)
        {
            try
            {
                // 将DTO转换为实体
                var record = new EMS_EQP_ADDN_RECORD
                {
                    EqpRecordId = dto.EqpRecordId,
                    EquipmentId = dto.EquipmentId,
                    EqpArchivesId = dto.EqpArchivesId,
                    EquitmentJson = dto.EquitmentJson,
                    EqpRecordAffix = dto.EqpRecordAffix,
                    EqpRecordSort = dto.EqpRecordSort,
                    EqpRecordState = dto.EqpRecordState,
                    Remark = dto.Remark
                };

                var result = _equipmentAdditionalRecordService.SaveEquipmentAdditionalRecord(record);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return Ok(new ResultDto { success = false, msg = ex.Message });
            }
        }

        /// <summary>
        /// 保存或更新设备扩展记录（直接使用实体）
        /// </summary>
        /// <param name="record">设备扩展记录实体</param>
        /// <returns>操作结果</returns>
        [HttpPost("SaveAdditionalRecordEntity")]
        [CustomResponseType(typeof(ResultDto))]
        public IActionResult SaveEquipmentAdditionalRecordEntity([FromBody] EMS_EQP_ADDN_RECORD record)
        {
            try
            {
                var result = _equipmentAdditionalRecordService.SaveEquipmentAdditionalRecord(record);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return Ok(new ResultDto { success = false, msg = ex.Message });
            }
        }

        /// <summary>
        /// 删除设备扩展记录
        /// </summary>
        /// <param name="eqpRecordId">记录ID</param>
        /// <returns>操作结果</returns>
        [HttpDelete("DeleteAdditionalRecord")]
        [CustomResponseType(typeof(ResultDto))]
        public IActionResult DeleteEquipmentAdditionalRecord(string eqpRecordId)
        {
            try
            {
                var result = _equipmentAdditionalRecordService.DeleteEquipmentAdditionalRecord(eqpRecordId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return Ok(new ResultDto { success = false, msg = ex.Message });
            }
        }
    }
}
