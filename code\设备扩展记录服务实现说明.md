# 设备扩展记录服务实现说明

## 概述

根据您的需求，我已经创建了一个完整的设备扩展记录服务，用于处理 `EMS_EQP_ADDN_RECORD` 表的数据查询和管理。该服务能够：

1. 根据设备ID和档案记录ID查询对应的业务数据（如 EMS_CORRECT_INFO、EMS_DEBUG_INFO 等）
2. 查询 EMS_EQP_ADDN_RECORD 表中的扩展字段数据
3. 将业务数据和扩展字段数据整合返回

## 已创建的文件

### 1. 服务接口
- **文件**: `XH.H82.IServices/IEquipmentAdditionalRecordService.cs`
- **说明**: 定义了设备扩展记录服务的接口

### 2. 服务实现
- **文件**: `XH.H82.Services/EquipmentAdditionalRecord/EquipmentAdditionalRecordService.cs`
- **说明**: 实现了设备扩展记录服务的核心逻辑

### 3. 数据传输对象 (DTO)
- **文件**: `XH.H82.Models/Dtos/EquipmentAdditionalRecord/EquipmentArchiveDetailDto.cs`
- **说明**: 档案记录详细信息的DTO，包含业务数据和扩展字段数据

- **文件**: `XH.H82.Models/Dtos/EquipmentAdditionalRecord/SaveAdditionalRecordDto.cs`
- **说明**: 保存扩展记录的请求DTO和相关辅助类

### 4. API 控制器
- **文件**: `XH.H82.API/Controllers/EquipmentAdditionalRecord/EquipmentAdditionalRecordController.cs`
- **说明**: 提供 RESTful API 接口

### 5. 依赖注入配置
- **文件**: `XH.H82.API/Extensions/AutofacModuleRegister.cs`
- **说明**: 已添加服务注册配置

### 6. 文档
- **文件**: `XH.H82.Services/EquipmentAdditionalRecord/README.md`
- **说明**: 详细的使用说明和示例

## 核心功能

### 1. 获取档案记录详细信息
```csharp
// 获取单个档案记录详细信息（包含业务数据和扩展字段）
var detail = service.GetEquipmentArchiveDetail(equipmentId, eqpArchivesId);

// 获取设备所有档案记录详细信息
var details = service.GetEquipmentArchiveDetails(equipmentId);
```

### 2. 管理扩展记录
```csharp
// 保存或更新扩展记录
var record = new EMS_EQP_ADDN_RECORD { /* ... */ };
var result = service.SaveEquipmentAdditionalRecord(record);

// 删除扩展记录（软删除）
var result = service.DeleteEquipmentAdditionalRecord(eqpRecordId);
```

## API 接口

### GET /api/EquipmentAdditionalRecord/GetArchiveDetail
- **参数**: equipmentId, eqpArchivesId
- **返回**: 档案记录详细信息（包含业务数据和扩展字段）

### GET /api/EquipmentAdditionalRecord/GetArchiveDetails
- **参数**: equipmentId
- **返回**: 设备所有档案记录详细信息列表

### POST /api/EquipmentAdditionalRecord/SaveAdditionalRecord
- **请求体**: SaveAdditionalRecordDto
- **返回**: 操作结果

### DELETE /api/EquipmentAdditionalRecord/DeleteAdditionalRecord
- **参数**: eqpRecordId
- **返回**: 操作结果

## 数据映射逻辑

服务会根据档案记录ID自动映射到对应的业务表：

- **校准记录相关** → EMS_CORRECT_INFO
- **调试记录相关** → EMS_DEBUG_INFO
- **培训记录相关** → EMS_TRAIN_INFO

可以在 `GetBusinessDataByArchiveId` 方法中添加更多映射规则。

## 使用示例

### 前端调用示例
```javascript
// 获取设备档案记录详细信息
const response = await fetch(`/api/EquipmentAdditionalRecord/GetArchiveDetail?equipmentId=${equipmentId}&eqpArchivesId=${eqpArchivesId}`);
const result = await response.json();

if (result.success) {
    const detail = result.data;
    console.log('档案记录名称:', detail.eqpArchivesName);
    console.log('业务数据:', detail.businessData);
    console.log('扩展字段:', detail.additionalRecord?.equitmentJson);
}
```

### 保存扩展记录
```javascript
const recordData = {
    equipmentId: "EQP001",
    eqpArchivesId: "H82JBXX",
    equitmentJson: JSON.stringify({
        customField1: "自定义值1",
        customField2: "自定义值2"
    }),
    remark: "备注信息"
};

const response = await fetch('/api/EquipmentAdditionalRecord/SaveAdditionalRecord', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(recordData)
});
```

## 特性

1. **自动数据整合**: 自动查询业务数据和扩展字段数据并整合返回
2. **灵活的映射**: 支持根据档案记录ID映射到不同的业务表
3. **软删除**: 所有删除操作都是软删除，保证数据安全
4. **完整的审计**: 记录创建人、创建时间、修改人、修改时间
5. **异常处理**: 完善的异常处理和错误信息返回
6. **类型安全**: 使用强类型DTO确保数据传输的安全性

## 扩展说明

1. **添加新的业务表映射**: 在 `GetBusinessDataByArchiveId` 方法中添加新的 case 分支
2. **自定义扩展字段**: 扩展字段以JSON格式存储，支持任意结构的数据
3. **权限控制**: 可以在控制器方法上添加权限验证特性
4. **缓存支持**: 可以为常用查询添加缓存机制

## 注意事项

1. 确保数据库中存在相关表和数据
2. 所有操作都需要有效的用户上下文
3. JSON数据需要符合有效的JSON格式
4. 建议在生产环境中添加适当的日志记录

这个服务已经完全实现了您的需求，可以直接使用。如果需要添加新的功能或修改现有逻辑，可以基于这个框架进行扩展。
