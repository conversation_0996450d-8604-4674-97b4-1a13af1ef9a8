﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<Nullable>disable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<UserSecretsId>aa5482e1-df92-433a-b2e8-b0e0610f3989</UserSecretsId>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
		<GenerateDocumentationFile>True</GenerateDocumentationFile>
		<ApplicationIcon>icon.ico</ApplicationIcon>
		<Authors>Copyright 杏和软件®</Authors>
		<Version>6.25.300</Version>
		<FileVersion>6.25.300</FileVersion>
		<GeneratePackageOnBuild>True</GeneratePackageOnBuild>
	</PropertyGroup>

	<ItemGroup>
		<Compile Remove=".template.config\**" />
		<Content Remove=".template.config\**" />
		<EmbeddedResource Remove=".template.config\**" />
		<None Remove=".template.config\**" />
		<Compile Remove="Program - 复制.cs" />
		<None Remove="configs\SqlXml\Test2.xml" />
		<None Remove="NPOI" />
		<None Remove="Npoi.Mapper" />
		<None Update="ExcelTemplate\系统设置.xls">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="ExampleFile\equipmentCard\仪器设备报废记录表.docx">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="ExampleFile\EquipmentInfoToBeMaintained.xlsx">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="ExcelTemplate\工具箱设置项.xlsx">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Docs\QRcodeCard_preview.repx">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Docs\QRcodeCard.repx">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="ExampleFile\ScrapRecord.docx">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="ExampleFile\ScrapRecord浙江省人.docx">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="CodeConfig.xml">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
	</ItemGroup>

	<ItemGroup>
		<Content Include="..\.dockerignore">
		  <Link>.dockerignore</Link>
		</Content>
		<Content Include="configs\SqlXml\Test2.xml">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="icon.ico" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Autofac.Extensions.DependencyInjection" Version="8.0.0" />
		<PackageReference Include="Autofac.Extras.DynamicProxy" Version="6.0.1" />
		<PackageReference Include="EPPlus" Version="6.2.6" />
		<PackageReference Include="ExcelDataReader" Version="3.7.0-develop00385" />
		<PackageReference Include="ExcelDataReader.DataSet" Version="3.7.0-develop00385" />
		<PackageReference Include="Microsoft.Extensions.Hosting.WindowsServices" Version="6.0.1" />
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
		<PackageReference Include="Namotion.Reflection" Version="2.1.1" />

		<PackageReference Include="NPOI" Version="2.6.0" />
		<PackageReference Include="Npoi.Mapper" Version="6.0.0" />
		<PackageReference Include="Yarp.ReverseProxy" Version="2.1.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\XH.H82.Base\XH.H82.Base.csproj" />
		<ProjectReference Include="..\XH.H82.IServices\XH.H82.IServices.csproj" />
		<ProjectReference Include="..\XH.H82.Models\XH.H82.Models.csproj" />
		<ProjectReference Include="..\XH.H82.Services\XH.H82.Services.csproj" />
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Include="swagger.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</EmbeddedResource>
	</ItemGroup>

	<ItemGroup>
		<Content Update="configs\CardTypeSetting.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
	</ItemGroup>

	<ItemGroup>
		<None Update="configs\SqlXml\Test1.xml">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="ExampleFile\cardTemplate\QRcodeCard.repx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="ExampleFile\cardTemplate\QRcodeCard_preview.repx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="ExampleFile\cardTemplate\StandardCard.repx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="ExampleFile\cardTemplate\StandardCard_preview.repx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="ExampleFile\default.jpg">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="ExampleFile\DeviceInfo.docx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="ExampleFile\EquipmentImportTemplate.xls">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="ExampleFile\EquipmentList.docx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="ExampleFile\EquipmentList.pdf">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="ExampleFile\EquipmentTemplate.docx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="ExampleFile\ScrapRecord广东省中.docx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="ExampleFile\watermark.png">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="ExampleFile\仪器设备报废记录表.docx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="ExampleFile\多页PDF.pdf">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="ExampleFile\设备导入模板.xls">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="ExampleFile\设备导出清单.docx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="update_log.md">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="xhdevcert.pfx">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="服务注册.bat">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
	</ItemGroup>

	<ProjectExtensions>
		<VisualStudio>
			<UserProperties configs_4appsettings_1json__JsonSchema="" />
		</VisualStudio>
	</ProjectExtensions>

</Project>
