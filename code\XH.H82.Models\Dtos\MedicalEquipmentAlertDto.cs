﻿using Spire.Additions.Xps.Schema;

namespace XH.H82.API.Controllers.IoTDevice.Dto;

public class MedicalEquipmentAlertDto
{
    public string name { get; set; }
    public string sn { get; set; }
    public string labId { get; set; }
    public int roomId { get; set; }
    public int checkpointId { get; set; }
    public List<MonitorAlertDto> monitors { get; set; }
}


public class MonitorAlertDto
{
    public double value { get; set; }
    public int bioAlarmType { get; set; }
    public int status { get; set; }
    public string alarmTime { get; set; }
    public string sn { get; set; }
}