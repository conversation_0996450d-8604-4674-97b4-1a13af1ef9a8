{"ExtendedData": {"inputs": ["http://118.31.15.75:16700/XingHePlatform.asmx"], "collectionTypes": ["System.Array", "System.Collections.Generic.Dictionary`2"], "namespaceMappings": ["*, XingHePlatform"], "references": ["AspNetCoreRateLimit, {AspNetCoreRateLimit, 4.0.2}", "<PERSON>Mapper, {AutoMapper, 12.0.0}", "AutoMapper.Extensions.Microsoft.DependencyInjection, {AutoMapper.Extensions.Microsoft.DependencyInjection, 12.0.0}", "BouncyCastle.Crypto, {Portable.BouncyCastle, 1.9.0}", "Castle.Core, {Castle.Core, 5.1.0}", "ClickHouse.Client, {ClickHouse.Client, 6.1.1}", "CSRedisCore, {CSRedisCore, 3.6.9}", "D:\\LIS系统git\\设备管理\\H82\\XH.H82.Services\\bin\\Debug\\net6.0\\XH.H82.Base.dll", "D:\\LIS系统git\\设备管理\\H82\\XH.H82.Services\\bin\\Debug\\net6.0\\XH.H82.IServices.dll", "D:\\LIS系统git\\设备管理\\H82\\XH.H82.Services\\bin\\Debug\\net6.0\\XH.H82.Models.dll", "<PERSON><PERSON>, {<PERSON><PERSON>, 2.0.123}", "DevExpress.AspNetCore.Common.v20.1, {DevExpress.AspNetCore.Common, 20.1.3}", "DevExpress.AspNetCore.Core.v20.1, {DevExpress.AspNetCore.Core, 20.1.3}", "DevExpress.AspNetCore.Reporting.v20.1, {DevExpress.AspNetCore.Reporting, 20.1.3}", "DevExpress.AspNetCore.Resources.v20.1, {DevExpress.AspNetCore.Resources, 20.1.3}", "DevExpress.Charts.v20.1.Core, {DevExpress.Charts.Core, 20.1.3}", "DevExpress.CodeParser.v20.1, {DevExpress.CodeParser, 20.1.3}", "DevExpress.Data.v20.1, {DevExpress.Data, 20.1.3}", "DevExpress.DataAccess.v20.1, {DevExpress.DataAccess, 20.1.3}", "DevExpress.Office.v20.1.Core, {DevExpress.Office.Core, 20.1.3}", "DevExpress.Pdf.v20.1.Core, {DevExpress.Pdf.Core, 20.1.3}", "DevExpress.Pdf.v20.1.Drawing, {DevExpress.Pdf.Drawing, 20.1.3}", "DevExpress.PivotGrid.v20.1.Core, {DevExpress.PivotGrid.Core, 20.1.3}", "DevExpress.Printing.v20.1.Core, {DevExpress.Printing.Core, 20.1.3}", "DevExpress.RichEdit.v20.1.Core, {DevExpress.RichEdit.Core, 20.1.3}", "DevExpress.RichEdit.v20.1.Export, {DevExpress.RichEdit.Export, 20.1.3}", "DevExpress.Sparkline.v20.1.Core, {DevExpress.Sparkline.Core, 20.1.3}", "DevExpress.Xpo.v20.1, {DevExpress.Xpo, 20.1.3}", "DevExpress.XtraCharts.v20.1, {DevExpress.Charts, 20.1.3}", "DevExpress.XtraGauges.v20.1.Core, {DevExpress.Gauges.Core, 20.1.3}", "DevExpress.XtraReports.v20.1, {DevExpress.Reporting.Core, 20.1.3}", "DevExpress.XtraReports.v20.1.Web, {DevExpress.Web.Reporting.Common, 20.1.3}", "EasyCaching.Core, {EasyCaching.Core, 1.7.0}", "EasyCaching.CSRedis, {EasyCaching.CSRedis, 1.7.0}", "EasyCaching.InMemory, {EasyCaching.InMemory, 1.7.0}", "EasyCaching.Redis, {EasyCaching.Redis, 1.7.0}", "EasyCaching.Serialization.Json, {EasyCaching.Serialization.Json, 1.7.0}", "EasyCaching.Serialization.MessagePack, {EasyCaching.Serialization.MessagePack, 1.7.0}", "EasyCaching.Serialization.Protobuf, {EasyCaching.Serialization.Protobuf, 1.7.0}", "Elasticsearch.Net, {Elasticsearch.Net, 7.8.1}", "Enums.NET, {Enums.NET, 4.0.0}", "FireflySoft.RateLimit.AspNetCore, {FireflySoft.RateLimit.AspNetCore, 3.0.0}", "FireflySoft.RateLimit.Core, {FireflySoft.RateLimit.Core, 3.0.0}", "FSharp.Core, {FSharp.Core, 6.0.5}", "<PERSON><PERSON><PERSON><PERSON><PERSON>, {<PERSON><PERSON><PERSON><PERSON><PERSON>, 2.0.3}", "ICSharpCode.SharpZipLib, {SharpZipLib, 1.3.3}", "KYSharp.SM, {KYSharp.SM.Core, 1.0.1}", "MathNet.Numerics, {MathNet.Numerics.Signed, 4.15.0}", "MessagePack, {MessagePack, 2.3.85}", "MessagePack.Annotations, {MessagePack.Annotations, 2.3.85}", "Microsoft.AspNetCore.Antiforgery, {Microsoft.AspNetCore.Antiforgery, 2.0.0}", "Microsoft.AspNetCore.Authentication.Abstractions, {Microsoft.AspNetCore.Authentication.Abstractions, 2.0.0}", "Microsoft.AspNetCore.Authentication.Core, {Microsoft.AspNetCore.Authentication.Core, 2.0.0}", "Microsoft.AspNetCore.Authentication.JwtBearer, {Microsoft.AspNetCore.Authentication.JwtBearer, 6.0.10}", "Microsoft.AspNetCore.Authorization, {Microsoft.AspNetCore.Authorization, 2.0.0}", "Microsoft.AspNetCore.Authorization.Policy, {Microsoft.AspNetCore.Authorization.Policy, 2.0.0}", "Microsoft.AspNetCore.Cors, {Microsoft.AspNetCore.Cors, 2.0.0}", "Microsoft.AspNetCore.Cryptography.Internal, {Microsoft.AspNetCore.Cryptography.Internal, 2.0.0}", "Microsoft.AspNetCore.DataProtection, {Microsoft.AspNetCore.DataProtection, 2.0.0}", "Microsoft.AspNetCore.DataProtection.Abstractions, {Microsoft.AspNetCore.DataProtection.Abstractions, 2.0.0}", "Microsoft.AspNetCore.Diagnostics.Abstractions, {Microsoft.AspNetCore.Diagnostics.Abstractions, 2.0.0}", "Microsoft.AspNetCore.Hosting.Abstractions, {Microsoft.AspNetCore.Hosting.Abstractions, 2.0.0}", "Microsoft.AspNetCore.Hosting.Server.Abstractions, {Microsoft.AspNetCore.Hosting.Server.Abstractions, 2.0.0}", "Microsoft.AspNetCore.Html.Abstractions, {Microsoft.AspNetCore.Html.Abstractions, 2.0.0}", "Microsoft.AspNetCore.Http, {Microsoft.AspNetCore.Http, 2.0.0}", "Microsoft.AspNetCore.Http.Abstractions, {Microsoft.AspNetCore.Http.Abstractions, 2.0.0}", "Microsoft.AspNetCore.Http.Extensions, {Microsoft.AspNetCore.Http.Extensions, 2.0.0}", "Microsoft.AspNetCore.Http.Features, {Microsoft.AspNetCore.Http.Features, 2.0.0}", "Microsoft.AspNetCore.JsonPatch, {Microsoft.AspNetCore.JsonPatch, 2.0.0}", "Microsoft.AspNetCore.Localization, {Microsoft.AspNetCore.Localization, 2.0.0}", "Microsoft.AspNetCore.Mvc, {Microsoft.AspNetCore.Mvc, 2.0.0}", "Microsoft.AspNetCore.Mvc.Abstractions, {Microsoft.AspNetCore.Mvc.Abstractions, 2.0.0}", "Microsoft.AspNetCore.Mvc.ApiExplorer, {Microsoft.AspNetCore.Mvc.ApiExplorer, 2.0.0}", "Microsoft.AspNetCore.Mvc.Core, {Microsoft.AspNetCore.Mvc.Core, 2.0.0}", "Microsoft.AspNetCore.Mvc.Cors, {Microsoft.AspNetCore.Mvc.Cors, 2.0.0}", "Microsoft.AspNetCore.Mvc.DataAnnotations, {Microsoft.AspNetCore.Mvc.DataAnnotations, 2.0.0}", "Microsoft.AspNetCore.Mvc.Formatters.Json, {Microsoft.AspNetCore.Mvc.Formatters.Json, 2.0.0}", "Microsoft.AspNetCore.Mvc.Localization, {Microsoft.AspNetCore.Mvc.Localization, 2.0.0}", "Microsoft.AspNetCore.Mvc.Ra<PERSON>, {Microsoft.AspNetCore.Mvc.Razor, 2.0.0}", "Microsoft.AspNetCore.Mvc.Razor.Extensions, {Microsoft.AspNetCore.Mvc.Razor.Extensions, 2.0.0}", "Microsoft.AspNetCore.Mvc.RazorPages, {Microsoft.AspNetCore.Mvc.RazorPages, 2.0.0}", "Microsoft.AspNetCore.Mvc.TagHelpers, {Microsoft.AspNetCore.Mvc.TagHelpers, 2.0.0}", "Microsoft.AspNetCore.Mvc.ViewFeatures, {Microsoft.AspNetCore.Mvc.ViewFeatures, 2.0.0}", "Microsoft.AspNetCore.Razor, {Microsoft.AspNetCore.Razor, 2.0.0}", "Microsoft.AspNetCore.Razor.Language, {Microsoft.AspNetCore.Razor.Language, 2.0.0}", "Microsoft.AspNetCore.Razor.Runtime, {Microsoft.AspNetCore.Razor.Runtime, 2.0.0}", "Microsoft.AspNetCore.ResponseCaching.Abstractions, {Microsoft.AspNetCore.ResponseCaching.Abstractions, 2.0.0}", "Microsoft.AspNetCore.Routing, {Microsoft.AspNetCore.Routing, 2.0.0}", "Microsoft.AspNetCore.Routing.Abstractions, {Microsoft.AspNetCore.Routing.Abstractions, 2.0.0}", "Microsoft.AspNetCore.WebUtilities, {Microsoft.AspNetCore.WebUtilities, 2.0.0}", "Microsoft.Bcl.AsyncInterfaces, {Microsoft.Bcl.AsyncInterfaces, 6.0.0}", "Microsoft.CodeAnalysis, {Microsoft.CodeAnalysis.Common, 2.3.1}", "Microsoft.CodeAnalysis.CSharp, {Microsoft.CodeAnalysis.CSharp, 2.3.1}", "Microsoft.CodeAnalysis.Razor, {Microsoft.CodeAnalysis.Razor, 2.0.0}", "Microsoft.Data.SqlClient, {Microsoft.Data.SqlClient, 2.1.4}", "Microsoft.EntityFrameworkCore, {Microsoft.EntityFrameworkCore, 6.0.11}", "Microsoft.EntityFrameworkCore.Abstractions, {Microsoft.EntityFrameworkCore.Abstractions, 6.0.11}", "Microsoft.EntityFrameworkCore.Relational, {Microsoft.EntityFrameworkCore.Relational, 6.0.11}", "Microsoft.EntityFrameworkCore.SqlServer, {Microsoft.EntityFrameworkCore.SqlServer, 6.0.11}", "Microsoft.Extensions.Caching.Abstractions, {Microsoft.Extensions.Caching.Abstractions, 6.0.0}", "Microsoft.Extensions.Caching.Memory, {Microsoft.Extensions.Caching.Memory, 6.0.1}", "Microsoft.Extensions.Configuration, {Microsoft.Extensions.Configuration, 2.0.0}", "Microsoft.Extensions.Configuration.Abstractions, {Microsoft.Extensions.Configuration.Abstractions, 6.0.0}", "Microsoft.Extensions.Configuration.Binder, {Microsoft.Extensions.Configuration.Binder, 6.0.0}", "Microsoft.Extensions.Configuration.FileExtensions, {Microsoft.Extensions.Configuration.FileExtensions, 2.0.0}", "Microsoft.Extensions.Configuration.Json, {Microsoft.Extensions.Configuration.Json, 2.0.0}", "Microsoft.Extensions.DependencyInjection, {Microsoft.Extensions.DependencyInjection, 6.0.1}", "Microsoft.Extensions.DependencyInjection.Abstractions, {Microsoft.Extensions.DependencyInjection.Abstractions, 6.0.0}", "Microsoft.Extensions.DependencyModel, {Microsoft.Extensions.DependencyModel, 3.1.6}", "Microsoft.Extensions.FileProviders.Abstractions, {Microsoft.Extensions.FileProviders.Abstractions, 3.1.8}", "Microsoft.Extensions.FileProviders.Composite, {Microsoft.Extensions.FileProviders.Composite, 2.0.0}", "Microsoft.Extensions.FileProviders.Physical, {Microsoft.Extensions.FileProviders.Physical, 2.0.0}", "Microsoft.Extensions.FileSystemGlobbing, {Microsoft.Extensions.FileSystemGlobbing, 2.0.0}", "Microsoft.Extensions.Hosting.Abstractions, {Microsoft.Extensions.Hosting.Abstractions, 3.1.8}", "Microsoft.Extensions.Http, {Microsoft.Extensions.Http, 6.0.0}", "Microsoft.Extensions.Localization, {Microsoft.Extensions.Localization, 2.0.0}", "Microsoft.Extensions.Localization.Abstractions, {Microsoft.Extensions.Localization.Abstractions, 2.0.0}", "Microsoft.Extensions.Logging, {Microsoft.Extensions.Logging, 6.0.0}", "Microsoft.Extensions.Logging.Abstractions, {Microsoft.Extensions.Logging.Abstractions, 6.0.1}", "Microsoft.Extensions.ObjectPool, {Microsoft.Extensions.ObjectPool, 5.0.10}", "Microsoft.Extensions.Options, {Microsoft.Extensions.Options, 6.0.0}", "Microsoft.Extensions.Options.ConfigurationExtensions, {Microsoft.Extensions.Options.ConfigurationExtensions, 6.0.0}", "Microsoft.Extensions.Primitives, {Microsoft.Extensions.Primitives, 6.0.0}", "Microsoft.Extensions.WebEncoders, {Microsoft.Extensions.WebEncoders, 2.0.0}", "Microsoft.IdentityModel.JsonWebTokens, {Microsoft.IdentityModel.JsonWebTokens, 6.10.0}", "Microsoft.IdentityModel.Logging, {Microsoft.IdentityModel.Logging, 6.10.0}", "Microsoft.IdentityModel.Protocols, {Microsoft.IdentityModel.Protocols, 6.10.0}", "Microsoft.IdentityModel.Protocols.OpenIdConnect, {Microsoft.IdentityModel.Protocols.OpenIdConnect, 6.10.0}", "Microsoft.IdentityModel.Tokens, {Microsoft.IdentityModel.Tokens, 6.10.0}", "Microsoft.IO.RecyclableMemoryStream, {Microsoft.IO.RecyclableMemoryStream, 2.2.0}", "Microsoft.Net.Http.Headers, {Microsoft.Net.Http.Headers, 2.0.0}", "Microsoft.OpenApi, {Microsoft.OpenApi, 1.3.1}", "Microsoft.Win32.Registry, {Microsoft.Win32.Registry, 4.7.0}", "Microsoft.Win32.SystemEvents, {Microsoft.Win32.SystemEvents, 6.0.0}", "MySqlConnector, {MySqlConnector, 2.1.2}", "Newtonsoft.J<PERSON>, {Newtonsoft<PERSON>J<PERSON>, 13.0.1}", "Newtonsoft.Json.<PERSON>, {Newtonsoft.Json.Bson, 1.0.1}", "Nito.AsyncEx.Context, {Nito.AsyncEx.Context, 5.1.0}", "Nito.AsyncEx.Coordination, {Nito.AsyncEx.Coordination, 5.1.0}", "Nito.AsyncEx.Interop.WaitHandles, {Nito.AsyncEx.Interop.WaitHandles, 5.1.0}", "Nito.AsyncEx.Oop, {Nito.AsyncEx.Oop, 5.1.0}", "Nito.AsyncEx.Tasks, {Nito.AsyncEx.Tasks, 5.1.0}", "<PERSON><PERSON>.Cancell<PERSON>, {<PERSON><PERSON>.Cancellation, 1.1.0}", "Nito.Collections.Deque, {Nito.Collections.Deque, 1.1.0}", "Nito.Disposables, {Nito.Disposables, 2.2.0}", "NodaTime, {NodaTime, 3.1.4}", "NPOI, {NPOI, 2.6.0}", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, {<PERSON><PERSON><PERSON><PERSON>, 4.1.0}", "NPOI.OOXML, {NPOI, 2.6.0}", "NPOI.OpenXml4Net, {NPOI, 2.6.0}", "NPOI.OpenXmlFormats, {NPOI, 2.6.0}", "Oracle.EntityFramework<PERSON><PERSON>, {Oracle.EntityFrameworkCore, 6.21.61}", "Oracle.ManagedDataAccess, {Oracle.ManagedDataAccess.Core, 3.21.80}", "Pipelines.Sockets.Unofficial, {Pipelines.Sockets.Unofficial, 2.2.2}", "Pomelo.EntityFrameworkCore.MySql, {Pomelo.EntityFrameworkCore.MySql, 6.0.2}", "protobuf-net, {protobuf-net, 3.1.0}", "protobuf-net.Core, {protobuf-net.Core, 3.1.22}", "RestSharp, {RestSharp, 108.0.2}", "<PERSON><PERSON><PERSON>, {<PERSON><PERSON><PERSON>, 3.3.0}", "<PERSON><PERSON><PERSON>, {Serilog, 2.11.0}", "Serilog.AspNetCore, {Serilog.AspNetCore, 6.0.1}", "Serilog.Extensions.Hosting, {Serilog.Extensions.Hosting, 5.0.1}", "Serilog.Extensions.Logging, {Serilog.Extensions.Logging, 3.1.0}", "Serilog.Formatting.Compact, {Serilog.Formatting.Compact, 1.1.0}", "Serilog.Formatting.Elasticsearch, {Serilog.Formatting.Elasticsearch, 8.4.1}", "Serilog.Settings.Configuration, {Serilog.Settings.Configuration, 3.3.0}", "Serilog.Sinks.Async, {Serilog.Sinks.Async, 1.5.0}", "Serilog.<PERSON><PERSON>.<PERSON>, {Serilog.Sinks.Console, 4.1.0}", "Serilog.Sinks.Debug, {Serilog.Sinks.Debug, 2.0.0}", "Serilog.Sinks.Elasticsearch, {Serilog.Sinks.Elasticsearch, 8.4.1}", "Serilog.Sinks.FastConsole, {Serilog.Sinks.FastConsole, 2.2.0}", "Serilog.Sinks.File, {Serilog.Sinks.File, 5.0.0}", "Serilog.Sinks.PeriodicBatching, {Serilog.Sinks.PeriodicBatching, 2.1.1}", "Serilog.Sinks.SpectreConsole, {Serilog.Sinks.SpectreConsole, 0.3.3}", "SixLabors.Fonts, {SixLabors.Fonts, 1.0.0-beta18}", "SixLabors.ImageSharp, {SixLabors.ImageSharp, 2.1.3}", "Snowflake.Core, {Snowflake.Core, 2.0.0}", "<PERSON><PERSON><PERSON><PERSON>, {<PERSON><PERSON><PERSON><PERSON>, 0.45.0}", "Spire.Doc, {S<PERSON>.<PERSON>, 11.3.1}", "Spire.Pdf, {Spire.Doc, 11.3.1}", "Spire.XLS, {Spire.XLS, 13.3.2}", "StackExchange.Redis, {StackExchange.Redis, 2.5.43}", "Swashbuckle.AspNetCore.Filters, {Swashbuckle.AspNetCore.Filters, 7.0.5}", "Swashbuckle.AspNetCore.Filters.Abstractions, {Swashbuckle.AspNetCore.Filters.Abstractions, 7.0.5}", "Swashbuckle.AspNetCore.Swagger, {Swashbuckle.AspNetCore.Swagger, 6.2.3}", "Swashbuckle.AspNetCore.SwaggerGen, {Swashbuckle.AspNetCore.SwaggerGen, 6.2.3}", "Swashbuckle.AspNetCore.SwaggerUI, {Swashbuckle.AspNetCore.SwaggerUI, 6.2.3}", "System.AppContext, {System.AppContext, 4.3.0}", "System.CodeDom, {System.CodeDom, 4.4.0}", "System.Collections, {System.Collections, 4.3.0}", "System.Collections.Concurrent, {System.Collections.Concurrent, 4.3.0}", "System.Collections.Immutable, {System.Collections.Immutable, 6.0.0}", "System.Configuration.ConfigurationManager, {System.Configuration.ConfigurationManager, 6.0.0}", "<PERSON><PERSON>Console, {<PERSON><PERSON>Console, 4.3.0}", "System.Data.Common, {System.Data.Common, 4.3.0}", "System.Data.SqlClient, {System.Data.SqlClient, 4.8.1}", "System.Diagnostics.Debug, {System.Diagnostics.Debug, 4.3.0}", "System.Diagnostics.DiagnosticSource, {System.Diagnostics.DiagnosticSource, 6.0.0}", "System.Diagnostics.EventLog, {System.Diagnostics.EventLog, 6.0.0}", "System.Diagnostics.PerformanceCounter, {System.Diagnostics.PerformanceCounter, 6.0.0}", "System.Diagnostics.Tools, {System.Diagnostics.Tools, 4.3.0}", "System.DirectoryServices, {System.DirectoryServices, 5.0.0}", "System.DirectoryServices.Protocols, {System.DirectoryServices.Protocols, 5.0.1}", "System.Drawing.Common, {System.Drawing.Common, 6.0.0}", "System.Dynamic.Runtime, {System.Dynamic.Runtime, 4.3.0}", "System.Formats.Asn1, {System.Formats.Asn1, 6.0.0}", "System.Globalization, {System.Globalization, 4.3.0}", "System.IdentityModel.Tokens.Jwt, {System.IdentityModel.Tokens.Jwt, 6.10.0}", "System.IO, {System.IO, 4.3.0}", "System.IO.Compression, {System.IO.Compression, 4.3.0}", "System.IO.FileSystem, {System.IO.FileSystem, 4.3.0}", "System.IO.FileSystem.AccessControl, {System.IO.FileSystem.AccessControl, 5.0.0}", "System.IO.FileSystem.Primitives, {System.IO.FileSystem.Primitives, 4.3.0}", "System.IO.Pipelines, {System.IO.Pipelines, 5.0.1}", "<PERSON><PERSON>, {System.Linq, 4.3.0}", "System.Linq.<PERSON>, {System.Linq.Async, 5.1.0}", "System.Linq.Expressions, {System.Linq.Expressions, 4.3.0}", "System.Net.Http, {System.Net.Http, 4.3.4}", "System.Net.Primitives, {System.Net.Primitives, 4.3.0}", "System.ObjectModel, {System.ObjectModel, 4.3.0}", "System.Reflection, {System.Reflection, 4.3.0}", "System.Reflection.DispatchProxy, {System.Reflection.DispatchProxy, 4.7.1}", "System.Reflection.Emit, {System.Reflection.Emit, 4.3.0}", "System.Reflection.Emit.ILGeneration, {System.Reflection.Emit.ILGeneration, 4.3.0}", "System.Reflection.Emit.Lightweight, {System.Reflection.Emit.Lightweight, 4.3.0}", "System.Reflection.Extensions, {System.Reflection.Extensions, 4.3.0}", "System.Reflection.Metadata, {System.Reflection.Metadata, 1.4.2}", "System.Reflection.Primitives, {System.Reflection.Primitives, 4.3.0}", "System.Resources.ResourceManager, {System.Resources.ResourceManager, 4.3.0}", "System.Runtime, {System.Runtime, 4.3.1}", "System.Runtime.CompilerServices.Unsafe, {System.Runtime.CompilerServices.Unsafe, 6.0.0}", "System.Runtime.Extensions, {System.Runtime.Extensions, 4.3.0}", "System.Runtime.Handles, {System.Runtime.Handles, 4.3.0}", "System.Runtime.InteropServices, {System.Runtime.InteropServices, 4.3.0}", "System.Runtime.Numerics, {System.Runtime.Numerics, 4.3.0}", "System.Security.AccessControl, {System.Security.AccessControl, 6.0.0}", "System.Security.Cryptography.Algorithms, {System.Security.Cryptography.Algorithms, 4.3.0}", "System.Security.Cryptography.Cng, {System.Security.Cryptography.Cng, 4.5.0}", "System.Security.Cryptography.Encoding, {System.Security.Cryptography.Encoding, 4.3.0}", "System.Security.Cryptography.Pkcs, {System.Security.Cryptography.Pkcs, 6.0.1}", "System.Security.Cryptography.Primitives, {System.Security.Cryptography.Primitives, 4.3.0}", "System.Security.Cryptography.ProtectedData, {System.Security.Cryptography.ProtectedData, 6.0.0}", "System.Security.Cryptography.X509Certificates, {System.Security.Cryptography.X509Certificates, 4.3.0}", "System.Security.Cryptography.Xml, {System.Security.Cryptography.Xml, 6.0.1}", "System.Security.Permissions, {System.Security.Permissions, 6.0.0}", "System.Security.Principal.Windows, {System.Security.Principal.Windows, 5.0.0}", "System.ServiceModel, {System.ServiceModel.Primitives, 4.10.0}", "System.ServiceModel.Duplex, {System.ServiceModel.Duplex, 4.8.1}", "System.ServiceModel.Http, {System.ServiceModel.Http, 4.10.0}", "System.ServiceModel.NetTcp, {System.ServiceModel.NetTcp, 4.8.1}", "System.ServiceModel.Primitives, {System.ServiceModel.Primitives, 4.10.0}", "System.ServiceModel.Security, {System.ServiceModel.Security, 4.8.1}", "System.Text.Encoding, {System.Text.Encoding, 4.3.0}", "System.Text.Encoding.CodePages, {System.Text.Encoding.CodePages, 6.0.0}", "System.Text.Encoding.Extensions, {System.Text.Encoding.Extensions, 4.3.0}", "System.Text.Encodings.Web, {System.Text.Encodings.Web, 6.0.0}", "System.<PERSON>.<PERSON>, {System.Text.<PERSON>, 6.0.6}", "System.Threading, {System.Threading, 4.3.0}", "System.Threading.AccessControl, {System.Threading.AccessControl, 4.4.0}", "System.Threading.Channels, {System.Threading.Channels, 6.0.0}", "System.Threading.Tasks, {System.Threading.Tasks, 4.3.0}", "System.Threading.Tasks.Parallel, {System.Threading.Tasks.Parallel, 4.3.0}", "System.Threading.Timer, {System.Threading.Timer, 4.0.1}", "System.Windows.Extensions, {System.Windows.Extensions, 6.0.0}", "System.Xml.ReaderWriter, {System.Xml.ReaderWriter, 4.3.0}", "System.Xml.XDocument, {System.Xml.XDocument, 4.3.0}", "System.Xml.XmlSerializer, {System.Xml.XmlSerializer, 4.3.0}", "Unchase.Swashbuckle.AspNetCore.Extensions, {Unchase.Swashbuckle.AspNetCore.Extensions, 2.6.12}", "Xinghe.Utility, {Xinghe.Utility, 1.1.22}", "Z.<PERSON>ti<PERSON>Framework.Extensions.EFCore, {Xinghe.Utility, 1.1.22}", "Z.<PERSON>tityFramework.Plus.EFCore, {Xinghe.Utility, 1.1.22}"], "targetFramework": "net6.0", "typeReuseMode": "All"}}