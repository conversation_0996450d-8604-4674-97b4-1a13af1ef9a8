using H.Utility;
using XH.H82.Models.Dtos.EquipmentAdditionalRecord;
using XH.H82.Models.EquipmengtClassNew;

namespace XH.H82.IServices
{
    /// <summary>
    /// 设备扩展记录服务接口
    /// </summary>
    public interface IEquipmentAdditionalRecordService
    {
        /// <summary>
        /// 根据设备ID和档案记录ID获取设备扩展记录
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <param name="eqpArchivesId">档案记录ID</param>
        /// <returns>设备扩展记录</returns>
        EMS_EQP_ADDN_RECORD? GetEquipmentAdditionalRecord(string equipmentId, string eqpArchivesId);

        /// <summary>
        /// 根据设备ID获取所有扩展记录
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <returns>设备扩展记录列表</returns>
        List<EMS_EQP_ADDN_RECORD> GetEquipmentAdditionalRecords(string equipmentId);

        /// <summary>
        /// 获取设备的档案记录详细信息（包含扩展字段）
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <param name="eqpArchivesId">档案记录ID</param>
        /// <returns>档案记录详细信息</returns>
        EquipmentArchiveDetailDto GetEquipmentArchiveDetail(string equipmentId, string eqpArchivesId);

        /// <summary>
        /// 获取设备的所有档案记录详细信息列表
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <returns>档案记录详细信息列表</returns>
        List<EquipmentArchiveDetailDto> GetEquipmentArchiveDetails(string equipmentId);

        /// <summary>
        /// 保存或更新设备扩展记录
        /// </summary>
        /// <param name="record">设备扩展记录</param>
        /// <returns>操作结果</returns>
        ResultDto SaveEquipmentAdditionalRecord(EMS_EQP_ADDN_RECORD record);

        /// <summary>
        /// 删除设备扩展记录
        /// </summary>
        /// <param name="eqpRecordId">记录ID</param>
        /// <returns>操作结果</returns>
        ResultDto DeleteEquipmentAdditionalRecord(string eqpRecordId);
    }
}
