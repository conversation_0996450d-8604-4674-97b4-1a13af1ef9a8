﻿using AutoMapper;
using AutoMapper.Configuration.Annotations;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XH.H82.Models.Entities.OperationLog;

namespace XH.H82.Models.Dtos
{

    [AutoMap(typeof(EMS_OPER_LOG))]
    public class BaseCirculationRecordDto
    {
        /// <summary>
        /// 操作记录ID
        /// </summary>
        /// 
        [SourceMember("OPER_LOGID")]
        public string  Id { get; set; }

        /// <summary>
        /// 流程状态
        /// </summary>
        [SourceMember("OPER_STATE")]
        public OperationStateEnum InitiatorState { get; set; }

        /// <summary>
        /// 流程状态中文
        /// </summary>
        [SourceMember("OPER_NAME")]
        public string InitiatorStateValue { get; set; }

        /// <summary>
        /// 流程发起人
        /// </summary>
        [SourceMember("OPER_PERSON")]
        public string InitiatorName { get; set; }

        /// <summary>
        /// 处理时间
        /// </summary>
        [SourceMember("LAST_MTIME")]
        public DateTime InitiatorDate { get; set; }

        /// <summary>
        /// 处理内容
        /// </summary>
        [SourceMember("OPER_CONTENT")]
        public string? Content { get; set; }


        /// <summary>
        /// 操作记录ID
        /// </summary>
        [SourceMember("PREV_OPER_ID")] 
        public string ForwardId { get; set; }

    }


    /// <summary>
    /// 流程处理记录Dto
    /// </summary>
    /// 
    [AutoMap(typeof(EMS_OPER_LOG))]
    public class EmsWorkPlanCirculationRecordDto : BaseCirculationRecordDto
    {

    }




}
