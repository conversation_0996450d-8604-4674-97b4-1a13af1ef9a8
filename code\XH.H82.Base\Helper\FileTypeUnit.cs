﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Base.Helper
{
    public static class FileTypeUnit
    {
        

        private static void CheckBlackList( string fileSuffix, IEnumerable<string> suffixs) {

            if (suffixs is null)
            {
                return;
            }
            if (suffixs.Contains(fileSuffix))
            {
                throw new ArgumentOutOfRangeException($"当前不支持类型{fileSuffix}.文件");
            }
        }


        public static string ReturnFileType(string suffix , IEnumerable<string> blackList = null) {


            CheckBlackList(suffix, blackList);

            var docType = suffix switch
            {
                ".docx" => "WORD",
                ".doc" => "WORD",
                ".pdf" => "PDF",
                ".xlsx" => "EXCEL",
                ".xls" => "EXCEL",
                ".jpg" => "IMG",
                ".png" => "IMG",
                _ => throw new ArgumentOutOfRangeException($"当前不支持类型{suffix}.文件")
            };
            return docType;
        }
    }
}
