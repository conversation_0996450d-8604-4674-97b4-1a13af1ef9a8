﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Services.OperationLog
{
    /// <summary>
    /// 提交工作计划操作模型
    /// </summary>
    public class SubmitWorkPlanInput : CheckUserPasswordInput
    {
        /// <summary>
        /// 工作计划ID  数组
        /// </summary>
        [MinLength(1,ErrorMessage ="请勾选数据")]
        public List<string> WorkPlanIds { get; set; } = new List<string>();
        /// <summary>
        /// 审核人姓名   userName
        /// </summary>
        [Required(ErrorMessage = "必填审核人姓名")]
        public string AuditorName { get; set; }

        /// <summary>
        /// 审核人ID    userNo
        /// </summary>
        [Required(ErrorMessage = "必填审核人的userNo")]
        public string AuditorId { get; set; }

    }


    /// <summary>
    /// 审核工作计划操作模型
    /// </summary>
    public class AuditWorkPlanInput : CheckUserPasswordInput
    {
        /// <summary>
        /// 工作计划ID  数组
        /// </summary>
        [MinLength(1, ErrorMessage = "请勾选数据")]
        public List<string> WorkPlanIds { get; set; } = new List<string>();
        /// <summary>
        /// 审核意见
        /// </summary>
        public string? Content { get; set; }

    }

    public class OverruledWorkPlanInput : CheckUserPasswordInput
    {
        /// <summary>
        /// 工作计划ID  数组
        /// </summary>
        [MinLength(1, ErrorMessage = "请勾选数据")]
        public List<string> WorkPlanIds { get; set; } = new List<string>();

        /// <summary>
        /// 驳回原由
        /// </summary>
        public string? Content { get; set; }

    }


    /// <summary>
    /// 操作前检查用户密码
    /// </summary>
    public class CheckUserPasswordInput
    {
        /// <summary>
        /// 用户密码
        /// </summary>
        [Required(ErrorMessage = "请填写密码")]
        public string Password { get; set; }

    }

}
