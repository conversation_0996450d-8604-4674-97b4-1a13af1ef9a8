/*2025/3/12 新增设备检测点阶段监测汇总表*/
CREATE TABLE XH_DATA.THS_MONITOR_HOUR(
    MONITOR_HOURID VARCHAR2(50) NOT NULL,
    EQUIPMENT_ID VARCHAR2(20) NOT NULL,
    POINT_ID VARCHAR2(20) NOT NULL,
    UNIT_ID VARCHAR2(20) NOT NULL,
    HOSPITAL_ID VARCHAR2(20) NOT NULL,
    ITEM_ID VARCHAR2(20) NOT NULL,
    MONITOR_TYPE VARCHAR2(20),
    MONITOR_DATE DATE,
    TIME_POINT_HOUR VARCHAR2(20),
    TOTAL_NUM INT,
    NORMAL_NUM INT,
    ABNORMAL_NUM INT,
    MAX_VALUE NUMBER(18,4),
    MIN_VALUE NUMBER(18,4),
    AVG_VALUE NUMBER(18,4),
    ACCUM_VALUE NUMBER(18,4),
    ALARM_NUM INT,
    OPER_PERSON VARCHAR2(50),
    O<PERSON>ER_TIME DATE,
    OPER_COMPUTER VARCHAR2(50),
    MON<PERSON>OR_HOUR_STATE VARCHAR2(20) DEFAULT  '1',
    FIRST_RPERSON VARCHAR2(50),
    FIRST_RTIME DATE,
    LAST_MPERSON VARCHAR2(50),
    LAST_MTIME DATE,
    REMARK VARCHAR2(200)
);
alter table XH_DATA.THS_MONITOR_HOUR add constraint PK_OA_THS_MONITOR_HOUR primary key (MONITOR_HOURID);

COMMENT ON TABLE XH_DATA.THS_MONITOR_HOUR IS '设备检测点阶段监测汇总表';
COMMENT ON COLUMN XH_DATA.THS_MONITOR_HOUR.MONITOR_HOURID IS '阶段监测记录ID';
COMMENT ON COLUMN XH_DATA.THS_MONITOR_HOUR.EQUIPMENT_ID IS '设备ID';
COMMENT ON COLUMN XH_DATA.THS_MONITOR_HOUR.POINT_ID IS '监测点ID';
COMMENT ON COLUMN XH_DATA.THS_MONITOR_HOUR.UNIT_ID IS '管理单元ID';
COMMENT ON COLUMN XH_DATA.THS_MONITOR_HOUR.HOSPITAL_ID IS '医疗机构ID';
COMMENT ON COLUMN XH_DATA.THS_MONITOR_HOUR.ITEM_ID IS '监测指标ID';
COMMENT ON COLUMN XH_DATA.THS_MONITOR_HOUR.MONITOR_TYPE IS '监测类型';
COMMENT ON COLUMN XH_DATA.THS_MONITOR_HOUR.MONITOR_DATE IS '记录日期';
COMMENT ON COLUMN XH_DATA.THS_MONITOR_HOUR.TIME_POINT_HOUR IS '监测记录小时范围';
COMMENT ON COLUMN XH_DATA.THS_MONITOR_HOUR.TOTAL_NUM IS '总记录数';
COMMENT ON COLUMN XH_DATA.THS_MONITOR_HOUR.NORMAL_NUM IS '正常记录数';
COMMENT ON COLUMN XH_DATA.THS_MONITOR_HOUR.ABNORMAL_NUM IS '异常记录数';
COMMENT ON COLUMN XH_DATA.THS_MONITOR_HOUR.MAX_VALUE IS '最高值';
COMMENT ON COLUMN XH_DATA.THS_MONITOR_HOUR.MIN_VALUE IS '最低值';
COMMENT ON COLUMN XH_DATA.THS_MONITOR_HOUR.AVG_VALUE IS '平均值';
COMMENT ON COLUMN XH_DATA.THS_MONITOR_HOUR.ACCUM_VALUE IS '累计时长';
COMMENT ON COLUMN XH_DATA.THS_MONITOR_HOUR.ALARM_NUM IS '报警记录数';
COMMENT ON COLUMN XH_DATA.THS_MONITOR_HOUR.OPER_PERSON IS '处理人员';
COMMENT ON COLUMN XH_DATA.THS_MONITOR_HOUR.OPER_TIME IS '处理时间';
COMMENT ON COLUMN XH_DATA.THS_MONITOR_HOUR.OPER_COMPUTER IS '处理电脑';
COMMENT ON COLUMN XH_DATA.THS_MONITOR_HOUR.MONITOR_HOUR_STATE IS '状态;1未确认 2已确认';
COMMENT ON COLUMN XH_DATA.THS_MONITOR_HOUR.FIRST_RPERSON IS '首次登记人';
COMMENT ON COLUMN XH_DATA.THS_MONITOR_HOUR.FIRST_RTIME IS '首次登记时间';
COMMENT ON COLUMN XH_DATA.THS_MONITOR_HOUR.LAST_MPERSON IS '最后修改人员';
COMMENT ON COLUMN XH_DATA.THS_MONITOR_HOUR.LAST_MTIME IS '最后修改时间';
COMMENT ON COLUMN XH_DATA.THS_MONITOR_HOUR.REMARK IS '备注';
grant select, insert, update, delete on XH_DATA.THS_MONITOR_HOUR to XH_COM;