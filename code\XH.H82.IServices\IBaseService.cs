﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Elastic.Clients.Elasticsearch;
using XH.H82.Models.Dtos;
using XH.H82.Models.Entities;
using XH.H82.Models.Entities.Common;
using H.Utility;
using XH.H82.Models.Dtos.Base;
using XH.LAB.UTILS.Models;

namespace XH.H82.IServices
{
    public interface IBaseService
    {
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        ResultDto GetUserLabList(string userNo);
        ResultDto GetMgroupList(string userNo, string hospitalId, string labId, string areaId, string? pGroupId);

        /// <summary>
        /// 获取所有院区的专业组下拉
        /// </summary>
        /// <param name="userNo"></param>
        /// <param name="labId"></param>
        /// <returns></returns>
        List<NewOATreeDto> GetAllHospitalMgroups(string userNo, string labId);
        List<EMS_DOC_INFO> GetEnclosureInfo(string DOC_CLASS, string DOC_INFO_ID, string file_upload_address);
        ResultDto EditEnclosureInfo(EMS_DOC_INFO doc_file);

        ResultDto BatchInsetEnclosureInfo(List<EMS_DOC_INFO> record, string userName);
        ResultDto BatchEditEnclosureInfo(List<EMS_DOC_INFO> record, string userName);
        ResultDto UploadEnclosureInfo(UploadFileDto uploadFiles, string userName, string hospitalId);
        //ResultDto UploadEnclosureInfo(EMS_DOC_INFO doc_file, string file_upload_address);
        ResultDto UploadFileOperate(string fileName, string base64String, string file_upload_address, string file_suffix);
        ResultDto DeleteEnclosureInfo(string doc_id, string userName);

        ResultDto GetAdverseEventInfo(string equipment_id, string file_preview_address);
        ResultDto GetMenuInfo(string hospitalId, string moduleId, string userNo);
        ResultDto GetFuncDictInfo(string menuId, string moduleId, string hospitalId);
        List<SYS6_BASE_DATA> GetCompanyClassList();
        ResultDto GetAreaPullList(string userNo, string labId, string hospitalId);
        List<EMS_DOC_INFO> GetStagingAreaFiles(string DOC_CLASS);
        void AddStagingAreaFiles(UploadStagingAreaFileDto uploadFile);
        void UnUseStagingAreaFile(string docId);
        void UseStagingAreaFile(string docId, string docInfoId);
        //sys6UserDto Test();


        public (string uploadFileNmae, string uploadFileTYPE, byte[] uploadFileByte) GetUploadFileInfo(
            UploadFileDto uploadFile, string? fileMinName);


        MenuInfoDto GetMenuInfoByPermission(MenuClassEnum includeMenuClass );
    }
}
