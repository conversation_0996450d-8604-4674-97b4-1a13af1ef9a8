﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using SqlSugar;
using System.Threading.Tasks;
using H.Utility.SqlSugarInfra;

namespace XH.H82.Models.Entities
{
    [DBOwner("XH_SYS")]
    [SugarTable("SYS6_COMPANY_CONTACT")]
    public class SYS6_COMPANY_CONTACT
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string CONTACT_ID { get; set; }
        public string HOSPITAL_ID { get; set; }
        public string COMPANY_ID { get; set; }
        public string PRODUCT_ID { get; set; }
        public string CONTACT_TYPE { get; set; }
        public string CONTACT_POST { get; set; }
        public string CONTACT_NAME { get; set; }
        public string CONTACT_SEX { get; set; }
        public string CONTACT_AGE { get; set; }
        public string CONTACT_SORT { get; set; }
        public string CONTACT_ADDRESS { get; set; }
        public string POSTALCODE { get; set; }
        public string PHONE_NO { get; set; }
        public string CONTACT_QQ { get; set; }
        public string CONTACT_WX { get; set; }
        public string E_MAIL { get; set; }
        public string SPELL_CODE { get; set; }
        public string CONTACT_STATE { get; set; }
        public string FIRST_RPERSON { get; set; }
        public DateTime? FIRST_RTIME { get; set; }
        public string LAST_MPERSON { get; set; }
        public DateTime? LAST_MTIME { get; set; }
        public string REMARK { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string IF_SELECT { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string COMPANY_NAME { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string COMPANY_TYPE { get; set; }
    }
}
