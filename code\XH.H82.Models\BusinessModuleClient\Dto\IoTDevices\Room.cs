using Newtonsoft.Json;

namespace XH.H82.Models.BusinessModuleClient.Dto.IoTDevices;

public class Room
{
    /// <summary>
    /// 房间三方主键
    /// </summary>
    [JsonProperty("id")]
    public int Id { get; set; }

    /// <summary>
    /// 房间名字
    /// </summary>
    [JsonProperty("name")]
    public string Name { get; set; }

    /// <summary>
    /// 实验室id
    /// </summary>
    [JsonProperty("labId")]
    public string? LabId { get; set; }
    /// <summary>
    /// 实验室名称
    /// </summary>
    [JsonProperty("labName")]
    public string? LabName { get; set;}

    /// <summary>
    /// 父级房间id
    /// </summary>
    [JsonProperty("parentId")]
    public int ParentId { get; set; }
}