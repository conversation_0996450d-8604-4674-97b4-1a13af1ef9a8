using System.Timers;
using EasyCaching.Core;
using H.Utility.Helper;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Serilog;
using StackExchange.Redis;
using XH.H82.IServices.IoTDevice;
using Timer = System.Timers.Timer;

namespace XH.H82.Services.ScheduleJobs;

public class LampDeviceSync : BackgroundService
{
    private readonly IIoTDeviceService _deviceService;
        private readonly IConfiguration _configuration;
        const string lockName = "XH:CACHE:EMS.LAMP.SYNC.H82";

        private Timer _timer;
        public LampDeviceSync(IIoTDeviceService deviceService, IConfiguration configuration)
        {
            _deviceService = deviceService;
            _configuration = configuration;
            _timer = new Timer(1000 * 60 * 2)
            {
                AutoReset = true,
                Enabled = true
            };
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _timer.Elapsed += OnTimedEvent;
            _timer.Start();
        }


        private void OnTimedEvent(object? source, ElapsedEventArgs e)
        {
            Log.Information("紫外灯任务开始执行");
            Task.Factory.StartNew(async () =>
            {
                try
                {
                    if (await GetTheLock())
                    {
                        Log.Information("紫外灯获取监控数据开始");
                        var now = DateTime.Now;
                        var equipments= _deviceService.GetObtainMonitoringEquipments()
                            .Where(x=>x.SMBL_CLASS == "5");
                        foreach (var equipment in equipments)
                        {
                            var data = _deviceService.GetUltravioletLampDeviceData(equipment);
                            if (data is null)
                            {
                                Log.Information($"{now.ToString("yyyy-MM-dd HH:mm:ss")},获取设备监控信息失败，设备代号为{equipment.EQUIPMENT_CODE}，设备序列号为{equipment.SERIAL_NUMBER}");
                                continue;
                            }
                            _deviceService.AddLampObtainMonitoring(equipment, data);
                        }
                        Log.Information("获取监控数据结束！");
                    }
                    else
                    {
                        await GetTheLockFalse();
                    }
                }
                catch (Exception e)
                {
                    var redis = XhRedisHelper.UseS02Client();
                    var redis02 = XhRedisHelper.UseS02();
                    var isDeleted = await redis.RemoveAsync(lockName);
                    if (isDeleted)
                    {
                        Log.Information($"已有停止正在执行的定时任务，服务名：EvnDeviceSync");
                    }
                    else
                    { 
                        var serviceName = await redis.GetAsync<string>(lockName);
                        var ttl = await redis02.KeyTimeToLiveAsync(lockName);
                        if (ttl.HasValue)
                        {
                            Log.Error($"已有停止正在执行的定时任务，服务名{serviceName}：EvnDeviceSync,缓存键删除失败，剩余时长为:{ttl}秒");
                        }
                    }
                    Log.Error($"自动刷新设备信息失败：{e.Message}");
                }
            });
        }

        private async Task<bool> GetTheLock()
        {
            //var time = DateTime.Now.AddDays(1).Date - DateTime.Now;

            //  var time = TimeSpan.FromMinutes(30);

            var minutes = _configuration.GetSection("IoTSyncTime").Value ?? "15";
            
            var time = TimeSpan.FromMinutes(int.Parse(minutes));

            var redis = XhRedisHelper.UseS02Client();
            var result = await redis.AddAsync(lockName, $"LampDeviceSync", time, When.NotExists);
            return result;
        }

        private async Task GetTheLockFalse()
        {
            var redis = XhRedisHelper.UseS02Client();
            var redisS02 = XhRedisHelper.UseS02();
            var serviceName = await redis.GetAsync<string>(lockName);
            var ttl = await redisS02.KeyTimeToLiveAsync(lockName);
            if (ttl.HasValue)
            {
                if (ttl.Value.Seconds / 3600 >= 1)
                {
                    Log.Warning($"已有服务正在执行，服务名：{serviceName},剩余时长未{ttl / 3600}小时");
                }
                if (ttl.Value.Seconds / 3600 < 1)
                {
                    Log.Warning($"已有服务正在执行，服务名：{serviceName},剩余时长未{ttl}秒");
                }
            }
            else
            {
                Log.Warning($"不存在服务正在执行，服务名：{serviceName},剩余时长未{ttl}秒");
            }
        }

        public override async Task StopAsync(CancellationToken cancellationToken)
        {
            var redis =XhRedisHelper.UseS02Client();
            var redisS02 = XhRedisHelper.UseS02();
            var isDeleted = await redis.RemoveAsync(lockName);
            if (isDeleted)
            {
                Log.Information($"已有停止正在执行的定时任务，服务名：LampDeviceSync");
            }
            else
            {
                var serviceName = await redis.GetAsync<string>(lockName);
                var ttl = await redisS02.KeyTimeToLiveAsync(lockName);
                Log.Error($"已有停止正在执行的定时任务，服务名：LampDeviceSync,缓存键删除失败，剩余时长为:{ttl}秒");
            }

            _timer.Stop();
            await base.StopAsync(cancellationToken);
        }

        public void Dispose()
        {
            _timer.Dispose();
        }
}