﻿using AutoMapper;
using EasyCaching.Core;
using H.BASE;
using H.BASE.SqlSugarInfra.Uow;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using XH.H82.API.Extensions;
using XH.H82.Base.Helper.InitDataOption.NewToolBox.Provider;
using XH.H82.IServices;
using XH.H82.IServices._demo;
using XH.H82.IServices.DeviceDataRefresh;
using XH.H82.IServices.EquipmentClassNew;
using XH.H82.IServices.IoTDevice;
using XH.H82.IServices.TemplateDesign;
using XH.H82.Models;
using XH.H82.Models.BusinessModuleClient.IoTDevices;
using XH.H82.Models.Entities;
using XH.H82.Models.Entities.THS;
using XH.H82.Models.Flow;
using XH.H82.Models.SugarDbContext;
using XH.H82.Services;
using XH.LAB.UTILS.Interface;

namespace XH.H82.API.Controllers
{
#if !DEBUG
    [NonController]
#endif
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize]
    public class TestController : ControllerBase
    {
        private readonly IMapper _mapper;
        private readonly IAuthorityService2 _authorityService;
        private IBaseDataServices _baseDataServices;
        private ILearningRecordService _learningRecordService;
        
        private readonly IH115OnlyOfficeService _h115OnlyOfficeService;
        private readonly IDeviceRefreshService _deviceRefreshService;
        private readonly ISqlSugarUow<SugarDbContext_Master> _dbContext;
        private readonly IConfiguration _configuration;
        private readonly IHttpContextAccessor _httpContext;
        private readonly IUploadFileService _uploadFileService;
        private readonly IMessageService _messageService;
        
        private readonly ITemplateDesignService _templateDesignService;

        private readonly IIoTDeviceService _iotDeviceService;
        
        private readonly IEquipmentClassService _equipmentClassService;

        public TestController(IMapper mapper,
             IH115OnlyOfficeService h115OnlyOfficeService,
            ILearningRecordService learningRecordService, 
             IDeviceRefreshService deviceRefreshService,
            ISqlSugarUow<SugarDbContext_Master> dbContext, 
             IConfiguration configuration,
            IHttpContextAccessor httpContext,
            IAuthorityService2 authorityService,
             IBaseDataServices baseDataServices, 
             IIoTDeviceService iotDeviceService, 
             IUploadFileService uploadFileService,
             ITemplateDesignService templateDesignService, IMessageService messageService, IEquipmentClassService equipmentClassService)
        {
            _mapper = mapper;
            
            _h115OnlyOfficeService = h115OnlyOfficeService;
            _learningRecordService = learningRecordService;
            _deviceRefreshService = deviceRefreshService;
            _dbContext = dbContext;
            _configuration = configuration;
            _httpContext = httpContext;
            _authorityService = authorityService;
            _baseDataServices = baseDataServices;
            _iotDeviceService = iotDeviceService;
            _uploadFileService = uploadFileService;
            _templateDesignService = templateDesignService;
            _messageService = messageService;
            _equipmentClassService = equipmentClassService;
        }
        
        [HttpGet]
        public async Task<IActionResult> GetTestNewBoxUpload()
        {
            var newToolBoxProvider = new NewToolBoxProvider(_configuration);
            newToolBoxProvider.SetModuleAndHospitalContext(AppSettingsProvider.CurrModuleId, "33A001");
            var result = await newToolBoxProvider.InitMeanConfigByExcelFileAsync(null, _httpContext.HttpContext.Request.Headers.Authorization.ToString());
            return Ok(result);
        }
        [HttpGet]
        public IActionResult GetTest10009(string id)
        {
            _dbContext.Db.Ado.SqlQuery<object,object>("");
            var result = _deviceRefreshService.GetDisplayInfoTest(id);
            return new FileContentResult(result, "image/jpeg");
        }

        [HttpGet]
        [AllowAnonymous]
        public IActionResult AutoTest()
        {
            _deviceRefreshService.AutoRefreshDisplayInfo();
            return Ok();

        }

 
        /// <summary>
        /// 定时任务紫外灯测试
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IActionResult Equipment_ziwaideng(string sn)
        {
            var client = new IoTDevicesClient(_configuration["IoTHttp"], _httpContext);
            var result =  client.GetGetUltravioletLampDeviceInfo(sn,"");
            return Ok(result);
        }
        
        
        /// <summary>
        /// 定时任务洗眼器测试
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IActionResult Equipment_xiyanqi(string sn)
        {
            var client = new IoTDevicesClient(_configuration["IoTHttp"], _httpContext);
            var result =  client.GetGetWaterPressureDeviceInfo(sn,"");
            return Ok(result);
        }
        
        /// <summary>
        /// 只智能开关测试
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IActionResult Equipment_zinengkaiguan(string model)
        {
            var client = new IoTDevicesClient(_configuration["IoTHttp"], _httpContext);
            var result =  client.GetIoTDeviceInfo("",model);
            return Ok(result);
        }
        
        /// <summary>
        /// 水压传感器开关测试
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IActionResult Equipment_shuiya(string sn)
        {
            var client = new IoTDevicesClient(_configuration["IoTHttp"], _httpContext);
            var result =  client.GetGetWaterPressureDeviceInfo(sn,"");
            return Ok(result);
        }
        
        
        /// <summary>
        /// 环境一体机测试
        /// </summary>
        /// <param name="sn"></param>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IActionResult GetEnvironmentDevicesDto(string sn)
        {
            var client = new IoTDevicesClient(_configuration["IoTHttp"], _httpContext);
            var result =  client.GetEnvironmentDevicesInfo(sn,"");
            return Ok(result);
        }
  
        /// <summary>
        /// 水压监测记录测试
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IActionResult EquipmentWaterInit(string id)
        {
            var equipment = _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>()
                .First(x => x.EQUIPMENT_ID == id);
            var result =  _iotDeviceService.GetWaterPressureDtoDeviceData(equipment);
            if (result is not  null)
            {
                _iotDeviceService.AddWaterObtainMonitoring(equipment,result);
            }
            return Ok(result); 
        }

        /// <summary>
        /// 紫外灯监测记录测试
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IActionResult EquipmentlabmInit(string id)
        {
            var equipment = _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>()
                .First(x => x.EQUIPMENT_ID == id);
            var result =  _iotDeviceService.GetUltravioletLampDeviceData(equipment);
            if (result is not  null)
            {
                _iotDeviceService.AddLampObtainMonitoring(equipment,result);
            }
            //var list = _iotDeviceService.GetObtainMonitoringData(id,DateTime.Now);
            return Ok(result); 
        }
                
                
        /// <summary>
        /// 环境一体机监测记录测试
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IActionResult EquipmentEvnInit(string id)
        {
            var equipment = _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>().First(x => x.EQUIPMENT_ID == id);
            var result =  _iotDeviceService.GetEnvironmentDeviceData(equipment);
            if (result.Any())
            {
                _iotDeviceService.AddEvnObtainMonitoring(equipment,result);
            }
            return Ok(result); 
        }
                
        /// <summary>
        /// 智能插座监测记录测试
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IActionResult EquipmentInit(string id)
        {
            var equipment =  _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>().First(x => x.EQUIPMENT_ID == id);
            var result =  _iotDeviceService.GetEquipmentIoTData(equipment);
            if (result is not  null)
            {
                _iotDeviceService.AddObtainMonitoringData(equipment,result);
            }
            return Ok(result); 
        }

        [HttpGet]
        [AllowAnonymous]
        public IActionResult InitEquipmentMoitorHoursData(string id  , DateTime date)
        {
            var equipment =  _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>().First(x => x.EQUIPMENT_ID == id);
            
            var thsEquipment = _dbContext.Db.Queryable<THS_EQUIPMENT_INFO>().First(x => x.EQUIPMENT_DID == equipment.EQUIPMENT_ID);
            
            var  records = _dbContext.Db.Queryable<THS_MONITOR_LIST>()
                .Includes(x=>x.ThsMonitorResults)
                .Where(x=>x.EQUIPMENT_ID == thsEquipment.EQUIPMENT_ID)
                .Where(x=>x.MONITOR_TIME.Value.Date == date.Date)
                .OrderByDescending(x=>x.MONITOR_TIME)
                .ToList();

            foreach (var record in records)
            {
                 _iotDeviceService.ConutEquipmentMonitoringItems(equipment,record);
            }
            return Ok();
        }
        
        /// <summary>
        /// 提供小时监测表测试数据
        /// </summary>
        /// <param name="id"></param>
        /// <param name="date"></param>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IActionResult GetEquipmentMoitorHoursData(string id  , DateTime date)
        {
            var equipment =  _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>().First(x => x.EQUIPMENT_ID == id);
            
            var thsEquipment = _dbContext.Db.Queryable<THS_EQUIPMENT_INFO>().First(x => x.EQUIPMENT_DID == equipment.EQUIPMENT_ID);
            var project = _iotDeviceService.GetEquipmentIndicatorBySmblClass(equipment.SMBL_CLASS);
            var hoursRecords = _dbContext.Db.Queryable<THS_MONITOR_HOUR>()
                .Where(x => x.EQUIPMENT_ID == thsEquipment.EQUIPMENT_ID)
                .Where(x => x.MONITOR_DATE.Value.Date == date.Date)
                .Where(x=>x.ITEM_ID.Contains("CUM") || x.ITEM_ID.Contains(project.Project))
                .ToList();
            return Ok(hoursRecords);
        }
        /// <summary>
        /// 提天统计
        /// </summary>
        /// <param name="id"></param>
        /// <param name="date"></param>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IActionResult ConutEquipmentMonitoringItemsDay(DateTime date)
        {
            _iotDeviceService.ConutEquipmentMonitoringItemsDay(date);
            return Ok();
        }
        
        /// <summary>
        /// 按天查询
        /// </summary>
        /// <param name="id"></param>
        /// <param name="date"></param>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IActionResult GetEquipmentMoitorHoursDataByDay(string id,DateTime date)
        {
            var equipment =  _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>().First(x => x.EQUIPMENT_ID == id);
            var thsEquipment = _dbContext.Db.Queryable<THS_EQUIPMENT_INFO>().First(x => x.EQUIPMENT_DID == equipment.EQUIPMENT_ID);
            var hoursRecords = _dbContext.Db.Queryable<THS_MONITOR_DAY>()
                .Where(x => x.EQUIPMENT_ID == thsEquipment.EQUIPMENT_ID)
                .Where(x => x.MONITOR_DATE.Value.Date == date.Date)
                .Where(x=>x.REMARK == "生安眼监测")
                .ToList();
            return Ok(hoursRecords);
        }

        [HttpGet]
        public IActionResult testSopFiles()
        {
          var resutrl =  _uploadFileService.AddEquipmentSopFiles(_dbContext, "1721768702287941632", new List<string> {"3751"});
          var result1 = _uploadFileService.GetEquipmentSopFiles(_dbContext, "1721768702287941632");
          var result2 = _uploadFileService.CancelEquipmentSopFiles(_dbContext, "1721768702287941632", new List<string> {"3751"});
          var result3 = _uploadFileService.GetEquipmentSopFiles(_dbContext, "1721768702287941632");
          return Ok(resutrl);
        }

        [HttpGet]
        public IActionResult TemplateDesign()
        {
            var result = _templateDesignService.InsertPersonInfoSetting();
            return Ok(result);
        }
        [HttpGet]
        public IActionResult GetFlowInstanceDetail(string code ,  string flowInstanceId)
        {
            var url = _configuration["H120"];
            var client = new H120Client(_httpContext,_configuration);
             var instances = client.GetFlowInstanceDetail(flowInstanceId);
             var node = instances.FlowInstanceNodes.First(x => x.InstanceState == "1");
            // var result = client.GetFlowDetailByCode(code);
            // var result = client.StartFlowInstanceByCode(code, new List<string>(){"7"});
            var result = client.NextFlow(flowInstanceId, node.InstanceNodeId, new ApprovalInput()
            {
                flowOperateType = "2",
                assignPerson = new List<string>(){"7"},
                targetNodeId = null,
                nodeRemark = "测试",
                rejectInfo = null
            });
            return Ok(result);
        }
        [HttpGet]
        [AllowAnonymous]
        public IActionResult MessageContext(string flowInstanceId)
        {
            var messageContext = new MessageContext(_messageService, _dbContext, _baseDataServices);
            messageContext.SendInkScreenMsg("D3:C3:F3:DD:12:2B");
            return Ok();
        }
        
        
        [HttpGet]
        [AllowAnonymous]
        public IActionResult ClassDictTest()
        {
            //var  resulte =  _equipmentClassService.GetEquipmentClassDictTree();
            
            var resulte = _equipmentClassService.GetEquipmentClassDict();

            //var resulte = _equipmentClassService.GetEquipmentArchivesByCondition(x => true);
            return Ok(resulte.Success());
        }


 
    }
}
