﻿#nullable enable
using XH.H82.Models;
using XH.LAB.UTILS.Models.Entites;

namespace XH.H82.IServices.oaBaseData;

public interface IOaBasedataService
{
    /// <summary>
    /// 自主添加OaBasedata
    /// </summary>
    /// <param name="fatherId">一级分类</param>
    /// <param name="classId">数据分类</param>
    /// <param name="dataId">数据id</param>
    /// <param name="content">数据内容</param>
    /// <returns></returns>
    OA_BASE_DATA AddOaBaseData(string? fatherId, string classId , OaBaseDataContent content);

    /// <summary>
    /// 更新oabasedata数据
    /// </summary>
    /// <param name="fatherId"></param>
    /// <param name="classId"></param>
    /// <param name="content"></param>
    /// <returns></returns>
    OA_BASE_DATA UpdataOaBaseData(string? fatherId, string classId,  OaBaseDataContent content);


    /// <summary>
    /// 删除oabasedata数据
    /// </summary>
    /// <param name="fatherId"></param>
    /// <param name="classId"></param>
    /// <param name="dataId"></param>
    /// <param name="isDeleted">是否真实删除</param>
    void DeleteOaBaseData(string? fatherId, string classId, string dataId ,  bool isDeleted = true);

    /// <summary>
    /// 获取oabasedata数据
    /// </summary>
    /// <param name="fatherId"></param>
    /// <param name="classId"></param>
    /// <param name="dataId"></param>
    /// <returns></returns>
    OA_BASE_DATA? GetOaBaseData(string? fatherId, string classId ,string dataId);
    
    
    /// <summary>
    /// 获取oabasedata数据
    /// </summary>
    /// <param name="fatherId"></param>
    /// <param name="classId"></param>
    /// <returns></returns>
    List<OA_BASE_DATA> GetOaBaseDatas(string? fatherId, string classId);


    /// <summary>
    /// 禁用停用oabasedata
    /// </summary>
    /// <param name="fatherId"></param>
    /// <param name="classId"></param>
    /// <param name="dataId"></param>
    void DisableOrEnableOaBaseData(string? fatherId, string classId, string dataId);
}
