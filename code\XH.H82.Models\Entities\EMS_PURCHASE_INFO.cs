﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using H.Utility.SqlSugarInfra;
using SqlSugar;

namespace XH.H82.Models.Entities
{
    [DBOwner("XH_OA")]
    public class EMS_PURCHASE_INFO
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string PURCHASE_ID { get; set; }
        public string HOSPITAL_ID { get; set; }
        public string LAB_ID { get; set; }
        public string MGROUP_ID { get; set; }
        public string EQUIPMENT_ID { get; set; }
        public string CALL_BIDS_NAME { get; set; }
        public string CALL_BIDS_NO { get; set; }
        public DateTime? CALL_BIDS_DATE { get; set; }
        public string CONTRACT_NAME { get; set; }
        public string CONTRACT_NO { get; set; }
        public DateTime? CONTRACT_DATE { get; set; }
        public string PURCHASE_STATE { get; set; }
        public string FIRST_RPERSON { get; set; }
        public DateTime? FIRST_RTIME { get; set; }
        public string LAST_MPERSON { get; set; }
        public DateTime? LAST_MTIME { get; set; }
        public string REMARK { get; set; }


    }
}
