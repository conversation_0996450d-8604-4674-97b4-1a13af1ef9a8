﻿using H.Utility;

namespace XH.H82.API.Extensions;

public static class ResultDtoExt
{
    /// <summary>
    /// 成功 200 返回数据
    /// </summary>
    /// <param name="data"></param>
    /// <param name="msg"></param>
    /// <param name="data1"></param>
    /// <param name="data2"></param>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    public static ResultDto<T> Success<T>(this T data , string msg  = "成功" , object? data1 = null , object? data2 = null)
    {
        return new ResultDto<T>()
        {
            data = data,
            success = true,
            errCode = null,
            data1 = data1,
            data2 = data2,
            msg = msg
        };
    }
    
    /// <summary>
    /// 失败 500 返回错误
    /// </summary>
    /// <param name="data"></param>
    /// <param name="msg"></param>
    /// <param name="data1"></param>
    /// <param name="data2"></param>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    public static ResultDto<T> Failure<T>(this T data , string msg = "失败" ,object? data1 = null , object? data2 = null)
    {
        return new ResultDto<T>()
        {
            data = data,
            success = false,
            errCode = null,
            data1 = data1,
            data2 = data2,
            msg = msg
        };
    }
}