﻿using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using NPOI.Util;
using SqlSugar;
using XH.H82.IServices;
using XH.H82.Models;
using XH.H82.Models.DeviceRelevantInformation;
using XH.H82.Models.Dtos;
using XH.H82.Models.Entities;
using XH.H82.Models.Entities.Common;
using XH.H82.Models.Entities.OperationLog;
using XH.H82.Models.Entities.THS;
using XH.H82.Models.SugarDbContext;
using XH.H82.Services.DeviceDataRefresh;
using XH.LAB.UTILS.Helpers;
using XH.LAB.UTILS.Interface;
using XH.LAB.UTILS.Models;
using ClaimsDto = H.Utility.ClaimsDto;

namespace XH.H82.Services
{
    public class WorkPlanService : IWorkPlanService
    {
        private readonly IXhWorkPlanService _xhWorkPlanService;
        private readonly ISqlSugarUow<SugarDbContext_Master> _dbContext;
        private readonly ILogger<OperationRecordService> _logger;
        private ClaimsDto _user;
        private readonly ISetUpService _setUpService;
        private readonly IAuthorityService _authorityService;

        public WorkPlanService(
            ISqlSugarUow<SugarDbContext_Master> dbContext,
            IHttpContextAccessor httpContext,
            ISetUpService setUpService,
            IXhWorkPlanService xhWorkPlanService,
            ILogger<OperationRecordService> logger, IAuthorityService authorityService)
        {
            _user = httpContext.HttpContext.User.ToClaimsDto();
            _user.USER_NAME = _user.HIS_NAME;
            _dbContext = dbContext;
            _setUpService = setUpService;
            _xhWorkPlanService = xhWorkPlanService;
            _logger = logger;
            _authorityService = authorityService;
            dbContext.SetCreateTimeAndCreatePersonData(_user);
            dbContext.IsDeleted<EMS_WORK_PLAN>(x => x.WORK_PLAN_STATE != "2");
        }

        public ResultDto SaveWorkPlan(List<WorkPlanDto> record, string userName)
        {
            ResultDto result = new ResultDto();

            var isNeedReviewProcess = _setUpService.NeedReviewProcess(_user.HOSPITAL_ID);
            try
            {
                if (isNeedReviewProcess)
                {
                    record.ForEach(item =>
                    {
                        _dbContext
                            .Db.Updateable<EMS_WORK_PLAN>()
                            .SetColumns(p => new EMS_WORK_PLAN
                            {
                                MAINTAIN_INTERVALS = item.MAINTAIN_INTERVALS,
                                MAINTAIN_WARN_INTERVALS = item.MAINTAIN_WARN_INTERVALS,
                                YEARLY_MAINTAIN = item.YEARLY_MAINTAIN,
                                YEARLY_MAINTAIN_WARN = item.YEARLY_MAINTAIN_WARN,
                                MONTHLY_MAINTAIN = item.MONTHLY_MAINTAIN,
                                MONTHLY_MAINTAIN_WARN = item.MONTHLY_MAINTAIN_WARN,
                                QUARTERLY_MAINTAIN = item.QUARTERLY_MAINTAIN,
                                QUARTERLY_MAINTAIN_WARN = item.QUARTERLY_MAINTAIN_WARN,
                                COMPARISON_INTERVALS = item.COMPARISON_INTERVALS,
                                COMPARISON_WARN_INTERVALS = item.COMPARISON_WARN_INTERVALS,
                                CORRECT_INTERVALS = item.CORRECT_INTERVALS,
                                CORRECT_WARN_INTERVALS = item.CORRECT_WARN_INTERVALS,
                                VERIFICATION_INTERVALS = item.VERIFICATION_INTERVALS,
                                VERIFICATION_WARN_INTERVALS = item.VERIFICATION_WARN_INTERVALS,
                                OPER_PERSON = userName,
                                OPER_TIME = DateTime.Now,
                                LAST_MPERSON = userName,
                                LAST_MTIME = DateTime.Now,
                                //CURRENT_STATE =  OperationStateEnum.NotSubmitted.ToID(),
                            })
                            .Where(p => p.WORK_PLAN_ID == item.WORK_PLAN_ID)
                            .Where(p =>
                                p.CURRENT_STATE == OperationStateEnum.Overruled.ToID()
                                || p.CURRENT_STATE == OperationStateEnum.NotSubmitted.ToID()
                            )
                            .ExecuteCommand();
                    });
                }
                else
                {
                    record.ForEach(item =>
                    {
                        _dbContext
                            .Db.Updateable<EMS_WORK_PLAN>()
                            .SetColumns(p => new EMS_WORK_PLAN
                            {
                                MAINTAIN_INTERVALS = item.MAINTAIN_INTERVALS,
                                MAINTAIN_WARN_INTERVALS = item.MAINTAIN_WARN_INTERVALS,
                                YEARLY_MAINTAIN = item.YEARLY_MAINTAIN,
                                YEARLY_MAINTAIN_WARN = item.YEARLY_MAINTAIN_WARN,
                                MONTHLY_MAINTAIN = item.MONTHLY_MAINTAIN,
                                MONTHLY_MAINTAIN_WARN = item.MONTHLY_MAINTAIN_WARN,
                                QUARTERLY_MAINTAIN = item.QUARTERLY_MAINTAIN,
                                QUARTERLY_MAINTAIN_WARN = item.QUARTERLY_MAINTAIN_WARN,
                                COMPARISON_INTERVALS = item.COMPARISON_INTERVALS,
                                COMPARISON_WARN_INTERVALS = item.COMPARISON_WARN_INTERVALS,
                                CORRECT_INTERVALS = item.CORRECT_INTERVALS,
                                CORRECT_WARN_INTERVALS = item.CORRECT_WARN_INTERVALS,
                                VERIFICATION_INTERVALS = item.VERIFICATION_INTERVALS,
                                VERIFICATION_WARN_INTERVALS = item.VERIFICATION_WARN_INTERVALS,
                                OPER_PERSON = userName,
                                OPER_TIME = DateTime.Now,
                                LAST_MPERSON = userName,
                                LAST_MTIME = DateTime.Now,
                                CURRENT_STATE = OperationStateEnum.Audited.ToID(),
                            })
                            .Where(p => p.WORK_PLAN_ID == item.WORK_PLAN_ID)
                            .ExecuteCommand();
                    });
                }
            }
            catch (Exception ex)
            {
                result.success = false;
                result.msg = "保存失败";
                _logger.LogError("保存失败:\n" + ex.Message);
            }
            return result;
        }

        public ClassTreeDto GetClassList(string hospitalId, string userNo, string labId)
        {
            //专业组列表
            var groupList = _authorityService.GetUserPermissionPgroup(_dbContext.Db, userNo, "H82", hospitalId, labId);
            // var groupList = _dbContext
            //     .Db.Queryable<SYS6_USER_POST>()
            //     .LeftJoin<SYS6_POST>((userPost, post) => userPost.POST_ID == post.POST_ID)
            //     .LeftJoin<SYS6_INSPECTION_PGROUP>(
            //         (userPost, post, pgroup) => post.PGROUP_ID == pgroup.PGROUP_ID
            //     )
            //     .Where(
            //         (userPost, post, pgroup) =>
            //             userPost.USER_NO == userNo
            //             && post.POST_STATE == "1"
            //             && pgroup.PGROUP_STATE == "1"
            //             && pgroup.LAB_ID == labId
            //     )
            //     .Select((userPost, post, pgroup) => pgroup)
            //     .Distinct()
            //     .ToList();
            List<string> arrPgroupList = new List<string>();
            groupList.ForEach(p =>
            {
                if (p.MGROUP_ID.IsNotNullOrEmpty())
                {
                    arrPgroupList.Add(p.MGROUP_ID);
                }
            });
            arrPgroupList = _dbContext
                .Db.Queryable<SYS6_INSPECTION_MGROUP>()
                .Where(p => arrPgroupList.Contains(p.MGROUP_ID) && p.MGROUP_STATE == "1")
                .Select(i => i.MGROUP_ID)
                .ToList();
            //设备类型树结构
            var classListDto = new ClassTreeDto();
            //设备类型列表
            var classList = _dbContext
                .Db.Queryable<SYS6_BASE_DATA>()
                .Where(p => p.CLASS_ID == "设备分类" && p.DATA_STATE == "1")
                .ToList();
            //二级节点
            var secondNode = new List<SECOND_CLASS_NODE>();
            //关联设备信息表
            var equipmentList = _dbContext
                .Db.Queryable<SYS6_BASE_DATA>()
                .LeftJoin<EMS_EQUIPMENT_INFO>((a, b) => a.DATA_ID == b.EQUIPMENT_CLASS)
                .Where(
                    (a, b) =>
                        a.CLASS_ID == "设备分类"
                        && a.DATA_STATE == "1"
                        && arrPgroupList.Contains(b.UNIT_ID)
                )
                .Select((a, b) => new { b.EQUIPMENT_CLASS })
                .ToList();
            //设备报废停用列表
            var scrapList = _dbContext
                .Db.Queryable<EMS_SCRAP_INFO>()
                .LeftJoin<EMS_EQUIPMENT_INFO>((a, b) => a.EQUIPMENT_ID == b.EQUIPMENT_ID)
                .Where((a, b) => a.SCRAP_STATE == "1" && arrPgroupList.Contains(b.UNIT_ID))
                .Select(
                    (a, b) =>
                        new
                        {
                            a.APPLY_STATE,
                            a.OPER_PERSON_ID,
                            a.EXAMINE_PERSON_ID,
                            a.APPROVE_PERSON_ID,
                            b.EQUIPMENT_CLASS,
                        }
                )
                .ToList();
            //报废停用dto
            var q = new List<ScrapStopListDto>();
            scrapList.ForEach(item =>
            {
                var dealPersonId = "";
                if (item.APPLY_STATE == "0" || item.APPLY_STATE == "4" || item.APPLY_STATE == "5")
                {
                    dealPersonId = item.OPER_PERSON_ID;
                }
                if (item.APPLY_STATE == "1")
                {
                    dealPersonId = item.EXAMINE_PERSON_ID;
                }
                if (item.APPLY_STATE == "2")
                {
                    dealPersonId = item.APPROVE_PERSON_ID;
                }
                q.Add(
                    new ScrapStopListDto
                    {
                        DEAL_PERSON_ID = dealPersonId,
                        EQUIPMENT_CLASS = classList
                            .Where(p => p.DATA_ID == item.EQUIPMENT_CLASS)
                            .FirstOrDefault()
                            ?.DATA_CNAME,
                    }
                );
            });
            q.ToList()
                .ForEach(item =>
                {
                    if (item.DEAL_PERSON_ID == userNo)
                    {
                        classListDto.SELF_PEND += 1;
                    }
                });
            classList.ForEach(item =>
            {
                secondNode.Add(
                    new SECOND_CLASS_NODE()
                    {
                        EQUIPMENT_CLASS = item.DATA_CNAME,
                        EQUIPMENT_CLASS_ID = item.DATA_ID,
                        PLAN_AMOUNT = equipmentList
                            .Where(p => p.EQUIPMENT_CLASS == item.DATA_ID)
                            .Count(),
                        SCRAP_AMOUNT = scrapList
                            .Where(p => p.EQUIPMENT_CLASS == item.DATA_ID)
                            .Count(),
                    }
                );
                classListDto.TOTAL_PlAN_AMOUNT += equipmentList
                    .Where(p => p.EQUIPMENT_CLASS == item.DATA_ID)
                    .Count();
            });
            classListDto.TOTAL_SCRAP_AMOUNT = scrapList.Count();
            classListDto.SECOND_CLASS_NODE = secondNode;
            return classListDto;
        }

        public List<CountWorkPlanDto> GetCountWorkPlan()
        {
            var result = new List<CountWorkPlanDto>();
            var equipmentContext = new EquipmentContext(_dbContext);
            //工作计划
            var workPlans = _dbContext.Db.Queryable<EMS_WORK_PLAN>()
                .Where(x => x.CURRENT_STATE == "3")
                .Where(x => !SqlFunc.IsNullOrEmpty(x.CORRECT_INTERVALS))
                .ToDictionary<string>(x=>x.EQUIPMENT_ID,x=>x.CORRECT_INTERVALS);
            var ids = workPlans.Select(x => x.Key).ToArray();
            //校准记录
            var corrents = _dbContext.Db.Queryable<EMS_CORRECT_INFO>()
                .Where(x => ids.Contains(x.EQUIPMENT_ID))
                .Where(x => x.CORRECT_STATE == "1")
                .Where(x => x.STATE == "已执行")
                .Where(x => x.CORRECT_DATE.HasValue)
                .OrderByDescending(x=>x.CORRECT_DATE)
                .ToList();
            var equipmentsIds = corrents.Select(x => x.EQUIPMENT_ID).Distinct().ToArray();
            var equipments = _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>()
                .Where(x => equipmentsIds.Contains(x.EQUIPMENT_ID))
                .Where(x=>x.EQUIPMENT_STATE == "1")
                .ToList();

            var no = 0;
            foreach (var equipment in equipments)
            {
                var cycle = workPlans[equipment.EQUIPMENT_ID];
                if (cycle is null)
                {
                    continue;
                }

                var correct = corrents.FirstOrDefault(x => x.EQUIPMENT_ID == equipment.EQUIPMENT_ID);
                if (correct is null)
                {
                    continue;
                }
                var item = new CountWorkPlanDto();
                item.No = ++no;
                item.Cycle = double.Parse(cycle);
                item.SbmlLabName = equipmentContext.ExchangeSmblLabName(equipment.SMBL_LAB_ID);
                item.SmblClassName = equipmentContext.ExchangeEquipmentSmblClass(equipment.SMBL_CLASS);
                item.EquipmentName = equipment.EQUIPMENT_NAME;
                item.LastInspectionTime = correct.CORRECT_DATE;
                item.NextInspectionTime = correct.CORRECT_DATE.Value.AddDays(double.Parse(cycle));
                result.Add(item);
            }
            return result;
        }

        public List<EquipmentYearCheckCountDto> GetEquipmentYearCheckCount(string? hospitalId, string? labId)
        {
            
            var  equipmentContext = new EquipmentContext(_dbContext);
            
            // 查询年检计划表
            var emsWorkPlanList = _dbContext.Db.Queryable<EMS_WORK_PLAN>()
                .Where(wp => wp.CURRENT_STATE == "3" && wp.CORRECT_INTERVALS != null)
                .ToList();

            var correctIntervalsMap = emsWorkPlanList
                .Where(wp => decimal.TryParse(wp.CORRECT_INTERVALS, out _))
                .ToDictionary(wp => wp.EQUIPMENT_ID, wp => wp.CORRECT_INTERVALS);

            var keys = correctIntervalsMap.Keys.ToArray();

            var equipmentYearCheckDtoList = _dbContext.Db.Queryable<EMS_CORRECT_INFO>()
                .LeftJoin<EMS_EQUIPMENT_INFO>((ci, ei) => ci.EQUIPMENT_ID == ei.EQUIPMENT_ID)
                .InnerJoin<THS_EQUIPMENT_INFO>((ci, ei, thi) => ei.EQUIPMENT_ID == thi.EQUIPMENT_DID && thi.UNIT_ID == ei.SMBL_LAB_ID)
                .Where((ci, ei, thi) => ci.CORRECT_DATE.HasValue
                                        && keys.Contains(ci.EQUIPMENT_ID)
                                        && ci.STATE == "已执行")
                .WhereIF(hospitalId.IsNotNullOrEmpty(), (ci, ei, thi) => ei.UNIT_ID == hospitalId)
                .WhereIF(labId.IsNotNullOrEmpty(), (ci, ei, thi) => ei.LAB_ID == labId)
                .Select((ci, ei, thi) => new EquipmentYearCheckDto
                {
                    Id = ci.EQUIPMENT_ID,
                    Name = ei.EQUIPMENT_NAME,
                    SmblLabName = ei.SMBL_LAB_ID,
                    SmblClass = ei.SMBL_CLASS,
                    LastYearCheckTime = ci.CORRECT_DATE.Value
                })
                .ToList();
                
                foreach (var equipmentYearCheckDto in equipmentYearCheckDtoList)
                {
                    equipmentYearCheckDto.SmblLabName = equipmentContext.ExchangeSmblLabName(equipmentYearCheckDto.SmblLabName);
                    equipmentYearCheckDto.SmblClass = equipmentContext.ExchangeEquipmentSmblClass(equipmentYearCheckDto.SmblClass);
                }
                
                var now = DateTime.Now;
                int currentYearPlanCount = 0;
                int nextYearPlanCount = 0;
                // 今年
                var currentYear = new DateTime(now.Year, 1, 1);
                // 明年
                var nextYear = currentYear.AddYears(1);
                // 后年
                var nextNextYear = currentYear.AddYears(2);

                foreach (var equipmentYearCheckDto in equipmentYearCheckDtoList)
                {
                    var checkTime = equipmentYearCheckDto.LastYearCheckTime;
                    // 检测周期天数
                    if (decimal.TryParse(correctIntervalsMap[equipmentYearCheckDto.Id], out var interval))
                    {
                        // 循环增加当前时间
                        do
                        {
                            checkTime = checkTime.AddDays((double)interval);
                            if (checkTime >= currentYear && checkTime < nextYear)
                            {
                                // 算今年
                                currentYearPlanCount++;
                            }
                            if (checkTime >= nextYear && checkTime < nextNextYear)
                            {
                                // 算明年
                                nextYearPlanCount++;
                            }
                        } while (checkTime < nextNextYear);
                    }
                }

                return new List<EquipmentYearCheckCountDto>
                {   
                    new EquipmentYearCheckCountDto { Year = nextYear.Year, Count = nextYearPlanCount },
                    new EquipmentYearCheckCountDto { Year = currentYear.Year, Count = currentYearPlanCount },
                };
        }
        
        public List<ConutEquipmentYearChectByMonth> GetEquipmentYearCheck(int year ,string? hospitalId, string? labId)
        {

            var equipmentContext = new EquipmentContext(_dbContext);
        // 查询年检计划表
        var emsWorkPlanList = _dbContext.Db.Queryable<EMS_WORK_PLAN>()
            .Where(wp => wp.CURRENT_STATE == "3" && wp.CORRECT_INTERVALS != null)
            .ToList();

        var correctIntervalsMap = emsWorkPlanList
            .Where(wp => decimal.TryParse(wp.CORRECT_INTERVALS, out _))
            .ToDictionary(wp => wp.EQUIPMENT_ID, wp => wp.CORRECT_INTERVALS);
        var keys = correctIntervalsMap.Keys.ToArray();
        
        // 查询所有记录表信息
        var equipmentYearCheckDtoList = _dbContext.Db.Queryable<EMS_CORRECT_INFO>()
            .LeftJoin<EMS_EQUIPMENT_INFO>((ci, ei) => ci.EQUIPMENT_ID == ei.EQUIPMENT_ID)
            .InnerJoin<THS_EQUIPMENT_INFO>((ci, ei, thi) => ei.EQUIPMENT_ID == thi.EQUIPMENT_DID && thi.UNIT_ID == ei.SMBL_LAB_ID)
            .Where((ci, ei, thi) => ci.CORRECT_DATE.HasValue
                && keys.Contains(ci.EQUIPMENT_ID)
                && ci.STATE == "已执行")
            .WhereIF(hospitalId.IsNotNullOrEmpty(), (ci, ei, thi) => ei.HOSPITAL_ID == hospitalId)
            .WhereIF(labId.IsNotNullOrEmpty(), (ci, ei, thi) => ei.LAB_ID == labId)
            .Select((ci, ei, thi) => new EquipmentYearCheckDto
            {
                Id = ci.EQUIPMENT_ID,
                Name = ei.EQUIPMENT_NAME,
                SmblLabName = ei.SMBL_LAB_ID,
                SmblClass = ei.SMBL_CLASS,
                LastYearCheckTime = ci.CORRECT_DATE.Value
            })
            .ToList();
        foreach (var equipmentYearCheckDto in equipmentYearCheckDtoList)
        {
            equipmentYearCheckDto.SmblLabName = equipmentContext.ExchangeSmblLabName(equipmentYearCheckDto.SmblLabName);
            equipmentYearCheckDto.SmblClass = equipmentContext.ExchangeEquipmentSmblClass(equipmentYearCheckDto.SmblClass);
        }
        
        var equipmentYearCheckDtos = new List<EquipmentYearCheckDto>();
        // 循环推算出下一次的年检时间
        // 算出今年的最后时间
        var endOfYear = new DateTime(year, 12, 31, 23, 59, 59);

        foreach (var equipmentYearCheckDto in equipmentYearCheckDtoList)
        {
            var checkTime = equipmentYearCheckDto.LastYearCheckTime;
            // 检测周期天数
            if (decimal.TryParse(correctIntervalsMap[equipmentYearCheckDto.Id], out var interval))
            {
                // 循环增加当前时间
                do
                {
                    var item = equipmentYearCheckDto.Copy();
                    checkTime = checkTime.AddDays((double)interval);
                    item.NextYearCheckTime = checkTime;
                    equipmentYearCheckDtos.Add(item);
                } while (checkTime <= endOfYear);
            }
        }

        var resutl = new List<ConutEquipmentYearChectByMonth>();
        for (int i = 1 ; i <= 12; i++)
        {
            var no = 0;
            var conutEquipmentYearChectByMonth = new ConutEquipmentYearChectByMonth();
            conutEquipmentYearChectByMonth.Month =$"{i}月";
            var list = equipmentYearCheckDtos
                .Where(x => x.NextYearCheckTime.Month == i && x.NextYearCheckTime.Year == DateTime.Now.Year).ToList();
            list.ForEach(x=>x.No = ++no);
            conutEquipmentYearChectByMonth.List.AddRange(list);
            conutEquipmentYearChectByMonth.Conut = conutEquipmentYearChectByMonth.List.Count;
            resutl.Add(conutEquipmentYearChectByMonth);
        }
        return resutl;
    }

        public List<ConutReminderDto> ConutReminders(string smblLabId)
        {
            var result = new List<ConutReminderDto>();
            var now = DateTime.Now;
            var equipmentContext = new EquipmentContext(_dbContext);
            var equipments = new List<EMS_EQUIPMENT_INFO>();
            _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>()
                .Where(x=>x.SMBL_LAB_ID == smblLabId)
                .Includes(i => i.eMS_MAINTAIN_INFO)
                .Includes(i => i.eMS_CORRECT_INFO)
                .ForEach(it => equipments.Add(it));
            var no = 0;
            foreach (var equipment in equipments)
            {
                var recordsMaintains  = equipment.eMS_MAINTAIN_INFO
                    .Where(x=>x.MAINTAIN_DATE.HasValue)
                    .Where(x=>x.MAINTAIN_DATE.Value.Year == now.Year)
                    .Where(x=>x.MAINTAIN_STATE == "1")
                    .Where(x=> !x.OCCUR_EVENT.IsNullOrEmpty())
                    .ToList();
                foreach (var recordsMaintain in recordsMaintains)
                {
                    var item = new ConutReminderDto();
                    item.No = ++ no;
                    item.ReminderType = "保养";
                    item.Date = recordsMaintain.MAINTAIN_DATE;
                    item.EquipmentName = equipment.EQUIPMENT_NAME;
                    item.SmblClass = equipmentContext.ExchangeEquipmentSmblClass(equipment.SMBL_CLASS);
                    item.EquipmentModel = equipment.EQUIPMENT_MODEL;
                    item.Content = recordsMaintain.OCCUR_EVENT ;
                    item.Status ="特殊事件" ;
                    result.Add(item);
                }
                
                var recordsCorrects  = equipment.eMS_CORRECT_INFO
                    .Where(x=>x.FIRST_RTIME.Value.Year == now.Year)
                    .Where(x=>x.STATE == "未执行")
                    .Where(x=>x.CORRECT_STATE == "1")
                    .ToList();
                foreach (var recordsCorrect in recordsCorrects)
                {
                    var content = "";
                    if (recordsCorrect.RELATION_EVENT.IsNotNullOrEmpty())
                    {
                        if (recordsCorrect.RELATION_EVENT.Contains("BY"))
                        {
                            content = "设备保养产生事件";
                        }
                        if (recordsCorrect.RELATION_EVENT.Contains("WX"))
                        {
                            content = "设备维修产生事件";
                        }
                    }
                    var item = new ConutReminderDto();
                    item.No = ++ no;
                    item.ReminderType = "校准";
                    item.Date = recordsCorrect.FIRST_RTIME;
                    item.EquipmentName = equipment.EQUIPMENT_NAME;
                    item.SmblClass = equipmentContext.ExchangeEquipmentSmblClass(equipment.SMBL_CLASS);
                    item.EquipmentModel = equipment.EQUIPMENT_MODEL;
                    item.Content = recordsCorrect.RELATION_EVENT.IsNotNullOrEmpty() ? content  :  "常规校准";
                    item.Status = recordsCorrect.STATE == "未执行"?  "待执行"  : "特殊事件";
                    result.Add(item);
                }

            }
            return result;
        }

        /// <summary>
        /// 获取所有记录 按年、月份
        /// </summary>
        /// <param name="time"></param>
        /// <returns></returns>
        public List<IRecord> GetAllRecordsByDate(DateTime? time)
        {
            var result = new List<IRecord>();

            var mainTainRecords = _dbContext.Db.Queryable<EMS_MAINTAIN_INFO>()
                .Where(x => x.MAINTAIN_DATE.HasValue)
                .Where(x => x.MAINTAIN_DATE!.Value.Year == time.Value.Year &&
                            x.MAINTAIN_DATE!.Value.Month == time.Value.Month).ToList();
            var correctRecords = _dbContext.Db.Queryable<EMS_CORRECT_INFO>().Where(x => x.CORRECT_DATE.HasValue)
                .Where(x => x.CORRECT_DATE.Value.Year == time.Value.Year &&
                            x.CORRECT_DATE!.Value.Month == time.Value.Month).ToList();
            var verifcationRecords = _dbContext.Db.Queryable<EMS_VERIFICATION_INFO>().Where(x => x.VERIFICATION_DATE.HasValue)
                .Where(x => x.VERIFICATION_DATE!.Value.Year == time.Value.Year &&
                            x.VERIFICATION_DATE!.Value.Month == time.Value.Month).ToList();
            var comparisonRecords = _dbContext.Db.Queryable<EMS_COMPARISON_INFO>().Where(x => x.COMPARISON_DATE.HasValue)
                .Where(x => x.COMPARISON_DATE!.Value.Year == time.Value.Year &&
                            x.COMPARISON_DATE!.Value.Month == time.Value.Month).ToList();
            var repairRecords = _dbContext.Db.Queryable<EMS_REPAIR_INFO>().Where(x => x.REPAIR_DATE.HasValue)
                .Where(x => x.REPAIR_DATE!.Value.Year == time.Value.Year &&
                            x.REPAIR_DATE!.Value.Month == time.Value.Month).ToList();
            var changeRecords = _dbContext.Db.Queryable<EMS_CHANGE_INFO>().Where(x => x.CHANGE_DATE.HasValue)
                .Where(x => x.CHANGE_DATE!.Value.Year == time.Value.Year &&
                            x.CHANGE_DATE!.Value.Month == time.Value.Month).ToList();
            result.AddRange(mainTainRecords);
            result.AddRange(correctRecords);
            result.AddRange(verifcationRecords);
            result.AddRange(comparisonRecords);
            result.AddRange(repairRecords);
            result.AddRange(changeRecords);
            return result;
        }

        public Dictionary<string, string> GetEquipmentNameDict(List<string> equipmentIds , string? smblLabId)
        {
            var equipmentNames = _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>()
                .WhereIF(smblLabId.IsNotNullOrEmpty(),x=>x.SMBL_LAB_ID == smblLabId)
                .Where(x => equipmentIds.Contains(x.EQUIPMENT_ID))
                .Select(x => new { x.EQUIPMENT_ID,  x.EQUIPMENT_NAME })
                .ToDictionary<string>(x => x.EQUIPMENT_ID, x => x.EQUIPMENT_NAME);
            return equipmentNames;
        }
    }
}   
