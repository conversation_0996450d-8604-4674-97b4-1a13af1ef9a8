﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.Dtos
{
    /// <summary>
    /// 使用归档
    /// </summary>
    public class IssueFileDto
    {
        /// <summary>
        /// 展示类型
        /// </summary>
        public string DISPLAY_TYPE { get; set; }
        /// <summary>
        /// 归档附件信息
        /// </summary>
        public List<IssueFileInfo> IssueFileInfo { get; set; }
    }
    public class IssueFileInfo
    {
        /// <summary>
        /// 保存时间
        /// </summary>
        public DateTime? ARCHIVING_TIME { get; set; }
        /// <summary>
        /// 文件地址
        /// </summary>
        public string ISSUE_FILE { get; set; }
        /// <summary>
        /// 附件来源（equipment：设备程序；tim：事务项归档）
        /// </summary>
        public string ISSUE_SOURCE { get; set; }
        /// <summary>
        /// 归档类型
        /// </summary>
        public string ISSUE_TYPE { get; set; }
        /// <summary>
        /// 操作人
        /// </summary>
        public string OPER_PERSON { get; set; }
        /// <summary>
        /// 附件名
        /// </summary>
        public string ISSUE_NAME { get; set; }
        /// <summary>
        /// 附件ID
        /// </summary>
        public string ISSUE_ID { get;set; }
        /// <summary>
        /// 附件类型
        /// </summary>
        public string FILE_TYPE { get; set; }
        public string ISSUE_DATE { get; set; }
    }
}