﻿using AutoMapper;
using XH.H82.Models.Dtos;
using XH.H82.Models.Entities;


namespace XH.H82.Models
{
    public class MappingProfile : Profile
    {
        public MappingProfile()
        {
            //模型映射演示
            CreateMap<StartTemplateDto, TEST_START_TEMPLATE>()
                .ForMember(d => d.ID,
                    opt => opt.MapFrom(s => s.Id))

                //允许反向
                .ReverseMap();
            CreateMap<EMS_EQUIPMENT_INFO, EquipmentInfoDto>();
        }

    }
}