﻿using XH.H82.Models.Dtos.Transaction;

namespace XH.H82.IServices.Transaction
{
    public interface IContentDictService
    {
        void AddContentDict(string classId, string content, string? remark);

        void UpdateContentDict(string id, string content, string? remark);
        void DeleteContentDict(string id);

        List<ContentDictDto> GetContentDicts(string? classId, string? content);

    }
}
