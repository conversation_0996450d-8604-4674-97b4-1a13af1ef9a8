using System.Diagnostics;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;
using H.Utility;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using RestSharp;
using Serilog;
using XH.H82.Models.BusinessModuleClient.Dto.H91;

namespace XH.H82.Models.BusinessModuleClient;

public class H91Client
{
    private string addressH91 { get; set; } = "";
    private IHttpContextAccessor _httpContext;
    public H91Client(string ip, IHttpContextAccessor httpContext)
    {
        if (ip.IsNullOrEmpty())
        {
            throw new ArgumentNullException("addressH91 为空");
        }
        addressH91 = ip;
        _httpContext = httpContext;
    }


    /// <summary>
    /// 获取文件正在使用的版本
    /// </summary>
    /// <param name="docId"></param>
    /// <returns></returns>
    public FileInfoDto? GetSysDocFileDropDownByUsing(string docId)
    {
        var dmisFiles = new List<FileInfoDto>();
        var url = $"api/Dmis/GetSysDocFileDropDown?doc_id={docId}";
        ResultDto rsp = H91ClientGet<object>(url, null, true);
        if (rsp.data.ToString().IsNullOrEmpty())
        {
            return null;
        }
        var data = JsonConvert.DeserializeObject<List<FileInfoDto>>(rsp.data.ToString());
        dmisFiles.AddRange(data);
        var result  = dmisFiles.FirstOrDefault(x => x.FILE_STATE == "1");
        return result;
    }

    public ResultDto H91ClientGet<T>(string url, T requestBody = default(T), bool isNeedToken = true)
    {
        if (addressH91.IsNullOrEmpty())
        {
            throw new ArgumentNullException("addressH91 为空");
        }
        using RestClient client = new RestClient(
            new RestClientOptions
            {
                RemoteCertificateValidationCallback = (object sender, X509Certificate? certificate, X509Chain? chain, SslPolicyErrors sslPolicyErrors) => true,
                BaseUrl = new Uri(addressH91),
                ThrowOnAnyError = true,

            });

        Stopwatch stopwatch = new Stopwatch();
        stopwatch.Start();
        string value = _httpContext.HttpContext.Request.Headers.Authorization.ToString();
        RestRequest request = new RestRequest(url);
        if (requestBody != null)
        {
            request.AddBody(requestBody);
        }
        if (isNeedToken)
        {
            request.AddHeader("Authorization", value);
        }
        try
        {
            Log.Information($"请求H91client:{addressH91}{url}");
            RestResponse<ResultDto> restResponse = client.ExecuteGet<ResultDto>(request);
            stopwatch.Stop();
            Log.ForContext("elapsed", stopwatch.ElapsedMilliseconds).Information($"调用H91模块[{url}],耗时:{stopwatch.ElapsedMilliseconds}ms");
            if (restResponse.IsSuccessful && restResponse.Data.success)
            {
                return restResponse.Data;
            }

            Log.Error($"调用H91模块[{url}]发生错误:{restResponse.ErrorException}");
            throw new BizException($"调用H91模块[{url}]发生错误:{restResponse.ErrorException}", restResponse.ErrorException);
        }
        catch (Exception ex)
        {
            Log.Error($"调用H91模块[{url}]发生错误:{ex}");
            throw new BizException(ex.Message);
        }
    }

}