﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.Dtos
{
    public class MgroupTreeDto
    {
        public int TOTAL_SUBSCRIBE_AMOUNT { get; set; }
        public int TOTAL_EQUIPMENT_AMOUNT { get; set; }
        public int TOTAL_SCRAP_AMOUNT { get; set; }
        public int SELF_PEND { get; set; }
        public int SELF_MGROUP_PEND { get;set; }
        public List<SECOND_MGROUP_NODE> SECOND_MGROUP_NODE { get; set; }
    }
    public class SECOND_MGROUP_NODE
    {
        public string MGROUP_ID { get; set; }
        public string MGROUP_NAME { get; set; }
        public int SUBSCRIBE_AMOUNT { get; set; }
        public int EQUIPMENT_AMOUNT { get; set; }
        public int SCRAP_AMOUNT { get; set; }
        public int MGROUP_PEND { get; set; }
    }
}
