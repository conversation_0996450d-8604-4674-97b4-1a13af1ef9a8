﻿namespace XH.H82.API.Controllers.IoTDevice;

/// <summary>
/// 设备运行情况信息模型
/// </summary>
public class CountTheTotalUseEquipmentDto
{
    /// <summary>
    /// 生安设备类型
    /// </summary>
    public string SmblClass { get; set; }
    /// <summary>
    /// 生安设备类型名
    /// </summary>
    public string SmblClassName { get; set; }
    /// <summary>
    /// 当日使用总台数
    /// </summary>
    public int TotalEquipmentCount { get; set; }
    /// <summary>
    /// 当日使用台数
    /// </summary>
    public int CurrentEquipmentCount { get; set; }
    /// <summary>
    /// 当日使用小时数/全部的设备的使用时间加起来
    /// </summary>
    public double CurrentUseDayHours { get; set; }
    /// <summary>
    /// 当月平均每台使用天数
    /// </summary>
    public double AvgMoonthUseCount { get; set; }
    /// <summary>
    /// 当月平均每台使用小时
    /// </summary>
    public double AvgMoonthUseHoursCount { get; set; }
    /// <summary>
    /// 是否接入
    /// </summary>
    public bool IsAccess { get; set; } = false;

    /// <summary>
    /// 使用情况明细
    /// </summary>
    public List<CountTheTotalUseEquipmentCategoriesDto> Categories { get; set; } =
        new List<CountTheTotalUseEquipmentCategoriesDto>();

}