using XH.H82.Models.EquipmengtClassNew;

namespace XH.H82.Models.Dtos.EquipmentAdditionalRecord
{
    /// <summary>
    /// 设备档案记录详细信息DTO
    /// </summary>
    public class EquipmentArchiveDetailDto
    {
        /// <summary>
        /// 档案记录ID
        /// </summary>
        public string EqpArchivesId { get; set; }

        /// <summary>
        /// 档案记录名称
        /// </summary>
        public string EqpArchivesName { get; set; }

        /// <summary>
        /// 档案记录类型 0:固定 1:扩展
        /// </summary>
        public string EqpArchivesType { get; set; }

        /// <summary>
        /// 档案记录父级ID
        /// </summary>
        public string? EqpArchivesPid { get; set; }

        /// <summary>
        /// 档案记录父级名称
        /// </summary>
        public string? EqpArchivesPName { get; set; }

        /// <summary>
        /// 档案顺序
        /// </summary>
        public double? EqpArchivesSort { get; set; }

        /// <summary>
        /// 档案记录状态 0禁用 1在用 2删除
        /// </summary>
        public string EqpArchivesState { get; set; }

        /// <summary>
        /// 表单配置记录ID
        /// </summary>
        public string? FormSetupId { get; set; }

        /// <summary>
        /// 表格配置记录ID
        /// </summary>
        public string? TableSetupId { get; set; }

        /// <summary>
        /// 档案记录扩展配置JSON
        /// </summary>
        public string? EqpArchivesJson { get; set; }

        /// <summary>
        /// 设备扩展记录信息
        /// </summary>
        public EMS_EQP_ADDN_RECORD? AdditionalRecord { get; set; }

        /// <summary>
        /// 档案记录对应的业务数据（如校准记录、调试记录等）
        /// </summary>
        public object? BusinessData { get; set; }

        /// <summary>
        /// 业务数据类型名称
        /// </summary>
        public string? BusinessDataTypeName { get; set; }

        /// <summary>
        /// 是否有扩展字段数据
        /// </summary>
        public bool HasAdditionalData => AdditionalRecord != null && !string.IsNullOrEmpty(AdditionalRecord.EquitmentJson);

        /// <summary>
        /// 是否有业务数据
        /// </summary>
        public bool HasBusinessData => BusinessData != null;
    }
}
