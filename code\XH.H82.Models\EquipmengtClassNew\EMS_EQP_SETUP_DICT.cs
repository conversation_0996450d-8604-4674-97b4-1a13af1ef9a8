﻿using H.Utility.SqlSugarInfra;
using SqlSugar;

namespace XH.H82.Models.EquipmengtClassNew;

/// <summary>
    /// 设备设置字典表
    /// </summary>
    [DBOwner("XH_OA")]
    [SugarTable("EMS_EQP_SETUP_DICT", TableDescription = "设备设置字典表")]
    public class EMS_EQP_SETUP_DICT
    {
        /// <summary>
        /// 设置ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true,ColumnName = "EQP_SETUP_ID")]
        public string EqpSetupId{ get; set; }
        
        /// <summary>
        /// 医疗机构ID
        /// </summary>
        [SugarColumn(ColumnName = "HOSPITAL_ID")]
        public string HospitalId{ get; set; }
        
        /// <summary>
        /// 自定义名称
        /// </summary>
        [SugarColumn(ColumnName = "EQP_SETUP_CNAME")]
        public string? EqpSetupCname{ get; set; }
        
        /// <summary>
        /// 设置类型
        /// </summary>
        [SugarColumn(ColumnName = "EQP_SETUP_TYPE")]
        public string? EqpSetupType{ get; set; }
        
        /// <summary>
        /// 设置属性;扩展属性
        /// </summary>
        [SugarColumn(ColumnName = "EQP_SETUP_JSON")]
        public string? EqpSetupJson{ get; set; }
        
        /// <summary>
        /// 排序号
        /// </summary>
        [SugarColumn(ColumnName = "EQP_SETUP_NSORT")]
        public double? EqpSetupNsort{ get; set; }
        
        /// <summary>
        /// 状态;0禁用  1在用  2删除
        /// </summary>
        [SugarColumn(ColumnName = "EQP_SETUP_NSTATE")]
        public string? EqpSetupNstate{ get; set; }
        
        /// <summary>
        /// 首次登记人
        /// </summary>
        [SugarColumn(ColumnName = "FIRST_RPERSON")]
        public string? FIRST_RPERSON{ get; set; }
        
        /// <summary>
        /// 首次登记时间
        /// </summary>
        [SugarColumn(ColumnName = "FIRST_RTIME")]
        public DateTime? FIRST_RTIME{ get; set; }
        
        /// <summary>
        /// 最后修改人员
        /// </summary>
        [SugarColumn(ColumnName = "LAST_MPERSON")]
        public string? LAST_MPERSON{ get; set; }
        
        /// <summary>
        /// 最后修改时间
        /// </summary>
        [SugarColumn(ColumnName = "LAST_MTIME")]
        public DateTime? LAST_MTIME{ get; set; }
        
        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(ColumnName = "REMARK")]
        public string? Remark{ get; set; }
        
    }