﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XH.H82.Models.BusinessModuleClient;
using XH.H82.Models.DeviceRelevantInformation.Enum;
using static XH.H82.Models.BusinessModuleClient.H92Client;

namespace XH.H82.Models.DeviceRelevantInformation
{


    /// <summary>
    /// 设备展示信息
    /// </summary>
    public class EquipmentDisplay
    {
        /// <summary>
        /// 主键
        /// </summary>
        public string Id { get; set; }
        /// <summary>
        /// 设备id
        /// </summary>
        public string EquipmentId { get; set; }
        /// <summary>
        /// 设备当前状态（中文）
        /// </summary>
        public string State { get; set; }
        public EquipmentStateEnum EquipmentState { get; set; }

        /// <summary>
        /// 设备状态变更时间
        /// </summary>
        public DateTime? ChangeStateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 保养类型
        /// </summary>
        public string MaintenanceType { get; set; }
        /// <summary>
        /// 保养预警多少天
        /// </summary>
        public int MaintenanceDate { get; set; } = 0;
        /// <summary>
        /// 提醒消息的状态   告警|预警|正常
        /// </summary>
        public TaskStatusEnum MaintenanceState { get; set; } = TaskStatusEnum.NoReminder;
        /// <summary>
        /// 校预警多少天
        /// </summary>
        public int CorrectDate { get; set; } = 0;
        /// <summary>
        /// 提醒消息的状态   告警|预警|正常
        /// </summary>
        public TaskStatusEnum CorrectState { get; set; } = TaskStatusEnum.NoReminder;
        /// <summary>
        /// 比对预警多少天
        /// </summary>
        public int ComparisonDate { get; set; } = 0;

        /// <summary>
        /// 提醒消息的状态   告警|预警|正常
        /// </summary>
        public TaskStatusEnum ComparisonState { get; set; } = TaskStatusEnum.NoReminder;

        /// <summary>
        /// 设备证书类型警告
        /// </summary>
        public List<CertificateWarn> Certificates = new List<CertificateWarn>();
    }


    public class CertificateWarn
    {
        public string CompanyId { get; set; }
        public string CompanyName { get; set; }
        /// <summary>
        /// 证书名称
        /// </summary>
        public string CertificateName { get; set; }
        /// <summary>
        /// 证书有效期状态
        /// </summary>
        public CertificatStatusEnum CertificateStatus { get; set; }

        /// <summary>
        /// 证书类型
        /// </summary>
        public string CertificateType { get; set; }
        /// <summary>
        /// 预警多少天
        /// </summary>
        public int CertificateDate
        { get; set; } = 0;
        /// <summary>
        /// 提醒消息的状态   告警|预警|正常
        /// </summary>
        public TaskStatusEnum CertificateState { get; set; } = TaskStatusEnum.NoReminder;

        public string UpdatePerson { get; set; }
        public string ToWarnMsg()
        {
            var str = "过期";

            if (CertificateStatus == CertificatStatusEnum.PreExpire)
            {

                if (CertificateDate == 0)
                {
                    return $"{CompanyName}的{CertificateType}今天到期";
                }
                str = "剩余";
            }

            return $"{CompanyName}的{CertificateType}{str}{CertificateDate}天";
        }
    }
}
