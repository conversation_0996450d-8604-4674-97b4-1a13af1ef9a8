﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.Dtos.Base
{
    public class ResultLogin
    {
        /// <summary>
        /// 返回内容
        /// </summary>
        public object ResponseContent { get; set; }

        /// <summary>
        /// 返回状态
        /// </summary>
        public ResponseStatus ResponseStatus { get; set; }

    }

    /// <summary>
    /// 返回值
    /// </summary>
    public class ResponseStatus
    {
        /// <summary>
        /// 错误代码
        /// </summary>
        public string ErrorCode { get; set; }
        /// <summary>
        /// 错误内容
        /// </summary>
        public string Message { get; set; }
        /// <summary>
        /// 返回成功
        /// </summary>
        public bool Success { get; set; } = true;
    }
}
