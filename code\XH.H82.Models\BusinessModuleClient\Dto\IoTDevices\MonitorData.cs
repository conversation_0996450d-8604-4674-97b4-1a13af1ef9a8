using Newtonsoft.Json;

namespace XH.H82.Models.BusinessModuleClient.Dto.IoTDevices;

/// <summary>
/// 传感器监测项的监测数据
/// </summary>
public class MonitorData
{
    /// <summary>
    /// 值
    /// </summary>
    [JsonProperty("value")]
    public double Value { get; set; }
    /// <summary>
    /// 监测类型
    /// </summary>
    [JsonProperty("bioAlarmType")]
    public int BioAlarmType { get; set; }
    /// <summary>
    /// 异常状态 1正常  0 异常
    /// </summary>
    [JsonProperty("status")]
    public int Status { get; set; }
}