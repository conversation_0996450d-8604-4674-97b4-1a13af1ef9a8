﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.Dtos
{
    public class MaintainFormDto
    {
        /// <summary>
        /// 保养周期
        /// </summary>
        public string MAINTAIN_CYCLE { get; set; }
        public List<MaintainFileDto> MaintainFileDto { get;set; }
    }
    public class MaintainFileDto
    {
        /// <summary>
        /// 保养日期
        /// </summary>
        public string MAINTAIN_DATE { get; set; }
        /// <summary>
        /// 保养人员
        /// </summary>
        public string MAINTAIN_PERSON { get; set; }
        /// <summary>
        /// 保养附件
        /// </summary>
        public List<IssueFile> MAINTAIN_FILES { get; set; }
    }
    public class IssueFile
    {
        public string ISSUE_ID { get; set; }
        public string ISSUE_NAME { get; set; }
        public string ISSUE_TYPE { get; set; }
        public string ISSUE_FILE { get; set; }
    }
}
