namespace XH.H82.Models.BusinessModuleClient.Dto;

        public class IncludesConfig
        {
            public bool INCLUDE_PURCHASE_INFO { get; set; } = false;
            public bool INCLUDE_INSTALL_INFO { get; set; } = false;
            public bool INCLUDE_COMPARISON_INFO { get; set; } = true;
            public bool INCLUDE_CORRECT_INFO { get; set; } = true;
            public bool INCLUDE_MAINTAIN_INFO { get; set; } = true;
            public bool INCLUDE_VERIFICATION_INFO { get; set; } = true;
            public bool INCLUDE_AUTHORIZE_INFO { get; set; } = false;
            public bool INCLUDE_CHANGE_INFO { get; set; } = false;
            public bool INCLUDE_DEBUG_INFO { get; set; } = false;
            public bool INCLUDE_PARTS_INFO { get; set; } = false;
            public bool INCLUDE_REPAIR_INFO { get; set; } = false;
            public bool INCLUDE_SCRAP_INFO { get; set; } = false;
            public bool INCLUDE_START_STOP { get; set; } = true;
            public bool INCLUDE_STARTUP_INFO { get; set; } = false;
            public bool INCLUDE_TRAIN_INFO { get; set; } = false;
            public bool INCLUDE_UNPACK_INFO { get; set; } = false;
            public bool INCLUDE_WORK_PLAN { get; set; } = true;
            public bool INCLUDE_ENVI_REQUIRE_INFO { get; set; } = false;
            public bool INCLUDE_CONTACTS_INFO { get; set; } = false;

            private List<string> _allUseConfig = new List<string>(){"eq","eqinfo"};
            public void SetIncludes(string key)
            {

                if (key == "week" || key == "moon" || key == "year")
                {
                    INCLUDE_WORK_PLAN = true;
                    INCLUDE_MAINTAIN_INFO = true;
                    _allUseConfig.AddRange(new List<string>(){"week","moon","year"});
                }
                if (key == "run")
                {
                    INCLUDE_WORK_PLAN = true;
                    INCLUDE_MAINTAIN_INFO = true;
                    INCLUDE_CORRECT_INFO = true;
                    INCLUDE_COMPARISON_INFO = true;
                    INCLUDE_VERIFICATION_INFO = true;
                    _allUseConfig.Add(key);
                }
                if (key == "evn")
                {
                    INCLUDE_ENVI_REQUIRE_INFO = true;
                    _allUseConfig.Add(key);
                }
                if (key == "deal" || key == "manu")
                {
                    INCLUDE_CONTACTS_INFO = true;
                    _allUseConfig.Add(key);
                }
                if (key == "subs")
                {
                    _allUseConfig.Add(key);
                }
                if (key == "train")
                {
                    INCLUDE_TRAIN_INFO = true;
                    _allUseConfig.Add(key);
                }
                if (key == "purc")
                {
                    INCLUDE_PURCHASE_INFO = true;
                    _allUseConfig.Add(key);
                }
                if (key == "prop")
                {
                    INCLUDE_STARTUP_INFO = true;
                    _allUseConfig.Add(key);
                }
                if (key == "plan")
                {
                    INCLUDE_WORK_PLAN = true;
                    INCLUDE_MAINTAIN_INFO = true;
                    INCLUDE_CORRECT_INFO = true;
                    INCLUDE_COMPARISON_INFO = true;
                    INCLUDE_VERIFICATION_INFO = true;
                    _allUseConfig.Add(key);
                }
                if (key == "open")
                {
                    INCLUDE_UNPACK_INFO = true;
                    _allUseConfig.Add(key);
                }
                if (key == "part")
                {
                    INCLUDE_PARTS_INFO = true;
                    _allUseConfig.Add(key);
                }
                if (key == "install")
                {
                    INCLUDE_INSTALL_INFO = true;
                    _allUseConfig.Add(key);
                }
                if (key == "corr")
                {
                    INCLUDE_WORK_PLAN = true;
                    INCLUDE_CORRECT_INFO = true;
                    _allUseConfig.Add(key);
                }
                if (key == "com")
                {
                    INCLUDE_WORK_PLAN = true;
                    INCLUDE_COMPARISON_INFO = true;
                    _allUseConfig.Add(key);
                }
                if (key == "mainte")
                {
                    INCLUDE_WORK_PLAN = true;
                    INCLUDE_MAINTAIN_INFO = true;
                    _allUseConfig.Add(key);
                }
                if (key == "verificae")
                {
                    INCLUDE_WORK_PLAN = true;
                    INCLUDE_VERIFICATION_INFO = true;
                    _allUseConfig.Add(key);
                }
                if (key == "bug")
                {
                    INCLUDE_DEBUG_INFO = true;
                    _allUseConfig.Add(key);
                }
                if (key == "auth")
                {
                    INCLUDE_AUTHORIZE_INFO = true;
                    _allUseConfig.Add(key);
                }

                if (key == "fix")
                {
                    INCLUDE_REPAIR_INFO = true;
                    _allUseConfig.Add(key);
                }

                if (key == "appe")
                {
                    _allUseConfig.Add(key);
                }
                if (key == "acce")
                {
                    _allUseConfig.Add(key);
                }

            }

            public bool IsxistsConfig(string key)
            {
                return _allUseConfig.Exists(x => x == key);
            }

            public static IncludesConfig InitFalse()
            {
                return new IncludesConfig()
                {
                    INCLUDE_COMPARISON_INFO = false,
                    INCLUDE_CORRECT_INFO = false,
                    INCLUDE_MAINTAIN_INFO = false,
                    INCLUDE_VERIFICATION_INFO = false,
                    INCLUDE_START_STOP = false,
                    INCLUDE_WORK_PLAN = false,
                };
            }
        }