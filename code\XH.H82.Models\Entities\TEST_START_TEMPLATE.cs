﻿using ProtoBuf;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace XH.H82.Models.Entities
{

    public class TEST_START_TEMPLATE
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string ID { get; set; }
        public string? NAME { get; set; }
        public int? VALUE { get; set; }
        public DateTime? CREATE_TIME { get; set; }    

    }
}
