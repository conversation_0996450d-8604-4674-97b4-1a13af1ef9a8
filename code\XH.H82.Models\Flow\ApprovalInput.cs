﻿namespace XH.H82.Models.Flow;

public class ApprovalInput
{
    /// <summary>
    /// 操作类型  2 审批通过 3 审批不通过 5 审批驳回
    /// </summary>
    public string flowOperateType { get; set; }
    /// <summary>
    /// 下一步审批人  当下一步审批人为指定审核人时才需要传
    /// </summary>
    public List<string> assignPerson { get; set; }
    /// <summary>
    /// 目标节点	  当操作为驳回时，才需要传
    /// </summary>
    public string? targetNodeId { get; set; }
    /// <summary>
    /// 驳回意见 当操作为驳回时才需要传
    /// </summary>
    public string? rejectInfo { get; set; }
    /// <summary>
    /// 审批意见	
    /// </summary>
    public string? nodeRemark { get; set; }
}