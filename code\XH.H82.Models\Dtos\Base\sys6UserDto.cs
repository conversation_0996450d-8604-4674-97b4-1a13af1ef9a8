﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Security.Cryptography;
using AutoMapper;
using H.Utility.SqlSugarInfra;
using SqlSugar;
using XH.LAB.UTILS.Models;

namespace XH.H82.Models.Dtos.Base
{
    [AutoMap(typeof(SYS6_USER), ReverseMap = true)]
    public class sys6UserDto
    {
        public string USER_NO { get; set; }
        public string LOGID { get; set; }
        public string USERNAME { get; set; }
        public string POWER { get; set; }
        public string DEPT_CODE { get; set; }
        public string TECH_POST { get; set; }
        public string STATE_FLAG { get; set; }
        public string REMARK { get; set; }
        public DateTime? PWD_EDIT_DATE { get; set; }
        public string LAB_ID { get; set; }
        public string PHONE_NO { get; set; }
        public string E_MAIL { get; set; }
        public string USER_TYPE { get; set; }
        public string P<PERSON>_BS { get; set; }
        public string HOSPITAL_ID { get; set; }
        public string HIS_ID { get; set; }
        public string QQ { get; set; }
        public string POWER_NAME { get; set; }
        public string DEPT_CODE_NAME { get; set; }
        public string TECH_POST_NAME { get; set; }
        public string STATE_FLAG_NAME { get; set; }
        public string USER_TYPE_NAME { get; set; }
        public string USER_CLASS_NAME { get; set; }
        public string LAB_NAME { get; set; }
        public string Token { get; set; }
        public string CLIENT_IP { get; set; }

        public string FILE_UPLOAD_ADDRESS { get; set; }
        public string FILE_PREVIEW_ADDRESS { get; set; }
        public string MGROUPID_AGGREGATE { get; set; }
        public string HOSPITAL_NAME { get; set; }

    }
}
