2025-07-14 08:57:06.169 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-14 09:02:51.270 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-14 09:41:14.936 +08:00 [ERR] Redis internal error ReadFromPipe
Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
2025-07-14 09:41:15.328 +08:00 [ERR] Redis internal error ReadFromPipe
Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
2025-07-14 09:41:15.187 +08:00 [ERR] Redis internal error ReadFromPipe
Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
2025-07-14 09:41:15.379 +08:00 [ERR] Redis internal error ReadFromPipe
Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
2025-07-14 09:41:16.039 +08:00 [ERR] Redis internal error ReadFromPipe
Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
2025-07-14 09:41:15.362 +08:00 [ERR] Redis internal error ReadFromPipe
Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
2025-07-14 09:41:15.203 +08:00 [ERR] Redis internal error ReadFromPipe
Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
2025-07-14 09:41:15.379 +08:00 [ERR] Redis internal error ReadFromPipe
Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
2025-07-14 09:41:15.362 +08:00 [ERR] Redis internal error ReadFromPipe
Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
2025-07-14 09:41:15.345 +08:00 [ERR] Redis internal error ReadFromPipe
Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
2025-07-14 09:41:15.344 +08:00 [ERR] Redis internal error ReadFromPipe
Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
2025-07-14 09:41:15.344 +08:00 [ERR] Redis internal error ReadFromPipe
Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
2025-07-14 09:41:15.362 +08:00 [ERR] Redis internal error ReadFromPipe
Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
2025-07-14 09:41:15.344 +08:00 [ERR] Redis internal error ReadFromPipe
Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
2025-07-14 09:41:15.168 +08:00 [ERR] Redis internal error ReadFromPipe
Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
2025-07-14 09:41:14.966 +08:00 [ERR] Redis internal error ReadFromPipe
Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
2025-07-14 09:41:17.459 +08:00 [ERR] Redis internal error ReadFromPipe
Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
2025-07-14 09:41:17.464 +08:00 [ERR] Redis internal error ReadFromPipe
Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
2025-07-14 09:41:18.062 +08:00 [ERR] Redis internal error ReadFromPipe
Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
2025-07-14 09:41:18.296 +08:00 [ERR] Redis connection error InternalFailure
StackExchange.Redis.RedisConnectionException: InternalFailure (ReadSocketError/ConnectionAborted, last-recv: 20) on **************:10090/Subscription, Idle/Faulted, last: PING, origin: ReadFromPipe, outstanding: 0, last-read: 2s ago, last-write: 3s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 20 of 32 available, in: 0, in-pipe: 0, out-pipe: 0, last-heartbeat: 0s ago, last-mbeat: 0s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
2025-07-14 09:41:18.562 +08:00 [ERR] Redis internal error ReadFromPipe
Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
2025-07-14 09:41:18.751 +08:00 [ERR] Redis connection error InternalFailure
StackExchange.Redis.RedisConnectionException: InternalFailure (ReadSocketError/ConnectionAborted, last-recv: 20) on **************:10089/Subscription, Idle/Faulted, last: PING, origin: ReadFromPipe, outstanding: 0, last-read: 2s ago, last-write: 3s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 20 of 32 available, in: 0, in-pipe: 0, out-pipe: 0, last-heartbeat: 0s ago, last-mbeat: 0s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
2025-07-14 09:41:18.859 +08:00 [ERR] Redis internal error ReadFromPipe
Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
2025-07-14 09:41:18.909 +08:00 [ERR] Redis connection error InternalFailure
StackExchange.Redis.RedisConnectionException: InternalFailure (ReadSocketError/ConnectionAborted, last-recv: 20) on **************:10088/Subscription, Idle/Faulted, last: PING, origin: ReadFromPipe, outstanding: 0, last-read: 3s ago, last-write: 3s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 20 of 32 available, in: 0, in-pipe: 0, out-pipe: 0, last-heartbeat: 0s ago, last-mbeat: 0s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
2025-07-14 09:41:18.909 +08:00 [ERR] Redis connection error InternalFailure
StackExchange.Redis.RedisConnectionException: InternalFailure (ReadSocketError/ConnectionAborted, last-recv: 636) on **************:10090/Interactive, Idle/Faulted, last: INFO, origin: ReadFromPipe, outstanding: 0, last-read: 3s ago, last-write: 3s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 20 of 32 available, in: 0, in-pipe: 0, out-pipe: 0, last-heartbeat: 0s ago, last-mbeat: 0s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
2025-07-14 09:41:19.170 +08:00 [ERR] Redis connection error InternalFailure
StackExchange.Redis.RedisConnectionException: InternalFailure (ReadSocketError/ConnectionAborted, last-recv: 542) on **************:10088/Interactive, Idle/Faulted, last: INFO, origin: ReadFromPipe, outstanding: 0, last-read: 3s ago, last-write: 3s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 19 of 32 available, in: 0, in-pipe: 0, out-pipe: 0, last-heartbeat: 0s ago, last-mbeat: 0s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
2025-07-14 09:41:19.173 +08:00 [ERR] Redis internal error ReadFromPipe
Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
2025-07-14 09:41:19.230 +08:00 [ERR] Redis connection error InternalFailure
StackExchange.Redis.RedisConnectionException: InternalFailure (ReadSocketError/ConnectionAborted, last-recv: 20) on **************:10090/Subscription, Idle/Faulted, last: PING, origin: ReadFromPipe, outstanding: 0, last-read: 7s ago, last-write: 7s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 17 of 32 available, in: 0, in-pipe: 0, out-pipe: 0, last-heartbeat: 0s ago, last-mbeat: 0s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
2025-07-14 09:41:19.367 +08:00 [ERR] Redis connection error InternalFailure
StackExchange.Redis.RedisConnectionException: InternalFailure (ReadSocketError/ConnectionAborted, last-recv: 20) on **************:10090/Subscription, Idle/Faulted, last: PING, origin: ReadFromPipe, outstanding: 0, last-read: 3s ago, last-write: 3s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 20 of 32 available, in: 0, in-pipe: 0, out-pipe: 0, last-heartbeat: 0s ago, last-mbeat: 0s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
2025-07-14 09:41:19.367 +08:00 [ERR] Redis connection error InternalFailure
StackExchange.Redis.RedisConnectionException: InternalFailure (ReadSocketError/ConnectionAborted, last-recv: 636) on **************:10090/Interactive, Idle/Faulted, last: INFO, origin: ReadFromPipe, outstanding: 0, last-read: 7s ago, last-write: 7s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 17 of 32 available, in: 0, in-pipe: 0, out-pipe: 0, last-heartbeat: 0s ago, last-mbeat: 0s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
2025-07-14 09:41:19.367 +08:00 [ERR] Redis connection error InternalFailure
StackExchange.Redis.RedisConnectionException: InternalFailure (ReadSocketError/ConnectionAborted, last-recv: 636) on **************:10090/Interactive, Idle/Faulted, last: INFO, origin: ReadFromPipe, outstanding: 0, last-read: 7s ago, last-write: 7s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 17 of 32 available, in: 0, in-pipe: 0, out-pipe: 0, last-heartbeat: 0s ago, last-mbeat: 0s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
2025-07-14 09:41:19.490 +08:00 [ERR] Redis connection error InternalFailure
StackExchange.Redis.RedisConnectionException: InternalFailure (ReadSocketError/ConnectionAborted, last-recv: 636) on **************:10089/Interactive, Idle/Faulted, last: INFO, origin: ReadFromPipe, outstanding: 0, last-read: 7s ago, last-write: 7s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 20 of 32 available, in: 0, in-pipe: 0, out-pipe: 0, last-heartbeat: 0s ago, last-mbeat: 0s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
2025-07-14 09:41:19.637 +08:00 [ERR] Redis internal error ReadFromPipe
Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
2025-07-14 09:41:19.675 +08:00 [ERR] Redis connection error UnableToConnect
StackExchange.Redis.RedisConnectionException: UnableToConnect on **************:10089/Interactive, Initializing/NotStarted, last: INFO, origin: BeginConnectAsync, outstanding: 0, last-read: 0s ago, last-write: 0s ago, keep-alive: 60s, state: Connecting, mgr: 18 of 32 available, last-heartbeat: never, last-mbeat: 0s ago, global: 0s ago, v: 2.8.31.52602
2025-07-14 09:41:19.687 +08:00 [ERR] Redis internal error ReadFromPipe
Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
2025-07-14 09:41:19.691 +08:00 [ERR] Redis internal error ReadFromPipe
Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
2025-07-14 09:41:19.706 +08:00 [ERR] Redis internal error ReadFromPipe
Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
2025-07-14 09:41:19.706 +08:00 [ERR] Redis connection error UnableToConnect
StackExchange.Redis.RedisConnectionException: UnableToConnect on **************:10089/Subscription, Initializing/NotStarted, last: PING, origin: BeginConnectAsync, outstanding: 0, last-read: 0s ago, last-write: 0s ago, keep-alive: 60s, state: Connecting, mgr: 19 of 32 available, last-heartbeat: never, last-mbeat: 0s ago, global: 0s ago, v: 2.8.31.52602
2025-07-14 09:41:19.789 +08:00 [ERR] Redis internal error ReadFromPipe
Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
2025-07-14 09:41:19.907 +08:00 [ERR] Redis connection error InternalFailure
StackExchange.Redis.RedisConnectionException: InternalFailure (ReadSocketError/ConnectionAborted, last-recv: 20) on **************:10088/Subscription, Idle/Faulted, last: PING, origin: ReadFromPipe, outstanding: 0, last-read: 7s ago, last-write: 7s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 17 of 32 available, in: 0, in-pipe: 0, out-pipe: 0, last-heartbeat: 0s ago, last-mbeat: 0s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
2025-07-14 09:41:19.907 +08:00 [ERR] Redis internal error ReadFromPipe
Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
2025-07-14 09:41:20.019 +08:00 [ERR] Redis connection error UnableToConnect
StackExchange.Redis.RedisConnectionException: UnableToConnect on **************:10088/Subscription, Initializing/NotStarted, last: PING, origin: BeginConnectAsync, outstanding: 0, last-read: 0s ago, last-write: 0s ago, keep-alive: 60s, state: Connecting, mgr: 17 of 32 available, last-heartbeat: never, last-mbeat: 0s ago, global: 0s ago, v: 2.8.31.52602
2025-07-14 09:41:20.168 +08:00 [ERR] Redis connection error InternalFailure
StackExchange.Redis.RedisConnectionException: InternalFailure (ReadSocketError/ConnectionAborted, last-recv: 542) on **************:10088/Interactive, Idle/Faulted, last: INFO, origin: ReadFromPipe, outstanding: 0, last-read: 8s ago, last-write: 8s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 15 of 32 available, in: 0, in-pipe: 0, out-pipe: 0, last-heartbeat: 1s ago, last-mbeat: 1s ago, global: 1s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
2025-07-14 09:41:20.534 +08:00 [ERR] Redis connection error InternalFailure
StackExchange.Redis.RedisConnectionException: InternalFailure (ReadSocketError/ConnectionAborted, last-recv: 636) on **************:10089/Interactive, Idle/Faulted, last: INFO, origin: ReadFromPipe, outstanding: 0, last-read: 8s ago, last-write: 8s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 16 of 32 available, in: 0, in-pipe: 0, out-pipe: 0, last-heartbeat: 1s ago, last-mbeat: 0s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
2025-07-14 09:41:20.536 +08:00 [ERR] Redis connection error UnableToConnect
StackExchange.Redis.RedisConnectionException: UnableToConnect on **************:10088/Subscription, Initializing/NotStarted, last: PING, origin: BeginConnectAsync, outstanding: 0, last-read: 1s ago, last-write: 1s ago, keep-alive: 60s, state: Connecting, mgr: 16 of 32 available, last-heartbeat: never, last-mbeat: 0s ago, global: 0s ago, v: 2.8.31.52602
2025-07-14 09:41:20.534 +08:00 [ERR] Redis connection error InternalFailure
StackExchange.Redis.RedisConnectionException: InternalFailure (ReadSocketError/ConnectionAborted, last-recv: 542) on **************:10088/Interactive, Idle/Faulted, last: PING, origin: ReadFromPipe, outstanding: 1, last-read: 4s ago, last-write: 0s ago, unanswered-write: 0s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 17 of 32 available, in: 0, in-pipe: 0, out-pipe: 14, last-heartbeat: 1s ago, last-mbeat: 1s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
   at StackExchange.Redis.ServerEndPoint.<>c__DisplayClass43_0.<<OnConnectedAsync>g__IfConnectedAsync|0>d.MoveNext() in /_/src/StackExchange.Redis/ServerEndPoint.cs:line 126
2025-07-14 09:41:20.534 +08:00 [ERR] Redis connection error InternalFailure
StackExchange.Redis.RedisConnectionException: InternalFailure (ReadSocketError/ConnectionAborted, last-recv: 542) on **************:10088/Interactive, Idle/Faulted, last: PING, origin: ReadFromPipe, outstanding: 1, last-read: 8s ago, last-write: 0s ago, unanswered-write: 0s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 16 of 32 available, in: 0, in-pipe: 0, out-pipe: 14, last-heartbeat: 1s ago, last-mbeat: 0s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
2025-07-14 09:41:20.553 +08:00 [ERR] Redis connection error InternalFailure
StackExchange.Redis.RedisConnectionException: InternalFailure (ReadSocketError/ConnectionAborted, last-recv: 20) on **************:10089/Subscription, Idle/Faulted, last: PING, origin: ReadFromPipe, outstanding: 0, last-read: 4s ago, last-write: 4s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 15 of 32 available, in: 0, in-pipe: 0, out-pipe: 0, last-heartbeat: 1s ago, last-mbeat: 1s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
2025-07-14 09:41:20.655 +08:00 [ERR] Redis connection error InternalFailure
StackExchange.Redis.RedisConnectionException: InternalFailure (ReadSocketError/ConnectionAborted, last-recv: 322) on d02.lis-china.com:10088/Interactive, Idle/Faulted, last: INFO, origin: ReadFromPipe, outstanding: 0, last-read: 4s ago, last-write: 4s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 30 of 32 available, in: 0, in-pipe: 0, out-pipe: 0, last-heartbeat: 1s ago, last-mbeat: 1s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
2025-07-14 09:41:20.655 +08:00 [ERR] Redis connection error InternalFailure
StackExchange.Redis.RedisConnectionException: InternalFailure (ReadSocketError/ConnectionAborted, last-recv: 322) on d02.lis-china.com:10088/Interactive, Idle/Faulted, last: INFO, origin: ReadFromPipe, outstanding: 0, last-read: 4s ago, last-write: 4s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 30 of 32 available, in: 0, in-pipe: 0, out-pipe: 0, last-heartbeat: 2s ago, last-mbeat: 1s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
2025-07-14 09:41:20.727 +08:00 [ERR] Redis connection error InternalFailure
StackExchange.Redis.RedisConnectionException: InternalFailure (ReadSocketError/ConnectionAborted, last-recv: 636) on **************:10090/Interactive, Idle/Faulted, last: INFO, origin: ReadFromPipe, outstanding: 0, last-read: 3s ago, last-write: 3s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 17 of 32 available, in: 0, in-pipe: 0, out-pipe: 0, last-heartbeat: 0s ago, last-mbeat: 0s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
2025-07-14 09:41:20.727 +08:00 [ERR] Redis connection error InternalFailure
StackExchange.Redis.RedisConnectionException: InternalFailure (ReadSocketError/ConnectionAborted, last-recv: 20) on d02.lis-china.com:10088/Subscription, Idle/Faulted, last: PING, origin: ReadFromPipe, outstanding: 0, last-read: 4s ago, last-write: 4s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 30 of 32 available, in: 0, in-pipe: 0, out-pipe: 0, last-heartbeat: 1s ago, last-mbeat: 1s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
2025-07-14 09:41:20.727 +08:00 [ERR] Redis connection error InternalFailure
StackExchange.Redis.RedisConnectionException: InternalFailure (ReadSocketError/ConnectionAborted, last-recv: 636) on **************:10089/Interactive, Idle/Faulted, last: INFO, origin: ReadFromPipe, outstanding: 0, last-read: 4s ago, last-write: 4s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 15 of 32 available, in: 0, in-pipe: 0, out-pipe: 0, last-heartbeat: 1s ago, last-mbeat: 1s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
2025-07-14 09:41:20.727 +08:00 [ERR] Redis connection error InternalFailure
StackExchange.Redis.RedisConnectionException: InternalFailure (ReadSocketError/ConnectionAborted, last-recv: 20) on **************:10089/Subscription, Idle/Faulted, last: PING, origin: ReadFromPipe, outstanding: 0, last-read: 9s ago, last-write: 9s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 23 of 32 available, in: 0, in-pipe: 0, out-pipe: 0, last-heartbeat: 2s ago, last-mbeat: 2s ago, global: 1s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
2025-07-14 09:41:20.727 +08:00 [ERR] Redis connection error InternalFailure
StackExchange.Redis.RedisConnectionException: InternalFailure (ReadSocketError/ConnectionAborted, last-recv: 20) on d02.lis-china.com:10088/Subscription, Idle/Faulted, last: PING, origin: ReadFromPipe, outstanding: 0, last-read: 4s ago, last-write: 4s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 30 of 32 available, in: 0, in-pipe: 0, out-pipe: 0, last-heartbeat: 1s ago, last-mbeat: 1s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
2025-07-14 09:41:20.862 +08:00 [ERR] Redis connection error InternalFailure
StackExchange.Redis.RedisConnectionException: InternalFailure (ReadSocketError/ConnectionAborted, last-recv: 20) on **************:10090/Subscription, Idle/Faulted, last: PING, origin: ReadFromPipe, outstanding: 0, last-read: 8s ago, last-write: 8s ago, keep-alive: 60s, state: ConnectedEstablished, mgr: 15 of 32 available, in: 0, in-pipe: 0, out-pipe: 0, last-heartbeat: 1s ago, last-mbeat: 1s ago, global: 0s ago, v: 2.8.31.52602
 ---> Pipelines.Sockets.Unofficial.ConnectionAbortedException: The connection was aborted
   at System.IO.Pipelines.Pipe.GetReadResult(ReadResult& result)
   at System.IO.Pipelines.Pipe.GetReadAsyncResult()
   at System.IO.Pipelines.Pipe.DefaultPipeReader.GetResult(Int16 token)
   at StackExchange.Redis.PhysicalConnection.ReadFromPipe() in /_/src/StackExchange.Redis/PhysicalConnection.cs:line 1883
   --- End of inner exception stack trace ---
2025-07-14 15:20:34.923 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-14 15:21:53.590 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-14 20:54:27.758 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-14 20:58:27.354 +08:00 [ERR] 未处理的异常::System.Text.Json.JsonException: The JSON value could not be converted to System.Nullable`1[System.DateTime]. Path: $.data[0].FIRST_RTIME | LineNumber: 0 | BytePositionInLine: 1292.
 ---> System.FormatException: The JSON value is not in a supported DateTime format.
   at System.Text.Json.ThrowHelper.ThrowFormatException(DataType dataType)
   at System.Text.Json.Utf8JsonReader.GetDateTime()
   at System.Text.Json.Serialization.Converters.DateTimeConverter.Read(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options)
   at System.Text.Json.Serialization.Converters.NullableConverter`1.Read(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options)
   at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonCollectionConverter`2.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, TCollection& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, Utf8JsonReader& reader, Exception ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.Deserialize(Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at RestSharp.Serializers.Json.SystemTextJsonSerializer.Deserialize[T](RestResponse response)
   at RestSharp.Serializers.RestSerializers.DeserializeContent[T](RestResponse response)
   at RestSharp.Serializers.RestSerializers.Deserialize[T](RestRequest request, RestResponse raw, ReadOnlyRestClientOptions options, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.ExecuteAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.AsyncHelpers.<>c__DisplayClass1_0`1.<<RunSync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.<Run>g__PostCallback|7_0(Object _)
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.Run()
   at RestSharp.AsyncHelpers.RunSync(Func`1 task)
   at RestSharp.AsyncHelpers.RunSync[T](Func`1 task)
   at RestSharp.RestClientExtensions.ExecuteGet[T](IRestClient client, RestRequest request)
   at XH.H82.Models.BusinessModuleClient.H04.H04Client.H04Request[T](String url, Method method, Object body) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Models\BusinessModuleClient\H04\H04Client.cs:line 65
   at XH.H82.Models.BusinessModuleClient.H04.H04Client.GetFieldDicts(String filedClass) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Models\BusinessModuleClient\H04\H04Client.cs:line 132
   at XH.H82.Services.TemplateDesign.TemplateDesignService.GetFieldDicts(String filedClass) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\TemplateDesign\TemplateDesignService.cs:line 152
   at Castle.Proxies.Invocations.ITemplateDesignService_GetFieldDicts.InvokeMethodOnTarget()
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.UseCacheWhenFailedAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.CacheAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at Castle.Proxies.ITemplateDesignServiceProxy.GetFieldDicts(String filedClass)
   at XH.H82.API.Controllers.EquipmentClassNew.EquipmentClassNewController.GetGetFieldDicts(String filedClass) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\Controllers\EquipmentClassNew\EquipmentClassNewController.cs:line 159
   at lambda_method918(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)System.FormatException: The JSON value is not in a supported DateTime format.
   at System.Text.Json.ThrowHelper.ThrowFormatException(DataType dataType)
   at System.Text.Json.Utf8JsonReader.GetDateTime()
   at System.Text.Json.Serialization.Converters.DateTimeConverter.Read(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options)
   at System.Text.Json.Serialization.Converters.NullableConverter`1.Read(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options)
   at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonCollectionConverter`2.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, TCollection& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
2025-07-14 20:58:27.363 +08:00 [ERR] HTTP GET /api/EquipmentClassNew/GetGetFieldDicts responded 500 in 125060.7778 ms
2025-07-14 20:59:35.460 +08:00 [ERR] 未处理的异常::System.Text.Json.JsonException: The JSON value could not be converted to System.Nullable`1[System.DateTime]. Path: $.data[0].FIRST_RTIME | LineNumber: 0 | BytePositionInLine: 1292.
 ---> System.FormatException: The JSON value is not in a supported DateTime format.
   at System.Text.Json.ThrowHelper.ThrowFormatException(DataType dataType)
   at System.Text.Json.Utf8JsonReader.GetDateTime()
   at System.Text.Json.Serialization.Converters.DateTimeConverter.Read(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options)
   at System.Text.Json.Serialization.Converters.NullableConverter`1.Read(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options)
   at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonCollectionConverter`2.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, TCollection& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, Utf8JsonReader& reader, Exception ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.Deserialize(Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at RestSharp.Serializers.Json.SystemTextJsonSerializer.Deserialize[T](RestResponse response)
   at RestSharp.Serializers.RestSerializers.DeserializeContent[T](RestResponse response)
   at RestSharp.Serializers.RestSerializers.Deserialize[T](RestRequest request, RestResponse raw, ReadOnlyRestClientOptions options, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.ExecuteAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.AsyncHelpers.<>c__DisplayClass1_0`1.<<RunSync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.<Run>g__PostCallback|7_0(Object _)
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.Run()
   at RestSharp.AsyncHelpers.RunSync(Func`1 task)
   at RestSharp.AsyncHelpers.RunSync[T](Func`1 task)
   at RestSharp.RestClientExtensions.ExecuteGet[T](IRestClient client, RestRequest request)
   at XH.H82.Models.BusinessModuleClient.H04.H04Client.H04Request[T](String url, Method method, Object body)
   at XH.H82.Models.BusinessModuleClient.H04.H04Client.GetFieldDicts(String filedClass)
   at XH.H82.Services.TemplateDesign.TemplateDesignService.GetFieldDicts(String filedClass) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\TemplateDesign\TemplateDesignService.cs:line 152
   at Castle.Proxies.Invocations.ITemplateDesignService_GetFieldDicts.InvokeMethodOnTarget()
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.UseCacheWhenFailedAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.CacheAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at Castle.Proxies.ITemplateDesignServiceProxy.GetFieldDicts(String filedClass)
   at XH.H82.API.Controllers.EquipmentClassNew.EquipmentClassNewController.GetGetFieldDicts(String filedClass) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\Controllers\EquipmentClassNew\EquipmentClassNewController.cs:line 159
   at lambda_method918(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)System.FormatException: The JSON value is not in a supported DateTime format.
   at System.Text.Json.ThrowHelper.ThrowFormatException(DataType dataType)
   at System.Text.Json.Utf8JsonReader.GetDateTime()
   at System.Text.Json.Serialization.Converters.DateTimeConverter.Read(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options)
   at System.Text.Json.Serialization.Converters.NullableConverter`1.Read(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options)
   at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonCollectionConverter`2.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, TCollection& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
2025-07-14 20:59:35.466 +08:00 [ERR] HTTP GET /api/EquipmentClassNew/GetGetFieldDicts responded 500 in 18783.4334 ms
2025-07-14 20:59:57.985 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-14 21:01:10.010 +08:00 [ERR] 未处理的异常::System.Text.Json.JsonException: The JSON value could not be converted to System.DateTime. Path: $.data[0].FIRST_RTIME | LineNumber: 0 | BytePositionInLine: 1292.
 ---> System.FormatException: The JSON value is not in a supported DateTime format.
   at System.Text.Json.ThrowHelper.ThrowFormatException(DataType dataType)
   at System.Text.Json.Utf8JsonReader.GetDateTime()
   at System.Text.Json.Serialization.Converters.DateTimeConverter.Read(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options)
   at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonCollectionConverter`2.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, TCollection& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, Utf8JsonReader& reader, Exception ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.Deserialize(Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at RestSharp.Serializers.Json.SystemTextJsonSerializer.Deserialize[T](RestResponse response)
   at RestSharp.Serializers.RestSerializers.DeserializeContent[T](RestResponse response)
   at RestSharp.Serializers.RestSerializers.Deserialize[T](RestRequest request, RestResponse raw, ReadOnlyRestClientOptions options, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.ExecuteAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.AsyncHelpers.<>c__DisplayClass1_0`1.<<RunSync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.<Run>g__PostCallback|7_0(Object _)
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.Run()
   at RestSharp.AsyncHelpers.RunSync(Func`1 task)
   at RestSharp.AsyncHelpers.RunSync[T](Func`1 task)
   at RestSharp.RestClientExtensions.ExecuteGet[T](IRestClient client, RestRequest request)
   at XH.H82.Models.BusinessModuleClient.H04.H04Client.H04Request[T](String url, Method method, Object body) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Models\BusinessModuleClient\H04\H04Client.cs:line 65
   at XH.H82.Models.BusinessModuleClient.H04.H04Client.GetFieldDicts(String filedClass) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Models\BusinessModuleClient\H04\H04Client.cs:line 132
   at XH.H82.Services.TemplateDesign.TemplateDesignService.GetFieldDicts(String filedClass) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\TemplateDesign\TemplateDesignService.cs:line 152
   at Castle.Proxies.Invocations.ITemplateDesignService_GetFieldDicts.InvokeMethodOnTarget()
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.UseCacheWhenFailedAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.CacheAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at Castle.Proxies.ITemplateDesignServiceProxy.GetFieldDicts(String filedClass)
   at XH.H82.API.Controllers.EquipmentClassNew.EquipmentClassNewController.GetGetFieldDicts(String filedClass) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\Controllers\EquipmentClassNew\EquipmentClassNewController.cs:line 161
   at lambda_method918(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)System.FormatException: The JSON value is not in a supported DateTime format.
   at System.Text.Json.ThrowHelper.ThrowFormatException(DataType dataType)
   at System.Text.Json.Utf8JsonReader.GetDateTime()
   at System.Text.Json.Serialization.Converters.DateTimeConverter.Read(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options)
   at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonCollectionConverter`2.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, TCollection& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
2025-07-14 21:01:10.083 +08:00 [ERR] HTTP GET /api/EquipmentClassNew/GetGetFieldDicts responded 500 in 64882.1765 ms
2025-07-14 21:03:16.574 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-14 21:03:51.138 +08:00 [ERR] 未处理的异常::System.Text.Json.JsonException: The JSON value could not be converted to System.Nullable`1[System.DateTime]. Path: $.data[0].FIRST_RTIME | LineNumber: 0 | BytePositionInLine: 1292.
 ---> System.FormatException: The JSON value is not in a supported DateTime format.
   at System.Text.Json.ThrowHelper.ThrowFormatException(DataType dataType)
   at System.Text.Json.Utf8JsonReader.GetDateTime()
   at System.Text.Json.Serialization.Converters.DateTimeConverter.Read(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options)
   at System.Text.Json.Serialization.Converters.NullableConverter`1.Read(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options)
   at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonCollectionConverter`2.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, TCollection& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, Utf8JsonReader& reader, Exception ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.Deserialize(Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at RestSharp.Serializers.Json.SystemTextJsonSerializer.Deserialize[T](RestResponse response)
   at RestSharp.Serializers.RestSerializers.DeserializeContent[T](RestResponse response)
   at RestSharp.Serializers.RestSerializers.Deserialize[T](RestRequest request, RestResponse raw, ReadOnlyRestClientOptions options, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.ExecuteAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.AsyncHelpers.<>c__DisplayClass1_0`1.<<RunSync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.<Run>g__PostCallback|7_0(Object _)
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.Run()
   at RestSharp.AsyncHelpers.RunSync(Func`1 task)
   at RestSharp.AsyncHelpers.RunSync[T](Func`1 task)
   at RestSharp.RestClientExtensions.ExecuteGet[T](IRestClient client, RestRequest request)
   at XH.H82.Models.BusinessModuleClient.H04.H04Client.H04Request[T](String url, Method method, Object body) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Models\BusinessModuleClient\H04\H04Client.cs:line 65
   at XH.H82.Models.BusinessModuleClient.H04.H04Client.GetFieldDicts(String filedClass) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Models\BusinessModuleClient\H04\H04Client.cs:line 132
   at XH.H82.Services.TemplateDesign.TemplateDesignService.GetFieldDicts(String filedClass) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\TemplateDesign\TemplateDesignService.cs:line 152
   at Castle.Proxies.Invocations.ITemplateDesignService_GetFieldDicts.InvokeMethodOnTarget()
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.UseCacheWhenFailedAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.CacheAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at Castle.Proxies.ITemplateDesignServiceProxy.GetFieldDicts(String filedClass)
   at XH.H82.API.Controllers.EquipmentClassNew.EquipmentClassNewController.GetGetFieldDicts(String filedClass) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\Controllers\EquipmentClassNew\EquipmentClassNewController.cs:line 161
   at lambda_method918(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)System.FormatException: The JSON value is not in a supported DateTime format.
   at System.Text.Json.ThrowHelper.ThrowFormatException(DataType dataType)
   at System.Text.Json.Utf8JsonReader.GetDateTime()
   at System.Text.Json.Serialization.Converters.DateTimeConverter.Read(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options)
   at System.Text.Json.Serialization.Converters.NullableConverter`1.Read(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options)
   at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonCollectionConverter`2.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, TCollection& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
2025-07-14 21:03:51.167 +08:00 [ERR] HTTP GET /api/EquipmentClassNew/GetGetFieldDicts responded 500 in 28319.2815 ms
2025-07-14 21:04:56.208 +08:00 [ERR] 未处理的异常::System.MissingMethodException: Attempted to invoke a deleted method implementation. This can happen when a method is deleted or its name or signature is changed while the application is running.
   at XH.H82.Models.BusinessModuleClient.H04.H04Client.GetFieldDicts(String filedClass)
   at XH.H82.Services.TemplateDesign.TemplateDesignService.GetFieldDicts(String filedClass) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\TemplateDesign\TemplateDesignService.cs:line 152
   at Castle.Proxies.Invocations.ITemplateDesignService_GetFieldDicts.InvokeMethodOnTarget()
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.UseCacheWhenFailedAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.CacheAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at Castle.Proxies.ITemplateDesignServiceProxy.GetFieldDicts(String filedClass)
   at XH.H82.API.Controllers.EquipmentClassNew.EquipmentClassNewController.GetGetFieldDicts(String filedClass) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\Controllers\EquipmentClassNew\EquipmentClassNewController.cs:line 161
   at lambda_method918(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-07-14 21:04:56.211 +08:00 [ERR] HTTP GET /api/EquipmentClassNew/GetGetFieldDicts responded 500 in 10972.6804 ms
2025-07-14 21:06:57.285 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-14 21:08:11.395 +08:00 [ERR] 未处理的异常::System.Text.Json.JsonException: The JSON value could not be converted to System.Nullable`1[System.DateTime]. Path: $.data[0].FIRST_RTIME | LineNumber: 0 | BytePositionInLine: 1292.
 ---> System.FormatException: The JSON value is not in a supported DateTime format.
   at System.Text.Json.ThrowHelper.ThrowFormatException(DataType dataType)
   at System.Text.Json.Utf8JsonReader.GetDateTime()
   at System.Text.Json.Serialization.Converters.DateTimeConverter.Read(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options)
   at System.Text.Json.Serialization.Converters.NullableConverter`1.Read(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options)
   at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonCollectionConverter`2.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, TCollection& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, Utf8JsonReader& reader, Exception ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.Deserialize(Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at RestSharp.Serializers.Json.SystemTextJsonSerializer.Deserialize[T](RestResponse response)
   at RestSharp.Serializers.RestSerializers.DeserializeContent[T](RestResponse response)
   at RestSharp.Serializers.RestSerializers.Deserialize[T](RestRequest request, RestResponse raw, ReadOnlyRestClientOptions options, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.ExecuteAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.AsyncHelpers.<>c__DisplayClass1_0`1.<<RunSync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.<Run>g__PostCallback|7_0(Object _)
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.Run()
   at RestSharp.AsyncHelpers.RunSync(Func`1 task)
   at RestSharp.AsyncHelpers.RunSync[T](Func`1 task)
   at RestSharp.RestClientExtensions.ExecuteGet[T](IRestClient client, RestRequest request)
   at XH.H82.Models.BusinessModuleClient.H04.H04Client.H04Request[T](String url, Method method, Object body)
   at XH.H82.Models.BusinessModuleClient.H04.H04Client.GetFieldDicts(String filedClass)
   at XH.H82.Services.TemplateDesign.TemplateDesignService.GetFieldDicts(String filedClass) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\TemplateDesign\TemplateDesignService.cs:line 152
   at Castle.Proxies.Invocations.ITemplateDesignService_GetFieldDicts.InvokeMethodOnTarget()
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.UseCacheWhenFailedAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.CacheAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at Castle.Proxies.ITemplateDesignServiceProxy.GetFieldDicts(String filedClass)
   at XH.H82.API.Controllers.EquipmentClassNew.EquipmentClassNewController.GetGetFieldDicts(String filedClass) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\Controllers\EquipmentClassNew\EquipmentClassNewController.cs:line 161
   at lambda_method918(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)System.FormatException: The JSON value is not in a supported DateTime format.
   at System.Text.Json.ThrowHelper.ThrowFormatException(DataType dataType)
   at System.Text.Json.Utf8JsonReader.GetDateTime()
   at System.Text.Json.Serialization.Converters.DateTimeConverter.Read(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options)
   at System.Text.Json.Serialization.Converters.NullableConverter`1.Read(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options)
   at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonCollectionConverter`2.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, TCollection& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
2025-07-14 21:08:11.404 +08:00 [ERR] HTTP GET /api/EquipmentClassNew/GetGetFieldDicts responded 500 in 28885.9557 ms
2025-07-14 21:11:04.450 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-14 21:12:18.879 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-14 21:13:16.179 +08:00 [ERR] 调用H04模块[/externalapi/External/GetComplexForms?hospitalId=33A001&moduleId=H82&setupId=H81_TG000004X&merge=1&qunitID=]请求完成,但是返回了错误:SYS6_MODULE_FUNC_DICT表中未找到设置项，或者FORM_COL_JSON字段为空
2025-07-14 21:13:23.815 +08:00 [ERR] 未处理的异常::System.Exception: 调用H04模块[/externalapi/External/GetComplexForms?hospitalId=33A001&moduleId=H82&setupId=H81_TG000004X&merge=1&qunitID=]请求完成,但是返回了错误:SYS6_MODULE_FUNC_DICT表中未找到设置项，或者FORM_COL_JSON字段为空
   at XH.H82.Models.BusinessModuleClient.H04.H04Client.H04Request[T](String url, Method method, Object body) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Models\BusinessModuleClient\H04\H04Client.cs:line 86
   at XH.H82.Models.BusinessModuleClient.H04.H04Client.GetComplexForms(String setUpId, String merge, String qunitId) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Models\BusinessModuleClient\H04\H04Client.cs:line 146
   at XH.H82.Services.TemplateDesign.TemplateDesignService.GetComplexForms(String setUpId, String merge, String qunitId) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\TemplateDesign\TemplateDesignService.cs:line 145
   at Castle.Proxies.Invocations.ITemplateDesignService_GetComplexForms.InvokeMethodOnTarget()
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.UseCacheWhenFailedAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.CacheAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at Castle.Proxies.ITemplateDesignServiceProxy.GetComplexForms(String setUpId, String merge, String qunitId)
   at XH.H82.API.Controllers.EquipmentClassNew.EquipmentClassNewController.GetComplexForms(String classId, String merge, String qunitId) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\Controllers\EquipmentClassNew\EquipmentClassNewController.cs:line 193
   at lambda_method918(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-07-14 21:13:23.823 +08:00 [ERR] HTTP POST /api/EquipmentClassNew/GetComplexForms/H81_TG000004X responded 500 in 11272.9932 ms
2025-07-14 21:13:42.693 +08:00 [ERR] 调用H04模块[/externalapi/External/GetComplexForms?hospitalId=33A001&moduleId=H82&setupId=H81_TG000004X&merge=1&qunitID=]请求完成,但是返回了错误:SYS6_MODULE_FUNC_DICT表中未找到设置项，或者FORM_COL_JSON字段为空
2025-07-14 21:13:52.589 +08:00 [ERR] 未处理的异常::System.Exception: 调用H04模块[/externalapi/External/GetComplexForms?hospitalId=33A001&moduleId=H82&setupId=H81_TG000004X&merge=1&qunitID=]请求完成,但是返回了错误:SYS6_MODULE_FUNC_DICT表中未找到设置项，或者FORM_COL_JSON字段为空
   at XH.H82.Models.BusinessModuleClient.H04.H04Client.H04Request[T](String url, Method method, Object body) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Models\BusinessModuleClient\H04\H04Client.cs:line 86
   at XH.H82.Models.BusinessModuleClient.H04.H04Client.GetComplexForms(String setUpId, String merge, String qunitId) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Models\BusinessModuleClient\H04\H04Client.cs:line 146
   at XH.H82.Services.TemplateDesign.TemplateDesignService.GetComplexForms(String setUpId, String merge, String qunitId) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\TemplateDesign\TemplateDesignService.cs:line 145
   at Castle.Proxies.Invocations.ITemplateDesignService_GetComplexForms.InvokeMethodOnTarget()
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.UseCacheWhenFailedAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.CacheAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at Castle.Proxies.ITemplateDesignServiceProxy.GetComplexForms(String setUpId, String merge, String qunitId)
   at XH.H82.API.Controllers.EquipmentClassNew.EquipmentClassNewController.GetComplexForms(String classId, String merge, String qunitId) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\Controllers\EquipmentClassNew\EquipmentClassNewController.cs:line 193
   at lambda_method918(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-07-14 21:13:52.592 +08:00 [ERR] HTTP POST /api/EquipmentClassNew/GetComplexForms/H81_TG000004X responded 500 in 10120.5331 ms
2025-07-14 21:14:33.852 +08:00 [ERR] 未处理的异常::System.Text.Json.JsonException: The JSON value could not be converted to System.String. Path: $.data.formColJson.form[0].contentHeightClass | LineNumber: 0 | BytePositionInLine: 636.
 ---> System.InvalidOperationException: Cannot get the value of a token type 'Number' as a string.
   at System.Text.Json.ThrowHelper.ThrowInvalidOperationException_ExpectedString(JsonTokenType tokenType)
   at System.Text.Json.Utf8JsonReader.GetString()
   at System.Text.Json.Serialization.Converters.StringConverter.Read(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options)
   at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonCollectionConverter`2.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, TCollection& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, Utf8JsonReader& reader, Exception ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.Deserialize(Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at RestSharp.Serializers.Json.SystemTextJsonSerializer.Deserialize[T](RestResponse response)
   at RestSharp.Serializers.RestSerializers.DeserializeContent[T](RestResponse response)
   at RestSharp.Serializers.RestSerializers.Deserialize[T](RestRequest request, RestResponse raw, ReadOnlyRestClientOptions options, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.ExecuteAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.AsyncHelpers.<>c__DisplayClass1_0`1.<<RunSync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.<Run>g__PostCallback|7_0(Object _)
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.Run()
   at RestSharp.AsyncHelpers.RunSync(Func`1 task)
   at RestSharp.AsyncHelpers.RunSync[T](Func`1 task)
   at RestSharp.RestClientExtensions.ExecuteGet[T](IRestClient client, RestRequest request)
   at XH.H82.Models.BusinessModuleClient.H04.H04Client.H04Request[T](String url, Method method, Object body) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Models\BusinessModuleClient\H04\H04Client.cs:line 66
   at XH.H82.Models.BusinessModuleClient.H04.H04Client.GetComplexForms(String setUpId, String merge, String qunitId) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Models\BusinessModuleClient\H04\H04Client.cs:line 146
   at XH.H82.Services.TemplateDesign.TemplateDesignService.GetComplexForms(String setUpId, String merge, String qunitId) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\TemplateDesign\TemplateDesignService.cs:line 145
   at Castle.Proxies.Invocations.ITemplateDesignService_GetComplexForms.InvokeMethodOnTarget()
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.UseCacheWhenFailedAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.CacheAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at Castle.Proxies.ITemplateDesignServiceProxy.GetComplexForms(String setUpId, String merge, String qunitId)
   at XH.H82.API.Controllers.EquipmentClassNew.EquipmentClassNewController.GetComplexForms(String classId, String merge, String qunitId) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\Controllers\EquipmentClassNew\EquipmentClassNewController.cs:line 193
   at lambda_method918(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)System.InvalidOperationException: Cannot get the value of a token type 'Number' as a string.
   at System.Text.Json.ThrowHelper.ThrowInvalidOperationException_ExpectedString(JsonTokenType tokenType)
   at System.Text.Json.Utf8JsonReader.GetString()
   at System.Text.Json.Serialization.Converters.StringConverter.Read(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options)
   at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonCollectionConverter`2.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, TCollection& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
2025-07-14 21:14:33.859 +08:00 [ERR] HTTP POST /api/EquipmentClassNew/GetComplexForms/H81_TG000004X responded 500 in 39812.1488 ms
2025-07-14 21:15:33.860 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-14 21:16:56.066 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-14 21:20:00.082 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-14 21:25:48.048 +08:00 [ERR] 未处理的异常::System.Net.Http.HttpRequestException: Request failed with status code Unauthorized
   at RestSharp.ResponseThrowExtension.ThrowIfError(RestResponse response)
   at RestSharp.RestClient.ExecuteAsync(RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.ExecuteAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.AsyncHelpers.<>c__DisplayClass1_0`1.<<RunSync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.<Run>g__PostCallback|7_0(Object _)
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.Run()
   at RestSharp.AsyncHelpers.RunSync(Func`1 task)
   at RestSharp.AsyncHelpers.RunSync[T](Func`1 task)
   at RestSharp.RestClientExtensions.ExecuteGet[T](IRestClient client, RestRequest request)
   at XH.H82.Models.BusinessModuleClient.H04.H04Client.H04Request[T](String url, Method method, Object body)
   at XH.H82.Models.BusinessModuleClient.H04.H04Client.GetComplexForms(String setUpId, String merge, String qunitId)
   at XH.H82.Services.TemplateDesign.TemplateDesignService.GetComplexForms(String setUpId, String merge, String qunitId) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\TemplateDesign\TemplateDesignService.cs:line 145
   at Castle.Proxies.Invocations.ITemplateDesignService_GetComplexForms.InvokeMethodOnTarget()
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.UseCacheWhenFailedAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.CacheAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at Castle.Proxies.ITemplateDesignServiceProxy.GetComplexForms(String setUpId, String merge, String qunitId)
   at XH.H82.API.Controllers.EquipmentClassNew.EquipmentClassNewController.GetComplexForms(String classId, String merge, String qunitId) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\Controllers\EquipmentClassNew\EquipmentClassNewController.cs:line 193
   at lambda_method918(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-07-14 21:25:48.053 +08:00 [ERR] HTTP GET /api/EquipmentClassNew/GetComplexForms/H81_TG000004X responded 500 in 12804.0369 ms
2025-07-14 21:29:20.647 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-14 21:29:46.024 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-14 21:41:07.675 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
