﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using H.Utility.SqlSugarInfra;
using SqlSugar;

namespace XH.H82.Models.Entities.Common
{
    [DBOwner("XH_SYS")]
    [SugarTable("SYS6_HOSPITAL_INFO")]
    public class SYS6_HOSPITAL_INFO
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string? HOSPITAL_ID { get; set; }
        public string? HOSPITAL_CNAME { get; set; }
        public string? HOSPITAL_STATE { get; set; }
    }
}
