﻿using System.ComponentModel;

namespace XH.H82.Models.Enums;

/// <summary>
/// 供应商服务范围
/// </summary>
public enum ServiceAreaEnum
{
    /// <summary>
    ///设备
    /// </summary>
    [Description("设备")]
    EQUIPMENT = 0,
    /// <summary>
    ///物资
    /// </summary>
    [Description("物资")]
    MATERIAL = 1,
    /// <summary>
    ///设备+物资
    /// </summary>
    [Description("设备+物资")]
    EQUIP_MAT = 2,
    /// <summary>
    ///信息系统
    /// </summary>
    [Description("信息系统")]
    INFO_SYS = 3,
    /// <summary>
    ///检测服务
    /// </summary>
    [Description("检测服务")]
    CHECK = 4,
    /// <summary>
    ///维保服务
    /// </summary>
    [Description("维保服务")]
    MAINTENANCE = 5,
}


/// <summary>
/// 院内供应商服务范围
/// </summary>
public enum HospitalInternalServiceAreaEnum
{
    /// <summary>
    ///清洁
    /// </summary>
    [Description("清洁服务")]
    CLEAN = 11,

    /// <summary>
    ///信息服务
    /// </summary>
    [Description( "信息服务")]
    EQUIP_INFORMATION = 12,

    /// <summary>
    ///设备管理服务
    /// </summary>
    [Description( "设备管理服务")]
    EQUIP_MANAGE = 13,

    /// <summary>
    ///设备维修服务
    /// </summary>
    [Description( "设备维修服务")]
    EQUIP_MAINT = 14,

    /// <summary>
    ///电脑管理服务
    /// </summary>
    [Description( "电脑管理服务")]
    COMPUTER_MANAGE = 15,

    /// <summary>
    ///消毒和供应服务
    /// </summary>
    [Description( "消毒和供应服务")]
    DISINFECT = 16,

    /// <summary>
    ///工程服务
    /// </summary>
    [Description( "工程服务")]
    ENGINEERING= 17,

    /// <summary>
    ///运送服务
    /// </summary>
    [Description( "运送服务")]
    TRANSPORT = 18,

    /// <summary>
    ///工人后勤保障服务
    /// </summary>
    [Description( "工人后勤保障服务")]
    GUARANTEE = 19,

}