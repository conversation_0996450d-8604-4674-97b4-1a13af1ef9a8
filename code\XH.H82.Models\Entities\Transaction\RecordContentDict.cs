﻿using H.Utility;
using XH.H82.Models.BusinessModuleClient;

namespace XH.H82.Models.Entities.Transaction
{
    /// <summary>
    /// 设备记录内容字典
    /// </summary>
    public class RecordContentDict
    {
        public static string RECORD_CONTENT_DICT = "RecordContentDict";
        public static string RECORD_AUTHORIZATION_DICT = "AuthorizationDict";
        const string ModeuleId = "H82";
        public static OA_BASE_DATA CreateRecordDict(string hospitalId, string userName, string classId, string content, string? remark)
        {
            var id = IDGenHelper.CreateGuid();
            return new OA_BASE_DATA(id, hospitalId, ModeuleId, RECORD_CONTENT_DICT, classId, null, content, null, null, null, null, null, "1", userName, DateTime.Now, userName, DateTime.Now, remark);
        }


        public static OA_BASE_DATA CreateAuthorizationDict(string hospitalId, string userName, string classId,
            string content, string? remark)
        {
            var id = IDGenHelper.CreateGuid();
            return new OA_BASE_DATA(id, hospitalId, ModeuleId, RECORD_AUTHORIZATION_DICT, classId, null, content, null, null, null, null, null, "1", userName, DateTime.Now, userName, DateTime.Now, remark);
        }


    }




}
