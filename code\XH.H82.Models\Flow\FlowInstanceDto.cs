﻿using Newtonsoft.Json;

namespace XH.H82.Models.Flow;

public class FlowInstanceDto
{
    [JsonProperty("flowInstanceId")]
    public string FlowInstanceId { get; set; }

    [JsonProperty("flowId")]
    public string FlowId { get; set; }

    [JsonProperty("flowName")]
    public string FlowName { get; set; }

    [JsonProperty("flowVer")]
    public string FlowVer { get; set; }

    [JsonProperty("instanceState")]
    public string InstanceState { get; set; }

    [JsonProperty("hospitalId")]
    public string HospitalId { get; set; }

    [JsonProperty("pgroupId")]
    public string PgroupId { get; set; }

    [JsonProperty("labId")]
    public string LabId { get; set; }

    [JsonProperty("createPerson")]
    public string  CreatePerson { get; set; }

    [JsonProperty("userId")]
    public string UserId { get; set; }

    [JsonProperty("createTime",  NullValueHandling = NullValueHandling.Ignore)]
    public string? CreateTime { get; set; }

    [JsonProperty("dataList")]
    public List<object> DataList { get; set; }

    [Json<PERSON>roperty("currentNodeId")]
    public string CurrentNodeId { get; set; }

    [JsonProperty("currentNodeName")]
    public string CurrentNodeName { get; set; }

    [JsonProperty("nodePrincipal")]
    public string NodePrincipal { get; set; }

    [JsonProperty("nodeState")]
    public string NodeState { get; set; }

    [JsonProperty("rejectInfo")]
    public string RejectInfo { get; set; }

    [JsonProperty("nodeRemark")]
    public string NodeRemark { get; set; }

    [JsonProperty("nodeStartTime",  NullValueHandling = NullValueHandling.Ignore)]
    public string? NodeStartTime { get; set; }

    [JsonProperty("nodeProcessTime",  NullValueHandling = NullValueHandling.Ignore)]
    public string? NodeProcessTime { get; set; }

    [JsonProperty("nodeRealPerson")]
    public string NodeRealPerson { get; set; }

    [JsonProperty("needChoseApprover")]
    public bool NeedChoseApprover { get; set; }

    [JsonProperty("flowInstanceNodes")]
    public List<FlowInstanceNodeDto> FlowInstanceNodes { get; set; }
    
}