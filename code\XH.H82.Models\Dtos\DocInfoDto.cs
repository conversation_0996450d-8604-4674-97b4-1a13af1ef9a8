﻿using AutoMapper;
using AutoMapper.Configuration.Annotations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion.Internal;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XH.H82.Models.Dtos.SysDocDtos;

namespace XH.H82.Models.Dtos
{
    [AutoMap(typeof(FileSystemFileInfo))]
    public class DocInfoDto
    {
        /// <summary>
        /// 文件主键
        /// </summary>
        [SourceMember("FileId")]
        public string FILE_ID { get; set; }
        /// <summary>
        /// 文档id
        /// </summary>
        [SourceMember("DocId")]
        public string DOC_ID { get; set; }
        /// <summary>
        /// 专业组名称
        /// </summary>
        [SourceMember("PgroupName")]
        public string PGROUP_NAME { get; set; }
        /// <summary>
        /// 专业组id
        /// </summary>
        [SourceMember("PgroupId")]

        public string PGROUP_ID { get; set; }
        /// <summary>
        /// 文档名称
        /// </summary>
        /// 
        [SourceMember("DocName")]

        public string DOC_NAME { get; set; }
        /// <summary>
        /// 文档类型
        /// </summary>
        /// 
        [SourceMember("DocType")]
        public string DOC_TYPE { get; set; }
        /// <summary>
        /// 文档编号
        /// </summary>
        /// 
        [SourceMember("DocCode")]
        public string DOC_CODE { get; set; }
        /// <summary>
        /// 版本号
        /// </summary>
        /// 
        [SourceMember("DocVersion")]

        public string DOC_VERSION { get; set; }
        /// <summary>
        /// 关键词
        /// </summary>
        /// 
        public string DOC_KEYWORD { get; set; }
        /// <summary>
        /// 是否选中（0：未选；1：已选）
        /// </summary>
        public string IF_SELECT { get; set; }

        public string FilePreviewNginx { get; set; }
        public string FilePath { get; set; }
    }
}
