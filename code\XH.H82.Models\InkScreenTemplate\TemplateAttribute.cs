﻿using H.Utility;
using Newtonsoft.Json;
using NPOI.OpenXmlFormats.Wordprocessing;
using NPOI.SS.Formula.Functions;
using System.Collections.Generic;
using XH.H82.Models.Card;
using XH.H82.Models.InkScreenTemplate.Dto;

namespace XH.H82.Models.InkScreenTemplate
{
    public class TemplateAttribute
    {
        public string colTitle { get; set; }
        public string colKey { get; set; }
        public TemplateAttribute(string colTitle, string colKey)
        {
            this.colTitle = colTitle;
            this.colKey = colKey;
        }
    };
    public record Template(int rowIndex, List<TemplateAttribute> cols);


    public class TemplateAContentInit
    {
        public static List<Template> Init(int row = 8, int col = 4)
        {
            var result = new List<Template>();

            for (int i = 1; i <= row; i++)
            {
                var templates = new List<TemplateAttribute>();

                if (i == 1)
                {
                    templates.Add(new("标题", "templateTitle"));
                    result.Add(new(i, templates));
                    continue;
                }

                if (i == 2)
                {
                    templates.Add(new("设备名称", "equipmentName"));
                    result.Add(new(i, templates));
                    continue;
                }

                if (i < 4)
                {
                    templates.Add(new("", ""));
                }
                else
                {
                    for (int j = 1; j <= col; j++)
                    {
                        if (j % 2 != 0)
                        {
                            templates.Add(new("", ""));
                        }
                    }
                }

                result.Add(new(i, templates));
            }

            return result;
        }

        public static List<Template> Default()
        {
            var result = Init(8, 4);
            var templateAttributes = new List<TemplateAttribute>();
            var idCard = new IdentificationCard();
            var attributes = idCard.ToAttributes();
            foreach (var attr in attributes)
            {
                if (attr.Value.IsNotNullOrEmpty())
                {
                    templateAttributes.Add(new(attr.Value, attr.Key));
                }
            }

            int number = 2;
            for (var i = 0; i < 8; i++)
            {
                if (i == 0)
                {
                    result[i].cols[0].colTitle = "标题";
                    result[i].cols[0].colKey = "templateTitle";
                    continue;
                }

                if (i == 1)
                {
                    result[i].cols[0].colTitle = "设备名称";
                    result[i].cols[0].colKey = "equipmentName";
                    continue;
                }

                for (var j = 0; j < 2; j++)
                {
                    if (i < 3)
                    {
                        if (j < 1)
                        {
                            result[i].cols[0].colTitle = templateAttributes[number].colTitle;
                            result[i].cols[0].colKey = templateAttributes[number].colKey;
                            number += 1;
                        }
                    }
                    else
                    {
                        result[i].cols[j].colTitle = templateAttributes[number].colTitle;
                        result[i].cols[j].colKey = templateAttributes[number].colKey;
                        number += 1;
                    }
                }
            }

            return result;
        }
    }
}
