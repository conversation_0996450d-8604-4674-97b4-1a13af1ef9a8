﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="EPPlus" Version="6.2.6" />
    <PackageReference Include="NuGet.Protocol" Version="6.6.1" />
    <PackageReference Include="SkiaSharp.NativeAssets.Linux" Version="2.88.6" />

    <PackageReference Include="System.ServiceModel.Duplex" Version="4.8.*" />
    <PackageReference Include="System.ServiceModel.Federation" Version="4.8.*" />
    <PackageReference Include="System.ServiceModel.Http" Version="4.10.0" />
    <PackageReference Include="System.ServiceModel.NetTcp" Version="4.8.*" />
    <PackageReference Include="System.ServiceModel.Security" Version="4.8.*" />
    <PackageReference Include="System.Xml.XmlSerializer" Version="4.3.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\XH.H82.Base\XH.H82.Base.csproj" />
    <ProjectReference Include="..\XH.H82.IServices\XH.H82.IServices.csproj" />
  </ItemGroup>

</Project>
