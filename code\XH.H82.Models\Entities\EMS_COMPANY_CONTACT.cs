﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.Entities
{
    [<PERSON>Owner("XH_OA")]
    public class EMS_COMPANY_CONTACT
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string CONTACT_ID { get; set; }
        public string HOSPITAL_ID { get; set; }
        public string COMPANY_ID { get; set; }
        public string PERSON_TYPE { get; set; }
        public string PERSON_NAME { get; set; }
        public string CONTACT_WAY { get; set; }
        public string CONTACT_MOBILE { get; set; }
        public string WEIXIN { get; set; }
        public string E_MAIL { get; set; }
        public string CONTACT_STATE { get; set; }
        public string FIRST_RPERSON { get; set; }
        public DateTime? FIRST_RTIME { get; set; }
        public string LAST_MPERSON { get; set; }
        public DateTime? LAST_MTIME { get; set; }
        public string REMARK { get; set; }
    }
}
