﻿using System;
using H.BASE.Encrypt;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using XH.H82.Models.Entities;
using XH.H82.Models.Entities.Common;
using XH.LAB.UTILS.H05Message;
using XH.LAB.UTILS.Models;

namespace XH.H82.Models
{
    public partial class MainDBContext : DbContext
    {
        public MainDBContext()
        {

        }

        public MainDBContext(DbContextOptions<MainDBContext> options)
            : base(options)
        {
           
        }
        public virtual DbSet<EMS_SUBSCRIBE_INFO> EMS_SUBSCRIBE_INFO { get; set; }
        public virtual DbSet<EMS_PURCHASE_INFO> EMS_PURCHASE_INFO { get; set; }
        public virtual DbSet<EMS_UNPACK_INFO> EMS_UNPACK_INFO { get; set; }
        public virtual DbSet<EMS_INSTALL_INFO> EMS_INSTALL_INFO { get; set; }
        public virtual DbSet<EMS_DEBUG_INFO> EMS_DEBUG_INFO { get; set; }
        public virtual DbSet<EMS_TRAIN_INFO> EMS_TRAIN_INFO { get; set; }
        public virtual DbSet<EMS_AUTHORIZE_INFO> EMS_AUTHORIZE_INFO { get; set; }
        public virtual DbSet<EMS_STARTUP_INFO> EMS_STARTUP_INFO { get; set; }
        public virtual DbSet<EMS_EQUIPMENT_INFO> EMS_EQUIPMENT_INFO { get; set; }
        public virtual DbSet<EMS_ENVI_REQUIRE_INFO> EMS_ENVI_REQUIRE_INFO { get; set; }
        public virtual DbSet<EMS_PARTS_INFO> EMS_PARTS_INFO { get; set; }
        public virtual DbSet<EMS_COMPANY_CONTACT> EMS_COMPANY_CONTACT { get; set; }
        public virtual DbSet<SYS6_SETUP_DICT> SYS_SETUP_DICT { get; set; }
        public virtual DbSet<EMS_START_STOP> EMS_START_STOP { get; set; }
        //public virtual DbSet<SYS_USER> SYS_USER { get; set; }
        public virtual DbSet<SYS6_COMPANY_CONTACT> SYS6_COMPANY_CONTACT { get; set; }
        public virtual DbSet<EMS_MAINTAIN_INFO> EMS_MAINTAIN_INFO { get; set; }
        public virtual DbSet<EMS_REPAIR_INFO> EMS_REPAIR_INFO { get; set; }
        public virtual DbSet<EMS_COMPARISON_INFO> EMS_COMPARISON_INFO { get; set; }
        public virtual DbSet<EMS_CORRECT_INFO> EMS_CORRECT_INFO { get; set; }
        public virtual DbSet<EMS_VERIFICATION_INFO> EMS_VERIFICATION_INFO { get; set; }
        public virtual DbSet<EMS_CHANGE_INFO> EMS_CHANGE_INFO { get; set; }
        public virtual DbSet<EMS_SCRAP_INFO> EMS_SCRAP_INFO { get; set; }
        public virtual DbSet<EMS_DOC_INFO> EMS_DOC_INFO { get; set; } 
        public virtual DbSet<EMS_SCRAP_LOG> EMS_SCRAP_LOG { get; set; }
        public virtual DbSet<SYS6_COMPANY_INFO> SYS6_COMPANY_INFO { get; set; }
        public virtual DbSet<EMS_EQUIPMENT_CONTACT> EMS_EQUIPMENT_CONTACT { get; set; }
        public virtual DbSet<DMIS_SYS_DOC> DMIS_SYS_DOC { get;set; }
        public virtual DbSet<DMIS_SYS_FILE> DMIS_SYS_FILE { get; set; }
       /// <summary>
       /// 不良事件主表
       /// </summary>
        public virtual DbSet<AER_ADVERSE_EVENT> AER_ADVERSE_EVENT { get; set; } 
        /// <summary>
        /// 不良事件主体
        /// </summary>
        public virtual DbSet<AER_EVENT_SUBJECT> AER_EVENT_SUBJECT { get; set; }



        /// <summary>
        /// 人员基本信息
        /// </summary>
        public virtual DbSet<SYS6_USER> SYS6_USER { get; set; }
        public virtual DbSet<SYS6_USER_PGROUP> SYS6_USER_PGROUP { get; set; }

        public virtual DbSet<SYS6_INSPECTION_LAB> SYS6_INSPECTION_LAB { get; set; }


        public virtual DbSet<SYS6_SOFT_MODULE_INFO> SYS6_SOFT_MODULE_INFO { get; set; }
        public virtual DbSet<SYS6_MENU> SYS6_MENU { get; set; }
        public virtual DbSet<SYS6_SERVER_INFO> SYS6_SERVER_INFO { get; set; }

        /// <summary>
        /// 固定基础数据
        /// </summary>
        public virtual DbSet<SYS6_BASE_DATA> SYS6_BASE_DATA { get; set; }
        public virtual DbSet<SYS6_POST_ROLE_COM> SYS6_POST_ROLE_COM { get; set; }
        public virtual DbSet<SYS6_ROLE_COM_LIST> SYS6_ROLE_COM_LIST { get; set; }
        public virtual DbSet<SYS6_ROLE_MENU> SYS6_ROLE_MENU { get; set; }
        public virtual DbSet<LIS6_INSPECTION_GROUP> LIS6_INSPECTION_GROUP { get; set; }



        /// <summary>
        /// 科室信息
        /// </summary>
        public virtual DbSet<SYS6_INSPECTION_LAB> LIS_INSPECTION_LAB { get; set; }
        public virtual DbSet<SYS6_POST> SYS6_POST { get; set; }
        public virtual DbSet<SYS6_USER_POST> SYS6_USER_POST { get; set; }

        //protected override void OnModelCreating(ModelBuilder modelBuilder)
        //{

        //    modelBuilder.Entity<SYS6_BASE_DATA>(entity =>
        //    {
        //        entity.HasKey(c => new { c.DATA_ID, c.CLASS_ID });
        //    });
        //    modelBuilder.Entity<SYS6_ROLE_COM_LIST>(entity =>
        //    {
        //        entity.HasKey(c => new { c.ROLECOM_ID, c.ROLE_ID ,c.MODULE_ID});
        //    });
        //}

        //protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        //{
        //    base.OnConfiguring(optionsBuilder);
        //    optionsBuilder.EnableSensitiveDataLogging();
        //    optionsBuilder.EnableDetailedErrors();
        //}
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            //敏感信息自动加解密中间件
            modelBuilder.UseHEncryption();
            //全局设置为unicode,todo:存疑 待讨论
            foreach (var property in modelBuilder.Model.GetEntityTypes().SelectMany(e => e.GetProperties().Where(p => p.ClrType == typeof(string))))
            {
                property.SetIsUnicode(false);
            } 
            modelBuilder.Entity<SYS6_BASE_DATA>(entity =>
            {
                entity.HasKey(c => new { c.DATA_ID, c.CLASS_ID });
            });
            modelBuilder.Entity<SYS6_ROLE_COM_LIST>(entity =>
            {
                entity.HasKey(c => new { c.ROLECOM_ID, c.ROLE_ID, c.MODULE_ID });
            });
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {

            base.OnConfiguring(optionsBuilder);
            optionsBuilder.EnableSensitiveDataLogging();
            optionsBuilder.EnableDetailedErrors();


        }



        /// <summary>
        /// 专业组信息
        /// </summary>
        public virtual DbSet<SYS6_INSPECTION_PGROUP> SYS6_INSPECTION_PGROUP { get; set; }
        public virtual DbSet<SYS6_INSPECTION_MGROUP> SYS6_INSPECTION_MGROUP { get; set; }

 

        /// <summary>
        /// 系统菜单设置字典表
        /// </summary>
        public virtual DbSet<SYS6_MENU_INFO_DICT> SYS_MENU_INFO_DICT { get; set; }

        /// <summary>
        /// 列表表头配置
        /// </summary>
        //public virtual DbSet<LIS5_RESULT_INFO_DICT> LIS5_RESULT_INFO_DICT { get; set; }



        /// <summary>
        ///输入信息设置字典表
        /// </summary>
        public virtual DbSet<LIS5_SAMPLE_INFO_DICT> LIS5_SAMPLE_INFO_DICT { get; set; }


        public virtual DbSet<LIS6_INSTRUMENT_INFO> LIS6_INSTRUMENT_INFO { get; set; }
        public virtual DbSet<LIS6_INSTRUMENT_ITEM> LIS6_INSTRUMENT_ITEM { get; set; }
        public virtual DbSet<LIS6_INSTRUMENT_ITEM_REAGENT> LIS6_INSTRUMENT_ITEM_REAGENT { get; set; } 
        public virtual DbSet<SYS6_MATERIAL_INFO> SYS6_MATERIAL_INFO { get; set; }
    }
}
