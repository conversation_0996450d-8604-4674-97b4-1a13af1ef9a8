﻿using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Npoi.Mapper;
using SqlSugar;
using XH.H82.IServices;
using XH.H82.Models;
using XH.H82.Models.Dtos;
using XH.H82.Models.Dtos.Tim;
using XH.H82.Models.Entities;
using XH.H82.Models.Entities.Common;
using XH.H82.Models.Entities.Tim;
using XH.H82.Models.Entities.Transaction;
using XH.H82.Models.SugarDbContext;
using XH.LAB.UTILS.Models;

namespace XH.H82.Services
{
    public class OperationRecordService : IOperationRecordService
    {
        private readonly IBaseService _baseService;
        private readonly ISqlSugarUow<SugarDbContext_Master> _dbContext;
        private readonly ILogger<OperationRecordService> _logger;
        private readonly IHttpContextAccessor _httpContext;
        
        public OperationRecordService(IBaseService baseService, ISqlSugarUow<SugarDbContext_Master> dbContext, IHttpContextAccessor httpContext)
        {
            _baseService = baseService;
            _dbContext = dbContext;
            _httpContext = httpContext;
            _dbContext.SetCreateTimeAndCreatePersonData(_httpContext.HttpContext.User.ToClaimsDto());
            //ExecutingChangeSqlHelper.ExecutingChangeSql(_sqlSugarUow);
        }
        public List<WorkSequentialDto> GetWorkSequentialList(string equipmentId, string year)
        {
            //设备保养
            var maintainList = _dbContext.Db.Queryable<EMS_MAINTAIN_INFO>()
                .Where(p => p.EQUIPMENT_ID == equipmentId && p.MAINTAIN_STATE == "1")
                .ToList();
            //设备维修
            var repairList = _dbContext.Db.Queryable<EMS_REPAIR_INFO>()
                .Where(p => p.EQUIPMENT_ID == equipmentId && p.REPAIR_STATE == "1")
                .ToList();
            //设备校准
            var correctList = _dbContext.Db.Queryable<EMS_CORRECT_INFO>()
                .Where(p => p.EQUIPMENT_ID == equipmentId && p.CORRECT_STATE == "1")
                .ToList();
            //设备比对
            var comparisonList = _dbContext.Db.Queryable<EMS_COMPARISON_INFO>()
                .Where(p => p.EQUIPMENT_ID == equipmentId && p.COMPARISON_STATE == "1")
                .ToList();
            //设备性能验证
            var verificationList = _dbContext.Db.Queryable<EMS_VERIFICATION_INFO>()
                .Where(p => p.EQUIPMENT_ID == equipmentId && p.VERIFICATION_STATE == "1")
                .ToList();
            var docList = _dbContext.Db.Queryable<EMS_DOC_INFO>()
                .Where(p => p.DOC_STATE == "1").ToList();
            //合并时间数组
            List<DateTime?> q = new List<DateTime?>();
            q.AddRange(maintainList.Select(i => i.MAINTAIN_DATE));
            q.AddRange(repairList.Select(i => i.REPAIR_DATE));
            q.AddRange(correctList.Select(i => i.CORRECT_DATE));
            q.AddRange(comparisonList.Select(i => i.COMPARISON_DATE));
            q.AddRange(verificationList.Select(i => i.VERIFICATION_DATE));
            //非空处理
            var a = q.Where(p => p.ToString().IsNotNullOrEmpty()).ToArray();
            string[] b = new string[a.Length];
            //修改格式保留“年月日”
            for (int i = 0; i < a.Length; i++)
            {
                b[i] = Convert.ToDateTime(a[i]).ToString("yyyy-MM-dd");
            }
            //去重倒序排列
            var arrWorkTime = b.GroupBy(p => p).Select(p => p.Key).OrderByDescending(p => p).ToArray();
            var workTimeDto = new List<WorkSequentialDto>();
            for (int i = 0; i < arrWorkTime.Length; i++)
            {
                workTimeDto.Add(new WorkSequentialDto()
                {
                    WORK_DATE = Convert.ToDateTime(arrWorkTime[i]),
                });
                var secondNode = new List<SECOND_SEQUENTIAL_NODE>();

                maintainList.ForEach(item =>
                {
                    string relatedEvent = ",";
                    if (item.OCCUR_EVENT != null)
                    {
                        if (item.OCCUR_EVENT.Contains("校准"))
                        {
                            relatedEvent += correctList.Where(p => p.RELATION_EVENT == item.MAINTAIN_NO).FirstOrDefault() != null
                            ? correctList.Where(p => p.RELATION_EVENT == item.MAINTAIN_NO).FirstOrDefault().CORRECT_NO + "," : null;
                        }
                        if (item.OCCUR_EVENT.Contains("比对"))
                        {
                            relatedEvent += comparisonList.Where(p => p.RELATION_EVENT == item.MAINTAIN_NO).FirstOrDefault() != null
                            ? comparisonList.Where(p => p.RELATION_EVENT == item.MAINTAIN_NO).FirstOrDefault().COMPARISON_NO + "," : null;
                        }
                        if (item.OCCUR_EVENT.Contains("性能验证"))
                        {
                            relatedEvent += verificationList.Where(p => p.RELATION_EVENT == item.MAINTAIN_NO).FirstOrDefault() != null
                            ? verificationList.Where(p => p.RELATION_EVENT == item.MAINTAIN_NO).FirstOrDefault().VERIFICATION_NO + "," : null;
                        }
                    }
                    if (Convert.ToDateTime(item.MAINTAIN_DATE).ToString("yyyy-MM-dd") == arrWorkTime[i])
                    {
                        secondNode.Add(new SECOND_SEQUENTIAL_NODE()
                        {
                            ID = item.MAINTAIN_ID,
                            TYPE = "保养",
                            OPER_PERSON = item.MAINTAIN_PERSON,
                            WORK_NO = item.MAINTAIN_NO,
                            WORK_MAIN_CONTENT = item.MAINTAIN_CYCLE + "保养",
                            WORK_LESS_CONTENT = item.MAINTAIN_CONTENT,
                            RELATED_EVENTS = relatedEvent,
                            EMS_DOC_INFO = docList.Where(p => p.DOC_INFO_ID == item.MAINTAIN_ID).ToList()
                        });
                    }
                });
                repairList.ForEach(item =>
                {
                    string relatedEvent = ",";
                    if (item.OCCUR_EVENT != null)
                    {
                        if (item.OCCUR_EVENT.Contains("校准"))
                        {
                            relatedEvent += correctList.Where(p => p.RELATION_EVENT == item.REPAIR_NO).FirstOrDefault() != null
                            ? correctList.Where(p => p.RELATION_EVENT == item.REPAIR_NO).FirstOrDefault().CORRECT_NO + "," : null;
                        }
                        if (item.OCCUR_EVENT.Contains("比对"))
                        {
                            relatedEvent += comparisonList.Where(p => p.RELATION_EVENT == item.REPAIR_NO).FirstOrDefault() != null
                            ? comparisonList.Where(p => p.RELATION_EVENT == item.REPAIR_NO).FirstOrDefault().COMPARISON_NO + "," : null;
                        }
                        if (item.OCCUR_EVENT.Contains("性能验证"))
                        {
                            relatedEvent += verificationList.Where(p => p.RELATION_EVENT == item.REPAIR_NO).FirstOrDefault() != null
                            ? verificationList.Where(p => p.RELATION_EVENT == item.REPAIR_NO).FirstOrDefault().VERIFICATION_NO + "," : null;
                        }
                    }
                    if (Convert.ToDateTime(item.REPAIR_DATE).ToString("yyyy-MM-dd") == arrWorkTime[i])
                    {
                        secondNode.Add(new SECOND_SEQUENTIAL_NODE()
                        {
                            ID = item.REPAIR_ID,
                            TYPE = "维修",
                            OPER_PERSON = item.REPAIR_PERSON,
                            WORK_NO = item.REPAIR_NO,
                            WORK_MAIN_CONTENT = item.REPAIR_CONTENT,
                            RELATED_EVENTS = relatedEvent,
                            EMS_DOC_INFO = docList.Where(p => p.DOC_INFO_ID == item.REPAIR_ID).ToList()
                        });
                    }
                });
                comparisonList.ForEach(item =>
                {
                    if (Convert.ToDateTime(item.COMPARISON_DATE).ToString("yyyy-MM-dd") == arrWorkTime[i])
                    {
                        secondNode.Add(new SECOND_SEQUENTIAL_NODE()
                        {
                            ID = item.COMPARISON_ID,
                            TYPE = "比对",
                            OPER_PERSON = item.COMPARISON_PERSON,
                            WORK_NO = item.COMPARISON_NO,
                            WORK_MAIN_CONTENT = item.COMPARISON_RESULT,
                            RELATED_EVENTS = item.RELATION_EVENT,
                            EMS_DOC_INFO = docList.Where(p => p.DOC_INFO_ID == item.COMPARISON_ID).ToList()
                        });
                    }
                });
                correctList.ForEach(item =>
                {
                    if (Convert.ToDateTime(item.CORRECT_DATE).ToString("yyyy-MM-dd") == arrWorkTime[i])
                    {
                        secondNode.Add(new SECOND_SEQUENTIAL_NODE()
                        {
                            ID = item.CORRECT_ID,
                            TYPE = "校准",
                            OPER_PERSON = item.CORRECT_PERSON,
                            WORK_NO = item.CORRECT_NO,
                            WORK_MAIN_CONTENT = item.CORRECT_RESULT,
                            RELATED_EVENTS = item.RELATION_EVENT,
                            EMS_DOC_INFO = docList.Where(p => p.DOC_INFO_ID == item.CORRECT_ID).ToList()
                        });
                    }
                });
                verificationList.ForEach(item =>
                {
                    if (Convert.ToDateTime(item.VERIFICATION_DATE).ToString("yyyy-MM-dd") == arrWorkTime[i])
                    {
                        secondNode.Add(new SECOND_SEQUENTIAL_NODE()
                        {
                            ID = item.VERIFICATION_ID,
                            TYPE = "性能验证",
                            OPER_PERSON = item.VERIFICATION_PERSON,
                            WORK_NO = item.VERIFICATION_NO,
                            WORK_MAIN_CONTENT = item.VERIFICATION_RESULT,
                            RELATED_EVENTS = item.RELATION_EVENT,
                            EMS_DOC_INFO = docList.Where(p => p.DOC_INFO_ID == item.VERIFICATION_ID).ToList()
                        });
                    }
                });
                workTimeDto[i].SECOND_SEQUENTIAL_NODE = secondNode;
            }
            if (year == null)
            {
                return workTimeDto;
            }
            return workTimeDto.Where(p => p.WORK_DATE.ToString("yyyy") == year).ToList();
        }

        public List<EMS_MAINTAIN_INFO> GetMaintainList(string equipmentId)
        {
            var res = _dbContext.Db.Queryable<EMS_MAINTAIN_INFO>()
                .Where(p => p.EQUIPMENT_ID == equipmentId && p.MAINTAIN_STATE == "1")
                .OrderByDescending(i => i.MAINTAIN_DATE)
                .ToList();
            var correct_relation = _dbContext.Db.Queryable<EMS_CORRECT_INFO>()
                .Where(p => p.CORRECT_STATE != "2")
                .Select(p => p.RELATION_EVENT).ToList();
            var comparison_relation = _dbContext.Db.Queryable<EMS_COMPARISON_INFO>()
                .Where(p => p.COMPARISON_STATE != "2")
                .Select(p => p.RELATION_EVENT).ToList();
            var verification_relation = _dbContext.Db.Queryable<EMS_VERIFICATION_INFO>()
                .Where(p => p.VERIFICATION_STATE != "2")
                .Select(p => p.RELATION_EVENT).ToList();
            res.ForEach(item =>
            {
                if (item.OCCUR_EVENT.IsNullOrEmpty())
                {
                    item.OCCUR_EVENT = "";
                }
                var occurEvent = "";
                if (correct_relation.Contains(item.MAINTAIN_NO))
                {
                    item.IF_CORRECT = "1";
                    occurEvent += "校准;";
                }
                if (comparison_relation.Contains(item.MAINTAIN_NO))
                {
                    item.IF_COMPARISON = "1";
                    occurEvent += "比对;";
                }
                if (verification_relation.Contains(item.MAINTAIN_NO))
                {
                    item.IF_VERIFICATION = "1";
                    occurEvent += "性能验证;";
                }
                if (item.OCCUR_EVENT.Contains("质控"))
                {
                    occurEvent += "质控;";
                }
                if (occurEvent.Length > 0)
                {
                    occurEvent = occurEvent.Substring(0, occurEvent.Length - 1);
                }
                item.OCCUR_EVENT = occurEvent;

                _dbContext.Db.Updateable(item).ExecuteCommand();
            });
            return res;
        }
        /// <inheritdoc />
        public List<EMS_MAINTAIN_INFO> GetMaintains(string equipmentId, string? maintainCycle,  DateTime? startTime, DateTime? endTime,
            string? content)
        {
            var result = _dbContext.Db.Queryable<EMS_MAINTAIN_INFO>()
                .Where(x => x.EQUIPMENT_ID == equipmentId && x.MAINTAIN_STATE == "1")
                .WhereIF(content.IsNotNullOrEmpty(),x=>SqlFunc.IsNullOrEmpty(x.MAINTAIN_CONTENT) || x.MAINTAIN_CONTENT.Contains(content!))
                .WhereIF(startTime.HasValue,x=>x.MAINTAIN_DATE >= startTime)
                .WhereIF(endTime.HasValue,x=>x.MAINTAIN_DATE<= endTime)
                .WhereIF(maintainCycle.IsNotNullOrEmpty(),x=>x.MAINTAIN_CYCLE == maintainCycle)
                .OrderByDescending(x => x.MAINTAIN_DATE)
                .ToList();
            EventMonitoring(result);
            return result;
        }

        public EMS_MAINTAIN_INFO SaveMaintainInfo(EMS_MAINTAIN_INFO record)
        {
            var old = _dbContext.Db.Queryable<EMS_MAINTAIN_INFO>()
                .Where(p => p.MAINTAIN_ID == record.MAINTAIN_ID)
                .First();
            if (old != null)
            {
                old.MAINTAIN_CYCLE = record.MAINTAIN_CYCLE;
                old.MAINTAIN_DATE = record.MAINTAIN_DATE;
                old.MAINTAIN_PERSON = record.MAINTAIN_PERSON;
                old.MAINTAIN_CONTENT = record.MAINTAIN_CONTENT;
                old.LAST_MPERSON = record.LAST_MPERSON;
                old.LAST_MTIME = record.LAST_MTIME;
                old.REMARK = record.REMARK;
                if (record.OCCUR_EVENT.IsNotNullOrEmpty())
                {
                    //关联事件
                    var arrOccurEvent = record.OCCUR_EVENT.TrimEnd(';').Split(';');
                    string occurEvent = "";
                    if (old.OCCUR_EVENT.IsNullOrEmpty())
                    {
                        arrOccurEvent.ForEach(item =>
                        {
                            occurEvent += item;
                        });
                    }
                    else
                    {
                        for (int i = 0; i < arrOccurEvent.Length; i++)
                        {
                            occurEvent += arrOccurEvent[i];
                        }
                    }

                    if (occurEvent.IsNotNullOrEmpty())
                    {
                        old.OCCUR_EVENT = CreateOccurEvent(occurEvent, record.EQUIPMENT_ID, record.HOSPITAL_ID, record.MAINTAIN_NO, record.LAST_MPERSON).data.ToString();
                    }
                }
                else
                {
                    old.OCCUR_EVENT = CreateOccurEvent("NULL", record.EQUIPMENT_ID, record.HOSPITAL_ID, record.MAINTAIN_NO, record.LAST_MPERSON).data.ToString();
                }
                _dbContext.Db.Updateable(old).ExecuteCommand();
            }
            else
            {
                var no = record.MAINTAIN_DATE!.Value.ToString("yyyyMMdd");
                var maintainInfo = _dbContext.Db.Queryable<EMS_MAINTAIN_INFO>()
                    .Where(x=>x.MAINTAIN_NO.Contains(no))        
                    .ToList();
                var numMaintain = 1;
                if (maintainInfo.Count != 0)
                {
                    var lastMaintain = maintainInfo.OrderByDescending(p => Convert.ToInt32(p.MAINTAIN_NO.Substring(12))).First();
                    numMaintain = Convert.ToInt32(lastMaintain.MAINTAIN_NO.Substring(12)) + 1;
                }
                record.MAINTAIN_NO = "SB" + Convert.ToDateTime(record.MAINTAIN_DATE).ToString("yyyyMMdd") + "BY" + numMaintain.ToString().PadLeft(4, '0');
                _dbContext.Db.Insertable(record).ExecuteCommand();
                if (record.OCCUR_EVENT.IsNotNullOrEmpty())
                {
                    CreateOccurEvent(record.OCCUR_EVENT, record.EQUIPMENT_ID, record.HOSPITAL_ID, record.MAINTAIN_NO, record.FIRST_RPERSON);
                }
            }
            return record;
        }
        

        /// <summary>
        /// 获取  IRecord的记录信息
        /// </summary>
        /// <param name="id">设备XX记录id</param>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        public T GetRecordById<T>(string id) where T :  class,IRecord,new()
        {
            var records = _dbContext.Db.Queryable<T>().ToList();
            var result = records.FirstOrDefault(x => x.GetId() == id);
            if (result is null)
            {
                throw new BizException("当前记录不存在，请刷新页面");
            }
            return result;
        }

        public List<EMS_COMPARISON_INFO> GetComparisons(string equipmentId)
        {
            var res = _dbContext.Db.Queryable<EMS_COMPARISON_INFO>()
                .Where(p => p.EQUIPMENT_ID == equipmentId && p.COMPARISON_STATE == "1")
                .OrderByDescending(i => i.COMPARISON_DATE)
                .ToList();
            return res;
        }


        public ResultDto DeleteMaintainInfo(string maintainId, string userName)
        {
            var res = _dbContext.Db.Queryable<EMS_MAINTAIN_INFO>()
                .Where(p => p.MAINTAIN_ID == maintainId)
                .First();
            if (res == null)
            {
                return new ResultDto { success = false, msg = "未找到该保养记录" };
            }
            if (res.OCCUR_EVENT != null)
            {
                var occurCorrect = _dbContext.Db.Queryable<EMS_CORRECT_INFO>()
                    .Where(p => p.RELATION_EVENT == res.MAINTAIN_NO && p.CORRECT_STATE == "1").First();
                if (occurCorrect != null)
                {
                    DeleteCorrectInfo(occurCorrect.CORRECT_ID, userName);
                }
                var occurComparison = _dbContext.Db.Queryable<EMS_COMPARISON_INFO>().Where(p => p.RELATION_EVENT == res.MAINTAIN_NO && p.COMPARISON_STATE == "1").First();
                if (occurComparison != null)
                {
                    DeleteComparisonInfo(occurComparison.COMPARISON_ID, userName);
                }
                var occurVerification = _dbContext.Db.Queryable<EMS_VERIFICATION_INFO>().Where(p => p.RELATION_EVENT == res.MAINTAIN_NO && p.VERIFICATION_STATE == "1").First();
                if (occurVerification != null)
                {
                    DeleteVerificationInfo(occurVerification.VERIFICATION_ID, userName);
                }
            }
            _dbContext.Db.Updateable<EMS_MAINTAIN_INFO>().SetColumns(predicate => new EMS_MAINTAIN_INFO
            {
                MAINTAIN_STATE = "2",
                LAST_MPERSON = userName,
                LAST_MTIME = DateTime.Now
            }).Where(predicate => predicate.MAINTAIN_ID == maintainId).ExecuteCommand();
            var fileList = _dbContext.Db.Queryable<EMS_DOC_INFO>()
                .Where(p => p.DOC_INFO_ID == maintainId && p.DOC_STATE == "1")
                .ToList();
            fileList.ForEach(item =>
            {
                _baseService.DeleteEnclosureInfo(item.DOC_ID, userName);
            });
            return new ResultDto { success = true };
        }
        public List<EMS_REPAIR_INFO> GetRepairList(string equipmentId)
        {
            var res = _dbContext.Db.Queryable<EMS_REPAIR_INFO>()
                .Where(p => p.EQUIPMENT_ID == equipmentId && p.REPAIR_STATE == "1")
                .OrderByDescending(i => i.REPAIR_DATE)
                .ToList();
            
            var correct_relation = _dbContext.Db.Queryable<EMS_CORRECT_INFO>().Select(p => p.RELATION_EVENT).ToList();
            var comparison_relation = _dbContext.Db.Queryable<EMS_COMPARISON_INFO>().Select(p => p.RELATION_EVENT).ToList();
            var verification_relation = _dbContext.Db.Queryable<EMS_VERIFICATION_INFO>().Select(p => p.RELATION_EVENT).ToList();
            res.ForEach(item =>
            {
                if (item.OCCUR_EVENT.IsNullOrEmpty())
                {
                    item.OCCUR_EVENT = "";
                }
                var occurEvent = "";
                if (correct_relation.Contains(item.REPAIR_NO))
                {
                    item.IF_CORRECT = "1";
                    occurEvent += "校准;";
                }
                if (comparison_relation.Contains(item.REPAIR_NO))
                {
                    item.IF_COMPARISON = "1";
                    occurEvent += "比对;";
                }
                if (verification_relation.Contains(item.REPAIR_NO))
                {
                    item.IF_VERIFICATION = "1";
                    occurEvent += "性能验证;";
                }
                if (item.OCCUR_EVENT.Contains("质控"))
                {
                    occurEvent += "质控;";
                }
                if (occurEvent.Length > 0)
                {
                    occurEvent = occurEvent.Substring(0, occurEvent.Length - 1);
                }
                item.OCCUR_EVENT = occurEvent;
                _dbContext.Db.Updateable(item).ExecuteCommand();
            });

            return res;
        }

        /// <summary>
        ///  查询设备维修记录
        /// </summary>
        /// <param name="equipmentId">设备id </param>
        /// <param name="content">维修内容 </param>
        /// <returns></returns>
        public List<EMS_REPAIR_INFO> GetRepairs(string equipmentId , string? content ,DateTime? startTime, DateTime? endTime)
        {
            var result = _dbContext.Db.Queryable<EMS_REPAIR_INFO>()
                .Where(p => p.EQUIPMENT_ID == equipmentId && p.REPAIR_STATE == "1")
                .WhereIF(content.IsNotNullOrEmpty(),x=>SqlFunc.IsNullOrEmpty(x.REPAIR_CONTENT) || x.REPAIR_CONTENT.Contains(content!))
                .WhereIF(startTime.HasValue,x=>x.REPAIR_DATE >= startTime)
                .WhereIF(endTime.HasValue,x=>x.REPAIR_DATE<= endTime)
                .OrderByDescending(i => i.REPAIR_DATE)
                .ToList();
            EventMonitoring(result);
            return result;
        }
        
        public EMS_REPAIR_INFO SaveRepairInfo(EMS_REPAIR_INFO record)
        {
            var old = _dbContext.Db.Queryable<EMS_REPAIR_INFO>()
                .Where(p => p.REPAIR_ID == record.REPAIR_ID)
                .First();
            if (old != null)
            {
                old.REPAIR_DATE = record.REPAIR_DATE;
                old.REPAIR_PERSON = record.REPAIR_PERSON;
                old.REPAIR_CONTENT = record.REPAIR_CONTENT;
                old.REPAIR_RESULT = record.REPAIR_RESULT;
                old.REMARK = record.REMARK;
                old.LAST_MPERSON = record.LAST_MPERSON;
                old.LAST_MTIME = record.LAST_MTIME;
                if (record.OCCUR_EVENT.IsNotNullOrEmpty())
                {
                    //关联事件
                    var arrOccurEvent = record.OCCUR_EVENT.TrimEnd(';').Split(';');
                    string occurEvent = "";
                    if (old.OCCUR_EVENT.IsNullOrEmpty())
                    {
                        arrOccurEvent.ForEach(item =>
                        {
                            occurEvent += item;
                        });
                    }
                    else
                    {
                        for (int i = 0; i < arrOccurEvent.Length; i++)
                        {
                            occurEvent += arrOccurEvent[i];
                        }
                    }
                    if (occurEvent.IsNotNullOrEmpty())
                    {
                        old.OCCUR_EVENT = CreateOccurEvent(occurEvent, record.EQUIPMENT_ID, record.HOSPITAL_ID, record.REPAIR_NO, record.LAST_MPERSON).data.ToString();
                    }

                }
                else
                {
                    old.OCCUR_EVENT = CreateOccurEvent("NULL", record.EQUIPMENT_ID, record.HOSPITAL_ID, record.REPAIR_NO, record.LAST_MPERSON).data.ToString();
                }
                _dbContext.Db.Updateable(old).ExecuteCommand();
            }
            else
            {
                var no = record.REPAIR_DATE!.Value.ToString("yyyyMMdd");
                var repirInfo = _dbContext.Db.Queryable<EMS_REPAIR_INFO>()
                    .Where(x=>x.REPAIR_NO.Contains(no))        
                    .ToList();
                var numMaintain = 1;
                if (repirInfo.Count != 0)
                {
                    var lastRepair = repirInfo.OrderByDescending(p => Convert.ToInt32(p.REPAIR_NO.Substring(12))).First();
                    numMaintain = Convert.ToInt32(lastRepair.REPAIR_NO.Substring(12)) + 1;
                }
                record.REPAIR_NO = "SB" + Convert.ToDateTime(record.REPAIR_DATE).ToString("yyyyMMdd") + "WX" + numMaintain.ToString().PadLeft(4, '0');
                _dbContext.Db.Insertable(record).ExecuteCommand();
                if (record.OCCUR_EVENT.IsNotNullOrEmpty())
                {
                    CreateOccurEvent(record.OCCUR_EVENT, record.EQUIPMENT_ID, record.HOSPITAL_ID, record.REPAIR_NO, record.FIRST_RPERSON);
                }
            }
            return record;
        }

        public ResultDto DeleteRepairInfo(string repairId, string userName)
        {
            var res = _dbContext.Db.Queryable<EMS_REPAIR_INFO>()
                .Where(p => p.REPAIR_ID == repairId)
                .First();
            if (res == null)
            {
                return new ResultDto { success = false, msg = "未找到该维修记录" };
            }
            if (res.OCCUR_EVENT != null)
            {
                var occurCorrect = _dbContext.Db.Queryable<EMS_CORRECT_INFO>().Where(p => p.RELATION_EVENT == res.REPAIR_NO && p.CORRECT_STATE == "1").First();
                if (occurCorrect != null)
                {
                    occurCorrect.CORRECT_STATE = "2";
                    occurCorrect.LAST_MPERSON = userName;
                    occurCorrect.LAST_MTIME = DateTime.Now;
                    _dbContext.Db.Updateable(occurCorrect).ExecuteCommand();
                    var correctFile = _dbContext.Db.Queryable<EMS_DOC_INFO>().Where(p => p.DOC_INFO_ID == occurCorrect.CORRECT_ID && p.DOC_STATE == "1").ToList();
                    correctFile.ForEach(item =>
                    {
                        _baseService.DeleteEnclosureInfo(item.DOC_ID, userName);
                    });
                }
                var occurComparison = _dbContext.Db.Queryable<EMS_COMPARISON_INFO>().Where(p => p.RELATION_EVENT == res.REPAIR_NO && p.COMPARISON_STATE == "1").First();
                if (occurComparison != null)
                {
                    occurComparison.COMPARISON_STATE = "2";
                    occurComparison.LAST_MPERSON = userName;
                    occurComparison.LAST_MTIME = DateTime.Now;
                    _dbContext.Db.Updateable(occurComparison).ExecuteCommand();
                    var comparisonFile = _dbContext.Db.Queryable<EMS_DOC_INFO>().Where(p => p.DOC_INFO_ID == occurComparison.COMPARISON_ID && p.DOC_STATE == "1").ToList();
                    comparisonFile.ForEach(item =>
                    {
                        _baseService.DeleteEnclosureInfo(item.DOC_ID, userName);
                    });
                }
                var occurVerification = _dbContext.Db.Queryable<EMS_VERIFICATION_INFO>().Where(p => p.RELATION_EVENT == res.REPAIR_NO && p.VERIFICATION_STATE == "1").First();
                if (occurVerification != null)
                {
                    occurVerification.VERIFICATION_STATE = "2";
                    occurVerification.LAST_MPERSON = userName;
                    occurVerification.LAST_MTIME = DateTime.Now;
                    _dbContext.Db.Updateable(occurVerification).ExecuteCommand();
                    var verificationFile = _dbContext.Db.Queryable<EMS_DOC_INFO>().Where(p => p.DOC_INFO_ID == occurVerification.VERIFICATION_ID && p.DOC_STATE == "1").ToList();
                    verificationFile.ForEach(item =>
                    {
                        _baseService.DeleteEnclosureInfo(item.DOC_ID, userName);
                    });
                }
            }
            _dbContext.Db.Updateable<EMS_REPAIR_INFO>().SetColumns(predicate => new EMS_REPAIR_INFO
            {
                REPAIR_STATE = "2",
                LAST_MPERSON = userName,
                LAST_MTIME = DateTime.Now
            }).Where(predicate => predicate.REPAIR_ID == repairId).ExecuteCommand();
            var fileList = _dbContext.Db.Queryable<EMS_DOC_INFO>()
                .Where(p => p.DOC_INFO_ID == repairId && p.DOC_STATE == "1")
                .ToList();
            fileList.ForEach(item =>
            {
                _baseService.DeleteEnclosureInfo(item.DOC_ID, userName);
            });
            return new ResultDto { success = true };
        }
        public List<EMS_CORRECT_INFO> GetCorrectList(string equipmentId)
        {
            var res = _dbContext.Db.Queryable<EMS_CORRECT_INFO>()
                .Where(p => p.EQUIPMENT_ID == equipmentId && p.CORRECT_STATE == "1")
                .OrderByDescending(i => i.CORRECT_DATE)
                .ToList();
            EventMonitoring(res);
            return res;
        }


        /// <summary>
        /// 获取设备校准记录有效期推断
        /// </summary>
        /// <param name="equipmentId"></param>
        /// <param name="records"></param>
        public void GetCorrectRecordValidityPeriod(string equipmentId, List<EMS_CORRECT_INFO> records)
        {
            var workPlan = _dbContext.Db.Queryable<EMS_WORK_PLAN>()
                .Where(x => x.CURRENT_STATE == "3")
                .Where(x => x.EQUIPMENT_ID == equipmentId)
                .First();
            if (workPlan is not null)
            {
                var correctWorkPlanDay = workPlan.CORRECT_INTERVALS.IsNullOrEmpty()
                    ? 0
                    : double.Parse(workPlan.CORRECT_INTERVALS);
                foreach (var item in records)
                {
                    item.DURATION_CORRECT_DATE = item.CORRECT_DATE.HasValue? item.CORRECT_DATE.Value.AddDays(correctWorkPlanDay) : null;
                }
            }
        }


        public List<EMS_CORRECT_INFO> GetCorrects(string equipmentId ,DateTime? startTime, DateTime? endTime)
        {
            var result = _dbContext.Db.Queryable<EMS_CORRECT_INFO>()
                .Where(x=>x.EQUIPMENT_ID == equipmentId)
                .Where(x=>x.CORRECT_STATE == "1")
                .WhereIF(startTime.HasValue,x=>x.FIRST_RTIME >= startTime)
                .WhereIF(endTime.HasValue,x=>x.FIRST_RTIME<= endTime.Value.AddDays(1))
                .OrderByDescending(i => i.CORRECT_DATE)
                .ToList();
            EventMonitoring(result);
            GetCorrectRecordValidityPeriod(equipmentId, result);
            return result;
        }
        
        public EMS_CORRECT_INFO SaveCorrectInfo(EMS_CORRECT_INFO record)
        {
            var old = _dbContext.Db.Queryable<EMS_CORRECT_INFO>()
                .Where(p => p.CORRECT_ID == record.CORRECT_ID)
                .First();
            if (old != null)
            {
                old.STATE = record.STATE;
                old.CORRECT_DEPT = record.CORRECT_DEPT;
                old.CORRECT_PERSON = record.CORRECT_PERSON;
                old.CORRECT_DATE = record.CORRECT_DATE;
                old.CORRECT_RESULT = record.CORRECT_RESULT;
                old.LAST_MPERSON = record.LAST_MPERSON;
                old.LAST_MTIME = record.LAST_MTIME;
                old.STATE = "已执行";
                old.REMARK = record.REMARK;
                if (record.OCCUR_EVENT.IsNotNullOrEmpty())
                {
                    //关联事件
                    var arrOccurEvent = record.OCCUR_EVENT.TrimEnd(';').Split(';');
                    string occurEvent = "";
                    if (old.OCCUR_EVENT.IsNullOrEmpty())
                    {
                        arrOccurEvent.ForEach(item =>
                        {
                            occurEvent += item;
                        });
                    }
                    else
                    {
                        for (int i = 0; i < arrOccurEvent.Length; i++)
                        {
                            //if (old.OCCUR_EVENT.Contains(arrOccurEvent[i]) == false)
                            //{
                            //    maintain += arrOccurEvent[i];
                            //}
                            occurEvent += arrOccurEvent[i];
                        }
                    }

                    if (occurEvent.IsNotNullOrEmpty())
                    {
                        old.OCCUR_EVENT = CreateOccurEvent(occurEvent, record.EQUIPMENT_ID, record.HOSPITAL_ID, record.CORRECT_NO, record.LAST_MPERSON).data.ToString();
                    }
                }
                else
                {
                    old.OCCUR_EVENT = CreateOccurEvent("NULL", record.EQUIPMENT_ID, record.HOSPITAL_ID, record.CORRECT_NO, record.LAST_MPERSON).data.ToString();
                }
                _dbContext.Db.Updateable(old).ExecuteCommand();
            }
            else
            {
                var no = record.CORRECT_DATE!.Value.ToString("yyyyMMdd");
                var numCorrect = _dbContext.Db.Queryable<EMS_CORRECT_INFO>()
                    .Where(x=>x.CORRECT_NO.Contains(no))        
                    .Count() + 1;
                record.CORRECT_NO = "SB" + Convert.ToDateTime(record.CORRECT_DATE).ToString("yyyyMMdd") + "JZ" + numCorrect.ToString().PadLeft(4, '0');
                _dbContext.Db.Insertable(record).ExecuteCommand();
                if (record.OCCUR_EVENT.IsNotNullOrEmpty())
                {
                    CreateOccurEvent(record.OCCUR_EVENT, record.EQUIPMENT_ID, record.HOSPITAL_ID, record.CORRECT_NO, record.FIRST_RPERSON);
                }
            }
            return record;
        }
        public ResultDto DeleteCorrectInfo(string correctId, string userName)
        {
           
            var res = _dbContext.Db.Queryable<EMS_CORRECT_INFO>().Where(p => p.CORRECT_ID == correctId).First();
            if (res == null)
            {
                return new ResultDto { success = false, msg = "未找到校准记录" };
            }
            if (res.OCCUR_EVENT != null)
            {
                var occurComparison = _dbContext.Db.Queryable<EMS_COMPARISON_INFO>().Where(p => p.RELATION_EVENT == res.CORRECT_NO && p.COMPARISON_STATE == "1").First();
                if (occurComparison != null)
                {
                    
                    DeleteComparisonInfo(occurComparison.COMPARISON_ID, userName);
                }
                var occurVerification = _dbContext.Db.Queryable<EMS_VERIFICATION_INFO>().Where(p => p.RELATION_EVENT == res.CORRECT_NO && p.VERIFICATION_STATE == "1").First();
                if (occurVerification != null)
                {
                    
                    DeleteVerificationInfo(occurVerification.VERIFICATION_ID, userName);
                }
            }
            _dbContext.Db.Updateable<EMS_CORRECT_INFO>().SetColumns(predicate => new EMS_CORRECT_INFO
            {
                CORRECT_STATE = "2",
                LAST_MPERSON = userName,
                LAST_MTIME = DateTime.Now
            }).Where(predicate => predicate.CORRECT_ID == correctId).ExecuteCommand();
            var fileList = _dbContext.Db.Queryable<EMS_DOC_INFO>()
                .Where(p => p.DOC_INFO_ID == correctId && p.DOC_STATE == "1")
                .ToList();
            fileList.ForEach(item =>
            {
                _baseService.DeleteEnclosureInfo(item.DOC_ID, userName);
            });
            return new ResultDto { success = true };

        }
        public List<EMS_COMPARISON_INFO> GetComparisonList(string equipmentId)
        {
            var res = _dbContext.Db.Queryable<EMS_COMPARISON_INFO>()
                .Where(p => p.EQUIPMENT_ID == equipmentId && p.COMPARISON_STATE == "1")
                .OrderByDescending(i => i.COMPARISON_DATE)
                .ToList();
            return res;
        }
        public EMS_COMPARISON_INFO SaveComparisonInfo(EMS_COMPARISON_INFO record)
        {
            var old = _dbContext.Db.Queryable<EMS_COMPARISON_INFO>()
                .Where(p => p.COMPARISON_ID == record.COMPARISON_ID)
                .First();
            if (old != null)
            {
                old.STATE = record.STATE;
                old.COMPARISON_DATE = record.COMPARISON_DATE;
                old.COMPARISON_OBJECT = record.COMPARISON_OBJECT;
                old.COMPARISON_PERSON = record.COMPARISON_PERSON;
                old.COMPARISON_RESULT = record.COMPARISON_RESULT;
                old.LAST_MPERSON = record.LAST_MPERSON;
                old.LAST_MTIME = record.LAST_MTIME;
                old.REMARK = record.REMARK;
                _dbContext.Db.Updateable(old).ExecuteCommand();
            }
            else
            {
                var no = record.COMPARISON_DATE!.Value.ToString("yyyyMMdd");
                var numComparison = _dbContext.Db.Queryable<EMS_COMPARISON_INFO>()
                    .Where(x=>x.COMPARISON_NO.Contains(no))       
                    .Count() + 1;
                record.COMPARISON_NO = "SB" + Convert.ToDateTime(record.COMPARISON_DATE).ToString("yyyyMMdd") + "BD" + numComparison.ToString().PadLeft(4, '0');
                _dbContext.Db.Insertable(record).ExecuteCommand();
            }
            return record;
        }
        public ResultDto DeleteComparisonInfo(string comparisonId, string userName)
        {
            var count = _dbContext.Db.Updateable<EMS_COMPARISON_INFO>().SetColumns(p => new EMS_COMPARISON_INFO
            {
                COMPARISON_STATE = "2",
                LAST_MPERSON = userName,
                LAST_MTIME = DateTime.Now
            }).Where(p => p.COMPARISON_ID == comparisonId).ExecuteCommand();
            return new ResultDto { success = true, data = count };
        }
        public List<EMS_VERIFICATION_INFO> GetVerificationList(string equipmentId)
        {
            var res = _dbContext.Db.Queryable<EMS_VERIFICATION_INFO>()
                .Where(p => p.EQUIPMENT_ID == equipmentId && p.VERIFICATION_STATE == "1")
                .OrderByDescending(i => i.VERIFICATION_DATE)
                .ToList();
            return res;
        }
        public EMS_VERIFICATION_INFO SaveVerificationInfo(EMS_VERIFICATION_INFO record)
        {
            var old = _dbContext.Db.Queryable<EMS_VERIFICATION_INFO>()
                .Where(p => p.VERIFICATION_ID == record.VERIFICATION_ID)
                .First();
            if (old != null)
            {
                old.STATE = record.STATE;
                old.VERIFICATION_DATE = record.VERIFICATION_DATE;
                old.VERIFICATION_PERSON = record.VERIFICATION_PERSON;
                old.VERIFICATION_RESULT = record.VERIFICATION_RESULT;
                old.LAST_MPERSON = record.LAST_MPERSON;
                old.LAST_MTIME = old.LAST_MTIME;
                old.STATE = "已执行";
                old.REMARK = record.REMARK;
                _dbContext.Db.Updateable(old).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommand();
            }
            else
            {
                var no = record.VERIFICATION_DATE!.Value.ToString("yyyyMMdd");
                var numVerification = _dbContext.Db.Queryable<EMS_VERIFICATION_INFO>()
                    .Where(x=>x.VERIFICATION_NO.Contains(no))
                    .Count() + 1;
                record.VERIFICATION_NO = Convert.ToDateTime(record.VERIFICATION_DATE).ToString("yyyyMMdd") + "XNYZ" + numVerification.ToString().PadLeft(4, '0');
                _dbContext.Db.Insertable(record).ExecuteCommand();
            }
            return record;
        }
        public ResultDto DeleteVerificationInfo(string verificationInfoId, string userName)
        {
            var count = _dbContext.Db.Updateable<EMS_VERIFICATION_INFO>().SetColumns(p => new EMS_VERIFICATION_INFO
            {
                VERIFICATION_STATE = "2",
                LAST_MPERSON = userName,
                LAST_MTIME = DateTime.Now
            }).Where(p => p.VERIFICATION_ID == verificationInfoId).ExecuteCommand();
            return new ResultDto { success = true, data = count };
        }
        public List<EMS_CHANGE_INFO> GetChangeList(string equipmentId)
        {
            var res = _dbContext.Db.Queryable<EMS_CHANGE_INFO>()
                .Where(p => p.EQUIPMENT_ID == equipmentId && p.CHANGE_STATE == "1")
                .OrderByDescending(i => i.CHANGE_DATE)
                .ToList();
            return res;
        }
        public EMS_CHANGE_INFO SaveChangeInfo(EMS_CHANGE_INFO record)
        {
            var old = _dbContext.Db.Queryable<EMS_CHANGE_INFO>()
                .Where(p => p.CHANGE_ID == record.CHANGE_ID)
                .First();
            if (old != null)
            {
                old.CHANGE_DATE = record.CHANGE_DATE;
                old.CHANGE_PERSON = record.CHANGE_PERSON;
                old.CHANGE_CONTENT = record.CHANGE_CONTENT;
                old.LAST_MPERSON = record.LAST_MPERSON;
                old.LAST_MTIME = record.LAST_MTIME;
                old.REMARK = record.REMARK;
                _dbContext.Db.Updateable(old).ExecuteCommand();
            }
            else
            {
                var no = record.CHANGE_DATE!.Value.ToString("yyyyMMdd");
                var changeInfo = _dbContext.Db.Queryable<EMS_CHANGE_INFO>()
                    .Where(x=>x.CHANGE_NO.Contains(no))        
                    .ToList();
                var num_change = 1;
                if (changeInfo.Count != 0)
                {
                    var lastChange = changeInfo.OrderByDescending(p => Convert.ToInt32(p.CHANGE_NO.Substring(10))).First();
                    num_change = Convert.ToInt32(lastChange.CHANGE_NO.Substring(10)) + 1;
                }
                record.CHANGE_NO = Convert.ToDateTime(record.CHANGE_DATE).ToString("yyyyMMdd") + "BG" + num_change.ToString().PadLeft(4, '0');
                _dbContext.Db.Insertable(record).ExecuteCommand();
            }
            return record;
        }
        public ResultDto DeleteChangeInfo(string changeId, string userName)
        {
            ResultDto result = new ResultDto();
            try
            {
                _dbContext.Db.Updateable<EMS_CHANGE_INFO>().SetColumns(predicate => new EMS_CHANGE_INFO
                {
                    CHANGE_STATE = "2",
                    LAST_MPERSON = userName,
                    LAST_MTIME = DateTime.Now
                }).Where(predicate => predicate.CHANGE_ID == changeId).ExecuteCommand();
                var fileList = _dbContext.Db.Queryable<EMS_DOC_INFO>()
                    .Where(p => p.DOC_INFO_ID == changeId && p.DOC_STATE == "1")
                    .ToList();
                fileList.ForEach(item =>
                {
                    _baseService.DeleteEnclosureInfo(item.DOC_ID, userName);
                });
                result.success = true;
            }
            catch (Exception ex)
            {
                result.success = false;
                result.msg = "删除变更记录失败";
                _logger.LogError("删除变更记录失败:\n" + ex.Message);
            }
            return result;
        }
        public List<EMS_DOC_INFO> GetOperFiles(string module, string operId)
        {
            var fileIdList = new List<string>();
            var result = new List<EMS_DOC_INFO>();
            //保养
            if (module == "0")
            {
                var maintainInfo = _dbContext.Db.Queryable<EMS_MAINTAIN_INFO>()
                    .Where(p => p.MAINTAIN_ID == operId && p.MAINTAIN_STATE == "1")
                    .First();
                if (maintainInfo == null)
                {
                    throw new BizException("找不到保养信息");
                }
                fileIdList.Add(maintainInfo.MAINTAIN_ID);
                if (maintainInfo.OCCUR_EVENT != null)
                {
                    if (maintainInfo.OCCUR_EVENT.Contains("校准"))
                    {
                        var relationEvent = _dbContext.Db.Queryable<EMS_CORRECT_INFO>()
                            .Where(p => p.RELATION_EVENT == maintainInfo.MAINTAIN_NO && p.CORRECT_STATE == "1")
                            .First();
                        if (relationEvent != null)
                        {
                            fileIdList.Add(relationEvent.CORRECT_ID);
                        }
                    }
                    if (maintainInfo.OCCUR_EVENT.Contains("比对"))
                    {
                        var relationEvent = _dbContext.Db.Queryable<EMS_COMPARISON_INFO>()
                            .Where(p => p.RELATION_EVENT == maintainInfo.MAINTAIN_NO && p.COMPARISON_STATE == "1")
                            .First();
                        if (relationEvent != null)
                        {
                            fileIdList.Add(relationEvent.COMPARISON_ID);
                        }
                    }
                    if (maintainInfo.OCCUR_EVENT.Contains("性能验证"))
                    {

                        var relationEvent = _dbContext.Db.Queryable<EMS_VERIFICATION_INFO>()
                            .Where(p => p.RELATION_EVENT == maintainInfo.MAINTAIN_NO && p.VERIFICATION_STATE == "1")
                            .First();
                        if (relationEvent != null)
                        {
                            fileIdList.Add(relationEvent.VERIFICATION_ID);
                        }
                    }
                }
            }
            //维修
            if (module == "1")
            {
                var repairInfo = _dbContext.Db.Queryable<EMS_REPAIR_INFO>()
                    .Where(p => p.REPAIR_ID == operId)
                    .First();
                if (repairInfo == null)
                {
                    throw new BizException("找不到维修信息");
                }
                fileIdList.Add(repairInfo.REPAIR_ID);
                if (repairInfo.OCCUR_EVENT != null)
                {
                    if (repairInfo.OCCUR_EVENT.Contains("校准"))
                    {
                        var relationEvent = _dbContext.Db.Queryable<EMS_CORRECT_INFO>()
                            .Where(p => p.RELATION_EVENT == repairInfo.REPAIR_NO && p.CORRECT_STATE == "1")
                            .First();
                        if (relationEvent != null)
                        {
                            fileIdList.Add(relationEvent.CORRECT_ID);
                        }
                    }
                    if (repairInfo.OCCUR_EVENT.Contains("比对"))
                    {
                        var relationEvent = _dbContext.Db.Queryable<EMS_COMPARISON_INFO>()
                            .Where(p => p.RELATION_EVENT == repairInfo.REPAIR_NO && p.COMPARISON_STATE == "1")
                            .First();
                        if (relationEvent != null)
                        {
                            fileIdList.Add(relationEvent.COMPARISON_ID);
                        }
                    }
                    if (repairInfo.OCCUR_EVENT.Contains("性能验证"))
                    {
                        var relationEvent = _dbContext.Db.Queryable<EMS_VERIFICATION_INFO>()
                            .Where(p => p.RELATION_EVENT == repairInfo.REPAIR_NO && p.VERIFICATION_STATE == "1")
                            .First();
                        if (relationEvent != null)
                        {
                            fileIdList.Add(relationEvent.VERIFICATION_ID);
                        }
                    }
                }
            }
            //校准
            if (module == "2")
            {
                var correctInfo = _dbContext.Db.Queryable<EMS_CORRECT_INFO>()
                    .Where(p => p.CORRECT_ID == operId)
                    .First();
                if (correctInfo == null)
                {
                    throw new BizException("找不到保养信息");
                }
                fileIdList.Add(correctInfo.CORRECT_ID);
                if (correctInfo.RELATION_EVENT != null)
                {
                    if (correctInfo.RELATION_EVENT.Contains("BY"))
                    {
                        var occurEvent = _dbContext.Db.Queryable<EMS_MAINTAIN_INFO>()
                               .Where(p => p.MAINTAIN_NO == correctInfo.RELATION_EVENT && p.MAINTAIN_STATE == "1")
                               .First();
                        if (occurEvent != null)
                        {
                            fileIdList.Add(occurEvent.MAINTAIN_ID);
                        }
                    }
                    if (correctInfo.RELATION_EVENT.Contains("WX"))
                    {
                        var occurEvent = _dbContext.Db.Queryable<EMS_REPAIR_INFO>()
                               .Where(p => p.REPAIR_NO == correctInfo.RELATION_EVENT && p.REPAIR_STATE == "1")
                               .First();
                        if (occurEvent != null)
                        {
                            fileIdList.Add(occurEvent.REPAIR_ID);
                        }
                    }
                }
            }
            //比对
            if (module == "3")
            {
                var comparisonInfo = _dbContext.Db.Queryable<EMS_COMPARISON_INFO>()
                    .Where(p => p.COMPARISON_ID == operId)
                    .First();
                if (comparisonInfo == null)
                {
                    throw new BizException("找不到比对信息");
                }
                fileIdList.Add(comparisonInfo.COMPARISON_ID);
                if (comparisonInfo.RELATION_EVENT != null)
                {
                    if (comparisonInfo.RELATION_EVENT.Contains("BY"))
                    {
                        var occurEvent = _dbContext.Db.Queryable<EMS_MAINTAIN_INFO>()
                               .Where(p => p.MAINTAIN_NO == comparisonInfo.RELATION_EVENT && p.MAINTAIN_STATE == "1")
                               .First();
                        if (occurEvent != null)
                        {
                            fileIdList.Add(occurEvent.MAINTAIN_ID);
                        }
                    }
                    if (comparisonInfo.RELATION_EVENT.Contains("WX"))
                    {
                        var occurEvent = _dbContext.Db.Queryable<EMS_REPAIR_INFO>()
                               .Where(p => p.REPAIR_NO == comparisonInfo.RELATION_EVENT && p.REPAIR_STATE == "1")
                               .First();
                        if (occurEvent != null)
                        {
                            fileIdList.Add(occurEvent.REPAIR_ID);
                        }
                    }
                }
            }
            //性能验证
            if (module == "4")
            {
                var verificationInfo = _dbContext.Db.Queryable<EMS_VERIFICATION_INFO>()
                    .Where(p => p.VERIFICATION_ID == operId)
                    .First();
                if (verificationInfo == null)
                {
                    throw new BizException("找不到比对信息");
                }
                fileIdList.Add(verificationInfo.VERIFICATION_ID);
                if (verificationInfo.RELATION_EVENT != null)
                {
                    if (verificationInfo.RELATION_EVENT.Contains("BY"))
                    {
                        var occurEvent = _dbContext.Db.Queryable<EMS_MAINTAIN_INFO>()
                               .Where(p => p.MAINTAIN_NO == verificationInfo.RELATION_EVENT && p.MAINTAIN_STATE == "1")
                               .First();
                        if (occurEvent != null)
                        {
                            fileIdList.Add(occurEvent.MAINTAIN_ID);
                        }
                    }
                    if (verificationInfo.RELATION_EVENT.Contains("WX"))
                    {
                        var occurEvent = _dbContext.Db.Queryable<EMS_REPAIR_INFO>()
                               .Where(p => p.REPAIR_NO == verificationInfo.RELATION_EVENT && p.REPAIR_STATE == "1")
                               .First();
                        if (occurEvent != null)
                        {
                            fileIdList.Add(occurEvent.REPAIR_ID);
                        }
                    }
                }
            }
            //变更
            if (module == "5")
            {
                fileIdList.Add(operId);
            }
            result = _dbContext.Db.Queryable<EMS_DOC_INFO>()
                    .Where(p => fileIdList.Contains(p.DOC_INFO_ID) && p.DOC_STATE == "1")
                    .ToList();
            result.ForEach(item =>
            {
                item.DOC_PATH = item.DOC_PATH;
            });
            return result.OrderByDescending(i => i.LAST_MTIME).ToList();
        }
        public RelationEventDto GetRelationEventInfo(string relationNo)
        {
            var result = new RelationEventDto();
            if (relationNo.IsNotNullOrEmpty())
            {
                if (relationNo.Contains("BY"))
                {
                    var relationEvent = _dbContext.Db.Queryable<EMS_MAINTAIN_INFO>()
                        .Where(p => p.MAINTAIN_NO == relationNo)
                        .First();
                    if (relationEvent != null)
                    {
                        result.RELATION_NO = "保养单号：" + relationEvent.MAINTAIN_NO;
                        result.RELATION_PERSON = "保养人员：" + relationEvent.MAINTAIN_PERSON;
                        result.RELATION_CONTENT = "保养内容：" + relationEvent.MAINTAIN_CONTENT;
                    }
                }
                if (relationNo.Contains("WX"))
                {
                    var relationEvent = _dbContext.Db.Queryable<EMS_REPAIR_INFO>()
                        .Where(p => p.REPAIR_NO == relationNo)
                        .First();
                    if (relationEvent != null)
                    {
                        result.RELATION_NO = "维修单号：" + relationEvent.REPAIR_NO;
                        result.RELATION_PERSON = "维修人员：" + relationEvent.REPAIR_PERSON;
                        result.RELATION_CONTENT = "维修内容：" + relationEvent.REPAIR_CONTENT;
                    }
                }
                if (relationNo.Contains("JZ"))
                {
                    var relationEvent = _dbContext.Db.Queryable<EMS_CORRECT_INFO>()
                        .Where(p => p.CORRECT_NO == relationNo)
                        .First();
                    if (relationEvent != null)
                    {
                        result.RELATION_NO = "校准/检定单号：" + relationEvent.CORRECT_NO;
                        result.RELATION_PERSON = "校准/检定人员：" + relationEvent.CORRECT_PERSON;
                        result.RELATION_CONTENT = "校准/检定部门：" + relationEvent.CORRECT_DEPT;
                    }
                }
            }
            return result;
        }
        public ResultDto CreateOccurEvent(string occurEvent, string equipmentId, string hospitalId, string relationEvent, string userName)
        {
            var data = "";
            if (occurEvent.Contains("校准"))
            {
                var oldCorrect = _dbContext.Db.Queryable<EMS_CORRECT_INFO>()
                    .Where(p => p.RELATION_EVENT == relationEvent)
                    .Where(p=>p.CORRECT_STATE !="2")
                    .First();
                if (oldCorrect == null)
                {
                    EMS_CORRECT_INFO correct_record = new();
                    var num_correct = _dbContext.Db.Queryable<EMS_CORRECT_INFO>().Count() + 1;
                    correct_record.CORRECT_ID = IDGenHelper.CreateGuid().ToString();
                    correct_record.HOSPITAL_ID = hospitalId;
                    correct_record.STATE = "未执行";
                    correct_record.CORRECT_NO = "SB" + DateTime.Now.ToString("yyyyMMdd") + "JZ" + num_correct.ToString().PadLeft(4, '0');
                    correct_record.EQUIPMENT_ID = equipmentId;
                    correct_record.RELATION_EVENT = relationEvent;
                    correct_record.FIRST_RPERSON = userName;
                    correct_record.FIRST_RTIME = DateTime.Now;
                    correct_record.LAST_MPERSON = userName;
                    correct_record.LAST_MTIME = DateTime.Now;
                    correct_record.CORRECT_STATE = "1";
                    _dbContext.Db.Insertable(correct_record).ExecuteCommand();
                    data = "校准;";
                }
                else
                {
                    data += "校准;";
                }
            }
            else
            {
                var oldCorrect = _dbContext.Db.Queryable<EMS_CORRECT_INFO>()
                    .Where(p => p.RELATION_EVENT == relationEvent)
                    .Where(p=>p.CORRECT_STATE !="2")
                    .First();
                if (oldCorrect != null)
                {
                    if (oldCorrect.STATE == "已执行")
                    {
                        data = "校准;";
                    }
                    else
                    {
                        _dbContext.Db.Deleteable(oldCorrect).ExecuteCommand();
                    }
                }
            }

            if (occurEvent.Contains("比对"))
            {
                var oldComparison = _dbContext.Db.Queryable<EMS_COMPARISON_INFO>()
                    .Where(p => p.RELATION_EVENT == relationEvent)
                    .Where(p=>p.COMPARISON_STATE !="2")
                    .First();
                if (oldComparison == null)
                {
                    EMS_COMPARISON_INFO comparison_record = new();
                    var num_comparison = _dbContext.Db.Queryable<EMS_COMPARISON_INFO>()
                            .Count() + 1;
                    comparison_record.COMPARISON_ID = IDGenHelper.CreateGuid().ToString();
                    comparison_record.HOSPITAL_ID = hospitalId;
                    comparison_record.COMPARISON_NO = "SB" + DateTime.Now.ToString("yyyyMMdd") + "BD" + num_comparison.ToString().PadLeft(4, '0');
                    comparison_record.EQUIPMENT_ID = equipmentId;
                    comparison_record.STATE = "未执行";
                    comparison_record.RELATION_EVENT = relationEvent;
                    comparison_record.FIRST_RPERSON = userName;
                    comparison_record.FIRST_RTIME = DateTime.Now;
                    comparison_record.LAST_MPERSON = userName;
                    comparison_record.LAST_MTIME = DateTime.Now;
                    comparison_record.COMPARISON_STATE = "1";
                    _dbContext.Db.Insertable(comparison_record).ExecuteCommand();
                    data += "比对;";
                }
                else
                {
                    data += "比对;";
                }

            }
            else
            {
                var oldComparison = _dbContext.Db.Queryable<EMS_COMPARISON_INFO>()
                    .Where(p => p.RELATION_EVENT == relationEvent)
                    .Where(p=>p.COMPARISON_STATE != "2")
                    .First();
                if (oldComparison != null)
                {
                    if (oldComparison.STATE == "已执行")
                    {
                        data += "比对;";
                    }
                    else
                    {
                        _dbContext.Db.Deleteable(oldComparison).ExecuteCommand();
                    }
                }
            }

            if (occurEvent.Contains("性能验证"))
            {
                var oldVerification = _dbContext.Db.Queryable<EMS_VERIFICATION_INFO>()
                    .Where(p => p.RELATION_EVENT == relationEvent)
                    .Where(p=>p.VERIFICATION_STATE != "2")
                    .First();
                if (oldVerification == null)
                {
                    EMS_VERIFICATION_INFO verification_record = new();
                    var num_verification = _dbContext.Db.Queryable<EMS_VERIFICATION_INFO>()
                            .Count() + 1;
                    verification_record.VERIFICATION_ID = IDGenHelper.CreateGuid().ToString();
                    verification_record.HOSPITAL_ID = hospitalId;
                    verification_record.VERIFICATION_NO = "SB" + DateTime.Now.ToString("yyyyMMdd") + "XNYZ" + num_verification.ToString().PadLeft(4, '0');
                    verification_record.EQUIPMENT_ID = equipmentId;
                    verification_record.STATE = "未执行";
                    verification_record.RELATION_EVENT = relationEvent;
                    verification_record.FIRST_RPERSON = userName;
                    verification_record.FIRST_RTIME = DateTime.Now;
                    verification_record.LAST_MPERSON = userName;
                    verification_record.LAST_MTIME = DateTime.Now;
                    verification_record.VERIFICATION_STATE = "1";
                    _dbContext.Db.Insertable(verification_record).ExecuteCommand();
                    data += "性能验证;";
                }
                else
                {
                    data += "性能验证;";
                }
            }
            else
            {
                var oldVerification = _dbContext.Db.Queryable<EMS_VERIFICATION_INFO>()
                    .Where(p => p.RELATION_EVENT == relationEvent)
                    .Where(p=>p.VERIFICATION_STATE != "2")
                    .First();
                if (oldVerification != null)
                {
                    if (oldVerification.STATE == "已执行")
                    {
                        data += "性能验证";
                    }
                    else
                    {
                        _dbContext.Db.Deleteable(oldVerification).ExecuteCommand();
                    }
                }

            }

            if (occurEvent.Contains("质控"))
            {
                data += "质控;";
            }

            return new ResultDto { success = true, data = data };
        }
        public IssueFileDto GetUsingFile(string equipmentId, int year, int? month)
        {
            var issueFileList = _dbContext.Db.Queryable<TIM_WORK_FORM_ISSUE>()
                .InnerJoin<TIM_WORK_FORM>((formIssue, workForm) => formIssue.FORM_ID == workForm.FORM_ID)
                .Where((formIssue, workForm) => formIssue.FORM_VER_MAIN_ID == equipmentId && formIssue.ISSUE_STATE == "2" && workForm.CLASS_ID == "1")
                .Select((formIssue, workForm) => new
                {
                    formIssue.ISSUE_FILE,
                    formIssue.ISSUE_DATE,
                    formIssue.ISSUE_YEAR,
                    formIssue.ISSUE_MONTH,
                    workForm.ISSUED_PLAN,
                    formIssue.ISSUE_NAME,
                    formIssue.ISSUE_ID
                }).ToList();
            var result = new IssueFileDto();
            result.DISPLAY_TYPE = "year";
            var issueFileInfo = new List<IssueFileInfo>();
            List<string> monthPlan = new List<string>() { "3", "4" };
            List<string> dayPlan = new List<string>() { "1", "2" };
            if (month != null)
            {
                issueFileList.Where(p => p.ISSUE_YEAR == year && p.ISSUE_MONTH == month).ForEach(item =>
                {
                    var issueTime = Convert.ToDateTime(item.ISSUE_DATE.Split("至")[1]);
                    issueFileInfo.Add(new IssueFileInfo
                    {
                        ARCHIVING_TIME = issueTime,
                        ISSUE_FILE = item.ISSUE_FILE,
                        ISSUE_SOURCE = "tim",
                        ISSUE_NAME = item.ISSUE_NAME,
                        ISSUE_ID = item.ISSUE_ID,
                        FILE_TYPE = "PDF"
                    });
                });
            }
            else
            {
                issueFileList.Where(p => p.ISSUE_YEAR == year).ForEach(item =>
                {
                    var issueTime = Convert.ToDateTime(item.ISSUE_DATE.Split("至")[1]);
                    issueFileInfo.Add(new IssueFileInfo
                    {
                        ARCHIVING_TIME = issueTime,
                        ISSUE_FILE = item.ISSUE_FILE,
                        ISSUE_SOURCE = "tim",
                        ISSUE_ID = item.ISSUE_ID,
                        ISSUE_NAME = item.ISSUE_NAME,
                        FILE_TYPE = "PDF"
                    });
                });
            }
            if (issueFileInfo.Count == 0)
            {
                result.DISPLAY_TYPE = "暂无归档数据";
            }
            else
            {
                var issuePlanList = issueFileList.Select(p => p.ISSUED_PLAN).ToList();
                if (issuePlanList.Intersect(monthPlan).Any())
                {
                    result.DISPLAY_TYPE = "month";
                }
                if (issuePlanList.Intersect(dayPlan).Any())
                {
                    result.DISPLAY_TYPE = "day";
                }
            }
            result.IssueFileInfo = issueFileInfo;
            return result;
        }
        public List<MaintainFormDto> GetMaintainFile(string equipmentId, int year, int month)
        {
            var equipmentIssueList = _dbContext.Db.Queryable<EMS_MAINTAIN_INFO>()
                .InnerJoin<EMS_DOC_INFO>((maintain, doc) => maintain.MAINTAIN_ID == doc.DOC_INFO_ID)
                .Where((maintain, doc) => maintain.EQUIPMENT_ID == equipmentId && maintain.MAINTAIN_STATE == "1" && doc.DOC_STATE == "1")
                .Select((maintain, doc) => new
                {
                    maintain.MAINTAIN_DATE,
                    maintain.MAINTAIN_CYCLE,
                    maintain.MAINTAIN_PERSON,
                    doc.DOC_PATH,
                    doc.DOC_ID,
                    doc.DOC_NAME,
                    doc.DOC_TYPE,
                }).ToList();
            var itemIssueList = _dbContext.Db.Queryable<TIM_WORK_FORM_ISSUE>()
                .InnerJoin<TIM_WORK_FORM>((formIssue, workForm) => formIssue.FORM_ID == workForm.FORM_ID)
                .Where((formIssue, workForm) => formIssue.FORM_VER_MAIN_ID == equipmentId && formIssue.ISSUE_STATE == "2" && workForm.CLASS_ID == "2" )
                .Select((formIssue, workForm) => new
                {
                    formIssue.ISSUE_FILE,
                    formIssue.ISSUE_DATE,
                    formIssue.ISSUE_YEAR,
                    formIssue.ISSUE_MONTH,
                    formIssue.ISSUED_PERSON,
                    workForm.ISSUED_PLAN,
                    formIssue.ISSUE_ID,
                    formIssue.ISSUE_NAME,
                }).ToList();
            var issueFileInfo = new List<IssueFileInfo>();
            var issueBaseData = _dbContext.Db.Queryable<SYS6_BASE_DATA>()
                .Where(p => p.DATA_STATE == "1" && p.CLASS_ID == "归档计划")
                .ToList();
            equipmentIssueList.Where(p => Convert.ToDateTime(p.MAINTAIN_DATE).Year == year && Convert.ToDateTime(p.MAINTAIN_DATE).Month == month)
                .ForEach(item =>
            {
                issueFileInfo.Add(new IssueFileInfo
                {
                    ARCHIVING_TIME = item.MAINTAIN_DATE,
                    ISSUE_FILE = item.DOC_PATH,
                    ISSUE_SOURCE = "equipment",
                    ISSUE_TYPE = item.MAINTAIN_CYCLE,
                    OPER_PERSON = item.MAINTAIN_PERSON,
                    FILE_TYPE = item.DOC_TYPE,
                    ISSUE_ID = item.DOC_ID,
                    ISSUE_NAME = item.DOC_NAME
                });
            });
            itemIssueList.Where(p => p.ISSUE_YEAR == year && p.ISSUE_MONTH == month).ForEach(item =>
            {
                var issueTime = Convert.ToDateTime(item.ISSUE_DATE.Split("至")[1]);
                var person = item.ISSUED_PERSON ?? "";
                if (person.Contains("-"))
                {
                    person = item.ISSUED_PERSON.Split("_")[1];
                }
                else
                {
                    person = item.ISSUED_PERSON;
                }

                if (person.IsNullOrEmpty())
                {
                    person = "自动归档";
                }

                issueFileInfo.Add(new IssueFileInfo
                {
                    ARCHIVING_TIME = issueTime,
                    ISSUE_FILE = item.ISSUE_FILE,
                    ISSUE_SOURCE = "tim",
                    ISSUE_TYPE = issueBaseData.Where(p => p.DATA_ID == item.ISSUED_PLAN).FirstOrDefault()?.DATA_CNAME,
                    OPER_PERSON = person,
                    FILE_TYPE = "PDF",
                    ISSUE_ID = item.ISSUE_ID,
                    ISSUE_NAME = item.ISSUE_NAME
                });
            });
            var maintainCycle = new List<string>() { "日/次", "周", "月", "季", "半年/年" };
            var result = new List<MaintainFormDto>();
            maintainCycle.ForEach(item =>
            {
                var maintainFiles = new List<MaintainFileDto>();
                var cycleIssue = new List<IssueFileInfo>();
                if (item == "日/次")
                {
                    cycleIssue = issueFileInfo.Where(p => p.ISSUE_TYPE == "每次" || p.ISSUE_TYPE == "日").ToList();
                }
                
                else if (item == "半年/年")
                {
                    cycleIssue = issueFileInfo.Where(p => p.ISSUE_TYPE == "半年" || p.ISSUE_TYPE == "年").ToList();
                }
                else 
                {
                    cycleIssue = issueFileInfo.Where(p => p.ISSUE_TYPE.Contains(item)).ToList();
                }
                var q = new List<DateTime?>();
                q.AddRange(cycleIssue.Select(p => p.ARCHIVING_TIME));
                var a = q.Where(p => p.ToString().IsNotNullOrEmpty()).ToArray().Distinct();
                var b = new List<string>();
                a.ForEach(i =>
                {
                    b.Add(Convert.ToDateTime(i).ToString("yyyy-MM-dd"));
                });
                b
                .Distinct()
                .ForEach(k =>
                {
                    var maintainData = issueFileInfo.Where( p => item.Contains(p.ISSUE_TYPE)   &&  Convert.ToDateTime(p.ARCHIVING_TIME).ToString("yyyy-MM-dd") == k).ToList();
                    var maintainPersonList = maintainData
                    .DistinctBy(p => p.OPER_PERSON)
                    .ToList();
                    if (maintainData.Count > 0)
                    {
                        var maintainPerson = "";
                        maintainPersonList.ForEach(k =>
                        {
                            maintainPerson += k.OPER_PERSON + ",";
                        });
                        maintainFiles.Add(new MaintainFileDto
                        {
                            MAINTAIN_DATE = k,
                            MAINTAIN_FILES = maintainData.Select(p => new IssueFile
                            {
                                ISSUE_ID = p.ISSUE_ID,
                                ISSUE_NAME = p.ISSUE_NAME,
                                ISSUE_FILE = p.ISSUE_FILE,
                                ISSUE_TYPE = p.FILE_TYPE
                            }).ToList(),
                            MAINTAIN_PERSON = maintainPerson.TrimEnd(','),
                        });
                    }
                });
                result.Add(new MaintainFormDto
                {
                    MAINTAIN_CYCLE = item,
                    MaintainFileDto = maintainFiles
                });
            });
            return result;
        }
        public List<RepairArchivingDto> GetRepairFile(string equipmentId, int year)
        {
            var equipmentIssueList = _dbContext.Db.Queryable<EMS_REPAIR_INFO>()
                .InnerJoin<EMS_DOC_INFO>((repair, doc) => repair.REPAIR_ID == doc.DOC_INFO_ID)
                .Where((repair, doc) => repair.EQUIPMENT_ID == equipmentId && repair.REPAIR_STATE == "1" && doc.DOC_STATE == "1")
                .Select((repair, doc) => new
                {
                    repair.REPAIR_DATE,
                    doc.DOC_PATH,
                    doc.DOC_ID,
                    doc.DOC_NAME,
                    doc.DOC_TYPE
                })
                .ToList();
            var itemIssueList = _dbContext.Db.Queryable<TIM_WORK_FORM_ISSUE>()
                .InnerJoin<TIM_WORK_FORM>((formIssue, workForm) => formIssue.FORM_ID == workForm.FORM_ID)
                .Where((formIssue, workForm) => formIssue.FORM_VER_MAIN_ID == equipmentId && formIssue.ISSUE_STATE == "2" && workForm.CLASS_ID == "3" )
                .Select((formIssue, workForm) => new
                {
                    formIssue.ISSUE_FILE,
                    formIssue.ISSUE_DATE,
                    formIssue.ISSUE_YEAR,
                    formIssue.ISSUE_MONTH,
                    workForm.ISSUED_PLAN,
                    formIssue.ISSUE_ID,
                    formIssue.ISSUE_NAME
                }).ToList();
            var equipmentFileInfo = new List<IssueFileInfo>();
            var issueFileInfo = new List<IssueFileInfo>();
            var result = new List<RepairArchivingDto>();
            equipmentIssueList.Where(p => Convert.ToDateTime(p.REPAIR_DATE).Year == year).ForEach(item =>
            {
                equipmentFileInfo.Add(new IssueFileInfo
                {
                    ARCHIVING_TIME = item.REPAIR_DATE,
                    ISSUE_FILE = item.DOC_PATH,
                    ISSUE_SOURCE = "equipment",
                    ISSUE_ID = item.DOC_ID,
                    ISSUE_NAME = item.DOC_NAME,
                    FILE_TYPE = item.DOC_TYPE
                });
            });
            itemIssueList.Where(p => p.ISSUE_YEAR == year).ForEach(item =>
            {
                var issueTime = Convert.ToDateTime(item.ISSUE_DATE.Split("至")[1]);
                issueFileInfo.Add(new IssueFileInfo
                {
                    ARCHIVING_TIME = issueTime,
                    ISSUE_FILE = item.ISSUE_FILE,
                    ISSUE_SOURCE = "tim",
                    ISSUE_ID = item.ISSUE_ID,
                    ISSUE_NAME = item.ISSUE_NAME,
                    FILE_TYPE = "PDF"
                });
            });
            var q1 = new List<DateTime?>();
            q1.AddRange(equipmentFileInfo.Select(p => p.ARCHIVING_TIME));
            var a1 = q1.Where(p => p.ToString().IsNotNullOrEmpty()).ToArray().Distinct();
            var b1 = new List<string>();
            a1.ForEach(i =>
            {
                b1.Add(Convert.ToDateTime(i).ToString("yyyy-MM-dd"));
            });
            b1.Distinct().ForEach(item =>
            {
                result.Add(new RepairArchivingDto
                {
                    ISSUE_SOURCE = "equipment",
                    REPAIR_DATE = item,
                    ISSUE_FILES = equipmentFileInfo.Where(p => Convert.ToDateTime(p.ARCHIVING_TIME).ToString("yyyy-MM-dd") == item).Select(i => new IssueFile
                    {
                        ISSUE_ID = i.ISSUE_ID,
                        ISSUE_NAME = i.ISSUE_NAME,
                        ISSUE_FILE = i.ISSUE_FILE,
                        ISSUE_TYPE = i.FILE_TYPE
                    }).ToList()
                });
            });

            var q2 = new List<DateTime?>();
            q2.AddRange(issueFileInfo.Select(p => p.ARCHIVING_TIME));
            var a2 = q2.Where(p => p.ToString().IsNotNullOrEmpty()).ToArray().Distinct();
            var b2 = new List<string>();
            a2.ForEach(i =>
            {
                b2.Add(Convert.ToDateTime(i).ToString("yyyy-MM-dd"));
            });
            b2.Distinct().ForEach(item =>
            {
                result.Add(new RepairArchivingDto
                {
                    ISSUE_SOURCE = "tim",
                    REPAIR_DATE = item,
                    ISSUE_FILES = issueFileInfo.Where(p => Convert.ToDateTime(p.ARCHIVING_TIME).ToString("yyyy-MM-dd") == item).Select(i => new IssueFile
                    {
                        ISSUE_ID = i.ISSUE_ID,
                        ISSUE_NAME = i.ISSUE_NAME,
                        ISSUE_FILE = i.ISSUE_FILE,
                        ISSUE_TYPE = i.FILE_TYPE
                    }).ToList()
                });
            });
            return result;
        }
        public List<CorrectArchivingDto> GetCorrectFile(string equipmentId, int year)
        {
            var equipmentIssueList = _dbContext.Db.Queryable<EMS_CORRECT_INFO>()
                .InnerJoin<EMS_DOC_INFO>((correct, doc) => correct.CORRECT_ID == doc.DOC_INFO_ID)
                .Where((correct, doc) => correct.EQUIPMENT_ID == equipmentId && correct.CORRECT_STATE == "1" && doc.DOC_STATE == "1")
                .Select((correct, doc) => new
                {
                    correct.CORRECT_DATE,
                    doc.DOC_PATH,
                    doc.DOC_ID,
                    doc.DOC_NAME,
                    doc.DOC_TYPE
                })
                .ToList();
            var itemIssueList = _dbContext.Db.Queryable<TIM_WORK_FORM_ISSUE>()
                .InnerJoin<TIM_WORK_FORM>((formIssue, workForm) => formIssue.FORM_ID == workForm.FORM_ID)
                .Where((formIssue, workForm) => formIssue.FORM_VER_MAIN_ID == equipmentId && formIssue.ISSUE_STATE == "2" && workForm.CLASS_ID == "4" )
                .Select((formIssue, workForm) => new
                {
                    formIssue.ISSUE_FILE,
                    formIssue.ISSUE_DATE,
                    formIssue.ISSUE_YEAR,
                    formIssue.ISSUE_MONTH,
                    workForm.ISSUED_PLAN,
                    formIssue.ISSUE_ID,
                    formIssue.ISSUE_NAME
                }).ToList();

            var equipmentFileInfo = new List<IssueFileInfo>();
            var issueFileInfo = new List<IssueFileInfo>();
            var result = new List<CorrectArchivingDto>();
            equipmentIssueList.Where(p => Convert.ToDateTime(p.CORRECT_DATE).Year == year).ForEach(item =>
            {
                equipmentFileInfo.Add(new IssueFileInfo
                {
                    ARCHIVING_TIME = item.CORRECT_DATE,
                    ISSUE_FILE = item.DOC_PATH,
                    ISSUE_SOURCE = "equipment",
                    ISSUE_ID = item.DOC_ID,
                    ISSUE_NAME = item.DOC_NAME,
                    FILE_TYPE = item.DOC_TYPE
                });
            });
            itemIssueList.Where(p => p.ISSUE_YEAR == year).ForEach(item =>
            {
                var issueTime = Convert.ToDateTime(item.ISSUE_DATE.Split("至")[1]);
                issueFileInfo.Add(new IssueFileInfo
                {
                    ARCHIVING_TIME = issueTime,
                    ISSUE_FILE = item.ISSUE_FILE,
                    ISSUE_SOURCE = "tim",
                    ISSUE_ID = item.ISSUE_ID,
                    ISSUE_NAME = item.ISSUE_NAME,
                    FILE_TYPE = "PDF"
                });
            });

            var q1 = new List<DateTime?>();
            q1.AddRange(equipmentFileInfo.Select(p => p.ARCHIVING_TIME));
            var a1 = q1.Where(p => p.ToString().IsNotNullOrEmpty()).ToArray().Distinct();
            var b1 = new List<string>();
            a1.ForEach(i =>
            {
                b1.Add(Convert.ToDateTime(i).ToString("yyyy-MM-dd"));
            });
            b1.Distinct().ForEach(item =>
            {
                result.Add(new CorrectArchivingDto
                {
                    ISSUE_SOURCE = "equipment",
                    CORRECT_DATE = item,
                    ISSUE_FILES = equipmentFileInfo.Where(p => Convert.ToDateTime(p.ARCHIVING_TIME).ToString("yyyy-MM-dd") == item).Select(i => new IssueFile
                    {
                        ISSUE_ID = i.ISSUE_ID,
                        ISSUE_NAME = i.ISSUE_NAME,
                        ISSUE_FILE = i.ISSUE_FILE,
                        ISSUE_TYPE = i.FILE_TYPE
                    }).ToList()
                });
            });

            var q2 = new List<DateTime?>();
            q2.AddRange(issueFileInfo.Select(p => p.ARCHIVING_TIME));
            var a2 = q2.Where(p => p.ToString().IsNotNullOrEmpty()).ToArray().Distinct();
            var b2 = new List<string>();
            a2.ForEach(i =>
            {
                b2.Add(Convert.ToDateTime(i).ToString("yyyy-MM-dd"));
            });
            b2.Distinct().ForEach(item =>
            {
                result.Add(new CorrectArchivingDto
                {
                    ISSUE_SOURCE = "tim",
                    CORRECT_DATE = item,
                    ISSUE_FILES = issueFileInfo.Where(p => Convert.ToDateTime(p.ARCHIVING_TIME).ToString("yyyy-MM-dd") == item).Select(i => new IssueFile
                    {
                        ISSUE_ID = i.ISSUE_ID,
                        ISSUE_NAME = i.ISSUE_NAME,
                        ISSUE_FILE = i.ISSUE_FILE,
                        ISSUE_TYPE = i.FILE_TYPE
                    }).ToList()
                });
            });
            return result;
        }

        public List<DecontaminationFileDto> GetDecontaminationFile(string equipmentId, int year)
        {
            var itemIssueList = _dbContext.Db.Queryable<TIM_WORK_FORM_ISSUE>()
                .InnerJoin<TIM_WORK_FORM>((formIssue, workForm) => formIssue.FORM_ID == workForm.FORM_ID)
                .Where((formIssue, workForm) => formIssue.FORM_VER_MAIN_ID == equipmentId && formIssue.ISSUE_STATE == "2" && workForm.CLASS_ID == "5" )
                .Select((formIssue, workForm) => new
                {
                    formIssue.ISSUE_FILE,
                    formIssue.ISSUE_DATE,
                    formIssue.ISSUE_YEAR,
                    formIssue.ISSUE_MONTH,
                    workForm.ISSUED_PLAN,
                    formIssue.ISSUE_ID,
                    formIssue.ISSUE_NAME
                }).ToList();
            
            var issueFileInfo = new List<IssueFileInfo>();
            var result = new List<DecontaminationFileDto>();
            itemIssueList.Where(p => p.ISSUE_YEAR == year).ForEach(item =>
            {
                var issueTime = Convert.ToDateTime(item.ISSUE_DATE.Split("至")[1]);
                issueFileInfo.Add(new IssueFileInfo
                {
                    ARCHIVING_TIME = issueTime,
                    ISSUE_FILE = item.ISSUE_FILE,
                    ISSUE_SOURCE = "tim",
                    ISSUE_ID = item.ISSUE_ID,
                    ISSUE_NAME = item.ISSUE_NAME,
                    FILE_TYPE = "PDF"
                });
            });

            var q2 = new List<DateTime?>();
            q2.AddRange(issueFileInfo.Select(p => p.ARCHIVING_TIME));
            var a2 = q2.Where(p => p.ToString().IsNotNullOrEmpty()).ToArray().Distinct();
            var b2 = new List<string>();
            a2.ForEach(i =>
            {
                b2.Add(Convert.ToDateTime(i).ToString("yyyy-MM-dd"));
            });
            b2.Distinct().ForEach(item =>
            {
                result.Add(new DecontaminationFileDto
                {
                    ISSUE_SOURCE = "tim",
                    DATE = item,
                    ISSUE_FILES = issueFileInfo.Where(p => Convert.ToDateTime(p.ARCHIVING_TIME).ToString("yyyy-MM-dd") == item).Select(i => new IssueFile
                    {
                        ISSUE_ID = i.ISSUE_ID,
                        ISSUE_NAME = i.ISSUE_NAME,
                        ISSUE_FILE = i.ISSUE_FILE,
                        ISSUE_TYPE = i.FILE_TYPE
                    }).ToList()
                });
            });
            return result;
        }


        public void EditImplement(EMS_IMPLEMENT_INFO implement)
        {
            var old = _dbContext.Db.Queryable<EMS_IMPLEMENT_INFO>()
                .Where(p => p.IMPLEMENT_ID == implement.IMPLEMENT_ID)
                .First();
            if (old != null)
            {
                implement.IMPLEMENT_STATE = old.IMPLEMENT_STATE;
                _dbContext.Db.Updateable(implement).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommand();
            }
            else
            {
                throw new BizException("不存在该使用记录");
            }
        }

        public EMS_IMPLEMENT_INFO CreateImplement(EMS_IMPLEMENT_INFO implement)
        {
            implement.IMPLEMENT_ID = IDGenHelper.CreateGuid();
            implement.IMPLEMENT_STATE = "1";
            var result = _dbContext.Db.Insertable(implement).ExecuteReturnEntity();
            return result;
        }

        public void DeleteImplement(string id)
        {
            var implement = _dbContext.Db.Queryable<EMS_IMPLEMENT_INFO>()
                    .Where(p => p.IMPLEMENT_ID == id)
                    .First();
            if (implement != null)
            {
                implement.IMPLEMENT_STATE = "2";
                _dbContext.Db.Updateable(implement).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommand();
            }
        }

        public List<EMS_IMPLEMENT_INFO> GetImplements(string equipmentId, DateTime? startTime, DateTime? endTime, string? context)
        {

            var result = new List<EMS_IMPLEMENT_INFO>();
            _dbContext.Db.Queryable<EMS_IMPLEMENT_INFO>()
           .Where(x => x.IMPLEMENT_STATE == "1")
           .Where(x => x.EQUIPMENT_ID == equipmentId)
           .WhereIF(context.IsNotNullOrEmpty(), x => x.IMPLEMENT_CONTEXT.ToLower().Contains(context.ToLower()))
           .WhereIF(startTime.HasValue, x => x.IMPLEMENT_DATA >= startTime)
           .WhereIF(endTime.HasValue, x => x.IMPLEMENT_DATA <= endTime)
           .ForEach(x => result.Add(x), 100);
            return result;
        }

        /// <summary>
        /// 查询使用记录附件
        /// </summary>
        /// <param name="implementId"></param>
        /// <returns></returns>
        public List<EMS_DOC_INFO> GetImplementDocs(string implementId)
        {
            var result = new List<EMS_DOC_INFO>();
            var files = GetEquipmentDocs(implementId, "使用记录", null);
            result.AddRange(files);
            return result;
        }

        /// <summary>
        /// 查询保养记录的附件
        /// </summary>
        /// <param name="maintainId"></param>
        /// <returns></returns>
        public List<EMS_DOC_INFO> GetMaintainDocs(string maintainId)
        {
            var result = new List<EMS_DOC_INFO>();
            var files = GetEquipmentDocs(maintainId, "保养记录", null);
            result.AddRange(files);

            var maintain = _dbContext.Db.Queryable<EMS_MAINTAIN_INFO>()
                    .Where(p => p.MAINTAIN_ID == maintainId && p.MAINTAIN_STATE == "1")
                    .First();
            if (maintain == null)
            {
                return result;
            }
            result.AddRange(EvnetMonitoringFiles(maintain));
            return result;
        }
        /// <summary>
        /// 查询维修记录的附件
        /// </summary>
        /// <param name="repairId"></param>
        /// <returns></returns>
        public List<EMS_DOC_INFO> GetRepairDocs(string repairId)
        {
            var result = new List<EMS_DOC_INFO>();
            var files = GetEquipmentDocs(repairId, "维修记录", null);
            result.AddRange(files);

            var repair = _dbContext.Db.Queryable<EMS_REPAIR_INFO>()
                    .Where(p => p.REPAIR_ID == repairId && p.REPAIR_STATE == "1")
                    .First();
            if (repair == null)
            {
                return result;
            }
            result.AddRange(EvnetMonitoringFiles(repair));
            return result;
        }

        /// <summary>
        /// 查询校准记录附件
        /// </summary>
        /// <param name="correctId"></param>
        /// <returns></returns>
        public List<EMS_DOC_INFO> GetCorrectDocs(string correctId)
        {
            var result = new List<EMS_DOC_INFO>();
            var files = GetEquipmentDocs(correctId, "校准记录", null);
            result.AddRange(files);

            var correct = _dbContext.Db.Queryable<EMS_CORRECT_INFO>()
                    .Where(p => p.CORRECT_ID == correctId && p.CORRECT_STATE == "1")
                    .First();
            if (correct == null)
            {
                return result;
            }
            result.AddRange(EvnetMonitoringFiles(correct));
            result.AddRange(BeEvnetMonitoringFiles(correct));
            return result;
        }

        /// <summary>
        /// 查询比对记录附件
        /// </summary>
        /// <param name="comparisonId"></param>
        /// <returns></returns>
        public List<EMS_DOC_INFO> GetComparisonDocs(string comparisonId)
        {
            var result = new List<EMS_DOC_INFO>();
            var files = GetEquipmentDocs(comparisonId, "比对记录", null);
            result.AddRange(files);

            var comparison = _dbContext.Db.Queryable<EMS_COMPARISON_INFO>()
                    .Where(p => p.COMPARISON_ID == comparisonId && p.STATE == "1")
                    .First();
            if (comparison == null)
            {
                return result;
            }
            result.AddRange(BeEvnetMonitoringFiles(comparison));
            return result;
        }

        /// <summary>
        /// 查询性能验证记录附件
        /// </summary>
        /// <param name="verificationId"></param>
        /// <returns></returns>
        public List<EMS_DOC_INFO> GetVerificationDocs(string verificationId)
        {
            var result = new List<EMS_DOC_INFO>();
            var files = GetEquipmentDocs(verificationId, "性能验证记录", null);
            result.AddRange(files);

            var verification = _dbContext.Db.Queryable<EMS_VERIFICATION_INFO>()
                    .Where(p => p.VERIFICATION_ID == verificationId && p.STATE == "1")
                    .First();
            if (verification == null)
            {
                return result;
            }
            result.AddRange(BeEvnetMonitoringFiles(verification));
            return result;
        }


        /// <summary>
        /// 查询事务项填写记录
        /// </summary>
        /// <param name="equipmentId"></param>
        /// <param name="transactionClass"></param>
        /// <returns></returns>
        public List<ExecRecord> GetTransactionFillingRecords(string equipmentId, string transactionClass)
        {
            var result = new List<ExecRecord>();
            var users = _dbContext.Db.Queryable<SYS6_USER>().ToDictionary<string>(x => x.USER_NO, x => x.USERNAME);
            var execRecords = _dbContext.Db.Queryable<TIM_WORK_EXEC_LIST>()
                .LeftJoin<TIM_WORK_INFO>((exce, work) => exce.WORK_ID == work.WORK_ID)
                .LeftJoin<TIM_WORK_FORM>((exce, work, form) => exce.FORM_ID == form.FORM_ID)
                .LeftJoin<TIM_WORK_FORM_VER>((exce, work, form, formVer) => exce.FORM_VER_MAIN_ID == formVer.FORM_VER_ID)
                .Where((exce, work, form, formVer) => exce.WORK_MAINID == equipmentId)
                .Where((exce, work, form, formVer) => exce.WEXEC_STATE != "0" && exce.WEXEC_STATE != "10")
                .Where((exce, work, form, formVer) => form.CLASS_ID == transactionClass)
                .Select<ExecRecord>()
                .ToList();
            if (execRecords.Count() == 0)
            {
                return new List<ExecRecord>();
            }
            else
            {
                execRecords.ForEach(rec =>
                {
                    rec.CLASS_NAME = rec.CLASS_ID switch
                    {
                        "1" => "使用",
                        "2" => "保养",
                        "3" => "维修",
                        "4" => "校准",
                        _ => "其他"
                    };
                    rec.WORK_PLAN_TYPE = rec.WORK_PLAN_TYPE switch
                    {
                        "1" => "日保养",
                        "2" => "月保养",
                        "3" => "季度保养",
                        "4" => "年保养",
                        _ => ""
                    };
                    rec.WORK_NAME = rec.WORK_NAME.Replace("\r\n", "").Trim();
                    rec.WEXEC_PERSON = rec.WEXEC_PERSON.IsNotNullOrEmpty() ? users[rec.WEXEC_PERSON] : "";
                });

                result.AddRange(execRecords);
            }
            return result;
        }


        /// <summary>
        /// 查询设备相关的事务记录
        /// </summary>
        /// <param name="equipmentId"></param>
        /// <returns></returns>
        public List<TransactionForm> GetTransactions(string equipmentId)
        {
            var transactionFormItmes = _dbContext.Db.Queryable<TIM_WORK_INFO>().Select<TransactionItem>().ToList();
            var forms = _dbContext.Db.Queryable<TIM_FORM_MAIN_INFO>()
                .InnerJoin<TIM_WORK_FORM>((main, form) => main.FORM_ID == form.FORM_ID)
                .InnerJoin<TIM_WORK_FORM_VER>((main, form, formVer) => form.FORM_ID == formVer.FORM_ID)
                .Where((main, form, formVer) => form.FORM_STATE == "1")
                .Where((main, form, formVer) => formVer.FORM_VER_STATE == "6")
                .Where((main, form, formVer) => main.WORK_MAINID == equipmentId)
                .Select<TransactionForm>()
                .ToList();
            foreach (var form in forms)
            {
                form.CLASS_NAME = form.CLASS_ID switch
                {
                    "1" => "使用",
                    "2" => "保养",
                    "3" => "维修",
                    "4" => "校准",
                    _ => "其他"
                };

                var equipmentTransactionFormItmes =
                    transactionFormItmes.Where(x => x.FORM_VER_ID == form.FORM_VER_ID).ToList();
                foreach (var equipmentTransactionFormItme in equipmentTransactionFormItmes)
                {
                    equipmentTransactionFormItme.WORK_NAME = equipmentTransactionFormItme.WORK_NAME.Replace("\r\n", "").Trim();

                    equipmentTransactionFormItme.WORK_PLAN_TYPE = equipmentTransactionFormItme.WORK_PLAN_TYPE switch
                    {
                        "1" => "日保养",
                        "2" => "月保养",
                        "3" => "季度保养",
                        "4" => "年保养",
                        _ => ""
                    };
                    equipmentTransactionFormItme.TRANSACTION_ITEM_CLASS = equipmentTransactionFormItme.WORK_PLAN_TYPE.IsNullOrEmpty() ? $"【{form.CLASS_NAME}】" : $"【{equipmentTransactionFormItme.WORK_PLAN_TYPE}】";
                }
                form.TransactionItems.AddRange(equipmentTransactionFormItmes);
            }
            return forms;
        }

        /// <summary>
        /// 查询变更记录附件
        /// </summary>
        /// <param name="changeId"></param>
        /// <returns></returns>
        public List<EMS_DOC_INFO> GetChangeDocs(string changeId)
        {
            var result = new List<EMS_DOC_INFO>();
            var files = GetEquipmentDocs(changeId, "变更记录", null);
            result.AddRange(files);
            return result;
        }


        private List<EMS_DOC_INFO> GetEquipmentDocs(string docInfoId, string docClass, string? equipmentId)
        {
            var result = new List<EMS_DOC_INFO>();
            _dbContext.Db.Queryable<EMS_DOC_INFO>()
                .WhereIF(equipmentId.IsNotNullOrEmpty(), x => x.EQUIPMENT_ID == equipmentId)
                .Where(x => x.DOC_STATE == "1")
                .Where(x => x.DOC_INFO_ID == docInfoId)
                .Where(x => x.DOC_CLASS == docClass)
                .ForEach(x => result.Add(x));
            return result;
        }
        
        
        /// <summary>
        /// 具有产生事件的监控，同步更新回数据库
        /// </summary>
        /// <param name="records"></param>
        /// <typeparam name="T"></typeparam>
        private void EventMonitoring<T>(List<T> records)  where  T :class , IRecordEvent ,new ()
        {
            var no = records.Select(x => x.GetNo());
            var correctRelation = _dbContext.Db.Queryable<EMS_CORRECT_INFO>()
                .Where(p => p.CORRECT_STATE != "2")
                .Where(p=> no.Contains(p.RELATION_EVENT))
                .Select(p => p.RELATION_EVENT)
                .ToList();
            var comparisonRelation = _dbContext.Db.Queryable<EMS_COMPARISON_INFO>()
                .Where(p => p.COMPARISON_STATE != "2")
                .Where(p=> no.Contains(p.RELATION_EVENT))
                .Select(p => p.RELATION_EVENT)
                .ToList();
            var verificationRelation = _dbContext.Db.Queryable<EMS_VERIFICATION_INFO>()
                .Where(p => p.VERIFICATION_STATE != "2")
                .Where(p=> no.Contains(p.RELATION_EVENT))
                .Select(p => p.RELATION_EVENT)
                .ToList();
            
            foreach (var record in records)
            {
                if (record.OCCUR_EVENT.IsNullOrEmpty())
                {
                    record.OCCUR_EVENT = "";
                }
                var occurEvent = "";
                if (correctRelation.Contains(record.GetNo()))
                {
                    record.IF_CORRECT = "1";
                    occurEvent += "校准;";
                }
                if (comparisonRelation.Contains(record.GetNo()))
                {
                    record.IF_COMPARISON = "1";
                    occurEvent += "比对;";
                }
                if (verificationRelation.Contains(record.GetNo()))
                {
                    record.IF_VERIFICATION = "1";
                    occurEvent += "性能验证;";
                }
                if (record.OCCUR_EVENT.Contains("质控"))
                {
                    occurEvent += "质控;";
                }
                if (occurEvent.Length > 0)
                {
                    occurEvent = occurEvent.Substring(0, occurEvent.Length - 1);
                }
                record.OCCUR_EVENT = occurEvent;
                _dbContext.Db.Updateable(record).ExecuteCommand();
            }
        }
        /// <summary>
        /// 产生事件相关的图片
        /// </summary>
        /// <param name="record"></param>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        private List<EMS_DOC_INFO> EvnetMonitoringFiles<T>(T record) where T : class, IRecordEvent, new()
        {
            var result = new List<EMS_DOC_INFO>();
            if (record.OCCUR_EVENT != null)
            {
                if (record.OCCUR_EVENT.Contains("校准"))
                {
                    var correct = _dbContext.Db.Queryable<EMS_CORRECT_INFO>()
                        .Where(p => p.RELATION_EVENT == record.GetNo() && p.CORRECT_STATE == "1")
                        .First();
                    if (correct is not null)
                    {
                        var correctFiles = GetEquipmentDocs(correct.CORRECT_ID, "校准记录", null);
                        result.AddRange(correctFiles);
                    }
                }
                if (record.OCCUR_EVENT.Contains("比对"))
                {
                    var comparison = _dbContext.Db.Queryable<EMS_COMPARISON_INFO>()
                        .Where(p => p.RELATION_EVENT == record.GetNo() && p.COMPARISON_STATE == "1")
                        .First();
                    if (comparison != null)
                    {
                        var comparisonFiles = GetEquipmentDocs(comparison.COMPARISON_ID, "比对记录", null);
                        result.AddRange(comparisonFiles);
                    }
                }
                if (record.OCCUR_EVENT.Contains("性能验证"))
                {
                    var verification = _dbContext.Db.Queryable<EMS_VERIFICATION_INFO>()
                        .Where(p => p.RELATION_EVENT == record.GetNo() && p.VERIFICATION_STATE == "1")
                        .First();
                    if (verification != null)
                    {
                        var comparisonFiles = GetEquipmentDocs(verification.VERIFICATION_ID, "性能验证记录", null);
                        result.AddRange(comparisonFiles);
                    }
                }
            }
            
            return result;
        }
        /// <summary>
        /// 被产生事件相关的图片
        /// </summary>
        /// <param name="record"></param>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        private List<EMS_DOC_INFO> BeEvnetMonitoringFiles<T>(T record) where T : class, IBeRecordEvent, new()
        {
            var result = new List<EMS_DOC_INFO>();
            if (record.RELATION_EVENT != null)
            {
                if (record.RELATION_EVENT.Contains("BY"))
                {
                    var maintain = _dbContext.Db.Queryable<EMS_MAINTAIN_INFO>()
                        .Where(p => p.MAINTAIN_NO == record.RELATION_EVENT && p.MAINTAIN_STATE == "1")
                        .First();
                    if (maintain != null)
                    {
                        var maintainFiles = GetEquipmentDocs(maintain.MAINTAIN_ID, "保养记录", null);
                        result.AddRange(maintainFiles);
                    }
                }
                if (record.RELATION_EVENT.Contains("WX"))
                {
                    var repair = _dbContext.Db.Queryable<EMS_REPAIR_INFO>()
                        .Where(p => p.REPAIR_NO == record.RELATION_EVENT && p.REPAIR_STATE == "1")
                        .First();
                    if (repair != null)
                    {
                        var repairFiles = GetEquipmentDocs(repair.REPAIR_ID, "维修记录", null);
                        result.AddRange(repairFiles);
                    }
                }
                if (record.RELATION_EVENT.Contains("JZ"))
                {
                    var correct = _dbContext.Db.Queryable<EMS_CORRECT_INFO>()
                        .Where(p => p.CORRECT_NO == record.RELATION_EVENT && p.CORRECT_STATE == "1")
                        .First();
                    if (correct != null)
                    {
                        var correctFiles = GetEquipmentDocs(correct.CORRECT_ID, "校准记录", null);
                        result.AddRange(correctFiles);
                    }
                }
            }
            
            return result;
        }

    }
}
