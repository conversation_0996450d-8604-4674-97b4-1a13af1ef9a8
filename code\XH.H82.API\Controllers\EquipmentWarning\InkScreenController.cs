﻿using System.ComponentModel.DataAnnotations;
using AutoMapper;
using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Serilog;
using SqlSugar;
using XH.H82.API.Extensions;
using XH.H82.IServices;
using XH.H82.IServices.DeviceDataRefresh;
using XH.H82.Models.BusinessModuleClient;
using XH.H82.Models.DeviceRelevantInformation;
using XH.H82.Models.DeviceRelevantInformation.Dto;
using XH.H82.Models.Dtos.Tree;
using XH.H82.Models.Entities.InkScreen;
using XH.H82.Models.InkScreenTemplate;
using XH.H82.Models.InkScreenTemplate.Dto;
using XH.H82.Models.SugarDbContext;
using XH.H82.Services;
using XH.H82.Services.DeviceDataRefresh;
using XH.H82.Services.InkScreen;
using XH.LAB.UTILS.Helpers;
using XH.LAB.UTILS.Interface;

namespace XH.H82.API.Controllers.EquipmentWarning
{
    /// <summary>
    /// 墨水屏设计
    /// </summary>
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize]
    public class InkScreenController : ControllerBase
    {
        private readonly ISqlSugarUow<SugarDbContext_Master> _dbContext;
        private IInkScreenService _inkScreenService;
        private IInkScreenTemplateService _inkScreenTemplateService;
        private IAuthorityService2 _authorityService; 
        private IMapper _mapper;
        private IConfiguration _configuration;
        private H115Client h115Client { get; set; }
        InkScreenTemplateContext _inkScreenTemplateContext;
        public InkScreenController(IHttpContextAccessor httpContext, IConfiguration configuration, IInkScreenService inkScreenService, IInkScreenTemplateService inkScreenTemplateService, IMapper mapper, ISqlSugarUow<SugarDbContext_Master> dbContext, InkScreenTemplateContext inkScreenTemplateContext, IAuthorityService2 authorityService)
        {
            h115Client = new(configuration["H115"], httpContext);
            _configuration = configuration;
            _inkScreenService = inkScreenService;
            _inkScreenTemplateService = inkScreenTemplateService;
            _mapper = mapper;
            _dbContext = dbContext;
            _inkScreenTemplateContext = inkScreenTemplateContext;
            _authorityService = authorityService;
        }

        /// <summary>
        /// 获取所有基础属性
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [CustomResponseType(typeof(List<TemplateAttribute>))]
        public IActionResult GetAllTemplateAttributes()
        {
            var result = _inkScreenTemplateService.GetAllTemplateAttributes();
            return Ok(result.ToResultDto());
        }


        /// <summary>
        /// 查询当权科室下，用户可以查询的墨水屏模板
        /// </summary>
        /// <param name="labId">科室id</param>
        /// <param name="groupId">检验专业组id</param>
        /// <param name="templateName">模板名称</param>
        /// <returns></returns>
        [HttpGet]
        [CustomResponseType(typeof(List<TemplatesDto>))]
        public IActionResult GetTemplatesTest1([Required] string labId, string? groupId, string? templateName)
        {
            var equipmentContext = new EquipmentContext(_dbContext);
            var templates = _inkScreenTemplateContext.GetTemplates(labId, groupId, templateName);

            var result = _mapper.Map<List<TemplatesDto>>(templates);
            foreach (var item in result)
            {
                var groups = item.GetGroups();
                foreach (var group in groups)
                {
                    item.APPLICATION_GROUPS_NAME += $"{equipmentContext.ExchangeProfessionalGroupName(group)};";
                }
                if (item.APPLICATION_GROUPS_NAME.Length > 0)
                {
                    item.APPLICATION_GROUPS_NAME = item.APPLICATION_GROUPS_NAME.Remove(item.APPLICATION_GROUPS_NAME.Length - 1);
                }

                var tempContent = JsonConvert.DeserializeObject<List<Template>>(item.TEMPLATE_CONTENT);
                for (int i = 0; i < tempContent.Count(); i++)
                {
                    if (tempContent[i].cols.Count() == 1)
                    {
                        tempContent[i].cols.Add(new TemplateAttribute("", ""));
                        continue;
                    }
                    if (tempContent[i].cols.Count() == 2)
                    {
                        var template = tempContent[i].cols[1];
                        tempContent[i].cols[1] = new TemplateAttribute("", "");
                        tempContent[i].cols.Add(template);
                        tempContent[i].cols.Add(new TemplateAttribute("", ""));
                        continue;
                    }
                }
                item.TEMPLATE_CONTENT = JsonConvert.SerializeObject(tempContent);
            }
            return Ok(result.ToResultDto());
        }

        /// <summary>
        /// 添加新的墨水屏模板
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [CustomResponseType(typeof(bool))]
        public IActionResult AddTemplate([FromBody] AddTemplateInput input)
        {
            _inkScreenTemplateService.AddTemplate(input.templateName, input.remark);
            return Ok(true.ToResultDto());
        }

        /// <summary>
        /// 修改墨水屏模板名称及备注
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPut("{templateId}")]
        [CustomResponseType(typeof(bool))]
        public IActionResult UpdateTemplate([Required] string templateId, [FromBody] UpdateTemplateInput input)
        {
            _inkScreenTemplateService.UpdateTemplate(templateId, input.templateName, input.remark);
            return Ok(true.ToResultDto());
        }

        /// <summary>
        /// 保存模板内容
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPut("{templateId}")]
        [CustomResponseType(typeof(bool))]
        public IActionResult SaveTemplate([Required] string templateId, [FromBody] SaveTemplateInput input)
        {

            //恢复初始化
            if (input.tempContent.IsNullOrEmpty())
            {
                var initTemplateJson = JsonConvert.SerializeObject(TemplateAContentInit.Default());

                _inkScreenTemplateService.SaveTemplate(templateId, initTemplateJson, input.tempTitle, input.setAbnormal, input.setQRCode, input.SetWireframe);
                return Ok(true.ToResultDto());
            }
            else
            {
                //检验入参的模板格式
                try
                {
                    var tryTempContentToJson = JsonConvert.DeserializeObject<List<Template>>(input.tempContent);
                    
                    if (tryTempContentToJson.Count() == 8)
                    {
                        foreach (var item in tryTempContentToJson)
                        {
                            if (item.cols.Count() == 2 || item.cols.Count() == 4)
                            {
                                if (item.cols.Count() == 2)
                                {
                                    item.cols[1].colTitle = "";
                                    item.cols[1].colKey = "";
                                }
                                if (item.cols.Count() == 4)
                                {
                                    item.cols[1].colTitle = "";
                                    item.cols[1].colKey = "";
                                    item.cols[3].colTitle = "";
                                    item.cols[3].colKey = "";
                                }
                            }
                            else
                            {
                                throw new BizException($"模板列数必须2列/4列");
                            }
                        }
                    }
                    else
                    {
                        throw new BizException($"可编辑模板行数必须8行");
                    }
                }
                catch (Exception e)
                {
                    Log.Error($"模板格式异常：{input.tempContent}");
                    if (e is BizException)
                    {
                        throw new ArgumentException($"模板格式异常：{e.Message}=>{input.tempContent}");
                    }
                    throw new ArgumentException($"模板格式异常：{input.tempContent}");
                }


                var tempContent = JsonConvert.DeserializeObject<List<Template>>(input.tempContent);
                for (int i = 0; i < tempContent.Count(); i++)
                {
                    if (tempContent[i].cols.Count() == 2)
                    {
                        tempContent[i].cols.RemoveAt(1);
                        continue;
                    }
                    if (tempContent[i].cols.Count() == 4)
                    {
                        tempContent[i].cols.RemoveAt(1);
                        tempContent[i].cols.RemoveAt(2);
                        continue;
                    }
                }
                var tempContentJson = JsonConvert.SerializeObject(tempContent);
                _inkScreenTemplateService.SaveTemplate(templateId, tempContentJson, input.tempTitle, input.setAbnormal, input.setQRCode, input.SetWireframe);
                return Ok(true.ToResultDto());

            }


        }

        /// <summary>
        /// 软删除墨水屏模板名称及备注
        /// </summary>
        /// <returns></returns>
        [HttpDelete("{templateId}")]
        [CustomResponseType(typeof(bool))]
        public IActionResult DeleteTemplate([Required] string templateId)
        {
            _inkScreenTemplateService.DeleteTemplate(templateId);
            return Ok(true.ToResultDto());
        }

        /// <summary>
        /// 复制模板
        /// </summary>
        /// <returns></returns>
        [HttpGet("{templateId}")]
        [AllowAnonymous]
        [CustomResponseType(typeof(bool))]
        public IActionResult CopyTemplate([Required] string templateId)
        {
            _inkScreenTemplateService.CopyTemplate(templateId);
            return Ok(true.ToResultDto());
        }



        /// <summary>
        /// 查询当权科室下，用户可以查询的墨水屏模板
        /// </summary>
        /// <param name="labId">科室id</param>
        /// <param name="groupId">检验专业组id</param>
        /// <param name="templateName">模板名称</param>
        /// <returns></returns>
        [HttpGet]
        [CustomResponseType(typeof(List<TemplatesDto>))]
        public IActionResult GetTemplates([Required] string labId, string? groupId, string? templateName)
        {
            var equipmentContext = new EquipmentContext(_dbContext);
            var templates = _inkScreenTemplateService.GetTemplates(labId, groupId, templateName);

            var result = _mapper.Map<List<TemplatesDto>>(templates);
            foreach (var item in result)
            {
                var groups = item.GetGroups();
                foreach (var group in groups)
                {
                    item.APPLICATION_GROUPS_NAME += $"{equipmentContext.ExchangeProfessionalGroupName(group)};";
                }
                if (item.APPLICATION_GROUPS_NAME.Length > 0)
                {
                    item.APPLICATION_GROUPS_NAME = item.APPLICATION_GROUPS_NAME.Remove(item.APPLICATION_GROUPS_NAME.Length - 1);
                }
                var tempContent = JsonConvert.DeserializeObject<List<Template>>(item.TEMPLATE_CONTENT);
                for (int i = 0; i < tempContent.Count(); i++)
                {
                    if (tempContent[i].cols.Count() == 1)
                    {
                        tempContent[i].cols.Add(new TemplateAttribute("", ""));
                        continue;
                    }
                    if (tempContent[i].cols.Count() == 2)
                    {
                        var template = tempContent[i].cols[1];
                        tempContent[i].cols[1] = new TemplateAttribute("", "");
                        tempContent[i].cols.Add(template);
                        tempContent[i].cols.Add(new TemplateAttribute("", ""));
                        continue;
                    }
                }
                item.TEMPLATE_CONTENT = JsonConvert.SerializeObject(tempContent);
            }
            return Ok(result.ToResultDto());
        }
        
        /// <summary>
        /// 专业组下拉选项
        /// </summary>
        /// <param name="labId"></param>
        /// <returns></returns>
        [HttpGet]
        [CustomResponseType(typeof(List<GroupDto>))]
        public IActionResult GetGroups([Required] string labId)
        {
            var result = new List<GroupDto>();
            var authorityContext = new AuthorityContext(_dbContext,_authorityService);
            var user = User.ToClaimsDto();
            authorityContext.SetUser(user,labId);
            var groups = authorityContext.GetAccessibleProfessionalGroups(labId, null).Where(x => x.PGROUP_STATE == "1");
            foreach (var group in groups)
            {
                result.Add(new(group.PGROUP_ID, group.PGROUP_NAME ?? ""));
            }
            return Ok(result.ToResultDto());
        }

        /// <summary>
        /// 返回专业组树
        /// </summary>
        /// <param name="labId"></param>
        /// <returns></returns>
        [HttpGet]
        [CustomResponseType(typeof(List<OrganizationalTreeNode>))]
        public IActionResult GetGroupsTree([Required] string labId, string? groupId)
        {
            var tree = OrganizationalTreeNode.GetRootNode("0", "根节点");
            var authorityContext = new AuthorityContext(_dbContext,_authorityService);
            var equipmentContext = new EquipmentContext(_dbContext);
            var user  = User.ToClaimsDto();
            authorityContext.SetUser(user,labId);
            var groups = authorityContext.GetAccessibleProfessionalGroups(labId, null)
                .WhereIF(groupId.IsNotNullOrEmpty(), x => x.PGROUP_ID == groupId)
                .Where(x => x.PGROUP_STATE == "1");
            var labName = equipmentContext.ExchangeLabName(labId);
            var labNode = new OrganizationalTreeNode()
            {
                NodeId = labId,
                NodeName = labName,
                NodeType = OrganizationalTreeNodeEnum.Lab,
                NodeState = "0",
                NodeStateName = "未应用",
                NodeTypeName = OrganizationalTreeNodeEnum.Lab.ToDesc(),
                isRoot = false,
            };
            tree.ChildrenNode.Add(labNode);

            foreach (var group in groups)
            {
                if (group.MGROUP_ID.IsNullOrEmpty())
                {
                    AddOrganizationalTreeNode(labNode, group.PGROUP_ID, group.PGROUP_NAME, InTemplate);
                }
                else
                {
                    var mGroupNode = tree.ChildrenNode.FirstOrDefault(x => x.NodeId == group.MGROUP_ID);
                    if (mGroupNode is null)
                    {
                        var newMGroupNode = new OrganizationalTreeNode()
                        {
                            NodeId = group.MGROUP_ID,
                            NodeName = equipmentContext.ExchangeManageGroupName(group.MGROUP_ID),
                            NodeType = OrganizationalTreeNodeEnum.Mgroup,
                            NodeState = "0",
                            NodeStateName = "未应用",
                            NodeTypeName = OrganizationalTreeNodeEnum.Mgroup.ToDesc(),
                            isRoot = false,
                        };
                        tree.ChildrenNode.Add(newMGroupNode);
                        AddOrganizationalTreeNode(newMGroupNode, group.PGROUP_ID, group.PGROUP_NAME, InTemplate);
                    }
                    else
                    {
                        AddOrganizationalTreeNode(mGroupNode, group.PGROUP_ID, group.PGROUP_NAME, InTemplate);
                    }
                }
            }
            tree.NodeStateTracking();
            return Ok(tree.ChildrenNode.ToResultDto());
        }

        /// <summary>
        /// 模板应用至专业组
        /// </summary>
        /// <param name="templateId">模板id</param>
        /// <param name="groups">专业组（复数）</param>
        /// <returns></returns>
        [HttpPut("{templateId}")]
        [CustomResponseType(typeof(bool))]
        public IActionResult ApplicationGroups([Required] string templateId, List<string> groups)
        {
            _inkScreenTemplateService.ApplicationGroups(templateId, groups);
            return Ok(true.ToResultDto());
        }

        /// <summary>
        /// 模板预览接口
        /// </summary>
        /// <param name="templateId">模板id</param>
        /// <param name="parameters">基础信息参数aaa;bbb</param>
        /// <returns></returns>
        [HttpGet("{templateId}")]
        public IActionResult GetTemplatePreview([Required] string templateId, string? parameters)
        {
            var idCardInfo = _inkScreenTemplateService.GetTemplatePreview(templateId, parameters);
            var result = h115Client.GetGenerateImgStream(idCardInfo.ToEinkChangeDisplay());
            return File(result, "image/jpeg", $"{Guid.NewGuid()}");
        }

        private void AddOrganizationalTreeNode(OrganizationalTreeNode node, string groupId, string groupName, Func<string, bool> action)
        {
            var result = action(groupId);
            var childrenNode = new OrganizationalTreeNode()
            {
                NodeId = groupId,
                NodeName = groupName,
                NodeType = OrganizationalTreeNodeEnum.Pgroup,
                NodeState = result ? "1" : "0",
                NodeStateName = result ? "已应用" : "未应用",
                NodeTypeName = OrganizationalTreeNodeEnum.Pgroup.ToDesc(),
                isRoot = false,
            };
            node.ChildrenNode.Add(childrenNode);
        }
        private bool InTemplate(string groupId)
        {
            if (_dbContext.Db.Queryable<EMS_INKSCREEN_TEMPLATE>().Count(x => x.PGROUP_SID.Contains(groupId)) > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        
        /// <summary>
        /// 墨水屏自定义模板下发功能
        /// </summary>
        /// <param name="mac">设备mac地址</param>
        /// <param name="values">模板信息于值</param>
        /// <param name="nextMaintainDateStatus">下次保养时间是否添加红色边框</param>
        /// <param name="nextCorrectDateStatus">下次校准时间是否添加红色边框</param>
        /// <param name="circumstance">设备运行状态：启用，停用，报废，未启用,待报废，待停用</param>
        /// <param name="meassage">异常信息 ： 只能放 设备需要保养 、 设备需要校准、证书需要维护</param>
        /// <returns></returns>
        [HttpPost("{mac}")]
        public IActionResult SetInkScreen(string mac ,List<TemplateDataAndValue> values ,bool nextMaintainDateStatus = false, bool nextCorrectDateStatus = false, string circumstance  = "启用" ,string meassage = "")
        {
            var idCardInfo = _inkScreenTemplateService.SetTemplateValue(values ,nextMaintainDateStatus,nextCorrectDateStatus, circumstance,meassage);
            if (_configuration.GetSection("IsAutoRefresh").Exists() && _configuration.GetSection("IsAutoRefresh").Value == "1")
            {
                try
                {
                    var rsp = h115Client.ChangeDisplay(idCardInfo.ToEinkChangeDisplay(mac));
                    Log.Information(JsonConvert.SerializeObject(rsp));
                }
                catch (Exception e)
                {
                    Log.Error($"调用下发墨水屏接口异常异常:{JsonConvert.SerializeObject(e.InnerException.ToString())}");
                }   
            }
            var result = h115Client.GetGenerateImgStream(idCardInfo.ToEinkChangeDisplay(mac));
            return File(result, "image/jpeg", $"{Guid.NewGuid()}.jpg");
        }
    }
}
