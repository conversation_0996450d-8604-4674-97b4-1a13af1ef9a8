﻿using H.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XH.H82.Base.Tree;
using XH.H82.Models.Dtos;

namespace XH.H82.IServices
{
    public interface ICurrencyService
    {
        PersonTreeDto GetPersonList(string mgroupId, string userNo, string hospitalId, string labId, string areaId);
        List<BasicDataDto> GetLabList(string hospitalId, string areaId);
        List<BasicDataDto> GetPersonTypeList();
        List<BasicDataDto> GetPostList();
        List<BasicDataDto> GetProfessionalClass();
        ResultDto AddPostInfo(string postName, string labId, string hospitalId, string userName);
        ResultDto DeletePostInfo(string BasciId);
        ResultDto AddPersonTypeInfo(string personTypeName, string labId, string hospitalId, string userName);
        ResultDto DeletePersonTypeInfo(string BasciId);
        List<BasicDataDto> GetPgroupPullList(string areaId, string userNo, string labId);


        List<ITreeNode> GetPullAreaPgroups(string? areaId, string labId);
        List<ITreeNode> GetPullPersons(string labId, string areaId, string mgroupId, string pgroupId, string modelId);

        public List<ITreeNode> GetEquipments(string? labId, string? areaId, string? mgroupId, string? pgroupId,
            string? modelId);

        public (RootNode rootNode, List<ITreeNode> nodes) GetISOBaseOrganizationTree(string labId);
        (RootNode rootNode, List<ITreeNode> nodes) GetISOLabBaseOrganizationTree(string labId);
    }
}
