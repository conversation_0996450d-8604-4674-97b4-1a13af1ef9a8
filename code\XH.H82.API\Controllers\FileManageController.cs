﻿using System.ComponentModel.DataAnnotations;
using EasyCaching.Core;
using H.Utility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.Extensions.Caching.Memory;
using XH.H82.IServices;
using XH.H82.Models.Dtos;
using XH.H82.Models.Dtos.SysDocDtos;
using XH.LAB.UTILS.Interface;

namespace XH.H82.API.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize]
    public class FileManageController : ControllerBase
    {
        private readonly IFileManageService _fileManageService;
        private readonly string file_preview_address;
        private readonly string file_upload_address;
        private readonly IConfiguration _configuration;
        private string currentLabKey = "XH:LIS:H82:CURRENTLAB:UserSelectedLab:";
        public FileManageController(IFileManageService fileManageService
            , IConfiguration configuration)
        {
            _fileManageService = fileManageService;
            _configuration = configuration;
            file_preview_address = _configuration["S54"];
            file_upload_address = _configuration["S28"];
        }

        /// <summary>
        /// 文档分类树结构
        /// </summary>
        /// <param name="labId">科室id</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetFileTree(string labId , bool isSmbl = false)
        {
            var claims = this.User.ToClaimsDto();
            var res = _fileManageService.GetFileTree(labId ,isSmbl);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        /// 文档管理页面专业组列表
        /// </summary>
        /// <param name="labId">当前所在的科室id</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetPgroupList(string labId)
        {
            var res = _fileManageService.GetPgroupList(labId);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   获取文档信息列表
        /// </summary>
        /// <param name="classId">文档树结构字段id</param>
        /// <param name="equipmentId">设备ID</param>
        /// <param name="pgroupId">专业组id</param>
        /// <param name="docType">文档类型（前端写死：项目SOP;仪器SOP）</param>
        /// <param name="keyword">检索</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetDocInfoList([BindRequired] string classId,/*[BindRequired]*/string equipmentId, string pgroupId, string docType, string keyword)
        {
            var claims = User.ToClaimsDto();
            var res = _fileManageService.GetDocInfoList(classId, pgroupId, docType, keyword, equipmentId);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   获取文档附件信息
        /// </summary>
        /// <param name="docId">文档id</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetDocFile([BindRequired] string docId)
        {
            var claims = User.ToClaimsDto();
            var res = _fileManageService.GetDocFile(docId, file_preview_address);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        /// 链接文档系统文件应用到设备系统
        /// </summary>
        /// <param name="filePath">文件信息</param>
        /// <param name="equipmentId"> 设备Id </param>
        /// <param name="docInfoId"> 如果是记录类型的链接文档  保养记录、维修记录、变更记录、校准记录、比对记录、性能验证记录、使用记录 需要传入记录Id ，SOP档案、设备说明书 侧传空  </param> 
        /// <param name="docClass"> SOP档案、设备说明书、保养记录、维修记录、变更记录、校准记录、比对记录、性能验证记录、使用记录</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadDocFile([BindRequired] List<DocFileDto> filePath, [BindRequired] string equipmentId, string docInfoId, string docClass)
        {
            var claims = User.ToClaimsDto();
            var userName = claims.HIS_NAME;
            var res = _fileManageService.UploadDocFile(filePath, docInfoId, docClass, equipmentId, userName, claims.HOSPITAL_ID);
            return Ok(res);
        }


        /// <summary>
        /// 查询文件系统内部的节点及文件列表
        /// </summary>
        /// <param name="labId">科室id</param>
        /// <param name="classId">文档菜单类型id</param>
        /// <param name="equipmentId">设备id</param>
        /// <param name="docClass">当前类型</param>
        /// <param name="docType"></param>
        /// <param name="pgroupId">专业组id</param>
        /// <param name="docName">文件名</param>
        /// <param name="docClassId">SOP分类</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult FindDocumentNodesAndDocuments([Required] string labId, [Required] string classId, [Required] string equipmentId, [Required] string firstmenukey, [Required] string docClass, string? docType, string? pgroupId, string? docName, SOPType? docClassId)
        {
            var result = _fileManageService.FindDoccumentNodesAndDocuments(labId, equipmentId, firstmenukey, docClass, classId, docType, pgroupId, docName, docClassId);

            return Ok(result.ToResultDto());
        }

       
        /// <summary>
        /// 添加设备SOP档案   链接文档系统
        /// </summary>
        /// <param name="equipmentId"></param>
        /// <param name="filePath"></param>
        /// <returns></returns>
        [HttpPost("{equipmentId}")]
        public IActionResult AddEquipmentSopFiles(string equipmentId, [BindRequired] List<DocFileDto> filePath)
        {
            _fileManageService.AddEquipmentSopFiles(equipmentId, filePath);
            return Ok(true.ToResultDto());
        }

        /// <summary>
        /// 获取设备sop文件列表
        /// </summary>
        /// <param name="equipmentId"></param>
        /// <returns></returns>
        [HttpGet("{equipmentId}")]
        public IActionResult GetEquipmentSopFiles([Required] string equipmentId)
        {

            var result = _fileManageService.GetEquipmentSopFils(equipmentId);
            return Ok(result.ToResultDto());
        }
    }
}
