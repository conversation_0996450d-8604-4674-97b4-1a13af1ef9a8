using Newtonsoft.Json;

namespace XH.H82.Models.BusinessModuleClient.Dto.IoTDevices;

/// <summary>
/// 实时监测记录
/// </summary>
public class DeviceMonitorRecord
{
    /// <summary>
    /// 电信设备唯一标识
    /// </summary>
    [JsonProperty("id")]
    public string Id { get; set; }
    /// <summary>
    /// 测点名称
    /// </summary>
    [JsonProperty("name")]
    public string Name { get; set; }
    /// <summary>
    /// 设备sn
    /// </summary>
    [JsonProperty("sn")]
    public string Sn { get; set; }
    /// <summary>
    /// 实验室id
    /// </summary>
    [JsonProperty("labId")]
    public long LabId { get; set; }
    /// <summary>
    /// 房间id
    /// </summary>
    [JsonProperty("roomId")]
    public long RoomId { get; set; }
    /// <summary>
    /// 测点id
    /// </summary>
    [JsonProperty("checkpointId")]
    public long CheckpointId { get; set; }
    
    /// <summary>
    /// 检测项数据集合
    /// </summary>
    [JsonProperty("monitors")] 
    public List<MonitorData>  Monitors { get; set; } = new ();
}