﻿using H.Utility.SqlSugarInfra;
using SqlSugar;

namespace XH.H82.Models.Entities.LIS6;


[DBOwner("XH_DATA")]
[SugarTable("LIS6_MSG_INFO", TableDescription = "")]
public class LIS6_MSG_INFO
{
    [SugarColumn(ColumnName = "MSG_ID")]
    public string MsgId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [SugarColumn(ColumnName = "HOSPITAL_ID")]
    public string HospitalId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [SugarColumn(ColumnName = "AREA_ID")]
    public string AreaId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [SugarColumn(ColumnName = "LAB_ID")]
    public string LabId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [SugarColumn(ColumnName = "MODULE_ID")]
    public string ModuleId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [SugarColumn(ColumnName = "MSG_CORRID")]
    public string MsgCorrid { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [SugarColumn(ColumnName = "RECEIVE_UNIT_ID")]
    public string ReceiveUnitId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [SugarColumn(ColumnName = "RECEIVE_UNIT_TYPE")]
    public string ReceiveUnitType { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [SugarColumn(ColumnName = "MAG_DATE")]
    public DateTime MagDate { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [SugarColumn(ColumnName = "MSG_CLASS")]
    public string? MsgClass { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [SugarColumn(ColumnName = "MSG_TYPE")]
    public string? MsgType { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [SugarColumn(ColumnName = "MSG_OPERATE")]
    public string? MsgOperate { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [SugarColumn(ColumnName = "MSG_TITLE")]
    public string? MsgTitle { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [SugarColumn(ColumnName = "MSG_LEVEL")]
    public string? MsgLevel { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [SugarColumn(ColumnName = "MSG_DISPOSE_URL")]
    public string? MsgDisposeUrl { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [SugarColumn(ColumnName = "MSG_DISPOSE_TYPE")]
    public string? MsgDisposeType { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [SugarColumn(ColumnName = "MSG_VALID_TIME")]
    public DateTime? MsgValidTime { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [SugarColumn(ColumnName = "SEND_PERSON")]
    public string? SendPerson { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [SugarColumn(ColumnName = "SEND_TIME")]
    public DateTime? SendTime { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [SugarColumn(ColumnName = "SEND_COMPUTER")]
    public string? SendComputer { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [SugarColumn(ColumnName = "RECEIVE_PERSON_TYPE")]
    public string? ReceivePersonType { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [SugarColumn(ColumnName = "RECEIVE_PERSON")]
    public string? ReceivePerson { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [SugarColumn(ColumnName = "RECEIVE_TIME")]
    public DateTime? ReceiveTime { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [SugarColumn(ColumnName = "RECEIVE_COMPUTER")]
    public string? ReceiveComputer { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [SugarColumn(ColumnName = "MSG_OVERTIME")]
    public double? MsgOvertime { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [SugarColumn(ColumnName = "MSG_STATE")]
    public string? MsgState { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [SugarColumn(ColumnName = "REMARK")]
    public string? Remark { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [SugarColumn(ColumnName = "SEND_UNIT_ID")]
    public string? SendUnitId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [SugarColumn(ColumnName = "DELAY_TIME")]
    public double? DelayTime { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [SugarColumn(ColumnName = "RECEIVE_UNIT_NAME")]
    public string? ReceiveUnitName { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [SugarColumn(ColumnName = "MSG_DISPOSE_URL_STYLE")]
    public string? MsgDisposeUrlStyle { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [SugarColumn(ColumnName = "MSG_WARNING_TIME")]
    public DateTime? MsgWarningTime { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [SugarColumn(ColumnName = "MSG_ALARM_TIME")]
    public DateTime? MsgAlarmTime { get; set; }
}
