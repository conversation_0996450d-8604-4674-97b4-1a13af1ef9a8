﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.DeactivationScrapping
{
    /// <summary>
    /// 提交输入
    /// </summary>
    /// <param name="applyType"></param>
    /// <param name="applyReason"></param>
    /// <param name="examinePerson"></param>
    /// <param name="examinePersonId"></param>
    /// <param name="scrapDate"></param>
    public record SubmitScapOrStopInput(string applyType, string applyReason, string examinePerson, string examinePersonId, DateTime scrapDate);

    /// <summary>
    /// 提交通过通过输入
    /// </summary>
    /// <param name="auditPerson"></param>
    /// <param name="auditPersonId"></param>
    public record SubmitPassInput(string auditPerson, string auditPersonId);

    /// <summary>
    /// 审核通过输入
    /// </summary>
    /// <param name="approvalPerson"></param>
    /// <param name="approvalPersonId"></param>
    public record AuditPassInput(string approvalPerson, string approvalPersonId);
}