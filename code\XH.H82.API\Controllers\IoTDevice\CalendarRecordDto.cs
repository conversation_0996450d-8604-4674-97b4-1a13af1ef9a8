﻿namespace XH.H82.API.Controllers.IoTDevice;

public class CalendarRecordDto
{
    /// <summary>
    /// 记录id
    /// </summary>
    public string Id { get; set; }
    /// <summary>
    /// 记录类型
    /// </summary>
    public string Type { get; set; }
    
    /// <summary>
    /// 记录类型 中文
    /// </summary>
    public string TypeName { get; set; }
    /// <summary>
    /// 标题（设备名称）
    /// </summary>
    public string Title { get; set; }
    /// <summary>
    /// 开始时间 （yyyy-MM-dd）
    /// </summary>
    public string BeginDate { get; set; }
    /// <summary>
    /// 结束时间（yyyy-MM-dd）
    /// </summary>
    public string EndDate { get; set; }
    
}