﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Security.Cryptography;
using System.Threading.Channels;
using XH.H82.Models.ViewDtos;
using SqlSugar;
using H.Utility.SqlSugarInfra;

namespace XH.H82.Models.Entities.Common
{
    [DBOwner("XH_SYS")]
    [SugarTable("LIS6_INSTRUMENT_ITEM_REAGENT")]
    public class LIS6_INSTRUMENT_ITEM_REAGENT
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string? REAGENT_ID { get; set; }
        public string? INSTRUMENT_ID { get; set; }
        public string? ITEM_ID { get; set; }
        public string? MATERIAL_ID { get; set; }
        public string? TEST_AMOUNT { get; set; }
        public string? REAGENT_STATE { get; set; }
        public string? REMARK { get; set; }
        public string? MATERIAL_TYPE { get; set; }
        public string? CHANNEL_ID { get; set; }
        public string? FIRST_RPERSON { get; set; }

        public DateTime? FIRST_RTIME { get; set; }

        public string? LAST_MPERSON { get; set; }

        public DateTime? LAST_MTIME { get; set; }
    }
}
