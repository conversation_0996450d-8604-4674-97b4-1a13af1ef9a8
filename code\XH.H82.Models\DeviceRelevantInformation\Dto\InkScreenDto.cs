﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XH.H82.Models.DeviceRelevantInformation.Enum;

namespace XH.H82.Models.DeviceRelevantInformation.Dto
{
    /// <summary>
    /// 新增墨水屏信息
    /// </summary>
    /// <param name="Mac">mac地址</param>
    /// <param name="InkScreenName">墨水屏名称</param>
    /// <param name="hosptialId">医疗机构id</param>
    /// <param name="Remark">备注</param>
    public record AddInkScreenInput([Required] string Mac, [Required] string InkScreenName, [Required] string hosptialId, string? Remark);


    /// <summary>
    /// 更新墨水屏信息
    /// </summary>
    /// <param name="Remark">备注</param>
    public record UpdateInkScreenInput(string? Remark);

    /// <summary>
    /// 墨水屏展示模型
    /// </summary>
    /// <param name="InkScreenId">墨水屏Id</param>
    /// <param name="LightState">亮灯状态</param>
    /// <param name="LightStateValue">亮灯状态中文</param>
    /// <param name="Mac">MAC地址</param>
    /// <param name="LineState">在线状态</param>
    /// <param name="LineStateValue">在线状态中文</param>
    /// <param name="Power">电量</param>
    /// <param name="LastPowerCheckPerson">最近一次检测人</param>
    /// <param name="LastPowerCheckDate">最近一次检时间</param>
    /// <param name="EquipmentState">设备状态</param>
    /// <param name="EquipmentStateValue">设备状态中文</param>
    /// <param name="EquipmentmCode">设备代号</param>
    /// <param name="GroupName">专业组</param>
    /// <param name="WarnMsg">报警信息</param>
    /// <param name="WarnTime">报警开始时间</param>
    /// <param name="WarnDuration">报警时长</param>
    /// <param name="Manufacturer">制造商</param>
    /// <param name="Dealer">供应商</param>
    /// <param name="InstallLocatin">安装位置</param>
    /// <param name="EquipmentResponsible">设备负责人</param>
    /// <param name="DeliveryPath">墨水屏下发时的实际样式</param>

    public record InkScreenDto(
        string InkScreenId,
        LightStateEnum LightState,
        string LightStateValue,
        string Mac,
        LineStateEnum LineState,
        string LineStateValue,
        string Power,
        int PowerWarn,
        string? LastPowerCheckPerson,
        DateTime? LastPowerCheckDate,
        string EquipmentId,
        string EquipmentState,
        string EquipmentStateValue,
        string EquipmentmCode,
        string GroupName,
        string WarnMsg,
        DateTime? WarnTime,
        int WarnDuration,
        string Manufacturer,
        string Dealer,
        string InstallLocatin,
        string EquipmentResponsible,
        string? DeliveryPath
        );

    /// <summary>
    /// 墨水屏-设备列表
    /// </summary>
    /// <param name="EquipmentId"> 设备id </param>
    /// <param name="EquipmentName"> 设备名称 </param>
    /// <param name="EquipmentCode"> 设备代号 </param>
    /// <param name="EquipmentModel"> 设备型号 </param>
    /// <param name="GroupName"> 专业组名 </param>
    public record InkScreenBindEquipmentDto(string EquipmentId, string EquipmentName, string EquipmentCode, string EquipmentModel, string GroupName);
    /// <summary>
    /// 历史报警信息
    /// </summary>
    /// <param name="EquipmentId">设备id</param>
    /// <param name="WarnRecordId">报警记录id</param>
    /// <param name="WarnMsg">报警信息</param>
    /// <param name="WarnTime">报警时间</param>
    /// <param name="DealWithPerpon">处理人</param>
    /// <param name="DealwithTime">处理时间</param>
    public record WarnRecordDto(string EquipmentId, string WarnRecordId, string WarnMsg, EquipmentSummaryEnum warnTpye, DateTime WarnTime, string? DealWithPerpon, DateTime? DealwithTime, DealWithStateEnum DealWithState);

    /// <summary>
    /// 墨水屏绑定信息列表
    /// </summary>
    /// <param name="InkScreenId">墨水屏id</param>
    /// <param name="BandStateValue">绑定状态</param>
    /// <param name="SortNo">排序号</param>
    /// <param name="Mac">mac地址</param>
    /// <param name="EquipmentCode">设备代号</param>
    /// <param name="GroupName">专业组</param>
    /// <param name="EquipmentId">设备id</param>
    public record InkScreenBindDto(string InkScreenId, string BandStateValue, string? SortNo, string Mac, string? EquipmentCode, string? GroupName, string EquipmentId);

    /// <summary>
    ///设备绑定标识
    /// </summary>
    /// <param name="EquipmentId">设备id</param>
    public record BindInkScreen(string EquipmentId);
}
