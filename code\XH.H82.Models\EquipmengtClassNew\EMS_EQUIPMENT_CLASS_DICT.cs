﻿using H.Utility.SqlSugarInfra;
using SqlSugar;

namespace XH.H82.Models.EquipmengtClassNew;

/// <summary>
/// 设备分类表
/// </summary>
[DBOwner("XH_OA")]
[SugarTable("EMS_EQUIPMENT_CLASS_DICT", TableDescription = "设备分类表")]
public class EMS_EQUIPMENT_CLASS_DICT
{
    /// <summary>
    /// 设备分类id
    /// </summary>
    [SugarColumn(IsPrimaryKey = true,ColumnName = "CLASS_ID")]
    public string ClassId{ get; set; }
    
    /// <summary>
    /// 医疗机构ID
    /// </summary>
    [SugarColumn(ColumnName = "HOSPITAL_ID")]
    public string HospitalId{ get; set; }
    
    /// <summary>
    /// 父级设备分类id;父级节点为固定大类的固定id
    /// </summary>
    [SugarColumn(ColumnName = "PARENT_CLASS_ID")]
    public string? ParentClassId{ get; set; }
    
    /// <summary>
    /// 设备分类名称
    /// </summary>
    [SugarColumn(ColumnName = "CLASS_NAME")]
    public string ClassName{ get; set; }
    
    /// <summary>
    /// 设备分类简称
    /// </summary>
    [SugarColumn(ColumnName = "CLASS_SNAME")]
    public string? ClassSname{ get; set; }
    
    /// <summary>
    /// 设备分类标签;0 iso 1生物安全  2POCT 3高等级
    /// </summary>
    [SugarColumn(ColumnName = "CLASS_TAG")]
    public string? ClassTag{ get; set; }
    
    /// <summary>
    /// 设备分样式;用于存储图案颜色
    /// </summary>
    [SugarColumn(ColumnName = "CLASS_STYLE")]
    public string? ClassStyle{ get; set; }
    
    /// <summary>
    /// 设备分优先级;根节点为0 逐级递增1  结合自定义代号作模板选择权重
    /// </summary>
    [SugarColumn(ColumnName = "CLASS_LEVEL")]
    public string ClassLevel{ get; set; }
    
    /// <summary>
    /// 设备分类状态;0 禁用   1在用  2删除
    /// </summary>
    [SugarColumn(ColumnName = "CLASS_STATE")]
    public string ClassState{ get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "REMARK")]
    public string? Remark{ get; set; }
    
    /// <summary>
    /// 最后修改时间
    /// </summary>
    [SugarColumn(ColumnName = "LAST_MTIME")]
    public DateTime? LAST_MTIME{ get; set; }
    
    /// <summary>
    /// 最后修改人员
    /// </summary>
    [SugarColumn(ColumnName = "LAST_MPERSON")]
    public string? LAST_MPERSON{ get; set; }
    
    /// <summary>
    /// 首次登记时间
    /// </summary>
    [SugarColumn(ColumnName = "FIRST_RTIME")]
    public DateTime? FIRST_RTIME{ get; set; }
    
    /// <summary>
    /// 首次登记人
    /// </summary>
    [SugarColumn(ColumnName = "FIRST_RPERSON")]
    public string? FIRST_RPERSON{ get; set; }
    
    /// <summary>
    /// 关联关系
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    [Navigate(NavigateType.OneToMany, nameof(EMS_EQP_CLASS_ARCHIVES.EqpClassId))]
    public List<EMS_EQP_CLASS_ARCHIVES> ClassArchives { get; set; }
    /// <summary>
    /// 初始化一级分类的数据
    /// </summary>
    /// <param name="hospitalId"></param>
    /// <returns></returns>
    public static List<EMS_EQUIPMENT_CLASS_DICT> CreatInit(string hospitalId)
    {
        var result =  new List<EMS_EQUIPMENT_CLASS_DICT>
        {
            new ()
            {
                ClassId = "H82CLASSJZL",
                HospitalId = hospitalId,
                ParentClassId = "0",
                ClassName = "检测类",
                ClassSname = "检",
                ClassTag = "0",
                ClassStyle = null,
                ClassLevel = "0",
                ClassState = "1",
                Remark = "初始化数据"
            },
            new ()
            {
            ClassId = "H82CLASSXXL",
            HospitalId = hospitalId,
            ParentClassId = "0",
            ClassName = "信息设备类",
            ClassSname = "信",
            ClassTag = "0",
            ClassStyle = null,
            ClassLevel = "0",
            ClassState = "1",
            Remark = "初始化数据"
        },
            new ()
            {
                ClassId = "H82CLASSFZL",
                HospitalId = hospitalId,
                ParentClassId = "0",
                ClassName = "辅助监测类",
                ClassSname = "辅",
                ClassTag = "0",
                ClassStyle = null,
                ClassLevel = "0",
                ClassState = "1",
                Remark = "初始化数据"
            },
            new ()
            {
                ClassId = "H82CLASSGYL",
                HospitalId = hospitalId,
                ParentClassId = "0",
                ClassName = "公用设备",
                ClassSname = "公用设备",
                ClassTag = "0",
                ClassStyle = null,
                ClassLevel = "0",
                ClassState = "1",
                Remark = "初始化数据"
            }
            ,
            new ()
            {
                ClassId = "1",
                HospitalId = hospitalId,
                ParentClassId = "H82CLASSJZL",
                ClassName = "检测仪器/流水线",
                ClassSname = "常",
                ClassTag = "0",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "1",
                ClassState = "1",
                Remark = "初始化数据"
            }
            ,
            new ()
            {
                ClassId = "4",
                HospitalId = hospitalId,
                ParentClassId = "H82CLASSJZL",
                ClassName = "POCT检测设备",
                ClassSname = "P",
                ClassTag = "0",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "1",
                ClassState = "1",
                Remark = "初始化数据"
            }
            ,
            new ()
            {
                ClassId = "6",
                HospitalId = hospitalId,
                ParentClassId = "H82CLASSJZL",
                ClassName = "计量设备",
                ClassSname = "计",
                ClassTag = "0",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "1",
                ClassState = "1",
                Remark = "初始化数据"
            }
            ,
            new ()
            {
                ClassId = "7",
                HospitalId = hospitalId,
                ParentClassId = "H82CLASSXXL",
                ClassName = "信息设备",
                ClassSname = "信",
                ClassTag = "0",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "1",
                ClassState = "1",
                Remark = "初始化数据"
            }
            ,
            new ()
            {
                ClassId = "8",
                HospitalId = hospitalId,
                ParentClassId = "H82CLASSGYL",
                ClassName = "科研设备",
                ClassSname = "科",
                ClassTag = "0",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "1",
                ClassState = "1",
                Remark = "初始化数据"
            }
            ,
            new ()
            {
                ClassId = "9",
                HospitalId = hospitalId,
                ParentClassId = "H82CLASSGYL",
                ClassName = "评估设备",
                ClassSname = "评",
                ClassTag = "0",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "1",
                ClassState = "1",
                Remark = "初始化数据"
            }
            ,
            new ()
            {
                ClassId = "11",
                HospitalId = hospitalId,
                ParentClassId = "H82CLASSXXL",
                ClassName = "监测设备",
                ClassSname = "监",
                ClassTag = "0",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "1",
                ClassState = "1",
                Remark = "初始化数据"
            }
            ,
            new ()
            {
                ClassId = "2",
                HospitalId = hospitalId,
                ParentClassId = "H82CLASSFZL",
                ClassName = "标本处理设备",
                ClassSname = "标",
                ClassTag = "0",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "1",
                ClassState = "1",
                Remark = "初始化数据"
            }
            ,
            new ()
            {
                ClassId = "3",
                HospitalId = hospitalId,
                ParentClassId = "H82CLASSFZL",
                ClassName = "检测辅助设备",
                ClassSname = "辅",
                ClassTag = "0",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "1",
                ClassState = "1",
                Remark = "初始化数据"
            }
            ,
            new ()
            {
                ClassId = "12",
                HospitalId = hospitalId,
                ParentClassId = "H82CLASSFZL",
                ClassName = "消毒设备",
                ClassSname = "消",
                ClassTag = "0",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "1",
                ClassState = "1",
                Remark = "初始化数据"
            }
            ,
            new ()
            {
                ClassId = "13",
                HospitalId = hospitalId,
                ParentClassId = "H82CLASSFZL",
                ClassName = "孵育设备",
                ClassSname = "孵",
                ClassTag = "0",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "1",
                ClassState = "1",
                Remark = "初始化数据"
            }
            ,
            new ()
            {
                ClassId = "5",
                HospitalId = hospitalId,
                ParentClassId = "H82CLASSFZL",
                ClassName = "存储设备",
                ClassSname = "存",
                ClassTag = "0",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "1",
                ClassState = "1",
                Remark = "初始化数据"
            }
            ,
            new ()
            {
                ClassId = "10",
                HospitalId = hospitalId,
                ParentClassId = "H82CLASSFZL",
                ClassName = "特种设备",
                ClassSname = "特",
                ClassTag = "0",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "1",
                ClassState = "1",
                Remark = "初始化数据"
            }
            ,
            new ()
            {
                ClassId = "14",
                HospitalId = hospitalId,
                ParentClassId = "H82CLASSGYL",
                ClassName = "样本前处理设备",
                ClassSname = "前",
                ClassTag = "0",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "1",
                ClassState = "1",
                Remark = "初始化数据"
            }
            ,
            new ()
            {
                ClassId = "15",
                HospitalId = hospitalId,
                ParentClassId = "H82CLASSGYL",
                ClassName = "冲洗设备",
                ClassSname = "洗",
                ClassTag = "0",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "1",
                ClassState = "1",
                Remark = "初始化数据"
            }
            ,
            new ()
            {
                ClassId = "16",
                HospitalId = hospitalId,
                ParentClassId = "H82CLASSGYL",
                ClassName = "纯化设备",
                ClassSname = "纯",
                ClassTag = "0",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "1",
                ClassState = "1",
                Remark = "初始化数据"
            }
            ,
            new ()
            {
                ClassId = "17",
                HospitalId = hospitalId,
                ParentClassId = "H82CLASSGYL",
                ClassName = "制冷设备",
                ClassSname = "冷",
                ClassTag = "0",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "1",
                ClassState = "1",
                Remark = "初始化数据"
            }
            ,
            new ()
            {
                ClassId = "10001",
                HospitalId = hospitalId,
                ParentClassId = "1",
                ClassName = "检测仪器",
                ClassSname = "检",
                ClassTag = "0",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "2",
                ClassState = "1",
                Remark = "初始化数据"
            }
            ,
            new ()
            {
                ClassId = "10002",
                HospitalId = hospitalId,
                ParentClassId = "1",
                ClassName = "流水线",
                ClassSname = "流",
                ClassTag = "0",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "2",
                ClassState = "1",
                Remark = "初始化数据"
            }
            ,
            new ()
            {
                ClassId = "10003",
                HospitalId = hospitalId,
                ParentClassId = "1",
                ClassName = "虚拟手工",
                ClassSname = "虚",
                ClassTag = "0",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "2",
                ClassState = "1",
                Remark = "初始化数据"
            }
            
            ,
            new ()
            {
                ClassId = "60001",
                HospitalId = hospitalId,
                ParentClassId = "6",
                ClassName = "移液器",
                ClassSname = "移",
                ClassTag = "0",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "2",
                ClassState = "1",
                Remark = "初始化数据"
            }
            ,
            new ()
            {
                ClassId = "60002",
                HospitalId = hospitalId,
                ParentClassId = "6",
                ClassName = "称重",
                ClassSname = "称",
                ClassTag = "0",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "2",
                ClassState = "1",
                Remark = "初始化数据"
            }
            ,
            new ()
            {
                ClassId = "40001",
                HospitalId = hospitalId,
                ParentClassId = "4",
                ClassName = "POCT血糖仪",
                ClassSname = "P",
                ClassTag = "0",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "2",
                ClassState = "1",
                Remark = "初始化数据"
            } 
            ,
            new ()
            {
                ClassId = "40002",
                HospitalId = hospitalId,
                ParentClassId = "4",
                ClassName = "POCT血气",
                ClassSname = "P",
                ClassTag = "0",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "2",
                ClassState = "1",
                Remark = "初始化数据"
            } 
            ,
            new ()
            {
                ClassId = "70001",
                HospitalId = hospitalId,
                ParentClassId = "7",
                ClassName = "电脑",
                ClassSname = "脑",
                ClassTag = "0",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "2",
                ClassState = "1",
                Remark = "初始化数据"
            } 
            ,
            new ()
            {
                ClassId = "70002",
                HospitalId = hospitalId,
                ParentClassId = "7",
                ClassName = "打印机",
                ClassSname = "印",
                ClassTag = "0",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "2",
                ClassState = "1",
                Remark = "初始化数据"
            } 
            ,
            new ()
            {
                ClassId = "70003",
                HospitalId = hospitalId,
                ParentClassId = "7",
                ClassName = "高拍仪",
                ClassSname = "拍",
                ClassTag = "0",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "2",
                ClassState = "1",
                Remark = "初始化数据"
            } 
            ,
            new ()
            {
                ClassId = "110001",
                HospitalId = hospitalId,
                ParentClassId = "11",
                ClassName = "智能开关",
                ClassSname = "智",
                ClassTag = "0",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "2",
                ClassState = "1",
                Remark = "初始化数据"
            } 
            ,
            new ()
            {
                ClassId = "110002",
                HospitalId = hospitalId,
                ParentClassId = "11",
                ClassName = "环境一体机",
                ClassSname = "环",
                ClassTag = "0",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "2",
                ClassState = "1",
                Remark = "初始化数据"
            }
            ,
            new ()
            {
                ClassId = "110003",
                HospitalId = hospitalId,
                ParentClassId = "11",
                ClassName = "温湿度探头",
                ClassSname = "探",
                ClassTag = "0",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "2",
                ClassState = "1",
                Remark = "初始化数据"
            } 
            ,
            new ()
            {
                ClassId = "110004",
                HospitalId = hospitalId,
                ParentClassId = "11",
                ClassName = "AI摄像头",
                ClassSname = "摄",
                ClassTag = "0",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "2",
                ClassState = "1",
                Remark = "初始化数据"
            } 
            ,
            new ()
            {
                ClassId = "110005",
                HospitalId = hospitalId,
                ParentClassId = "11",
                ClassName = "IPC摄像头",
                ClassSname = "摄",
                ClassTag = "0",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "2",
                ClassState = "1",
                Remark = "初始化数据"
            } 
            ,
            new ()
            {
                ClassId = "110006",
                HospitalId = hospitalId,
                ParentClassId = "11",
                ClassName = "门禁设备",
                ClassSname = "门",
                ClassTag = "0",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "2",
                ClassState = "1",
                Remark = "初始化数据"
            } 
            ,
            new ()
            {
                ClassId = "20001",
                HospitalId = hospitalId,
                ParentClassId = "2",
                ClassName = "轨道",
                ClassSname = "轨",
                ClassTag = "0",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "2",
                ClassState = "1",
                Remark = "初始化数据"
            } 
            ,
            new ()
            {
                ClassId = "20002",
                HospitalId = hospitalId,
                ParentClassId = "2",
                ClassName = "开盖机",
                ClassSname = "盖",
                ClassTag = "0",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "2",
                ClassState = "1",
                Remark = "初始化数据"
            } 
            ,
            new ()
            {
                ClassId = "30001",
                HospitalId = hospitalId,
                ParentClassId = "3",
                ClassName = "生物安全柜",
                ClassSname = "柜",
                ClassTag = "1",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "2",
                ClassState = "1",
                Remark = "初始化数据"
            } 
            ,
            new ()
            {
                ClassId = "30002",
                HospitalId = hospitalId,
                ParentClassId = "3",
                ClassName = "离心机",
                ClassSname = "离",
                ClassTag = "1",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "2",
                ClassState = "1",
                Remark = "初始化数据"
            } 
            ,
            new ()
            {
                ClassId = "120001",
                HospitalId = hospitalId,
                ParentClassId = "12",
                ClassName = "紫外线灯",
                ClassSname = "灯",
                ClassTag = "1",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "2",
                ClassState = "1",
                Remark = "初始化数据"
            } 
            ,
            new ()
            {
                ClassId = "130001",
                HospitalId = hospitalId,
                ParentClassId = "13",
                ClassName = "培育箱",
                ClassSname = "培",
                ClassTag = "0",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "2",
                ClassState = "1",
                Remark = "初始化数据"
            } 
            ,
            new ()
            {
                ClassId = "130002",
                HospitalId = hospitalId,
                ParentClassId = "13",
                ClassName = "孵育箱",
                ClassSname = "孵",
                ClassTag = "0",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "2",
                ClassState = "1",
                Remark = "初始化数据"
            } 
            ,
            new ()
            {
                ClassId = "130003",
                HospitalId = hospitalId,
                ParentClassId = "13",
                ClassName = "金属浴",
                ClassSname = "浴",
                ClassTag = "0",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "2",
                ClassState = "1",
                Remark = "初始化数据"
            } 
            ,
            new ()
            {
                ClassId = "50001",
                HospitalId = hospitalId,
                ParentClassId = "5",
                ClassName = "冰箱",
                ClassSname = "冰",
                ClassTag = "0",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "2",
                ClassState = "1",
                Remark = "初始化数据"
            } 
            ,
            new ()
            {
                ClassId = "100001",
                HospitalId = hospitalId,
                ParentClassId = "10",
                ClassName = "电梯",
                ClassSname = "梯",
                ClassTag = "0",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "2",
                ClassState = "1",
                Remark = "初始化数据"
            } 
            ,
            new ()
            {
                ClassId = "100002",
                HospitalId = hospitalId,
                ParentClassId = "10",
                ClassName = "高压灭菌锅",
                ClassSname = "锅",
                ClassTag = "1",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "2",
                ClassState = "1",
                Remark = "初始化数据"
            } 
            ,
            new ()
            {
                ClassId = "100003",
                HospitalId = hospitalId,
                ParentClassId = "10",
                ClassName = "气瓶",
                ClassSname = "瓶",
                ClassTag = "0",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "2",
                ClassState = "1",
                Remark = "初始化数据"
            } 
            ,
            new ()
            {
                ClassId = "140001",
                HospitalId = hospitalId,
                ParentClassId = "14",
                ClassName = "切片机",
                ClassSname = "切",
                ClassTag = "0",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "2",
                ClassState = "1",
                Remark = "初始化数据"
            } 
            ,
            new ()
            {
                ClassId = "140002",
                HospitalId = hospitalId,
                ParentClassId = "14",
                ClassName = "甩片机",
                ClassSname = "甩",
                ClassTag = "0",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "2",
                ClassState = "1",
                Remark = "初始化数据"
            } 
            ,
            new ()
            {
                ClassId = "140003",
                HospitalId = hospitalId,
                ParentClassId = "14",
                ClassName = "细菌培养皿",
                ClassSname = "皿",
                ClassTag = "0",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "2",
                ClassState = "1",
                Remark = "初始化数据"
            } 
            ,
            new ()
            {
                ClassId = "150001",
                HospitalId = hospitalId,
                ParentClassId = "15",
                ClassName = "洗眼装置",
                ClassSname = "洗",
                ClassTag = "1",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "2",
                ClassState = "1",
                Remark = "初始化数据"
            } 
            ,
            new ()
            {
                ClassId = "150002",
                HospitalId = hospitalId,
                ParentClassId = "15",
                ClassName = "冲淋装置",
                ClassSname = "冲",
                ClassTag = "1",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "2",
                ClassState = "1",
                Remark = "初始化数据"
            } 
            ,
            new ()
            {
                ClassId = "160001",
                HospitalId = hospitalId,
                ParentClassId = "16",
                ClassName = "纯化制水机",
                ClassSname = "水",
                ClassTag = "0",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "2",
                ClassState = "1",
                Remark = "初始化数据"
            }
            ,
            new ()
            {
                ClassId = "170001",
                HospitalId = hospitalId,
                ParentClassId = "17",
                ClassName = "制冰机",
                ClassSname = "冰",
                ClassTag = "0",
                ClassStyle = "#1677FF-#ffffff",
                ClassLevel = "2",
                ClassState = "1",
                Remark = "初始化数据"
            }
            };
        foreach (var item in result)
        {
            if (item.ClassLevel == "2")
            {
                item.ClassState = "0";
            }
            item.LAST_MTIME = DateTime.Now;
            item.FIRST_RTIME = DateTime.Now;
            item.LAST_MPERSON = "H82初始化";
            item.FIRST_RPERSON = "H82初始化";
        }
        return result;
    }
}