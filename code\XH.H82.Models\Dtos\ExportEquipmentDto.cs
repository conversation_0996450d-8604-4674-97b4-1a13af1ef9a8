﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.Dtos
{
    public class ExportEquipmentDto
    {
        /// <summary>
        /// 设备名称
        /// </summary>
        public string EQUIPMENT_NAME { get; set; }
        /// <summary>
        /// 设备型号
        /// </summary>
        public string EQUIPMENT_MODEL { get; set; }
        /// <summary>
        /// 设备代号
        /// </summary>
        public string EQUIPMENT_CODE { get; set; }
        /// <summary>
        /// 设备科编号
        /// </summary>
        public string SECTION_NO { get; set; }
        /// <summary>
        /// 科室设备科编号
        /// </summary>
        public string DEPT_SECTION_NO { get; set; }
        /// <summary>
        /// 设备序列号
        /// </summary>
        public string SERIAL_NUMBER { get;set; }
        /// <summary>
        /// 首次启用日期
        /// </summary>
        public DateTime? ENABLE_TIME { get; set; }
        /// <summary>
        /// 设备序号
        /// </summary>
        public string EQUIPMENT_NUM { get; set; }
        /// <summary>
        /// 设备分类
        /// </summary>
        public string EQUIPMENT_CLASS { get; set; }
        /// <summary>
        /// 所属专业组
        /// </summary>
        public string MGROUP_NAME { get;set; }
        /// <summary>
        /// 专业分类
        /// </summary>
        public string PROFESSIONAL_ClASS { get; set; }
        /// <summary>
        /// 制造商
        /// </summary>
        public string MANUFACTURER { get; set; }
        /// <summary>
        /// 经销商
        /// </summary>
        public string DEALER { get; set; }
        /// <summary>
        /// 安装日期
        /// </summary>
        public DateTime? INSTALL_DATE { get; set; }
        /// <summary>
        /// 安装位置
        /// </summary>
        public string INSTALL_AREA { get; set; }
        /// <summary>
        /// 保管人员
        /// </summary>
        public string KEEP_PERSON { get; set; }
        /// <summary>
        /// 联系方式
        /// </summary>
        public string CONTACT_PHONE { get;set; }

        /// <summary>
        /// 注册证号
        /// </summary>
        public string? REGISTRATION_NUM { get; set; }
        
    }
}
