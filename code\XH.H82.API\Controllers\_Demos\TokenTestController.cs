﻿using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using EasyCaching.Core;
using H.BASE;
using H.Utility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;
using XH.H82.Base.Helper;
using XH.H82.IServices;

namespace XH.H82.API.Controllers._Demos
{
    [Route("api/[controller]/[action]")]
    [NonController]
    [Authorize]
    [ApiExplorerSettings(GroupName = "DEMO")]
    public class TokenTestController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        private readonly ISystemService _systemService;
        private IEasyCachingProvider redisC31;
        public TokenTestController(IConfiguration configuration, IEasyCachingProviderFactory cacheFactory, ISystemService systemService)
        {

            _configuration = configuration;
            _systemService = systemService;
            redisC31 = cacheFactory.GetCachingProvider("C31");
        }

        /// <summary>
        /// 模拟统一登录请求S01颁发token
        /// 注意:此方法仅演示使用,除特殊项目外生产环境严禁向外部暴露token获取接口
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IActionResult CreateToken()
        {
            var tokenId = IDGenHelper.CreateGuid().ToString();
            var res = _systemService.GetIssueTokenInfo("2", tokenId, AppSettingsProvider.CurrModuleId, "");
            return Ok(res);
        }

        [AllowAnonymous]
        [HttpPut]
        public IActionResult ReNewToken(string expriedToken, string refeshToken)
        {
            var claim = User.ToClaimsDto();

            var res = _systemService.ReNewToken(expriedToken, refeshToken);
            return Ok(res);
        }


        [HttpGet]
        public IActionResult AuthorizeApi()
        {
            return Ok(new ResultDto() { data = "Auth OK!" });
        }

        /// <summary>
        /// 不需要角色验证的接口
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [NonRoleAuthorize]
        public IActionResult NonRoleAuthorizeTest()
        {
            return Ok();
        }


    }
}
