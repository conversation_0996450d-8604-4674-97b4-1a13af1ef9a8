﻿
using Spectre.Console;
using System.ComponentModel;
using H.Utility;
using XH.H82.Models.Dtos.Base;
using XH.LAB.UTILS.Helpers;
using XH.LAB.UTILS.Models;

namespace XH.H82.Base.Tree
{

    public enum TreeNodeTypeEnum 
    {

        [Description("用户")]
        USER = 0,
        [Description("检验专业组")]
        PGROUP = 1,
        [Description("管理专业组")]
        MGROUP = 2,
        [Description("院区实例")]
        AREA = 3,
        [Description("科室实例")]
        LAB = 4,
        [Description("科室级公共分类")]
        LABPUBLIC = 10,
        [Description("管理专业组级公共分类")]
        MGROUPPUBLIC = 11,
        [Description("全部")]
        ALL = 100,
        [Description("机构实例")] HOSPITAL = 111,
        [Description("备案实验室")] SMBLLAB = 114,
        [Description("设备")]
        EQUIPMENT = 82,
        [Description("设备分类")]
        EQUIPMENTCLASS = 8200,
        [Description("常规检验设备")]
        Conventional = 8201,
        [Description("标本处理设备")]
        Specimens = 8202,
        [Description("标本辅助设备")]
        Auxiliary=8203,
        [Description("POCT检测设备")]
        POCT = 8204,
        [Description("储存设备")]
        Storage = 8205,
        [Description("计量设备")]
        Metering =8206,
        [Description("信息设备")]
        Information = 8207,
        [Description("科研设备")]
        Scientific = 8208,
        [Description("评估设备")]
        Assessment = 8209,
        [Description("特种设备")]
        Special = 8210,
        [Description("检测设备")]
        Monitoring = 8211,
        [Description("设备类型大类")]
        EQUIPMENTNEWCLASSNO0 = 82000,
        [Description("设备类型二级分类")]
        EQUIPMENTNEWCLASSNO1 = 82001,
        [Description("设备类型细分类")]
        EQUIPMENTNEWCLASSNO2 = 82002,
        
    }

    public interface ITreeNode
    {
        /// <summary>
        /// 节点名称(前端展示的名称)
        /// </summary>
        public string NAME { get; set; }
        /// <summary>
        /// 在一次返回里唯一的顺序号，从0开始
        /// </summary>
        public int NODE_NO { get; set; }
        /// <summary>
        /// 节点的顺序号(具有唯一性)
        /// </summary>
        public string AREA_ID { get; set; }
        /// <summary>
        /// 科室ID
        /// </summary>
        public string LAB_ID { get; set; }

        /// <summary>
        /// 节点类型：0 - 用户  1 - 检验专业组  2 - 管理专业组  3 - 院区
        /// </summary>        
        public TreeNodeTypeEnum NODE_TYPE { get; set; }
        /// <summary>
        /// 节点的类型名称
        /// </summary>
        public string NODE_TYPE_NAME { get; set; }

        /// <summary>
        /// 专业组或业务对象的ID
        /// </summary>
        public string SOURCE_ID { get; set; }
        /// <summary>
        /// 专业组或业务对象
        /// </summary>
        public object? SOURCE { get; set; }
        /// <summary>
        /// 节点计数
        /// </summary>
        public int NUM { get; set; }
        /// <summary>
        /// 分组排序字段
        /// </summary>
        public string SORT { get; set; }
        public string SOURCE_PATH { get; set; }
        public List<ITreeNode> CHILDREN { get; set; }
        /// <summary>
        /// 是否是叶子节点
        /// </summary>
        public bool ISLEAVES { get; set; }
        void AddChild(ITreeNode child);
        public void AddChilds(List<ITreeNode> childs);
        int ConutChildrens(ITreeNode root);
        public int ConutChildrensNoAppendRootPath(string rootPath = "");
    }

    public class AllTreeNode : ITreeNode
    {
        /// <summary>
        /// 节点名称(前端展示的名称)
        /// </summary>
        public string NAME { get; set; }
        /// <summary>
        /// 在一次返回里唯一的顺序号，从0开始
        /// </summary>
        public int NODE_NO { get; set; }
        /// <summary>
        /// 节点的顺序号(具有唯一性)
        /// </summary>
        public string AREA_ID { get; set; }
        /// <summary>
        /// 科室ID
        /// </summary>
        public string LAB_ID { get; set; }

        /// <summary>
        /// 节点类型：0 - 用户  1 - 检验专业组  2 - 管理专业组  3 - 院区
        /// </summary>        
        public TreeNodeTypeEnum NODE_TYPE { get; set; }
        /// <summary>
        /// 节点的类型名称
        /// </summary>
        public string NODE_TYPE_NAME { get; set; }

        /// <summary>
        /// 专业组或业务对象的ID
        /// </summary>
        public string SOURCE_ID { get; set; }
        /// <summary>
        /// 专业组或业务对象
        /// </summary>
        public object? SOURCE { get; set; }
        /// <summary>
        /// 节点计数
        /// </summary>
        public int NUM { get; set; }
        /// <summary>
        /// 分组排序字段
        /// </summary>
        public string SORT { get; set; }
        public bool ISLEAVES { get; set; } = false;

        public List<ITreeNode> CHILDREN { get; set; } = new List<ITreeNode>();
        public string SOURCE_PATH { get; set; }

        public void AddChild(ITreeNode child)
        {
            CHILDREN.Add(child);
        }

        public void AddChilds(List<ITreeNode> childs)
        {
            CHILDREN.AddRange(childs);
        }

        public int ConutChildrens(ITreeNode root)
        {
            int result = 0;
            if (NODE_TYPE   == TreeNodeTypeEnum.ALL || NODE_TYPE == TreeNodeTypeEnum.LABPUBLIC || NODE_TYPE == TreeNodeTypeEnum.MGROUPPUBLIC)
            {
                SOURCE_PATH = this is RootNode ? NODE_TYPE.ToID() : $"{((AllTreeNode)root).SOURCE_PATH}/{NODE_TYPE.ToID()}";
            }
            else
            {
                SOURCE_PATH = this is RootNode ? SOURCE_ID : $"{((AllTreeNode)root).SOURCE_PATH}/{SOURCE_ID}";
            }
            if (ISLEAVES)
            {
                result++;
            }

            foreach (var item in CHILDREN)
            {
                NUM += item.ConutChildrens(this);
            }
            result += NUM;
            return result;
        }
        
        
        public int ConutChildrensNoAppendRootPath(string rootPath = "")
        {
            int result = 0;
            if (NODE_TYPE   == TreeNodeTypeEnum.ALL || NODE_TYPE == TreeNodeTypeEnum.LABPUBLIC || NODE_TYPE == TreeNodeTypeEnum.MGROUPPUBLIC)
            {
                SOURCE_PATH = this is RootNode ? rootPath : rootPath.IsNullOrEmpty() ? $"{NODE_TYPE.ToID()}" : $"{rootPath}/{NODE_TYPE.ToID()}";
            }
            else
            {
                SOURCE_PATH = this is RootNode ? rootPath : rootPath.IsNullOrEmpty() ? $"{SOURCE_ID}" : $"{rootPath}/{SOURCE_ID}";
            }
            if (ISLEAVES)
            {
                result++;
            }
            rootPath = SOURCE_PATH;
            foreach (var item in CHILDREN)
            {
                NUM += item.ConutChildrensNoAppendRootPath(rootPath);
            }
            result += NUM;
            return result;
        }
    }

    public class RootNode : AllTreeNode
    {
        public RootNode() : base()
        {
            NAME = "全部";
            NODE_NO = 0;
            NODE_TYPE = TreeNodeTypeEnum.ALL;
            NODE_TYPE_NAME = TreeNodeTypeEnum.ALL.ToDesc();
            SOURCE_ID = string.Empty;
            NUM = 0;
        }
    }
}
