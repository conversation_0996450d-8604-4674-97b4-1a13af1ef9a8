﻿using System.ComponentModel.DataAnnotations;
using EasyCaching.Core;
using H.Utility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using SqlSugar;
using XH.H82.API.Extensions;
using XH.H82.Base.Setup;
using XH.H82.IServices;
using XH.H82.Models.Dtos;
using XH.H82.Services.OperationLog;

namespace XH.H82.API.Controllers
{
    namespace XH.H82.API.Controllers
    {
        [Route("api/[controller]/[action]")]
        [ApiController]
        [Authorize]
        public class XhWorkPlanController : ControllerBase
        {
            private readonly IXhWorkPlanService _xhWorkPlanService;
            private IOperationRecordService _operationRecordService;
            private readonly IConfiguration _configuration;
            private readonly IMemoryCache _easyCacheMemory;
            private string currentLabKey = "XH:LIS:H82:CURRENTLAB:UserSelectedLab:";
            private readonly string RedisModule;
            private readonly IModuleLabGroupService _IModuleLabGroupService;



            public XhWorkPlanController(IXhWorkPlanService xhWorkPlanService, IConfiguration configuration, IMemoryCache easyCahceFactory, IModuleLabGroupService iModuleLabGroupService, IOperationRecordService operationRecordService)
            {
                _xhWorkPlanService = xhWorkPlanService;
                _configuration = configuration;
                RedisModule = _configuration["RedisModule"];
                _easyCacheMemory = easyCahceFactory;
                _IModuleLabGroupService = iModuleLabGroupService;
                _operationRecordService = operationRecordService;
            }
            /// <summary>
            ///   获取工作计划列表（停用）
            /// </summary>
            /// <param name="keyword">检索</param>
            /// <param name="mgroupId">专业组id</param>
            /// <param name="equipmentClass">设备类型</param>
            /// <param name="pgroupId">检验专业组ID</param>
            /// <param name="areaId">病区ID</param>
            /// <returns></returns>
            [HttpGet]
            public IActionResult GetWorkPlanList(string keyword, string mgroupId, string equipmentClass, string pgroupId, string areaId)
            {
                var claims = User.ToClaimsDto();
                var clientMac = GetLocalMac.GetMac();
                currentLabKey = currentLabKey + claims.USER_NO + "." + clientMac;
                var labId = _easyCacheMemory.Get<string>(currentLabKey);
                if (mgroupId != null)
                {
                    if (mgroupId.Contains("MG") == false)
                    {
                        pgroupId = mgroupId;
                        mgroupId = null;
                    }
                }
                var res = _xhWorkPlanService.GetWorkPlanList(claims.USER_NO, claims.HOSPITAL_ID, keyword
                    , mgroupId, equipmentClass, labId, pgroupId, areaId);
                return Ok(res.ToResultDto());
            }


            /// <summary>
            /// 获取工作计划列表
            /// </summary>
            /// <param name="labId">科室ID</param>
            /// <param name="keyword">设备名称/型号/代号</param>
            /// <param name="mgroupId">管理专业组</param>
            /// <param name="equipmentClass">设备分类</param>
            /// <param name="pgroupId">检验专业组</param>
            /// <param name="areaId">院区</param>
            /// <returns></returns>
            [HttpGet]
            [CustomResponseTypeAttribute(typeof(List<EmsWorkPlanDto>))]
            public IActionResult GetWorkPlans(string labId, string keyword, string mgroupId, string equipmentClass, string pgroupId, string areaId)
            {
                var user = User.ToClaimsDto();
                if (mgroupId != null)
                {
                    if (mgroupId.Contains("MG") == false)
                    {
                        pgroupId = mgroupId;
                        mgroupId = null;
                    }
                }
                var res = _xhWorkPlanService.GetWorkPlans(user, keyword
                    , mgroupId, equipmentClass, labId, pgroupId, areaId);

                return Ok(res.ToResultDto());
            }

            /// <summary>
            /// 提交工作计划
            /// </summary>
            /// <param name="input">输入模型</param>
            /// <returns></returns>
            [HttpPost]
            public IActionResult SubmitWorkPlans(SubmitWorkPlanInput input)
            {
                var user = User.ToClaimsDto();
                user.USER_NAME = user.HIS_NAME;
                var passwordIsTrue = _IModuleLabGroupService.GetUserInfo(user.LOGID, input.Password);
                if (passwordIsTrue is null)
                {
                    return Ok(false.ToResultDto(false, "密码错误！"));
                }

                try
                {
                    if (input.WorkPlanIds.Count > 0)
                    {
                        foreach (var workPlanId in input.WorkPlanIds)
                        {
                            _xhWorkPlanService.SubmitPlan(user, workPlanId, input.AuditorName, input.AuditorId);
                        }
                    }
                }
                catch (Exception e)
                {

                    return Ok(new ResultDto()
                    {
                        msg = e.Message,
                        success = false
                    });
                }

                return Ok(true.ToResultDto());
            }


            /// <summary>
            /// 审核工作计划
            /// </summary>
            /// <param name="input">输入模型</param>
            /// <returns></returns>
            [HttpPost]
            public IActionResult AuditWorkPlans(AuditWorkPlanInput input)
            {
                var user = User.ToClaimsDto();
                user.USER_NAME = user.HIS_NAME;
                var passwordIsTrue = _IModuleLabGroupService.GetUserInfo(user.LOGID, input.Password);
                if (passwordIsTrue is null)
                {
                    return Ok(false.ToResultDto(false, "密码错误！"));
                }

                try
                {
                    if (input.WorkPlanIds.Count > 0)
                    {
                        foreach (var workPlanId in input.WorkPlanIds)
                        {
                            _xhWorkPlanService.AuditPlan(user, workPlanId, input.Content);
                        }
                    }
                }
                catch (Exception e)
                {

                    return Ok(new ResultDto()
                    {
                        msg = e.Message,
                        success = false
                    });
                }

                return Ok(true.ToResultDto());

            }


            /// <summary>
            /// 驳回工作计划
            /// </summary>
            /// <param name="input">输入模型</param>
            /// <returns></returns>
            [HttpPost]
            public IActionResult OverruledWorkPlans(OverruledWorkPlanInput input)
            {
                var user = User.ToClaimsDto();
                user.USER_NAME = user.HIS_NAME;
                var passwordIsTrue = _IModuleLabGroupService.GetUserInfo(user.LOGID, input.Password);
                if (passwordIsTrue is null)
                {
                    return Ok(false.ToResultDto(false, "密码错误！"));
                }

                try
                {
                    if (input.WorkPlanIds.Count > 0)
                    {
                        foreach (var workPlanId in input.WorkPlanIds)
                        {
                            _xhWorkPlanService.OverrulPlan(user, workPlanId, input.Content);
                        }
                    }
                }
                catch (Exception e)
                {

                    return Ok(new ResultDto()
                    {
                        msg = e.Message,
                        success = false
                    });
                }

                return Ok(true.ToResultDto());

            }


            /// <summary>
            /// 获取工作计划操作记录列表
            /// </summary>
            /// <param name="workPlanId">工作计划id</param>
            /// <returns></returns>
            [HttpGet]
            [CustomResponseType(typeof(List<EmsWorkPlanCirculationRecordDto>))]
            public IActionResult GetEmsWordPlanCirculationRecords([Required] string workPlanId)
            {

                var result = _xhWorkPlanService.GetEmsWordPlanCirculationRecords(workPlanId);

                return Ok(result.ToResultDto());
            }


            /// <summary>
            ///   专业组树结构
            /// </summary>
            /// <returns></returns>
            [HttpGet]
            [CustomResponseType(typeof(NewOATreeDto))]
            public IActionResult GetMgroupList(string areaId, string? pGroupId)
            {
                var claims = User.ToClaimsDto();
                var clientMac = GetLocalMac.GetMac();
                currentLabKey = currentLabKey + claims.USER_NO + "." + clientMac;
                var labId = _easyCacheMemory.Get<string>(currentLabKey);
                var result = _xhWorkPlanService.GetMgroupList(claims.USER_NO, claims.HOSPITAL_ID, labId, areaId)
                    .OrderBy(x=>x.AREA_ID)
                    .WhereIF(areaId.IsNotNullOrEmpty(),x=>x.AREA_ID == areaId)
                    .First();

                foreach (var secondStageTree in result.SecondStageTree)
                {
                    secondStageTree.ThirdStageTree.RemoveAll(x => x.FourthStageTree.Count() == 0);
                }
                result.SecondStageTree.RemoveAll(x => x.ThirdStageTree.Count() == 0);
                if (pGroupId is not null)
                {
                    result.SecondStageTree.RemoveAll(x => x.ThirdStageTree.Any(y => y.PGROUP_ID != pGroupId));
                }


                return Ok(result.ToResultDto());
            }

            /// <summary>
            ///   设备类型树结构（显示设备）
            /// </summary>
            /// <param name="areaId">院区ID</param>
            /// <returns></returns>
            [HttpGet]
            public IActionResult GetEquipmentClassList(string areaId ,string? pGroupId)
            {
                var claims = User.ToClaimsDto();
                var clientMac = GetLocalMac.GetMac();
                currentLabKey = currentLabKey + claims.USER_NO + "." + clientMac;
                var labId = _easyCacheMemory.Get<string>(currentLabKey);
                var result = _xhWorkPlanService.GetEquipmentClassList(claims.USER_NO, claims.HOSPITAL_ID, labId, areaId);


                foreach (var secondStageTree in result.SecondStageTree)
                {
                    foreach (var thirdStageTree in secondStageTree.ThirdStageTree)
                    {
                        thirdStageTree.FourthStageTree.RemoveAll(x => x.FifthStageTree.Count() == 0);
                    }
                }

                foreach (var secondStageTree in result.SecondStageTree)
                {
                    secondStageTree.ThirdStageTree.RemoveAll(x => x.FourthStageTree.Count() == 0);
                }


                if (pGroupId is not null)
                {
                    foreach (var secondStageTree in result.SecondStageTree)
                    {
                        secondStageTree.ThirdStageTree.RemoveAll(x => x.FourthStageTree.Any(x => x.PGROUP_ID != pGroupId));
                    }
                }

                return Ok(result.ToResultDto());
            }



        }
    }
}