﻿using H.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XH.H82.Models.Dtos;
using XH.H82.Models.Dtos.FileTemplate;
using XH.H82.Models.Entities;
using XH.H82.Models.Entities.FileTemplate;

namespace XH.H82.IServices
{
    public interface IOnlyOfficeService
    {

        /// <summary>
        /// 调用H115返回导出模板文件
        /// </summary>
        /// <param name="templateFillDataDto"></param>
        /// <returns></returns>
        public byte[] ExportStyleFile(StyleTemplateFillDataDto templateFillDataDto);

        /// <summary>
        /// 调用H115返回导出模板文件
        /// </summary>
        /// <param name="templateFillDataDto"></param>
        /// <returns></returns>
        public byte[] ExportStylePDFFile(StyleTemplateFillDataDto templateFillDataDto);

        /// <summary>
        /// 调用H115返回导出模板文件
        /// </summary>
        /// <param name="templateFillDataDto"></param>
        /// <returns></returns>
        public byte[] PreviewFile(StyleTemplateFillDataDto templateFillDataDto);

        ResultDto LoadExcelData(OaExcelFillDataDto requestBody);

        /// <summary>
        /// 导出信息集合转换
        /// </summary>
        /// <param name="equipmentInfo"></param>
        /// <param name="environmentInfo"></param>
        /// <param name="dealercontacts"></param>
        /// <param name="manufacturercontacts"></param>
        /// <param name="FillEquipmentInfo"></param>
        /// <param name=""></param>
        /// <returns></returns>
        EquipmentArchivesInfo ExchangeEquipmentArchivesInfo(EMS_EQUIPMENT_INFO equipmentInfo, EMS_ENVI_REQUIRE_INFO environmentInfo, List<SYS6_COMPANY_CONTACT> dealercontacts, List<SYS6_COMPANY_CONTACT> manufacturercontacts);

        /// <summary>
        /// 填充设备信息
        /// </summary>
        /// <param name="styleId"></param>
        /// <param name="archivesInfo"></param>
        /// <returns></returns>
        StyleTemplateFillDataDto FillEquipmentInfo(string styleId, EquipmentArchivesInfo archivesInfo);


        StyleTemplateFillDataDto FillEquipmentsWorkPlanInfo(string styleId, List<WorkPlansInfo> workPlans);

        List<WorkPlansInfo> ExchangeEquipmentsWorkPlan(List<EmsWorkPlanDto> emsWorkPlans);

        StyleTemplateFillDataDto FillEquipments(string styleId, List<EquipmentInfo> equipmentInfos);
        List<EquipmentInfo> ExchangeEquipments(List<EquipmentInfoDto> equipmentInfos);
        OaExcelFillDataDto FillExcelModelEquipments(string styleId, List<EquipmentInfo> equipmentInfosExcel);
        OaExcelFillDataDto FillExcelModelEquipmentsWorkPlan(string styleId, List<WorkPlansInfo> workplansExcel);
    }
}
