using H.Utility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using XH.H82.API.Extensions;
using XH.H82.IServices.Sbml;
using XH.H82.Models.Dtos;
using XH.H82.Services.DeviceDataRefresh;
using ClaimsDto = H.Utility.ClaimsDto;

namespace XH.H82.API.Controllers;

/// <inheritdoc />
[Route("api/[controller]/[action]")]
[ApiController]
[Authorize]
public class SmblEquipmentController  : ControllerBase
{
    private readonly IHttpContextAccessor _httpContext;
    private readonly ClaimsDto _user;
    private EquipmentTreeContext _equipmentTreeContext;
    private readonly ISmblServicve _smblServicve;

    /// <inheritdoc />
    public SmblEquipmentController(IHttpContextAccessor httpContext,  EquipmentTreeContext equipmentTreeContext, ISmblServicve smblServicve)
    {
        _httpContext = httpContext;
        _user =  _user = httpContext.HttpContext?.User.ToClaimsDto();
        _equipmentTreeContext = equipmentTreeContext;
        _smblServicve = smblServicve;
    }
    /// <summary>
    /// 生安-查询设备申购记录
    /// </summary>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="state">待审、通过、驳回、仪器已安装、合同已签订</param>
    /// <param name="search">申购项目|申购人</param>
    /// <param name="labId">科室id</param>
    /// <param name="smblLabId">备案实验室id</param>
    /// <param name="smblFlag">生安标识过滤  0非生安   1生安</param>
    /// <returns></returns>
    [HttpGet] 
    public IActionResult GetSubscribeInfoList([BindRequired] DateTime startTime, [BindRequired] DateTime endTime,
        string state, string search, string? labId, string? smblLabId,string? smblFlag )
    {
        _smblServicve.AsSmblforPgroups(labId,smblLabId);
        var result = _smblServicve.GetSubscrs(startTime, endTime, state, search);
        if (smblFlag.IsNotNullOrEmpty())
        {
            result.RemoveAll(x => x.SMBL_FLAG != smblFlag);
        }
        
        return Ok(result.ToResultDto());
    }

    /// <summary>
    /// 生安-设备档案列表查询
    /// </summary>
    /// <param name="state"> 0 未启用  1启用   2停用  3报废</param>
    /// <param name="smblEquipmentClass">生安设备类型id</param>
    /// <param name="type">设备类型id</param>
    /// <param name="keyWord">设备名称/代号/制造商/供应商/医院设备编号检索</param>
    /// <param name="isHidd">是否显示隐藏设备 0 不显示隐藏  1 显示隐藏 不填为不显示隐藏</param>
    /// <param name="labId"></param>
    /// <param name="smblLabId"></param>
    /// <param name="smblFlag"></param>
    /// <returns></returns>
    [HttpGet]
    public IActionResult GetEquipmentList(string state,  string? smblEquipmentClass  , string type, string keyWord, string? isHidd ,string? labId, string? smblLabId,string? smblFlag )
    {
        _smblServicve.AsSmblforPgroups(labId,smblLabId);

        var result = _smblServicve.Getequipments(state, type, keyWord,smblEquipmentClass);

        if (isHidd.IsNullOrEmpty())
        {
            result.RemoveAll(x => x.IS_HIDE != "0");
        }
        else
        {
            if (isHidd == "0")
            {
                result.RemoveAll(x => x.IS_HIDE != "0");
            }
        }
        if (smblFlag.IsNotNullOrEmpty())
        {
            result.RemoveAll(x => x.SMBL_FLAG != smblFlag);
        }
        return Ok(result.ToResultDto());
    }

    /// <summary>
    /// 生安-获取工作计划列表
    /// </summary>
    /// <param name="labId">科室ID</param>
    /// <param name="keyword">设备名称/型号/代号</param>
    /// <param name="smblEquipmentClass">生安设备类型</param>
    /// <param name="equipmentClass">设备分类</param>
    /// <param name="smblLabId">备案实验室id</param>
    /// <param name="smblFlag">生安标识</param>
    /// <returns></returns>
    [HttpGet]
    [CustomResponseType(typeof(List<EmsWorkPlanDto>))]
    public IActionResult GetWorkPlans(string keyword,string? smblEquipmentClass, string? equipmentClass,string? labId, string? smblLabId,string? smblFlag )
    {
        _smblServicve.AsSmblforPgroups(labId,smblLabId);
        var result = _smblServicve.GetWorkPlanDtos(keyword, equipmentClass,smblEquipmentClass);
        if (smblFlag.IsNotNullOrEmpty())
        {
            result = result.Where(x => x.SMBL_FLAG == smblFlag).ToList();
        }
        return Ok(result.ToResultDto());
    }

    /// <summary>
    ///  生安-获取报废停用列表
    /// </summary>
    /// <param name="dtBeginTime">开始时间</param>
    /// <param name="dtEndTime">结束时间</param>
    /// <param name="smblEquipmentClass">生安设备类型</param>
    /// <param name="equipmentClass">设备类型</param>
    /// <param name="szLastDispose">状态</param>
    /// <param name="szEventClass">申请类型</param>
    /// <param name="equipmentKey">设备检索</param>
    /// <param name="personKey">人物检索</param>
    /// <param name="person_select">过滤本人处理</param>
    /// <param name="labId">科室id</param>
    /// <param name="smblLabId">备案实验室id</param>
    /// <param name="smblFlag">生安标识</param>
    /// <returns></returns>
    [HttpGet]
    public IActionResult GetScrapStopList([BindRequired] DateTime dtBeginTime, [BindRequired] DateTime dtEndTime, string? smblEquipmentClass  , string? equipmentClass, string szLastDispose,
        string szEventClass, string equipmentKey, string personKey,
        string person_select,string? labId, string? smblLabId,string? smblFlag)
    {
        var result = new List<ScrapStopListDto>();
        _smblServicve.AsSmblforPgroups(labId,smblLabId);
       
        if (person_select == "0" )
        {
             result = _smblServicve.GetScrapStopList(
                dtBeginTime, dtEndTime,smblEquipmentClass, equipmentClass, szLastDispose, szEventClass, equipmentKey,
                personKey,
                null);
        }
        else
        { 
            var user = User.ToClaimsDto();
            result = _smblServicve.GetScrapStopList(
            dtBeginTime, dtEndTime, smblEquipmentClass,equipmentClass, szLastDispose, szEventClass, equipmentKey,
            personKey, 
            user.USER_NO);
        }
        if (smblFlag.IsNotNullOrEmpty())
        {
            result = result.Where(p => p.SMBL_FLAG == smblFlag).ToList();
        }
        
        return Ok(result.ToResultDto());
    }

    /// <summary>
    ///   生安-获取生安待报废停用的设备列表
    /// </summary>
    /// <param name="equipmentClass">设备类型</param>
    /// <param name="keyword">查询</param>
    /// <param name="pGourpId">检验专业组id</param>
    /// <param name="labId">科室id</param>
    /// <param name="smblLabId">备案实验室id</param>
    /// <param name="smblFlag">生安标识</param>
    /// <returns></returns>
    [HttpGet]
    public IActionResult GetEquipmentApplyList(string equipmentClass, string keyword, string? pGourpId ,string? labId,
        string? smblLabId, string? smblFlag)
    {    
        _smblServicve.AsSmblforPgroups(labId,smblLabId);
        var result = _smblServicve.GetEquipmentApplyList(equipmentClass, keyword);
        if (pGourpId.IsNotNullOrEmpty())
        {
            result = result.Where(p => p.UNIT_ID == pGourpId).ToList();
        }
        if (smblFlag.IsNotNullOrEmpty())
        {
            result = result.Where(p => p.SMBL_FLAG == smblFlag).ToList();
        }
        return Ok(result.ToResultDto());
    }
    
}