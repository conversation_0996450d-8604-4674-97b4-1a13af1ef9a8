﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XH.H82.Models.Entities.Common;
using XH.H82.Models.Entities;
using SqlSugar;
using XH.LAB.UTILS.Models;

namespace XH.H82.Models.ViewDtos
{
    /// <summary>
    /// 接口获取用户信息
    /// </summary>
    public class UserInfo
    {
        public string? USER_NO { get; set; }
        public string? LOGIN_ID { get; set; }
        public string? USER_ID { get; set; }
        public string? USER_NAME { get; set; }
        public string? PASSWORD { get; set; }
        public string? HIS_ID { get; set; }
        public string? POWER { get; set; }

        public string? POWER_NAME { get; set; }
        public string? IME { get; set; }
        public string? DEPT_CODE { get; set; }
        public string? TECH_POST { get; set; }
        public string? SELECT_GROUP { get; set; }
        public string? STATE_FLAG { get; set; }
        public string? MATERIAL_DEPT { get; set; }
        public string? USERNAME_SHORT { get; set; }
        public string? MOBILENO { get; set; }
        public string? USER_SORT { get; set; }
        public string? LAB_ID { get; set; }
        public string? DEL_POWER { get; set; }
        public DateTime? PWD_EDIT_DATE { get; set; }
        public string? ASSIGN_STATE { get; set; }
        public string? PWD_BS { get; set; }
        public string? SYS_SIGN { get; set; }
        public string? USER_TYPE { get; set; }
        public string? E_MAIL { get; set; }
        public string? PHONE_NO { get; set; }
        public string? REMARK { get; set; }
        public string? HANDUNLOCK { get; set; }
        public string? FIRST_RPERSON { get; set; }
        public string? FIRST_RTIME { get; set; }
        public string? LAST_MPERSON { get; set; }
        public string? LAST_MTIME { get; set; }
        public string? HOSPITAL_ID { get; set; }
        public string? HOSPITAL_NAME { get; set; }
        public string? RD_STATE { get; set; }
        public string? CA_VERIFY { get; set; }
        public string? CHECK_PWDD { get; set; }
        public string? CA_KEY { get; set; }
        public string? ID_CARD { get; set; }


        public string? OPERATE_PERSON { get; set; }

        [SugarColumn(IsIgnore = true)]
        public IEnumerable<SYS6_INSPECTION_LAB> LIS_INSPECTION_LAB { get; set; }

        [SugarColumn(IsIgnore = true)]
        public IEnumerable<SYS6_INSPECTION_MGROUP> SYS6_INSPECTION_MGROUP { get; set; }

        [SugarColumn(IsIgnore = true)]
        public IEnumerable<SYS6_INSPECTION_PGROUP> SYS6_INSPECTION_PGROUP { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string? Token { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string? RefreshToken { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string? moduleList { get; set; }

        [SugarColumn(IsIgnore = true)]
        public string? LIS_USER_UNIT { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string? COMMONMENU { get; set; }

        public string? NO_REGISTER_URL { get; set; }
        /// <summary>
        /// 人员图片路径
        /// </summary>
        public string? PERSON_PHOTO_PATH { get; set; }

        public string? CLIENT_IP { get; set; }

        public string? MAC_IP { get; set; }

        public string? VERSION_NUM { get; set; }
        public string? HOSPITAL_CNAME { get; set; }


        public string? FILE_UPLOAD_ADDRESS { get; set; }

        public string? FILE_PREVIEW_ADDRESS { get; set; }
    }

    /// <summary>
    /// 账户信息
    /// </summary>
    //public class AccountDto
    //{
    //    public UserInfo USER { get; set; }

    //    public List<SYS5_MENU> MENUS { get; set; }

    //    public string Token { get; set; }
    //} 

}
