﻿//using H.Utility.SqlSugarInfra;
//using SqlSugar;

//namespace XH.H82.Models.Entities.Common
//{
//    [DBOwner("XH_SYS")]
//    [SugarTable("SYS6_HOSPITAL_INFO")]
//    public class SYS6_INTERFACE_MODULE
//    {
//        public string INTERFACE_M_ID { get; set; }

//        public string INTERFACE_ID { get; set; }
//        public string MODULE_ID { get; set; }
//        public string APP_SECRET { get; set; }
//        public string APP_KEY { get; set; }
//        public string MODULE_STATE { get; set; }
//        public string FIRST_RPERSON { get; set; }
//        public string FIRST_RTIME { get; set; }
//        public string LAST_MPERSON { get; set; }
//        public string LAST_MTIME { get; set; }
//        public string REMARK { get; set; }
//        public string TIMESTAMP_LIMIT { get; set; }

//    }
//}
