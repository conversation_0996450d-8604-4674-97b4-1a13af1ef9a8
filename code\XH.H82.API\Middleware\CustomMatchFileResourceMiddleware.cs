﻿using Microsoft.Extensions.Caching.Memory;
using RestSharp;
using Serilog;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;

namespace XH.H82.API.Middleware
{
    public class CustomMatchFileResourceMiddleware
    {
        private readonly RequestDelegate _next;

        private readonly List<string> _routers;
        private readonly string _targetServiceModule;
        private readonly IMemoryCache _cachingProvider;


        public CustomMatchFileResourceMiddleware(RequestDelegate next, string targetServiceModule, List<string> routers, IMemoryCache cachingProvider)
        {
            _next = next;
            _routers = routers;
            _targetServiceModule = targetServiceModule;
            _cachingProvider = cachingProvider;
        }


        public async Task InvokeAsync(HttpContext context, IConfiguration configuration, IHttpContextAccessor httpContextAccessor)
        {
            foreach (var _router in _routers)
            {
                if (context.Request.Path.StartsWithSegments(_router))
                {
                    Log.Error(context.Request.Path);
                    var targetUrl = configuration.GetSection(_targetServiceModule).Value;
                    var token = httpContextAccessor.HttpContext.Request.Headers.Authorization.ToString();
                    await GetResources(_router, context, targetUrl, token);
                    return;
                }
            }
            await _next(context);
        }

        private async Task GetResources(string router, HttpContext context, string targetUrl, string token)
        {

            var url = context.Request.Path.Value.Split(router);

            if (url[1].Contains("//"))
            {
                url[1] = url[1].Replace("//", "/");
            }
            Log.Error($"{targetUrl}{router}{url[1]}");
            await RequestNormalFile(context, targetUrl, url[1], token);

        }



        private async Task RequestNormalFile(HttpContext context, string targetUrl, string fileUrl, string token)
        {
            using RestClient client = new RestClient(new RestClientOptions
            {
                RemoteCertificateValidationCallback = (object sender, X509Certificate? certificate, X509Chain? chain, SslPolicyErrors sslPolicyErrors) => true,
                BaseUrl = new Uri(targetUrl),
                ThrowOnAnyError = true,
                MaxTimeout = 10 * 60 * 1000
            });
            try
            {
                RestRequest request = new RestRequest(fileUrl, Method.Get);
                request.AddHeader("Authorization", token);
                var restResponse = await client.ExecuteAsync(request);

                (byte[], string) fileBytes = (restResponse.RawBytes, restResponse.ContentType);
                var fileLength = fileBytes.Item1.Length;
                using var fileStream = new MemoryStream(fileBytes.Item1);
                //var fileBytes = _cachingProvider.Get<(byte[], string)>(fileUrl);
                //if (fileBytes.Item1 is null)
                //{
                //    RestRequest request = new RestRequest(fileUrl, Method.Get);
                //    request.AddHeader("Authorization", token);
                //    var restResponse = await client.ExecuteAsync(request);
                //    if (restResponse.IsSuccessStatusCode)
                //    {
                //        _cachingProvider.Set<(byte[], string)>(fileUrl, (restResponse.RawBytes, restResponse.ContentType), TimeSpan.FromMinutes(5));
                //    }
                //}
                //fileBytes = _cachingProvider.Get<(byte[], string)>(fileUrl);
                //if (fileBytes.Item1 is null)
                //{
                //    throw new BizException("文件不存在!");
                //}
                //var fileLength = fileBytes.Item1.Length;

                //using var fileStream = new MemoryStream(fileBytes.Item1);
                context.Response.ContentType = fileBytes.Item2;
                context.Response.ContentLength = fileLength;
                //var file = "C:\\Users\\<USER>\\Desktop\\工作相关文件\\设备系统\\code\\XH.H82.API\\软件需求第三版.pdf";
                //FileInfo file1 = new FileInfo(file);
                //using var fileStream = file1.OpenRead();
                //var fileBytes = new byte[fileStream.Length];
                //var fileLength = fileStream.Length;
                //context.Response.ContentLength = fileLength;
                //fileStream.Read(fileBytes);

                //构建请求文件上下文 用于计算range协议
                FragmentReturnsFileStream streamContext = new FragmentReturnsFileStream(context);
                try
                {
                    //含有range参数 进行切片   pdfjs会按页数分割   其他文件可自行定义  
                    if (streamContext.TryAnalyseRange(context.Request, fileLength, out var indexes))
                    {
                        streamContext.SetPdfDownloadHeaders(true, indexes.FromIndex, indexes.ToIndex, fileLength);
                        await context.Response.Body.WriteAsync(streamContext.WriteToMemoryStream(fileStream, indexes.FromIndex, indexes.ToIndex).ToArray());
                    }
                    else //直接返回文件流
                    {
                        streamContext.SetPdfDownloadHeaders(false, 0, 0, fileStream.Length);
                        await context.Response.Body.WriteAsync(fileBytes.Item1, 0, (int)fileStream.Length);
                    }
                }
                catch (Exception ex)
                {
                    throw ex;
                }
                finally
                {
                    fileStream.Close();
                }

            }
            catch (Exception e)
            {
                throw e;
            }
        }


    }
}
