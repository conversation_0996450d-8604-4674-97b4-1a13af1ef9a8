﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using H.Utility.SqlSugarInfra;
using SqlSugar;

namespace XH.H82.Models.Entities
{
    [DBOwner("XH_OA")]
    public class EMS_SUBSCRIBE_INFO
    {
        [SugarColumn(IsPrimaryKey = true)]
    
        public string? SUBSCRIBE_ID { get; set; }
        public string? HOSPITAL_ID { get; set; }      
        public string? LAB_ID { get; set; }
        /// <summary>
        /// 检验专业组
        /// </summary>
        [Required(ErrorMessage = "实验室不能为空")]
        public string? MGROUP_ID { get; set; }
        public string? EQUIPMENT_ID { get; set; }
        [Required(ErrorMessage = "申购名称不能为空")]
        public string? SUBSCRIBE_NAME { get; set; }
        public string? SUBSCRIBE_NO { get; set; }
        [Required(ErrorMessage = "申购日期不能为空")]
        public DateTime? SUBSCRIBE_DATE { get; set; }
        [Required(ErrorMessage = "申购人不能为空")]
        public string? SUBSCRIBE_PERSON { get; set; }
        public DateTime? REQ_TIME { get; set; }
        public string? APPROVE_PERSON { get; set; }
        public DateTime? APPROVE_TIME { get; set; }
        public string? APPROVE_OPINION { get; set; }
        public string? SUBSCRIBE_STATE { get; set; }
        public string? FIRST_RPERSON { get; set; }
        public DateTime? FIRST_RTIME { get; set; }
        public string? LAST_MPERSON { get; set; }
        public DateTime? LAST_MTIME { get; set; }
        public string? REMARK { get; set; }

        [SugarColumn(IsIgnore = true)]
        public string? MGROUP_NAME { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string? EQUIPMENT_LIST { get; set; }
        public string SMBL_FLAG { get; set; } = "0";
        public string? SMBL_LAB_ID { get; set; }

    }
}
