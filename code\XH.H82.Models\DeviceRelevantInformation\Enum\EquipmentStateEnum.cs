﻿using System.ComponentModel;

namespace XH.H82.Models.DeviceRelevantInformation.Enum
{
    /// <summary>
    /// 设备状态
    /// </summary>
    public enum EquipmentStateEnum
    {

        /// <summary>
        /// 未启用
        /// </summary>
        [Description("未启用")]
        NotEnabled = 0 ,
        /// <summary>
        /// 启用
        /// </summary>
        [Description("启用")]
        Normal = 1,
        [Description("待停用")]
        PreDeactivat = 2,
        [Description("待报废")]
        PreScrapped = 3,
        /// <summary>
        /// 停用
        /// </summary>
        [Description("停用")]
        Deactivated = 4,
        /// <summary>
        /// 报废
        /// </summary>
        [Description("报废")]
        Scrapped = 5,
    }
}
