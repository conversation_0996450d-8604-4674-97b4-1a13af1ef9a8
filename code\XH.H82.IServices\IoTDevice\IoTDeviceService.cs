﻿using H.Utility;
using XH.H82.API.Controllers.IoTDevice.Dto;
using XH.H82.Models.BusinessModuleClient.Dto.IoTDevices;
using XH.H82.Models.Entities;
using XH.H82.Models.Entities.THS;

namespace XH.H82.IServices.IoTDevice;

public interface IIoTDeviceService
{
    /// <summary>
    /// 统计小时表里面的汇总，按天
    /// </summary>
    /// <param name="dateTime"></param>
    public void ConutEquipmentMonitoringItemsDay(DateTime dateTime);
    /// <summary>
    /// 统计设备的每一条的监测记录
    /// </summary>
    /// <param name="equipment">EMS设备</param>
    /// <param name="thsMonitorRecord">监测记录数据</param>
    public void ConutEquipmentMonitoringItems(EMS_EQUIPMENT_INFO equipment, THS_MONITOR_LIST thsMonitorRecord);
    public EMS_EQUIPMENT_INFO GetEquipmentInfo(string id);
    /// <summary>
    /// 根据设备id初始化 监控设备相关信息
    /// </summary>
    /// <param name="equipmentId"></param>
    /// <exception cref="BizException"></exception>
    public void InitEquipmentToThs(string equipmentId);

    /// <summary>
    /// 查询已经被纳入监测的设备
    /// </summary>
    /// <returns></returns>
    public List<EMS_EQUIPMENT_INFO> GetObtainMonitoringEquipments();

    /// <summary>
    /// 写入设备监测数据
    /// </summary>
    /// <param name="equipment">设备信息</param>
    /// <param name="data">获取到的监测数据</param>
    void AddObtainMonitoringData(EMS_EQUIPMENT_INFO equipment, IoTDevicesDto data);

    /// <summary>
    /// 获取设备监测数据
    /// </summary>
    /// <param name="equipmentId">设备id</param>
    /// <param name="dateTime">日期</param>
    List<THS_MONITOR_LIST> GetObtainMonitoringData(string equipmentId, DateTime? dateTime);

    IoTDevicesDto GetEquipmentIoTData(EMS_EQUIPMENT_INFO equipmentInfo);
    string GetEquipmentSwitchStatus(EMS_EQUIPMENT_INFO equipment, DateTime? dateTime);
    List<THS_MONITOR_ITEM> GetMonitorItems(string smblClass);
    string GetSwitchStatusName(string onlineStatus);

    void AddEvnObtainMonitoring(EMS_EQUIPMENT_INFO equipment, List<EnvironmentDevicesDto> datas);


    void AddLampObtainMonitoring(EMS_EQUIPMENT_INFO equipment, UltravioletLampDto datas);

    /// <summary>
    /// 环境一体机电信接口查询
    /// </summary>
    /// <param name="equipmentInfo">监控设备信息</param>
    /// <returns></returns>
    List<EnvironmentDevicesDto> GetEnvironmentDeviceData(EMS_EQUIPMENT_INFO equipmentInfo);

    /// <summary>
    /// 紫外线灯查询
    /// </summary>
    /// <param name="equipmentInfo"></param>
    /// <returns></returns>
    UltravioletLampDto GetUltravioletLampDeviceData(EMS_EQUIPMENT_INFO equipmentInfo);

    /// <summary>
    /// 根据时间范围查询设备监测数据
    /// </summary>
    /// <param name="equipmentId"></param>
    /// <param name="startTime"></param>
    /// <param name="endTime"></param>
    /// <returns></returns>
    List<THS_MONITOR_LIST> GetObtainMonitoringDataByTime(string equipmentId, DateTime startTime, DateTime endTime);

    /// <summary>
    /// 获取第三方房间列表
    /// </summary>
    /// <param name="name"></param>
    /// <returns></returns>
    List<Room> GetRooms(string? name);

    /// <summary>
    /// 获取门禁列表
    /// </summary>
    /// <returns></returns>
    object GetAccessControls();

    /// <summary>
    /// 房间对照
    /// </summary>
    /// <param name="positiopnId">科室位置id</param>
    /// <param name="roomId">第三方房间id</param>
    void RoomControl(string positiopnId, string roomId);

    /// <summary>
    /// 获取第三方医疗设备类型
    /// </summary>
    /// <returns></returns>
    List<DeviceType> GetDeviceTypes();
    
    /// <summary>
    /// 获取传感去类型列表
    /// </summary>
    /// <returns></returns>
    List<SensorsType> GetSensorTypes();
    
    /// <summary>
    /// 获取监测类型列表
    /// </summary>
    /// <returns></returns>
    List<MonitorType> GetMonitorTypes();
    
    /// <summary>
    /// 创建医疗设备
    /// </summary>
    /// <param name="device"></param>
    void AddDevice(ThirdPartyDevice device);

    /// <summary>
    /// 获取医疗设备列表
    /// </summary>
    /// <param name="roomId">设备科室位置id</param>
    /// <param name="type">第三方设备类型</param>
    /// <param name="isOnLine">是否在线</param>
    /// <returns></returns>
    List<ThirdPartyDevice> GetThirdPartyDevices(string roomId, int type, bool isOnLine);

    
    /// <summary>
    /// 传感器列表
    /// </summary>
    /// <param name="sensorTypes"> 传感去类型</param>
    /// <param name="roomId">科室位置id</param>
    /// <param name="labId">备案实验室id</param>
    /// <returns></returns>
    List<Sensor> GetSensors(List<int>? sensorTypes , string? roomId ,string? labId);

    /// <summary>
    /// 设备对照
    /// </summary>
    /// <param name="equipmentId">设备id</param>
    /// <param name="sn">第三方设备sn</param>
    void DeviceControl(string equipmentId, string sn);


    /// <summary>
    /// 设备关联传感器
    /// </summary>
    /// <param name="equipmentId">设备id</param>
    /// <param name="sensorSns">多个传感器sn</param>
    void DeviceControlSensors(string equipmentId, List<string> sensorSns);

    /// <summary>
    /// 实时查询设备监测数据
    /// </summary>
    /// <param name="equipmentId">设备id</param>
    /// <param name="sensorSns">传感器sn过滤</param>
    /// <returns></returns>
    List<DeviceMonitorRecord> GetEquipmentMonitorData(string equipmentId, List<string>? sensorSns);

    WaterPressureDto GetWaterPressureDtoDeviceData(EMS_EQUIPMENT_INFO equipmentInfo);
    
    void AddWaterObtainMonitoring(EMS_EQUIPMENT_INFO equipment, WaterPressureDto data);
    (  string Project,string ProjectClass) GetEquipmentIndicator(string equipmentId);

    

    /// <summary>
    /// 返回对应的关机、待机、使用中的判断
    /// </summary>
    /// <param name="smblClass"></param>
    /// <returns></returns>
    DevicesStatusValue GetDevicesStatusValue(string smblClass , string ItemId);

    /// <summary>
    /// 按机构/按备案实验是，查询重点设备数量
    /// </summary>
    /// <param name="hospitalId">机构id</param>
    /// <param name="labId">科室id</param>
    /// <param name="smblLabId">备案实验室</param>
    /// <returns></returns>
    public List<EMS_EQUIPMENT_INFO> GetEquipmentsUseCase(string? hospitalId , string? labId,  string? smblLabId);


    /// <summary>
    /// 机构层级统计机构下的生安设备
    /// </summary>
    /// <param name="type">iso / smbl</param>
    /// <param name="hospitalId">机构id</param>
    /// <returns></returns>
    public List<EMS_EQUIPMENT_INFO> GetGetEquipmentsDistributionByHospital(string type , string hospitalId);
    /// <summary>
    /// 科室层级统计科室下的设备
    /// </summary>
    /// <param name="labId"></param>
    /// <returns></returns>
    public List<EMS_EQUIPMENT_INFO> GetGetEquipmentsDistributionByLab(string type,string labId);
    /// <summary>
    /// 备案实验室层级统计备案实验室下的设备
    /// </summary>
    /// <param name="smblLabId"></param>
    /// <returns></returns>
    public List<EMS_EQUIPMENT_INFO> GetGetEquipmentsDistributionBySmblLab(string smblLabId);

    /// <summary>
    /// 查询一年内的生安相关设备的使用情况
    /// </summary>
    /// <param name="year">年份</param>
    /// <param name="hospitalId">机构id</param>
    /// <param name="labId">科室id</param>
    /// <param name="smblLabId">备案实验室id</param>
    /// <param name="smblClass">生安设备类型</param>
    /// <param name="equipmentName">设备名称</param>
    /// <returns></returns>
    public List<THS_MONITOR_DAY> GetObtainMonitoringDataByYear(int year , string hospitalId , string? labId, string? smblLabId ,string? smblClass,string? equipmentName);


    ( string Project,string ProjectClass) GetEquipmentIndicatorBySmblClass(string smblClass);
    List<object> GetIotDevice();
    List<object> Chuanganqi();

    /// <summary>
    /// 初始化ai告警类型
    /// </summary>
    /// <returns></returns>
    bool InitCameraAlerts();

    List<THS_MONITOR_ITEM> GetCameraAlerts();



    /// <summary>
    /// 初始化AI摄像头
    /// </summary>
    /// <param name="equipmentId">设备id</param>
    /// <param name="sn">sn</param>
    /// <param name="aiSettinbg">AI配置json</param>
    /// <exception cref="BizException"></exception>
    void AiCameraInit(string equipmentId , string sn , string aiSetting ,bool isAi = false);

    /// <summary>
    /// AI摄像头告警信息推送信息处理
    /// </summary>
    void PushAiCameraAlert(CameraAlertDto input);

    /// <summary>
    /// 医疗设备告警信息推送信息处理
    /// </summary>
    void PushEquipmentCameraAlert(MedicalEquipmentAlertDto input);

    /// <summary>
    /// 获取生安设备相关可维护信息列表
    /// </summary>
    /// <param name="smblLabId"></param>
    /// <returns></returns>
    List<EMS_EQUIPMENT_INFO> GetSmblToBeMaintainedEquipments(string smblLabId);

    /// <summary>
    /// 修改监测设备的sn
    /// </summary>
    /// <param name="id">ems设备ID</param>
    /// <param name="sn">ths设备SN</param>
    void ChangeThsEquipmentSn(string id , string sn);


    /// <summary>
    /// 修改thsEquipment的DID
    /// </summary>
    /// <param name="thsEquipmentId"></param>
    /// <param name="emsEquipmentId"></param>
    
    void ChangeThsEquipmentDid(string thsEquipmentId, string emsEquipmentId);

    /// <summary>
    /// 获取全部设备相关可维护信息列表
    /// </summary>
    /// <returns></returns>
    List<EMS_EQUIPMENT_INFO> GetToBeMaintainedEquipments();
}