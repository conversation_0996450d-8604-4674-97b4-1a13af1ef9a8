using H.Utility.SqlSugarInfra;
using Microsoft.EntityFrameworkCore;
using SqlSugar;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace XH.H82.Models.Entities.Tim
{
    [DBOwner("XH_OA")]
    public class TIM_WORK_INFO
    {
        /// <summary>
        /// 事务ID
        /// </summary>
        [Key]
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        [Column("WORK_ID")]
        [Required(ErrorMessage = "事务ID不允许为空")]

        [StringLength(20, ErrorMessage = "事务ID长度不能超出20字符")]
        [Unicode(false)]
        public string WORK_ID { get; set; }

        /// <summary>
        /// 首次登记人
        /// </summary>
        [Column("FIRST_RPERSON")]
        [StringLength(50, ErrorMessage = "首次登记人长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string FIRST_RPERSON { get; set; }

        /// <summary>
        /// 事务编号
        /// </summary>
        [Column("WORK_NO")]
        [StringLength(50, ErrorMessage = "事务编号长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string WORK_NO { get; set; }

        /// <summary>
        /// 排序号
        /// </summary>
        [Column("WORK_SORT")]
        [StringLength(200, ErrorMessage = "排序号长度不能超出200字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string WORK_SORT { get; set; }

        /// <summary>
        /// 归档布局1纵向单列2纵向多列3横向
        /// </summary>
        [Column("WORK_LAYOUT")]
        [StringLength(20, ErrorMessage = "归档布局1纵向单列2纵向多列3横向长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string WORK_LAYOUT { get; set; }

        /// <summary>
        /// 事务状态
        /// </summary>
        [Column("WORK_STATE")]
        [StringLength(20, ErrorMessage = "事务状态长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string WORK_STATE { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        [Column("LAST_MTIME")]
        [Unicode(false)]
        public DateTime? LAST_MTIME { get; set; }

        /// <summary>
        /// 记录单版本ID
        /// </summary>
        [Column("FORM_VER_ID")]
        [Required(ErrorMessage = "记录单版本ID不允许为空")]

        [StringLength(50, ErrorMessage = "记录单版本ID长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string FORM_VER_ID { get; set; }

        /// <summary>
        /// 事务说明
        /// </summary>
        [Column("WORK_EXPLIAN")]
        [StringLength(4000, ErrorMessage = "事务说明长度不能超出4000字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string WORK_EXPLIAN { get; set; }

        /// <summary>
        /// 模块ID
        /// </summary>
        [Column("MODULE_ID")]
        [Required(ErrorMessage = "模块ID不允许为空")]

        [StringLength(20, ErrorMessage = "模块ID长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string MODULE_ID { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Column("REMARK")]
        [StringLength(100, ErrorMessage = "备注长度不能超出100字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string REMARK { get; set; }

        /// <summary>
        /// 归档行数
        /// </summary>
        public int? ISSUED_ROW { get; set; }

        /// <summary>
        /// 事务名称
        /// </summary>
        [Column("WORK_NAME")]
        [StringLength(200, ErrorMessage = "事务名称长度不能超出200字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string WORK_NAME { get; set; }

        /// <summary>
        /// 执行频率内容
        /// </summary>
        [Column("FREQUENCY_SETUP")]
        [StringLength(200, ErrorMessage = "执行频率内容长度不能超出200字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string FREQUENCY_SETUP { get; set; }

        /// <summary>
        /// 首次登记时间
        /// </summary>
        [Column("FIRST_RTIME")]
        [Unicode(false)]
        public DateTime? FIRST_RTIME { get; set; }

        /// <summary>
        /// 最后修改人员
        /// </summary>
        [Column("LAST_MPERSON")]
        [StringLength(50, ErrorMessage = "最后修改人员长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string LAST_MPERSON { get; set; }

        /// <summary>
        /// 医疗机构ID
        /// </summary>
        [Column("HOSPITAL_ID")]
        [Required(ErrorMessage = "医疗机构ID不允许为空")]

        [StringLength(20, ErrorMessage = "医疗机构ID长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string HOSPITAL_ID { get; set; }

        /// <summary>
        /// 执行频率类型1按需2天3周4月5季6年7按天多次
        /// </summary>
        [Column("WORK_FREQUENCY")]
        [StringLength(20, ErrorMessage = "执行频率类型1按需2天3周4月5季6年7按天多次长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string WORK_FREQUENCY { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string TIME_FORMAT { get; set; }

        /// <summary>
        /// 指定下一级事务操作人
        /// </summary>
        [Column("NEXT_WORK_PERSON")]
        [StringLength(20, ErrorMessage = "NEXT_WORK_PERSON长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string NEXT_WORK_PERSON { get; set; }

        /// <summary>
        /// 事务描述
        /// </summary>
        [Column("WORK_DESC")]
        [StringLength(20, ErrorMessage = "WORK_DESC长度不能超出200字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string WORK_DESC { get; set; }

        /// <summary>
        /// 排除星期(设置6,7)
        /// </summary>
        [Column("EXCLUDE_WEEK")]
        [StringLength(20, ErrorMessage = "EXCLUDE_WEEK长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string EXCLUDE_WEEK { get; set; }

        /// <summary>
        /// 周末提醒方式（1向前提醒2向后提醒）
        /// </summary>
        [Column("PROMPT_TYPE")]
        [StringLength(10, ErrorMessage = "PROMPT_TYPE长度不能超出10字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string PROMPT_TYPE { get; set; }

        /// <summary>
        /// 计划类型（1周保养2月保养3季保养4年保养）
        /// </summary>
        [Column("WORK_PLAN_TYPE")]
        [StringLength(10, ErrorMessage = "WORK_PLAN_TYPE长度不能超出10字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string WORK_PLAN_TYPE { get; set; }
    }
}
