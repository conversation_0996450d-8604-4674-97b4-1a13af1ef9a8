﻿using System.Text;
using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using H.Utility.Helper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Serilog;
using SqlSugar;
using XH.H82.API.Controllers.IoTDevice.Dto;
using XH.H82.IServices;
using XH.H82.IServices.IoTDevice;
using XH.H82.IServices.Sbml;
using XH.H82.Models.BusinessModuleClient;
using XH.H82.Models.BusinessModuleClient.Dto.IoTDevices;
using XH.H82.Models.BusinessModuleClient.IoTDevices;
using XH.H82.Models.DeviceRelevantInformation;
using XH.H82.Models.Dtos;
using XH.H82.Models.Dtos.S28;
using XH.H82.Models.Entities;
using XH.H82.Models.Entities.THS;
using XH.H82.Models.Enums;
using XH.H82.Models.SugarDbContext;
using XH.H82.Services.DeviceDataRefresh;
using XH.LAB.UTILS.Interface;

namespace XH.H82.Services.IoTDevice;

public class IoTDeviceService : IIoTDeviceService
{
    
    private readonly IHttpContextAccessor _httpContext;
    private readonly ISqlSugarUow<SugarDbContext_Master> _dbContext;
    private readonly EquipmentContext equipmentContext;
    private readonly IConfiguration _configuration;
    private IoTDevicesClient _client;
    private IBaseDataServices _baseDataServices;
    private readonly ISmblServicve _smblService;
    private readonly IAuthorityService2 _authorityService;

    
    public IoTDeviceService(IHttpContextAccessor httpContext, ISqlSugarUow<SugarDbContext_Master> dbContext,IConfiguration configuration, ISmblServicve smblService, IBaseDataServices baseDataServices, IAuthorityService2 authorityService)
    {
        _dbContext = dbContext;
        _dbContext.NoLog();
        _httpContext = httpContext;
        equipmentContext = new(_dbContext);
        _configuration = configuration;
        _smblService = smblService;
        _baseDataServices = baseDataServices;
        _authorityService = authorityService;
        _client = new IoTDevicesClient(_configuration["IoTHttp"],_httpContext);
    }


    public List<object> GetIotDevice()
    {
        var equipments = _client.GetIoTDevices();
        return equipments.Rows;
    }

    
    public List<object> Chuanganqi()
    {
        var equipments = _client.GetIChuanganqi();
        return equipments.Rows;
    }
    
    public EMS_EQUIPMENT_INFO GetEquipmentInfo(string id)
    {
        return _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>().First(x => x.EQUIPMENT_ID == id);
    }

    /// <summary>
    /// 根据设备id初始化 监控设备相关信息
    /// </summary>
    /// <param name="equipmentId"></param>
    /// <returns></returns>
    /// <exception cref="BizException"></exception>
    public void InitEquipmentToThs(string equipmentId)
    { 
        Log.Information("查询设备系统是否记录该设备");
       var equipment =  _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>().First(x => x.EQUIPMENT_ID == equipmentId);
       if (equipment is null)
       {
           throw new BizException("该设备不存在，请把该设备信息录入到设备系统");
       }
       Log.Information("调用第三方设备接口查询实时监控数据");

       var unit =  _dbContext.Db.Queryable<THS_UNIT_INFO>().First(x => x.UNIT_ID == equipment.SMBL_LAB_ID);
       if (unit is null)
       {
           throw new BizException("请执行脚本插入监测单元信息（备案实验室）");
       }
       if (equipment.SMBL_CLASS.IsNullOrEmpty())
       {
           throw new BizException("请维护设备的生安类型");
       }
       var thsItems = GetMonitorItems(equipment.SMBL_CLASS);
        
       var sn = "";
       var roomId = equipment.POSITION_ID;
       var isOnlie = 1;
       
       //生物安全柜
       if (equipment.SMBL_CLASS == "1" || equipment.SMBL_CLASS == "2")
       {
           var ioTData = GetEquipmentIoTData(equipment);
           if (ioTData is null)
           {
               ioTData = new IoTDevicesDto()
               {
                   RoomId = 2000000,
                   Name = equipment.EQUIPMENT_CODE,
                   IsOnline = 1,
                   Sn = "8CCE4E4E3C4F"
               };
           }

           sn = ioTData.Sn;
           isOnlie = ioTData.IsOnline;
       }
       //环境一体机
       else if (equipment.SMBL_CLASS == "7")
       {
           var envData = GetEnvironmentDeviceData(equipment);
           if (envData is null  || envData.Count ==0)
           {
               envData = new();
               envData.Add(new ()
               {
                   Sn = "15100075", //15100076  正式环境数据
                   RoomId = 2000000,
               });
           }
           var info = envData[0];
           sn = info.Sn;
 
       }
       else if (equipment.SMBL_CLASS == "5")
       {
           //紫外线灯
            sn = "345F4597B51C";
       }
       else if (equipment.SMBL_CLASS == "4")
       {
           //洗眼器
           sn = "345F4597B394";
       }
        
       var  thsEquipment = AddAndGetNewThsEquipment(equipment, roomId,sn);
           //备案实验室下，这台被监测的设备有没有被纳入监测范围，没有则新建默认生安设备只有一个监测点
           var thsPoints = _dbContext.Db.Queryable<THS_EQUIPMENT_POINT>()
               .Where(x => x.UNIT_ID == equipment.SMBL_LAB_ID && x.EQUIPMENT_ID == thsEquipment.EQUIPMENT_ID)
               .ToList();
           if (!thsPoints.Any())
           {
               var id = IDGenHelper.CreateGuid().Substring(0,18);
               var newThsPoint = new THS_EQUIPMENT_POINT()
               {
                   POINT_ID = id,
                   UNIT_ID = thsEquipment.UNIT_ID,
                   HOSPITAL_ID = thsEquipment.HOSPITAL_ID,
                   EQUIPMENT_ID = thsEquipment.EQUIPMENT_ID,
                   POINT_NAME = thsEquipment.EQUIPMENT_CODE,
                   POINT_SNAME = thsEquipment.EQUIPMENT_CODE,
                   POINT_SORT = "",
                   POINT_NUM = roomId,
                   OBTAIN_MODE = "",
                   ONLINE_STATE = isOnlie == 1 ? "1" : "2",
                   ABNORMAL_STATE = "1",
                   POINT_STATE = "1",
                   FIRST_RPERSON = thsEquipment.FIRST_RPERSON,
                   FIRST_RTIME = DateTime.Now,
                   LAST_MPERSON = thsEquipment.LAST_MPERSON,
                   LAST_MTIME = DateTime.Now,
                   REMARK = "生安眼监测",
                   ONLINE_TIME_LIMIT = 10,
                   OBTAIN_FREQUENCY = 10,
                   EQUIPMENT_MID = "0",
                   LAST_TIME_POINT = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
               }; 
               _dbContext.Db.Insertable(newThsPoint).ExecuteCommand();
               thsPoints.Add(newThsPoint);
           }
           var newThsPointItems = new List<THS_EQUIPMENT_POINT_ITEM>();
           foreach (var thsPoint in thsPoints)
           {
               foreach (var thsItem in thsItems)
               { 
                   var newThsPointItem =  AddAndGetNewThsPointItem(thsPoint, thsItem);
                   newThsPointItems.Add(newThsPointItem);
               }
           }
           Console.WriteLine(JsonConvert.SerializeObject(newThsPointItems,Formatting.Indented));
    }

    public void ConutEquipmentMonitoringItemsDay(DateTime dateTime)
    {
        var equipments =  _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>()
                        .InnerJoin<THS_EQUIPMENT_INFO>((emsEquipment, thsEquipment) =>
                            emsEquipment.EQUIPMENT_ID == thsEquipment.EQUIPMENT_DID &&
                            emsEquipment.SMBL_LAB_ID == thsEquipment.UNIT_ID)
                        .Select((emsEquipment, thsEquipment) => thsEquipment)
                            .ToList();
        var thsMonitorDays = new List<THS_MONITOR_DAY>();
        foreach (var equipment in equipments)
        {  
            var monitorHours =  _dbContext.Db.Queryable<THS_MONITOR_HOUR>()
                .Where(x=>x.EQUIPMENT_ID == equipment.EQUIPMENT_ID)
                .Where(x=>x.MONITOR_DATE!.Value.Date == dateTime.Date)
                .ToList();
            var monitorHoursGroups = monitorHours.GroupBy(x => new {x.ITEM_ID, x.MONITOR_HOUR_STATE, x.POINT_ID,x.MONITOR_DATE});
            var monitorDays = _dbContext.Db.Queryable<THS_MONITOR_DAY>()
                .Where(x => x.EQUIPMENT_ID == equipment.EQUIPMENT_ID)
                .Where(x => x.MONITOR_DATE!.Value.Date == dateTime.Date)
                .ToList();
            foreach (var monitorHoursGroup in monitorHoursGroups)
            {   
                if (monitorDays.Count(x=>x.POINT_ID == monitorHoursGroup.Key.POINT_ID 
                                         && x.ITEM_ID == monitorHoursGroup.Key.ITEM_ID 
                                         && x.MONITOR_DATE!.Value.Date == monitorHoursGroup.Key.MONITOR_DATE) == 0)
                {
                    var monitorDay = new THS_MONITOR_DAY();
                    monitorDay.MONITOR_DAYID = IDGenHelper.CreateGuid().Substring(0, 18);
                    monitorDay.MONITOR_DATE = monitorHoursGroup.Key.MONITOR_DATE;
                    monitorDay.EQUIPMENT_ID = equipment.EQUIPMENT_ID;
                    monitorDay.POINT_ID = monitorHoursGroup.Key.POINT_ID;
                    monitorDay.UNIT_ID = equipment.UNIT_ID;
                    monitorDay.HOSPITAL_ID = equipment.HOSPITAL_ID;
                    monitorDay.ITEM_ID = monitorHoursGroup.Key.ITEM_ID;
                    monitorDay.TOTAL_NUM = monitorHoursGroup.Sum(x=>x.TOTAL_NUM);
                    monitorDay.TIME_POINT = monitorHoursGroup.Sum(x=>x.TOTAL_NUM).ToString();
                    monitorDay.NORMAL_NUM = monitorHoursGroup.Sum(x=>x.NORMAL_NUM);
                    monitorDay.ABNORMAL_NUM = monitorHoursGroup.Sum(x=>x.ABNORMAL_NUM);
                    monitorDay.MAX_VALUE = monitorHoursGroup.Max(x=>x.MAX_VALUE)!.Value.ToString("F4");
                    monitorDay.MIN_VALUE = monitorHoursGroup.Min(x=>x.MIN_VALUE)!.Value.ToString("F4");
                    monitorDay.AVG_VALUE = monitorHoursGroup.Average(x=>x.MIN_VALUE)!.Value.ToString("F4");
                    monitorDay.ALARM_NUM = monitorHoursGroup.Sum(x=>x.ALARM_NUM);
                    monitorDay.ACCUM_VALUE = monitorHoursGroup.Sum(x=>x.ACCUM_VALUE)!.Value;
                    monitorDay.OPER_PERSON = "";
                    monitorDay.OPER_TIME = null;
                    monitorDay.OPER_COMPUTER = "";
                    monitorDay.MONITOR_DAY_STATE = "1";
                    monitorDay.FIRST_RPERSON = "system";
                    monitorDay.FIRST_RTIME = dateTime;
                    monitorDay.LAST_MPERSON = "system";
                    monitorDay.LAST_MTIME = dateTime;
                    monitorDay.REMARK = "生安眼监测";
                    thsMonitorDays.Add(monitorDay);
                }
            }
        }
        if (thsMonitorDays.Any())
        {
            _dbContext.Db.Insertable(thsMonitorDays).UseParameter().ExecuteCommand();
        }
    }

    /// <summary>
    /// 统计设备的每一条的监测记录
    /// </summary>
    /// <param name="equipment">EMS设备</param>
    /// <param name="thsMonitorRecord">监测记录数据</param>
    public void ConutEquipmentMonitoringItems(EMS_EQUIPMENT_INFO equipment , THS_MONITOR_LIST thsMonitorRecord)
    {
        if (!thsMonitorRecord.ThsMonitorResults.Any())
        {
            return;
        }
        var conutHours = new List<THS_MONITOR_HOUR>();
        conutHours.AddRange(CreatMonitorHoursModels(equipment,thsMonitorRecord));
        conutHours.AddRange(CreatCunHoursModels(equipment,thsMonitorRecord));
        _dbContext.Db.GetSimpleClient<THS_MONITOR_HOUR>().InsertOrUpdate(conutHours);
    }
    
    public void ConutAiMonitoringItems(EMS_EQUIPMENT_INFO equipment,THS_MONITOR_LIST thsMonitorRecord)
    {
        if (!thsMonitorRecord.ThsMonitorResults.Any())
        {
            return;
        }
        var conutHours = new List<THS_MONITOR_HOUR>();
        conutHours.AddRange(CreatAcountHoursModels(equipment,thsMonitorRecord));
        _dbContext.Db.GetSimpleClient<THS_MONITOR_HOUR>().InsertOrUpdate(conutHours);
    }
    /// <summary>
    /// 统计采集的监测数据
    /// </summary>
    /// <param name="thsMonitorRecord">监测记录</param>
    /// <returns></returns>
    private List<THS_MONITOR_HOUR> CreatMonitorHoursModels(EMS_EQUIPMENT_INFO equipment, THS_MONITOR_LIST thsMonitorRecord)
    {
        var result = new List<THS_MONITOR_HOUR>();
        var project = GetEquipmentIndicatorBySmblClass(equipment.SMBL_CLASS!);
        var itemValue = double.NaN;
        var itemValueResult = thsMonitorRecord.ThsMonitorResults.FirstOrDefault(x => x.ITEM_ID == project.Project);
        if (itemValueResult is  not null)
        {
            itemValue = double.Parse(itemValueResult.ITEM_VALUE!);
        }
        var statusValue =  GetDevicesStatusValue(project.ProjectClass, project.Project);
        var status = statusValue.UseFunc(itemValue) ? "使用中" : statusValue.StandByFunc(itemValue) ? "待机" : statusValue.DisinfectionFunc(itemValue) ? "消毒" : "关机";
        foreach (var monitorResult in thsMonitorRecord.ThsMonitorResults)
        {
            if (monitorResult.ITEM_ID == "SMBL_1" || monitorResult.ITEM_ID == "SMBL_6" )
            {
                continue;
            }
            result.Add(CreatHoursModel(thsMonitorRecord,monitorResult,status));
        }
        return result;
    }

    /// <summary>
    /// 统计累加项的数据
    /// </summary>
    /// <param name="equipment">设备</param>
    /// <param name="thsMonitorRecord">监测记录</param>
    /// <returns></returns>
    private List<THS_MONITOR_HOUR> CreatCunHoursModels(EMS_EQUIPMENT_INFO equipment, THS_MONITOR_LIST thsMonitorRecord )
    {
        var result =  new List<THS_MONITOR_HOUR>();
        var project = GetEquipmentIndicatorBySmblClass(equipment.SMBL_CLASS!);
        //环境一体机因可监测多个项目，故需要特殊处理
        if (project.Project.IsNotNullOrEmpty())
        {
            var itemValue = double.NaN;
            var itemValueResult = thsMonitorRecord.ThsMonitorResults.FirstOrDefault(x => x.ITEM_ID == project.Project);
            if (itemValueResult is  not null)
            {
                itemValue = double.Parse(itemValueResult.ITEM_VALUE!);
            }
            var statusValue =  GetDevicesStatusValue(project.ProjectClass, project.Project);
            var cumItemIds =  new string[] { "CUM_01","CUM_02","CUM_03","CUM_04" };
            var newRsults = GetTimeByItemIdAndstatusValue(itemValue, cumItemIds, statusValue);
            foreach (var newRsultsItem in newRsults)
            {
                result.Add(CreatHoursModel(thsMonitorRecord,newRsultsItem,"累加"));
            }
        }
        return result;
    }

    private List<THS_MONITOR_HOUR> CreatAcountHoursModels(EMS_EQUIPMENT_INFO equipment, THS_MONITOR_LIST thsMonitorRecord )
    {
        var result =  new List<THS_MONITOR_HOUR>();
        foreach (var newRsultsItem in thsMonitorRecord.ThsMonitorResults)
        {
            result.Add(CreatHoursModel(thsMonitorRecord,newRsultsItem,"计算"));
        }
        return result;
    }

    /// <summary>
    /// 累加项的时间累加计算
    /// </summary>
    /// <param name="itemValue">用于计算使用状态的监测指标的值</param>
    /// <param name="CumItemIds">累加项类型的检测指标，多个，目前只包含CUM_01 累计使用时长  CUM_02 累计待机时长  CUM_03  累计关机时长</param>
    /// <param name="statusValue">状态值计算方法</param>
    /// <returns></returns>
    private List<THS_MONITOR_RESULT> GetTimeByItemIdAndstatusValue(double itemValue, string[] CumItemIds , DevicesStatusValue statusValue)
    {
        //根据监测时间间隔累加时间  按分钟为单位
        var time = _configuration.GetSection("IoTSyncTime").Value;
        var result = new List<THS_MONITOR_RESULT>();
        foreach (var cumItemId in CumItemIds)
        {
            var newResult = new THS_MONITOR_RESULT();
            newResult.ITEM_ID = cumItemId;
            newResult.ITEM_VALUE = "0";
            newResult.ITEM_STATE = "1";
            newResult.FIRST_RPERSON = "system";
            newResult.FIRST_RTIME = DateTime.Now;
            newResult.LAST_MPERSON = "system";
            newResult.LAST_MTIME = DateTime.Now;
            newResult.REMARK = "生安眼监测";
            if (cumItemId == "CUM_01")
            {
                if (statusValue.UseFunc(itemValue))
                {
                    newResult.ITEM_VALUE = time;
                }
            }
            if (cumItemId == "CUM_02")
            {
                if (statusValue.StandByFunc(itemValue))
                {
                    newResult.ITEM_VALUE = time;
                }
            }
            if (cumItemId == "CUM_03")
            {
                if (statusValue.ShutDownFunc(itemValue))
                {
                    newResult.ITEM_VALUE = time;
                }
            }
            if (cumItemId == "CUM_04")
            {
                if (statusValue.DisinfectionFunc(itemValue))
                {
                    newResult.ITEM_VALUE = time;
                }
            }
            result.Add(newResult);
        }
        return result;
    }

    /// <summary>
    /// 构建统计数据模型 （包含三种监测类型：监测项（平均值+最大值+最小值+数量项）、累加项（累计值+数量项）、数量项）
    /// </summary>
    /// <param name="thsMonitorRecords"> 监测记录</param>
    /// <param name="thsMonitorResult"> 监测结果（累加项的可以虚拟监测结果）</param>
    /// <param name="UseStatus">检测类的 需要记录当前自己的使用状态   累加的类的均为 “累加” </param>
    /// <returns></returns>
    private THS_MONITOR_HOUR CreatHoursModel(THS_MONITOR_LIST thsMonitorRecords , THS_MONITOR_RESULT  thsMonitorResult , string UseStatus = "使用中")
    {
        var isNormal = thsMonitorResult.ITEM_STATE == "1" ;
        var monitorType = GetItemIdToMonitorType(thsMonitorResult.ITEM_ID!); 
        //这个一个设备的这个一个检测点的这个检测项在这天的这个小时段，这个检测项的记录是否存在联系的记录，如果发生最后一条记录的状态变化，需要重写写入
        var result = _dbContext.Db.Queryable<THS_MONITOR_HOUR>()
            .Where(x=>x.EQUIPMENT_ID == thsMonitorRecords.EQUIPMENT_ID)
            .Where(x=>x.MONITOR_TYPE == monitorType)
            .Where(x=>x.ITEM_ID == thsMonitorResult.ITEM_ID)
            .Where(x=>x.POINT_ID == thsMonitorRecords.POINT_ID)
            .Where(x=>x.MONITOR_DATE == thsMonitorRecords.MONITOR_TIME!.Value.Date)
            .Where(x=>x.TIME_POINT_HOUR == thsMonitorRecords.MONITOR_TIME!.Value.Hour.ToString())
            .OrderByDescending(x=>x.LAST_MTIME)
            .First();
        if (result is null)
        {
            result = new THS_MONITOR_HOUR();
            result.MONITOR_HOURID = IDGenHelper.CreateGuid().Substring(0,18);
            result.EQUIPMENT_ID = thsMonitorRecords.EQUIPMENT_ID;
            result.POINT_ID = thsMonitorRecords.POINT_ID;
            result.UNIT_ID = thsMonitorRecords.UNIT_ID;
            result.HOSPITAL_ID = thsMonitorRecords.HOSPITAL_ID;
            result.ITEM_ID = thsMonitorResult.ITEM_ID;
            result.MONITOR_TYPE = monitorType;
            result.MONITOR_DATE = thsMonitorRecords.MONITOR_TIME.Value.Date;
            result.TIME_POINT_HOUR = thsMonitorRecords.MONITOR_TIME.Value.Hour.ToString();
            result.TOTAL_NUM = 1;
            result.NORMAL_NUM = isNormal ? 1 : 0;
            result.ABNORMAL_NUM = isNormal ? 0 : 1;
            result.MAX_VALUE = monitorType  is MonitorTypeEnum.Monitor? double.Parse(thsMonitorResult.ITEM_VALUE) : 0;
            result.MIN_VALUE = monitorType  is MonitorTypeEnum.Monitor? double.Parse(thsMonitorResult.ITEM_VALUE) : 0;
            result.AVG_VALUE = monitorType  is MonitorTypeEnum.Monitor? double.Parse(thsMonitorResult.ITEM_VALUE) : 0;
            result.ACCUM_VALUE = monitorType  is MonitorTypeEnum.Cumulative? int.Parse(thsMonitorResult.ITEM_VALUE) : 0;
            result.ALARM_NUM = isNormal ? 0 : 1;
            result.OPER_PERSON = String.Empty;
            result.OPER_TIME = null;
            result.OPER_COMPUTER = String.Empty;
            result.MONITOR_HOUR_STATE = UseStatus;
            result.FIRST_RPERSON = "system";
            result.FIRST_RTIME = thsMonitorResult.FIRST_RTIME;
            result.LAST_MPERSON = "system";
            result.LAST_MTIME = thsMonitorResult.LAST_MTIME;
            result.REMARK = thsMonitorResult.REMARK;
            return result;
        }
        if (result.MONITOR_HOUR_STATE != UseStatus)
        {
            result = new THS_MONITOR_HOUR();
            result.MONITOR_HOURID = IDGenHelper.CreateGuid().Substring(0,18);
            result.EQUIPMENT_ID = thsMonitorRecords.EQUIPMENT_ID;
            result.POINT_ID = thsMonitorRecords.POINT_ID;
            result.UNIT_ID = thsMonitorRecords.UNIT_ID;
            result.HOSPITAL_ID = thsMonitorRecords.HOSPITAL_ID;
            result.ITEM_ID = thsMonitorResult.ITEM_ID;
            result.MONITOR_TYPE = monitorType;
            result.MONITOR_DATE = thsMonitorRecords.MONITOR_TIME.Value.Date;
            result.TIME_POINT_HOUR = thsMonitorRecords.MONITOR_TIME.Value.Hour.ToString();
            result.TOTAL_NUM = 1;
            result.NORMAL_NUM = isNormal ? 1 : 0;
            result.ABNORMAL_NUM = isNormal ? 0 : 1;
            result.MAX_VALUE = monitorType  is MonitorTypeEnum.Monitor? double.Parse(thsMonitorResult.ITEM_VALUE) : 0;
            result.MIN_VALUE = monitorType  is MonitorTypeEnum.Monitor? double.Parse(thsMonitorResult.ITEM_VALUE) : 0;
            result.AVG_VALUE = monitorType  is MonitorTypeEnum.Monitor? double.Parse(thsMonitorResult.ITEM_VALUE) : 0;
            result.ACCUM_VALUE = monitorType  is MonitorTypeEnum.Cumulative? int.Parse(thsMonitorResult.ITEM_VALUE) : 0;
            result.ALARM_NUM = isNormal ? 0 : 1;
            result.OPER_PERSON = String.Empty;
            result.OPER_TIME = null;
            result.OPER_COMPUTER = String.Empty;
            result.MONITOR_HOUR_STATE = UseStatus;
            result.FIRST_RPERSON = "system";
            result.FIRST_RTIME = thsMonitorResult.FIRST_RTIME;
            result.LAST_MPERSON = "system";
            result.LAST_MTIME = thsMonitorResult.LAST_MTIME;
            result.REMARK = thsMonitorResult.REMARK;
            return result;
        }
        result.TOTAL_NUM+=1;
        if (isNormal)
        {
            result.NORMAL_NUM += 1;
        }
        else
        {
            result.ABNORMAL_NUM += 1;
            result.ALARM_NUM += 1;
        }
        if (monitorType is MonitorTypeEnum.Monitor)
        {
            result.MAX_VALUE =  result.MAX_VALUE > double.Parse(thsMonitorResult.ITEM_VALUE!)  ? result.MAX_VALUE : double.Parse(thsMonitorResult.ITEM_VALUE!);
            result.MIN_VALUE = result.MIN_VALUE < double.Parse(thsMonitorResult.ITEM_VALUE!)  ? result.MIN_VALUE : double.Parse(thsMonitorResult.ITEM_VALUE!);
            result.AVG_VALUE = (result.AVG_VALUE + double.Parse(thsMonitorResult.ITEM_VALUE!)) / 2;
        }
        if (monitorType is MonitorTypeEnum.Cumulative)
        {
            result.ACCUM_VALUE += int.Parse(thsMonitorResult.ITEM_VALUE!);
        }
        return result;
    }


    /// <summary>
    /// 根据监测指标分类统计的监测类型
    /// </summary>
    /// <param name="itemId">监测指标</param>
    /// <returns></returns>
    private MonitorTypeEnum GetItemIdToMonitorType(string itemId)
    {
        //监测项
        if (itemId.Contains("SMBL"))
        {
            return MonitorTypeEnum.Monitor;
        }
        //累加项
        if (itemId.Contains("CUM"))
        {
            return MonitorTypeEnum.Cumulative;
        }
        //数量项
        return MonitorTypeEnum.Conut;
    }

    /// <summary>
    /// 获取生安类型设备的监测指标
    /// </summary>
    /// <param name="equipmentId"></param>
    /// <returns></returns>
    public  (string Project,string ProjectClass) GetEquipmentIndicator(string equipmentId)
    {
        var smblClass = _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>()
            .Where(x=>x.EQUIPMENT_ID == equipmentId)
            .Select<string>(x => x.SMBL_CLASS)
            .First();
        var result = smblClass switch
        {
            "1" => "SMBL_5",
            "2" => "SMBL_5",
            "4" => "SMBL_206",
            "5" => "SMBL_205",
            "9" => "SMBL_206",
            _ => ""
        };
        return (result , smblClass);

    }


    public List<THS_MONITOR_DAY> GetObtainMonitoringDataByYear(int year, string hospitalId,string? labId, string? smblLabId, string? smblClass , string? equipmentName)
    {
        var monitorDays = _dbContext.Db.Queryable<THS_MONITOR_DAY>()
            .Where(days => days.HOSPITAL_ID == hospitalId)
            .Where(days => days.ITEM_ID == "CUM_01")
            .InnerJoin<THS_EQUIPMENT_INFO>((days,thsEquipment)=> thsEquipment.EQUIPMENT_ID == days.EQUIPMENT_ID)
            .InnerJoin<EMS_EQUIPMENT_INFO>((days,thsEquipment, emsEquipment) =>
                thsEquipment.EQUIPMENT_DID == emsEquipment.EQUIPMENT_ID)
            .WhereIF(labId.IsNotNullOrEmpty(), (days, thsEquipment, emsEquipment) => emsEquipment.LAB_ID == labId)
            .WhereIF(smblLabId.IsNotNullOrEmpty(), (days, thsEquipment, emsEquipment) => thsEquipment.UNIT_ID == smblLabId)
            .WhereIF(smblClass.IsNotNullOrEmpty(), (days, thsEquipment, emsEquipment) => emsEquipment.SMBL_CLASS == smblClass)
            .WhereIF(equipmentName.IsNotNullOrEmpty(), (days, thsEquipment, emsEquipment) => emsEquipment.EQUIPMENT_NAME == equipmentName)
            .Where((days, thsEquipment, emsEquipment) => days.MONITOR_DATE!.Value.Year == year )
            .ToList();
        return monitorDays;
    }

    /// <summary>
    /// 获取生安设备相关可维护信息列表
    /// </summary>
    /// <param name="smblLabId"></param>
    /// <returns></returns>
    public List<EMS_EQUIPMENT_INFO> GetSmblToBeMaintainedEquipments(string smblLabId)
    {
          var equipments= _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>()
             .Where(x=>x.SMBL_FLAG == "1")
             .Where(x=>x.IS_HIDE == "0" || x.IS_HIDE == null)
             .WhereIF(smblLabId.IsNotNullOrEmpty(),x=>x.SMBL_LAB_ID == smblLabId)
             .Includes(i=>i.Contacts) //设备联系人
             .Includes(i=>i.Documents) //其他的附件信息
             .Includes(i => i.CertificateInfos)//设备资质证书
             .Includes(i => i.eMS_ENVI_REQUIRE_INFO) //环境信息
             .Includes(i => i.eMS_PURCHASE_INFO) //申购信息
             .Includes(i => i.eMS_MAINTAIN_INFO) //保养记录
             .Includes(i => i.eMS_CORRECT_INFO) //校准记录
             .Includes(i => i.eMS_COMPARISON_INFO) //比对记录
             .Includes(i => i.eMS_VERIFICATION_INFO) //性能验证记录
             .Includes(i => i.eMS_INSTALL_INFO) //安装信息
             .Includes(i => i.eMS_AUTHORIZE_INFO) //授权记录
             // .Includes(i => i.eMS_DEBUG_INFO) //调试运行记录
             .Includes(i => i.eMS_TRAIN_INFO) //培训记录
            
             .ToList();
          var equipmentContext = new EquipmentContext(_dbContext);
          
          foreach (var equipment in equipments)
          {
              equipment.SMBL_CLASS_NAME = equipmentContext.ExchangeEquipmentSmblClass(equipment.SMBL_CLASS);
          }
          
         return equipments;
    }

    
    /// <summary>
    /// 获取全部设备相关可维护信息列表
    /// </summary>
    /// <returns></returns>
    public List<EMS_EQUIPMENT_INFO> GetToBeMaintainedEquipments()
    {
        var equipments = new List<EMS_EQUIPMENT_INFO>();
            _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>()
            .Where(x=>x.IS_HIDE == "0" || x.IS_HIDE == null)
            .Includes(i=>i.Contacts) //设备联系人
            .Includes(i=>i.Documents) //其他的附件信息
            .Includes(i => i.CertificateInfos)//设备资质证书
            .Includes(i => i.eMS_ENVI_REQUIRE_INFO) //环境信息
            .Includes(i => i.eMS_PURCHASE_INFO) //申购信息
            .Includes(i => i.eMS_MAINTAIN_INFO) //保养记录
            .Includes(i => i.eMS_CORRECT_INFO) //校准记录
            .Includes(i => i.eMS_COMPARISON_INFO) //比对记录
            .Includes(i => i.eMS_VERIFICATION_INFO) //性能验证记录
            .Includes(i => i.eMS_INSTALL_INFO) //安装信息
            .Includes(i => i.eMS_AUTHORIZE_INFO) //授权记录
            // .Includes(i => i.eMS_DEBUG_INFO) //调试运行记录
            .Includes(i => i.eMS_TRAIN_INFO) //培训记录
            .ForEach(i=>equipments.Add(i));
        
        var equipmentContext = new EquipmentContext(_dbContext);
          
        foreach (var equipment in equipments)
        {
            equipment.SMBL_CLASS_NAME = equipmentContext.ExchangeEquipmentSmblClass(equipment.SMBL_CLASS);
        }
          
        return equipments;
    }


    
    public void ChangeThsEquipmentSn(string id, string sn)
    {
       var  thsEquipment =   _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>()
            .InnerJoin<THS_EQUIPMENT_INFO>((ei, ti) => ei.EQUIPMENT_ID == ti.EQUIPMENT_DID)
            .Where((ei,ti)=>ei.EQUIPMENT_ID == id)
            .Select((ei,ti)=>ti)
            .First();
       if (thsEquipment is not null)
       {
           thsEquipment!.EQUIPMENT_SN = sn;
           _dbContext.Db.GetSimpleClient<THS_EQUIPMENT_INFO>().Update(thsEquipment);
       }
    }

    public void ChangeThsEquipmentDid(string thsEquipmentId, string emsEquipmentId)
    {
        var  thsEquipment =   _dbContext.Db.Queryable<THS_EQUIPMENT_INFO>()
            .Where(ti=>ti.EQUIPMENT_ID == thsEquipmentId)
            .First();
        if (thsEquipment is not null)
        {
            thsEquipment!.EQUIPMENT_DID = emsEquipmentId;
            _dbContext.Db.GetSimpleClient<THS_EQUIPMENT_INFO>().Update(thsEquipment);
        }
    }

    /// <summary>
    /// 获取设备计算累计项的检测项
    /// </summary>
    /// <param name="smblClass">生安设备类型</param>
    /// <returns></returns>
    public  ( string Project,string ProjectClass) GetEquipmentIndicatorBySmblClass(string smblClass)
    {
        var result = smblClass switch
        {
            "1" => "SMBL_5",
            "2" => "SMBL_5",
            "4" => "SMBL_206",
            "5" => "SMBL_205",
            "7" => "SMBL_200",
            "9" => "SMBL_206",
            _ => ""
        };
        return (result , smblClass);

    }


    /// <summary>
    /// 返回对应的关机、待机、使用中的判断
    /// </summary>
    /// <param name="smblClass"></param>
    /// <returns></returns>
    public DevicesStatusValue GetDevicesStatusValue(string smblClass ,string itemId)
    {
        //生物安全柜是功率
        if (smblClass == "1")
        {
            return new DevicesStatusValue()
            {
                UseFunc = value => value > 100,
                ShutDownFunc = value => value<1,
                StandByFunc = value => value<22 & value>1,
                DisinfectionFunc = value => value>22 & value>100
            };
        }
        
        //高压锅是功率
        if (smblClass =="2")
        {
            return new DevicesStatusValue()
            {
                UseFunc = value => value >= 50,
                ShutDownFunc = value => value < 1,
                StandByFunc = value=> value >= 1 & value < 50,
                DisinfectionFunc = value => false
            };
        }
        //洗眼器是水压 无待机
        if (smblClass == "4")
        {
            return new DevicesStatusValue()
            {
                StandByFunc = value => false,
                UseFunc = value => 0< value & value < 0.15,
                ShutDownFunc = value => value >= 0.15 | value == 0,
                DisinfectionFunc = value => false
            };
        }
        //紫外灯是紫外线指数  
        if (smblClass == "5")
        {
            return new DevicesStatusValue()
            {
                StandByFunc = value => false,
                UseFunc = value => value > 0 | value < 0,
                ShutDownFunc = value => value ==0,
                DisinfectionFunc = value => false
            };
        }

        if (smblClass == "7")
        {
            return new DevicesStatusValue()
            {
                StandByFunc = value => false,
                UseFunc = value => !double.IsNaN(value),
                ShutDownFunc = value => double.IsNaN(value),
                DisinfectionFunc = value => false
            };
        }
        if (smblClass == "9")
        {
            return new DevicesStatusValue()
            {
                StandByFunc = value => false,
                UseFunc = value => value > 0 | value < 0,
                ShutDownFunc = value => value is 0 or Double.NaN, 
                DisinfectionFunc = value => false,
            };
        }
        return  new DevicesStatusValue()
        {
            StandByFunc = value => false,
            UseFunc = value => value > 0 | value < 0,
            ShutDownFunc = value => value ==0,
            DisinfectionFunc = value => false
        };
        
    }
    public List<EMS_EQUIPMENT_INFO> GetEquipmentsUseCase(string? hospitalId,string? labId, string? smblLabId)
    {
        var authContext = new AuthorityContext(_dbContext,_authorityService);
        var result = new List<EMS_EQUIPMENT_INFO>();
        var equipmentContext = new EquipmentContext(_dbContext);
        //重点生安设备类型
        var keyTypes =  new List<string>{ "1","2","4","5","9"};
         _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>().Where(x=> keyTypes.Contains(x.SMBL_CLASS))
             .WhereIF(!hospitalId.IsNullOrEmpty(),x=>x.HOSPITAL_ID == hospitalId)
             .WhereIF(!labId.IsNullOrEmpty(),x=>x.LAB_ID == labId)
             .WhereIF(!smblLabId.IsNullOrEmpty(),x=>x.SMBL_LAB_ID == smblLabId)
             .ForEach(x=>result.Add(x));
         foreach (var item in result)
         {
             item.SMBL_LAB_NAME = equipmentContext.ExchangeSmblLabName(item.SMBL_LAB_ID);
             item.SMBL_CLASS_NAME = equipmentContext.ExchangeEquipmentSmblClass(item.SMBL_CLASS);
         }

         if (hospitalId.IsNotNullOrEmpty())
         {
             var smbllabids = authContext.GetUserSmbls().Select(x=>x.SMBL_LAB_ID);
             result.RemoveAll(x => !smbllabids.Contains(x.SMBL_LAB_ID));
         }
         if (labId.IsNotNullOrEmpty())
         {
             var smbllabids = authContext.GetUserSmbls().Where(x=>x.LAB_ID == labId).Select(x=>x.SMBL_LAB_ID);
             result.RemoveAll(x => !smbllabids.Contains(x.SMBL_LAB_ID));
         }
         
         return result;
    }

    public List<EMS_EQUIPMENT_INFO> GetGetEquipmentsDistributionByHospital(string type, string hospitalId)
    {  
        
        var authContext = new AuthorityContext(_dbContext,_authorityService);
        
        var equipments =  _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>()
            .Where(x => x.HOSPITAL_ID == hospitalId)
            .Where(x => x.UNIT_ID != null)
            .Where(x => x.IS_HIDE == "0" || x.IS_HIDE == null)
            .WhereIF(type == "smbl", x => x.SMBL_FLAG == "1")
            .ToList();
        if (type == "smbl")
        {
            var smbllabids = authContext.GetUserSmbls().Select(x=>x.SMBL_LAB_ID);
            equipments.RemoveAll(x => !smbllabids.Contains(x.SMBL_LAB_ID));
        }
        else
        {
            var labids = authContext.GetUserLabList().Select(x => x.LAB_ID);
            equipments.RemoveAll(x => !labids.Contains(x.LAB_ID));
        }
       return equipments;
    }
    public List<EMS_EQUIPMENT_INFO> GetGetEquipmentsDistributionByLab(string type,string labId)
    {
        var authContext = new AuthorityContext(_dbContext,_authorityService);
        var equipments =  _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>()
            .Where(x => x.LAB_ID == labId)
            .Where(x => x.UNIT_ID != null)
            .Where(x => x.IS_HIDE == "0" || x.IS_HIDE == null)
            .WhereIF(type == "smbl", x => x.SMBL_FLAG == "1")
            .ToList();
        if (type == "smbl")
        {
            var smbllabids = authContext.GetUserSmbls().Where(x=>x.LAB_ID == labId).Select(x=>x.SMBL_LAB_ID);
            equipments.RemoveAll(x => !smbllabids.Contains(x.SMBL_LAB_ID));
        }
        return equipments;
    }
    public List<EMS_EQUIPMENT_INFO> GetGetEquipmentsDistributionBySmblLab(string smblLabId)
    {
        var equipments =  _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>()
            .Where(x => x.SMBL_LAB_ID == smblLabId)
            .Where(x => x.UNIT_ID != null)
            .Where(x => x.IS_HIDE == "0" || x.IS_HIDE == null)
            .ToList();
        return equipments;
    }
    /// <summary>
    /// 调用外部接口获取监控设备信息
    /// </summary>
    /// <param name="equipmentInfo"></param>
    /// <returns></returns>
    public IoTDevicesDto? GetEquipmentIoTData(EMS_EQUIPMENT_INFO equipmentInfo)
    {
        try
        {
            var rsp =  _client.GetIoTDeviceInfo("",equipmentInfo.SERIAL_NUMBER);
            if (rsp.Data.IsOnline == 0)
            {
                return null;
            }
            return rsp.Data;    
        }
        catch (Exception e)
        {
            return null;
        }
    }
    public UltravioletLampDto GetUltravioletLampDeviceData(EMS_EQUIPMENT_INFO equipmentInfo)
    {
        var equipment =  _dbContext.Db.Queryable<THS_EQUIPMENT_INFO>().Where(x => x.EQUIPMENT_DID == equipmentInfo.EQUIPMENT_ID).First();
        if (equipment.EQUIPMENT_SN.IsNullOrEmpty())
        {
            try
            {
                var data =  _client.GetGetUltravioletLampDeviceInfo("",equipmentInfo.SERIAL_NUMBER);

                if (double.Parse(data.Value)> 0.1)
                {
                    data.SwitchStatus = 2;
                }
                else
                {
                    data.SwitchStatus = 0;
                }
                return data;    
            }
            catch (Exception e)
            {
                return null;
            }
        }
        else
        {
            try
            {
                var data =  _client.GetGetUltravioletLampDeviceInfo(equipment.EQUIPMENT_SN,"");
                
                return data;    
            }
            catch (Exception e)
            {
                return null;
            }
        }
        
    }

    public WaterPressureDto GetWaterPressureDtoDeviceData(EMS_EQUIPMENT_INFO equipmentInfo)
    {
        var equipment =  _dbContext.Db.Queryable<THS_EQUIPMENT_INFO>().Where(x => x.EQUIPMENT_DID == equipmentInfo.EQUIPMENT_ID).First();
        if (equipment.EQUIPMENT_SN.IsNullOrEmpty())
        {
            try
            {
                var data =  _client.GetGetWaterPressureDeviceInfo(equipment.EQUIPMENT_SN,"");

                if (double.Parse(data.Value)> 0.1)
                {
                    data.SwitchStatus = 2;
                }
                else
                {
                    data.SwitchStatus = 0;
                }
                return data;    
            }
            catch (Exception e)
            {
                return null;
            }
        }
        else
        {
            try
            {
                var data =  _client.GetGetWaterPressureDeviceInfo(equipment.EQUIPMENT_SN,"");
                
                return data;    
            }
            catch (Exception e)
            {
                return null;
            }
        }
        
    }
    /// <summary>
    /// 环境一体机电信接口查询
    /// </summary>
    /// <param name="equipmentInfo">监控设备信息</param>
    /// <returns></returns>
    public List<EnvironmentDevicesDto>? GetEnvironmentDeviceData(EMS_EQUIPMENT_INFO equipmentInfo)
    {
        try
        {
            var rsp =  _client.GetEnvironmentDevicesInfo(equipmentInfo.SERIAL_NUMBER,"");
            return rsp.Data;    
        }
        catch (Exception e)
        {
            return null;
        }
    }
    
    /// <summary>
    /// 查询已经被纳入监测的设备
    /// </summary>
    /// <returns></returns>
    public List<EMS_EQUIPMENT_INFO> GetObtainMonitoringEquipments()
    {
        var equipments = _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>()
            .InnerJoin<THS_EQUIPMENT_INFO>((emsEquipment, thsEquipment) =>
                emsEquipment.EQUIPMENT_ID == thsEquipment.EQUIPMENT_DID &&
                emsEquipment.SMBL_LAB_ID == thsEquipment.UNIT_ID)
            .ToList();
        return equipments;
    }

    public string GetEquipmentSwitchStatus(EMS_EQUIPMENT_INFO equipment,DateTime? dateTime)
    {
        try
        {
            var thsEquipment = _dbContext.Db.Queryable<THS_EQUIPMENT_INFO>()
                .Where(x => x.EQUIPMENT_DID == equipment.EQUIPMENT_ID).First();

            if (thsEquipment is null)
            {
                return equipmentContext.ExchangeEquipmentState(equipment.EQUIPMENT_STATE);
            }
            var monitor = _dbContext.Db.Queryable<THS_MONITOR_LIST>()
                .Where(x=>x.EQUIPMENT_ID == thsEquipment.EQUIPMENT_ID)
                .WhereIF(dateTime.HasValue,x=>x.MONITOR_TIME.HasValue && x.MONITOR_TIME.Value.Date == dateTime.Value.Date)
                .OrderByDescending(x=>x.MONITOR_TIME)
                .First();
            if (monitor is null)
            {
                return equipmentContext.ExchangeEquipmentState(equipment.EQUIPMENT_STATE);
            }

            monitor.ThsMonitorResults = _dbContext.Db.Queryable<THS_MONITOR_RESULT>()
                .Where(x => x.MONITOR_ID == monitor.MONITOR_ID).ToList();
            if (!monitor.ThsMonitorResults.Any())
            {
                return equipmentContext.ExchangeEquipmentState(equipment.EQUIPMENT_STATE);
            }
            else
            {
                var monitorResult = monitor.ThsMonitorResults.FirstOrDefault(x=>x.ITEM_ID == "SMBL_1");
                if (monitorResult is null)
                {
                    return equipmentContext.ExchangeEquipmentState(equipment.EQUIPMENT_STATE);
                }
                else
                {
                    var result = GetSwitchStatusName(monitorResult.ITEM_VALUE);
                    return result;
                }
            }
        }
        catch (BizException e)
        {
            Console.WriteLine("不存在这个设备的监控信息");
            return equipmentContext.ExchangeEquipmentState(equipment.EQUIPMENT_STATE);
        }
    }

    /// <summary>
    /// 获取设备监测数据
    /// </summary>
    /// <param name="equipmentId">设备id</param>
    public List<THS_MONITOR_LIST> GetObtainMonitoringData(string equipmentId , DateTime? dateTime = null)
    {
        var equipmentInfo = _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>().First(x => x.EQUIPMENT_ID == equipmentId);
        if (equipmentInfo is null)
        {
            Log.Information("设备不存在",equipmentId);
            return new List<THS_MONITOR_LIST>();
        }

        var thsEquipment = _dbContext.Db.Queryable<THS_EQUIPMENT_INFO>().Where(x =>
                            x.UNIT_ID == equipmentInfo.SMBL_LAB_ID && x.EQUIPMENT_DID == equipmentId)
                            .First();
        
        if (thsEquipment is null)
        {
            Log.Information("监控设备不存在",equipmentId);
            return new List<THS_MONITOR_LIST>();
        }
        var result = _dbContext.Db.Queryable<THS_MONITOR_LIST>()
            .Includes(x=> x.ThsMonitorResults)
            .WhereIF(dateTime.HasValue,x=>x.MONITOR_TIME.HasValue && x.MONITOR_TIME.Value.Date == dateTime.Value.Date)
            .Where(x => x.UNIT_ID == thsEquipment.UNIT_ID && x.EQUIPMENT_ID == thsEquipment.EQUIPMENT_ID)
            .OrderByDescending(x=>x.MONITOR_TIME)
            .ToList();
        return result.Where(x=>x.ThsMonitorResults.Any()).ToList();
    }


    /// <summary>
    /// 根据时间范围查询设备监测数据
    /// </summary>
    /// <param name="equipmentId"></param>
    /// <param name="startTime"></param>
    /// <param name="endTime"></param>
    /// <returns></returns>
    public List<THS_MONITOR_LIST> GetObtainMonitoringDataByTime(string equipmentId, DateTime startTime, DateTime endTime)
    {
        var equipmentInfo = _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>().First(x => x.EQUIPMENT_ID == equipmentId);
        if (equipmentInfo is null)
        {
            Log.Information("设备不存在",equipmentId);
            return new List<THS_MONITOR_LIST>();
        }
        var thsEquipment = _dbContext.Db.Queryable<THS_EQUIPMENT_INFO>().Where(x =>
                x.UNIT_ID == equipmentInfo.SMBL_LAB_ID && x.EQUIPMENT_DID == equipmentId)
            .First();
        if (thsEquipment is null)
        {
            Log.Information("监控设备不存在",equipmentInfo);
            return new List<THS_MONITOR_LIST>();
        }
        var result = _dbContext.Db.Queryable<THS_MONITOR_LIST>()
            .Includes(x=> x.ThsMonitorResults)
            .Where(x=> startTime.Date <= x.MONITOR_TIME!.Value  && x.MONITOR_TIME.Value.Date <= endTime.Date)
            .Where(x => x.UNIT_ID == thsEquipment.UNIT_ID && x.EQUIPMENT_ID == thsEquipment.EQUIPMENT_ID)
            .OrderByDescending(x=>x.MONITOR_TIME)
            .ToList();
        return result.Where(x=>x.ThsMonitorResults.Any()).ToList();
    }

    public List<Room> GetRooms(string? name)
    {
        throw new NotImplementedException();
    }

    public object GetAccessControls()
    {
        var equipments = _client.GetMentjin();
        return equipments;
    }

    public void RoomControl(string positiopnId, string roomId)
    {
        throw new NotImplementedException();
    }

    public List<DeviceType> GetDeviceTypes()
    {
        throw new NotImplementedException();
    }

    public List<SensorsType> GetSensorTypes()
    {
        throw new NotImplementedException();
    }

    public List<MonitorType> GetMonitorTypes()
    {
        throw new NotImplementedException();
    }

    public void AddDevice(ThirdPartyDevice device)
    {
        throw new NotImplementedException();
    }
    public List<ThirdPartyDevice> GetThirdPartyDevices(string roomId, int type, bool isOnLine)
    {
        throw new NotImplementedException();
    }
    public List<Sensor> GetSensors(List<int>? sensorTypes, string? roomId, string? labId)
    {
        throw new NotImplementedException();
    }
    public void DeviceControl(string equipmentId, string sn)
    {
        throw new NotImplementedException();
    }
    public void DeviceControlSensors(string equipmentId, List<string> sensorSns)
    {
        throw new NotImplementedException();
    }
    public List<DeviceMonitorRecord> GetEquipmentMonitorData(string equipmentId, List<string>? sensorSns)
    {
        throw new NotImplementedException();
    }

    /// <summary>
    /// 写入生物安全柜只智能开关监测数据
    /// </summary>
    /// <param name="equipment">设备信息</param>
    /// <param name="data">获取到的监测数据</param>
    public void AddObtainMonitoringData(EMS_EQUIPMENT_INFO equipment , IoTDevicesDto  data)
    {
        var recordTime = DateTime.Now;
        try
        {
            _dbContext.Db.CurrentConnectionConfig.IsAutoCloseConnection = false;
            var thsEquipment = _dbContext.Db.Queryable<THS_EQUIPMENT_INFO>()
                .Where(x =>
                    x.UNIT_ID == equipment.SMBL_LAB_ID && x.EQUIPMENT_DID == equipment.EQUIPMENT_ID)
                .First();

            if (thsEquipment is null)
            {
                throw new BizException("监控设备不存在");
            }

            var points = _dbContext.Db.Queryable<THS_EQUIPMENT_POINT>()
                .Where(x => x.UNIT_ID == thsEquipment.UNIT_ID && x.EQUIPMENT_ID == thsEquipment.EQUIPMENT_ID)
                .ToList();
            if (!points.Any())
            {
                throw new BizException("请创建设备的监测点信息");
            }

            foreach (var point in points)
            {
                var pointItems = GetMonitorItemsByEquipment(point);
                var id = IDGenHelper.CreateGuid();
                var record = new THS_MONITOR_LIST
                {
                    MONITOR_ID =  id,
                    EQUIPMENT_ID = point.EQUIPMENT_ID,
                    POINT_ID = point.POINT_ID,
                    UNIT_ID = point.UNIT_ID,
                    HOSPITAL_ID = point.HOSPITAL_ID,
                    MONITOR_DATE = DateTime.Parse(data.CreateTime ?? recordTime.ToString("yyyy-MM-dd")).Date,
                    TIME_POINT = DateTime.Parse(data.CreateTime ?? recordTime.ToString("yyyy-MM-dd HH:mm:dd")).ToString("HH:mm:dd"),
                    MONITOR_PERSON = "system",
                    MONITOR_TIME = recordTime,
                    MONITOR_COMPUTER = "",
                    MONITOR_TID = "000000",
                    MONITOR_STATE = data.SwitchStatus == 3 ? "2" : "1",
                    FIRST_RPERSON = "system",
                    FIRST_RTIME = recordTime,
                    LAST_MPERSON = "system",
                    LAST_MTIME = recordTime,
                    REMARK = "生安眼监测",
                    OPER_PERSON = "",
                    OPER_TIME = null,
                    OPER_COMPUTER = "",
                    ThsMonitorResults = new ()
                };
                _dbContext.Db.Insertable(record).ExecuteCommand();
                AddMonitoringDataResult(record, pointItems, data);
                ConutEquipmentMonitoringItems(equipment, record);
            }

        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }
        finally
        {
            _dbContext.Db.Ado.Connection.Close();
        }
    }

    public void AddLampObtainMonitoring(EMS_EQUIPMENT_INFO equipment, UltravioletLampDto data)
    {
        var recordTime = DateTime.Now;
        try
        {
            _dbContext.Db.CurrentConnectionConfig.IsAutoCloseConnection = false;
            var thsEquipment = _dbContext.Db.Queryable<THS_EQUIPMENT_INFO>()
                .Where(x =>
                    x.UNIT_ID == equipment.SMBL_LAB_ID && x.EQUIPMENT_DID == equipment.EQUIPMENT_ID)
                .First();

            if (thsEquipment is null)
            {
                throw new BizException("监控设备不存在");
            }

            var points = _dbContext.Db.Queryable<THS_EQUIPMENT_POINT>()
                .Where(x => x.UNIT_ID == thsEquipment.UNIT_ID && x.EQUIPMENT_ID == thsEquipment.EQUIPMENT_ID)
                .ToList();
            if (!points.Any())
            {
                throw new BizException("请创建设备的监测点信息");
            }
            
            foreach (var point in points)
            {
                var pointItems = GetMonitorItemsByEquipment(point);
                var id = IDGenHelper.CreateGuid();
                var record = new THS_MONITOR_LIST
                {
                    MONITOR_ID =  id,
                    EQUIPMENT_ID = point.EQUIPMENT_ID,
                    POINT_ID = point.POINT_ID,
                    UNIT_ID = point.UNIT_ID,
                    HOSPITAL_ID = point.HOSPITAL_ID,
                    MONITOR_DATE = DateTime.Parse(recordTime.ToString("yyyy-MM-dd")).Date,
                    TIME_POINT = DateTime.Parse( recordTime.ToString("yyyy-MM-dd HH:mm:dd")).ToString("HH:mm:dd"),
                    MONITOR_PERSON = "system",
                    MONITOR_TIME = recordTime,
                    MONITOR_COMPUTER = "",
                    MONITOR_TID = "000000",
                    MONITOR_STATE =  data.Alarm == 1 ? "2" : "1",
                    FIRST_RPERSON = "system",
                    FIRST_RTIME = recordTime,
                    LAST_MPERSON = "system",
                    LAST_MTIME = recordTime,
                    REMARK = "生安眼监测",
                    OPER_PERSON = "",
                    OPER_TIME = null,
                    OPER_COMPUTER = "",
                    ThsMonitorResults = new ()
                };
                _dbContext.Db.Insertable(record).ExecuteCommand();
                AddLampMonitoringDataResult(record, pointItems, data);
                ConutEquipmentMonitoringItems(equipment, record);
            }

        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }
        finally
        {
            _dbContext.Db.Ado.Connection.Close();
        }
    }
    
    private void AddLampMonitoringDataResult(THS_MONITOR_LIST record, List<THS_EQUIPMENT_POINT_ITEM> pointItems, UltravioletLampDto data)
    {

        foreach (var item in pointItems)
        {
            var recordItemResult = CreatLampMonitorResult(record.MONITOR_ID, item, data);
            _dbContext.Db.Insertable(recordItemResult).ExecuteCommand();
            record.ThsMonitorResults.Add(recordItemResult);
        }
    }
    
    public void AddWaterObtainMonitoring(EMS_EQUIPMENT_INFO equipment, WaterPressureDto data)
    {
        var recordTime = DateTime.Now;
        try
        {
            _dbContext.Db.CurrentConnectionConfig.IsAutoCloseConnection = false;
            var thsEquipment = _dbContext.Db.Queryable<THS_EQUIPMENT_INFO>()
                .Where(x =>
                    x.UNIT_ID == equipment.SMBL_LAB_ID && x.EQUIPMENT_DID == equipment.EQUIPMENT_ID)
                .First();

            if (thsEquipment is null)
            {
                throw new BizException("监控设备不存在");
            }

            var points = _dbContext.Db.Queryable<THS_EQUIPMENT_POINT>()
                .Where(x => x.UNIT_ID == thsEquipment.UNIT_ID && x.EQUIPMENT_ID == thsEquipment.EQUIPMENT_ID)
                .ToList();
            if (!points.Any())
            {
                throw new BizException("请创建设备的监测点信息");
            }
            
            foreach (var point in points)
            {
                var pointItems = GetMonitorItemsByEquipment(point);
                var id = IDGenHelper.CreateGuid();
                var record = new THS_MONITOR_LIST
                {
                    MONITOR_ID =  id,
                    EQUIPMENT_ID = point.EQUIPMENT_ID,
                    POINT_ID = point.POINT_ID,
                    UNIT_ID = point.UNIT_ID,
                    HOSPITAL_ID = point.HOSPITAL_ID,
                    MONITOR_DATE = DateTime.Parse(recordTime.ToString("yyyy-MM-dd")).Date,
                    TIME_POINT = DateTime.Parse( recordTime.ToString("yyyy-MM-dd HH:mm:dd")).ToString("HH:mm:dd"),
                    MONITOR_PERSON = "system",
                    MONITOR_TIME = recordTime,
                    MONITOR_COMPUTER = "",
                    MONITOR_TID = "000000",
                    MONITOR_STATE =  data.Alarm == 1 ? "2" : "1",
                    FIRST_RPERSON = "system",
                    FIRST_RTIME = recordTime,
                    LAST_MPERSON = "system",
                    LAST_MTIME = recordTime,
                    REMARK = "生安眼监测",
                    OPER_PERSON = "",
                    OPER_TIME = null,
                    OPER_COMPUTER = "",
                    ThsMonitorResults = new()
                };
                _dbContext.Db.Insertable(record).ExecuteCommand();
                AddWaterMonitoringDataResult(record, pointItems, data);
                ConutEquipmentMonitoringItems(equipment, record);
            }

        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }
        finally
        {
            _dbContext.Db.Ado.Connection.Close();
        }
    }

    private void AddWaterMonitoringDataResult(THS_MONITOR_LIST record, List<THS_EQUIPMENT_POINT_ITEM> pointItems, WaterPressureDto data)
    {
        foreach (var item in pointItems)
        {
            var recordItemResult = CreatWaterMonitorResult(record.MONITOR_ID, item, data);
            _dbContext.Db.Insertable(recordItemResult).ExecuteCommand();
            record.ThsMonitorResults.Add(recordItemResult);
        }
    }
    /// <summary>
    /// 记录紫外灯监测结果值
    /// </summary>
    /// <param name="preRecord"></param>
    /// <param name="monitorId"></param>
    /// <param name="monitorPointItem"></param>
    /// <param name="data"></param>
    /// <returns></returns>
    private THS_MONITOR_RESULT CreatLampMonitorResult( string? monitorId, THS_EQUIPMENT_POINT_ITEM monitorPointItem, UltravioletLampDto data)
    {
        var now = DateTime.Now;
        var result = new THS_MONITOR_RESULT();
        result.MONITOR_ID = monitorId;
        result.ITEM_ID = monitorPointItem.ITEM_ID;
        result.ITEM_VALUE = data is null ? "0":data.Value;
        result.ITEM_STATE = monitorPointItem.ABNORMAL_ITEM_STATE;
        result.FIRST_RPERSON = "system";
        result.FIRST_RTIME = now;
        result.LAST_MPERSON= "system";
        result.LAST_MTIME= now;
        result.REMARK = "生安眼监测";
        result.RULE_ID = monitorPointItem.RULE_ID;
        //result.ITEM_VLAUE_JSON = "";
        result.ITEM_STATUS = "Z";
        if (monitorPointItem.ITEM_ID == "SMBL_1")
        {
            result.ITEM_VALUE = GetOnlineStatus(data.SwitchStatus);
            if (data.SwitchStatus == 3)
            {
                result.ITEM_STATE = "2";
                result.CHART_STATE = "2";
                result.ITEM_STATUS = "H";
            }
        }
        if (monitorPointItem.ITEM_ID == "SMBL_205")
        {
            result.ITEM_VALUE = data.Value;
            if (data.Alarm == 1 )
            {
                if (double.Parse(data.Value) < 2)
                {
                    result.ITEM_STATUS = "L";
                    result.ITEM_STATE = "2";
                }

                if (double.Parse(data.Value) > 5)
                {
                    result.ITEM_STATUS = "H";
                    result.ITEM_STATE = "2";
                }
            }
        }
        return result;
    }

    private THS_MONITOR_RESULT CreatWaterMonitorResult( string? monitorId, THS_EQUIPMENT_POINT_ITEM monitorPointItem, WaterPressureDto data)
    {
        var now = DateTime.Now;
        var result = new THS_MONITOR_RESULT();
        result.MONITOR_ID = monitorId;
        result.ITEM_ID = monitorPointItem.ITEM_ID;
        result.ITEM_VALUE = data is null ? "0":data.Value;
        result.ITEM_STATE = monitorPointItem.ABNORMAL_ITEM_STATE;
        result.FIRST_RPERSON = "system";
        result.FIRST_RTIME = now;
        result.LAST_MPERSON= "system";
        result.LAST_MTIME= now;
        result.REMARK = "生安眼监测";
        result.RULE_ID = monitorPointItem.RULE_ID;
        //result.ITEM_VLAUE_JSON = "";
        result.ITEM_STATUS = "Z";
        if (monitorPointItem.ITEM_ID == "SMBL_1")
        {
            result.ITEM_VALUE = GetOnlineStatus(data.SwitchStatus);
            if (data.SwitchStatus == 3)
            {
                result.ITEM_STATE = "2";
                result.CHART_STATE = "2";
                result.ITEM_STATUS = "H";
            }
        }
        if (monitorPointItem.ITEM_ID == "SMBL_206")
        {
            result.ITEM_VALUE = data.Value;
            if (data.Alarm == 1 )
            {
                if (double.Parse(data.Value) < 0.1)
                {
                    result.ITEM_STATUS = "L";
                    result.ITEM_STATE = "2";
                }

                if (double.Parse(data.Value) > 0.3)
                {
                    result.ITEM_STATUS = "H";
                    result.ITEM_STATE = "2";
                }
            }
        }
        return result;
    }

    /// <summary>
    /// 添加环境一体机监数据
    /// </summary>
    /// <param name="equipment">ems设备</param>
    /// <param name="data">电信监测数据</param>
    public void AddEvnObtainMonitoring(EMS_EQUIPMENT_INFO equipment , List<EnvironmentDevicesDto> datas)
    {
        var recordTime = DateTime.Now;
        var data = datas[0];
        try
        {
            _dbContext.Db.CurrentConnectionConfig.IsAutoCloseConnection = false;
            var thsEquipment = _dbContext.Db.Queryable<THS_EQUIPMENT_INFO>()
                .Where(x =>
                    x.UNIT_ID == equipment.SMBL_LAB_ID && x.EQUIPMENT_DID == equipment.EQUIPMENT_ID)
                .First();

            if (thsEquipment is null)
            {
                throw new BizException("监控设备不存在");
            }

            var points = _dbContext.Db.Queryable<THS_EQUIPMENT_POINT>()
                .Where(x => x.UNIT_ID == thsEquipment.UNIT_ID && x.EQUIPMENT_ID == thsEquipment.EQUIPMENT_ID)
                .ToList();
            if (!points.Any())
            {
                throw new BizException("请创建设备的监测点信息");
            }
            
            foreach (var point in points)
            {
                var pointItems = GetMonitorItemsByEquipment(point);
                var id = IDGenHelper.CreateGuid();
                var record = new THS_MONITOR_LIST
                {
                    MONITOR_ID =  id,
                    EQUIPMENT_ID = point.EQUIPMENT_ID,
                    POINT_ID = point.POINT_ID,
                    UNIT_ID = point.UNIT_ID,
                    HOSPITAL_ID = point.HOSPITAL_ID,
                    MONITOR_DATE = DateTime.Parse(data.CreateTime ?? recordTime.ToString("yyyy-MM-dd")).Date,
                    TIME_POINT = DateTime.Parse(data.CreateTime ?? recordTime.ToString("yyyy-MM-dd HH:mm:dd")).ToString("HH:mm:dd"),
                    MONITOR_PERSON = "system",
                    MONITOR_TIME = recordTime,
                    MONITOR_COMPUTER = "",
                    MONITOR_TID = "000000",
                    MONITOR_STATE =  datas.Any(x=>x.Status == 0) ? "2" : "1",
                    FIRST_RPERSON = "system",
                    FIRST_RTIME = recordTime,
                    LAST_MPERSON = "system",
                    LAST_MTIME = recordTime,
                    REMARK = "生安眼监测",
                    OPER_PERSON = "",
                    OPER_TIME = null,
                    OPER_COMPUTER = "",
                    ThsMonitorResults = new()
                };
                _dbContext.Db.Insertable(record).ExecuteCommand();
                AddEvnMonitoringDataResult(record, pointItems, datas);
                ConutEquipmentMonitoringItems(equipment, record);
            }

        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }
        finally
        {
            _dbContext.Db.Ado.Connection.Close();
        }
    }

    private void AddEvnMonitoringDataResult(THS_MONITOR_LIST record, List<THS_EQUIPMENT_POINT_ITEM> pointItems, List<EnvironmentDevicesDto> datas)
    {
        foreach (var item in pointItems)
        {
            var itemTypeEnum = item.ITEM_ID switch
            {
                "SMBL_200" => BioAlarmTypeEnum.Temperature,
                "SMBL_201" => BioAlarmTypeEnum.Humidity,
                "SMBL_202" => BioAlarmTypeEnum.Noise,
                "SMBL_203" => BioAlarmTypeEnum.AirPressure,
            };
            if (datas.FirstOrDefault(x=>x.BioAlarmType == itemTypeEnum) is null)
            {
                Log.Error($"本次监控记录：{record.MONITOR_ID} 缺失类型为{itemTypeEnum}的监控数据");
                continue;
            }
            var recordItemResult = CreatEvnMonitorResult(record.MONITOR_ID, item, datas.FirstOrDefault(x=>x.BioAlarmType == itemTypeEnum));
            _dbContext.Db.Insertable(recordItemResult).ExecuteCommand();
            record.ThsMonitorResults.Add(recordItemResult);
        }
    }

    private THS_MONITOR_RESULT CreatEvnMonitorResult( string? monitorId, THS_EQUIPMENT_POINT_ITEM monitorPointItem, EnvironmentDevicesDto data)
    {
        var now = DateTime.Now;
        var result = new THS_MONITOR_RESULT();
        result.MONITOR_ID = monitorId;
        result.ITEM_ID = monitorPointItem.ITEM_ID;
        result.ITEM_VALUE = data is null ? "0":data.Value.ToString("F1");
        result.ITEM_STATE = monitorPointItem.ABNORMAL_ITEM_STATE;
        result.FIRST_RPERSON = "system";
        result.FIRST_RTIME = now;
        result.LAST_MPERSON= "system";
        result.LAST_MTIME= now;
        result.REMARK = "生安眼监测";
        result.RULE_ID = monitorPointItem.RULE_ID;
        //result.ITEM_VLAUE_JSON = "";
        result.ITEM_STATUS = "Z";
        if (data.BioAlarmType is BioAlarmTypeEnum.Temperature)
        {
            //取温度的报警规则确定当权状态值
            result.ALARM_RANGE = "<18.0 >27.0";
            if (data.Value > 27.0)
            {
                result.ITEM_STATUS = "H";
                result.ITEM_STATE = "2";
            }

            if (data.Value < 18.0)
            {
                result.ITEM_STATUS = "L";
                result.ITEM_STATE = "2";
            }
        }
        if (data.BioAlarmType is BioAlarmTypeEnum.Humidity)
        {
            //取湿度报警规则确定当权状态值
            result.ALARM_RANGE = "<30.0 >65.0";
            if (data.Value > 65.0)
            {
                result.ITEM_STATUS = "H";
                result.ITEM_STATE = "2";
            }

            if (data.Value < 30.0)
            {
                result.ITEM_STATUS = "L";
                result.ITEM_STATE = "2";
            }
        }
        if (data.BioAlarmType is BioAlarmTypeEnum.Noise)
        {
            //取噪声报警规则确定当权状态值
            result.ALARM_RANGE = "<0.0 >60.0";
            if (data.Value > 60.0)
            {
                result.ITEM_STATUS = "H";
                result.ITEM_STATE = "2";
            }

            if (data.Value < 0.0)
            {
                result.ITEM_STATUS = "L";
                result.ITEM_STATE = "2";
            }
        }
        if (data.BioAlarmType is BioAlarmTypeEnum.AirPressure)
        {
            //取大气压强报警规则确定当权状态值
            result.ALARM_RANGE = "<-20.0 >110.0";
            if (data.Value > 110.0)
            {
                result.ITEM_STATUS = "H";
                result.ITEM_STATE = "2";
            }
            if (data.Value < -20.0)
            {
                result.ITEM_STATUS = "L";
                result.ITEM_STATE = "2";
            }
        }
        return result;
    }

    public List<THS_MONITOR_ITEM> GetMonitorItems(string smblClass)
    {
        var items = Equipment2MonitorItems(smblClass);
        var result = new List<THS_MONITOR_ITEM>();
        var thsItems =  _dbContext.Db.Queryable<THS_MONITOR_ITEM>()
            .Where(x => items.Contains(x.ITEM_ID)).ToList();
        
        if (!thsItems.Any())
        { 
            foreach (var item in items)
            {
                var name = item switch
                {
                    "SMBL_1"=> "运行状态",
                    "SMBL_2"=> "用电量",
                    "SMBL_3"=> "电压",
                    "SMBL_4"=> "电流",
                    "SMBL_5"=> "功率",
                    "SMBL_6"=> "累计开机时长",
                    "SMBL_200"=>"温度",
                    "SMBL_201"=>"湿度",
                    "SMBL_202"=>"噪声",
                    "SMBL_203"=>"气压",
                    "SMBL_205"=>"紫外线指数",
                    "SMBL_206"=>"水压",
                    _=>""
                };

                var unit = item switch
                {
                    "SMBL_1"=> "",
                    "SMBL_2"=> "kw*h",
                    "SMBL_3"=> "V",
                    "SMBL_4"=> "A",
                    "SMBL_5"=> "W",
                    "SMBL_6"=> "分钟",
                    "SMBL_200"=>"°C",
                    "SMBL_201"=>"%HR",
                    "SMBL_202"=>"dB",
                    "SMBL_203"=>"kPa",
                    "SMBL_205"=>"W/㎡",
                    "SMBL_206"=>"MPa",
                    _=>""
                };
                
                var precision = item switch
                {
                    "SMBL_1"=> "0",
                    "SMBL_2"=> "3",
                    "SMBL_3"=> "3",
                    "SMBL_4"=> "3",
                    "SMBL_5"=> "3",
                    "SMBL_200"=>"1",
                    "SMBL_201"=>"1",
                    "SMBL_202"=>"1",
                    "SMBL_203"=>"1",
                    "SMBL_205"=>"3",
                    "SMBL_206"=>"3",
                    _=>"0"
                };

                if (name.IsNullOrEmpty())
                {
                    continue;
                }
                
                result.Add(new ()
                {
                    ITEM_ID = item,
                    HOSPITAL_ID = "33A001",
                    ITEM_NAME = name,
                    ITEM_SORT = "",
                    ITEM_UNIT = unit,
                    ITEM_STATE = "1",
                    ITEM_PRECISION = precision,
                    FIRST_RPERSON = "system",
                    FIRST_RTIME = DateTime.Now,
                    LAST_MPERSON = "system",
                    LAST_MTIME = DateTime.Now,
                    REMARK = "生安眼监测"
                });
            }
            _dbContext.Db.Insertable(result).ExecuteCommand();
            return result;
        }
        if (items.Length > thsItems.Count)
        {
            var addItems = items.Except(thsItems.Select(x=>x.ITEM_ID)).ToArray();
            foreach (var item in addItems)
            {
                var name = item switch
                {
                    "SMBL_1"=> "运行状态",
                    "SMBL_2"=> "用电量",
                    "SMBL_3"=> "电压",
                    "SMBL_4"=> "电流",
                    "SMBL_5"=> "功率",
                    "SMBL_6"=> "累计开机时长",
                    "SMBL_200"=>"温度",
                    "SMBL_201"=>"湿度",
                    "SMBL_202"=>"噪声",
                    "SMBL_203"=>"气压",
                    "SMBL_205"=>"紫外线指数",
                    "SMBL_206"=>"水压",
                    _=>""
                };

                var unit = item switch
                {
                    "SMBL_1"=> "",
                    "SMBL_2"=> "kw*h",
                    "SMBL_3"=> "V",
                    "SMBL_4"=> "A",
                    "SMBL_5"=> "W",
                    "SMBL_6"=> "分钟",
                    "SMBL_200"=>"°C",
                    "SMBL_201"=>"%",
                    "SMBL_202"=>"dB",
                    "SMBL_203"=>"kPa",
                    "SMBL_205"=>"W/㎡",
                    "SMBL_206"=>"MPa",
                    _=>""
                };
                
                var precision = item switch
                {
                    "SMBL_1"=> "0",
                    "SMBL_2"=> "3",
                    "SMBL_3"=> "3",
                    "SMBL_4"=> "3",
                    "SMBL_5"=> "3",
                    "SMBL_200"=>"1",
                    "SMBL_201"=>"1",
                    "SMBL_202"=>"1",
                    "SMBL_203"=>"1",
                    "SMBL_205"=>"3",
                    "SMBL_206"=>"3",
                    _=>"0"
                };

                if (name.IsNullOrEmpty())
                {
                    continue;
                }
                var data = new THS_MONITOR_ITEM()
                {
                    ITEM_ID = item,
                    HOSPITAL_ID = "33A001",
                    ITEM_NAME = name,
                    ITEM_SORT = "",
                    ITEM_UNIT = unit,
                    ITEM_STATE = "1",
                    ITEM_PRECISION = precision,
                    FIRST_RPERSON = "system",
                    FIRST_RTIME = DateTime.Now,
                    LAST_MPERSON = "system",
                    LAST_MTIME = DateTime.Now,
                    REMARK = "生安眼监测"
                };
                thsItems.Add(_dbContext.Db.Insertable(data).ExecuteReturnEntity());
            }
            //throw new BizException("请完善监测项THS_MONITOR_ITEM 的数据");
        }
        result.AddRange(thsItems);
        return result;
    }

    /// <summary>
    /// 多种生安设备监测项的指标
    /// </summary>
    /// <param name="smblClass"></param>
    /// <returns></returns>

    /// <summary>
    /// 查询设备的监测项
    /// </summary>
    /// <param name="thsEquipment"> 监测设备信息 </param>
    /// <returns></returns>
    private List<THS_EQUIPMENT_POINT_ITEM> GetMonitorItemsByEquipment(THS_EQUIPMENT_POINT point)
    {
        var pointItems = _dbContext.Db.Queryable<THS_EQUIPMENT_POINT_ITEM>()
            .Where(x=>point.POINT_ID == x.POINT_ID)
            .ToList();
        return pointItems;
    }
    
    /// <summary>
    /// 添加监测设备信息
    /// </summary>
    /// <param name="equipment">设备档案信息</param>
    /// <param name="roomId">房间号</param>
    /// <returns></returns>
    /// <exception cref="BizException"></exception>
    private THS_EQUIPMENT_INFO AddAndGetNewThsEquipment(EMS_EQUIPMENT_INFO equipment , string roomId , string sn)
    {
        var keyTypes =  new List<string>{ "1","2", "4", "5","9"};
        var now = DateTime.Now;
        if (equipment.SMBL_LAB_ID.IsNullOrEmpty())
        {
            throw new BizException("请把设备添加分配到备案实验室");
        }

        if (equipment.SERIAL_NUMBER.IsNullOrEmpty())
        {
            throw new BizException("请填写设备序列号 （SERIAL_NUMBER）");
        }

        if (equipment.POSITION_ID.IsNullOrEmpty())
        {
            throw new BizException("请维护设备房间");
        }
        var roomInfo = _dbContext.Db.Queryable<SYS6_POSITION_DICT>()
            .First(x => x.POSITION_ID == roomId);
        if (roomInfo is null)
        {
            throw new BizException($"请维护房间信息，基础数据里面的公共自字典上 ，sbml_lab_id的值为：{equipment.SMBL_LAB_ID}");
        }
        
        //备案实验室下，有没有这台监测设备，没有则新建
        var thsEquipment =  _dbContext.Db.Queryable<THS_EQUIPMENT_INFO>()
            .First(x => x.UNIT_ID == equipment.SMBL_LAB_ID && x.EQUIPMENT_DID == equipment.EQUIPMENT_ID);
        if (thsEquipment is null)
        {
            var id = IDGenHelper.CreateGuid().Substring(0,18);
            var thsEquipmentInfo = new THS_EQUIPMENT_INFO()
            {
                EQUIPMENT_ID = id,
                EQUIPMENT_DID = equipment.EQUIPMENT_ID,
                EQUIPMENT_NAME = equipment.EQUIPMENT_NAME,
                EQUIPMENT_CODE = equipment.EQUIPMENT_CODE,
                EQUIPMENT_POSITION =  roomInfo.POSITION_ID,
                UNIT_ID = equipment.SMBL_LAB_ID,
                SHELF_NUM = 0,
                COORDINATE_PARAM = "",
                IF_MAJOR_EQUIPMENT = keyTypes.Contains(equipment.SMBL_CLASS) ? "1" : "0",
                EQUIPMENT_SORT = equipment.EQUIPMENT_SORT,
                FIRST_RPERSON = "system",
                FIRST_RTIME = now,
                LAST_MPERSON= "system",
                LAST_MTIME= now,
                REMARK = "生安眼监测",
                HOSPITAL_ID = equipment.HOSPITAL_ID,
                EQUIPMENT_STATE = "1",
                EQUIPMENT_TYPE = "1",
                EQUIPMENT_SN = sn
            };
            thsEquipment =  _dbContext.Db.Insertable(thsEquipmentInfo).ExecuteReturnEntity();
        }
        return thsEquipment;
    }
    
    /// <summary>
    /// 添加监测点对应监测的检测项
    /// </summary>
    /// <param name="point">监测点</param>
    /// <param name="item">检测项</param>
    /// <returns></returns>
    private THS_EQUIPMENT_POINT_ITEM AddAndGetNewThsPointItem(THS_EQUIPMENT_POINT point , THS_MONITOR_ITEM item)
    {
        var result = _dbContext.Db.Queryable<THS_EQUIPMENT_POINT_ITEM>()
            .Where(x => x.ITEM_ID == item.ITEM_ID && x.POINT_ID == point.POINT_ID)
            .First();
        if (result is null)
        {
            var newThsPointItem = new THS_EQUIPMENT_POINT_ITEM()
            {
                POINT_ID = point.POINT_ID,
                ITEM_ID = item.ITEM_ID,
                ITEM_NAME = item.ITEM_NAME,
                ITEM_SORT = item.ITEM_SORT,
                RULE_ID = "",
                ONLINE_ITEM_STATE = "1",
                ABNORMAL_ITEM_STATE = "1",
                ITEM_STATE = "1",
                FIRST_RPERSON = "system",
                FIRST_RTIME = DateTime.Now,
                LAST_MPERSON = "system",
                LAST_MTIME = DateTime.Now,
                REMARK = "生安眼监测",
                COORDINATE_PARAM = "",
                ITEM_CHANNEL = ""
            };
            _dbContext.Db.Insertable(newThsPointItem).ExecuteCommand();
            result = newThsPointItem;
        }
        return result;
    }

    /// <summary>
    /// 创建监测记录的指标的结果
    /// </summary>
    /// <param name="point">设备对应的监测点</param>
    /// <param name="items">监测点对应的检测项</param>
    /// <param name="data"></param>
    private void AddMonitoringDataResult(THS_MONITOR_LIST record , List<THS_EQUIPMENT_POINT_ITEM>  items,  IoTDevicesDto data)
    {
        foreach (var item in items)
        {
            var recordItemResult = CreatMonitorResult(record.MONITOR_ID, item.ITEM_ID!, data);
            _dbContext.Db.Insertable(recordItemResult).ExecuteCommand();
            record.ThsMonitorResults.Add(recordItemResult);
        }
    }

    /// <summary>
    /// 根据不同类型的监测项 创建对应的监测结果对象
    /// </summary>
    /// <param name="preRecord">上次监测记录</param>
    /// <param name="monitorId">监测记录id</param>
    /// <param name="monitorItemId"></param>
    /// <param name="data"></param>
    /// <returns></returns>
    private THS_MONITOR_RESULT CreatMonitorResult(string  monitorId ,  string monitorItemId , IoTDevicesDto data)
    {
        var now = DateTime.Now;
        var result = new THS_MONITOR_RESULT();
        result.MONITOR_ID = monitorId;
        result.ITEM_ID = monitorItemId;
        result.FIRST_RPERSON = "system";
        result.FIRST_RTIME = now;
        result.LAST_MPERSON= "system";
        result.LAST_MTIME= now;
        result.ALARM_RANGE = "";
        result.REMARK = "生安眼监测";
        result.RULE_ID = "";
        result.ITEM_STATUS = "Z";
        result.ITEM_STATE = "1";
        if (monitorItemId == "SMBL_1")
        {
            result.ITEM_VALUE = GetOnlineStatus(data.SwitchStatus);
            if (data.SwitchStatus == 3)
            {
                result.ITEM_STATE = "2";
                result.CHART_STATE = "2";
                result.ITEM_STATUS = "H";
            }
        }
        if (monitorItemId == "SMBL_2")
        {
            result.ITEM_VALUE = data.Energy.ToString("F3");
        }
        if (monitorItemId == "SMBL_3")
        {
            result.ITEM_VALUE = data.Voltage.ToString("F3");
        }
        if (monitorItemId == "SMBL_4")
        {
            result.ITEM_VALUE = data.Current.ToString("F3");
        }
        if (monitorItemId == "SMBL_5")
        {
            result.ITEM_VALUE = data.Power.ToString("F3");
            if (data.SwitchStatus == 3)
            {
                result.ITEM_STATE = "2";
                result.CHART_STATE = "2";
                result.ITEM_STATUS = "H";
            }
        }
        return result;
    }
    
    /// <summary>
    /// 获取在线状态自定义枚举值
    /// </summary>
    /// <param name="switchStatus"></param>
    /// <returns></returns>
    private string GetOnlineStatus(int switchStatus)
    {
        return switchStatus switch
        {
            0=>"SMBL00", //关机
            1=>"SMBL01", //待机 
            2=>"SMBL02", //使用中
            3=>"SMBL03", //过载
            201=>"SMBL04", //消毒
            _ => "SMBL99",//开机
        };
    }
    /// <summary>
    /// 电信返回的状态转换
    /// </summary>
    /// <param name="onlineStatus"></param>
    /// <returns></returns>
    public string GetSwitchStatusName(string onlineStatus)
    {
        return onlineStatus switch
        {
            "SMBL00"=>"关机", //关机
            "SMBL01"=>"待机", //待机 
            "SMBL02"=>"使用中", //使用中
            "SMBL03"=>"过载", //过载
            "SMBL04"=>"紫外消毒",//消毒杀菌
            "SMBL99"=>"开机",
            _=>"使用中"
        };
    }
    /// <summary>
    /// 自定义封装的监测项枚举
    /// </summary>
    /// <param name="smblClass"></param>
    /// <returns></returns>
    private  string [] Equipment2MonitorItems(string smblClass)
    {
        if (smblClass is null)
        {
            return new string[]{};
        }
        //生物安全柜检测项  和高压锅       运行状态   用电量     电压   电流   功率 
        string[] items = new []{"SMBL_1","SMBL_2","SMBL_3","SMBL_4","SMBL_5"};
        //环境一体机监测项           温度         湿度         噪声       气压
        string[] items1 = new[] {"SMBL_200","SMBL_201","SMBL_202","SMBL_203"};
        //紫外线灯监测项             运行状态       紫外线灯
        string[] items2 = new[] { "SMBL_1","SMBL_205"};
        //水压检测项               运行状态         水压
        string[] items3 = new[] { "SMBL_1","SMBL_206"};
        
        
        Dictionary<string, string[]> dict  = new ();
        var smblClasses = _smblService.GetSmblEquipmentClassPull();
        foreach (var smblClassItem in smblClasses)
        {
            var id = smblClassItem.DATA_ID;
            switch (id)
            {
                case "1" :
                    dict.Add(smblClassItem.DATA_ID, items);
                    break;
                case "2" :
                    dict.Add(smblClassItem.DATA_ID, items);
                    break;
                case "7":
                    dict.Add(smblClassItem.DATA_ID, items1);
                    break;
                case "5":
                    dict.Add(smblClassItem.DATA_ID, items2);
                    break;
                case "4":
                    dict.Add(smblClassItem.DATA_ID, items3);
                    break;
                default:
                    dict.Add(smblClassItem.DATA_ID, new string[]{});
                    break;
            }
        }
        return dict[smblClass];
    }


    /// <summary>
    /// 初始化ai告警类型
    /// </summary>
    /// <returns></returns>
    public bool InitCameraAlerts()
    {
        var initDatas = new List<KeyValueDto>();
        initDatas.Add(new KeyValueDto()
        {
            Key = "19",
            Value = "人过少"
        });
        initDatas.Add(new KeyValueDto()
        {
            Key = "20",
            Value = "人过多"
        });
        initDatas.Add(new KeyValueDto()
        {
            Key = "23",
            Value = "人数统计"
        });
        initDatas.Add(new KeyValueDto()
        {
            Key = "32",
            Value = "未穿防护服"
        });
        initDatas.Add(new KeyValueDto()
        {
            Key = "33",
            Value = "未带发罩"
        });
        initDatas.Add(new KeyValueDto()
        {
            Key = "34",
            Value = "未带手套"
        });
        initDatas.Add(new KeyValueDto()
        {
            Key = "35",
            Value = "未戴口罩"
        });

        var inits = new List<THS_MONITOR_ITEM>();
        foreach (var initData in initDatas)
        {
            inits.Add(new THS_MONITOR_ITEM()
            {
                ITEM_ID = $"COUNT_{initData.Key}",
                HOSPITAL_ID = "33A001",
                ITEM_NAME = initData.Value,
                ITEM_SORT = "",
                ITEM_UNIT = "",
                ITEM_STATE = "1",
                ITEM_PRECISION = "0",
                FIRST_RPERSON = "system",
                FIRST_RTIME = DateTime.Now,
                LAST_MPERSON = "system",
                LAST_MTIME = DateTime.Now,
                REMARK = "生安眼监测"
            });
        }
        return  _dbContext.Db.GetSimpleClient<THS_MONITOR_ITEM>().InsertOrUpdate(inits);
    }
    
    /// <summary>
    /// 获取AI告警监测项
    /// </summary>
    /// <returns></returns>
    public List<THS_MONITOR_ITEM> GetCameraAlerts()
    {
        return _dbContext.Db.GetSimpleClient<THS_MONITOR_ITEM>().GetList(x => x.ITEM_ID.Contains("COUNT") && x.ITEM_STATE =="1");
    }
    
    
    /// <summary>
    /// 初始化AI摄像头
    /// </summary>
    /// <param name="equipmentId">设备id</param>
    /// <param name="sn">sn</param>
    /// <param name="aiSettinbg">AI配置json</param>
    /// <exception cref="BizException"></exception>
    public  void AiCameraInit(string equipmentId , string sn , string? aiSetting ,bool isAi = false)
    {
        Log.Information("查询设备系统是否记录该设备");
       var equipment =  _dbContext.Db.GetSimpleClient<EMS_EQUIPMENT_INFO>().GetSingle(x => x.EQUIPMENT_ID == equipmentId);
       if (equipment is null)
       {
           throw new BizException("该设备不存在，请把该设备信息录入到设备系统");
       }
       Log.Information("调用第三方设备接口查询实时监控数据");

       if (!aiSetting.IsNullOrEmpty())
       {
           equipment.EQUIPMENT_JSON = aiSetting;
           _dbContext.Db.GetSimpleClient<EMS_EQUIPMENT_INFO>().Update(equipment);
       }

       if (!isAi)
       {
           return;
       }
       
       var unit =  _dbContext.Db.Queryable<THS_UNIT_INFO>().First(x => x.UNIT_ID == equipment.SMBL_LAB_ID);
       if (unit is null)
       {
           throw new BizException("请执行脚本插入监测单元信息（备案实验室）");
       }
       if (equipment.SMBL_CLASS.IsNullOrEmpty())
       {
           throw new BizException("请维护设备的生安类型");
       }
       var thsItems = _dbContext.Db.Queryable<THS_MONITOR_ITEM>()
           .Where(x => x.ITEM_ID.Contains("COUNT") && x.ITEM_STATE == "1").ToList();
       var roomId = equipment.POSITION_ID;
       var isOnlie = 1;
       var  thsEquipment = AddAndGetNewThsEquipment(equipment, roomId,sn);
           //备案实验室下，这台被监测的设备有没有被纳入监测范围，没有则新建默认生安设备只有一个监测点
           var thsPoints = _dbContext.Db.Queryable<THS_EQUIPMENT_POINT>()
               .Where(x => x.UNIT_ID == equipment.SMBL_LAB_ID && x.EQUIPMENT_ID == thsEquipment.EQUIPMENT_ID)
               .ToList();
           if (!thsPoints.Any())
           {
               var id = IDGenHelper.CreateGuid().Substring(0,18);
               var newThsPoint = new THS_EQUIPMENT_POINT()
               {
                   POINT_ID = id,
                   UNIT_ID = thsEquipment.UNIT_ID,
                   HOSPITAL_ID = thsEquipment.HOSPITAL_ID,
                   EQUIPMENT_ID = thsEquipment.EQUIPMENT_ID,
                   POINT_NAME = thsEquipment.EQUIPMENT_CODE,
                   POINT_SNAME = thsEquipment.EQUIPMENT_CODE,
                   POINT_SORT = "",
                   POINT_NUM = roomId,
                   OBTAIN_MODE = "",
                   ONLINE_STATE = isOnlie == 1 ? "1" : "2",
                   ABNORMAL_STATE = "1",
                   POINT_STATE = "1",
                   FIRST_RPERSON = thsEquipment.FIRST_RPERSON,
                   FIRST_RTIME = DateTime.Now,
                   LAST_MPERSON = thsEquipment.LAST_MPERSON,
                   LAST_MTIME = DateTime.Now,
                   REMARK = "生安眼监测",
                   ONLINE_TIME_LIMIT = 10,
                   OBTAIN_FREQUENCY = 10,
                   EQUIPMENT_MID = "0",
                   LAST_TIME_POINT = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
               }; 
               _dbContext.Db.Insertable(newThsPoint).ExecuteCommand();
               thsPoints.Add(newThsPoint);
           }
           var newThsPointItems = new List<THS_EQUIPMENT_POINT_ITEM>();
           foreach (var thsPoint in thsPoints)
           {
               foreach (var thsItem in thsItems)
               { 
                   var newThsPointItem =  AddAndGetNewThsPointItem(thsPoint, thsItem);
                   newThsPointItems.Add(newThsPointItem);
               }
           }
           Console.WriteLine(JsonConvert.SerializeObject(newThsPointItems,Formatting.Indented));
    }


    /// <summary>
    /// AI摄像头告警信息推送信息处理
    /// </summary>
    public void PushAiCameraAlert(CameraAlertDto input)
    { 
        const string doc_folder = "EMS/datafile";
        const string if_cover = "true";
        var pics = new List<string>();
        if (input.bigPics.Any())
        {
            var token = "";
            var sys6_interface_module = _dbContext.Db.Queryable<SYS6_INTERFACE_MODULE>().First(p => p.MODULE_ID == "H82");
            if (sys6_interface_module != null)
            {
                string app_key = sys6_interface_module.APP_KEY;
                string app_secret = sys6_interface_module.APP_SECRET;
                ContentKeyDto contentKey = new ContentKeyDto()
                {
                    callModuleId = "H82",
                    moduleId = "S28",
                    requestTime = DateTime.Now,
                    tokenGuid = IDGenHelper.CreateGuid(),
                    userNo = "H82",
                    isInteriorUser = false,
                    obj = ""
                };
                string strKey = JsonHelper.ToJson(contentKey);
                string secretKey = SmxUtilsHelper.SM4UtilsEncrypt(app_secret, strKey);
                ThirdVerifyInfoDto infoDto = new ThirdVerifyInfoDto()
                {
                    key = secretKey,
                    appKey = app_key
                };
                var rsp  = _baseDataServices.GetCustomToken(infoDto);
                if (rsp.success)
                {
                    token = JObject.Parse(rsp.data.ToString())["AccessToken"].ToString();
                }
                else
                {
                    throw new BizException($"{rsp.msg}");
                }
                
            }
            foreach (var picBase64 in input.bigPics)
            {
                var guid = Guid.NewGuid().ToString("N") + ".jpg";
                var path =  _baseDataServices.UploadToS28ByToken(doc_folder, if_cover, guid,Convert.FromBase64String(picBase64), token);
                pics.Add(path);
            }
            input.bigPics = pics;
        }
        var date = DateTime.Now;
        var thsEquipment =  _dbContext.Db.GetSimpleClient<THS_EQUIPMENT_INFO>()
            .GetFirst(x => x.EQUIPMENT_SN == input.deviceId);
        var equipment = _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>()
            .Where(x => x.EQUIPMENT_ID == thsEquipment.EQUIPMENT_DID)
            .First();
       if (thsEquipment is null)
       {
           throw new BizException("该设备不存在，请把该设备信息录入到设备系统");
       }
       var point = _dbContext.Db.Queryable<THS_EQUIPMENT_POINT>()
           .Where(x => x.EQUIPMENT_ID == thsEquipment.EQUIPMENT_ID)
           .First();
       if (point is null)
       {
           throw new BizException("该设备没有构建监测点");
       }
       var pointItems = _dbContext.Db.Queryable<THS_EQUIPMENT_POINT_ITEM>()
           .Where(x => x.POINT_ID == point.POINT_ID)
           .Where(x => x.ITEM_ID.Contains($"COUNT_{input.alarmType}"))
           .First();
       if (pointItems is null)
       {
           throw new BizException("该设备没有构建监测点检测项");
       }
       
       var record = new THS_MONITOR_LIST
       {
           MONITOR_ID =   IDGenHelper.CreateGuid().Substring(0,18),
           EQUIPMENT_ID = point.EQUIPMENT_ID,
           POINT_ID = point.POINT_ID,
           UNIT_ID = point.UNIT_ID,
           HOSPITAL_ID = point.HOSPITAL_ID,
           MONITOR_DATE = date.Date,
           TIME_POINT = DateTime.Parse(date.ToString("yyyy-MM-dd HH:mm:dd")).ToString("HH:mm:dd"),
           MONITOR_PERSON = "system",
           MONITOR_TIME = date,
           MONITOR_COMPUTER = "",
           MONITOR_TID = "000000",
           MONITOR_STATE ="2",
           FIRST_RPERSON = "system",
           FIRST_RTIME = date,
           LAST_MPERSON = "system",
           LAST_MTIME = date,
           REMARK = "生安眼监测",
           OPER_PERSON = "",
           OPER_TIME = null,
           OPER_COMPUTER = ""
       };
       _dbContext.Db.GetSimpleClient<THS_MONITOR_LIST>().Insert(record);
       var thsMonitorResult = new THS_MONITOR_RESULT
       {
           MONITOR_ID = record.MONITOR_ID,
           ITEM_ID = pointItems.ITEM_ID,
           ITEM_VALUE = "1",
           ITEM_STATE = "2",
           FIRST_RPERSON = "system",
           FIRST_RTIME = date,
           LAST_MPERSON = "system",
           LAST_MTIME = date,
           REMARK = "生安眼监测",
           ALARM_RANGE = "",
           RULE_ID = "",
           CHART_STATE = "2",
           ITEM_STATUS = "H",
           ITEM_VALUE_JSON = JsonConvert.SerializeObject(input)
       };
       _dbContext.Db.GetSimpleClient<THS_MONITOR_RESULT>().Insert(thsMonitorResult);
       var alert = new THS_ABNORMAL_OPER_LIST
       {
           OPER_ID = IDGenHelper.CreateGuid().Substring(0,18),
           MONITOR_ID = record.MONITOR_ID,
           OPER_TYPE = $"SMBL_{input.alarmType}",
           OPER_PERSON = "",
           OPER_TIME = null,
           OPER_COMPUTER = "",
           OPER_CAUSE_CLASS = "",
           OPER_CAUSE = "",
           OPER_STATE = "0",
           EQUIPMENT_ID = thsEquipment.EQUIPMENT_ID,
           ALARM_TIME = date,
           ALARM_CONTINUOUS_TIME = 10,
           POINT_ID = point.POINT_ID,
           REMARK = "生安眼监测",
           FIRST_RPERSON = "system",
           FIRST_RTIME = date,
           LAST_MPERSON = "system",
           LAST_MTIME = date
       };
       _dbContext.Db.GetSimpleClient<THS_ABNORMAL_OPER_LIST>().Insert(alert);
       record.ThsMonitorResults.Add(thsMonitorResult);
       ConutAiMonitoringItems(equipment,record);
    }
    
    /// <summary>
    /// 医疗设备告警信息推送信息处理
    /// </summary>
    public void PushEquipmentCameraAlert(MedicalEquipmentAlertDto input)
    {
        Log.Information($"{JsonConvert.SerializeObject(input)}开始推送设备告警信息");
        var date = DateTime.Now;
        var thsEquipment = _dbContext.Db.GetSimpleClient<THS_EQUIPMENT_INFO>()
            .GetFirst(x => x.EQUIPMENT_SN == input.sn);
        if (thsEquipment is null)
        {
            throw new BizException("该设备不存在，请把该设备信息录入到设备系统");
        }
        var equipment = _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>()
            .Where(x => x.EQUIPMENT_ID == thsEquipment.EQUIPMENT_DID)
            .First();
        if (equipment is null)
        {
            throw new BizException("该设备不存在，请把该设备信息录入到设备系统");
        }
        if (input.monitors.Count == 0)
        {
            throw new BizException("该设备没有告警信息");
        }
        var point = _dbContext.Db.Queryable<THS_EQUIPMENT_POINT>()
            .Where(x => x.EQUIPMENT_ID == thsEquipment.EQUIPMENT_ID)
            .First();
        if (point is null)
        {
            throw new BizException("该设备没有构建监测点");
        }
        var record = new THS_MONITOR_LIST
        {
            MONITOR_ID =   IDGenHelper.CreateGuid().Substring(0,18),
            EQUIPMENT_ID = point.EQUIPMENT_ID,
            POINT_ID = point.POINT_ID,
            UNIT_ID = point.UNIT_ID,
            HOSPITAL_ID = point.HOSPITAL_ID,
            MONITOR_DATE = date.Date,
            TIME_POINT = DateTime.Parse(date.ToString("yyyy-MM-dd HH:mm:dd")).ToString("HH:mm:dd"),
            MONITOR_PERSON = "system",
            MONITOR_TIME = date,
            MONITOR_COMPUTER = "",
            MONITOR_TID = "000000",
            MONITOR_STATE ="2",
            FIRST_RPERSON = "system",
            FIRST_RTIME = date,
            LAST_MPERSON = "system",
            LAST_MTIME = date,
            REMARK = "生安眼监测",
            OPER_PERSON = "",
            OPER_TIME = null,
            OPER_COMPUTER = ""
        };
        _dbContext.Db.GetSimpleClient<THS_MONITOR_LIST>().Insert(record);
        foreach (var monitor in input.monitors)
        {
            var itemId = GetEquipmentAlarmType(monitor.bioAlarmType);
            var pointItems = _dbContext.Db.Queryable<THS_EQUIPMENT_POINT_ITEM>()
                .Where(x => x.POINT_ID == point.POINT_ID)
                .Where(x => x.ITEM_ID == itemId)
                .First();
            if (pointItems is null)
            {
                throw new BizException("该设备没有构建监测点检测项");
            }
            var thsMonitorResult = new THS_MONITOR_RESULT
            {
                MONITOR_ID = record.MONITOR_ID,
                ITEM_ID = pointItems.ITEM_ID,
                ITEM_VALUE = monitor.value.ToString("F3"),
                ITEM_STATE = "2",
                FIRST_RPERSON = "system",
                FIRST_RTIME = date,
                LAST_MPERSON = "system",
                LAST_MTIME = date,
                REMARK = "生安眼监测",
                ALARM_RANGE = "",
                RULE_ID = "",
                CHART_STATE = "2",
                ITEM_STATUS = "H",
                ITEM_VALUE_JSON = JsonConvert.SerializeObject(monitor)
            };
            _dbContext.Db.GetSimpleClient<THS_MONITOR_RESULT>().Insert(thsMonitorResult);
            record.ThsMonitorResults.Add(thsMonitorResult);
            
            var alert = new THS_ABNORMAL_OPER_LIST
            {
                OPER_ID = IDGenHelper.CreateGuid().Substring(0,18),
                MONITOR_ID = record.MONITOR_ID,
                OPER_TYPE = $"SMBL_{monitor.bioAlarmType}",
                OPER_PERSON = "",
                OPER_TIME = null,
                OPER_COMPUTER = "",
                OPER_CAUSE_CLASS = "",
                OPER_CAUSE = "",
                OPER_STATE = "0",
                EQUIPMENT_ID = thsEquipment.EQUIPMENT_ID,
                ALARM_TIME = date,
                ALARM_CONTINUOUS_TIME = 0,
                POINT_ID = point.POINT_ID,
                REMARK = "生安眼监测",
                FIRST_RPERSON = "system",
                FIRST_RTIME = date,
                LAST_MPERSON = "system",
                LAST_MTIME = date
            };
            _dbContext.Db.GetSimpleClient<THS_ABNORMAL_OPER_LIST>().Insert(alert);
        }
        ConutEquipmentMonitoringItems(equipment,record);
    }
    
    private string GetEquipmentAlarmType(int bioAlarmType)
    {
        switch (bioAlarmType)
        {
            case 200:
                return "SMBL_200";
            case 201:
                return "SMBL_201";
            case 202:
                return "SMBL_202";
            case 203:
                return "SMBL_203";
            case 406:
                return "SMBL_406";
            case 408:
                return "SMBL_206";
            case 407:
                return "SMBL_205";
            case 700:
                return "SMBL_700";
            case 701:
                return "SMBL_4";
            case 702:
                return "SMBL_3";
            case 703:
                return "SMBL_5";
            case 704:
                return "SMBL_2";
            default:
                return "";
        }
    }
}