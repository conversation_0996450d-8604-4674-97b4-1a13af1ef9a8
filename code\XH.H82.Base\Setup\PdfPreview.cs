﻿using Serilog;
using SkiaSharp;

namespace XH.H82.Base.Setup
{
    public static class PdfPreview
    {
        public static string PDFPreview(string base64)
        {
            Spire.Pdf.License.LicenseProvider.SetLicenseKey("Exgti80+VQEAWvzoJ2DBP9zbFeaWRiqJ/S7/AglNpZZMbHab9dehnYHo44VeuHrtD6stIccrs26WJqPdD782lV4VYGOWZaF1H+i1WGT1X9BdQ9h5p7QNXQeCHbCLH5oBErmfA7Y4eCsh5M3J1lmDVDFfNKQ9VS6jJjKFA7EgQOa2P4G26eoExTZewyaF3AIgftu4BuX/S3aXH5rVG6RtmwG0pLuTDVyn2mcVw1nYlzc1jPIvyzpAZNSehpG33QXRXMRo7Wp9POYi/egG8NhPpl2f85235tt0d3LXjnIDNZblQcXGzTdIIjX3QR+dsirkqVg7A4mGX3JA9W+M1iRdAfRz3P42Nh/AOFQ5sz1ZUAhfHpWwebFZ/u9AtqJe1CPTwgW1pyKZVyKBXTNITYLkK7priwixTOxnL2s5i6uT/gUFfYrTPJfP/Ynyqlb6DMxxiMGAjB0GzLe51Q57vGaCCGtivTacHWKML65k90FW3AAOhXdhwKqLyWy+amkcJg7lpW82ZsxHYERTZGkDwRf4EODIWbqhVc3x+rZUKabBZULB3LJK8TjRfp5A1LDLnqqom5L9ZFtR+cdG78wFuVCV9dXFY66AZhspaJp8ALj/jQyoRNfih2zBqkbGzvsVS1XkYVFG0AM4CAKUuY2sM0cHoPdqyI/N1s4Y16Ec66hJzYzIEJUmkQ25WGMQWYoZUQzjFyWIO6/tr6uiwu+q3dx/eXFOouQ5pqDDjqxEa4S7cL+FgWZmPIh5lu12lYGyk9ahgOi/1SBrRun3U9LJxPx2lg92XXHG+/yOoRBa2twX6Tx6fRMflERyRi+8SECJu8IoMjcTwW/zqEclKhe3Au9PpDuRZ9PBQt1LBCBbSaIuE2poeoQ3DWHUlHar9wjFX7RQVWrVEi5roaIrZMr1f63acNFil2U9f0VJSeYgIM1kM9gM5f4fxISh6A0pkdSO1SykBHqIQFkpxzbzPIU8KGEuTE/MnYOBkxXIdQZ1aMepYX+p5sv7Brf2vkU+y2JajxXusOdoVaGeiMIWrbMOF6D0CGpuFvKRiktIuxLX1FaKi4SvpNZAcvZgUReZO+HHEDS45jIPkCXiVjcA7R5OiwfAzvMFxI2IZW9oNY+se9OTf/7/UZ1rSETlCY0NbYmSVX9X4yeHSGL/IpqsdJN+JvbXjBb88UZgZN/M1Yu1f+dsMqf8n0Iz3IH9PFEw6KK1NpNj5tRq1FDV+Av8NJy07ovpYBuGitvZZCSixgcUfo92v1IFJ6sI4FYaodPo8OC58NGuB8jR4FZH3CDbW0CbAwRf0gmMdUw0UMJV1fKetp48b/K1/UtK4KyutIlYqMQQBZHlMe1iu0InccHEMOo/XMulrpM/phc8nvdKYWSnJcYQRtdyNIvmLWKnTUGmWDEUG/PEzJaDl3d6v4Q3dwlUb/aFqDPsrOsofNwG9CTs0B0z7Y71bW/nyeCE4Lk/iHUZmgK9Le1t1+vclc4yrSo2oOziyJ/fyqg00FSr13qazezxyVs=");
            Spire.Pdf.PdfDocument doc = new Spire.Pdf.PdfDocument();
            byte[] bytes = Convert.FromBase64String(base64);
            doc.LoadFromBytes(bytes);
            var ms = doc.SaveAsImage(0);
            var bitmap = SKBitmap.Decode(ms);
            var image = SKImage.FromBitmap(bitmap);
            var data = image.Encode(SKEncodedImageFormat.Jpeg, 100);
            var memoryStream = new MemoryStream();
            data.SaveTo(memoryStream);
            var res = Convert.ToBase64String(memoryStream.ToArray());
            //byte[] bytes = Convert.FromBase64String(base64);
            //var firstPageBase64 = "";
            //using (MemoryStream outPDF = new MemoryStream())
            //{
            //    using (iTextSharp.text.pdf.PdfReader reader = new iTextSharp.text.pdf.PdfReader(bytes))
            //    {

            //        iTextSharp.text.Document doc = new iTextSharp.text.Document();
            //            iTextSharp.text.Document.Compress = true;
            //            iTextSharp.text.pdf.PdfWriter writer = iTextSharp.text.pdf.PdfWriter.GetInstance(doc, outPDF);
            //            doc.Open();
            //            PdfContentByte cb = writer.DirectContent;
            //            PdfImportedPage page;

            //            page = writer.GetImportedPage(reader, 1);
            //            cb.AddTemplate(page, PageSize.LETTER.Width / reader.GetPageSize(1).Width, 0, 0, PageSize.LETTER.Height / reader.GetPageSize(1).Height, 0, 0);
            //            doc.NewPage();

            //            doc.Close();

            //        reader.Close();
            //    }
            //    firstPageBase64 = Convert.ToBase64String(outPDF.ToArray());
            //}
            return res;
        }
        //public static byte[] PDFPreview_Byte(byte[] bytes)
        //{
        //    Spire.Pdf.License.LicenseProvider.SetLicenseKey("Exgti80+VQEAWvzoJ2DBP9zbFeaWRiqJ/S7/AglNpZZMbHab9dehnYHo44VeuHrtD6stIccrs26WJqPdD782lV4VYGOWZaF1H+i1WGT1X9BdQ9h5p7QNXQeCHbCLH5oBErmfA7Y4eCsh5M3J1lmDVDFfNKQ9VS6jJjKFA7EgQOa2P4G26eoExTZewyaF3AIgftu4BuX/S3aXH5rVG6RtmwG0pLuTDVyn2mcVw1nYlzc1jPIvyzpAZNSehpG33QXRXMRo7Wp9POYi/egG8NhPpl2f85235tt0d3LXjnIDNZblQcXGzTdIIjX3QR+dsirkqVg7A4mGX3JA9W+M1iRdAfRz3P42Nh/AOFQ5sz1ZUAhfHpWwebFZ/u9AtqJe1CPTwgW1pyKZVyKBXTNITYLkK7priwixTOxnL2s5i6uT/gUFfYrTPJfP/Ynyqlb6DMxxiMGAjB0GzLe51Q57vGaCCGtivTacHWKML65k90FW3AAOhXdhwKqLyWy+amkcJg7lpW82ZsxHYERTZGkDwRf4EODIWbqhVc3x+rZUKabBZULB3LJK8TjRfp5A1LDLnqqom5L9ZFtR+cdG78wFuVCV9dXFY66AZhspaJp8ALj/jQyoRNfih2zBqkbGzvsVS1XkYVFG0AM4CAKUuY2sM0cHoPdqyI/N1s4Y16Ec66hJzYzIEJUmkQ25WGMQWYoZUQzjFyWIO6/tr6uiwu+q3dx/eXFOouQ5pqDDjqxEa4S7cL+FgWZmPIh5lu12lYGyk9ahgOi/1SBrRun3U9LJxPx2lg92XXHG+/yOoRBa2twX6Tx6fRMflERyRi+8SECJu8IoMjcTwW/zqEclKhe3Au9PpDuRZ9PBQt1LBCBbSaIuE2poeoQ3DWHUlHar9wjFX7RQVWrVEi5roaIrZMr1f63acNFil2U9f0VJSeYgIM1kM9gM5f4fxISh6A0pkdSO1SykBHqIQFkpxzbzPIU8KGEuTE/MnYOBkxXIdQZ1aMepYX+p5sv7Brf2vkU+y2JajxXusOdoVaGeiMIWrbMOF6D0CGpuFvKRiktIuxLX1FaKi4SvpNZAcvZgUReZO+HHEDS45jIPkCXiVjcA7R5OiwfAzvMFxI2IZW9oNY+se9OTf/7/UZ1rSETlCY0NbYmSVX9X4yeHSGL/IpqsdJN+JvbXjBb88UZgZN/M1Yu1f+dsMqf8n0Iz3IH9PFEw6KK1NpNj5tRq1FDV+Av8NJy07ovpYBuGitvZZCSixgcUfo92v1IFJ6sI4FYaodPo8OC58NGuB8jR4FZH3CDbW0CbAwRf0gmMdUw0UMJV1fKetp48b/K1/UtK4KyutIlYqMQQBZHlMe1iu0InccHEMOo/XMulrpM/phc8nvdKYWSnJcYQRtdyNIvmLWKnTUGmWDEUG/PEzJaDl3d6v4Q3dwlUb/aFqDPsrOsofNwG9CTs0B0z7Y71bW/nyeCE4Lk/iHUZmgK9Le1t1+vclc4yrSo2oOziyJ/fyqg00FSr13qazezxyVs=");
        //    Spire.Pdf.PdfDocument doc = new Spire.Pdf.PdfDocument();
        //    doc.LoadFromBytes(bytes);
        //    var ms = doc.SaveAsImage(0);
        //    var bitmap = SKBitmap.Decode(ms);
        //    var image = SKImage.FromBitmap(bitmap);
        //    var data = image.Encode(SKEncodedImageFormat.Jpeg, 100);
        //    var memoryStream = new MemoryStream();
        //    data.SaveTo(memoryStream);
        //    return memoryStream.ToArray();
        //}

        /// <summary>
        /// 获取pdf非空白封面 返回base64
        /// </summary>
        /// <param name="pdfBytes"></param>
        /// <returns></returns>
        public static string GetPdfCover(byte[] pdfBytes)
        {
            Stream stream = null;
            try
            {
                using Spire.Pdf.PdfDocument document = new Spire.Pdf.PdfDocument();
                document.LoadFromBytes(pdfBytes);
                //遍历文档中所有页面 
                for (int i = 0; i < document.Pages.Count; i++)
                {
                    //诊断页面是否为空白页
                    if (document.Pages[i].IsBlank())
                    {
                        //删除空白页 
                        //document.Pages.RemoveAt(i);
                        continue;
                    }
                    else
                    {
                        //Bitmap图像在windows支持 linux要额外安装库 先不做图片首页判断空白的错误
                        stream = document.SaveAsImage(i);
                        byte[] bte = new byte[stream.Length];
                        stream.Read(bte, 0, bte.Length);
                        stream.Seek(0, SeekOrigin.Begin);
                        return Convert.ToBase64String(bte);
                    }
                }

            }
            catch (Exception ex)
            {
            }
            finally
            {
                stream?.Close();
                stream?.Dispose();
            }
            return "";
        }


        /// <summary>
        /// 获取pdf非空白封面 返回base64
        /// </summary>
        /// <param name="pdfBytes"></param>
        /// <returns></returns>
        public static byte[] PDFPreview_Byte(byte[] pdfBytes)
        {
            Stream stream = null;
            try
            {
                using Spire.Pdf.PdfDocument document = new Spire.Pdf.PdfDocument();
                document.LoadFromBytes(pdfBytes);
                //遍历文档中所有页面 
                for (int i = 0; i < document.Pages.Count; i++)
                {
                    //诊断页面是否为空白页
                    if (document.Pages[i].IsBlank())
                    {
                        //删除空白页 
                        //document.Pages.RemoveAt(i);
                        continue;
                    }
                    else
                    {

                        //Bitmap图像在windows支持 linux要额外安装库 先不做图片首页判断空白的错误
                        stream = document.SaveAsImage(i);
                        byte[] bte = new byte[stream.Length];
                        stream.Read(bte, 0, bte.Length);
                        stream.Seek(0, SeekOrigin.Begin);
                        return bte;
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error($"pdf转图片报错:{ex.Message}");
            }
            finally
            {
                stream?.Close();
                stream?.Dispose();
            }
            return new byte[0];
        }
        
    }
}
