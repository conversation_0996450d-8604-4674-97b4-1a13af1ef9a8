﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.BusinessModuleClient
{
    /// <summary>
    /// 上传文件记录表
    /// </summary>
    [DBOwner("XH_OA")]
    [SugarTable("OA_UPLOAD_FILE", TableDescription = "上传文件记录表")]
    public class OA_UPLOAD_FILE
    {
        /// <summary>
        /// 上传文件ID
        /// </summary>
        [SugarColumn(IsIdentity = true, IsPrimaryKey = true)]
        public string FILE_ID { get; set; }

        /// <summary>
        /// 医疗机构ID
        /// </summary>
        public string HOSPITAL_ID { get; set; }

        /// <summary>
        /// 院区ID
        /// </summary>
        public string AREA_ID { get; set; }

        /// <summary>
        /// 科室ID
        /// </summary>
        public string LAB_ID { get; set; }

        /// <summary>
        /// 专业组ID
        /// </summary>
        public string PGROUP_ID { get; set; }

        /// <summary>
        /// 模块ID
        /// </summary>
        public string MODULE_ID { get; set; }

        /// <summary>
        /// 文件服务器模块ID;LIS6_INSPECT_GRAPH
        /// </summary>
        public string FILE_MODULE_ID { get; set; }

        /// <summary>
        /// 来源类型;(规则：模块ID.编号) H92.1-供应商资质  H92.7-评价表样式  H92.8-评价表模板
        /// </summary>
        public string DATA_CLASS { get; set; }

        /// <summary>
        /// 来源数据ID
        /// </summary>
        public string DATA_ID { get; set; }

        /// <summary>
        /// 文档附件源文件地址;S28返回的json地址，含文件名和后缀
        /// </summary>
        public string FILE_URL { get; set; }

        /// <summary>
        /// 文档附件源文件名(含后缀);含后缀
        /// </summary>
        public string FILE_NAME { get; set; }

        /// <summary>
        /// 文档附件源文件分类;按类型填入：PDF/WORD/EXCEL   /PIC
        /// </summary>
        public string FILE_TYPE { get; set; }

        /// <summary>
        /// 转换后的文件后缀（含.）
        /// </summary>
        public string FILE_SUFFIX { get; set; }

        /// <summary>
        /// 原始文件预览地址
        /// </summary>
        public string ORIGIN_FILE_URL { get; set; }

        /// <summary>
        /// 原始文件大小(K)
        /// </summary>
        public string FILE_SIZE { get; set; }

        /// <summary>
        /// 原始文件大小(K)
        /// </summary>
        public string ORIGIN_FILE_SIZE { get; set; }

        /// <summary>
        /// 文档附件预览地址;S28返回的json地址，含文件名和后缀
        /// </summary>
        public string PREVIEW_FILE_URL { get; set; }

        /// <summary>
        /// 文档附件文件名(含后缀);含后缀
        /// </summary>
        public string PREVIEW_FILE_NAME { get; set; }

        /// <summary>
        /// 状态;0禁用1启用2删除
        /// </summary>
        public string FILE_STATE { get; set; }

        /// <summary>
        /// 首次登记人
        /// </summary>
        public string FIRST_RPERSON { get; set; }

        /// <summary>
        /// 首次登记时间
        /// </summary>
        public DateTime FIRST_RTIME { get; set; }

        /// <summary>
        /// 最后修改人员
        /// </summary>
        public string LAST_MPERSON { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime LAST_MTIME { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string REMARK { get; set; }

        /// <summary>
        /// 原始文件名（不含后缀）
        /// </summary>
        public string ORIGIN_FILE_NAME { get; set; }
        /// <summary>
        /// 原始文件类型（不含.）
        /// </summary>
        public string ORIGIN_FILE_TYPE { get; set; }
        /// <summary>
        /// 原始文件后缀（含.）
        /// </summary>
        public string ORIGIN_FILE_SUFFIX { get; set; }



    }
}
