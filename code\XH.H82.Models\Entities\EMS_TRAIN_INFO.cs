﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using H.Utility.SqlSugarInfra;
using SqlSugar;

namespace XH.H82.Models.Entities
{
    [DBOwner("XH_OA")]
    public class EMS_TRAIN_INFO
    {
        [SugarColumn(IsPrimaryKey = true)]

        public string TRAIN_ID { get; set; }
        public string HOSPITAL_ID { get; set; }
        public string EQUIPMENT_ID { get; set; }
        public DateTime? TRAIN_TIME { get; set; }
        public string TRAIN_NAME { get; set; }
        public string TRAIN_ADDR { get; set; }
        public string TRAIN_HOUR { get; set; }
        public string JOIN_PERSON { get; set; }
        public string TRAIN_TEACHER { get; set; }
        public string TRAIN_STATE { get; set; }
        public string FIRST_RPERSON { get; set; }
        public DateTime? FIRST_RTIME { get; set; }
        public string LAST_MPERSON { get; set; }
        public DateTime? LAST_MTIME { get; set; }
        public string REMARK { get; set; }

    }
}
