﻿using System.Collections.Concurrent;
using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using NuGet.Packaging;
using XH.H82.IServices;
using XH.H82.Models.BusinessModuleClient;
using XH.H82.Models.BusinessModuleClient.Dto;
using XH.H82.Models.Entities;
using XH.H82.Models.Entities.Common;
using XH.H82.Models.SugarDbContext;
using XH.H82.Services.DeviceDataRefresh;
using XH.LAB.UTILS.Interface;
using XH.LAB.UTILS.Models;

namespace XH.H82.Services
{
    public class SetUpService : ISetUpService
    {
        private readonly ISqlSugarUow<SugarDbContext_Master> _dbContext;
        private readonly ILogger<OperationRecordService> _logger;
        private readonly IMemoryCache _cache;
        private readonly IHttpContextAccessor _httpContext;
        private readonly IAuthorityService _authorityService;

        public SetUpService(ISqlSugarUow<SugarDbContext_Master> sqlSugarUow, IAuthorityService authorityService, IMemoryCache cache, IHttpContextAccessor httpContext)
        {
            _dbContext = sqlSugarUow;
            _authorityService = authorityService;
            _cache = cache;
            _httpContext = httpContext;
            //ExecutingChangeSqlHelper.ExecutingChangeSql(_sqlSugarUow);
        }

        public List<SYS6_SETUP_DICT> GetSysSetUp(string mgroupId)
        {
            var res = _dbContext
                .Db.Queryable<SYS6_SETUP_DICT>()
                .Where(p => p.UNIT_ID == mgroupId && p.SETUP_STATE == "1")
                .ToList();
            return res;
        }

        public ResultDto UpdateSysSetUpInfo(SYS6_SETUP_DICT record)
        {
            ResultDto result = new ResultDto();
            try
            {
                var old = _dbContext
                    .Db.Queryable<SYS6_SETUP_DICT>()
                    .Where(p => p.SETUP_NO == record.SETUP_NO)
                    .First();
                if (old == null)
                {
                    throw new KeyNotFoundException("数据不存在");
                }
                old.CHOICE_VALUE = record.CHOICE_VALUE;
                old.LAST_MPERSON = record.LAST_MPERSON;
                old.LAST_MTIME = record.LAST_MTIME;
                _dbContext
                    .Db.Updateable(old)
                    .IgnoreColumns(ignoreAllNullColumns: true)
                    .ExecuteCommand();
                result.success = true;
            }
            catch (Exception ex)
            {
                result.success = false;
                result.msg = "修改失败";
                _logger.LogError("修改失败:\n" + ex.Message);
            }
            return result;
        }

        public ResultDto ApplyAllSetUpInfo(
            string userNo,
            string hospitalId,
            string userName,
            string choiceValue,
            string labId
        )
        {
            ResultDto result = new ResultDto();
            try
            {
                var  groupList =  _authorityService.GetUserPermissionPgroup(_dbContext.Db, userNo, "H82", hospitalId, labId);
                List<string> arrPgroupList = new List<string>();
                groupList.ForEach(p =>
                {
                    if (p.MGROUP_ID.IsNotNullOrEmpty())
                    {
                        arrPgroupList.Add(p.MGROUP_ID);
                    }
                });
                //所有符合条件的数据
                var old = _dbContext
                    .Db.Queryable<SYS6_SETUP_DICT>()
                    .Where(p => arrPgroupList.Contains(p.UNIT_ID) && p.SETUP_STATE == "1")
                    .ToList();
                old.ForEach(item =>
                {
                    item.CHOICE_VALUE = choiceValue;
                    item.LAST_MPERSON = userName;
                    item.LAST_MTIME = DateTime.Now;
                });
                _dbContext
                    .Db.Updateable(old)
                    .IgnoreColumns(ignoreAllNullColumns: true)
                    .ExecuteCommand();
                result.success = true;
            }
            catch (Exception ex)
            {
                result.success = false;
                result.msg = "应用失败";
                _logger.LogError("应用失败:\n" + ex.Message);
            }
            return result;
        }

        public bool NeedReviewProcess(string hospitalId)
        {
            const string KEY = "H820101";
            var result = true;
            var SetUpValue1 = _dbContext
                .Db.Queryable<SYS6_SETUP>()
                .Where(x => x.SETUP_NO == KEY && x.HOSPITAL_ID == hospitalId)
                .First();
            if (SetUpValue1 is not null)
            {
                result = SetUpValue1.SETUP_VALUE == "1";
                return result;
            }
            var SetUpValue2 = _dbContext
                .Db.Queryable<SYS6_SETUP_DICT>()
                .Where(x => x.SETUP_NO == KEY && x.SETUP_STATE == "1")
                .First();

            if (SetUpValue2 is not null)
            {
                result = SetUpValue2.DEFAULT_VALUE == "1";
            }
            return result;
        }

        public bool NeedNeedCheckPasswordByEditEquipment(string hospitalId)
        {
            const string KEY = "H820102";
            var result = true;
            var setUpValue1 = _dbContext
                .Db.Queryable<SYS6_SETUP>()
                .Where(x => x.SETUP_NO == KEY && x.HOSPITAL_ID == hospitalId)
                .First();
            if (setUpValue1 is not null)
            {
                result = setUpValue1.SETUP_VALUE == "1";
                return result;
            }
            var setUpValue2 = _dbContext
                .Db.Queryable<SYS6_SETUP_DICT>()
                .Where(x => x.SETUP_NO == KEY && x.SETUP_STATE == "1")
                .First();
            if (setUpValue2 is not null)
            {
                result = setUpValue2.DEFAULT_VALUE == "1";
            }
            return result;
        }

        public List<SYS6_POSITION_DICT> GetPositions()
        {
            return _dbContext
                .Db.Queryable<SYS6_POSITION_DICT>()
                .Where(x => x.POSITION_STATE == "1")
                .ToList();
        }

        public ConcurrentDictionary<string, List<AllBaseDataDto>> GetBaseDatas()
        {
            var user =  _httpContext.HttpContext.User.ToClaimsDto();
            var result = new ConcurrentDictionary<string, List<AllBaseDataDto>>();
            
            var classes = _dbContext.Db.Queryable<SYS6_BASE_DATA_CLASS>()
                .Where(x=>x.CLASS_STATE == "1")
                .ToList();
            foreach (var classDict in classes)
            {
                if (classDict.CLASS_ID.IsNotNullOrEmpty())
                {
                    result.TryAdd(classDict.CLASS_ID, new ());
                }
            }
            result.TryAdd("设备类型汇总", new());
            
            var sys6data = GetSys6BaseDatas().Select(x=> new AllBaseDataDto
            {
                DataId = x.DATA_ID,
                DataName = x.DATA_CNAME ?? x.DATA_SNAME,
                DataTable = "SYS6_BASE_DATA",
                FatherId = x.ONE_CLASS,
                ClassId = x.CLASS_ID,
                IsEdit = false,
                IsPull = true,
            });
            var oaBaseData = GetOaBaseDatas(user.HOSPITAL_ID)
                .Select(x=> new AllBaseDataDto
                {
                    DataId = x.DATA_ID,
                    DataName = x.DATA_NAME ?? x.DATA_SNAME,
                    DataTable = "OA_BASE_DATA",
                    FatherId = x.FATHER_ID,
                    ClassId = x.CLASS_ID,
                    IsEdit = true,
                    IsPull = true,
                });
            var basedata = GetEquipmentBaseData(user.HOSPITAL_ID).ToList();

            
            
            foreach (var item in result)
            {
                var baseClass = basedata.Where(x => x.ClassId == item.Key).ToList();
                if (baseClass.Any())
                { 
                    item.Value.AddRange(baseClass.Select(x => new AllBaseDataDto
                    {
                        DataId = x.DataId,
                        DataName = x.DataName,
                        DataTable = x.DataTable,
                        FatherId = x.FatherId,
                        ClassId = x.ClassId,
                        IsEdit = true,
                        IsPull = true,
                    }));
                }
                var sysClass = sys6data.Where(x => x.ClassId == item.Key).ToList();
                if (sysClass.Any())
                { 
                    item.Value.AddRange(sysClass);
                    continue;
                }
                var oaClass = oaBaseData.Where(x => x.DataId == item.Key).ToList();
                if (oaClass.Any())
                {
                    item.Value.AddRange(oaClass);
                }
            }
            
            return result;
        }
        
        /// <summary>
        /// 基础数据管理基础数据
        /// </summary>
        /// <returns></returns>
        private List<SYS6_BASE_DATA> GetSys6BaseDatas()
        { 
            return _dbContext
                .Db.Queryable<SYS6_BASE_DATA>()
                .InnerJoin<SYS6_BASE_DATA_CLASS>((x,y)=> x.CLASS_ID == y.CLASS_ID)
                .Where(x => x.DATA_STATE == "1")
                .ToList();
        }
        
        /// <summary>
        /// 实验室管理的基础数据管理基础数据
        /// </summary>
        /// <returns></returns>
        private List<OA_BASE_DATA> GetOaBaseDatas(string hospitalId)
        { 
            return _dbContext
                .Db.Queryable<OA_BASE_DATA>()
                .Where(x => x.HOSPITAL_ID == hospitalId)
                .Where(x => x.STATE_FLAG != "2")
                .ToList();
        }

        
        private List<AllBaseDataDto> GetEquipmentBaseData(string hospitalId)
        {
           var  result = Array.Empty<AllBaseDataDto>().ToList();

           var equipmentContext = new EquipmentContext(_dbContext);

           var classes =   equipmentContext.EquipmentClassDicts
               .Select(x => new AllBaseDataDto
               {
                DataId = x.ClassId,
                DataName = x.ClassName,
                DataTable = "EMS_EQUIPMENT_CLASS_DICT",
                FatherId = x.ParentClassId,
                ClassId = "设备类型汇总",
                IsEdit = false,
                IsPull = true,
                Status = x.ClassState,
               });
           result.AddRange(classes);
           return result;
        }
    }
}
