using H.Utility;
using XH.H82.Models.BusinessModuleClient;
using XH.H82.Models.Dtos.AuthorizationRecord;
using XH.H82.Models.Entities;
using XH.H82.Models.Entities.Transaction;
using XH.H82.Services.AuthorizationRecord;

namespace XH.H82.IServices.AuthorizationRecord;

public interface IAuthorizationRecordService
{
    /// <summary>
    /// 新增授权权限字典
    /// </summary>
    /// <param name="content"></param>
    /// <param name="remark"></param>
    /// <returns></returns>
    public OA_BASE_DATA AddAuthorizationRecordDict(string content, string? remark);
    /// <summary>
    /// 更新授权权限字典
    /// </summary>
    /// <param name="id"></param>
    /// <param name="content"></param>
    /// <param name="remark"></param>
    public void UpdateAuthorizationRecordDict(string id, string content, string? remark);
    /// <summary>
    /// 删除授权权限字典
    /// </summary>
    /// <param name="id"></param>
    public void DeleteAuthorizationRecordDict(string id);
    /// <summary>
    /// 查询授权权限字典
    /// </summary>
    /// <param name="content"></param>
    /// <returns></returns>
    public List<OA_BASE_DATA> GetAuthorizationRecordDicts(string? content);


    /// <summary>
    /// 添加授权记录
    /// </summary>
    /// <param name="equipmentId">设备id</param>
    /// <param name="input"></param>
    /// <returns></returns>
    public EMS_AUTHORIZE_INFO AddAuthorizationRecord(string equipmentId, AuthorizationRecordDto input);
    
    
    /// <summary>
    /// 更新授权记录
    /// </summary>
    /// <param name="授权记录id  AUTHORIZE_ID ">设备id</param>
    /// <param name="input"></param>
    /// <returns></returns>
    public void UpdateAuthorizationRecord(string id, AuthorizationRecordDto input);

    /// <summary>
    /// 根据用户hisName 返回用户信息
    /// </summary>
    /// <param name="names"></param>
    /// <returns></returns>
    List<TableUserInfoDto> GetTableUsersByHisName(List<string> names);

    /// <summary>
    /// 根据用户userNo 返回用户信息
    /// </summary>
    /// <param name="ids"></param>
    /// <returns></returns>
    List<TableUserInfoDto> GetTableUsersByIds(List<string> ids);
}