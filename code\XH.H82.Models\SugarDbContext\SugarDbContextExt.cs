﻿using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using XH.H82.Models.Entities;

namespace XH.H82.Models.SugarDbContext
{
    public static class SugarDbContextExt
    {
        public static ISqlSugarUow<SugarDbContext_Master> SetCreateTimeAndCreatePersonData(this ISqlSugarUow<SugarDbContext_Master> context, ClaimsDto user)
        {
            var now = DateTime.Now;
            context.Db.Aop.DataExecuting = (oldValue, entityInfo) =>
            {
                if (user is null)
                {
                    return;
                }
                if (entityInfo.OperationType == DataFilterType.InsertByObject)
                {
                    if (entityInfo.PropertyName == "HOSPITAL_ID")
                    {
                        if (entityInfo.EntityValue == null)
                        {
                            entityInfo.SetValue(user.HOSPITAL_ID);
                        }
                    }
                    if (entityInfo.PropertyName == "FIRST_RPERSON")
                    {
                        entityInfo.SetValue(user.HIS_NAME);
                    }
                    if (entityInfo.PropertyName == "FIRST_RTIME")
                    {
                        entityInfo.SetValue(now);
                    }
                    if (entityInfo.PropertyName == "LAST_MPERSON")
                    {
                        entityInfo.SetValue(user.HIS_NAME);
                    }
                    if (entityInfo.PropertyName == "LAST_MTIME")
                    {
                        entityInfo.SetValue(now);
                    }
                }
                if (entityInfo.OperationType == DataFilterType.UpdateByObject)
                {
                    if (entityInfo.PropertyName == "LAST_MPERSON")
                    {
                        entityInfo.SetValue(user.HIS_NAME);
                    }
                    if (entityInfo.PropertyName == "LAST_MTIME")
                    {
                        entityInfo.SetValue(now);
                    }
                }
            };

            return context;
        }
        
        public static ISqlSugarUow<SugarDbContext_Master> IsDeleted<T>(this ISqlSugarUow<SugarDbContext_Master> context, Expression<Func<T, bool>> where)
        {
            context.Db.QueryFilter.AddTableFilter<T>(where);
            return context;
        }

        /// <summary>
        /// 根据机构id查询
        /// </summary>
        /// <param name="context"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        public static ISqlSugarUow<SugarDbContext_Master> SelectHospitalId(this ISqlSugarUow<SugarDbContext_Master> context, ClaimsDto user = null)
        {
            if (user is null)
            {
                return context;
            }
            context.Db.QueryFilter.AddTableFilter<EMS_WORK_PLAN>(x => x.HOSPITAL_ID == user.HOSPITAL_ID);
            context.Db.QueryFilter.AddTableFilter<EMS_EQUIPMENT_INFO>(x => x.HOSPITAL_ID == user.HOSPITAL_ID);

            return context;
        }


        /// <summary>
        /// 不打印日志
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public static ISqlSugarUow<SugarDbContext_Master> NoLog(this ISqlSugarUow<SugarDbContext_Master> context)
        {
            
            context.Db.Aop.OnLogExecuting = (sql, values) =>
            {
                
            };
            context.Db.Aop.OnLogExecuted = (sql, values) =>
            {
                Console.WriteLine($"sql start=========================================");
                Console.WriteLine();
                //Console.WriteLine($"sql语句:{sql}");
                foreach (var item in values)
                {
                    sql = sql.Replace(item.ParameterName, item.Value.ToString());
                }
                Console.WriteLine($"sql语句:{sql}");
                //foreach (var item in values) Console.WriteLine($"ParameterName\t\t{item.ParameterName}\t\t{item.Value}");
                Console.WriteLine();
                Console.WriteLine($"sql end=================================");
            };

            return context;
            
        }
    }
}
