﻿using System.ComponentModel;
using XH.LAB.UTILS.Helpers;

namespace XH.H82.API.Controllers.IoTDevice;

/// <summary>
/// 设备大各个状态大概情况
/// </summary>
public class EquipmentDistributionDto
{
    /// <summary>
    /// 总数 重点设备/全部设备
    /// </summary>
    public DistributionDto[] AllNumList { get; set; } = new DistributionDto[2];
    /// <summary>
    /// 各个状态总数
    /// </summary>
    public DistributionDto[] equipmentStateList { get; set; } = new DistributionDto[4];
}

public class DistributionDto
{
    /// <summary>
    /// 数量
    /// </summary>
    public int Num { get; set; }
    /// <summary>
    /// 状态名称
    /// </summary>
    public string name => enumState.ToDesc();
    public DistributionState enumState { get; init; }
    public string state  => enumState.ToString();

}


public enum DistributionState
{
    [Description("总设备")]
    all =  0,
    [Description("关键设备")]
    focus = 1,
    [Description("正常")]
    active = 2,
    [Description("未启用")]
    notNabled = 3,
    [Description("停用")]
    stopUse = 4,
    [Description("报废")]
    scrap= 5
}