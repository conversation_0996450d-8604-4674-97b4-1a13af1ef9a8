2025-07-03 12:47:38.877 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-03 12:47:42.866 +08:00 [ERR] 查询出错:中文提示 :  连接数据库过程中发生错误，检查服务器是否正常连接字符串是否正确，错误信息：ORA-12545: Network Transport: Unable to resolve connect hostnameDbType="Oracle";ConfigId="".
English Message : Connection open error . ORA-12545: Network Transport: Unable to resolve connect hostnameDbType="Oracle";ConfigId="" :
SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON" FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "LAST_MTIME" IS NULL )
2025-07-03 12:47:58.142 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-03 12:52:55.145 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-03 12:56:19.547 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-03 13:05:19.737 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-03 13:16:20.332 +08:00 [ERR] 未处理的异常::System.MissingMethodException: Method not found: 'System.Collections.Generic.List`1<XH.H82.Models.Dtos.KeyValueDto> XH.H82.Models.EquipmentCodeCustom.DisplayContent.CreatFixedFieldDisplays()'.
   at XH.H82.API.Controllers.EquipmentCodeCustom.CodeCustomController.AddEquipmentCodeCustomDict(AddEquipmentCodeCustomDictDto input)
   at lambda_method909(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-07-03 13:16:20.336 +08:00 [ERR] HTTP POST /api/CodeCustom/AddEquipmentCodeCustomDict responded 500 in 96.9597 ms
2025-07-03 13:16:46.520 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-03 13:18:44.449 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-03 13:21:00.188 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-03 13:23:13.473 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-03 13:49:59.956 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-03 13:50:02.094 +08:00 [ERR] 查询出错:中文提示 :  连接数据库过程中发生错误，检查服务器是否正常连接字符串是否正确，错误信息：ORA-12545: Network Transport: Unable to resolve connect hostnameDbType="Oracle";ConfigId="".
English Message : Connection open error . ORA-12545: Network Transport: Unable to resolve connect hostnameDbType="Oracle";ConfigId="" :
SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON" FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "LAST_MTIME" IS NULL )
2025-07-03 13:50:20.512 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-03 13:59:35.373 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-03 13:59:35.412 +08:00 [ERR] 调用H07检测版本依赖时发生错误:Index and length must refer to a location within the string. (Parameter 'length')
System.ArgumentOutOfRangeException: Index and length must refer to a location within the string. (Parameter 'length')
   at System.String.Substring(Int32 startIndex, Int32 length)
   at Spectre.Console.Rendering.Segment.SplitOverflow(Segment segment, Nullable`1 overflow, Int32 maxWidth) in /_/src/Spectre.Console/Rendering/Segment.cs:line 351
   at Spectre.Console.Paragraph.SplitLines(Int32 maxWidth) in /_/src/Spectre.Console/Widgets/Paragraph.cs:line 242
   at Spectre.Console.Paragraph.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Widgets/Paragraph.cs:line 144
   at Spectre.Console.Rendering.Renderable.Spectre.Console.Rendering.IRenderable.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Rendering/Renderable.cs:line 19
   at Spectre.Console.Markup.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Widgets/Markup.cs:line 54
   at Spectre.Console.Rendering.Renderable.Spectre.Console.Rendering.IRenderable.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Rendering/Renderable.cs:line 19
   at Spectre.Console.TableRenderer.Render(TableRendererContext context, List`1 columnWidths) in /_/src/Spectre.Console/Widgets/Table/TableRenderer.cs:line 31
   at Spectre.Console.Table.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Widgets/Table/Table.cs:line 141
   at Spectre.Console.Rendering.Renderable.Spectre.Console.Rendering.IRenderable.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Rendering/Renderable.cs:line 19
   at Spectre.Console.RenderableExtensions.GetSegments(IAnsiConsole console, RenderContext options, IEnumerable`1 renderables) in /_/src/Spectre.Console/Extensions/RenderableExtensions.cs:line 37
   at Spectre.Console.RenderableExtensions.GetSegments(IRenderable renderable, IAnsiConsole console) in /_/src/Spectre.Console/Extensions/RenderableExtensions.cs:line 29
   at Spectre.Console.AnsiBuilder.Build(IAnsiConsole console, IRenderable renderable) in /_/src/Spectre.Console/Internal/Backends/Ansi/AnsiBuilder.cs:line 17
   at Spectre.Console.AnsiConsoleBackend.Write(IRenderable renderable) in /_/src/Spectre.Console/Internal/Backends/Ansi/AnsiConsoleBackend.cs:line 30
   at Spectre.Console.AnsiConsoleFacade.Write(IRenderable renderable) in /_/src/Spectre.Console/Internal/Backends/AnsiConsoleFacade.cs:line 40
   at Spectre.Console.AnsiConsole.Write(IRenderable renderable) in /_/src/Spectre.Console/AnsiConsole.Rendering.cs:line 29
   at H.BASE.AppInit.<RegisterInfraServer>b__22_0()
2025-07-03 14:10:35.855 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-03 14:11:05.325 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-03 14:43:57.895 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
