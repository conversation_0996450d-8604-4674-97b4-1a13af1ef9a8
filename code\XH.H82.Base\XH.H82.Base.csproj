﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="bin\**" />
    <Compile Remove="obj\**" />
    <EmbeddedResource Remove="bin\**" />
    <EmbeddedResource Remove="obj\**" />
    <None Remove="bin\**" />
    <None Remove="obj\**" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Helper\SerilogHelper.cs" />
    <Compile Remove="NonRoleAuthorizeAttribute.cs" />
    <Compile Remove="Setup\AppSettingsProvider.cs" />
    <Compile Remove="Setup\CacheSetup.cs" />
    <Compile Remove="Setup\DBContextSetup.cs" />
    <Compile Remove="Setup\ELKSetup.cs" />
    <Compile Remove="Setup\GetDBtype.cs" />
    <Compile Remove="Setup\JwtAuthenticationSetup.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="EasyCaching.Serialization.Protobuf" Version="1.7.0" />
    <PackageReference Include="EPPlus" Version="6.2.6" />
    <PackageReference Include="FireflySoft.RateLimit.AspNetCore" Version="3.0.0" />
    <PackageReference Include="iTextSharp" Version="5.5.13.3" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="6.0.15" />
    <PackageReference Include="RestSharp" Version="112.1.0" />
    <PackageReference Include="Serilog.Sinks.FastConsole" Version="2.2.0" />
    <PackageReference Include="Serilog.Sinks.SpectreConsole" Version="0.3.3" />
    <PackageReference Include="SkiaSharp" Version="2.88.9" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.2.3" />
    <PackageReference Include="Swashbuckle.AspNetCore.Filters" Version="7.0.5" />
    <PackageReference Include="Swashbuckle.AspNetCore.Filters.Abstractions" Version="7.0.5" />
    <PackageReference Include="Unchase.Swashbuckle.AspNetCore.Extensions" Version="2.6.12" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\XH.H82.Models\XH.H82.Models.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="Spire.Pdf">
      <HintPath>..\..\..\..\..\.nuget\packages\spire.officefor.netstandard\8.8.0\lib\netstandard2.0\Spire.Pdf.dll</HintPath>
    </Reference>
  </ItemGroup>

</Project>
