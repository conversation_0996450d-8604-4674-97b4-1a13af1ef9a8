﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.Dtos.FileTemplate
{
    /// <summary>
    /// OnlyOffice模版样式批量添加  STYLE_INFO
    /// </summary>
    public class OaOfficeStyleTemplateBatchCreateDto
    {
        /// <summary>
        /// 医疗机构ID
        /// </summary>
        public string HOSPITAL_ID { get; set; }

        /// <summary>
        /// 科室ID
        /// </summary>
        public string LAB_ID { get; set; }

        /// <summary>
        /// 院区ID
        /// </summary>
        public string? AREA_ID { get; set; }

        /// <summary>
        /// 检验专业组ID
        /// </summary>
        public string? PGROUP_ID { get; set; }

        /// <summary>
        /// 所属类型 0科室 1专业组
        /// </summary>
        public string? LAB_PGROUP_TYPE { get; set; }

        /// <summary>
        /// 模版样式类型 提供给业务模块做多种分类样式表过滤
        /// </summary>
        public string? STYLE_CLASS_CODE { get; set; }

        /// <summary>
        /// 模板名称
        /// </summary>
        public string STYLE_NAME { get; set; }

        /// <summary>
        /// 来源数据ID  提供给业务模块冗余用
        /// </summary>
        public string? DATA_ID { get; set; }

        /// <summary>
        /// 状态;0禁用1启用2删除
        /// </summary>
        public string STYLE_STATE { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string REMARK { get; set; }


    }
}
