﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.Dtos.SysDocDtos
{
    /// <summary>
    /// 文件系统中的文件对象
    /// </summary>
    public class FileSystemFileInfo
    {
        [JsonProperty("DOC_ID")]
        public string DocId { get; set; }

        [JsonProperty("DOC_NAME")]
        public string DocName { get; set; }

        [JsonProperty("DOC_PROCCESS_STATE")]
        public string DocProccessState { get; set; }

        [JsonProperty("DOC_TYPE")]
        public string DocType { get; set; }

        [JsonProperty("PGROUP_ID")]
        public string PgroupId { get; set; }

        public string PgroupName { get; set; }

        [JsonProperty("DOC_SORT")]
        public string DocSort { get; set; }

        [JsonProperty("DRAFTERS_DATE")]
        public DateTime? DraftersDate { get; set; }

        [JsonProperty("FILE_SUFFIX")]
        public string FileSuffix { get; set; }

        [JsonProperty("CLICK_VOLUME")]
        public string ClickVolume { get; set; }

        [JsonProperty("COLLECT_ID")]
        public string CollectId { get; set; }

        [JsonProperty("FILE_ID")]
        public string FileId { get; set; }

        [JsonProperty("DOC_CODE")]
        public string DocCode { get; set; }

        [JsonProperty("DOC_VERSION")]
        public string DocVersion { get; set; }

        [JsonProperty("FILE_PREVIEW_NGINX")]
        public string FilePreviewNginx { get; set; }

        public string FilePath { get; set; }

    }
}
