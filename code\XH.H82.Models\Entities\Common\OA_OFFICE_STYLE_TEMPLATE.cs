﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.Entities.Common
{
    /// <summary>
    /// OnlyOffice模版样式记录表 
    /// </summary>
    [DBOwner("XH_OA")]

    public class OA_OFFICE_STYLE_TEMPLATE
    {
        /// <summary>
        /// 上传文件ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        /// <summary>
        /// 样式ID
        /// </summary>
        public string STYLE_ID { get; set; }

        /// <summary>
        /// 医疗机构ID
        /// </summary>
        public string? HOSPITAL_ID { get; set; }

        /// <summary>
        /// 科室ID
        /// </summary>
        public string? LAB_ID { get; set; }

        /// <summary>
        /// 院区ID
        /// </summary>
        public string? AREA_ID { get; set; }

        /// <summary>
        /// 检验专业组ID
        /// </summary>
        public string? PGROUP_ID { get; set; }

        /// <summary>
        /// 所属类型 0科室 1专业组
        /// </summary>
        public string? LAB_PGROUP_TYPE { get; set; }

        /// <summary>
        /// 模块ID
        /// </summary>
        public string MODULE_ID { get; set; }

        /// <summary>
        /// 所属目录名（只有一级目录）
        /// </summary>
        public string CATALOG_NAME { get; set; }

        /// <summary>
        /// 模版样式类型 提供给业务模块做多种分类样式表过滤
        /// </summary>
        public string? STYLE_CLASS_CODE { get; set; }

        /// <summary>
        /// 文件上传ID OA_UPLOAD_FILE的FILE_ID
        /// </summary>
        public string FILE_ID { get; set; }

        /// <summary>
        /// 原始文件
        /// </summary>
        public string ORIGIN_FILE_ID { get; set; }

        /// <summary>
        /// 页眉页脚ID
        /// </summary>
        public string? HEADER_ID { get; set; }

        /// <summary>
        /// 模板名称
        /// </summary>
        public string STYLE_NAME { get; set; }

        /// <summary>
        /// 来源数据ID  提供给业务模块冗余用
        /// </summary>
        public string? DATA_ID { get; set; }

        /// <summary>
        /// 状态;0禁用1启用2删除
        /// </summary>
        public string STYLE_STATE { get; set; }

        /// <summary>
        /// 首次登记人
        /// </summary>
        public string FIRST_RPERSON { get; set; }

        /// <summary>
        /// 首次登记时间
        /// </summary>
        public DateTime FIRST_RTIME { get; set; }

        /// <summary>
        /// 最后修改人员
        /// </summary>
        public string LAST_MPERSON { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime LAST_MTIME { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? REMARK { get; set; }


        /// <summary>
        /// 配置的分类及字段(OA_FIELD_DICT),格式CLASS_CODE:FIELD_CODE1,FIELD_CODE2
        /// </summary>
        public string CLASS_COL_JSON { get; set; }


    }
}
