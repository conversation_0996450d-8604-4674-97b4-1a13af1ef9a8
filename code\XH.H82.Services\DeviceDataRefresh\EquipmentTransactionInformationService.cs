﻿using EasyCaching.Core;
using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using RestSharp;
using SqlSugar;
using XH.H82.IServices.DeviceDataRefresh;
using XH.H82.Models.BusinessModuleClient;
using XH.H82.Models.DeviceRelevantInformation;
using XH.H82.Models.DeviceRelevantInformation.Dto;
using XH.H82.Models.SugarDbContext;
using XH.LAB.UTILS.Helpers;
using XH.LAB.UTILS.Interface;
using XH.LAB.UTILS.Models;

namespace XH.H82.Services.DeviceDataRefresh
{
    public class EquipmentTransactionInformationService : IDeviceRefreshService
    {
        private readonly IHttpContextAccessor _httpContext;
        private readonly ISqlSugarUow<SugarDbContext_Master> _dbContext;
        private readonly EquipmentContext equipmentContext;
        private readonly IConfiguration _configuration;
        private readonly IAuthorityService2 _authorityService;
        private IMemoryCache _memoryCache;

        private H115Client h115Client { get; set; }
        private H92Client h92Client { get; set; }


        public EquipmentTransactionInformationService(ISqlSugarUow<SugarDbContext_Master> dbContext, IHttpContextAccessor httpContext, IConfiguration configuration, IMemoryCache memoryCache, IAuthorityService2 authorityService)
        {

            h115Client = new(configuration["H115"], httpContext);
            h92Client = new(configuration["H92"], httpContext);
            _dbContext = dbContext;
            _dbContext.NoLog();
            _httpContext = httpContext;
            equipmentContext = new(_dbContext);
            _configuration = configuration;
            _memoryCache = memoryCache;
            _authorityService = authorityService;
        }

        public EquipmentWarnInfoDto GetEquipmentWarnInfo(string hosptalId, string? areaId, string? labId)
        {
            var key = $"{hosptalId}:{labId}:warnInfo";
            var result = new EquipmentWarnInfoDto();

            var equipmentWarnInfo = _memoryCache.Get<EquipmentWarnInfoDto>(key);
            if (equipmentWarnInfo is null)
            {
                var warnInfo = equipmentContext.GetEquipmentWarnInfo(hosptalId, labId);
                _memoryCache.Set(key, warnInfo, TimeSpan.FromMinutes(5));
            }
            equipmentWarnInfo = _memoryCache.Get<EquipmentWarnInfoDto>(key);
            if (equipmentWarnInfo is null)
            {
                return new EquipmentWarnInfoDto();
            }
            if (areaId.IsNullOrEmpty())
            {
                return equipmentWarnInfo;
            }
            else
            {
                var pGroups = _dbContext.Db.Queryable<SYS6_INSPECTION_PGROUP>()
               .Where(x => x.AREA_ID == areaId)
               .Select(x => x.PGROUP_ID).ToList();
                if (pGroups is null)
                {
                    pGroups = new List<string>();
                }
                var faultRecords = equipmentWarnInfo.FaultRecords.Where(x => pGroups.Contains(x.GroupId));
                var comparisonRecords = equipmentWarnInfo.ComparisonRecords.Where(x => pGroups.Contains(x.GroupId));
                var maintenanceRecords = equipmentWarnInfo.MaintenanceRecords.Where(x => pGroups.Contains(x.GroupId));
                var correctRecords = equipmentWarnInfo.CorrectRecords.Where(x => pGroups.Contains(x.GroupId));
                result.CorrectRecords.AddRange(correctRecords);
                result.MaintenanceRecords.AddRange(maintenanceRecords);
                result.FaultRecords.AddRange(faultRecords);
                result.ComparisonRecords.AddRange(comparisonRecords);
            }
            return result;
        }

        public List<EquipmentDisplay> GetEquipmentDisplayInfo()
        {
            equipmentContext.Injection();
            equipmentContext.AutoAllEarlyWarning();
            return equipmentContext.equipmentDisplays;
        }

        /// <summary>
        /// 终端设备展示设备信息
        /// </summary>
        /// <param name="equipmentId"></param>
        /// <returns></returns>
        public ResultDto GetDisplayInfo(string equipmentId)
        {
            equipmentContext.Injection(equipmentId);
            var data = equipmentContext.ExchangeIdentificationCard(equipmentId);
            var result = h115Client.GetGenerateImgBase64(data.ToEinkChangeDisplay());
            return result;
        }


        /// <summary>
        /// 终端设备展示设备信息
        /// </summary>
        /// <param name="equipmentId"></param>
        /// <returns></returns>
        public byte[] GetDisplayInfoTest(string equipmentId)
        {
            equipmentContext.Injection(equipmentId);
            var data = equipmentContext.ExchangeIdentificationCard(equipmentId);
            var result = h115Client.GetGenerateImgStream(data.ToEinkChangeDisplay());
            return result;
        }

        public void AutoRefreshDisplayInfo()
        {
            try
            {
                equipmentContext.Injection();
                equipmentContext.LoadCertificates(h92Client);
                equipmentContext.AutoAllEarlyWarning();
                Task.Factory.StartNew(() =>
                {
                    if (_configuration.GetSection("IsAutoRefresh").Exists())
                    {
                        if (_configuration.GetSection("IsAutoRefresh").Value == "1")
                        {
                            equipmentContext.RefreshCradInfo(h115Client);
                        }
                    }
                });
            }
            catch (Exception e)
            {
                throw e;
            }
            finally
            {
                _dbContext.Db.Dispose();
            }

        }



        public void AutoRefreshDisplayInfo(string equipmentId)
        {
            try
            {
                equipmentContext.Injection(equipmentId);
                equipmentContext.LoadCertificates(h92Client);
                equipmentContext.AutoAllEarlyWarning();
                Task.Factory.StartNew(() =>
                {
                    if (_configuration.GetSection("IsAutoRefresh").Exists())
                    {
                        if (_configuration.GetSection("IsAutoRefresh").Value == "1")
                        {
                            equipmentContext.RefreshCradInfo(h115Client);
                        }
                    }
                });
            }
            catch (Exception e)
            {
                throw e;
            }

        }


        /// <summary>
        /// 查询可绑定墨水屏的设备列表
        /// </summary>
        /// <param name="hosptialId"></param>
        /// <param name="pGourpId"></param>
        /// <param name="codeOrModelOrName"></param>
        /// <returns></returns>
        public List<InkScreenBindEquipmentDto> GetInkScreenBindEquipments(ClaimsDto user, string hosptialId, string? labId, string? pGourpId, string? codeOrModelOrName)
        {
            var ids = new List<string>();
            var result = new List<InkScreenBindEquipmentDto>();
            equipmentContext.SetHosptalIdAndLabId(hosptialId, null);
            equipmentContext.Injection();
            var authorityContext = new AuthorityContext(_dbContext, _authorityService);
            authorityContext.SetUser(user, labId);
            ids = authorityContext.GetAccessibleProfessionalGroups(labId, null).Where(x => x.PGROUP_STATE == "1").Select(x => x.PGROUP_ID).ToList();
            var equipments = equipmentContext.equipments
                .Where(x => x.IS_HIDE != "1")
                .WhereIF(ids.Count() > 0, x => ids.Contains(x.UNIT_ID))
                .WhereIF(codeOrModelOrName.IsNotNullOrEmpty(), x => x.EQUIPMENT_NAME is not null)
                .WhereIF(codeOrModelOrName.IsNotNullOrEmpty(), x => x.EQUIPMENT_MODEL is not null)
                .WhereIF(codeOrModelOrName.IsNotNullOrEmpty(), x => x.EQUIPMENT_CODE is not null)
                .WhereIF(codeOrModelOrName.IsNotNullOrEmpty(), x => (x.EQUIPMENT_CODE.Contains(codeOrModelOrName) || x.EQUIPMENT_MODEL.Contains(codeOrModelOrName) || x.EQUIPMENT_NAME.Contains(codeOrModelOrName)))
                .WhereIF(pGourpId.IsNotNullOrEmpty(), x => x.UNIT_ID == pGourpId);


            foreach (var equipment in equipments)
            {
                result.Add(new InkScreenBindEquipmentDto(equipment.EQUIPMENT_ID, equipment.EQUIPMENT_NAME, equipment.EQUIPMENT_CODE, equipment.EQUIPMENT_MODEL, equipmentContext.ExchangeProfessionalGroupName(equipment.UNIT_ID)));
            }

            return result;
        }
    }
}
