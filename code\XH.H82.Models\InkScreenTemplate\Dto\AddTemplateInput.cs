﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.InkScreenTemplate.Dto
{
    /// <summary>
    /// 输入模型
    /// </summary>
    /// <param name="templateName">模板名称</param>
    /// <param name="remark">备注</param>
    public record AddTemplateInput([Required] string templateName, string? remark);

    /// <summary>
    /// 输入模型
    /// </summary>
    /// <param name="templateName">模板名称</param>
    /// <param name="remark">备注</param>
    public record UpdateTemplateInput([Required] string templateName, string? remark);


    /// <summary>
    ///保存模型内容信息
    /// </summary>
    /// <param name="tempContent">模板内容json字符串</param>
    /// <param name="tempTitle">模板标题</param>
    /// <param name="setAbnormal">异常亮灯</param>
    /// <param name="setQRCode">二维码</param>
    /// <param name="SetWireframe">表格线框</param>
    public record SaveTemplateInput( string tempContent, string? tempTitle, bool setAbnormal, bool setQRCode, bool SetWireframe);
}
