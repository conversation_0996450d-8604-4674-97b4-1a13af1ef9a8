using Newtonsoft.Json;

namespace XH.H82.Models.BusinessModuleClient.Dto.IoTDevices;

/// <summary>
/// 传感器
/// </summary>
public class Sensor
{
    /// <summary>
    /// 传感器ID
    /// </summary>
    [JsonProperty("id")]
    public string Id { get; set; }
    /// <summary>
    /// 传感器名称
    /// </summary>
    [JsonProperty("name")]
    public string Name { get; set; }
    /// <summary>
    /// SN码 唯一
    /// </summary>
    [JsonProperty("sn")]
    public string Sn { get; set; }
    /// <summary>
    /// 传感器型号
    /// </summary>
    [JsonProperty("model")]
    public string Model { get; set; }

    [JsonProperty("ip")]
    public string? Ip { get; set; }
    /// <summary>
    /// 传感器类型
    /// </summary>
    [JsonProperty("sensorType")]
    public int SensorType { get; set; }
    /// <summary>
    /// 传感器所在房间id
    /// </summary>
    [JsonProperty("roomId")]
    public long? RoomId { get; set; }
    /// <summary>
    /// 传感器所在房间名称
    /// </summary>
    [JsonProperty("roomName")]
    public string? RoomName { get; set; }
    /// <summary>
    /// 传感器所在房间监测点id
    /// </summary>
    [JsonProperty("checkpointId")]
    public string? CheckpointId { get; set; }
    /// <summary>
    /// 传感器所在监测点名称
    /// </summary>
    [JsonProperty("checkpointName")]
    public string? CheckpointName { get; set; }
    /// <summary>
    /// 传感器所属实验室id
    /// </summary>
    [JsonProperty("labId")]
    public long? LabId { get; set; }
    /// <summary>
    /// 传感器所属实验室名称
    /// </summary>
    [JsonProperty("labName")]
    public string LabName { get; set; } = "";
    /// <summary>
    /// 传感器是否在线  1在线  0离线
    /// </summary>
    [JsonProperty("isOnline")]
    public int IsOnline { get; set; } 
    /// <summary>
    /// 医疗设备开关状态 0离线/关机 1待机,2运行,201生物安全柜紫外灯运行,3过载
    /// </summary>
    [JsonProperty("switchStatus")]
    public int SwitchStatus { get; set; } 
    
    [JsonProperty("alarmRules",NullValueHandling = NullValueHandling.Ignore)]
    public List<AlarmRules> alarmRules { get; set; } = new ();
}