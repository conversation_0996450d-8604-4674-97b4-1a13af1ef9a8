﻿using H.Utility;
using XH.H82.Models.BusinessModuleClient;

namespace XH.H82.IServices
{
    /// <summary>
    /// 基础数据服务接口
    /// 注意各自项目不要修改此接口
    /// 由余祥琼统一维护
    /// </summary>
    public interface IBaseDataServices
    {
        /// <summary>
        /// 调用系统数据管理获取固定基础数据
        /// </summary>
        /// <param name="classId"></param>
        /// <param name="systemId"></param>
        /// <param name="moduleId"></param>
        /// <param name="oneClass"></param>
        /// <param name="getType"><see cref="EnumBaseDataGetType"/></param>
        /// <returns></returns>
        public List<Lis5BaseDataDto> GetLis5BaseData(string classId, string systemId, string moduleId, string oneClass, EnumBaseDataGetType getType);

        /// <summary>
        /// 获取组织机构数据
        /// 组织机构数据包括：系统实例,外部系统,医疗机构,院区、分院/检验科室/位置字典/检验专业组/管理专业组
        /// </summary>
        /// <param name="hospitalId">机构ID</param>
        /// <param name="getType">1,缓存数据，2,表数据</param>
        /// <returns></returns>
        OrganizeDataDto GetOrganizeData(string hospitalId, EnumBaseDataGetType getType);

        /// <summary>
        /// 基础数据
        /// 病人科别,病人病区,护理单元,医院护工表,医院医生表,医院护士表,公司信息,公司联系人
        /// </summary>
        /// <param name="classId">多个用英文逗号隔开</param>
        /// <param name="hospitalId">医疗机构ID</param>
        /// <param name="labId">科室ID</param>
        /// <param name="getType">1,缓存数据，2,表数据</param>
        /// <returns></returns>
        LisBaseDataDto GetLisBaseData(string classId, string hospitalId, string labId, EnumBaseDataGetType getType);

        /// <summary>
        /// 菜单按钮API权限
        /// </summary>
        /// <param name="moduleId"></param>
        /// <param name="getType">1,缓存数据，2,表数据</param>
        /// <returns></returns>
        MenuButtonApiDto GetMenuButtonApi(string moduleId, EnumBaseDataGetType getType);


        /// <summary>
        /// 账号信息
        /// </summary>
        /// <param name="hospitalId">医疗机构ID</param>
        /// <param name="getType">1,缓存数据，2,表数据</param>
        /// <returns></returns>
        AccountInfoDto AccountInfo(string hospitalId, EnumBaseDataGetType getType);

        /// <summary>
        /// 获取设置值
        /// </summary>
        /// <param name="moduleId">模块ID</param>
        /// <param name="unitId">单元ID</param>
        /// <param name="unitType">单元类型</param>
        /// <param name="setupClass">设置分类</param>
        /// <param name="hospitalId">医疗机构ID</param>
        /// <param name="setupNo">设置ID</param>
        /// <param name="getType">1,缓存数据，2,表数据</param>
        /// <returns></returns>
        List<Lis5SetupDictCacheDto> GetLis5SetupDicts(string moduleId, string hospitalId, string unitId,
           string unitType, string setupClass, string setupNo, EnumBaseDataGetType getType);

        ResultDto GetTableMax(string tableName, string fieldName = "", int addCount = 1, int ifTableMax = 1, string dBOwner = "XH_OA");
        ResultDto UploadPathFile(string jsonStr, string serviceUrl);
        ResultDto UploadPathFormDataFile(string doc_folder, string if_cover, string doc_name, byte[] bytes);
        string UploadToS28(string doc_folder, string if_cover, string doc_name, byte[] bytes);

        /// <summary>
        /// 无账号体系token上传S28
        /// </summary>
        /// <param name="doc_folder"></param>
        /// <param name="if_cover"></param>
        /// <param name="doc_name"></param>
        /// <param name="bytes"></param>
        /// <param name="token"></param>
        /// <returns></returns>
        /// <exception cref="BizException"></exception>
        string UploadToS28ByToken(string doc_folder, string if_cover, string doc_name, byte[] bytes , string token);

        ResultDto GetCustomToken(ThirdVerifyInfoDto infoDto);

        /// <summary>
        /// 无账号体系外部模块信息查询
        /// </summary>
        /// <param name="moduleId"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        ThirdVerifyInfoDto GetCustomTokenInfo(string moduleId);
    }
}
