using H.Utility;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace XH.H82.Models.Dtos;

public class EntranceEquipment
{
    public string EQUIPMENT_JSON { get; set; } = "";
    /// <summary>
    /// 门禁进出方向
    /// </summary>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    public int IsIn {
        get
        {
            if (EQUIPMENT_JSON.IsNullOrEmpty())
            {
                return IsIn;
            }
            else
            {
                var jObj = JObject.Parse(EQUIPMENT_JSON!);
                var result = jObj["IS_IN"]!.Value<int>();
                return result;
            }
        }
        set
        {
            var input = new
            {
                IS_IN = value
            };
            EQUIPMENT_JSON = JsonConvert.SerializeObject(input);
        }
    }
}