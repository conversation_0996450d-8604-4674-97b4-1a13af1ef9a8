﻿using SqlSugar;

namespace XH.H82.Models;

/// <summary>
/// 被产生事件记录模型
/// </summary>
public interface IBeRecordEvent : IRecord
{
    public string? RELATION_EVENT { get; set;}
}

public interface  IRecord
{
    /// <summary>
    /// 记录id
    /// </summary>
    public string GetId();

    public string GetEquipmentId();
    /// <summary>
    /// 记录类型
    /// </summary>
    /// <returns></returns>
    public (string Type , string TypeName ) GetType();

    /// <summary>
    /// 记录时间
    /// </summary>
    /// <returns></returns>
    public DateTime? GetRecordDate();
}