﻿using Yarp.ReverseProxy.Transforms;

namespace XH.H82.API.Extensions.YARP
{
    public static class CustomReverseProxyExt
    {
        public static IServiceCollection AddCustomReverseProxy(this IServiceCollection service, ProxyConfig proxyConfig)
        {
            service.AddReverseProxy().ConfigureHttpClient((context, handle) =>
            {
                handle.SslOptions.RemoteCertificateValidationCallback = (a, v, b, q) => true;
            })
            .LoadFromMemory(proxyConfig.GetRoutes(), proxyConfig.GetClusters())
            .AddTransforms((x) =>
            {
                x.CopyRequestHeaders = true;
                x.CopyResponseHeaders = true;
                x.CopyResponseTrailers = true;
                foreach (var route in proxyConfig.Routes)
                {
                    x.AddPathRemovePrefix($"/{route}");
                }
            }
            );
            return service;
        }
    }


}
