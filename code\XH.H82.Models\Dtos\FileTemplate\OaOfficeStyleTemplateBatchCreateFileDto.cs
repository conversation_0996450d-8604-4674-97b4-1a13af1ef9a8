﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.Dtos.FileTemplate
{
    /// <summary>
    /// OnlyOffice模版样式批量添加  (多个模板信息+单个文件)
    /// </summary>
    public class OaOfficeStyleTemplateBatchCreateFileDto : OOFileConvertPdfDto
    {
        public List<OaOfficeStyleTemplateBatchCreateDto> STYLE_INFOS { get; set; }

    }
}
