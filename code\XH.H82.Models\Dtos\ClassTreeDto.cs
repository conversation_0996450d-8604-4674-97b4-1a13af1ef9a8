﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.Dtos
{
    public class ClassTreeDto
    {
        public int TOTAL_PlAN_AMOUNT { get; set; }
        public int TOTAL_SCRAP_AMOUNT { get; set; }
        public int SELF_PEND { get; set; }
        public List<SECOND_CLASS_NODE> SECOND_CLASS_NODE { get; set; }
        
    }
    public class SECOND_CLASS_NODE
    {
        public string EQUIPMENT_CLASS { get; set; }
        public string EQUIPMENT_CLASS_ID { get; set; }
        public int PLAN_AMOUNT { get; set; }
        public int SCRAP_AMOUNT { get; set; }
    }
}
