﻿using System.ComponentModel.DataAnnotations;
using H.Utility;
using H.Utility.SqlSugarInfra;
using SqlSugar;
using DbType = System.Data.DbType;

namespace XH.H82.Models.Entities.PMS
{
    [DBOwner("XH_OA")]
    public class PMS_PERSON_INFO
    {
        /// <summary>
        ///人员ID
        /// </summary>
        [Key]
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = DbType.AnsiString)]
        public string PERSON_ID { get; set; }
        /// <summary>
        /// 科室ID
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? LAB_ID { get; set; }
        /// <summary>
        ///管理单元ID
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? PGROUP_ID { get; set; }
        /// <summary>
        ///管理单元名称
        /// </summary>
        [SugarColumn(IsIgnore = true, SqlParameterDbType = DbType.AnsiString)]
        public string? PGROUP_NAME { get; set; }
        /// <summary>
        ///医疗机构ID
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? HOSPITAL_ID { get; set; }
        /// <summary>
        ///账号ID
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? USER_ID { get; set; }

        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? LOGID { get; set; }
        /// <summary>
        ///人员类型
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        //1-正式员工2-合同职工3-返聘职工4-实习人数5-进修人数6-规培人数7-临聘人数
        public string? USER_TYPE { get; set; }
        /// <summary>
        ///姓名
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? USER_NAME { get; set; }
        /// <summary>
        /// 英文名
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? USER_ENAME { get; set; }
        /// <summary>
        ///性别
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? SEX { get; set; }
        /// <summary>
        ///年龄
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? AGE { get; set; }
        /// <summary>
        ///出生年月
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? BIRTHDAY { get; set; }
        /// <summary>
        ///民族
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? NATION { get; set; }
        /// <summary>
        ///籍贯
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? NATIVE_PLACE { get; set; }
        /// <summary>
        ///政治面貌
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? POLITICIAN { get; set; }
        /// <summary>
        ///毕业专业
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? PROFESSION { get; set; }
        /// <summary>
        ///最高学历
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? HIGHEST_DEGREE { get; set; }
        /// <summary>
        ///学历取得时间
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? DEGREE_TIME { get; set; }
        /// <summary>
        ///最高学位
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? HIGHEST_DIPLOMA { get; set; }
        /// <summary>
        ///学位取得时间
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? DIPLOMA_TIME { get; set; }
        /// <summary>
        ///参加工作时间
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? WORK_TIME { get; set; }
        /// <summary>
        ///连续工龄
        /// </summary>
        public int? LENGTH_SERVICE { get; set; } = 0;
        /// <summary>
        ///进院日期
        /// </summary>
        public DateTime? IN_HOSPITAL_DATE { get; set; }
        /// <summary>
        ///院龄
        /// </summary>
        public int? LENGTH_HOSPITAL { get; set; } = 0;
        /// <summary>
        ///行政职务
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? DUTIES { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = DbType.AnsiString)]
        public string? DUTIES_NAME { get; set; }
        /// <summary>
        ///职称级别
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? TECH_POST { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = DbType.AnsiString)]
        public string? TECH_POST_NAME { get; set; }
        /// <summary>
        ///职称名称 对应系统数据管理USER表的TECH_POST字段
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? ACADEMIC_POST { get; set; }

        [SugarColumn(IsIgnore = true, SqlParameterDbType = DbType.AnsiString)]
        public string? ACADEMIC_POST_NAME { get; set; }
        /// <summary>
        ///通信地址
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? COMM_ADDR { get; set; }
        /// <summary>
        ///家庭电话
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? HOME_TEL { get; set; }
        /// <summary>
        ///手机
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? PHONE { get; set; }
        /// <summary>
        ///短号
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? CORNET { get; set; }
        /// <summary>
        ///出生地
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? BIRTH_PLACE { get; set; }
        /// <summary>
        ///身高
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? HEIGHT { get; set; }
        /// <summary>
        ///视力
        /// </summary>
        public string? EYESIGHT { get; set; }
        /// <summary>
        ///左视力
        /// </summary>
        [SugarColumn(IsIgnore = true)] //TODO
        public string? EYESIGHT_LEFT
        { 
            get { return EYESIGHT.IsNullOrEmpty() ? "" : EYESIGHT.Split('/')[0]; }
            set { EYESIGHT = $"{value}/{EYESIGHT_RIGHT}"; }
        }
        /// <summary>
        ///右视力
        /// </summary>
        [SugarColumn(IsIgnore = true)] //TODO
        public string? EYESIGHT_RIGHT
        {
            get { return EYESIGHT.IsNullOrEmpty() || !EYESIGHT.Contains('/') ? "" : EYESIGHT.Split('/')[1]; }
            set { EYESIGHT = $"{EYESIGHT_LEFT}/{value}"; }
        }
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? ENGLISH_RANK { get; set; }
        /// <summary>
        ///英语等级成绩
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? ENGLISH_RANK_SCORE { get; set; }
        /// <summary>
        ///婚姻状况
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? MARITAL_STATUS { get; set; }
        /// <summary>
        ///有无子女
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? CHILDREN_CONDITION { get; set; }
        /// <summary>
        ///证件类型
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? CARD_TYPE { get; set; }
        /// <summary>
        ///身份证
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? ID_CARD { get; set; }
        /// <summary>
        ///户籍所在地
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? DOMICILE_PLACE { get; set; }
        /// <summary>
        ///紧急联系人
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? EMERGENCY_CONTACT { get; set; }
        /// <summary>
        ///与紧急联系人的关系
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? ECONTACT_RELACTION { get; set; }
        /// <summary>
        ///紧急联系人电话
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? ECONTACT_PHONE { get; set; }
        /// <summary>
        ///现居住地
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? CURRENT_ADDRESS { get; set; }
        /// <summary>
        ///健康状况
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? HEALTH { get; set; }
        /// <summary>
        ///邮箱
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? E_MAIL { get; set; }
        /// <summary>
        ///办公电话
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? OFFICE_PHONE { get; set; }
        /// <summary>
        ///聘任职称评定单位
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? EMPLOYMENT_UNIT { get; set; }
        /// <summary>
        ///职称类型
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? TECHNOLOGY_TYPE { get; set; }
        /// <summary>
        ///专业技术资格取得时间
        /// </summary>
        public DateTime? TECH_CERTIFICE_TIME { get; set; }
        /// <summary>
        ///职称专业
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? TECH_POST_PROFESSION { get; set; }
        /// <summary>
        ///招聘来源
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? EMPLOYMENT_SOURE { get; set; }
        /// <summary>
        ///专业特长
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? PROFESSION_EXPERTISE { get; set; }
        /// <summary>
        ///聘用时间
        /// </summary>
        public DateTime? EMPLOY_TIME { get; set; }
        /// <summary>
        ///退休时间
        /// </summary>
        public DateTime? RETIRE_TIME { get; set; }
        /// <summary>
        ///来院时间
        /// </summary>
        public DateTime? IN_HOSPITAL_TIME { get; set; }
        /// <summary>
        ///离院时间
        /// </summary>
        public DateTime? OUT_HOSPITAL_TIME { get; set; }
        /// <summary>
        ///返聘时间
        /// </summary>
        public DateTime? REEMPLOY_TIME { get; set; }
        /// <summary>
        ///登记模式
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? REGISTER_MODE { get; set; }
        /// <summary>
        ///登记人员
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? REGISTER_PERSON { get; set; }
        /// <summary>
        ///登记时间
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? REGISTER_TIME { get; set; }
        /// <summary>
        ///提交人员
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? SUBMIT_PERSON { get; set; }
        /// <summary>
        ///提交时间
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? SUBMIT_TIME { get; set; }
        /// <summary>
        ///审核人员
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? CHECK_PERSON { get; set; }
        /// <summary>
        ///审核时间
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? CHECK_TIME { get; set; }
        /// <summary>
        ///审核电脑
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? CHECK_COMPUTER { get; set; }
        /// <summary>
        ///放置场所
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? DOC_PLACE { get; set; }
        /// <summary>
        ///人员性质
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? PERSON_DOC_STATE { get; set; }
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? PERSON_PHOTO_PATH { get; set; }
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? IN_LAB_TIME { get; set; }//来科日期
        public int LENGTH_LAB { get; set; } = 0;//科龄
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? OUT_LAB_TIME { get; set; }//离科日期

        /// <summary>
        ///状态
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? PERSON_STATE { get; set; }
        /// <summary>
        ///首次登记人
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? FIRST_RPERSON { get; set; }
        /// <summary>
        ///首次登记时间
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? FIRST_RTIME { get; set; }
        /// <summary>
        ///最后修改人员
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? LAST_MPERSON { get; set; }
        /// <summary>
        ///最后修改时间
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? LAST_MTIME { get; set; }
        /// <summary>
        ///备注
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? REMARK { get; set; }
        /// <summary>
        /// 工号
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? HIS_ID { get; set; }
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? IF_EMPLOYMENT { get; set; }
        /// <summary>
        ///毕业院校
        /// </summary>
        public string? GRADUATE_SCHOOL { get; set; } 
        /// <summary>
        ///毕业日期
        /// </summary>
        public DateTime? GRADUATE_DATE { get; set; }

        /// <summary>
        ///颜色视觉障碍（0-正常  1-色弱 2-色盲）
        /// </summary>
        public string? COLOR_DEFICIENCY { get; set; }

        /// <summary>
        /// 扩展字段
        /// </summary>
        [SugarColumn(SqlParameterDbType = DbType.AnsiString)]
        public string? RECORD_DATA { get; set; }
    }
}
