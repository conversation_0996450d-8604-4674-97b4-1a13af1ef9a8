﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.Dtos.Base
{
    public class MenuDto
    {
        public string MENU_ID { get; set; }
        public string MENU_NAME { get; set; }
        public string MODULE_ID { get; set; }
        public string MENU_LEVEL { get; set; }
        public string ICON_NAME { get; set; }
        public string MENU_CLASS { get; set; }
        public string MENU_URL { get; set; }
        public string MENU_SORT { get; set; }
        public string PARENT_MENU_ID { get; set; }
        public string MENU_STATE { get; set; }
        public string FIRST_RPERSON { get; set; }
        public DateTime? FIRST_RTIME { get; set; }
        public string LAST_MPERSON { get; set; }
        public DateTime? LAST_MTIME { get; set; }
        public string HOSPITAL_ID { get; set; }
    }
}
