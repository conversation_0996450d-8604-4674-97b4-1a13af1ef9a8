using H.Utility;
using H.Utility.SqlSugarInfra;
using Microsoft.AspNetCore.Http;
using SqlSugar;
using XH.H82.IServices;
using XH.H82.Models.Dtos;
using XH.H82.Models.Dtos.EquipmentAdditionalRecord;
using XH.H82.Models.Entities;
using XH.H82.Models.EquipmengtClassNew;

namespace XH.H82.Services.EquipmentAdditionalRecord
{
    /// <summary>
    /// 设备扩展记录服务实现
    /// </summary>
    public class EquipmentAdditionalRecordService : IEquipmentAdditionalRecordService
    {
        private readonly ISqlSugarUow<SugarDbContext_Master> _dbContext;
        private readonly IHttpContextAccessor _httpContext;
        private readonly ClaimsDto_Local _user;

        public EquipmentAdditionalRecordService(
            ISqlSugarUow<SugarDbContext_Master> dbContext,
            IHttpContextAccessor httpContext)
        {
            _dbContext = dbContext;
            _httpContext = httpContext;
            _user = _httpContext.HttpContext?.Items["UserInfo"] as ClaimsDto_Local ?? new ClaimsDto_Local();
        }

        /// <summary>
        /// 根据设备ID和档案记录ID获取设备扩展记录
        /// </summary>
        public EMS_EQP_ADDN_RECORD? GetEquipmentAdditionalRecord(string equipmentId, string eqpArchivesId)
        {
            return _dbContext.Db.Queryable<EMS_EQP_ADDN_RECORD>()
                .Where(x => x.EquipmentId == equipmentId && x.EqpArchivesId == eqpArchivesId)
                .Where(x => x.EqpRecordState != "2") // 排除已删除的记录
                .First();
        }

        /// <summary>
        /// 根据设备ID获取所有扩展记录
        /// </summary>
        public List<EMS_EQP_ADDN_RECORD> GetEquipmentAdditionalRecords(string equipmentId)
        {
            return _dbContext.Db.Queryable<EMS_EQP_ADDN_RECORD>()
                .Where(x => x.EquipmentId == equipmentId)
                .Where(x => x.EqpRecordState != "2") // 排除已删除的记录
                .OrderBy(x => x.EqpRecordSort)
                .ToList();
        }

        /// <summary>
        /// 获取设备的档案记录详细信息（包含扩展字段）
        /// </summary>
        public EquipmentArchiveDetailDto GetEquipmentArchiveDetail(string equipmentId, string eqpArchivesId)
        {
            // 获取档案记录字典信息
            var archiveDict = _dbContext.Db.Queryable<EMS_EQP_ARCHIVES_DICT>()
                .Where(x => x.EqpArchivesId == eqpArchivesId)
                .Where(x => x.EqpArchivesState == "1") // 只查询在用状态
                .First();

            if (archiveDict == null)
            {
                throw new ArgumentException($"档案记录ID {eqpArchivesId} 不存在或已禁用");
            }

            // 获取父级名称
            string? parentName = null;
            if (!string.IsNullOrEmpty(archiveDict.EqpArchivesPid) && archiveDict.EqpArchivesPid != "0")
            {
                var parentDict = _dbContext.Db.Queryable<EMS_EQP_ARCHIVES_DICT>()
                    .Where(x => x.EqpArchivesId == archiveDict.EqpArchivesPid)
                    .First();
                parentName = parentDict?.EqpArchivesName;
            }

            // 获取设备扩展记录
            var additionalRecord = GetEquipmentAdditionalRecord(equipmentId, eqpArchivesId);

            // 获取对应的业务数据
            var businessData = GetBusinessDataByArchiveId(equipmentId, eqpArchivesId);

            var result = new EquipmentArchiveDetailDto
            {
                EqpArchivesId = archiveDict.EqpArchivesId,
                EqpArchivesName = archiveDict.EqpArchivesName,
                EqpArchivesType = archiveDict.EqpArchivesType,
                EqpArchivesPid = archiveDict.EqpArchivesPid,
                EqpArchivesPName = parentName,
                EqpArchivesSort = archiveDict.EqpArchivesSort,
                EqpArchivesState = archiveDict.EqpArchivesState,
                FormSetupId = archiveDict.FormSetupId,
                TableSetupId = archiveDict.TableSetupId,
                EqpArchivesJson = archiveDict.EqpArchivesJson,
                AdditionalRecord = additionalRecord,
                BusinessData = businessData.data,
                BusinessDataTypeName = businessData.typeName
            };

            return result;
        }

        /// <summary>
        /// 获取设备的所有档案记录详细信息列表
        /// </summary>
        public List<EquipmentArchiveDetailDto> GetEquipmentArchiveDetails(string equipmentId)
        {
            // 获取所有在用的档案记录字典
            var archiveDicts = _dbContext.Db.Queryable<EMS_EQP_ARCHIVES_DICT>()
                .Where(x => x.EqpArchivesState == "1") // 只查询在用状态
                .OrderBy(x => x.EqpArchivesSort)
                .ToList();

            var result = new List<EquipmentArchiveDetailDto>();

            foreach (var archiveDict in archiveDicts)
            {
                try
                {
                    var detail = GetEquipmentArchiveDetail(equipmentId, archiveDict.EqpArchivesId);
                    result.Add(detail);
                }
                catch (Exception)
                {
                    // 如果某个档案记录获取失败，继续处理其他记录
                    continue;
                }
            }

            return result;
        }

        /// <summary>
        /// 保存或更新设备扩展记录
        /// </summary>
        public ResultDto SaveEquipmentAdditionalRecord(EMS_EQP_ADDN_RECORD record)
        {
            try
            {
                // 验证设备是否存在
                var equipment = _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>()
                    .Where(x => x.EQUIPMENT_ID == record.EquipmentId)
                    .First();

                if (equipment == null)
                {
                    return new ResultDto { success = false, msg = "设备不存在" };
                }

                // 验证档案记录是否存在
                var archiveDict = _dbContext.Db.Queryable<EMS_EQP_ARCHIVES_DICT>()
                    .Where(x => x.EqpArchivesId == record.EqpArchivesId)
                    .Where(x => x.EqpArchivesState == "1")
                    .First();

                if (archiveDict == null)
                {
                    return new ResultDto { success = false, msg = "档案记录不存在或已禁用" };
                }

                var now = DateTime.Now;
                var userName = _user.USER_NAME ?? "系统";

                // 检查是否已存在记录
                var existingRecord = _dbContext.Db.Queryable<EMS_EQP_ADDN_RECORD>()
                    .Where(x => x.EquipmentId == record.EquipmentId && x.EqpArchivesId == record.EqpArchivesId)
                    .Where(x => x.EqpRecordState != "2")
                    .First();

                if (existingRecord != null)
                {
                    // 更新现有记录
                    existingRecord.EquitmentJson = record.EquitmentJson;
                    existingRecord.EqpRecordAffix = record.EqpRecordAffix;
                    existingRecord.EqpRecordSort = record.EqpRecordSort;
                    existingRecord.EqpRecordState = record.EqpRecordState ?? "1";
                    existingRecord.LAST_MPERSON = userName;
                    existingRecord.LAST_MTIME = now;
                    existingRecord.Remark = record.Remark;

                    _dbContext.Db.Updateable(existingRecord)
                        .IgnoreColumns(ignoreAllNullColumns: true)
                        .ExecuteCommand();
                }
                else
                {
                    // 创建新记录
                    record.EqpRecordId = IDGenHelper.CreateGuid();
                    record.HospitalId = _user.HOSPITAL_ID ?? "H0000";
                    record.EqpRecordState = record.EqpRecordState ?? "1";
                    record.FIRST_RPERSON = userName;
                    record.FIRST_RTIME = now;
                    record.LAST_MPERSON = userName;
                    record.LAST_MTIME = now;

                    _dbContext.Db.Insertable(record).ExecuteCommand();
                }

                return new ResultDto { success = true, msg = "保存成功" };
            }
            catch (Exception ex)
            {
                return new ResultDto { success = false, msg = $"保存失败：{ex.Message}" };
            }
        }

        /// <summary>
        /// 删除设备扩展记录
        /// </summary>
        public ResultDto DeleteEquipmentAdditionalRecord(string eqpRecordId)
        {
            try
            {
                var record = _dbContext.Db.Queryable<EMS_EQP_ADDN_RECORD>()
                    .Where(x => x.EqpRecordId == eqpRecordId)
                    .First();

                if (record == null)
                {
                    return new ResultDto { success = false, msg = "记录不存在" };
                }

                // 软删除：设置状态为删除
                record.EqpRecordState = "2";
                record.LAST_MPERSON = _user.USER_NAME ?? "系统";
                record.LAST_MTIME = DateTime.Now;

                _dbContext.Db.Updateable(record)
                    .UpdateColumns(x => new { x.EqpRecordState, x.LAST_MPERSON, x.LAST_MTIME })
                    .ExecuteCommand();

                return new ResultDto { success = true, msg = "删除成功" };
            }
            catch (Exception ex)
            {
                return new ResultDto { success = false, msg = $"删除失败：{ex.Message}" };
            }
        }

        /// <summary>
        /// 根据档案记录ID获取对应的业务数据
        /// </summary>
        private (object? data, string? typeName) GetBusinessDataByArchiveId(string equipmentId, string eqpArchivesId)
        {
            // 根据不同的档案记录ID查询对应的业务表数据
            // 这里需要根据实际的档案记录ID映射到对应的业务表
            return eqpArchivesId switch
            {
                // 校准记录
                "H82JZJLXX" or var id when id.Contains("校准") => 
                    GetCorrectInfo(equipmentId),
                
                // 调试记录  
                "H82TSJLXX" or var id when id.Contains("调试") => 
                    GetDebugInfo(equipmentId),
                
                // 培训记录
                "H82PXJLXX" or var id when id.Contains("培训") => 
                    GetTrainInfo(equipmentId),
                
                // 默认返回空
                _ => (null, null)
            };
        }

        /// <summary>
        /// 获取校准记录
        /// </summary>
        private (List<EMS_CORRECT_INFO>? data, string typeName) GetCorrectInfo(string equipmentId)
        {
            var data = _dbContext.Db.Queryable<EMS_CORRECT_INFO>()
                .Where(x => x.EQUIPMENT_ID == equipmentId)
                .Where(x => x.CORRECT_STATE == "1")
                .OrderByDescending(x => x.CORRECT_DATE)
                .ToList();
            
            return (data, "EMS_CORRECT_INFO");
        }

        /// <summary>
        /// 获取调试记录
        /// </summary>
        private (List<EMS_DEBUG_INFO>? data, string typeName) GetDebugInfo(string equipmentId)
        {
            var data = _dbContext.Db.Queryable<EMS_DEBUG_INFO>()
                .Where(x => x.EQUIPMENT_ID == equipmentId)
                .Where(x => x.DEBUG_STATE == "1")
                .OrderByDescending(x => x.DEBUG_DATE)
                .ToList();
            
            return (data, "EMS_DEBUG_INFO");
        }

        /// <summary>
        /// 获取培训记录
        /// </summary>
        private (List<EMS_TRAIN_INFO>? data, string typeName) GetTrainInfo(string equipmentId)
        {
            var data = _dbContext.Db.Queryable<EMS_TRAIN_INFO>()
                .Where(x => x.EQUIPMENT_ID == equipmentId)
                .OrderByDescending(x => x.TRAIN_TIME)
                .ToList();
            
            return (data, "EMS_TRAIN_INFO");
        }
    }
}
