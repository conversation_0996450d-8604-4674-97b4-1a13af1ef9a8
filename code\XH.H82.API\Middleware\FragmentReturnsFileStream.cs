﻿using Microsoft.Net.Http.Headers;

namespace XH.H82.API.Middleware
{
    public class FragmentReturnsFileStream
    {
        public HttpContext Context { get; set; }
        public HttpRequest Request { get; set; }
        public HttpResponse Response { get; set; }


        /// <summary>
        /// 构造
        /// </summary>
        /// <param name="ctx"></param>
        public FragmentReturnsFileStream(HttpContext ctx)
        {
            Context = ctx;
            Request = ctx.Request;
            Response = ctx.Response;
        }


        /// <summary>
        /// 分流截取文件流
        /// </summary>
        /// <param name="stream">文件流</param>
        /// <param name="fromIndex">开始下标</param>
        /// <param name="toIndex">结束下标</param>
        /// <returns></returns>
        public MemoryStream WriteToMemoryStream(Stream stream, long fromIndex, long toIndex)
        {
            using MemoryStream memoryStream = new MemoryStream();
            byte[] buffer = new byte[toIndex - fromIndex + 1];
            //跳过开始下标之前的字节数，按byte块读取文件流
            stream.Seek(fromIndex, SeekOrigin.Begin);
            stream.Read(buffer, 0, buffer.Length);
            memoryStream.Write(buffer, 0, buffer.Length);
            memoryStream.Position = 0;
            return memoryStream;
        }


        /// <summary>
        /// 设置pdf流下载请求头为range
        /// </summary>
        /// <param name="isRange">是否开启range协议</param>
        /// <param name="fromIndex">开始下标</param>
        /// <param name="toIndex">结束下标</param>
        /// <param name="fileLength">总长度</param>
        public void SetPdfDownloadHeaders(bool isRange, long fromIndex, long toIndex, long fileLength)
        {
            //以下请求头均为开启range协议设置
            Response.Headers.Add("Access-Control-Expose-Headers",
                HeaderNames.ContentRange + "," + HeaderNames.AcceptRanges + "," + HeaderNames.ContentEncoding + "," + HeaderNames.ContentLength);

            Response.Headers[HeaderNames.AcceptRanges] = "bytes";
            Response.Headers[HeaderNames.ContentType] = Context.Response.ContentType;
            Response.Headers[HeaderNames.ContentLength] = $"{Context.Response.ContentLength}";

            //206影响为请求部分成功，需继续获取资源
            if (isRange)
            {
                Response.StatusCode = StatusCodes.Status206PartialContent;
                Response.Headers[HeaderNames.ContentLength] = (toIndex - fromIndex + 1).ToString(); //本次响应大小
                Response.Headers[HeaderNames.ContentRange] = new ContentRangeHeaderValue(fromIndex, toIndex, fileLength).ToString();//range协议大小 bytes= 0-1024/10240
            }
            else
            {
                Response.StatusCode = StatusCodes.Status200OK;
            }
        }

        /// <summary>
        /// 尝试从请求头解析是否有range参数
        /// </summary>
        /// <param name="request">请求</param>
        /// <param name="fileLength">文件总长度</param>
        /// <param name="indexes"> FromIndex 开始的下标   ToIndex结束的下标 </param>
        /// <returns></returns>
        public bool TryAnalyseRange(HttpRequest request, long fileLength, out (long FromIndex, long ToIndex) indexes)
        {
            var requestHeaders = Request.GetTypedHeaders();
            //判断本次请求有没有range参数
            if (requestHeaders.Range != null && requestHeaders.Range.Ranges.Count > 0)
            {
                var range = requestHeaders.Range.Ranges.FirstOrDefault();
                indexes.FromIndex = range.From ?? 0L;
                indexes.ToIndex = range.To ?? 0L;
                //判断结束的下标是否超出文件总长度
                if (indexes.ToIndex > fileLength - 1)
                {
                    //结束下标等于文件最大长度
                    indexes.ToIndex = fileLength - 1;
                }

                return true;
            }
            else
            {
                //不含有range参数设置默认值
                indexes.FromIndex = 0L;
                indexes.ToIndex = 0L;

                return false;
            }
        }

    }
}
