﻿using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Serilog;
using SqlSugar;
using XH.H82.IServices.DeviceDataRefresh;
using XH.H82.Models.BusinessModuleClient;
using XH.H82.Models.Card;
using XH.H82.Models.DeviceRelevantInformation;
using XH.H82.Models.DeviceRelevantInformation.Dto;
using XH.H82.Models.DeviceRelevantInformation.Enum;
using XH.H82.Models.Entities.InkScreen;
using XH.H82.Models.SugarDbContext;
using XH.LAB.UTILS.Helpers;
using XH.LAB.UTILS.Models;

namespace XH.H82.Services.DeviceDataRefresh
{
    public class InkScreenService : IInkScreenService
    {

        EquipmentContext equipmentContext;
        private readonly ISqlSugarUow<SugarDbContext_Master> _dbContext;
        private ClaimsDto _user;
        private H115Client _h115Client;
        private IConfiguration _configuration;
        public InkScreenService(ISqlSugarUow<SugarDbContext_Master> dbContext, IHttpContextAccessor httpContext, IConfiguration configuration)
        {
            _configuration = configuration;
            _h115Client = new H115Client(configuration["H115"], httpContext);
            //_h115Client = new H115Client("https://***********:18515", httpContext);

            _user = httpContext.HttpContext.User.ToClaimsDto();
            _dbContext = dbContext;
            _dbContext.SetCreateTimeAndCreatePersonData(_user);
            equipmentContext = new(_dbContext);
        }

        void IInkScreenService.AddNewInkScreen(string mac, string inkScreenName, string hosptialId, string? remark)
        {

            if (_dbContext.Db.Queryable<EMS_INKSCREEN_INFO>().Count(x => x.MAC == mac) > 0)
            {
                throw new BizException("已有相同的mac地址，请修改后重新添加");
            }

            var screenInfo = new EMS_INKSCREEN_INFO()
            {
                INKSCREEN_ID = IDGenHelper.CreateGuid(),
                MAC = mac,
                INKSCREEN_NAME = inkScreenName,
                HOSPITAL_ID = hosptialId,
                INKSCREEN_SORT = mac,
                ONLINE_STATE = LineStateEnum.OnLine,
                LIGHT_STATE = LightStateEnum.lightsOut,
                POWER = 100,
                POWER_THRESHOLD = 20,
            };
            _dbContext.Db.Insertable(screenInfo).ExecuteCommand();
        }
        void IInkScreenService.BandEquipment(string inkScreenId, string equipmentId)
        {
            var inkScreen = _dbContext.Db.Queryable<EMS_INKSCREEN_INFO>().First(x => x.INKSCREEN_ID == inkScreenId) ?? throw new BizException($"{inkScreenId}的墨水屏不存在，请刷新！");
            inkScreen.EQUIPMENT_ID = equipmentId;
            _dbContext.Db.Updateable(inkScreen).IgnoreColumns(ignoreAllNullColumns:true).ExecuteCommand();


            if (_configuration.GetSection("IsAutoRefresh").Exists())
            {
                if (_configuration.GetSection("IsAutoRefresh").Value == "1")
                {
                    equipmentContext.Injection(equipmentId);
                    var cardInfo = equipmentContext.ExchangeIdentificationCard(equipmentId);

                    if (cardInfo.equipmentName.IsNullOrEmpty())
                    {
                        Log.Error(JsonConvert.SerializeObject(cardInfo));
                        return;
                    }

                    var rsp = _h115Client.ChangeDisplay(cardInfo.ToEinkChangeDisplay(inkScreen.MAC));
                    if (!rsp.success)
                    {
                        throw new BizException($"墨水屏设备{inkScreen.INKSCREEN_NAME}已断开链接，连线后自动恢复！");
                    }
                }
            }


        }
        void IInkScreenService.DeleteInkScreen(string InkScreenId)
        {
            _dbContext.Db.Deleteable<EMS_INKSCREEN_INFO>().Where(x => x.INKSCREEN_ID == InkScreenId).ExecuteCommand();
        }
        void IInkScreenService.UpdateInkScreenInfo(string inkScreenId, string? remark)
        {
            var inkScreen = _dbContext.Db.Queryable<EMS_INKSCREEN_INFO>().First(x => x.INKSCREEN_ID == inkScreenId);
            if (inkScreen is null)
            {
                throw new BizException($"{inkScreenId}的墨水屏不存在，请刷新！");
            }
            inkScreen.REMARK = remark;
            _dbContext.Db.Updateable(inkScreen).IgnoreColumns(ignoreAllNullColumns:true);
            _dbContext.SaveChanges();
        }
        public void CheckPower()
        {
            var inkScreens = _dbContext.Db.Queryable<EMS_INKSCREEN_INFO>().ToList();
            if (inkScreens is null)
            {
                throw new BizException($"当前不存在墨水屏设备，请添加后重试！");
            }
            if (inkScreens.Count() == 0)
            {
                throw new BizException($"当前不存在墨水屏设备，请添加后重试！");
            }

            if (inkScreens[0].OPER_TIME.HasValue)
            {
                var checkInterval = DateTime.Now - inkScreens[0].OPER_TIME;
                //if (checkInterval.Value.TotalHours < 12)
                //{
                //    throw new BizException($"12小时内不能再次获取电量!");
                //}
                if (checkInterval.Value.TotalSeconds < 30)
                {
                    throw new BizException($"12小时内不能再次获取电量!");
                }
            }

            if (_configuration.GetSection("IsAutoRefresh").Exists())
            {
                if (_configuration.GetSection("IsAutoRefresh").Value == "1")
                {
                    UpdatePower(inkScreens);
                }
                else
                {
                    UpdatePowerNotUseH115(inkScreens);
                }
            }
            else
            {
                UpdatePowerNotUseH115(inkScreens);
            }


        }

        /// <summary>
        /// 实时监控墨水屏设备
        /// </summary>
        /// <param name="inkScreens"></param>
        public void RealTimeMonitoring()
        {

            var inkScreens = _dbContext.Db.Queryable<EMS_INKSCREEN_INFO>()
            .ToList();

            if (inkScreens.Count() == 0)
            {
                return;
            }
            var devices = _h115Client.QueryDevice();
            if (devices is null)
            {
                return;
            }
            if (devices.Count() == 0)
            {
                return;
            }
            foreach (var inkScreen in inkScreens)
            {
                var device = devices.Where(x => x.mac == inkScreen.MAC).FirstOrDefault();
                if (device is null)
                {
                    continue;
                }
                inkScreen.POWER = device.voltagePct;
                inkScreen.ONLINE_STATE = device.status ? LineStateEnum.OnLine : LineStateEnum.OutLine;
                inkScreen.LIGHT_STATE = device.redLightStatus ? LightStateEnum.Lighting : LightStateEnum.lightsOut;
                inkScreen.INKSCREEN_NAME = device.alias;
                inkScreen.DELIVERY = device.images is null ? "" : device.images.imageFile;
            }
            _dbContext.Db.Updateable(inkScreens).IgnoreColumns(ignoreAllNullColumns:true).ExecuteCommand();

        }

        public void UpdatePower(List<EMS_INKSCREEN_INFO> inkScreens)
        {
            var devices = _h115Client.QueryDevice();
            if (devices is null)
            {
                return;
            }
            if (devices.Count() == 0)
            {
                return;
            }

            foreach (var inkScreen in inkScreens)
            {
                var device = devices.Where(x => x.mac == inkScreen.MAC).FirstOrDefault();
                if (device is null)
                {
                    continue;
                }
                inkScreen.POWER = device.voltagePct;
                inkScreen.ONLINE_STATE = device.status ? LineStateEnum.OnLine : LineStateEnum.OutLine;
                inkScreen.LIGHT_STATE = device.redLightStatus ? LightStateEnum.Lighting : LightStateEnum.lightsOut;
                inkScreen.INKSCREEN_NAME = device.alias;
                inkScreen.OPER_PERSON = _user.HIS_NAME;
                inkScreen.OPER_TIME = DateTime.Now;
            }
            _dbContext.Db.Updateable(inkScreens).ExecuteCommand();
        }

        public void UpdatePowerNotUseH115(List<EMS_INKSCREEN_INFO> inkScreens)
        {
            foreach (var inkScreen in inkScreens)
            {
                inkScreen.OPER_PERSON = _user.HIS_NAME;
                inkScreen.OPER_TIME = DateTime.Now;
            }
            _dbContext.Db.Updateable(inkScreens).ExecuteCommand();
        }


        public List<InkScreenBindDto> GetInkScreenBindEquipments(string hosptialId, bool? isBand, string? pGourpId, string? mac)
        {

            Log.Information($"设备绑定开始...");
            var inkScreens = _dbContext.Db.Queryable<EMS_INKSCREEN_INFO>()
                 .Where(x => x.HOSPITAL_ID == hosptialId)
                 .ToList();
            var result = new List<InkScreenBindDto>();
            foreach (var inkScreen in inkScreens)
            {
                if (inkScreen.EQUIPMENT_ID.IsNullOrEmpty())
                {
                    result.Add(new InkScreenBindDto(inkScreen.INKSCREEN_ID, "未绑定", inkScreen.INKSCREEN_SORT, inkScreen.MAC, "", "", ""));
                }
                else
                {
                    equipmentContext.Injection(inkScreen.EQUIPMENT_ID);
                    var equipment = equipmentContext.GetEquipment(inkScreen.EQUIPMENT_ID);
                    result.Add(new InkScreenBindDto(inkScreen.INKSCREEN_ID, "已绑定", inkScreen.INKSCREEN_SORT, inkScreen.MAC, equipment.EQUIPMENT_CODE, equipmentContext.ExchangeProfessionalGroupName(equipment!.UNIT_ID), inkScreen.EQUIPMENT_ID));
                }
            }
            var pGourpName = equipmentContext.ExchangeProfessionalGroupName(pGourpId);
            result = result
                .WhereIF(isBand.HasValue, x => x.EquipmentId.IsNotNullOrEmpty() == isBand)
                .WhereIF(pGourpName.IsNotNullOrEmpty(), x => x.GroupName == pGourpName)
                .WhereIF(mac.IsNotNullOrEmpty(), x => x.Mac.Contains(mac) || mac.Contains(x.Mac))
                .OrderBy(x => x.SortNo)
                .ToList();
            Log.Information($"设备绑定结束...");
            return result;
        }

        /// <summary>
        /// 获取设备墨水屏列表
        /// </summary>
        /// <param name="hosptialId"></param>
        /// <param name="lightState"></param>
        /// <param name="lineState"></param>
        /// <param name="equipmentState"></param>
        /// <param name="pGourpId"></param>
        /// <param name="macOrEquipmentCode"></param>
        /// <returns></returns>
        public List<InkScreenDto> GetInkScreens(string hosptialId, LightStateEnum? lightState, LineStateEnum? lineState, EquipmentStateEnum? equipmentState, string? pGourpId, string? macOrEquipmentCode)
        {

            if (_configuration.GetSection("IsAutoRefresh").Exists())
            {
                if (_configuration.GetSection("IsAutoRefresh").Value == "1")
                {
                    RealTimeMonitoring();
                }
            }
            var InkScreenDtos = new List<InkScreenDto>();

            var inkScreensQuery = _dbContext.Db.Queryable<EMS_INKSCREEN_INFO>()
                .Where(x => x.HOSPITAL_ID == hosptialId)
                .WhereIF(lightState.HasValue, x => x.LIGHT_STATE == lightState)
                .WhereIF(lineState.HasValue, x => x.ONLINE_STATE == lineState)
                .ToList();

            if (inkScreensQuery.Count() == 0)
            {
                return new List<InkScreenDto>();
            }
            equipmentContext.SetHosptalIdAndLabId(hosptialId, null);

            var inkScreens = inkScreensQuery.Where(x => x.EQUIPMENT_ID.IsNotNullOrEmpty());



            foreach (var inkScreen in inkScreens)
            {
                equipmentContext.Injection(inkScreen.EQUIPMENT_ID);
                var equipment = equipmentContext.GetEquipment(inkScreen.EQUIPMENT_ID);
                var (warnDuration, warnTime, warnMsgs) = equipmentContext.CombinedWarnInfo(equipment.WarnRecords);
                var inkScreenInfo = new InkScreenDto(
                    inkScreen.INKSCREEN_ID,
                    inkScreen.LIGHT_STATE,
                    inkScreen.LIGHT_STATE.ToDesc(),
                    inkScreen.MAC,
                    inkScreen.ONLINE_STATE,
                    inkScreen.ONLINE_STATE.ToDesc(),
                    inkScreen.POWER.ToString("F3"),
                    inkScreen.POWER_THRESHOLD,
                    inkScreen.OPER_PERSON,
                    inkScreen.OPER_TIME,
                    equipment.EQUIPMENT_ID,
                    equipment.EQUIPMENT_STATE,
                    equipmentContext.ExchangeEquipmentState(equipment.EQUIPMENT_STATE),
                    equipment.EQUIPMENT_CODE,
                    equipmentContext.ExchangeProfessionalGroupName(equipment.UNIT_ID),
                    warnMsgs,
                    warnTime,
                    warnDuration,
                    equipment.MANUFACTURER,
                    equipment.DEALER,
                    equipment.eMS_INSTALL_INFO is null ? "" : equipment.eMS_INSTALL_INFO.INSTALL_AREA,
                    equipment.KEEP_PERSON,
                    inkScreen.DELIVERY
                );
                InkScreenDtos.Add(inkScreenInfo);
            }
            var result = InkScreenDtos
                 .WhereIF(equipmentState.HasValue, x => equipmentContext.ExchangeEquipmentStateEnum(x.EquipmentState) == equipmentState)
                 .WhereIF(pGourpId.IsNotNullOrEmpty(), x => x.GroupName == equipmentContext.ExchangeProfessionalGroupName(pGourpId))
                 .WhereIF(macOrEquipmentCode.IsNotNullOrEmpty(), x => x.Mac.ToLower().Contains(macOrEquipmentCode) || x.EquipmentmCode is null ? false : x.EquipmentmCode.Contains(macOrEquipmentCode))
                 .ToList();
            return result;

        }

        public List<WarnRecordDto> GetEquipmentWarnRecords(string? equipmentId, string? warnMsg, DateTime? startTime, DateTime? endTime)
        {

            var result = new List<WarnRecordDto>();
            if (equipmentId is null)
            {
                equipmentContext.SetHosptalIdAndLabId(_user.HOSPITAL_ID, null);
                equipmentContext.Injection();
            }
            else
            {
                equipmentContext.Injection(equipmentId);
            }

            foreach (var equipment in equipmentContext.equipments)
            {
                if (equipment.WarnRecords is null)
                {
                    return new();
                }
                var warnRecords = equipment.WarnRecords
                    .WhereIF(warnMsg.IsNotNullOrEmpty(), x => x.WARN_MSG.Contains(warnMsg))
                    .WhereIF(startTime.HasValue, x => x.WARN_TIME >= startTime)
                    .WhereIF(endTime.HasValue, x => x.WARN_TIME <= endTime)
                    .ToList();

                foreach (var warnRecord in warnRecords)
                {
                    result.Add(new(warnRecord.EQUIPMENT_ID, warnRecord.WARN_RID, warnRecord.WARN_MSG, warnRecord.WARN_TYPE, warnRecord.WARN_TIME!.Value, warnRecord.DISPOSE_PERSON, warnRecord.DISPOSE_TIME, warnRecord.DISPOSE_STATE));
                }
            }
            return result;
        }

        public List<InkScreenDevice> GetAllInkScreen()
        {

            var result = new List<InkScreenDevice>();
            if (_configuration.GetSection("IsAutoRefresh").Exists())
            {
                if (_configuration.GetSection("IsAutoRefresh").Value == "1")
                {
                    var list = _h115Client.QueryDeviceMac();
                    foreach (var device in list)
                    {
                        result.Add(new(device.mac, device.aliasName));
                    }
                }
            }

            return result;

        }

        public void InkScreenDeviceLampControl(string inkScreenId, LightStateEnum lightStateEnum)
        {
            var inkScreen = _dbContext.Db.Queryable<EMS_INKSCREEN_INFO>().Where(x => x.INKSCREEN_ID == inkScreenId).First();
            if (inkScreen is null)
            {
                return;
            }

            if (_configuration.GetSection("IsAutoRefresh").Exists())
            {
                if (_configuration.GetSection("IsAutoRefresh").Value == "1")
                {
                    if (lightStateEnum == LightStateEnum.Lighting)
                    {
                        _h115Client.LightRedLED(inkScreen.MAC);
                    }
                    else if (lightStateEnum == LightStateEnum.lightsOut)
                    {

                        _h115Client.ClosseseLED(inkScreen.MAC);
                    }
                }
            }
            inkScreen.LIGHT_STATE = lightStateEnum;
            _dbContext.Db.Updateable(inkScreen).ExecuteCommand();
        }

        /// <summary>
        /// 查询当前登录人能过滤的专业组
        /// </summary>
        /// <returns></returns>
        public List<GroupDto> GetPgroups(string? labId)
        {
            var result = new List<GroupDto>();

            var groups = _dbContext.Db.Queryable<SYS6_INSPECTION_PGROUP>()
                .WhereIF(labId is not null, x => x.LAB_ID == labId)
                .Where(x => x.HOSPITAL_ID == _user.HOSPITAL_ID)
                .Where(x => x.PGROUP_STATE == "1")
                .ToList();
            if (groups is null)
            {
                return new();
            }
            foreach (var group in groups)
            {
                result.Add(new(group.PGROUP_ID, group.PGROUP_NAME ?? ""));
            }
            return result;
        }
    }
}
