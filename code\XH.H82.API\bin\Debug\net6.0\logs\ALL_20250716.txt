2025-07-16 08:54:14.715 +08:00 [ERR] 未处理的异常::System.ArgumentNullException: Value cannot be null. (Parameter 'key')
   at System.Collections.Generic.Dictionary`2.FindValue(TKey key)
   at System.Collections.Generic.Dictionary`2.TryGetValue(TKey key, TValue& value)
   at XH.H82.Services.TemplateDesign.TemplateDesignService.BuildFieldDict(Items item, Dictionary`2 widgetPropsMap, Int32 sort)
   at XH.H82.Services.TemplateDesign.TemplateDesignService.<>c__DisplayClass13_0.<ExtractFieldDicts>b__3(Items item, Int32 idx)
   at System.Linq.Enumerable.SelectIterator[TSource,TResult](IEnumerable`1 source, Func`3 selector)+MoveNext()
   at XH.H82.Services.TemplateDesign.TemplateDesignService.InitBaseInfoFieldDict()
   at Castle.Proxies.Invocations.ITemplateDesignService_InitBaseInfoFieldDict.InvokeMethodOnTarget()
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.UseCacheWhenFailedAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.CacheAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at Castle.Proxies.ITemplateDesignServiceProxy.InitBaseInfoFieldDict()
   at XH.H82.API.Controllers.EquipmentClassNew.EquipmentClassNewController.InitBaseInfoFieldDict() in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\Controllers\EquipmentClassNew\EquipmentClassNewController.cs:line 205
   at lambda_method918(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-07-16 08:54:14.741 +08:00 [ERR] HTTP POST /api/EquipmentClassNew/InitBaseInfoFieldDict responded 500 in ********.7923 ms
2025-07-16 08:54:14.750 +08:00 [INF] 【接口超时阀值预警】 [6dc1604f3442b9eb10d7acb90a95b75a]接口/api/EquipmentClassNew/InitBaseInfoFieldDict,耗时:[********]毫秒
2025-07-16 08:54:14.817 +08:00 [INF] HTTP GET /api/Test/TemplateDesign responded 401 in 103.1438 ms
2025-07-16 08:54:20.171 +08:00 [INF] 第三方url地址为：http://************
2025-07-16 08:54:24.129 +08:00 [INF] HTTP GET /api/Test/TemplateDesign responded 200 in 6011.4683 ms
2025-07-16 08:54:24.129 +08:00 [INF] 【接口超时阀值预警】 [bb45792464e9a07807f58c4583481598]接口/api/Test/TemplateDesign,耗时:[6012]毫秒
2025-07-16 08:55:52.524 +08:00 [INF] 第三方url地址为：http://************
2025-07-16 08:56:53.794 +08:00 [INF] HTTP GET /api/Test/TemplateDesign responded 200 in 61402.2530 ms
2025-07-16 08:56:53.795 +08:00 [INF] 【接口超时阀值预警】 [eb914e802b9c34abab4a4d548b92e8f2]接口/api/Test/TemplateDesign,耗时:[61402]毫秒
2025-07-16 09:00:26.360 +08:00 [INF] 【SQL执行耗时:495.5685ms】

[Sql]: SELECT "SETUP_ID","FUNC_ID","HOSPITAL_ID","FIRST_RPERSON","SKIN_ID","LAST_MTIME","IF_DOUBLE_CLICK","FORM_QUERY_JSON","SETUP_CNAME","SETUP_SORT","SETUP_STATE","SETUP_DESC","FORM_COL_JSON","MODULE_ID","REMARK","FIRST_RTIME","LAST_MPERSON","FORM_JSON","IF_QUERY","SETUP_NAME","SETUP_CLASS"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ( "SETUP_ID" = :SETUP_ID0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::SETUP_ID0 [Value]:******** [Type]:AnsiString    

2025-07-16 09:02:20.252 +08:00 [INF] ==>App Start..2025-07-16 09:02:20
2025-07-16 09:02:20.474 +08:00 [INF] ==>当前模块:H82,6.25.329
2025-07-16 09:02:20.478 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-16 09:02:22.605 +08:00 [INF] ==>基础连接请求完成.
2025-07-16 09:02:22.989 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-16 09:02:23.496 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-16 09:02:23.816 +08:00 [INF] ==>S01版本写入成功:6.25.329
2025-07-16 09:02:26.902 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-16 09:02:27.675 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-16 09:02:28.378 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-16 09:02:28.378 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-16 09:02:29.443 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-16 09:02:31.638 +08:00 [INF] ==>初始化完成..
2025-07-16 09:02:31.714 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-16 09:02:31.715 +08:00 [INF] 设备启用任务
2025-07-16 09:02:31.715 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-16 09:02:32.103 +08:00 [INF] 【SQL执行耗时:364.668ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-16 09:02:32.270 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-16 09:02:32.285 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-16 09:02:32.286 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-16 09:02:32.287 +08:00 [INF] Hosting environment: Development
2025-07-16 09:02:32.287 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-16 09:06:05.037 +08:00 [INF] ==>App Start..2025-07-16 09:06:05
2025-07-16 09:06:05.222 +08:00 [INF] ==>当前模块:H82,6.25.329
2025-07-16 09:06:05.226 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-16 09:06:06.806 +08:00 [INF] ==>基础连接请求完成.
2025-07-16 09:06:07.184 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-16 09:06:07.631 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-16 09:06:07.953 +08:00 [INF] ==>S01版本写入成功:6.25.329
2025-07-16 09:06:08.836 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-16 09:06:08.956 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-16 09:06:09.432 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-16 09:06:09.432 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-16 09:06:10.485 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-16 09:06:12.734 +08:00 [INF] ==>初始化完成..
2025-07-16 09:06:12.764 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-16 09:06:12.767 +08:00 [INF] 设备启用任务
2025-07-16 09:06:12.768 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-16 09:06:13.218 +08:00 [INF] 【SQL执行耗时:417.8811ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-16 09:06:13.405 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-16 09:06:13.424 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-16 09:06:13.426 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-16 09:06:13.426 +08:00 [INF] Hosting environment: Development
2025-07-16 09:06:13.426 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-16 10:00:24.513 +08:00 [INF] ==>App Start..2025-07-16 10:00:24
2025-07-16 10:00:24.748 +08:00 [INF] ==>当前模块:H82,6.25.329
2025-07-16 10:00:24.752 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-16 10:00:26.616 +08:00 [INF] ==>基础连接请求完成.
2025-07-16 10:00:27.043 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-16 10:00:27.557 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-16 10:00:27.925 +08:00 [INF] ==>S01版本写入成功:6.25.329
2025-07-16 10:00:29.131 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-16 10:00:29.254 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-16 10:00:29.818 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-16 10:00:29.818 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-16 10:00:30.928 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-16 10:00:33.386 +08:00 [INF] ==>初始化完成..
2025-07-16 10:00:33.419 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-16 10:00:33.422 +08:00 [INF] 设备启用任务
2025-07-16 10:00:33.423 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-16 10:00:33.837 +08:00 [INF] 【SQL执行耗时:385.3473ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-16 10:00:40.253 +08:00 [INF] ==>App Start..2025-07-16 10:00:40
2025-07-16 10:00:40.441 +08:00 [INF] ==>当前模块:H82,6.25.329
2025-07-16 10:00:40.445 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-16 10:00:41.871 +08:00 [INF] ==>基础连接请求完成.
2025-07-16 10:00:42.260 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-16 10:00:42.671 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-16 10:00:42.995 +08:00 [INF] ==>S01版本写入成功:6.25.329
2025-07-16 10:00:43.834 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-16 10:00:43.940 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-16 10:00:44.418 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-16 10:00:44.418 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-16 10:00:45.306 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-16 10:00:47.701 +08:00 [INF] ==>初始化完成..
2025-07-16 10:00:47.729 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-16 10:00:47.732 +08:00 [INF] 设备启用任务
2025-07-16 10:00:47.734 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-16 10:00:48.134 +08:00 [INF] 【SQL执行耗时:370.4648ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-16 10:00:48.326 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-16 10:00:48.348 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-16 10:00:48.350 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-16 10:00:48.350 +08:00 [INF] Hosting environment: Development
2025-07-16 10:00:48.350 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-16 10:01:33.850 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetToolBoxUrl responded 401 in 442.0551 ms
2025-07-16 10:01:42.642 +08:00 [INF] 调用S01模块[颁发Token],耗时:598ms
2025-07-16 10:01:42.707 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetToolBoxUrl responded 200 in 5993.8350 ms
2025-07-16 10:01:42.709 +08:00 [INF] 【接口超时阀值预警】 [aebce70f4fbf0bd087c938d3d18c83fb]接口/api/EquipmentClassNew/GetToolBoxUrl,耗时:[5994]毫秒
2025-07-16 10:02:48.074 +08:00 [INF] ==>App Start..2025-07-16 10:02:48
2025-07-16 10:02:48.262 +08:00 [INF] ==>当前模块:H82,6.25.329
2025-07-16 10:02:48.266 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-16 10:02:49.850 +08:00 [INF] ==>基础连接请求完成.
2025-07-16 10:02:50.256 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-16 10:02:50.729 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-16 10:02:51.077 +08:00 [INF] ==>S01版本写入成功:6.25.329
2025-07-16 10:02:52.087 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-16 10:02:52.210 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-16 10:02:52.757 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-16 10:02:52.757 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-16 10:02:53.723 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-16 10:02:56.110 +08:00 [INF] ==>初始化完成..
2025-07-16 10:02:56.143 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-16 10:02:56.146 +08:00 [INF] 设备启用任务
2025-07-16 10:02:56.147 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-16 10:02:56.542 +08:00 [INF] 【SQL执行耗时:365.6615ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-16 10:02:56.718 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-16 10:02:56.738 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-16 10:02:56.740 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-16 10:02:56.741 +08:00 [INF] Hosting environment: Development
2025-07-16 10:02:56.741 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-16 10:06:43.364 +08:00 [INF] 调用S01模块[颁发Token],耗时:277ms
2025-07-16 10:06:43.447 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetToolBoxUrl responded 200 in 5643.1010 ms
2025-07-16 10:06:43.451 +08:00 [INF] 【接口超时阀值预警】 [72ad65d7402c70d3d5f9c6365367fd48]接口/api/EquipmentClassNew/GetToolBoxUrl,耗时:[5652]毫秒
2025-07-16 10:07:23.616 +08:00 [INF] 调用S01模块[颁发Token],耗时:265ms
2025-07-16 10:07:43.501 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetToolBoxUrl responded 200 in 26431.2022 ms
2025-07-16 10:07:43.502 +08:00 [INF] 【接口超时阀值预警】 [2a07eae557f562eff3d43342ab06b3e2]接口/api/EquipmentClassNew/GetToolBoxUrl,耗时:[26431]毫秒
2025-07-16 10:08:02.263 +08:00 [INF] 调用S01模块[颁发Token],耗时:284ms
2025-07-16 10:08:02.265 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetToolBoxUrl responded 200 in 4039.6319 ms
2025-07-16 10:08:02.266 +08:00 [INF] 【接口超时阀值预警】 [4f47c5042fb12b268b6722a0d1fa9df0]接口/api/EquipmentClassNew/GetToolBoxUrl,耗时:[4041]毫秒
2025-07-16 10:08:51.669 +08:00 [INF] 调用S01模块[颁发Token],耗时:286ms
2025-07-16 10:08:52.374 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetToolBoxUrl responded 200 in 13901.0658 ms
2025-07-16 10:08:52.375 +08:00 [INF] 【接口超时阀值预警】 [753f918a6189f86d39d3c4976fe761cc]接口/api/EquipmentClassNew/GetToolBoxUrl,耗时:[13901]毫秒
2025-07-16 10:13:34.363 +08:00 [INF] 调用S01模块[颁发Token],耗时:263ms
2025-07-16 10:13:45.985 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetToolBoxUrl responded 200 in 21260.2666 ms
2025-07-16 10:13:45.986 +08:00 [INF] 【接口超时阀值预警】 [aa956483e1e5eafa362db96a99dbec6a]接口/api/EquipmentClassNew/GetToolBoxUrl,耗时:[21260]毫秒
2025-07-16 10:13:54.126 +08:00 [INF] 调用S01模块[颁发Token],耗时:263ms
2025-07-16 10:13:54.127 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetToolBoxUrl responded 200 in 1615.1122 ms
2025-07-16 10:13:54.128 +08:00 [INF] 【接口超时阀值预警】 [1753250518e137c49234f891dce58151]接口/api/EquipmentClassNew/GetToolBoxUrl,耗时:[1615]毫秒
2025-07-16 10:14:19.926 +08:00 [INF] 调用S01模块[颁发Token],耗时:268ms
2025-07-16 10:14:19.928 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetToolBoxUrl responded 200 in 1495.2533 ms
2025-07-16 10:14:19.930 +08:00 [INF] 【接口超时阀值预警】 [3cc6f48a688b0ed37d9028152f4f7287]接口/api/EquipmentClassNew/GetToolBoxUrl,耗时:[1497]毫秒
2025-07-16 10:28:49.771 +08:00 [INF] ==>App Start..2025-07-16 10:28:49
2025-07-16 10:28:49.969 +08:00 [INF] ==>当前模块:H82,6.25.329
2025-07-16 10:28:49.973 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-16 10:28:51.613 +08:00 [INF] ==>基础连接请求完成.
2025-07-16 10:28:52.071 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-16 10:28:52.528 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-16 10:28:53.022 +08:00 [INF] ==>S01版本写入成功:6.25.329
2025-07-16 10:28:53.817 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-16 10:28:53.934 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-16 10:28:54.434 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-16 10:28:54.434 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-16 10:28:55.007 +08:00 [ERR] 查询出错:中文提示 :  连接数据库过程中发生错误，检查服务器是否正常连接字符串是否正确，错误信息：ORA-12545: Network Transport: Unable to resolve connect hostnameDbType="Oracle";ConfigId="".
English Message : Connection open error . ORA-12545: Network Transport: Unable to resolve connect hostnameDbType="Oracle";ConfigId="" :
SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON" FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "LAST_MTIME" IS NULL )
2025-07-16 10:30:44.916 +08:00 [INF] ==>App Start..2025-07-16 10:30:44
2025-07-16 10:30:45.085 +08:00 [INF] ==>当前模块:H82,6.25.329
2025-07-16 10:30:45.088 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-16 10:30:46.555 +08:00 [INF] ==>基础连接请求完成.
2025-07-16 10:30:46.933 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-16 10:30:47.360 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-16 10:30:47.680 +08:00 [INF] ==>S01版本写入成功:6.25.329
2025-07-16 10:30:48.486 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-16 10:30:48.579 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-16 10:30:49.023 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-16 10:30:49.023 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-16 10:30:49.952 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-16 10:30:52.252 +08:00 [INF] ==>初始化完成..
2025-07-16 10:30:52.283 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-16 10:30:52.285 +08:00 [INF] 设备启用任务
2025-07-16 10:30:52.286 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-16 10:30:52.674 +08:00 [INF] 【SQL执行耗时:364.6413ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-16 10:30:52.815 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-16 10:30:52.828 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-16 10:30:52.829 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-16 10:30:52.830 +08:00 [INF] Hosting environment: Development
2025-07-16 10:30:52.830 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-16 10:31:21.965 +08:00 [INF] 【SQL执行耗时:350.8151ms】

[Sql]: SELECT "SETUP_ID","FUNC_ID","HOSPITAL_ID","FIRST_RPERSON","SKIN_ID","LAST_MTIME","IF_DOUBLE_CLICK","FORM_QUERY_JSON","SETUP_CNAME","SETUP_SORT","SETUP_STATE","SETUP_DESC","FORM_COL_JSON","MODULE_ID","REMARK","FIRST_RTIME","LAST_MPERSON","FORM_JSON","IF_QUERY","SETUP_NAME","SETUP_CLASS"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ( "SETUP_ID" = :SETUP_ID0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::SETUP_ID0 [Value]:******** [Type]:AnsiString    

2025-07-16 10:31:55.816 +08:00 [ERR] 未处理的异常::System.InvalidOperationException: Sequence contains no matching element
   at System.Linq.ThrowHelper.ThrowNoMatchException()
   at System.Linq.Enumerable.First[TSource](IEnumerable`1 source, Func`2 predicate)
   at XH.H82.Services.TemplateDesign.TemplateDesignService.<>c__DisplayClass13_0.<ExtractFieldDicts>b__3(Items item) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\TemplateDesign\TemplateDesignService.cs:line 223
   at Npoi.Mapper.EnumerableExtensions.ForEach[T](IEnumerable`1 sequence, Action`1 action)
   at XH.H82.Services.TemplateDesign.TemplateDesignService.ExtractFieldDicts(FormJsonDto form, PageSettingForm pageSettingForm, Dictionary`2 widgetPropsMap) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\TemplateDesign\TemplateDesignService.cs:line 219
   at XH.H82.Services.TemplateDesign.TemplateDesignService.InitBaseInfoFieldDict() in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\TemplateDesign\TemplateDesignService.cs:line 104
   at Castle.Proxies.Invocations.ITemplateDesignService_InitBaseInfoFieldDict.InvokeMethodOnTarget()
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.UseCacheWhenFailedAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.CacheAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at Castle.Proxies.ITemplateDesignServiceProxy.InitBaseInfoFieldDict()
   at XH.H82.API.Controllers.EquipmentClassNew.EquipmentClassNewController.InitBaseInfoFieldDict() in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\Controllers\EquipmentClassNew\EquipmentClassNewController.cs:line 210
   at lambda_method918(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-07-16 10:31:55.827 +08:00 [ERR] HTTP POST /api/EquipmentClassNew/InitBaseInfoFieldDict responded 500 in 38508.1167 ms
2025-07-16 10:31:55.831 +08:00 [INF] 【接口超时阀值预警】 [ff014fa6760434c92d89e000ef4ff6d7]接口/api/EquipmentClassNew/InitBaseInfoFieldDict,耗时:[38519]毫秒
2025-07-16 10:31:59.005 +08:00 [INF] 【SQL执行耗时:364.2801ms】

[Sql]: SELECT "SETUP_ID","FUNC_ID","HOSPITAL_ID","FIRST_RPERSON","SKIN_ID","LAST_MTIME","IF_DOUBLE_CLICK","FORM_QUERY_JSON","SETUP_CNAME","SETUP_SORT","SETUP_STATE","SETUP_DESC","FORM_COL_JSON","MODULE_ID","REMARK","FIRST_RTIME","LAST_MPERSON","FORM_JSON","IF_QUERY","SETUP_NAME","SETUP_CLASS"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ( "SETUP_ID" = :SETUP_ID0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::SETUP_ID0 [Value]:******** [Type]:AnsiString    

2025-07-16 10:35:24.096 +08:00 [INF] ==>App Start..2025-07-16 10:35:24
2025-07-16 10:35:24.312 +08:00 [INF] ==>当前模块:H82,6.25.329
2025-07-16 10:35:24.315 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-16 10:35:26.094 +08:00 [INF] ==>基础连接请求完成.
2025-07-16 10:35:26.606 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-16 10:35:27.125 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-16 10:35:27.455 +08:00 [INF] ==>S01版本写入成功:6.25.329
2025-07-16 10:35:28.485 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-16 10:35:28.606 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-16 10:35:29.091 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-16 10:35:29.092 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-16 10:35:30.110 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-16 10:35:35.114 +08:00 [INF] ==>初始化完成..
2025-07-16 10:35:35.137 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-16 10:35:35.139 +08:00 [INF] 设备启用任务
2025-07-16 10:35:35.139 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-16 10:35:35.540 +08:00 [INF] 【SQL执行耗时:376.7877ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-16 10:35:35.700 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-16 10:35:35.716 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-16 10:35:35.717 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-16 10:35:35.718 +08:00 [INF] Hosting environment: Development
2025-07-16 10:35:35.718 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-16 10:36:23.551 +08:00 [INF] 【SQL执行耗时:379.1532ms】

[Sql]: SELECT "SETUP_ID","FUNC_ID","HOSPITAL_ID","FIRST_RPERSON","SKIN_ID","LAST_MTIME","IF_DOUBLE_CLICK","FORM_QUERY_JSON","SETUP_CNAME","SETUP_SORT","SETUP_STATE","SETUP_DESC","FORM_COL_JSON","MODULE_ID","REMARK","FIRST_RTIME","LAST_MPERSON","FORM_JSON","IF_QUERY","SETUP_NAME","SETUP_CLASS"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ( "SETUP_ID" = :SETUP_ID0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::SETUP_ID0 [Value]:******** [Type]:AnsiString    

2025-07-16 10:37:37.125 +08:00 [INF] ==>App Start..2025-07-16 10:37:37
2025-07-16 10:37:37.307 +08:00 [INF] ==>当前模块:H82,6.25.329
2025-07-16 10:37:37.311 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-16 10:37:38.801 +08:00 [INF] ==>基础连接请求完成.
2025-07-16 10:37:39.251 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-16 10:37:39.771 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-16 10:37:40.111 +08:00 [INF] ==>S01版本写入成功:6.25.329
2025-07-16 10:37:40.977 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-16 10:37:41.082 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-16 10:37:41.528 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-16 10:37:41.529 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-16 10:37:42.520 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-16 10:37:45.754 +08:00 [INF] ==>初始化完成..
2025-07-16 10:37:45.778 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-16 10:37:45.779 +08:00 [INF] 设备启用任务
2025-07-16 10:37:45.780 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-16 10:37:46.213 +08:00 [INF] 【SQL执行耗时:411.2272ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-16 10:37:46.364 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-16 10:37:46.381 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-16 10:37:46.382 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-16 10:37:46.383 +08:00 [INF] Hosting environment: Development
2025-07-16 10:37:46.383 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-16 10:38:22.974 +08:00 [INF] HTTP POST /api/EquipmentClassNew/InitBaseInfoFieldDict responded 401 in 276.4427 ms
2025-07-16 10:38:33.273 +08:00 [INF] 【SQL执行耗时:352.6038ms】

[Sql]: SELECT "SETUP_ID","FUNC_ID","HOSPITAL_ID","FIRST_RPERSON","SKIN_ID","LAST_MTIME","IF_DOUBLE_CLICK","FORM_QUERY_JSON","SETUP_CNAME","SETUP_SORT","SETUP_STATE","SETUP_DESC","FORM_COL_JSON","MODULE_ID","REMARK","FIRST_RTIME","LAST_MPERSON","FORM_JSON","IF_QUERY","SETUP_NAME","SETUP_CLASS"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ( "SETUP_ID" = :SETUP_ID0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::SETUP_ID0 [Value]:******** [Type]:AnsiString    

2025-07-16 10:43:11.525 +08:00 [ERR] 未处理的异常::System.NullReferenceException: Object reference not set to an instance of an object.
   at XH.H82.Services.TemplateDesign.TemplateDesignService.<>c__DisplayClass13_0.<ExtractFieldDicts>b__3(Items item) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\TemplateDesign\TemplateDesignService.cs:line 226
   at Npoi.Mapper.EnumerableExtensions.ForEach[T](IEnumerable`1 sequence, Action`1 action)
   at XH.H82.Services.TemplateDesign.TemplateDesignService.ExtractFieldDicts(FormJsonDto form, PageSettingForm pageSettingForm, Dictionary`2 widgetPropsMap) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\TemplateDesign\TemplateDesignService.cs:line 219
   at XH.H82.Services.TemplateDesign.TemplateDesignService.InitBaseInfoFieldDict() in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\TemplateDesign\TemplateDesignService.cs:line 104
   at Castle.Proxies.Invocations.ITemplateDesignService_InitBaseInfoFieldDict.InvokeMethodOnTarget()
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.UseCacheWhenFailedAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.CacheAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at Castle.Proxies.ITemplateDesignServiceProxy.InitBaseInfoFieldDict()
   at XH.H82.API.Controllers.EquipmentClassNew.EquipmentClassNewController.InitBaseInfoFieldDict() in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\Controllers\EquipmentClassNew\EquipmentClassNewController.cs:line 210
   at lambda_method918(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-07-16 10:43:11.529 +08:00 [ERR] HTTP POST /api/EquipmentClassNew/InitBaseInfoFieldDict responded 500 in 281703.9757 ms
2025-07-16 10:43:11.532 +08:00 [INF] 【接口超时阀值预警】 [44bcf708ed9b3874aab9fadee89bb873]接口/api/EquipmentClassNew/InitBaseInfoFieldDict,耗时:[281704]毫秒
2025-07-16 10:43:11.872 +08:00 [INF] 第三方url地址为：http://************
2025-07-16 10:44:09.230 +08:00 [INF] HTTP GET /api/Test/TemplateDesign responded 200 in 57716.4136 ms
2025-07-16 10:44:09.230 +08:00 [INF] 【接口超时阀值预警】 [19bc5b366b32dd3d5d4532dfca9a01d3]接口/api/Test/TemplateDesign,耗时:[57716]毫秒
2025-07-16 10:47:19.158 +08:00 [INF] 【SQL执行耗时:517.8627ms】

[Sql]: SELECT "SETUP_ID","FUNC_ID","HOSPITAL_ID","FIRST_RPERSON","SKIN_ID","LAST_MTIME","IF_DOUBLE_CLICK","FORM_QUERY_JSON","SETUP_CNAME","SETUP_SORT","SETUP_STATE","SETUP_DESC","FORM_COL_JSON","MODULE_ID","REMARK","FIRST_RTIME","LAST_MPERSON","FORM_JSON","IF_QUERY","SETUP_NAME","SETUP_CLASS"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ( "SETUP_ID" = :SETUP_ID0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::SETUP_ID0 [Value]:******** [Type]:AnsiString    

2025-07-16 10:47:48.519 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:371ms
2025-07-16 10:47:48.733 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:211ms
2025-07-16 10:47:48.736 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 10:47:48.811 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:74ms
2025-07-16 10:47:48.923 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:111ms
2025-07-16 10:47:48.923 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 10:47:49.002 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:77ms
2025-07-16 10:47:49.113 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:111ms
2025-07-16 10:47:49.114 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 10:47:49.188 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:73ms
2025-07-16 10:47:49.300 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:110ms
2025-07-16 10:47:49.300 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 10:47:49.376 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:75ms
2025-07-16 10:47:49.486 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:109ms
2025-07-16 10:47:49.487 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 10:47:49.561 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:73ms
2025-07-16 10:47:49.674 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:111ms
2025-07-16 10:47:49.674 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 10:47:49.750 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:75ms
2025-07-16 10:47:49.858 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:107ms
2025-07-16 10:47:49.858 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 10:47:49.933 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:73ms
2025-07-16 10:47:50.045 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:112ms
2025-07-16 10:47:50.045 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 10:47:50.119 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:72ms
2025-07-16 10:47:50.233 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:113ms
2025-07-16 10:47:50.233 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 10:47:50.309 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:75ms
2025-07-16 10:47:50.424 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:115ms
2025-07-16 10:47:50.425 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 10:47:50.509 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:83ms
2025-07-16 10:47:50.618 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:108ms
2025-07-16 10:47:50.619 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 10:47:50.709 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:89ms
2025-07-16 10:47:50.820 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:111ms
2025-07-16 10:47:50.821 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 10:47:50.909 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:87ms
2025-07-16 10:47:51.023 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:113ms
2025-07-16 10:47:51.024 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 10:47:51.025 +08:00 [INF] HTTP POST /api/EquipmentClassNew/InitBaseInfoFieldDict responded 200 in 32423.0390 ms
2025-07-16 10:47:51.025 +08:00 [INF] 【接口超时阀值预警】 [e1f7ae9cf7ccdccb1a9230841b5e81d2]接口/api/EquipmentClassNew/InitBaseInfoFieldDict,耗时:[32423]毫秒
2025-07-16 11:04:27.183 +08:00 [INF] 第三方url地址为：http://************
2025-07-16 11:04:31.290 +08:00 [INF] HTTP GET /api/Test/TemplateDesign responded 200 in 4148.7702 ms
2025-07-16 11:04:31.290 +08:00 [INF] 【接口超时阀值预警】 [99d5339e7ebbc5637173f59314f6fa96]接口/api/Test/TemplateDesign,耗时:[4149]毫秒
2025-07-16 11:07:31.165 +08:00 [INF] ==>App Start..2025-07-16 11:07:31
2025-07-16 11:07:31.353 +08:00 [INF] ==>当前模块:H82,6.25.329
2025-07-16 11:07:31.357 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-16 11:07:33.007 +08:00 [INF] ==>基础连接请求完成.
2025-07-16 11:07:33.415 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-16 11:07:33.979 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-16 11:07:34.331 +08:00 [INF] ==>S01版本写入成功:6.25.329
2025-07-16 11:07:35.512 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-16 11:07:35.634 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-16 11:07:36.113 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-16 11:07:36.113 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-16 11:07:37.106 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-16 11:07:39.329 +08:00 [INF] ==>初始化完成..
2025-07-16 11:07:39.362 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-16 11:07:39.366 +08:00 [INF] 设备启用任务
2025-07-16 11:07:39.367 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-16 11:07:40.306 +08:00 [INF] 【SQL执行耗时:909.5661ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-16 11:07:40.517 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-16 11:07:40.535 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-16 11:07:40.537 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-16 11:07:40.537 +08:00 [INF] Hosting environment: Development
2025-07-16 11:07:40.537 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-16 11:08:25.722 +08:00 [INF] 第三方url地址为：http://************
2025-07-16 11:08:32.515 +08:00 [INF] HTTP GET /api/Test/TemplateDesign responded 200 in 11709.3650 ms
2025-07-16 11:08:32.519 +08:00 [INF] 【接口超时阀值预警】 [e5dd0ccb223eb2b80bd7dbc9c89ee647]接口/api/Test/TemplateDesign,耗时:[11717]毫秒
2025-07-16 11:11:24.221 +08:00 [INF] 【SQL执行耗时:363.872ms】

[Sql]: SELECT "SETUP_ID","FUNC_ID","HOSPITAL_ID","FIRST_RPERSON","SKIN_ID","LAST_MTIME","IF_DOUBLE_CLICK","FORM_QUERY_JSON","SETUP_CNAME","SETUP_SORT","SETUP_STATE","SETUP_DESC","FORM_COL_JSON","MODULE_ID","REMARK","FIRST_RTIME","LAST_MPERSON","FORM_JSON","IF_QUERY","SETUP_NAME","SETUP_CLASS"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ( "SETUP_ID" = :SETUP_ID0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::SETUP_ID0 [Value]:H8205618 [Type]:AnsiString    

2025-07-16 11:11:24.792 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:226ms
2025-07-16 11:11:25.011 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:216ms
2025-07-16 11:11:25.015 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 11:11:25.091 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:73ms
2025-07-16 11:11:25.208 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:115ms
2025-07-16 11:11:25.208 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 11:11:25.282 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:72ms
2025-07-16 11:11:25.397 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:114ms
2025-07-16 11:11:25.398 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 11:11:25.471 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:72ms
2025-07-16 11:11:25.586 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:114ms
2025-07-16 11:11:25.587 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 11:11:25.660 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:72ms
2025-07-16 11:11:25.782 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:121ms
2025-07-16 11:11:25.783 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 11:11:25.856 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:72ms
2025-07-16 11:11:25.970 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:113ms
2025-07-16 11:11:25.971 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 11:11:26.045 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:73ms
2025-07-16 11:11:26.159 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:112ms
2025-07-16 11:11:26.159 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 11:11:26.232 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:72ms
2025-07-16 11:11:26.353 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:120ms
2025-07-16 11:11:26.353 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 11:11:26.427 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:72ms
2025-07-16 11:11:26.571 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:144ms
2025-07-16 11:11:26.572 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 11:11:26.645 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:72ms
2025-07-16 11:11:26.767 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:121ms
2025-07-16 11:11:26.767 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 11:11:26.841 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:72ms
2025-07-16 11:11:26.955 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:113ms
2025-07-16 11:11:26.955 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 11:11:27.028 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:72ms
2025-07-16 11:11:27.140 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:111ms
2025-07-16 11:11:27.140 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 11:11:27.214 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:72ms
2025-07-16 11:11:27.338 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:123ms
2025-07-16 11:11:27.338 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 11:11:27.412 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:72ms
2025-07-16 11:11:27.525 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:113ms
2025-07-16 11:11:27.526 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 11:11:27.601 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:74ms
2025-07-16 11:11:27.712 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:110ms
2025-07-16 11:11:27.713 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 11:11:27.786 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:72ms
2025-07-16 11:11:27.901 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:115ms
2025-07-16 11:11:27.902 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 11:11:27.975 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:72ms
2025-07-16 11:11:28.085 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:110ms
2025-07-16 11:11:28.086 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 11:11:28.159 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:72ms
2025-07-16 11:11:28.271 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:110ms
2025-07-16 11:11:28.271 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 11:11:28.344 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:72ms
2025-07-16 11:11:28.455 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:110ms
2025-07-16 11:11:28.455 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 11:11:28.528 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:72ms
2025-07-16 11:11:28.639 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:110ms
2025-07-16 11:11:28.640 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 11:11:28.714 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:73ms
2025-07-16 11:11:28.827 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:113ms
2025-07-16 11:11:28.828 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 11:11:28.901 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:72ms
2025-07-16 11:11:29.014 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:111ms
2025-07-16 11:11:29.014 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 11:11:29.088 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:72ms
2025-07-16 11:11:29.199 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:110ms
2025-07-16 11:11:29.199 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 11:11:29.272 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:72ms
2025-07-16 11:11:29.384 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:111ms
2025-07-16 11:11:29.384 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 11:11:29.459 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:73ms
2025-07-16 11:11:29.571 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:111ms
2025-07-16 11:11:29.571 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 11:11:29.644 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:72ms
2025-07-16 11:11:29.755 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:110ms
2025-07-16 11:11:29.756 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 11:11:29.829 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:72ms
2025-07-16 11:11:29.951 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:121ms
2025-07-16 11:11:29.952 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 11:11:30.025 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:72ms
2025-07-16 11:11:30.145 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:119ms
2025-07-16 11:11:30.145 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 11:11:30.219 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:73ms
2025-07-16 11:11:30.336 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:115ms
2025-07-16 11:11:30.336 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 11:11:30.410 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:72ms
2025-07-16 11:11:30.548 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:138ms
2025-07-16 11:11:30.549 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 11:11:30.622 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:72ms
2025-07-16 11:11:30.738 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:116ms
2025-07-16 11:11:30.739 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 11:11:30.813 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:73ms
2025-07-16 11:11:30.929 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:115ms
2025-07-16 11:11:30.929 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 11:11:30.931 +08:00 [INF] HTTP POST /api/EquipmentClassNew/InitBaseInfoFieldDict responded 200 in 7487.5701 ms
2025-07-16 11:11:30.931 +08:00 [INF] 【接口超时阀值预警】 [afc0ba8a5799f89c03143f66ea3c8161]接口/api/EquipmentClassNew/InitBaseInfoFieldDict,耗时:[7488]毫秒
2025-07-16 11:13:01.180 +08:00 [INF] 调用H04模块[/externalapi/External/GetComplexForms?hospitalId=33A001&moduleId=H82&setupId=1&merge=1&qunitID=],耗时:175ms
2025-07-16 11:13:01.180 +08:00 [ERR] 调用H04模块[/externalapi/External/GetComplexForms?hospitalId=33A001&moduleId=H82&setupId=1&merge=1&qunitID=]请求完成,但是返回了错误:SYS6_MODULE_FUNC_DICT表中未找到设置项，或者FORM_COL_JSON字段为空
2025-07-16 11:13:11.224 +08:00 [ERR] 未处理的异常::System.Exception: 调用H04模块[/externalapi/External/GetComplexForms?hospitalId=33A001&moduleId=H82&setupId=1&merge=1&qunitID=]请求完成,但是返回了错误:SYS6_MODULE_FUNC_DICT表中未找到设置项，或者FORM_COL_JSON字段为空
   at XH.H82.Models.BusinessModuleClient.H04.H04Client.H04Request[T](String url, Method method, Object body) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Models\BusinessModuleClient\H04\H04Client.cs:line 86
   at XH.H82.Models.BusinessModuleClient.H04.H04Client.GetComplexForms(String setUpId, String merge, String qunitId) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Models\BusinessModuleClient\H04\H04Client.cs:line 146
   at XH.H82.Services.TemplateDesign.TemplateDesignService.GetComplexForms(String setUpId, String merge, String qunitId) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\TemplateDesign\TemplateDesignService.cs:line 145
   at Castle.Proxies.Invocations.ITemplateDesignService_GetComplexForms.InvokeMethodOnTarget()
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.UseCacheWhenFailedAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.CacheAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at Castle.Proxies.ITemplateDesignServiceProxy.GetComplexForms(String setUpId, String merge, String qunitId)
   at XH.H82.API.Controllers.EquipmentClassNew.EquipmentClassNewController.GetComplexForms(String classId, String merge, String qunitId) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\Controllers\EquipmentClassNew\EquipmentClassNewController.cs:line 197
   at lambda_method1042(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-07-16 11:13:11.227 +08:00 [ERR] HTTP GET /api/EquipmentClassNew/GetComplexForms/1 responded 500 in 10293.0177 ms
2025-07-16 11:13:11.227 +08:00 [INF] 【接口超时阀值预警】 [fef39915008c858b2ddbf78ce4e0db59]接口/api/EquipmentClassNew/GetComplexForms/1,耗时:[10293]毫秒
2025-07-16 11:17:30.511 +08:00 [INF] HTTP POST /api/EquipmentClassNew/CreateComplexForms/1 responded 401 in 12.5915 ms
2025-07-16 11:17:39.341 +08:00 [INF] 【SQL执行耗时:448.6657ms】

[Sql]: SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON"  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "CLASS_ID" = :ClassId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::ClassId0 [Value]:1 [Type]:String    

2025-07-16 11:17:50.170 +08:00 [ERR] 未处理的异常::System.Net.Http.HttpRequestException: Request failed with status code InternalServerError
   at RestSharp.ResponseThrowExtension.ThrowIfError(RestResponse response)
   at RestSharp.RestClient.ExecuteAsync(RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.ExecuteAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.AsyncHelpers.<>c__DisplayClass1_0`1.<<RunSync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.<Run>g__PostCallback|7_0(Object _)
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.Run()
   at RestSharp.AsyncHelpers.RunSync(Func`1 task)
   at RestSharp.AsyncHelpers.RunSync[T](Func`1 task)
   at RestSharp.RestClientExtensions.ExecutePost[T](IRestClient client, RestRequest request)
   at XH.H82.Models.BusinessModuleClient.H04.H04Client.H04Request[T](String url, Method method, Object body) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Models\BusinessModuleClient\H04\H04Client.cs:line 67
   at XH.H82.Models.BusinessModuleClient.H04.H04Client.CreateComplexForms(TagTemplateDto body) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Models\BusinessModuleClient\H04\H04Client.cs:line 122
   at XH.H82.Services.TemplateDesign.TemplateDesignService.CreateComplexForms(String classId, String layoutId)
   at Castle.Proxies.Invocations.ITemplateDesignService_CreateComplexForms.InvokeMethodOnTarget()
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.UseCacheWhenFailedAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.CacheAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at Castle.Proxies.ITemplateDesignServiceProxy.CreateComplexForms(String classId, String layoutId)
   at XH.H82.API.Controllers.EquipmentClassNew.EquipmentClassNewController.CreateComplexForms(String classId)
   at lambda_method1051(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-07-16 11:17:50.171 +08:00 [ERR] HTTP POST /api/EquipmentClassNew/CreateComplexForms/1 responded 500 in 11329.7882 ms
2025-07-16 11:17:50.172 +08:00 [INF] 【接口超时阀值预警】 [76c2280f15c6cf8f183ec2ec040978cb]接口/api/EquipmentClassNew/CreateComplexForms/1,耗时:[11330]毫秒
2025-07-16 11:19:33.147 +08:00 [INF] 【SQL执行耗时:368.6315ms】

[Sql]: SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON"  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "CLASS_ID" = :ClassId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::ClassId0 [Value]:1 [Type]:String    

2025-07-16 11:20:04.824 +08:00 [ERR] 未处理的异常::System.Net.Http.HttpRequestException: Request failed with status code InternalServerError
   at RestSharp.ResponseThrowExtension.ThrowIfError(RestResponse response)
   at RestSharp.RestClient.ExecuteAsync(RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.ExecuteAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.AsyncHelpers.<>c__DisplayClass1_0`1.<<RunSync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.<Run>g__PostCallback|7_0(Object _)
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.Run()
   at RestSharp.AsyncHelpers.RunSync(Func`1 task)
   at RestSharp.AsyncHelpers.RunSync[T](Func`1 task)
   at RestSharp.RestClientExtensions.ExecutePost[T](IRestClient client, RestRequest request)
   at XH.H82.Models.BusinessModuleClient.H04.H04Client.H04Request[T](String url, Method method, Object body) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Models\BusinessModuleClient\H04\H04Client.cs:line 67
   at XH.H82.Models.BusinessModuleClient.H04.H04Client.CreateComplexForms(TagTemplateDto body) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Models\BusinessModuleClient\H04\H04Client.cs:line 122
   at XH.H82.Services.TemplateDesign.TemplateDesignService.CreateComplexForms(String classId, String layoutId)
   at Castle.Proxies.Invocations.ITemplateDesignService_CreateComplexForms.InvokeMethodOnTarget()
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.UseCacheWhenFailedAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.CacheAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at Castle.Proxies.ITemplateDesignServiceProxy.CreateComplexForms(String classId, String layoutId)
   at XH.H82.API.Controllers.EquipmentClassNew.EquipmentClassNewController.CreateComplexForms(String classId)
   at lambda_method1051(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-07-16 11:20:04.826 +08:00 [ERR] HTTP POST /api/EquipmentClassNew/CreateComplexForms/1 responded 500 in 32085.4382 ms
2025-07-16 11:20:04.826 +08:00 [INF] 【接口超时阀值预警】 [0acdf86f99076de20dd4149b4cef79a5]接口/api/EquipmentClassNew/CreateComplexForms/1,耗时:[32085]毫秒
2025-07-16 11:21:31.614 +08:00 [INF] ==>App Start..2025-07-16 11:21:31
2025-07-16 11:21:31.802 +08:00 [INF] ==>当前模块:H82,6.25.329
2025-07-16 11:21:31.806 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-16 11:21:33.338 +08:00 [INF] ==>基础连接请求完成.
2025-07-16 11:21:33.769 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-16 11:21:34.195 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-16 11:21:34.516 +08:00 [INF] ==>S01版本写入成功:6.25.329
2025-07-16 11:21:35.395 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-16 11:21:35.505 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-16 11:21:36.087 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-16 11:21:36.087 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-16 11:21:37.168 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-16 11:21:39.415 +08:00 [INF] ==>初始化完成..
2025-07-16 11:21:39.439 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-16 11:21:39.441 +08:00 [INF] 设备启用任务
2025-07-16 11:21:39.442 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-16 11:21:39.892 +08:00 [INF] 【SQL执行耗时:428.2576ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-16 11:21:40.059 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-16 11:21:40.078 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-16 11:21:40.080 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-16 11:21:40.080 +08:00 [INF] Hosting environment: Development
2025-07-16 11:21:40.081 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-16 11:21:52.214 +08:00 [INF] 【SQL执行耗时:362.506ms】

[Sql]: SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON"  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "CLASS_ID" = :ClassId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::ClassId0 [Value]:1 [Type]:String    

2025-07-16 11:21:52.737 +08:00 [INF] HTTP POST /api/EquipmentClassNew/CreateComplexForms/1 responded 200 in 6253.9865 ms
2025-07-16 11:21:52.741 +08:00 [INF] 【接口超时阀值预警】 [e781062863a888f5e062b017d33e637e]接口/api/EquipmentClassNew/CreateComplexForms/1,耗时:[6264]毫秒
2025-07-16 11:22:39.038 +08:00 [INF] 【SQL执行耗时:347.7639ms】

[Sql]: SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON"  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "CLASS_ID" = :ClassId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::ClassId0 [Value]:1 [Type]:String    

2025-07-16 11:22:39.349 +08:00 [INF] HTTP POST /api/EquipmentClassNew/CreateComplexForms/1 responded 200 in 717.4451 ms
2025-07-16 11:22:39.350 +08:00 [INF] 【接口超时阀值预警】 [a7281b444ab8be7a9c3d27853c16fe85]接口/api/EquipmentClassNew/CreateComplexForms/1,耗时:[717]毫秒
2025-07-16 11:22:52.838 +08:00 [INF] 【SQL执行耗时:493.545ms】

[Sql]: SELECT "SETUP_ID","FUNC_ID","HOSPITAL_ID","FIRST_RPERSON","SKIN_ID","LAST_MTIME","IF_DOUBLE_CLICK","FORM_QUERY_JSON","SETUP_CNAME","SETUP_SORT","SETUP_STATE","SETUP_DESC","FORM_COL_JSON","MODULE_ID","REMARK","FIRST_RTIME","LAST_MPERSON","FORM_JSON","IF_QUERY","SETUP_NAME","SETUP_CLASS"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ( "SETUP_ID" = :SETUP_ID0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::SETUP_ID0 [Value]:1 [Type]:AnsiString    

2025-07-16 11:22:53.038 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetComplexForms/1 responded 200 in 747.9902 ms
2025-07-16 11:22:53.039 +08:00 [INF] 【接口超时阀值预警】 [fa017e50414234b7156ea6646da2ff23]接口/api/EquipmentClassNew/GetComplexForms/1,耗时:[749]毫秒
2025-07-16 11:23:04.450 +08:00 [INF] 【SQL执行耗时:372.4282ms】

[Sql]: SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON"  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "CLASS_ID" = :ClassId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::ClassId0 [Value]:1 [Type]:String    

2025-07-16 11:23:04.798 +08:00 [INF] HTTP POST /api/EquipmentClassNew/CreateComplexForms/1 responded 200 in 761.0868 ms
2025-07-16 11:23:04.798 +08:00 [INF] 【接口超时阀值预警】 [b3191f180063b29a79889a8773b860d8]接口/api/EquipmentClassNew/CreateComplexForms/1,耗时:[761]毫秒
2025-07-16 11:23:18.714 +08:00 [INF] 【SQL执行耗时:542.7032ms】

[Sql]: SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON"  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "CLASS_ID" = :ClassId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::ClassId0 [Value]:1 [Type]:String    

2025-07-16 11:24:02.739 +08:00 [INF] HTTP POST /api/EquipmentClassNew/CreateComplexForms/1 responded 200 in 44619.8832 ms
2025-07-16 11:24:02.853 +08:00 [INF] 【接口超时阀值预警】 [6e0102eca62ae431f9c4612b81860b31]接口/api/EquipmentClassNew/CreateComplexForms/1,耗时:[44733]毫秒
2025-07-16 11:24:05.404 +08:00 [INF] 【SQL执行耗时:370.5277ms】

[Sql]: SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON"  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "CLASS_ID" = :ClassId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::ClassId0 [Value]:1 [Type]:String    

2025-07-16 11:25:22.447 +08:00 [INF] HTTP POST /api/EquipmentClassNew/CreateComplexForms/1 responded 200 in 77448.9443 ms
2025-07-16 11:25:22.449 +08:00 [INF] 【接口超时阀值预警】 [19ace902728721a7768ba66321aef30f]接口/api/EquipmentClassNew/CreateComplexForms/1,耗时:[77450]毫秒
2025-07-16 11:25:46.377 +08:00 [INF] 【SQL执行耗时:750.6714ms】

[Sql]: SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON"  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "CLASS_ID" = :ClassId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::ClassId0 [Value]:1 [Type]:String    

2025-07-16 11:26:43.472 +08:00 [INF] HTTP POST /api/EquipmentClassNew/CreateComplexForms/1 responded 200 in 57933.2408 ms
2025-07-16 11:26:43.472 +08:00 [INF] 【接口超时阀值预警】 [1986291fd4373b9d2d07d938b9395c6a]接口/api/EquipmentClassNew/CreateComplexForms/1,耗时:[57933]毫秒
2025-07-16 11:28:15.919 +08:00 [INF] 【SQL执行耗时:541.2711ms】

[Sql]: SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON"  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "CLASS_ID" = :ClassId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::ClassId0 [Value]:1 [Type]:String    

2025-07-16 11:28:19.641 +08:00 [INF] 调用H04模块[/externalapi/External/CreateComplexForms],耗时:310ms
2025-07-16 11:28:19.649 +08:00 [INF] 调用H04模块/externalapi/External/CreateComplexForms请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 11:28:19.650 +08:00 [INF] HTTP POST /api/EquipmentClassNew/CreateComplexForms/1 responded 200 in 4313.2126 ms
2025-07-16 11:28:19.650 +08:00 [INF] 【接口超时阀值预警】 [2d60091147839e6cded080b57ec8da36]接口/api/EquipmentClassNew/CreateComplexForms/1,耗时:[4313]毫秒
2025-07-16 11:28:24.618 +08:00 [INF] 【SQL执行耗时:357.6131ms】

[Sql]: SELECT "SETUP_ID","FUNC_ID","HOSPITAL_ID","FIRST_RPERSON","SKIN_ID","LAST_MTIME","IF_DOUBLE_CLICK","FORM_QUERY_JSON","SETUP_CNAME","SETUP_SORT","SETUP_STATE","SETUP_DESC","FORM_COL_JSON","MODULE_ID","REMARK","FIRST_RTIME","LAST_MPERSON","FORM_JSON","IF_QUERY","SETUP_NAME","SETUP_CLASS"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ( "SETUP_ID" = :SETUP_ID0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::SETUP_ID0 [Value]:1 [Type]:AnsiString    

2025-07-16 11:28:24.690 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetComplexForms/1 responded 200 in 470.3671 ms
2025-07-16 11:28:48.770 +08:00 [INF] 【SQL执行耗时:359.0558ms】

[Sql]: SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON"  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "CLASS_ID" = :ClassId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::ClassId0 [Value]:1 [Type]:String    

2025-07-16 11:28:57.608 +08:00 [INF] 调用H04模块[/externalapi/External/CreateComplexForms],耗时:4857ms
2025-07-16 11:29:03.561 +08:00 [INF] 调用H04模块/externalapi/External/CreateComplexForms请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 11:29:30.753 +08:00 [INF] HTTP POST /api/EquipmentClassNew/CreateComplexForms/1 responded 200 in 42392.1863 ms
2025-07-16 11:29:30.754 +08:00 [INF] 【接口超时阀值预警】 [29bfc67ff7be3d1dfe50cf2b1d4bf1d9]接口/api/EquipmentClassNew/CreateComplexForms/1,耗时:[42392]毫秒
2025-07-16 11:29:37.857 +08:00 [INF] 【SQL执行耗时:357.116ms】

[Sql]: SELECT "SETUP_ID","FUNC_ID","HOSPITAL_ID","FIRST_RPERSON","SKIN_ID","LAST_MTIME","IF_DOUBLE_CLICK","FORM_QUERY_JSON","SETUP_CNAME","SETUP_SORT","SETUP_STATE","SETUP_DESC","FORM_COL_JSON","MODULE_ID","REMARK","FIRST_RTIME","LAST_MPERSON","FORM_JSON","IF_QUERY","SETUP_NAME","SETUP_CLASS"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ( "SETUP_ID" = :SETUP_ID0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::SETUP_ID0 [Value]:1 [Type]:AnsiString    

2025-07-16 11:29:37.930 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetComplexForms/1 responded 200 in 469.8926 ms
2025-07-16 11:30:16.666 +08:00 [INF] 【SQL执行耗时:372.8412ms】

[Sql]: SELECT "SETUP_ID","FUNC_ID","HOSPITAL_ID","FIRST_RPERSON","SKIN_ID","LAST_MTIME","IF_DOUBLE_CLICK","FORM_QUERY_JSON","SETUP_CNAME","SETUP_SORT","SETUP_STATE","SETUP_DESC","FORM_COL_JSON","MODULE_ID","REMARK","FIRST_RTIME","LAST_MPERSON","FORM_JSON","IF_QUERY","SETUP_NAME","SETUP_CLASS"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ( "SETUP_ID" ='H82_'|| CAST('1' AS VARCHAR2(4000))||'') AND   ROWNUM = 1 

2025-07-16 11:30:20.002 +08:00 [INF] 调用H04模块[/externalapi/External/GetComplexForms?hospitalId=33A001&moduleId=H82&setupId=1&merge=1&qunitID=],耗时:676ms
2025-07-16 11:30:20.003 +08:00 [ERR] 调用H04模块[/externalapi/External/GetComplexForms?hospitalId=33A001&moduleId=H82&setupId=1&merge=1&qunitID=]请求完成,但是返回了错误:SYS6_MODULE_FUNC_DICT表中未找到设置项，或者FORM_COL_JSON字段为空
2025-07-16 11:30:22.376 +08:00 [ERR] 未处理的异常::H.Utility.BizException: 调用H04模块[/externalapi/External/GetComplexForms?hospitalId=33A001&moduleId=H82&setupId=1&merge=1&qunitID=]请求完成,但是返回了错误:SYS6_MODULE_FUNC_DICT表中未找到设置项，或者FORM_COL_JSON字段为空
   at XH.H82.Models.BusinessModuleClient.H04.H04Client.H04Request[T](String url, Method method, Object body) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Models\BusinessModuleClient\H04\H04Client.cs:line 86
   at XH.H82.Models.BusinessModuleClient.H04.H04Client.GetComplexForms(String setUpId, String merge, String qunitId) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Models\BusinessModuleClient\H04\H04Client.cs:line 146
   at XH.H82.Services.TemplateDesign.TemplateDesignService.GetComplexForms(String setUpId, String merge, String qunitId)
   at Castle.Proxies.Invocations.ITemplateDesignService_GetComplexForms.InvokeMethodOnTarget()
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.UseCacheWhenFailedAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.CacheAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at Castle.Proxies.ITemplateDesignServiceProxy.GetComplexForms(String setUpId, String merge, String qunitId)
   at XH.H82.API.Controllers.EquipmentClassNew.EquipmentClassNewController.GetComplexForms(String classId, String merge, String qunitId) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\Controllers\EquipmentClassNew\EquipmentClassNewController.cs:line 197
   at lambda_method930(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-07-16 11:30:22.378 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetComplexForms/1 responded 200 in 6180.1115 ms
2025-07-16 11:30:22.379 +08:00 [INF] 【接口超时阀值预警】 [2bae4112e1478b0fd0b283e0c08899c9]接口/api/EquipmentClassNew/GetComplexForms/1,耗时:[6180]毫秒
2025-07-16 11:30:34.421 +08:00 [INF] 【SQL执行耗时:352.4204ms】

[Sql]: SELECT "SETUP_ID","FUNC_ID","HOSPITAL_ID","FIRST_RPERSON","SKIN_ID","LAST_MTIME","IF_DOUBLE_CLICK","FORM_QUERY_JSON","SETUP_CNAME","SETUP_SORT","SETUP_STATE","SETUP_DESC","FORM_COL_JSON","MODULE_ID","REMARK","FIRST_RTIME","LAST_MPERSON","FORM_JSON","IF_QUERY","SETUP_NAME","SETUP_CLASS"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ( "SETUP_ID" ='H82_'|| CAST('1' AS VARCHAR2(4000))||'') AND   ROWNUM = 1 

2025-07-16 11:30:48.786 +08:00 [INF] 调用H04模块[/externalapi/External/GetComplexForms?hospitalId=33A001&moduleId=H82&setupId=H82_1&merge=1&qunitID=],耗时:279ms
2025-07-16 11:30:48.822 +08:00 [INF] 调用H04模块/externalapi/External/GetComplexForms?hospitalId=33A001&moduleId=H82&setupId=H82_1&merge=1&qunitID=请求完成,返回了数据:{
  "formJson": {
    "layout": {
      "type": "fixed",
      "widthPercent": 0,
      "labelCol": 0,
      "verticalGap": 0,
      "size": null,
      "style": null,
      "showRowNum": false,
      "labelLayout": null,
      "labelWrap": false
    },
    "rows": [
      {
        "rowId": "row_3242fb33",
        "cols": [
          {
            "colId": "col_32422fd6",
            "span": 8,
            "items": [
              {
                "itemId": "item_3242c137",
                "group": null,
                "formId": null,
                "icon": null,
                "formName": null,
                "formCname": "设备名称",
                "formCode": "EQUIPMENT_NAME",
                "isForm": true,
                "dataType": null,
                "widgetType": "fixed",
                "dataClass": null,
                "labelHide": false,
                "rules": null,
                "WidgetProps": null,
                "propslist": null,
                "labelStyle": null
              }
            ]
          },
          {
            "colId": "col_324200b9",
            "span": 8,
            "items": [
              {
                "itemId": "item_2782599b",
                "group": null,
                "formId": null,
                "icon": null,
                "formName": null,
                "formCname": "设备序号",
                "formCode": "EQUIPMENT_NUM",
                "isForm": true,
                "dataType": null,
                "widgetType": "fixed",
                "dataClass": null,
                "labelHide": false,
                "rules": null,
                "WidgetProps": null,
                "propslist": null,
                "labelStyle": null
              }
            ]
          },
          {
            "colId": "col_278255fc",
            "span": 8,
            "items": [
              {
                "itemId": "item_5190fb4b",
                "group": null,
                "formId": null,
                "icon": null,
                "formName": null,
                "formCname": "所属医疗机构",
                "formCode": "HOSPITAL_ID",
                "isForm": true,
                "dataType": null,
                "widgetType": "fixed",
                "dataClass": null,
                "labelHide": false,
                "rules": null,
                "WidgetProps": null,
                "propslist": null,
                "labelStyle": null
              }
            ]
          }
        ]
      },
      {
        "rowId": "row_591985ca",
        "cols": [
          {
            "colId": "col_59195d4a",
            "span": 8,
            "items": [
              {
                "itemId": "item_59191737",
                "group": null,
                "formId": null,
                "icon": null,
                "formName": null,
                "formCname": "设备英文名称",
                "formCode": "EQUIPMENT_ENAME",
                "isForm": true,
                "dataType": null,
                "widgetType": "fixed",
                "dataClass": null,
                "labelHide": false,
                "rules": null,
                "WidgetProps": null,
                "propslist": null,
                "labelStyle": null
              }
            ]
          },
          {
            "colId": "col_5919f7f3",
            "span": 8,
            "items": [
              {
                "itemId": "item_90481161",
                "group": null,
                "formId": null,
                "icon": null,
                "formName": null,
                "formCname": "医院设备编号",
                "formCode": "SECTION_NO",
                "isForm": true,
                "dataType": null,
                "widgetType": "fixed",
                "dataClass": null,
                "labelHide": false,
                "rules": null,
                "WidgetProps": null,
                "propslist": null,
                "labelStyle": null
              }
            ]
          },
          {
            "colId": "col_9048f575",
            "span": 8,
            "items": [
              {
                "itemId": "item_1659c00a",
                "group": null,
                "formId": null,
                "icon": null,
                "formName": null,
                "formCname": "所属科室",
                "formCode": "LAB_ID",
                "isForm": true,
                "dataType": null,
                "widgetType": "fixed",
                "dataClass": null,
                "labelHide": false,
                "rules": null,
                "WidgetProps": null,
                "propslist": null,
                "labelStyle": null
              }
            ]
          }
        ]
      },
      {
        "rowId": "row_29091037",
        "cols": [
          {
            "colId": "col_290930e8",
            "span": 8,
            "items": [
              {
                "itemId": "item_2909c931",
                "group": null,
                "formId": null,
                "icon": null,
                "formName": null,
                "formCname": "设备代号",
                "formCode": "EQUIPMENT_CODE",
                "isForm": true,
                "dataType": null,
                "widgetType": "fixed",
                "dataClass": null,
                "labelHide": false,
                "rules": null,
                "WidgetProps": null,
                "propslist": null,
                "labelStyle": null
              }
            ]
          },
          {
            "colId": "col_29090fad",
            "span": 8,
            "items": [
              {
                "itemId": "item_9983f4c4",
                "group": null,
                "formId": null,
                "icon": null,
                "formName": null,
                "formCname": "注册证号",
                "formCode": "REGISTRATION_NUM",
                "isForm": true,
                "dataType": null,
                "widgetType": "fixed",
                "dataClass": null,
                "labelHide": false,
                "rules": null,
                "WidgetProps": null,
                "propslist": null,
                "labelStyle": null
              }
            ]
          },
          {
            "colId": "col_9983884c",
            "span": 8,
            "items": [
              {
                "itemId": "item_30157c62",
                "group": null,
                "formId": null,
                "icon": null,
                "formName": null,
                "formCname": "设备责任人",
                "formCode": "KEEP_PERSON",
                "isForm": true,
                "dataType": null,
                "widgetType": "fixed",
                "dataClass": null,
                "labelHide": false,
                "rules": null,
                "WidgetProps": null,
                "propslist": null,
                "labelStyle": null
              }
            ]
          }
        ]
      },
      {
        "rowId": "row_7656732b",
        "cols": [
          {
            "colId": "col_76567ade",
            "span": 8,
            "items": [
              {
                "itemId": "item_765637f9",
                "group": null,
                "formId": null,
                "icon": null,
                "formName": null,
                "formCname": "设备类型",
                "formCode": "EQUIPMENT_CLASS",
                "isForm": true,
                "dataType": null,
                "widgetType": "fixed",
                "dataClass": null,
                "labelHide": false,
                "rules": null,
                "WidgetProps": null,
                "propslist": null,
                "labelStyle": null
              }
            ]
          },
          {
            "colId": "col_765670ac",
            "span": 8,
            "items": [
              {
                "itemId": "item_1119f6cd",
                "group": null,
                "formId": null,
                "icon": null,
                "formName": null,
                "formCname": "英文注册证号",
                "formCode": "REGISTRATION_ENUM",
                "isForm": true,
                "dataType": null,
                "widgetType": "fixed",
                "dataClass": null,
                "labelHide": false,
                "rules": null,
                "WidgetProps": null,
                "propslist": null,
                "labelStyle": null
              }
            ]
          },
          {
            "colId": "col_111966d5",
            "span": 8,
            "items": [
              {
                "itemId": "item_553867c2",
                "group": null,
                "formId": null,
                "icon": null,
                "formName": null,
                "formCname": "联系方式",
                "formCode": "CONTACT_PHONE",
                "isForm": true,
                "dataType": null,
                "widgetType": "fixed",
                "dataClass": null,
                "labelHide": false,
                "rules": null,
                "WidgetProps": null,
                "propslist": null,
                "labelStyle": null
              }
            ]
          }
        ]
      },
      {
        "rowId": "row_0105e9d0",
        "cols": [
          {
            "colId": "col_0105a709",
            "span": 8,
            "items": [
              {
                "itemId": "item_010578b5",
                "group": null,
                "formId": null,
                "icon": null,
                "formName": null,
                "formCname": "专业分类",
                "formCode": "PROFESSIONAL_CLASS",
                "isForm": true,
                "dataType": null,
                "widgetType": "fixed",
                "dataClass": null,
                "labelHide": false,
                "rules": null,
                "WidgetProps": null,
                "propslist": null,
                "labelStyle": null
              }
            ]
          },
          {
            "colId": "col_01050ed4",
            "span": 8,
            "items": [
              {
                "itemId": "item_303588c3",
                "group": null,
                "formId": null,
                "icon": null,
                "formName": null,
                "formCname": "安装位置",
                "formCode": "INSTALL_AREA",
                "isForm": true,
                "dataType": null,
                "widgetType": "fixed",
                "dataClass": null,
                "labelHide": false,
                "rules": null,
                "WidgetProps": null,
                "propslist": null,
                "labelStyle": null
              }
            ]
          },
          {
            "colId": "col_30357e71",
            "span": 8,
            "items": [
              {
                "itemId": "item_6263962b",
                "group": null,
                "formId": null,
                "icon": null,
                "formName": null,
                "formCname": "备注",
                "formCode": "REMARK",
                "isForm": true,
                "dataType": null,
                "widgetType": "fixed",
                "dataClass": null,
                "labelHide": false,
                "rules": null,
                "WidgetProps": null,
                "propslist": null,
                "labelStyle": null
              }
            ]
          }
        ]
      },
      {
        "rowId": "row_0400a946",
        "cols": [
          {
            "colId": "col_040081a1",
            "span": 8,
            "items": [
              {
                "itemId": "item_040004e8",
                "group": null,
                "formId": null,
                "icon": null,
                "formName": null,
                "formCname": "设备序列号",
                "formCode": "SERIAL_NUMBER",
                "isForm": true,
                "dataType": null,
                "widgetType": "fixed",
                "dataClass": null,
                "labelHide": false,
                "rules": null,
                "WidgetProps": null,
                "propslist": null,
                "labelStyle": null
              }
            ]
          },
          {
            "colId": "col_0400acf7",
            "span": 8,
            "items": [
              {
                "itemId": "item_5148d9b1",
                "group": null,
                "formId": null,
                "icon": null,
                "formName": null,
                "formCname": "所属流水线",
                "formCode": "VEST_PIPELINE",
                "isForm": true,
                "dataType": null,
                "widgetType": "fixed",
                "dataClass": null,
                "labelHide": false,
                "rules": null,
                "WidgetProps": null,
                "propslist": null,
                "labelStyle": null
              }
            ]
          },
          {
            "colId": "col_51480e3e",
            "span": 8,
            "items": [
              {
                "itemId": "item_81710347",
                "group": null,
                "formId": null,
                "icon": null,
                "formName": null,
                "formCname": "生物安全",
                "formCode": "SMBL_FLAG",
                "isForm": true,
                "dataType": null,
                "widgetType": "fixed",
                "dataClass": null,
                "labelHide": false,
                "rules": null,
                "WidgetProps": null,
                "propslist": null,
                "labelStyle": null
              }
            ]
          }
        ]
      },
      {
        "rowId": "row_387138ec",
        "cols": [
          {
            "colId": "col_38715460",
            "span": 8,
            "items": [
              {
                "itemId": "item_38719b67",
                "group": null,
                "formId": null,
                "icon": null,
                "formName": null,
                "formCname": "备案实验室",
                "formCode": "SMBL_LAB_ID",
                "isForm": true,
                "dataType": null,
                "widgetType": "fixed",
                "dataClass": null,
                "labelHide": false,
                "rules": null,
                "WidgetProps": null,
                "propslist": null,
                "labelStyle": null
              }
            ]
          },
          {
            "colId": "col_3871e71e",
            "span": 8,
            "items": [
              {
                "itemId": "item_214907fc",
                "group": null,
                "formId": null,
                "icon": null,
                "formName": null,
                "formCname": "设备状态",
                "formCode": "EQUIPMENT_STATE",
                "isForm": true,
                "dataType": null,
                "widgetType": "fixed",
                "dataClass": null,
                "labelHide": false,
                "rules": null,
                "WidgetProps": null,
                "propslist": null,
                "labelStyle": null
              }
            ]
          },
          {
            "colId": "col_7619678e",
            "span": 8,
            "items": []
          }
        ]
      },
      {
        "rowId": "row_8254f630",
        "cols": [
          {
            "colId": "col_8254c86d",
            "span": 24,
            "items": [
              {
                "itemId": "item_82545177",
                "group": null,
                "formId": null,
                "icon": null,
                "formName": null,
                "formCname": "设备自定义代号",
                "formCode": "EQUIPMENT_UCODE",
                "isForm": true,
                "dataType": null,
                "widgetType": "fixed",
                "dataClass": null,
                "labelHide": false,
                "rules": null,
                "WidgetProps": null,
                "propslist": null,
                "labelStyle": null
              }
            ]
          }
        ]
      },
      {
        "rowId": "row_3069f084",
        "cols": [
          {
            "colId": "col_30699988",
            "span": 8,
            "items": [
              {
                "itemId": "item_306977cd",
                "group": null,
                "formId": null,
                "icon": null,
                "formName": null,
                "formCname": "出厂日期",
                "formCode": "EQ_OUT_TIME",
                "isForm": true,
                "dataType": null,
                "widgetType": "fixed",
                "dataClass": null,
                "labelHide": false,
                "rules": null,
                "WidgetProps": null,
                "propslist": null,
                "labelStyle": null
              }
            ]
          },
          {
            "colId": "col_3069226a",
            "span": 8,
            "items": [
              {
                "itemId": "item_9289efc7",
                "group": null,
                "formId": null,
                "icon": null,
                "formName": null,
                "formCname": "到货日期",
                "formCode": "EQ_IN_TIME",
                "isForm": true,
                "dataType": null,
                "widgetType": "fixed",
                "dataClass": null,
                "labelHide": false,
                "rules": null,
                "WidgetProps": null,
                "propslist": null,
                "labelStyle": null
              }
            ]
          },
          {
            "colId": "col_9289675a",
            "span": 8,
            "items": [
              {
                "itemId": "item_225722c7",
                "group": null,
                "formId": null,
                "icon": null,
                "formName": null,
                "formCname": "安装日期",
                "formCode": "INSTALL_DATE",
                "isForm": true,
                "dataType": null,
                "widgetType": "fixed",
                "dataClass": null,
                "labelHide": false,
                "rules": null,
                "WidgetProps": null,
                "propslist": null,
                "labelStyle": null
              }
            ]
          }
        ]
      },
      {
        "rowId": "row_464155ab",
        "cols": [
          {
            "colId": "col_4641a8d1",
            "span": 8,
            "items": [
              {
                "itemId": "item_4641f9a0",
                "group": null,
                "formId": null,
                "icon": null,
                "formName": null,
                "formCname": "首次启用日期",
                "formCode": "ENABLE_TIME",
                "isForm": true,
                "dataType": null,
                "widgetType": "fixed",
                "dataClass": null,
                "labelHide": false,
                "rules": null,
                "WidgetProps": null,
                "propslist": null,
                "labelStyle": null
              }
            ]
          },
          {
            "colId": "col_46412677",
            "span": 8,
            "items": [
              {
                "itemId": "item_6723a22b",
                "group": null,
                "formId": null,
                "icon": null,
                "formName": null,
                "formCname": "折旧年限",
                "formCode": "DEPRECIATION_TIME",
                "isForm": true,
                "dataType": null,
                "widgetType": "fixed",
                "dataClass": null,
                "labelHide": false,
                "rules": null,
                "WidgetProps": null,
                "propslist": null,
                "labelStyle": null
              }
            ]
          },
          {
            "colId": "col_67237d9f",
            "span": 8,
            "items": [
              {
                "itemId": "item_938329e3",
                "group": null,
                "formId": null,
                "icon": null,
                "formName": null,
                "formCname": "设备价格",
                "formCode": "SELL_PRICE",
                "isForm": true,
                "dataType": null,
                "widgetType": "fixed",
                "dataClass": null,
                "labelHide": false,
                "rules": null,
                "WidgetProps": null,
                "propslist": null,
                "labelStyle": null
              }
            ]
          }
        ]
      },
      {
        "rowId": "row_9760b33c",
        "cols": [
          {
            "colId": "col_97605cd0",
            "span": 8,
            "items": [
              {
                "itemId": "item_97607435",
                "group": null,
                "formId": null,
                "icon": null,
                "formName": null,
                "formCname": "所属专业组",
                "formCode": "UNIT_ID",
                "isForm": true,
                "dataType": null,
                "widgetType": "fixed",
                "dataClass": null,
                "labelHide": false,
                "rules": null,
                "WidgetProps": null,
                "propslist": null,
                "labelStyle": null
              }
            ]
          },
          {
            "colId": "col_9760d961",
            "span": 8,
            "items": [
              {
                "itemId": "item_1124f329",
                "group": null,
                "formId": null,
                "icon": null,
                "formName": null,
                "formCname": "科室设备编号",
                "formCode": "DEPT_SECTION_NO",
                "isForm": true,
                "dataType": null,
                "widgetType": "fixed",
                "dataClass": null,
                "labelHide": false,
                "rules": null,
                "WidgetProps": null,
                "propslist": null,
                "labelStyle": null
              }
            ]
          },
          {
            "colId": "col_1124de22",
            "span": 8,
            "items": [
              {
                "itemId": "item_2396ec91",
                "group": null,
                "formId": null,
                "icon": null,
                "formName": null,
                "formCname": "设备型号",
                "formCode": "EQUIPMENT_MODEL",
                "isForm": true,
                "dataType": null,
                "widgetType": "fixed",
                "dataClass": null,
                "labelHide": false,
                "rules": null,
                "WidgetProps": null,
                "propslist": null,
                "labelStyle": null
              }
            ]
          }
        ]
      },
      {
        "rowId": "row_390802d0",
        "cols": [
          {
            "colId": "col_390810cb",
            "span": 8,
            "items": [
              {
                "itemId": "item_39080e48",
                "group": null,
                "formId": null,
                "icon": null,
                "formName": null,
                "formCname": "使用年限",
                "formCode": "EQ_SERVICE_LIFE",
                "isForm": true,
                "dataType": null,
                "widgetType": "fixed",
                "dataClass": null,
                "labelHide": false,
                "rules": null,
                "WidgetProps": null,
                "propslist": null,
                "labelStyle": null
              }
            ]
          },
          {
            "colId": "col_39083616",
            "span": 8,
            "items": [
              {
                "itemId": "item_5180c302",
                "group": null,
                "formId": null,
                "icon": null,
                "formName": null,
                "formCname": "设备产出地",
                "formCode": "COUNTRY_ORIGIN",
                "isForm": true,
                "dataType": null,
                "widgetType": "fixed",
                "dataClass": null,
                "labelHide": false,
                "rules": null,
                "WidgetProps": null,
                "propslist": null,
                "labelStyle": null
              }
            ]
          },
          {
            "colId": "col_518068a5",
            "span": 8,
            "items": []
          }
        ]
      }
    ]
  },
  "formColJson": {
    "form": [
      {
        "formName": "设备名称",
        "formCname": "设备名称",
        "formCode": "EQUIPMENT_NAME",
        "ifNew": null,
        "ifShow": true,
        "titleShow": true,
        "titleColor": "#000000",
        "titleSize": 13,
        "titleStyle": "0",
        "titleBackground": null,
        "titleAlign": "3",
        "contentLine": 1,
        "contentMaxLine": 2,
        "contentHeightClass": 1,
        "contentHeightRatio": null,
        "contentAlign": "1",
        "contentColor": "#000000",
        "contentFontSize": 13,
        "contentStyle": "0",
        "contentBackground": null,
        "titleAndContentType": "1",
        "contentEnlarge": null,
        "ifRequired": null,
        "replaceField": null,
        "onlyRead": "1",
        "default": null,
        "resetContent": null,
        "dataType": null,
        "dataClass": null,
        "allowMaintainDropDownData": false,
        "formDesc": null,
        "suffix": null,
        "sort": null,
        "editeState": false,
        "unitFlag": false
      },
      {
        "formName": "设备序号",
        "formCname": "设备序号",
        "formCode": "EQUIPMENT_NUM",
        "ifNew": null,
        "ifShow": true,
        "titleShow": true,
        "titleColor": "#000000",
        "titleSize": 13,
        "titleStyle": "0",
        "titleBackground": null,
        "titleAlign": "3",
        "contentLine": 1,
        "contentMaxLine": 2,
        "contentHeightClass": 1,
        "contentHeightRatio": null,
        "contentAlign": "1",
        "contentColor": "#000000",
        "contentFontSize": 13,
        "contentStyle": "0",
        "contentBackground": null,
        "titleAndContentType": "1",
        "contentEnlarge": null,
        "ifRequired": null,
        "replaceField": null,
        "onlyRead": "1",
        "default": null,
        "resetContent": null,
        "dataType": null,
        "dataClass": null,
        "allowMaintainDropDownData": false,
        "formDesc": null,
        "suffix": null,
        "sort": null,
        "editeState": false,
        "unitFlag": false
      },
      {
        "formName": "所属医疗机构",
        "formCname": "所属医疗机构",
        "formCode": "HOSPITAL_ID",
        "ifNew": null,
        "ifShow": true,
        "titleShow": true,
        "titleColor": "#000000",
        "titleSize": 13,
        "titleStyle": "0",
        "titleBackground": null,
        "titleAlign": "3",
        "contentLine": 1,
        "contentMaxLine": 2,
        "contentHeightClass": 1,
        "contentHeightRatio": null,
        "contentAlign": "1",
        "contentColor": "#000000",
        "contentFontSize": 13,
        "contentStyle": "0",
        "contentBackground": null,
        "titleAndContentType": "1",
        "contentEnlarge": null,
        "ifRequired": null,
        "replaceField": null,
        "onlyRead": "1",
        "default": null,
        "resetContent": null,
        "dataType": null,
        "dataClass": null,
        "allowMaintainDropDownData": false,
        "formDesc": null,
        "suffix": null,
        "sort": null,
        "editeState": false,
        "unitFlag": false
      },
      {
        "formName": "设备英文名称",
        "formCname": "设备英文名称",
        "formCode": "EQUIPMENT_ENAME",
        "ifNew": null,
        "ifShow": true,
        "titleShow": true,
        "titleColor": "#000000",
        "titleSize": 13,
        "titleStyle": "0",
        "titleBackground": null,
        "titleAlign": "3",
        "contentLine": 1,
        "contentMaxLine": 2,
        "contentHeightClass": 1,
        "contentHeightRatio": null,
        "contentAlign": "1",
        "contentColor": "#000000",
        "contentFontSize": 13,
        "contentStyle": "0",
        "contentBackground": null,
        "titleAndContentType": "1",
        "contentEnlarge": null,
        "ifRequired": null,
        "replaceField": null,
        "onlyRead": "1",
        "default": null,
        "resetContent": null,
        "dataType": null,
        "dataClass": null,
        "allowMaintainDropDownData": false,
        "formDesc": null,
        "suffix": null,
        "sort": null,
        "editeState": false,
        "unitFlag": false
      },
      {
        "formName": "医院设备编号",
        "formCname": "医院设备编号",
        "formCode": "SECTION_NO",
        "ifNew": null,
        "ifShow": true,
        "titleShow": true,
        "titleColor": "#000000",
        "titleSize": 13,
        "titleStyle": "0",
        "titleBackground": null,
        "titleAlign": "3",
        "contentLine": 1,
        "contentMaxLine": 2,
        "contentHeightClass": 1,
        "contentHeightRatio": null,
        "contentAlign": "1",
        "contentColor": "#000000",
        "contentFontSize": 13,
        "contentStyle": "0",
        "contentBackground": null,
        "titleAndContentType": "1",
        "contentEnlarge": null,
        "ifRequired": null,
        "replaceField": null,
        "onlyRead": "1",
        "default": null,
        "resetContent": null,
        "dataType": null,
        "dataClass": null,
        "allowMaintainDropDownData": false,
        "formDesc": null,
        "suffix": null,
        "sort": null,
        "editeState": false,
        "unitFlag": false
      },
      {
        "formName": "所属科室",
        "formCname": "所属科室",
        "formCode": "LAB_ID",
        "ifNew": null,
        "ifShow": true,
        "titleShow": true,
        "titleColor": "#000000",
        "titleSize": 13,
        "titleStyle": "0",
        "titleBackground": null,
        "titleAlign": "3",
        "contentLine": 1,
        "contentMaxLine": 2,
        "contentHeightClass": 1,
        "contentHeightRatio": null,
        "contentAlign": "1",
        "contentColor": "#000000",
        "contentFontSize": 13,
        "contentStyle": "0",
        "contentBackground": null,
        "titleAndContentType": "1",
        "contentEnlarge": null,
        "ifRequired": null,
        "replaceField": null,
        "onlyRead": "1",
        "default": null,
        "resetContent": null,
        "dataType": null,
        "dataClass": null,
        "allowMaintainDropDownData": false,
        "formDesc": null,
        "suffix": null,
        "sort": null,
        "editeState": false,
        "unitFlag": false
      },
      {
        "formName": "设备价格",
        "formCname": "设备价格",
        "formCode": "SELL_PRICE",
        "ifNew": null,
        "ifShow": true,
        "titleShow": true,
        "titleColor": "#000000",
        "titleSize": 13,
        "titleStyle": "0",
        "titleBackground": null,
        "titleAlign": "3",
        "contentLine": 1,
        "contentMaxLine": 2,
        "contentHeightClass": 1,
        "contentHeightRatio": null,
        "contentAlign": "1",
        "contentColor": "#000000",
        "contentFontSize": 13,
        "contentStyle": "0",
        "contentBackground": null,
        "titleAndContentType": "1",
        "contentEnlarge": null,
        "ifRequired": null,
        "replaceField": null,
        "onlyRead": "1",
        "default": null,
        "resetContent": null,
        "dataType": null,
        "dataClass": null,
        "allowMaintainDropDownData": false,
        "formDesc": null,
        "suffix": null,
        "sort": null,
        "editeState": false,
        "unitFlag": false
      },
      {
        "formName": "设备代号",
        "formCname": "设备代号",
        "formCode": "EQUIPMENT_CODE",
        "ifNew": null,
        "ifShow": true,
        "titleShow": true,
        "titleColor": "#000000",
        "titleSize": 13,
        "titleStyle": "0",
        "titleBackground": null,
        "titleAlign": "3",
        "contentLine": 1,
        "contentMaxLine": 2,
        "contentHeightClass": 1,
        "contentHeightRatio": null,
        "contentAlign": "1",
        "contentColor": "#000000",
        "contentFontSize": 13,
        "contentStyle": "0",
        "contentBackground": null,
        "titleAndContentType": "1",
        "contentEnlarge": null,
        "ifRequired": null,
        "replaceField": null,
        "onlyRead": "1",
        "default": null,
        "resetContent": null,
        "dataType": null,
        "dataClass": null,
        "allowMaintainDropDownData": false,
        "formDesc": null,
        "suffix": null,
        "sort": null,
        "editeState": false,
        "unitFlag": false
      },
      {
        "formName": "所属流水线",
        "formCname": "所属流水线",
        "formCode": "VEST_PIPELINE",
        "ifNew": null,
        "ifShow": true,
        "titleShow": true,
        "titleColor": "#000000",
        "titleSize": 13,
        "titleStyle": "0",
        "titleBackground": null,
        "titleAlign": "3",
        "contentLine": 1,
        "contentMaxLine": 2,
        "contentHeightClass": 1,
        "contentHeightRatio": null,
        "contentAlign": "1",
        "contentColor": "#000000",
        "contentFontSize": 13,
        "contentStyle": "0",
        "contentBackground": null,
        "titleAndContentType": "1",
        "contentEnlarge": null,
        "ifRequired": null,
        "replaceField": null,
        "onlyRead": "1",
        "default": null,
        "resetContent": null,
        "dataType": null,
        "dataClass": null,
        "allowMaintainDropDownData": false,
        "formDesc": null,
        "suffix": null,
        "sort": null,
        "editeState": false,
        "unitFlag": false
      },
      {
        "formName": "生物安全",
        "formCname": "生物安全",
        "formCode": "SMBL_FLAG",
        "ifNew": null,
        "ifShow": true,
        "titleShow": true,
        "titleColor": "#000000",
        "titleSize": 13,
        "titleStyle": "0",
        "titleBackground": null,
        "titleAlign": "3",
        "contentLine": 1,
        "contentMaxLine": 2,
        "contentHeightClass": 1,
        "contentHeightRatio": null,
        "contentAlign": "1",
        "contentColor": "#000000",
        "contentFontSize": 13,
        "contentStyle": "0",
        "contentBackground": null,
        "titleAndContentType": "1",
        "contentEnlarge": null,
        "ifRequired": null,
        "replaceField": null,
        "onlyRead": "1",
        "default": null,
        "resetContent": null,
        "dataType": null,
        "dataClass": null,
        "allowMaintainDropDownData": false,
        "formDesc": null,
        "suffix": null,
        "sort": null,
        "editeState": false,
        "unitFlag": false
      },
      {
        "formName": "安装位置",
        "formCname": "安装位置",
        "formCode": "INSTALL_AREA",
        "ifNew": null,
        "ifShow": true,
        "titleShow": true,
        "titleColor": "#000000",
        "titleSize": 13,
        "titleStyle": "0",
        "titleBackground": null,
        "titleAlign": "3",
        "contentLine": 1,
        "contentMaxLine": 2,
        "contentHeightClass": 1,
        "contentHeightRatio": null,
        "contentAlign": "1",
        "contentColor": "#000000",
        "contentFontSize": 13,
        "contentStyle": "0",
        "contentBackground": null,
        "titleAndContentType": "1",
        "contentEnlarge": null,
        "ifRequired": null,
        "replaceField": null,
        "onlyRead": "1",
        "default": null,
        "resetContent": null,
        "dataType": null,
        "dataClass": null,
        "allowMaintainDropDownData": false,
        "formDesc": null,
        "suffix": null,
        "sort": null,
        "editeState": false,
        "unitFlag": false
      },
      {
        "formName": "备注",
        "formCname": "备注",
        "formCode": "REMARK",
        "ifNew": null,
        "ifShow": true,
        "titleShow": true,
        "titleColor": "#000000",
        "titleSize": 13,
        "titleStyle": "0",
        "titleBackground": null,
        "titleAlign": "3",
        "contentLine": 1,
        "contentMaxLine": 2,
        "contentHeightClass": 1,
        "contentHeightRatio": null,
        "contentAlign": "1",
        "contentColor": "#000000",
        "contentFontSize": 13,
        "contentStyle": "0",
        "contentBackground": null,
        "titleAndContentType": "1",
        "contentEnlarge": null,
        "ifRequired": null,
        "replaceField": null,
        "onlyRead": "1",
        "default": null,
        "resetContent": null,
        "dataType": null,
        "dataClass": null,
        "allowMaintainDropDownData": false,
        "formDesc": null,
        "suffix": null,
        "sort": null,
        "editeState": false,
        "unitFlag": false
      },
      {
        "formName": "设备序列号",
        "formCname": "设备序列号",
        "formCode": "SERIAL_NUMBER",
        "ifNew": null,
        "ifShow": true,
        "titleShow": true,
        "titleColor": "#000000",
        "titleSize": 13,
        "titleStyle": "0",
        "titleBackground": null,
        "titleAlign": "3",
        "contentLine": 1,
        "contentMaxLine": 2,
        "contentHeightClass": 1,
        "contentHeightRatio": null,
        "contentAlign": "1",
        "contentColor": "#000000",
        "contentFontSize": 13,
        "contentStyle": "0",
        "contentBackground": null,
        "titleAndContentType": "1",
        "contentEnlarge": null,
        "ifRequired": null,
        "replaceField": null,
        "onlyRead": "1",
        "default": null,
        "resetContent": null,
        "dataType": null,
        "dataClass": null,
        "allowMaintainDropDownData": false,
        "formDesc": null,
        "suffix": null,
        "sort": null,
        "editeState": false,
        "unitFlag": false
      },
      {
        "formName": "注册证号",
        "formCname": "注册证号",
        "formCode": "REGISTRATION_NUM",
        "ifNew": null,
        "ifShow": true,
        "titleShow": true,
        "titleColor": "#000000",
        "titleSize": 13,
        "titleStyle": "0",
        "titleBackground": null,
        "titleAlign": "3",
        "contentLine": 1,
        "contentMaxLine": 2,
        "contentHeightClass": 1,
        "contentHeightRatio": null,
        "contentAlign": "1",
        "contentColor": "#000000",
        "contentFontSize": 13,
        "contentStyle": "0",
        "contentBackground": null,
        "titleAndContentType": "1",
        "contentEnlarge": null,
        "ifRequired": null,
        "replaceField": null,
        "onlyRead": "1",
        "default": null,
        "resetContent": null,
        "dataType": null,
        "dataClass": null,
        "allowMaintainDropDownData": false,
        "formDesc": null,
        "suffix": null,
        "sort": null,
        "editeState": false,
        "unitFlag": false
      },
      {
        "formName": "设备责任人",
        "formCname": "设备责任人",
        "formCode": "KEEP_PERSON",
        "ifNew": null,
        "ifShow": true,
        "titleShow": true,
        "titleColor": "#000000",
        "titleSize": 13,
        "titleStyle": "0",
        "titleBackground": null,
        "titleAlign": "3",
        "contentLine": 1,
        "contentMaxLine": 2,
        "contentHeightClass": 1,
        "contentHeightRatio": null,
        "contentAlign": "1",
        "contentColor": "#000000",
        "contentFontSize": 13,
        "contentStyle": "0",
        "contentBackground": null,
        "titleAndContentType": "1",
        "contentEnlarge": null,
        "ifRequired": null,
        "replaceField": null,
        "onlyRead": "1",
        "default": null,
        "resetContent": null,
        "dataType": null,
        "dataClass": null,
        "allowMaintainDropDownData": false,
        "formDesc": null,
        "suffix": null,
        "sort": null,
        "editeState": false,
        "unitFlag": false
      },
      {
        "formName": "设备类型",
        "formCname": "设备类型",
        "formCode": "EQUIPMENT_CLASS",
        "ifNew": null,
        "ifShow": true,
        "titleShow": true,
        "titleColor": "#000000",
        "titleSize": 13,
        "titleStyle": "0",
        "titleBackground": null,
        "titleAlign": "3",
        "contentLine": 1,
        "contentMaxLine": 2,
        "contentHeightClass": 1,
        "contentHeightRatio": null,
        "contentAlign": "1",
        "contentColor": "#000000",
        "contentFontSize": 13,
        "contentStyle": "0",
        "contentBackground": null,
        "titleAndContentType": "1",
        "contentEnlarge": null,
        "ifRequired": null,
        "replaceField": null,
        "onlyRead": "1",
        "default": null,
        "resetContent": null,
        "dataType": null,
        "dataClass": null,
        "allowMaintainDropDownData": false,
        "formDesc": null,
        "suffix": null,
        "sort": null,
        "editeState": false,
        "unitFlag": false
      },
      {
        "formName": "英文注册证号",
        "formCname": "英文注册证号",
        "formCode": "REGISTRATION_ENUM",
        "ifNew": null,
        "ifShow": true,
        "titleShow": true,
        "titleColor": "#000000",
        "titleSize": 13,
        "titleStyle": "0",
        "titleBackground": null,
        "titleAlign": "3",
        "contentLine": 1,
        "contentMaxLine": 2,
        "contentHeightClass": 1,
        "contentHeightRatio": null,
        "contentAlign": "1",
        "contentColor": "#000000",
        "contentFontSize": 13,
        "contentStyle": "0",
        "contentBackground": null,
        "titleAndContentType": "1",
        "contentEnlarge": null,
        "ifRequired": null,
        "replaceField": null,
        "onlyRead": "1",
        "default": null,
        "resetContent": null,
        "dataType": null,
        "dataClass": null,
        "allowMaintainDropDownData": false,
        "formDesc": null,
        "suffix": null,
        "sort": null,
        "editeState": false,
        "unitFlag": false
      },
      {
        "formName": "联系方式",
        "formCname": "联系方式",
        "formCode": "CONTACT_PHONE",
        "ifNew": null,
        "ifShow": true,
        "titleShow": true,
        "titleColor": "#000000",
        "titleSize": 13,
        "titleStyle": "0",
        "titleBackground": null,
        "titleAlign": "3",
        "contentLine": 1,
        "contentMaxLine": 2,
        "contentHeightClass": 1,
        "contentHeightRatio": null,
        "contentAlign": "1",
        "contentColor": "#000000",
        "contentFontSize": 13,
        "contentStyle": "0",
        "contentBackground": null,
        "titleAndContentType": "1",
        "contentEnlarge": null,
        "ifRequired": null,
        "replaceField": null,
        "onlyRead": "1",
        "default": null,
        "resetContent": null,
        "dataType": null,
        "dataClass": null,
        "allowMaintainDropDownData": false,
        "formDesc": null,
        "suffix": null,
        "sort": null,
        "editeState": false,
        "unitFlag": false
      },
      {
        "formName": "专业分类",
        "formCname": "专业分类",
        "formCode": "PROFESSIONAL_CLASS",
        "ifNew": null,
        "ifShow": true,
        "titleShow": true,
        "titleColor": "#000000",
        "titleSize": 13,
        "titleStyle": "0",
        "titleBackground": null,
        "titleAlign": "3",
        "contentLine": 1,
        "contentMaxLine": 2,
        "contentHeightClass": 1,
        "contentHeightRatio": null,
        "contentAlign": "1",
        "contentColor": "#000000",
        "contentFontSize": 13,
        "contentStyle": "0",
        "contentBackground": null,
        "titleAndContentType": "1",
        "contentEnlarge": null,
        "ifRequired": null,
        "replaceField": null,
        "onlyRead": "1",
        "default": null,
        "resetContent": null,
        "dataType": null,
        "dataClass": null,
        "allowMaintainDropDownData": false,
        "formDesc": null,
        "suffix": null,
        "sort": null,
        "editeState": false,
        "unitFlag": false
      },
      {
        "formName": "备案实验室",
        "formCname": "备案实验室",
        "formCode": "SMBL_LAB_ID",
        "ifNew": null,
        "ifShow": true,
        "titleShow": true,
        "titleColor": "#000000",
        "titleSize": 13,
        "titleStyle": "0",
        "titleBackground": null,
        "titleAlign": "3",
        "contentLine": 1,
        "contentMaxLine": 2,
        "contentHeightClass": 1,
        "contentHeightRatio": null,
        "contentAlign": "1",
        "contentColor": "#000000",
        "contentFontSize": 13,
        "contentStyle": "0",
        "contentBackground": null,
        "titleAndContentType": "1",
        "contentEnlarge": null,
        "ifRequired": null,
        "replaceField": null,
        "onlyRead": "1",
        "default": null,
        "resetContent": null,
        "dataType": null,
        "dataClass": null,
        "allowMaintainDropDownData": false,
        "formDesc": null,
        "suffix": null,
        "sort": null,
        "editeState": false,
        "unitFlag": false
      },
      {
        "formName": "设备状态",
        "formCname": "设备状态",
        "formCode": "EQUIPMENT_STATE",
        "ifNew": null,
        "ifShow": true,
        "titleShow": true,
        "titleColor": "#000000",
        "titleSize": 13,
        "titleStyle": "0",
        "titleBackground": null,
        "titleAlign": "3",
        "contentLine": 1,
        "contentMaxLine": 2,
        "contentHeightClass": 1,
        "contentHeightRatio": null,
        "contentAlign": "1",
        "contentColor": "#000000",
        "contentFontSize": 13,
        "contentStyle": "0",
        "contentBackground": null,
        "titleAndContentType": "1",
        "contentEnlarge": null,
        "ifRequired": null,
        "replaceField": null,
        "onlyRead": "1",
        "default": null,
        "resetContent": null,
        "dataType": null,
        "dataClass": null,
        "allowMaintainDropDownData": false,
        "formDesc": null,
        "suffix": null,
        "sort": null,
        "editeState": false,
        "unitFlag": false
      },
      {
        "formName": "设备自定义代号",
        "formCname": "设备自定义代号",
        "formCode": "EQUIPMENT_UCODE",
        "ifNew": null,
        "ifShow": true,
        "titleShow": true,
        "titleColor": "#000000",
        "titleSize": 13,
        "titleStyle": "0",
        "titleBackground": null,
        "titleAlign": "3",
        "contentLine": 1,
        "contentMaxLine": 2,
        "contentHeightClass": 1,
        "contentHeightRatio": null,
        "contentAlign": "1",
        "contentColor": "#000000",
        "contentFontSize": 13,
        "contentStyle": "0",
        "contentBackground": null,
        "titleAndContentType": "1",
        "contentEnlarge": null,
        "ifRequired": null,
        "replaceField": null,
        "onlyRead": "1",
        "default": null,
        "resetContent": null,
        "dataType": null,
        "dataClass": null,
        "allowMaintainDropDownData": false,
        "formDesc": null,
        "suffix": null,
        "sort": null,
        "editeState": false,
        "unitFlag": false
      },
      {
        "formName": "出厂日期",
        "formCname": "出厂日期",
        "formCode": "EQ_OUT_TIME",
        "ifNew": null,
        "ifShow": true,
        "titleShow": true,
        "titleColor": "#000000",
        "titleSize": 13,
        "titleStyle": "0",
        "titleBackground": null,
        "titleAlign": "3",
        "contentLine": 1,
        "contentMaxLine": 2,
        "contentHeightClass": 1,
        "contentHeightRatio": null,
        "contentAlign": "1",
        "contentColor": "#000000",
        "contentFontSize": 13,
        "contentStyle": "0",
        "contentBackground": null,
        "titleAndContentType": "1",
        "contentEnlarge": null,
        "ifRequired": null,
        "replaceField": null,
        "onlyRead": "1",
        "default": null,
        "resetContent": null,
        "dataType": null,
        "dataClass": null,
        "allowMaintainDropDownData": false,
        "formDesc": null,
        "suffix": null,
        "sort": null,
        "editeState": false,
        "unitFlag": false
      },
      {
        "formName": "到货日期",
        "formCname": "到货日期",
        "formCode": "EQ_IN_TIME",
        "ifNew": null,
        "ifShow": true,
        "titleShow": true,
        "titleColor": "#000000",
        "titleSize": 13,
        "titleStyle": "0",
        "titleBackground": null,
        "titleAlign": "3",
        "contentLine": 1,
        "contentMaxLine": 2,
        "contentHeightClass": 1,
        "contentHeightRatio": null,
        "contentAlign": "1",
        "contentColor": "#000000",
        "contentFontSize": 13,
        "contentStyle": "0",
        "contentBackground": null,
        "titleAndContentType": "1",
        "contentEnlarge": null,
        "ifRequired": null,
        "replaceField": null,
        "onlyRead": "1",
        "default": null,
        "resetContent": null,
        "dataType": null,
        "dataClass": null,
        "allowMaintainDropDownData": false,
        "formDesc": null,
        "suffix": null,
        "sort": null,
        "editeState": false,
        "unitFlag": false
      },
      {
        "formName": "安装日期",
        "formCname": "安装日期",
        "formCode": "INSTALL_DATE",
        "ifNew": null,
        "ifShow": true,
        "titleShow": true,
        "titleColor": "#000000",
        "titleSize": 13,
        "titleStyle": "0",
        "titleBackground": null,
        "titleAlign": "3",
        "contentLine": 1,
        "contentMaxLine": 2,
        "contentHeightClass": 1,
        "contentHeightRatio": null,
        "contentAlign": "1",
        "contentColor": "#000000",
        "contentFontSize": 13,
        "contentStyle": "0",
        "contentBackground": null,
        "titleAndContentType": "1",
        "contentEnlarge": null,
        "ifRequired": null,
        "replaceField": null,
        "onlyRead": "1",
        "default": null,
        "resetContent": null,
        "dataType": null,
        "dataClass": null,
        "allowMaintainDropDownData": false,
        "formDesc": null,
        "suffix": null,
        "sort": null,
        "editeState": false,
        "unitFlag": false
      },
      {
        "formName": "首次启用日期",
        "formCname": "首次启用日期",
        "formCode": "ENABLE_TIME",
        "ifNew": null,
        "ifShow": true,
        "titleShow": true,
        "titleColor": "#000000",
        "titleSize": 13,
        "titleStyle": "0",
        "titleBackground": null,
        "titleAlign": "3",
        "contentLine": 1,
        "contentMaxLine": 2,
        "contentHeightClass": 1,
        "contentHeightRatio": null,
        "contentAlign": "1",
        "contentColor": "#000000",
        "contentFontSize": 13,
        "contentStyle": "0",
        "contentBackground": null,
        "titleAndContentType": "1",
        "contentEnlarge": null,
        "ifRequired": null,
        "replaceField": null,
        "onlyRead": "1",
        "default": null,
        "resetContent": null,
        "dataType": null,
        "dataClass": null,
        "allowMaintainDropDownData": false,
        "formDesc": null,
        "suffix": null,
        "sort": null,
        "editeState": false,
        "unitFlag": false
      },
      {
        "formName": "科室设备编号",
        "formCname": "科室设备编号",
        "formCode": "DEPT_SECTION_NO",
        "ifNew": null,
        "ifShow": true,
        "titleShow": true,
        "titleColor": "#000000",
        "titleSize": 13,
        "titleStyle": "0",
        "titleBackground": null,
        "titleAlign": "3",
        "contentLine": 1,
        "contentMaxLine": 2,
        "contentHeightClass": 1,
        "contentHeightRatio": null,
        "contentAlign": "1",
        "contentColor": "#000000",
        "contentFontSize": 13,
        "contentStyle": "0",
        "contentBackground": null,
        "titleAndContentType": "1",
        "contentEnlarge": null,
        "ifRequired": null,
        "replaceField": null,
        "onlyRead": "1",
        "default": null,
        "resetContent": null,
        "dataType": null,
        "dataClass": null,
        "allowMaintainDropDownData": false,
        "formDesc": null,
        "suffix": null,
        "sort": null,
        "editeState": false,
        "unitFlag": false
      },
      {
        "formName": "折旧年限",
        "formCname": "折旧年限",
        "formCode": "DEPRECIATION_TIME",
        "ifNew": null,
        "ifShow": true,
        "titleShow": true,
        "titleColor": "#000000",
        "titleSize": 13,
        "titleStyle": "0",
        "titleBackground": null,
        "titleAlign": "3",
        "contentLine": 1,
        "contentMaxLine": 2,
        "contentHeightClass": 1,
        "contentHeightRatio": null,
        "contentAlign": "1",
        "contentColor": "#000000",
        "contentFontSize": 13,
        "contentStyle": "0",
        "contentBackground": null,
        "titleAndContentType": "1",
        "contentEnlarge": null,
        "ifRequired": null,
        "replaceField": null,
        "onlyRead": "1",
        "default": null,
        "resetContent": null,
        "dataType": null,
        "dataClass": null,
        "allowMaintainDropDownData": false,
        "formDesc": null,
        "suffix": null,
        "sort": null,
        "editeState": false,
        "unitFlag": false
      },
      {
        "formName": "所属专业组",
        "formCname": "所属专业组",
        "formCode": "UNIT_ID",
        "ifNew": null,
        "ifShow": true,
        "titleShow": true,
        "titleColor": "#000000",
        "titleSize": 13,
        "titleStyle": "0",
        "titleBackground": null,
        "titleAlign": "3",
        "contentLine": 1,
        "contentMaxLine": 2,
        "contentHeightClass": 1,
        "contentHeightRatio": null,
        "contentAlign": "1",
        "contentColor": "#000000",
        "contentFontSize": 13,
        "contentStyle": "0",
        "contentBackground": null,
        "titleAndContentType": "1",
        "contentEnlarge": null,
        "ifRequired": null,
        "replaceField": null,
        "onlyRead": "1",
        "default": null,
        "resetContent": null,
        "dataType": null,
        "dataClass": null,
        "allowMaintainDropDownData": false,
        "formDesc": null,
        "suffix": null,
        "sort": null,
        "editeState": false,
        "unitFlag": false
      },
      {
        "formName": "设备型号",
        "formCname": "设备型号",
        "formCode": "EQUIPMENT_MODEL",
        "ifNew": null,
        "ifShow": true,
        "titleShow": true,
        "titleColor": "#000000",
        "titleSize": 13,
        "titleStyle": "0",
        "titleBackground": null,
        "titleAlign": "3",
        "contentLine": 1,
        "contentMaxLine": 2,
        "contentHeightClass": 1,
        "contentHeightRatio": null,
        "contentAlign": "1",
        "contentColor": "#000000",
        "contentFontSize": 13,
        "contentStyle": "0",
        "contentBackground": null,
        "titleAndContentType": "1",
        "contentEnlarge": null,
        "ifRequired": null,
        "replaceField": null,
        "onlyRead": "1",
        "default": null,
        "resetContent": null,
        "dataType": null,
        "dataClass": null,
        "allowMaintainDropDownData": false,
        "formDesc": null,
        "suffix": null,
        "sort": null,
        "editeState": false,
        "unitFlag": false
      },
      {
        "formName": "使用年限",
        "formCname": "使用年限",
        "formCode": "EQ_SERVICE_LIFE",
        "ifNew": null,
        "ifShow": true,
        "titleShow": true,
        "titleColor": "#000000",
        "titleSize": 13,
        "titleStyle": "0",
        "titleBackground": null,
        "titleAlign": "3",
        "contentLine": 1,
        "contentMaxLine": 2,
        "contentHeightClass": 1,
        "contentHeightRatio": null,
        "contentAlign": "1",
        "contentColor": "#000000",
        "contentFontSize": 13,
        "contentStyle": "0",
        "contentBackground": null,
        "titleAndContentType": "1",
        "contentEnlarge": null,
        "ifRequired": null,
        "replaceField": null,
        "onlyRead": "1",
        "default": null,
        "resetContent": null,
        "dataType": null,
        "dataClass": null,
        "allowMaintainDropDownData": false,
        "formDesc": null,
        "suffix": null,
        "sort": null,
        "editeState": false,
        "unitFlag": false
      },
      {
        "formName": "设备产出地",
        "formCname": "设备产出地",
        "formCode": "COUNTRY_ORIGIN",
        "ifNew": null,
        "ifShow": true,
        "titleShow": true,
        "titleColor": "#000000",
        "titleSize": 13,
        "titleStyle": "0",
        "titleBackground": null,
        "titleAlign": "3",
        "contentLine": 1,
        "contentMaxLine": 2,
        "contentHeightClass": 1,
        "contentHeightRatio": null,
        "contentAlign": "1",
        "contentColor": "#000000",
        "contentFontSize": 13,
        "contentStyle": "0",
        "contentBackground": null,
        "titleAndContentType": "1",
        "contentEnlarge": null,
        "ifRequired": null,
        "replaceField": null,
        "onlyRead": "1",
        "default": null,
        "resetContent": null,
        "dataType": null,
        "dataClass": null,
        "allowMaintainDropDownData": false,
        "formDesc": null,
        "suffix": null,
        "sort": null,
        "editeState": false,
        "unitFlag": false
      }
    ]
  }
}
2025-07-16 11:30:48.896 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetComplexForms/1 responded 200 in 14867.2630 ms
2025-07-16 11:30:48.897 +08:00 [INF] 【接口超时阀值预警】 [086172e0fca45630e2463d9df9a00414]接口/api/EquipmentClassNew/GetComplexForms/1,耗时:[14867]毫秒
2025-07-16 11:50:49.172 +08:00 [INF] ==>App Start..2025-07-16 11:50:49
2025-07-16 11:50:49.371 +08:00 [INF] ==>当前模块:H82,6.25.329
2025-07-16 11:50:49.374 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-16 11:50:50.974 +08:00 [INF] ==>基础连接请求完成.
2025-07-16 11:50:51.384 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-16 11:50:51.914 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-16 11:50:52.295 +08:00 [INF] ==>S01版本写入成功:6.25.329
2025-07-16 11:50:53.257 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-16 11:50:53.395 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-16 11:50:53.928 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-16 11:50:53.928 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-16 11:50:54.909 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-16 11:50:57.264 +08:00 [INF] ==>初始化完成..
2025-07-16 11:50:57.287 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-16 11:50:57.290 +08:00 [INF] 设备启用任务
2025-07-16 11:50:57.291 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-16 11:50:57.682 +08:00 [INF] 【SQL执行耗时:368.0248ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-16 11:50:57.833 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-16 11:50:57.848 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-16 11:50:57.850 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-16 11:50:57.850 +08:00 [INF] Hosting environment: Development
2025-07-16 11:50:57.850 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-16 11:52:11.432 +08:00 [INF] 【SQL执行耗时:414.2394ms】

[Sql]: SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON"  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "CLASS_ID" = :ClassId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::ClassId0 [Value]:10002 [Type]:String    

2025-07-16 11:52:26.753 +08:00 [INF] 调用H04模块[/externalapi/External/CreateComplexForms],耗时:268ms
2025-07-16 11:52:26.756 +08:00 [INF] 调用H04模块/externalapi/External/CreateComplexForms请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 11:52:26.792 +08:00 [INF] HTTP POST /api/EquipmentClassNew/CreateComplexForms/10002 responded 200 in 27971.1004 ms
2025-07-16 11:52:26.796 +08:00 [INF] 【接口超时阀值预警】 [2f3f5634b540295ad6b954280cfa4d94]接口/api/EquipmentClassNew/CreateComplexForms/10002,耗时:[27979]毫秒
2025-07-16 11:57:07.520 +08:00 [INF] HTTP POST /api/EquipmentClassNew/CreateComplexForms/10003 responded 401 in 19.9667 ms
2025-07-16 11:57:10.522 +08:00 [INF] HTTP POST /api/EquipmentClassNew/CreateComplexForms/10003 responded 401 in 1.1992 ms
2025-07-16 11:57:15.104 +08:00 [INF] HTTP POST /api/EquipmentClassNew/CreateComplexForms/10003 responded 401 in 1.2663 ms
2025-07-16 11:57:22.552 +08:00 [INF] 【SQL执行耗时:506.6361ms】

[Sql]: SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON"  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "CLASS_ID" = :ClassId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::ClassId0 [Value]:10003 [Type]:String    

2025-07-16 11:57:48.132 +08:00 [INF] 调用H04模块[/externalapi/External/CreateComplexForms],耗时:2849ms
2025-07-16 11:57:56.836 +08:00 [INF] 调用H04模块/externalapi/External/CreateComplexForms请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 11:57:58.865 +08:00 [INF] HTTP POST /api/EquipmentClassNew/CreateComplexForms/10003 responded 200 in 39352.4779 ms
2025-07-16 11:57:58.865 +08:00 [INF] 【接口超时阀值预警】 [305c23ebc46caea022e7d36074949dd8]接口/api/EquipmentClassNew/CreateComplexForms/10003,耗时:[39352]毫秒
2025-07-16 12:04:00.529 +08:00 [INF] 第三方url地址为：http://************
2025-07-16 12:04:02.299 +08:00 [ERR] 查询出错:中文提示 :  连接数据库过程中发生错误，检查服务器是否正常连接字符串是否正确，错误信息：ORA-12545: Network Transport: Unable to resolve connect hostnameDbType="Oracle";ConfigId="".
English Message : Connection open error . ORA-12545: Network Transport: Unable to resolve connect hostnameDbType="Oracle";ConfigId="" :
 SELECT "SETUP_ID","FUNC_ID","HOSPITAL_ID","FIRST_RPERSON","SKIN_ID","LAST_MTIME","IF_DOUBLE_CLICK","FORM_QUERY_JSON","SETUP_CNAME","SETUP_SORT","SETUP_STATE","SETUP_DESC","FORM_COL_JSON","MODULE_ID","REMARK","FIRST_RTIME","LAST_MPERSON","FORM_JSON","IF_QUERY","SETUP_NAME","SETUP_CLASS"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ( "SETUP_ID" = 'H8205618' ) AND   ROWNUM = 1 
2025-07-16 12:04:05.965 +08:00 [ERR] 未处理的异常::SqlSugar.SqlSugarException: 中文提示 :  连接数据库过程中发生错误，检查服务器是否正常连接字符串是否正确，错误信息：ORA-12545: Network Transport: Unable to resolve connect hostnameDbType="Oracle";ConfigId="".
English Message : Connection open error . ORA-12545: Network Transport: Unable to resolve connect hostnameDbType="Oracle";ConfigId="" 
   at SqlSugar.Check.Exception(Boolean isException, String message, String[] args)
   at SqlSugar.AdoProvider.CheckConnection()
   at SqlSugar.OracleProvider.GetCommand(String sql, SugarParameter[] parameters)
   at SqlSugar.AdoProvider.GetDataReader(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetData[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToList[TResult]()
   at SqlSugar.QueryableProvider`1.ToList()
   at SqlSugar.QueryableProvider`1.First()
   at XH.H82.Services.TemplateDesign.TemplateDesignService.InsertPersonInfoSetting()
   at Castle.Proxies.Invocations.ITemplateDesignService_InsertPersonInfoSetting.InvokeMethodOnTarget()
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.UseCacheWhenFailedAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.CacheAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at Castle.Proxies.ITemplateDesignServiceProxy.InsertPersonInfoSetting()
   at XH.H82.API.Controllers.TestController.TemplateDesign() in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\Controllers\TestController.cs:line 339
   at lambda_method931(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-07-16 12:04:05.968 +08:00 [ERR] HTTP GET /api/Test/TemplateDesign responded 500 in 6002.4148 ms
2025-07-16 12:04:05.969 +08:00 [INF] 【接口超时阀值预警】 [8e3165752c2df584a9e4309d7aabf0c5]接口/api/Test/TemplateDesign,耗时:[6003]毫秒
2025-07-16 12:04:17.234 +08:00 [INF] 第三方url地址为：http://************
2025-07-16 12:04:18.437 +08:00 [INF] HTTP GET /api/Test/TemplateDesign responded 200 in 1250.3968 ms
2025-07-16 12:04:18.439 +08:00 [INF] 【接口超时阀值预警】 [2efab8507b66227759b414e0b5e3f6c2]接口/api/Test/TemplateDesign,耗时:[1253]毫秒
2025-07-16 12:05:58.477 +08:00 [INF] 【SQL执行耗时:363.7747ms】

[Sql]: SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON"  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "CLASS_ID" = :ClassId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::ClassId0 [Value]:10003 [Type]:String    

2025-07-16 12:06:07.079 +08:00 [INF] 调用H04模块[/externalapi/External/CreateComplexForms],耗时:3358ms
2025-07-16 12:06:07.080 +08:00 [INF] 调用H04模块/externalapi/External/CreateComplexForms请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:06:07.083 +08:00 [INF] HTTP POST /api/EquipmentClassNew/CreateComplexForms/10003 responded 200 in 11953.8408 ms
2025-07-16 12:06:07.083 +08:00 [INF] 【接口超时阀值预警】 [86421bee72a427785c124c039c1dfe6f]接口/api/EquipmentClassNew/CreateComplexForms/10003,耗时:[11954]毫秒
2025-07-16 12:06:26.858 +08:00 [INF] 【SQL执行耗时:398.3183ms】

[Sql]: SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON"  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "CLASS_ID" = :ClassId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::ClassId0 [Value]:10001 [Type]:String    

2025-07-16 12:06:27.183 +08:00 [INF] 调用H04模块[/externalapi/External/CreateComplexForms],耗时:285ms
2025-07-16 12:06:27.183 +08:00 [INF] 调用H04模块/externalapi/External/CreateComplexForms请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:06:27.184 +08:00 [INF] HTTP POST /api/EquipmentClassNew/CreateComplexForms/10001 responded 200 in 777.0815 ms
2025-07-16 12:06:27.185 +08:00 [INF] 【接口超时阀值预警】 [dbf710355a01fae5b31387b3ffb4489a]接口/api/EquipmentClassNew/CreateComplexForms/10001,耗时:[777]毫秒
2025-07-16 12:06:42.356 +08:00 [INF] 【SQL执行耗时:358.7861ms】

[Sql]: SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON"  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "CLASS_ID" = :ClassId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::ClassId0 [Value]:10001 [Type]:String    

2025-07-16 12:06:42.644 +08:00 [INF] 调用H04模块[/externalapi/External/CreateComplexForms],耗时:250ms
2025-07-16 12:06:42.645 +08:00 [INF] 调用H04模块/externalapi/External/CreateComplexForms请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:06:42.646 +08:00 [INF] HTTP POST /api/EquipmentClassNew/CreateComplexForms/10001 responded 200 in 701.5745 ms
2025-07-16 12:06:42.646 +08:00 [INF] 【接口超时阀值预警】 [24fa1d53d924ee6d3b6e3562b4c91732]接口/api/EquipmentClassNew/CreateComplexForms/10001,耗时:[701]毫秒
2025-07-16 12:06:49.681 +08:00 [INF] 【SQL执行耗时:429.8305ms】

[Sql]: SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON"  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "CLASS_ID" = :ClassId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::ClassId0 [Value]:10001 [Type]:String    

2025-07-16 12:06:49.963 +08:00 [INF] 调用H04模块[/externalapi/External/CreateComplexForms],耗时:243ms
2025-07-16 12:06:49.964 +08:00 [INF] 调用H04模块/externalapi/External/CreateComplexForms请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:06:49.965 +08:00 [INF] HTTP POST /api/EquipmentClassNew/CreateComplexForms/10001 responded 200 in 752.5736 ms
2025-07-16 12:06:49.965 +08:00 [INF] 【接口超时阀值预警】 [7e763a5eaa5740be69d973564ab6dd53]接口/api/EquipmentClassNew/CreateComplexForms/10001,耗时:[752]毫秒
2025-07-16 12:08:10.131 +08:00 [INF] 【SQL执行耗时:353.2831ms】

[Sql]: SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON"  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "CLASS_ID" = :ClassId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::ClassId0 [Value]:10001 [Type]:String    

2025-07-16 12:08:10.313 +08:00 [INF] 调用H04模块[/externalapi/External/ClearCache?hospitalId=33A001&moduleId=H82],耗时:145ms
2025-07-16 12:08:10.313 +08:00 [INF] 调用H04模块/externalapi/External/ClearCache?hospitalId=33A001&moduleId=H82请求完成,返回了数据:{
  "ValueKind": 1
}
2025-07-16 12:08:10.471 +08:00 [INF] 调用H04模块[/externalapi/External/CreateComplexForms],耗时:156ms
2025-07-16 12:08:10.471 +08:00 [INF] 调用H04模块/externalapi/External/CreateComplexForms请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:08:10.472 +08:00 [INF] HTTP POST /api/EquipmentClassNew/CreateComplexForms/10001 responded 200 in 733.3939 ms
2025-07-16 12:08:10.472 +08:00 [INF] 【接口超时阀值预警】 [068ebdc15164fc7081b6b3fa7135c6f4]接口/api/EquipmentClassNew/CreateComplexForms/10001,耗时:[733]毫秒
2025-07-16 12:09:28.252 +08:00 [INF] ==>App Start..2025-07-16 12:09:28
2025-07-16 12:09:28.666 +08:00 [INF] ==>当前模块:H82,6.25.329
2025-07-16 12:09:28.674 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-16 12:09:30.889 +08:00 [INF] ==>基础连接请求完成.
2025-07-16 12:09:31.273 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-16 12:09:32.342 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-16 12:09:32.656 +08:00 [INF] ==>S01版本写入成功:6.25.329
2025-07-16 12:09:35.355 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-16 12:09:35.937 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-16 12:09:36.539 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-16 12:09:36.540 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-16 12:09:37.612 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-16 12:09:40.433 +08:00 [INF] ==>初始化完成..
2025-07-16 12:09:40.495 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-16 12:09:40.497 +08:00 [INF] 设备启用任务
2025-07-16 12:09:40.497 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-16 12:09:40.918 +08:00 [INF] 【SQL执行耗时:396.4427ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-16 12:09:41.086 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-16 12:09:41.100 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-16 12:09:41.102 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-16 12:09:41.102 +08:00 [INF] Hosting environment: Development
2025-07-16 12:09:41.102 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-16 12:09:49.069 +08:00 [INF] 【SQL执行耗时:449.9277ms】

[Sql]: SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON"  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "CLASS_ID" = :ClassId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::ClassId0 [Value]:10001 [Type]:String    

2025-07-16 12:09:51.164 +08:00 [INF] 调用H04模块[/externalapi/External/ClearCache?hospitalId=33A001&moduleId=H82],耗时:134ms
2025-07-16 12:09:51.167 +08:00 [INF] 调用H04模块/externalapi/External/ClearCache?hospitalId=33A001&moduleId=H82请求完成,返回了数据:{
  "ValueKind": 1
}
2025-07-16 12:09:54.133 +08:00 [INF] 调用H04模块[/externalapi/External/CreateComplexForms],耗时:161ms
2025-07-16 12:09:54.134 +08:00 [INF] 调用H04模块/externalapi/External/CreateComplexForms请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:09:54.157 +08:00 [INF] HTTP POST /api/EquipmentClassNew/CreateComplexForms/10001 responded 200 in 9862.6596 ms
2025-07-16 12:09:54.160 +08:00 [INF] 【接口超时阀值预警】 [904808682db7c75f0f1580363a067251]接口/api/EquipmentClassNew/CreateComplexForms/10001,耗时:[9870]毫秒
2025-07-16 12:13:03.836 +08:00 [INF] 【SQL执行耗时:370.3916ms】

[Sql]: SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON"  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "CLASS_ID" = :ClassId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::ClassId0 [Value]:1 [Type]:String    

2025-07-16 12:13:05.470 +08:00 [INF] 调用H04模块[/externalapi/External/ClearCache?hospitalId=33A001&moduleId=H82],耗时:279ms
2025-07-16 12:13:05.470 +08:00 [INF] 调用H04模块/externalapi/External/ClearCache?hospitalId=33A001&moduleId=H82请求完成,返回了数据:{
  "ValueKind": 1
}
2025-07-16 12:13:05.629 +08:00 [INF] 调用H04模块[/externalapi/External/CreateComplexForms],耗时:157ms
2025-07-16 12:13:05.630 +08:00 [INF] 调用H04模块/externalapi/External/CreateComplexForms请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:13:05.631 +08:00 [INF] HTTP POST /api/EquipmentClassNew/CreateComplexForms/1 responded 200 in 2226.2338 ms
2025-07-16 12:13:05.632 +08:00 [INF] 【接口超时阀值预警】 [2da0a50cec9c43f47624eba341e2321c]接口/api/EquipmentClassNew/CreateComplexForms/1,耗时:[2226]毫秒
2025-07-16 12:14:39.811 +08:00 [INF] 【SQL执行耗时:514.6398ms】

[Sql]: SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON"  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "CLASS_ID" = :ClassId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::ClassId0 [Value]:1 [Type]:String    

2025-07-16 12:14:41.339 +08:00 [INF] 调用H04模块[/externalapi/External/ClearCache?hospitalId=33A001&moduleId=H82],耗时:162ms
2025-07-16 12:14:41.340 +08:00 [INF] 调用H04模块/externalapi/External/ClearCache?hospitalId=33A001&moduleId=H82请求完成,返回了数据:{
  "ValueKind": 1
}
2025-07-16 12:14:41.534 +08:00 [INF] 调用H04模块[/externalapi/External/CreateComplexForms],耗时:193ms
2025-07-16 12:14:41.534 +08:00 [INF] 调用H04模块/externalapi/External/CreateComplexForms请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:14:41.535 +08:00 [INF] HTTP POST /api/EquipmentClassNew/CreateComplexForms/1 responded 200 in 2284.4399 ms
2025-07-16 12:14:41.536 +08:00 [INF] 【接口超时阀值预警】 [4d78cdb8778b5e8713e051d8fc35d894]接口/api/EquipmentClassNew/CreateComplexForms/1,耗时:[2284]毫秒
2025-07-16 12:18:57.880 +08:00 [INF] 【SQL执行耗时:347.4584ms】

[Sql]: SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON"  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "CLASS_ID" = :ClassId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::ClassId0 [Value]:2 [Type]:String    

2025-07-16 12:19:01.761 +08:00 [INF] 调用H04模块[/externalapi/External/ClearCache?hospitalId=33A001&moduleId=H82],耗时:3844ms
2025-07-16 12:19:01.761 +08:00 [INF] 调用H04模块/externalapi/External/ClearCache?hospitalId=33A001&moduleId=H82请求完成,返回了数据:{
  "ValueKind": 1
}
2025-07-16 12:19:02.040 +08:00 [INF] 调用H04模块[/externalapi/External/CreateComplexForms],耗时:278ms
2025-07-16 12:19:02.040 +08:00 [INF] 调用H04模块/externalapi/External/CreateComplexForms请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:19:02.041 +08:00 [INF] HTTP POST /api/EquipmentClassNew/CreateComplexForms/2 responded 200 in 4547.1731 ms
2025-07-16 12:19:02.041 +08:00 [INF] 【接口超时阀值预警】 [84da1a6dd635c1c74789acc65b9f6aaf]接口/api/EquipmentClassNew/CreateComplexForms/2,耗时:[4547]毫秒
2025-07-16 12:20:37.585 +08:00 [INF] 【SQL执行耗时:365.7345ms】

[Sql]: SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON"  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "CLASS_ID" = :ClassId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::ClassId0 [Value]:2 [Type]:String    

2025-07-16 12:20:37.754 +08:00 [INF] 调用H04模块[/externalapi/External/ClearCache?hospitalId=33A001&moduleId=H82],耗时:132ms
2025-07-16 12:20:37.754 +08:00 [INF] 调用H04模块/externalapi/External/ClearCache?hospitalId=33A001&moduleId=H82请求完成,返回了数据:{
  "ValueKind": 1
}
2025-07-16 12:20:37.949 +08:00 [INF] 调用H04模块[/externalapi/External/CreateComplexForms],耗时:194ms
2025-07-16 12:20:37.949 +08:00 [INF] 调用H04模块/externalapi/External/CreateComplexForms请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:20:37.950 +08:00 [INF] HTTP POST /api/EquipmentClassNew/CreateComplexForms/2 responded 200 in 775.4496 ms
2025-07-16 12:20:37.950 +08:00 [INF] 【接口超时阀值预警】 [2e941b0b7b38486e48cd2c36f48fbc32]接口/api/EquipmentClassNew/CreateComplexForms/2,耗时:[775]毫秒
2025-07-16 12:28:05.331 +08:00 [INF] 第三方url地址为：http://************
2025-07-16 12:28:06.424 +08:00 [ERR] 查询出错:中文提示 :  连接数据库过程中发生错误，检查服务器是否正常连接字符串是否正确，错误信息：ORA-12545: Network Transport: Unable to resolve connect hostnameDbType="Oracle";ConfigId="".
English Message : Connection open error . ORA-12545: Network Transport: Unable to resolve connect hostnameDbType="Oracle";ConfigId="" :
 SELECT "SETUP_ID","FUNC_ID","HOSPITAL_ID","FIRST_RPERSON","SKIN_ID","LAST_MTIME","IF_DOUBLE_CLICK","FORM_QUERY_JSON","SETUP_CNAME","SETUP_SORT","SETUP_STATE","SETUP_DESC","FORM_COL_JSON","MODULE_ID","REMARK","FIRST_RTIME","LAST_MPERSON","FORM_JSON","IF_QUERY","SETUP_NAME","SETUP_CLASS"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ( "SETUP_ID" = 'H8205618' ) AND   ROWNUM = 1 
2025-07-16 12:28:12.185 +08:00 [ERR] 未处理的异常::SqlSugar.SqlSugarException: 中文提示 :  连接数据库过程中发生错误，检查服务器是否正常连接字符串是否正确，错误信息：ORA-12545: Network Transport: Unable to resolve connect hostnameDbType="Oracle";ConfigId="".
English Message : Connection open error . ORA-12545: Network Transport: Unable to resolve connect hostnameDbType="Oracle";ConfigId="" 
   at SqlSugar.Check.Exception(Boolean isException, String message, String[] args)
   at SqlSugar.AdoProvider.CheckConnection()
   at SqlSugar.OracleProvider.GetCommand(String sql, SugarParameter[] parameters)
   at SqlSugar.AdoProvider.GetDataReader(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetData[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToList[TResult]()
   at SqlSugar.QueryableProvider`1.ToList()
   at SqlSugar.QueryableProvider`1.First()
   at XH.H82.Services.TemplateDesign.TemplateDesignService.InsertPersonInfoSetting() in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\TemplateDesign\TemplateDesignService.cs:line 185
   at Castle.Proxies.Invocations.ITemplateDesignService_InsertPersonInfoSetting.InvokeMethodOnTarget()
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.UseCacheWhenFailedAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.CacheAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at Castle.Proxies.ITemplateDesignServiceProxy.InsertPersonInfoSetting()
   at XH.H82.API.Controllers.TestController.TemplateDesign()
   at lambda_method931(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-07-16 12:28:12.188 +08:00 [ERR] HTTP GET /api/Test/TemplateDesign responded 500 in 7238.9868 ms
2025-07-16 12:28:12.188 +08:00 [INF] 【接口超时阀值预警】 [97c79e0152b5ed4d3acbdac38c5a4fad]接口/api/Test/TemplateDesign,耗时:[7239]毫秒
2025-07-16 12:28:14.059 +08:00 [INF] 第三方url地址为：http://************
2025-07-16 12:28:15.265 +08:00 [INF] HTTP GET /api/Test/TemplateDesign responded 200 in 1251.1515 ms
2025-07-16 12:28:15.266 +08:00 [INF] 【接口超时阀值预警】 [d0f6f86a9d73cbe48a2e4b58192e1159]接口/api/Test/TemplateDesign,耗时:[1251]毫秒
2025-07-16 12:29:34.460 +08:00 [INF] 第三方url地址为：http://************
2025-07-16 12:29:35.465 +08:00 [INF] HTTP GET /api/Test/TemplateDesign responded 200 in 1046.2751 ms
2025-07-16 12:29:35.466 +08:00 [INF] 【接口超时阀值预警】 [7d118843ed4ef1779044841716973a53]接口/api/Test/TemplateDesign,耗时:[1046]毫秒
2025-07-16 12:29:48.221 +08:00 [INF] 【SQL执行耗时:362.1096ms】

[Sql]: SELECT "SETUP_ID","FUNC_ID","HOSPITAL_ID","FIRST_RPERSON","SKIN_ID","LAST_MTIME","IF_DOUBLE_CLICK","FORM_QUERY_JSON","SETUP_CNAME","SETUP_SORT","SETUP_STATE","SETUP_DESC","FORM_COL_JSON","MODULE_ID","REMARK","FIRST_RTIME","LAST_MPERSON","FORM_JSON","IF_QUERY","SETUP_NAME","SETUP_CLASS"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ( "SETUP_ID" = :SETUP_ID0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::SETUP_ID0 [Value]:H8205618 [Type]:AnsiString    

2025-07-16 12:29:48.856 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:317ms
2025-07-16 12:29:49.093 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:234ms
2025-07-16 12:29:49.094 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:29:49.167 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:72ms
2025-07-16 12:29:49.287 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:119ms
2025-07-16 12:29:49.288 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:29:49.360 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:71ms
2025-07-16 12:29:49.479 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:119ms
2025-07-16 12:29:49.480 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:29:49.553 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:72ms
2025-07-16 12:29:49.665 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:111ms
2025-07-16 12:29:49.665 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:29:49.738 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:72ms
2025-07-16 12:29:49.848 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:109ms
2025-07-16 12:29:49.848 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:29:49.922 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:72ms
2025-07-16 12:29:50.033 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:110ms
2025-07-16 12:29:50.033 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:29:50.106 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:71ms
2025-07-16 12:29:50.216 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:109ms
2025-07-16 12:29:50.216 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:29:50.289 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:71ms
2025-07-16 12:29:50.400 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:110ms
2025-07-16 12:29:50.400 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:29:50.473 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:71ms
2025-07-16 12:29:50.583 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:109ms
2025-07-16 12:29:50.583 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:29:50.657 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:73ms
2025-07-16 12:29:50.773 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:115ms
2025-07-16 12:29:50.773 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:29:50.852 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:78ms
2025-07-16 12:29:50.962 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:109ms
2025-07-16 12:29:50.962 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:29:51.035 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:71ms
2025-07-16 12:29:51.145 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:110ms
2025-07-16 12:29:51.146 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:29:51.220 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:73ms
2025-07-16 12:29:51.329 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:108ms
2025-07-16 12:29:51.330 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:29:51.404 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:74ms
2025-07-16 12:29:51.514 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:109ms
2025-07-16 12:29:51.514 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:29:51.590 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:74ms
2025-07-16 12:29:51.701 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:111ms
2025-07-16 12:29:51.702 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:29:51.775 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:71ms
2025-07-16 12:29:51.885 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:110ms
2025-07-16 12:29:51.886 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:29:51.959 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:72ms
2025-07-16 12:29:52.070 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:110ms
2025-07-16 12:29:52.071 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:29:52.144 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:72ms
2025-07-16 12:29:52.254 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:109ms
2025-07-16 12:29:52.254 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:29:52.327 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:72ms
2025-07-16 12:29:52.438 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:110ms
2025-07-16 12:29:52.438 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:29:52.512 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:72ms
2025-07-16 12:29:52.622 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:110ms
2025-07-16 12:29:52.623 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:29:52.696 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:72ms
2025-07-16 12:29:52.817 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:120ms
2025-07-16 12:29:52.817 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:29:52.890 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:71ms
2025-07-16 12:29:53.000 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:109ms
2025-07-16 12:29:53.000 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:29:53.073 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:71ms
2025-07-16 12:29:53.184 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:110ms
2025-07-16 12:29:53.184 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:29:53.257 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:72ms
2025-07-16 12:29:53.371 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:113ms
2025-07-16 12:29:53.372 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:29:53.445 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:72ms
2025-07-16 12:29:53.556 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:110ms
2025-07-16 12:29:53.557 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:29:53.629 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:71ms
2025-07-16 12:29:53.740 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:110ms
2025-07-16 12:29:53.740 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:29:53.815 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:73ms
2025-07-16 12:29:53.926 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:111ms
2025-07-16 12:29:53.927 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:29:54.000 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:72ms
2025-07-16 12:29:54.114 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:112ms
2025-07-16 12:29:54.114 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:29:54.187 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:72ms
2025-07-16 12:29:54.296 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:108ms
2025-07-16 12:29:54.297 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:29:54.369 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:71ms
2025-07-16 12:29:54.479 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:109ms
2025-07-16 12:29:54.479 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:29:54.552 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:72ms
2025-07-16 12:29:54.663 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:110ms
2025-07-16 12:29:54.664 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:29:54.736 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:71ms
2025-07-16 12:29:54.845 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:109ms
2025-07-16 12:29:54.846 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:29:54.847 +08:00 [INF] HTTP POST /api/EquipmentClassNew/InitBaseInfoFieldDict responded 200 in 7028.1549 ms
2025-07-16 12:29:54.848 +08:00 [INF] 【接口超时阀值预警】 [8f26529c8948a56f938f7398276b0bff]接口/api/EquipmentClassNew/InitBaseInfoFieldDict,耗时:[7028]毫秒
2025-07-16 12:31:51.667 +08:00 [INF] 【SQL执行耗时:368.4638ms】

[Sql]: SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON"  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "CLASS_ID" = :ClassId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::ClassId0 [Value]:3 [Type]:String    

2025-07-16 12:31:51.843 +08:00 [INF] 调用H04模块[/externalapi/External/ClearCache?hospitalId=33A001&moduleId=H82],耗时:139ms
2025-07-16 12:31:51.843 +08:00 [INF] 调用H04模块/externalapi/External/ClearCache?hospitalId=33A001&moduleId=H82请求完成,返回了数据:{
  "ValueKind": 1
}
2025-07-16 12:31:52.042 +08:00 [INF] 调用H04模块[/externalapi/External/CreateComplexForms],耗时:198ms
2025-07-16 12:31:52.043 +08:00 [INF] 调用H04模块/externalapi/External/CreateComplexForms请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:31:52.044 +08:00 [INF] HTTP POST /api/EquipmentClassNew/CreateComplexForms/3 responded 200 in 783.2170 ms
2025-07-16 12:31:52.044 +08:00 [INF] 【接口超时阀值预警】 [ec11b033afc5e62bacd3fd4f3694c610]接口/api/EquipmentClassNew/CreateComplexForms/3,耗时:[783]毫秒
2025-07-16 12:53:40.283 +08:00 [INF] HTTP POST /api/EquipmentClassNew/InitBaseInfoFieldDict responded 401 in 10.9093 ms
2025-07-16 12:53:42.639 +08:00 [INF] 【SQL执行耗时:439.9334ms】

[Sql]: SELECT "SETUP_ID","FUNC_ID","HOSPITAL_ID","FIRST_RPERSON","SKIN_ID","LAST_MTIME","IF_DOUBLE_CLICK","FORM_QUERY_JSON","SETUP_CNAME","SETUP_SORT","SETUP_STATE","SETUP_DESC","FORM_COL_JSON","MODULE_ID","REMARK","FIRST_RTIME","LAST_MPERSON","FORM_JSON","IF_QUERY","SETUP_NAME","SETUP_CLASS"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ( "SETUP_ID" = :SETUP_ID0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::SETUP_ID0 [Value]:H8205618 [Type]:AnsiString    

2025-07-16 12:54:26.244 +08:00 [INF] ==>App Start..2025-07-16 12:54:26
2025-07-16 12:54:26.437 +08:00 [INF] ==>当前模块:H82,6.25.329
2025-07-16 12:54:26.441 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-16 12:54:28.022 +08:00 [INF] ==>基础连接请求完成.
2025-07-16 12:54:28.448 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-16 12:54:28.890 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-16 12:54:29.237 +08:00 [INF] ==>S01版本写入成功:6.25.329
2025-07-16 12:54:30.036 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-16 12:54:30.133 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-16 12:54:30.600 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-16 12:54:30.600 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-16 12:54:31.725 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-16 12:54:34.044 +08:00 [INF] ==>初始化完成..
2025-07-16 12:54:34.072 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-16 12:54:34.076 +08:00 [INF] 设备启用任务
2025-07-16 12:54:34.077 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-16 12:54:34.501 +08:00 [INF] 【SQL执行耗时:395.0561ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-16 12:54:34.670 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-16 12:54:34.687 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-16 12:54:34.689 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-16 12:54:34.689 +08:00 [INF] Hosting environment: Development
2025-07-16 12:54:34.689 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-16 12:59:00.167 +08:00 [INF] 【SQL执行耗时:505.259ms】

[Sql]: SELECT "SETUP_ID","FUNC_ID","HOSPITAL_ID","FIRST_RPERSON","SKIN_ID","LAST_MTIME","IF_DOUBLE_CLICK","FORM_QUERY_JSON","SETUP_CNAME","SETUP_SORT","SETUP_STATE","SETUP_DESC","FORM_COL_JSON","MODULE_ID","REMARK","FIRST_RTIME","LAST_MPERSON","FORM_JSON","IF_QUERY","SETUP_NAME","SETUP_CLASS"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ( "SETUP_ID" = :SETUP_ID0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::SETUP_ID0 [Value]:H8205618 [Type]:AnsiString    

2025-07-16 12:59:00.835 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:309ms
2025-07-16 12:59:01.052 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:212ms
2025-07-16 12:59:01.055 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:59:01.128 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:71ms
2025-07-16 12:59:01.237 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:108ms
2025-07-16 12:59:01.238 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:59:01.308 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:69ms
2025-07-16 12:59:01.422 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:113ms
2025-07-16 12:59:01.422 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:59:01.494 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:70ms
2025-07-16 12:59:01.612 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:117ms
2025-07-16 12:59:01.613 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:59:01.692 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:78ms
2025-07-16 12:59:01.803 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:110ms
2025-07-16 12:59:01.803 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:59:01.873 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:69ms
2025-07-16 12:59:01.982 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:108ms
2025-07-16 12:59:01.982 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:59:02.053 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:70ms
2025-07-16 12:59:02.163 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:109ms
2025-07-16 12:59:02.163 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:59:02.235 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:71ms
2025-07-16 12:59:02.343 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:108ms
2025-07-16 12:59:02.344 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:59:02.414 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:69ms
2025-07-16 12:59:02.523 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:108ms
2025-07-16 12:59:02.523 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:59:02.593 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:69ms
2025-07-16 12:59:02.703 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:109ms
2025-07-16 12:59:02.703 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:59:02.773 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:69ms
2025-07-16 12:59:02.884 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:110ms
2025-07-16 12:59:02.884 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:59:02.954 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:68ms
2025-07-16 12:59:03.064 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:109ms
2025-07-16 12:59:03.064 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:59:03.154 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:89ms
2025-07-16 12:59:03.274 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:119ms
2025-07-16 12:59:03.275 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:59:03.345 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:70ms
2025-07-16 12:59:03.455 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:109ms
2025-07-16 12:59:03.456 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:59:03.526 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:69ms
2025-07-16 12:59:03.632 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:105ms
2025-07-16 12:59:03.632 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:59:03.702 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:69ms
2025-07-16 12:59:03.809 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:106ms
2025-07-16 12:59:03.810 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:59:03.880 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:69ms
2025-07-16 12:59:03.991 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:110ms
2025-07-16 12:59:03.991 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:59:04.061 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:69ms
2025-07-16 12:59:04.174 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:112ms
2025-07-16 12:59:04.174 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:59:04.245 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:69ms
2025-07-16 12:59:04.354 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:108ms
2025-07-16 12:59:04.354 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:59:04.425 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:69ms
2025-07-16 12:59:04.535 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:109ms
2025-07-16 12:59:04.535 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:59:04.605 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:69ms
2025-07-16 12:59:04.716 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:110ms
2025-07-16 12:59:04.716 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:59:04.786 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:69ms
2025-07-16 12:59:04.896 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:109ms
2025-07-16 12:59:04.896 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:59:04.968 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:70ms
2025-07-16 12:59:05.084 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:116ms
2025-07-16 12:59:05.085 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:59:05.157 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:71ms
2025-07-16 12:59:05.267 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:110ms
2025-07-16 12:59:05.268 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:59:05.353 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:84ms
2025-07-16 12:59:05.463 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:109ms
2025-07-16 12:59:05.463 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:59:05.534 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:70ms
2025-07-16 12:59:05.644 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:109ms
2025-07-16 12:59:05.644 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:59:05.740 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:95ms
2025-07-16 12:59:05.852 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:111ms
2025-07-16 12:59:05.853 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:59:05.943 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:89ms
2025-07-16 12:59:06.055 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:111ms
2025-07-16 12:59:06.056 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:59:06.160 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:103ms
2025-07-16 12:59:06.272 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:111ms
2025-07-16 12:59:06.273 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:59:06.389 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:115ms
2025-07-16 12:59:06.519 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:129ms
2025-07-16 12:59:06.519 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:59:06.609 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:89ms
2025-07-16 12:59:06.721 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:110ms
2025-07-16 12:59:06.721 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:59:06.809 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:86ms
2025-07-16 12:59:06.918 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:109ms
2025-07-16 12:59:06.918 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 12:59:06.955 +08:00 [INF] HTTP POST /api/EquipmentClassNew/InitBaseInfoFieldDict responded 200 in 17997.7486 ms
2025-07-16 12:59:06.958 +08:00 [INF] 【接口超时阀值预警】 [12965873d898c91ba92144a48c2dc3b2]接口/api/EquipmentClassNew/InitBaseInfoFieldDict,耗时:[18005]毫秒
2025-07-16 13:01:16.028 +08:00 [INF] 【SQL执行耗时:439.8803ms】

[Sql]: SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON"  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "CLASS_ID" = :ClassId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::ClassId0 [Value]:1 [Type]:String    

2025-07-16 13:01:16.210 +08:00 [INF] 调用H04模块[/externalapi/External/ClearCache?hospitalId=33A001&moduleId=H82],耗时:132ms
2025-07-16 13:01:16.210 +08:00 [INF] 调用H04模块/externalapi/External/ClearCache?hospitalId=33A001&moduleId=H82请求完成,返回了数据:{
  "ValueKind": 1
}
2025-07-16 13:01:16.370 +08:00 [INF] 调用H04模块[/externalapi/External/CreateComplexForms],耗时:158ms
2025-07-16 13:01:16.370 +08:00 [INF] 调用H04模块/externalapi/External/CreateComplexForms请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 13:01:16.376 +08:00 [INF] HTTP POST /api/EquipmentClassNew/CreateComplexForms/1 responded 200 in 875.6630 ms
2025-07-16 13:01:16.377 +08:00 [INF] 【接口超时阀值预警】 [ba07e30d8a6f15c3b8cf21534fc42121]接口/api/EquipmentClassNew/CreateComplexForms/1,耗时:[876]毫秒
2025-07-16 13:01:34.648 +08:00 [INF] 【SQL执行耗时:371.0729ms】

[Sql]: SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON"  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "CLASS_ID" = :ClassId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::ClassId0 [Value]:2 [Type]:String    

2025-07-16 13:01:34.816 +08:00 [INF] 调用H04模块[/externalapi/External/ClearCache?hospitalId=33A001&moduleId=H82],耗时:129ms
2025-07-16 13:01:34.816 +08:00 [INF] 调用H04模块/externalapi/External/ClearCache?hospitalId=33A001&moduleId=H82请求完成,返回了数据:{
  "ValueKind": 1
}
2025-07-16 13:01:34.982 +08:00 [INF] 调用H04模块[/externalapi/External/CreateComplexForms],耗时:165ms
2025-07-16 13:01:34.983 +08:00 [INF] 调用H04模块/externalapi/External/CreateComplexForms请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 13:01:34.983 +08:00 [INF] HTTP POST /api/EquipmentClassNew/CreateComplexForms/2 responded 200 in 754.6207 ms
2025-07-16 13:01:34.984 +08:00 [INF] 【接口超时阀值预警】 [3e1cc8aae08949a66cc6099353f59a3c]接口/api/EquipmentClassNew/CreateComplexForms/2,耗时:[754]毫秒
2025-07-16 13:17:27.473 +08:00 [INF] 【SQL执行耗时:527.0617ms】

[Sql]: SELECT "SETUP_ID","FUNC_ID","HOSPITAL_ID","FIRST_RPERSON","SKIN_ID","LAST_MTIME","IF_DOUBLE_CLICK","FORM_QUERY_JSON","SETUP_CNAME","SETUP_SORT","SETUP_STATE","SETUP_DESC","FORM_COL_JSON","MODULE_ID","REMARK","FIRST_RTIME","LAST_MPERSON","FORM_JSON","IF_QUERY","SETUP_NAME","SETUP_CLASS"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ( "SETUP_ID" = :SETUP_ID0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::SETUP_ID0 [Value]:H8205618 [Type]:AnsiString    

2025-07-16 13:17:28.178 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:427ms
2025-07-16 13:17:28.380 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:201ms
2025-07-16 13:17:28.380 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 13:17:28.455 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:74ms
2025-07-16 13:17:28.569 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:112ms
2025-07-16 13:17:28.569 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 13:17:28.639 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:69ms
2025-07-16 13:17:28.752 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:112ms
2025-07-16 13:17:28.752 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 13:17:28.833 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:80ms
2025-07-16 13:17:28.945 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:111ms
2025-07-16 13:17:28.946 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 13:17:29.015 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:68ms
2025-07-16 13:17:29.129 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:113ms
2025-07-16 13:17:29.129 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 13:17:29.201 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:71ms
2025-07-16 13:17:29.319 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:117ms
2025-07-16 13:17:29.320 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 13:17:29.408 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:88ms
2025-07-16 13:17:29.520 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:111ms
2025-07-16 13:17:29.521 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 13:17:29.619 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:97ms
2025-07-16 13:17:29.730 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:110ms
2025-07-16 13:17:29.731 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 13:17:29.801 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:70ms
2025-07-16 13:17:29.911 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:109ms
2025-07-16 13:17:29.912 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 13:17:29.982 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:69ms
2025-07-16 13:17:30.095 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:112ms
2025-07-16 13:17:30.095 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 13:17:30.168 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:71ms
2025-07-16 13:17:30.280 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:111ms
2025-07-16 13:17:30.281 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 13:17:30.350 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:69ms
2025-07-16 13:17:30.461 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:110ms
2025-07-16 13:17:30.461 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 13:17:30.531 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:69ms
2025-07-16 13:17:30.642 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:110ms
2025-07-16 13:17:30.642 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 13:17:30.711 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:68ms
2025-07-16 13:17:30.822 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:109ms
2025-07-16 13:17:30.822 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 13:17:30.892 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:69ms
2025-07-16 13:17:31.002 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:109ms
2025-07-16 13:17:31.003 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 13:17:31.073 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:69ms
2025-07-16 13:17:31.181 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:107ms
2025-07-16 13:17:31.182 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 13:17:31.265 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:82ms
2025-07-16 13:17:31.377 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:111ms
2025-07-16 13:17:31.378 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 13:17:31.449 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:70ms
2025-07-16 13:17:31.562 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:113ms
2025-07-16 13:17:31.563 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 13:17:31.633 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:69ms
2025-07-16 13:17:31.744 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:110ms
2025-07-16 13:17:31.744 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 13:17:31.815 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:69ms
2025-07-16 13:17:31.928 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:112ms
2025-07-16 13:17:31.928 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 13:17:32.000 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:70ms
2025-07-16 13:17:32.110 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:110ms
2025-07-16 13:17:32.111 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 13:17:32.186 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:75ms
2025-07-16 13:17:32.299 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:112ms
2025-07-16 13:17:32.299 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 13:17:32.370 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:69ms
2025-07-16 13:17:32.481 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:111ms
2025-07-16 13:17:32.482 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 13:17:32.551 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:68ms
2025-07-16 13:17:32.662 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:110ms
2025-07-16 13:17:32.662 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 13:17:32.732 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:68ms
2025-07-16 13:17:32.844 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:111ms
2025-07-16 13:17:32.844 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 13:17:32.914 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:69ms
2025-07-16 13:17:33.032 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:116ms
2025-07-16 13:17:33.032 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 13:17:33.103 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:70ms
2025-07-16 13:17:33.212 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:108ms
2025-07-16 13:17:33.212 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 13:17:33.282 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:69ms
2025-07-16 13:17:33.392 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:110ms
2025-07-16 13:17:33.392 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 13:17:33.477 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:84ms
2025-07-16 13:17:33.591 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:113ms
2025-07-16 13:17:33.591 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 13:17:33.661 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:69ms
2025-07-16 13:17:33.776 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:113ms
2025-07-16 13:17:33.776 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 13:17:33.846 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:69ms
2025-07-16 13:17:33.958 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:111ms
2025-07-16 13:17:33.958 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 13:17:34.027 +08:00 [INF] 调用H07模块[/api/External/GetTableMax?tableName=SYS6_FUNC_FIELD_DICT&addCount=1&ifTableMax=1&fieldName=FIELD_ID&dBOwner=XH_SYS],耗时:68ms
2025-07-16 13:17:34.138 +08:00 [INF] 调用H04模块[/externalapi/External/SaveFieldDict],耗时:109ms
2025-07-16 13:17:34.138 +08:00 [INF] 调用H04模块/externalapi/External/SaveFieldDict请求完成,返回了数据:{
  "ValueKind": 4
}
2025-07-16 13:17:34.139 +08:00 [INF] HTTP POST /api/EquipmentClassNew/InitBaseInfoFieldDict responded 200 in 7244.0430 ms
2025-07-16 13:17:34.139 +08:00 [INF] 【接口超时阀值预警】 [258e5b4276f2701afd2bd6941cabd467]接口/api/EquipmentClassNew/InitBaseInfoFieldDict,耗时:[7244]毫秒
2025-07-16 14:43:39.235 +08:00 [INF] ==>App Start..2025-07-16 14:43:39
2025-07-16 14:43:39.469 +08:00 [INF] ==>当前模块:H82,6.25.329
2025-07-16 14:43:39.473 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-16 14:43:41.295 +08:00 [INF] ==>基础连接请求完成.
2025-07-16 14:43:41.721 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-16 14:43:42.183 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-16 14:43:42.536 +08:00 [INF] ==>S01版本写入成功:6.25.329
2025-07-16 14:43:43.793 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-16 14:43:43.923 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-16 14:43:44.513 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-16 14:43:44.513 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-16 14:43:45.746 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-16 14:43:48.228 +08:00 [INF] ==>初始化完成..
2025-07-16 14:43:48.254 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-16 14:43:48.255 +08:00 [INF] 设备启用任务
2025-07-16 14:43:48.256 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-16 14:43:48.656 +08:00 [INF] 【SQL执行耗时:377.5064ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-16 14:43:48.805 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-16 14:43:48.818 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-16 14:43:48.819 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-16 14:43:48.820 +08:00 [INF] Hosting environment: Development
2025-07-16 14:43:48.820 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-16 14:44:29.074 +08:00 [INF] 【SQL执行耗时:365.6353ms】

[Sql]: SELECT "SETUP_ID","FUNC_ID","HOSPITAL_ID","FIRST_RPERSON","SKIN_ID","LAST_MTIME","IF_DOUBLE_CLICK","FORM_QUERY_JSON","SETUP_CNAME","SETUP_SORT","SETUP_STATE","SETUP_DESC","FORM_COL_JSON","MODULE_ID","REMARK","FIRST_RTIME","LAST_MPERSON","FORM_JSON","IF_QUERY","SETUP_NAME","SETUP_CLASS"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ((( "MODULE_ID" = :MODULE_ID0 ) AND ( "HOSPITAL_ID" = :HOSPITAL_ID1 )) AND ( "SETUP_NAME" = :SETUP_NAME2 )) AND   ROWNUM = 1  
[Pars]:
[Name]::MODULE_ID0 [Value]:H82 [Type]:AnsiString    
[Name]::HOSPITAL_ID1 [Value]:33A001 [Type]:AnsiString    
[Name]::SETUP_NAME2 [Value]:导航栏 [Type]:AnsiString    

2025-07-16 14:44:29.796 +08:00 [INF] HTTP GET /api/Base/GetMenuInfo responded 200 in 6416.8618 ms
2025-07-16 14:44:29.799 +08:00 [INF] 【接口超时阀值预警】 [d086aa870d95103a0c496d048537fd86]接口/api/Base/GetMenuInfo,耗时:[6425]毫秒
2025-07-16 14:44:37.747 +08:00 [INF] 【SQL执行耗时:369.9878ms】

[Sql]: SELECT "SETUP_ID","FUNC_ID","HOSPITAL_ID","FIRST_RPERSON","SKIN_ID","LAST_MTIME","IF_DOUBLE_CLICK","FORM_QUERY_JSON","SETUP_CNAME","SETUP_SORT","SETUP_STATE","SETUP_DESC","FORM_COL_JSON","MODULE_ID","REMARK","FIRST_RTIME","LAST_MPERSON","FORM_JSON","IF_QUERY","SETUP_NAME","SETUP_CLASS"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ((( "MODULE_ID" = :MODULE_ID0 ) AND ( "HOSPITAL_ID" = :HOSPITAL_ID1 )) AND ( "SETUP_NAME" = :SETUP_NAME2 )) AND   ROWNUM = 1  
[Pars]:
[Name]::MODULE_ID0 [Value]:H82 [Type]:AnsiString    
[Name]::HOSPITAL_ID1 [Value]:33A001 [Type]:AnsiString    
[Name]::SETUP_NAME2 [Value]:导航栏 [Type]:AnsiString    

2025-07-16 14:46:53.918 +08:00 [INF] HTTP GET /api/Base/GetMenuInfo responded 200 in 139609.0762 ms
2025-07-16 14:46:53.919 +08:00 [INF] 【接口超时阀值预警】 [20e0d4c908159a12883d69c5e8210343]接口/api/Base/GetMenuInfo,耗时:[139609]毫秒
2025-07-16 14:48:09.492 +08:00 [INF] ==>App Start..2025-07-16 14:48:09
2025-07-16 14:48:09.689 +08:00 [INF] ==>当前模块:H82,6.25.329
2025-07-16 14:48:09.692 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-16 14:48:11.254 +08:00 [INF] ==>基础连接请求完成.
2025-07-16 14:48:11.664 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-16 14:48:12.047 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-16 14:48:12.402 +08:00 [INF] ==>S01版本写入成功:6.25.329
2025-07-16 14:48:13.168 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-16 14:48:13.256 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-16 14:48:13.682 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-16 14:48:13.682 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-16 14:48:14.582 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-16 14:48:16.864 +08:00 [INF] ==>初始化完成..
2025-07-16 14:48:16.885 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-16 14:48:16.887 +08:00 [INF] 设备启用任务
2025-07-16 14:48:16.888 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-16 14:48:17.274 +08:00 [INF] 【SQL执行耗时:362.7243ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-16 14:48:17.413 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-16 14:48:17.429 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-16 14:48:17.431 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-16 14:48:17.431 +08:00 [INF] Hosting environment: Development
2025-07-16 14:48:17.432 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-16 14:48:31.425 +08:00 [INF] 【SQL执行耗时:360.0489ms】

[Sql]: SELECT "FUNC_ID"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ((( "MODULE_ID" = :MODULE_ID0 ) AND ( "HOSPITAL_ID" = :HOSPITAL_ID1 )) AND ( "SETUP_NAME" = :SETUP_NAME2 )) AND   ROWNUM = 1  
[Pars]:
[Name]::MODULE_ID0 [Value]:H82 [Type]:AnsiString    
[Name]::HOSPITAL_ID1 [Value]:33A001 [Type]:AnsiString    
[Name]::SETUP_NAME2 [Value]:导航栏 [Type]:AnsiString    

2025-07-16 14:51:38.889 +08:00 [INF] HTTP GET /api/Base/GetMenuInfo responded 200 in 192200.9208 ms
2025-07-16 14:51:38.891 +08:00 [INF] 【接口超时阀值预警】 [25f0857b698b4759a70862d9b8d230bd]接口/api/Base/GetMenuInfo,耗时:[192208]毫秒
2025-07-16 14:52:54.399 +08:00 [INF] ==>App Start..2025-07-16 14:52:54
2025-07-16 14:52:54.593 +08:00 [INF] ==>当前模块:H82,6.25.329
2025-07-16 14:52:54.596 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-16 14:52:56.077 +08:00 [INF] ==>基础连接请求完成.
2025-07-16 14:52:56.490 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-16 14:52:56.934 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-16 14:52:57.277 +08:00 [INF] ==>S01版本写入成功:6.25.329
2025-07-16 14:52:58.098 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-16 14:52:58.195 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-16 14:52:58.646 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-16 14:52:58.646 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-16 14:52:59.638 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-16 14:53:01.922 +08:00 [INF] ==>初始化完成..
2025-07-16 14:53:01.950 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-16 14:53:01.954 +08:00 [INF] 设备启用任务
2025-07-16 14:53:01.955 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-16 14:53:02.451 +08:00 [INF] 【SQL执行耗时:466.588ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-16 14:53:02.628 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-16 14:53:02.645 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-16 14:53:02.647 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-16 14:53:02.648 +08:00 [INF] Hosting environment: Development
2025-07-16 14:53:02.648 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-16 14:55:24.929 +08:00 [INF] 【SQL执行耗时:367.352ms】

[Sql]: SELECT "FUNC_ID"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ((( "MODULE_ID" = :MODULE_ID0 ) AND ( "HOSPITAL_ID" = :HOSPITAL_ID1 )) AND ( "SETUP_NAME" = :SETUP_NAME2 )) AND   ROWNUM = 1  
[Pars]:
[Name]::MODULE_ID0 [Value]:H82 [Type]:AnsiString    
[Name]::HOSPITAL_ID1 [Value]:33A001 [Type]:AnsiString    
[Name]::SETUP_NAME2 [Value]:导航栏 [Type]:AnsiString    

2025-07-16 14:55:41.613 +08:00 [INF] HTTP GET /api/Base/GetMenuInfo responded 200 in 22226.6700 ms
2025-07-16 14:55:41.616 +08:00 [INF] 【接口超时阀值预警】 [c19d2a0618edb5e291b9a4640bcd9c75]接口/api/Base/GetMenuInfo,耗时:[22235]毫秒
2025-07-16 15:09:20.361 +08:00 [INF] ==>App Start..2025-07-16 15:09:20
2025-07-16 15:09:20.540 +08:00 [INF] ==>当前模块:H82,6.25.329
2025-07-16 15:09:20.544 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-16 15:09:22.030 +08:00 [INF] ==>基础连接请求完成.
2025-07-16 15:09:22.407 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-16 15:09:22.810 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-16 15:09:23.133 +08:00 [INF] ==>S01版本写入成功:6.25.329
2025-07-16 15:09:23.955 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-16 15:09:24.050 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-16 15:09:24.484 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-16 15:09:24.484 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-16 15:09:25.537 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-16 15:09:27.825 +08:00 [INF] ==>初始化完成..
2025-07-16 15:09:27.853 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-16 15:09:27.858 +08:00 [INF] 设备启用任务
2025-07-16 15:09:27.859 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-16 15:09:28.283 +08:00 [INF] 【SQL执行耗时:393.1593ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-16 15:09:28.452 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-16 15:09:28.468 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-16 15:09:28.470 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-16 15:09:28.470 +08:00 [INF] Hosting environment: Development
2025-07-16 15:09:28.471 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-16 15:09:37.229 +08:00 [INF] 【SQL执行耗时:360.2781ms】

[Sql]: SELECT "FUNC_ID"  FROM "XH_SYS"."SYS6_MODULE_FUNC_DICT"  WHERE ((( "MODULE_ID" = :MODULE_ID0 ) AND ( "HOSPITAL_ID" = :HOSPITAL_ID1 )) AND ( "SETUP_NAME" = :SETUP_NAME2 )) AND   ROWNUM = 1  
[Pars]:
[Name]::MODULE_ID0 [Value]:H82 [Type]:AnsiString    
[Name]::HOSPITAL_ID1 [Value]:33A001 [Type]:AnsiString    
[Name]::SETUP_NAME2 [Value]:导航栏 [Type]:AnsiString    

2025-07-16 15:09:37.764 +08:00 [INF] HTTP GET /api/Base/GetMenuInfo responded 200 in 6166.6695 ms
2025-07-16 15:09:37.767 +08:00 [INF] 【接口超时阀值预警】 [ce20ae0781404157b256f53052119333]接口/api/Base/GetMenuInfo,耗时:[6176]毫秒
