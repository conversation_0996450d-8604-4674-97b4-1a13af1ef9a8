﻿using H.Utility;
using XH.H82.Models.Entities;

namespace XH.H82.Models.Dtos.Maintain;

public class MaintainDto
{
    public static EMS_MAINTAIN_INFO CreateAddModule(string equipmentId, string? hospitalId  , MaintainInput input)
    {
        var record = new EMS_MAINTAIN_INFO();
        record.EQUIPMENT_ID = equipmentId;
        record.MAINTAIN_ID = IDGenHelper.CreateGuid();
        record.HOSPITAL_ID = hospitalId ?? "H0000";
        record.MAINTAIN_CYCLE = input.MAINTAIN_CYCLE;
        record.MAINTAIN_DATE = input.MAINTAIN_DATE;
        record.MAINTAIN_PERSON = input.MAINTAIN_PERSON;
        if (record.MAINTAIN_PERSON.IsNotNullOrEmpty() && record.MAINTAIN_PERSON.Contains("_"))
        {
            record.MAINTAIN_PERSON = record.MAINTAIN_PERSON.Split('_')[1];
        }
        record.MAINTAIN_CONTENT = input.MAINTAIN_CONTENT;
        record.REMARK = input.REMARK;
        record.OCCUR_EVENT = input.OCCUR_EVENT;
        record.MAINTAIN_STATE = "1";
        return record;
    }
}

/// <summary>
/// 保养记录入参模型
/// </summary>
/// <param name="MAINTAIN_CYCLE">保养周期</param>
/// <param name="MAINTAIN_DATE">保养时间</param>
/// <param name="MAINTAIN_PERSON">保养人员</param>
/// <param name="MAINTAIN_CONTENT">保养内容</param>
/// <param name="OCCUR_EVENT">产生事件</param>
/// <param name="REMARK">备注</param>
public record MaintainInput(
    string MAINTAIN_CYCLE,
    DateTime? MAINTAIN_DATE,
    string? MAINTAIN_PERSON,
    string? MAINTAIN_CONTENT,
    string OCCUR_EVENT,
    string? REMARK);