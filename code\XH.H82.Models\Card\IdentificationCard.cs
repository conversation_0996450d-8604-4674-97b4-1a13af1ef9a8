﻿using AutoMapper.Internal;
using XH.H82.Models.InkScreenTemplate;

namespace XH.H82.Models.Card
{
    public class IdentificationCard
    {
        public string templateTitle { get; set; } = "检验中心设备运行状态及保养/校准标识";
        public string equipmentName { get; set; }
        public string dealer { get; set; }
        public string equipmentCode { get; set; }
        public string deptSectionNo { get; set; }
        public string enableTime { get; set; }
        public string installDate { get; set; }
        public string nextMaintainDate { get; set; }
        public string nextMaintainDateStatus { get; set; }
        public string maintainType { get; set; }
        public string nextCorrectDate { get; set; }
        public string nextCorrectDateStatus { get; set; }
        public string correctDept { get; set; }
        public string eqInPerson { get; set; }
        public string installArea { get; set; }
        public string circumstance { get; set; }
        public string meassage { get; set; }
        public string registrationNum { get; set; }

        public string eqOutTime { get; set; }
        public string professionalClass { get; set; }
        public string manufacturer { get; set; }
        public string serialNumber { get; set; }
        public string dealerContact { get; set; }
        public string lastComparisonDate { get; set; }
        public string depreciationTime { get; set; }
        public string correctIntervals { get; set; }
        public string manufacturerContact { get; set; }
        public string hospitalName { get; set; }
        public string labName { get; set; }
        public string sellPrice { get; set; }
        public string equipmentSize { get; set; }
        public string correctDate { get; set; }
        
        public string model { get; set; }
        public string maintainDate { get; set; }

        private List<Template> _templates = new List<Template>();

        private string ToName(string key)
        {
            return key switch
            {
                "templateTitle" => "标题",
                "equipmentName" => "设备名称",
                "dealer" => "经销商",
                "equipmentCode" => "设备代号",
                "deptSectionNo" => "科室编号",
                "enableTime" => "启用时间",
                "installDate" => "安装时间",
                "nextMaintainDate" => "下次保养",
                "nextMaintainDateStatus" => "",
                "maintainType" => "保养周期",
                "nextCorrectDate" => "下次校准",
                "nextCorrectDateStatus" => "",
                "correctDept" => "校准部门",
                "eqInPerson" => "负责人",
                "installArea" => "安装位置",
                "circumstance" => "",
                "meassage" => "",
                "registrationNum" => "注册证号",
                "eqOutTime" => "出厂日期",
                "professionalClass" => "专业分类",
                "manufacturer" => "制造商",
                "serialNumber" => "序列号",
                "dealerContact" => "经销商联系人",
                "lastComparisonDate" => "上次比对日期",
                "depreciationTime" => "折旧年限",
                "correctIntervals" => "校准有效期",
                "manufacturerContact" => "制造商联系人",
                "hospitalName" => "所属医疗机构",
                "labName" => "所属科室",
                "sellPrice" => "设备价格",
                "equipmentSize" => "尺寸(mm)",
                "correctDate"=>"校准日期",
                "model"=>"设备型号",
                "maintainDate"=>"保养日期",
                _ => ""
            };
        }
        public Dictionary<string, string> ToAttributes()
        {
            var result = new Dictionary<string, string>();
            var properties = GetType().GetProperties().Where(x => x.IsPublic());
            foreach (var property in properties)
            {
                result.Add(property.Name, ToName(property.Name));
            }
            return result;
        }
        public Dictionary<string, string> ToDictionary()
        {
            var result = new Dictionary<string, string>();
            var properties = this.GetType().GetProperties();

            foreach (var property in properties)
            {
                var value = property.GetValue(this);
                if (value is null)
                {
                    result.Add(property.Name, "");
                }
                else
                {
                    result.Add(property.Name, value.ToString());
                }
            }

            return result;
        }

        public void SetTemplates(List<Template> templates)
        {
            if (templates is null || templates.Count == 0)
            {
                _templates = TemplateAContentInit.Default();
            }
            else
            {
                _templates = templates;
            }
            _templates.ForEach(x => {
                x.cols.ForEach(y =>
                {
                    if (y.colKey == "templateTitle")
                    {
                        y.colTitle = "";
                    }
                });
            });
        }

        public EInkChangeDisplayDto ToEinkChangeDisplay(string mac = "D4:3D:39:1C:E1:70", bool configErrorLight = true, bool isuploadFile = false)
        {
            var result = new EInkChangeDisplayDto();
            result.mac = mac;
            result.info = ToDictionary();
            result.qrCodeImg = null;
            result.isuploadFile = isuploadFile;
            result.isConfigErrorLight = configErrorLight;
            result.colStyles = _templates;
            return result;
        }
    }

    /// <summary>
    /// 水墨屏展示效果
    /// </summary>
    public class EInkChangeDisplayDto
    {
        /// <summary>
        /// 记录ID
        /// </summary>
        public string recordUUID { get; set; } = Guid.NewGuid().ToString();
        /// <summary>
        /// mac地址
        /// </summary>
        public string mac { get; set; } = "D4:3D:39:1C:E1:70";
        /// <summary>
        /// 是否亮灯
        /// </summary>
        public bool isConfigErrorLight { get; set; } = true;
        public bool isuploadFile { get; set; } = false;
        /// <summary>
        /// 数据信息
        /// </summary>
        public Dictionary<string, string> info { get; set; }

        /// <summary>
        ///二维码  64*64px的
        /// </summary>
        public string? qrCodeImg { get; set; }

        public List<Template> colStyles { get; set; }
    }
}
