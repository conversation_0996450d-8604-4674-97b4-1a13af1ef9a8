﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using XH.H82.Models.DeviceRelevantInformation.Enum;

namespace XH.H82.Models.Entities.WarnRecord
{
    [DBOwner("XH_OA")]
    [SugarTable("EMS_EQUIPMENT_WARN")]
    public class EMS_EQUIPMENT_WARN : BaseField
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string WARN_RID { get; set; }

        public string EQUIPMENT_ID { get; set; }

        public EquipmentSummaryEnum WARN_TYPE { get; set; }

        public string WARN_MSG { get; set; }

        public DateTime? WARN_TIME { get; set; }

        public string DISPOSE_PERSON { get; set; }

        public DateTime? DISPOSE_TIME { get; set; }

        public DealWithStateEnum DISPOSE_STATE { get; set; }

    }
}
