{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Models\\XH.H82.Models.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Models\\XH.H82.Models.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Models\\XH.H82.Models.csproj", "projectName": "XH.H82.Models", "projectPath": "C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Models\\XH.H82.Models.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Models\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"EPPlus": {"target": "Package", "version": "[6.2.6, )"}, "XH.LAB.UTILS": {"target": "Package", "version": "[6.25.301.26, )"}, "Xinghe.Utility": {"target": "Package", "version": "[6.25.206, )"}, "protobuf-net.Core": {"target": "Package", "version": "[3.1.22, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}}}