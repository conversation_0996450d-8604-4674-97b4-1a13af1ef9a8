﻿using AutoMapper;
using H.Utility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Serilog;
using XH.H82.API.Extensions;
using XH.H82.Base.Tree;
using XH.H82.IServices;
using XH.H82.IServices.Sbml;
using XH.H82.Models.Smbl.Dto;

namespace XH.H82.API.Controllers.Organization
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize]
    public class OrganizationPullController : ControllerBase
    {

        private readonly ICurrencyService _currencyService;
        private readonly ISmblServicve _smblService;
        private readonly IConfiguration _configuration;
        private IMapper _mapper;

        public OrganizationPullController(ICurrencyService currencyService, ISmblServicve smblService, IMapper mapper, IConfiguration configuration)
        {
            _currencyService = currencyService;
            _smblService = smblService;
            _mapper = mapper;
            _configuration = configuration;
        }


        /// <summary>
        /// 院区-专业组下拉
        /// </summary>
        /// <param name="labId"></param>
        /// <param name="areaId"></param>
        /// <returns></returns>
        [HttpGet]
        [CustomResponseType(typeof(List<AllTreeNode>))]
        public IActionResult GenericPullAreaPGroups(string? labId, string? areaId)
        {
            
            var result = _currencyService.GetPullAreaPgroups(areaId, labId);
            return Ok(result.ToResultDto());
        }



        /// <summary>
        /// 人员树下拉
        /// </summary>
        /// <param name="labId"></param>
        /// <param name="areaId"></param>
        /// <param name="mgroupId"></param>
        /// <param name="pgroupId"></param>
        /// <param name="modelId"></param>
        /// <returns></returns>
        [HttpGet]
        [CustomResponseType(typeof(List<AllTreeNode>))]
        public IActionResult GenericPullPersons(string labId, string? areaId, string? mgroupId, string? pgroupId, string? modelId)
        {
            var result = _currencyService.GetPullPersons(labId, areaId, mgroupId, pgroupId, modelId);
            return Ok(result.ToResultDto());
        }

        
        /// <summary>
        /// 生安-备案实验室下拉
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [CustomResponseType(typeof(List<AllTreeNode>))]
        public IActionResult SmblPullAreaPGroups()
        {
            var user = User.ToClaimsDto();
            if (_configuration.GetSection("IsSmbl").Value == "1")
            {
                var result = _smblService.GetSmblLabPullTree(user.HOSPITAL_ID);
                result.rootNode.ConutChildrensNoAppendRootPath();
                return Ok(result.rootNode.CHILDREN.ToResultDto());
            }
            return Ok(Array.Empty<ITreeNode>().ToList());
        }

        /// <summary>
        /// 生安设备类型列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [CustomResponseType(typeof(List<SmblEquipmentClassDto>))]
        public IActionResult SmblEquipmentClassPull()
        {
            var result = new List<SmblEquipmentClassDto>();
            try
            {
                var equipmentClassPull = _smblService.GetSmblEquipmentClassPull();
                equipmentClassPull = equipmentClassPull.OrderBy(x=>x.DATA_SORT).ToList();
                result = _mapper.Map<List<SmblEquipmentClassDto>>(equipmentClassPull);
                return Ok(result.ToResultDto());
            }
            catch (Exception e)
            {
                Log.Error($"生安设备类型需要补充脚本：{e}");
                return Ok(result.ToResultDto());
            }
        }
        
        
        
    }
}
