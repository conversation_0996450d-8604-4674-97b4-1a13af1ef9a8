2025-07-08 09:49:51.223 +08:00 [INF] ==>App Start..2025-07-08 09:49:51
2025-07-08 09:49:51.438 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-08 09:49:51.442 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-08 09:49:53.576 +08:00 [INF] ==>基础连接请求完成.
2025-07-08 09:49:53.980 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-08 09:49:54.367 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-08 09:49:54.680 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-08 09:49:54.682 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-08 09:49:55.019 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-08 09:49:56.865 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-08 09:49:57.295 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-08 09:49:57.853 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-08 09:49:57.853 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-08 09:49:58.984 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-08 09:50:01.734 +08:00 [INF] ==>初始化完成..
2025-07-08 09:50:01.795 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-08 09:50:01.797 +08:00 [INF] 设备启用任务
2025-07-08 09:50:01.797 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-08 09:50:02.194 +08:00 [INF] 【SQL执行耗时:379.1956ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-08 09:50:02.361 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-08 09:50:02.379 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-08 09:50:02.380 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-08 09:50:02.381 +08:00 [INF] Hosting environment: Development
2025-07-08 09:50:02.381 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-08 09:50:59.726 +08:00 [INF] 【SQL执行耗时:356.5799ms】

[Sql]:SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" <> :EqpNoState0 ) 
[Pars]:
[Name]::EqpNoState0 [Value]:2 [Type]:String    

2025-07-08 09:51:00.449 +08:00 [INF] 【SQL执行耗时:363.2813ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-08 09:51:00.882 +08:00 [INF] 【SQL执行耗时:341.105ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-08 09:51:01.297 +08:00 [INF] 【SQL执行耗时:340.7321ms】

[Sql]:SELECT "HOSPITAL_ID","HOSPITAL_LOGIN","HOSPITAL_CNAME","HOSPITAL_SNAME","HOSPITAL_TYPE","HOSPITAL_LEVEL","LEVEL1_CODE","LEVEL2_CODE","LEVEL3_CODE","HOSPITAL_BARCODE","HOSPITAL_PROPERTY","HOSPITAL_PERSON","HOSPITAL_ADDRESS","HOSPITAL_TEL","HOSPITAL_STATE","FIRST_PERSON","FIRST_RTIME","LAST_MPRESON","LAST_MTIME","REMARK","HOSPITAL_BCODE","HOSPITAL_RANGE","BARCODE_TYPE","BARCODE_RANGE","CHARGE_PROVIDE_TYPE","DATADB_IP","WECHAT_NAME","LAST_MPERSON","FIRST_RPERSON","UPDATE_FLAG","INSERFACE_SERVICE","SAMELEVEL_HOSPITAL","HOSPITAL_CLASS","HOSPITAL_URL","REGISTER_CODE","PACK_BAR_PREFIX","IF_ITEM_MANAGE","IF_TEMPLATE_HOS","MEDICAL_CONGENER","MEDICAL_CONGENER_ROLE","VEST_IN_DC","INSTANCE_ID","HOSPITAL_ATTRIBUTE","DATASYNC_TYPE","HGROUP_ID","CITEM_MANAGE" FROM "XH_SYS"."SYS6_HOSPITAL_INFO" 

2025-07-08 09:51:01.735 +08:00 [INF] 【SQL执行耗时:351.4415ms】

[Sql]:SELECT "LAB_ID","FIRST_RPERSON","LAB_NAME","LAB_ADDRESS","LAB_CODE","LAST_MTIME","LAB_SORT","LAB_RNAME","LAB_CLASS","LAB_PERSON","LINKMAN_TEL","REGISTER_CODE","LAB_LINKMAN","STATE_FLAG","REMARK","LAST_MPERSON","FIRST_RTIME","SPELL_CODE","LAB_TEL","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_LAB" 

2025-07-08 09:51:02.177 +08:00 [INF] 【SQL执行耗时:361.3314ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP" 

2025-07-08 09:51:02.624 +08:00 [INF] 【SQL执行耗时:361.8569ms】

[Sql]:SELECT "AREA_ID","AREA_CODE","AREA_SORT","AREA_NAME","AREA_CLASS","AREA_PERSON","AREA_ADDRESS","AREA_TEL","SPELL_CODE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","STATE_FLAG","REMARK","HOSPITAL_ID","TAG_SIGN","AREA_ANAME","PACK_BAR_PREFIX" FROM "XH_SYS"."SYS6_INSPECTION_AREA" 

2025-07-08 09:51:20.443 +08:00 [ERR] 未处理的异常::System.NullReferenceException: Object reference not set to an instance of an object.
   at XH.H82.Services.EquipmentCodeCustom.CustomCodeService.GetEquipmentCodeCustomDictByCondetion(Expression`1 expression) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentCodeCustom\CustomCodeService.cs:line 79
   at XH.H82.Services.EquipmentCodeCustom.CustomCodeService.GetEquipmentCodeCustomDict() in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentCodeCustom\CustomCodeService.cs:line 93
   at Castle.Proxies.Invocations.ICustomCodeService_GetEquipmentCodeCustomDict.InvokeMethodOnTarget()
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.UseCacheWhenFailedAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.CacheAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at Castle.Proxies.ICustomCodeServiceProxy.GetEquipmentCodeCustomDict()
   at XH.H82.API.Controllers.EquipmentCodeCustom.CodeCustomController.GetEquipmentCodeCustomDict() in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\Controllers\EquipmentCodeCustom\CodeCustomController.cs:line 37
   at lambda_method910(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-07-08 09:51:20.453 +08:00 [ERR] HTTP GET /api/CodeCustom/GetEquipmentCodeCustomDict responded 500 in 24274.9557 ms
2025-07-08 09:51:20.457 +08:00 [INF] 【接口超时阀值预警】 [157349309c8bd312fac156afb1e7b1a7]接口/api/CodeCustom/GetEquipmentCodeCustomDict,耗时:[24284]毫秒
2025-07-08 09:53:15.517 +08:00 [INF] 【SQL执行耗时:359.2172ms】

[Sql]:SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" <> :EqpNoState0 ) 
[Pars]:
[Name]::EqpNoState0 [Value]:2 [Type]:String    

2025-07-08 09:54:45.658 +08:00 [INF] ==>App Start..2025-07-08 09:54:45
2025-07-08 09:54:45.849 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-08 09:54:45.853 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-08 09:54:47.246 +08:00 [INF] ==>基础连接请求完成.
2025-07-08 09:54:47.612 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-08 09:54:47.928 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-08 09:54:48.305 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-08 09:54:48.312 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-08 09:54:48.678 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-08 09:54:49.078 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-08 09:54:49.176 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-08 09:54:49.594 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-08 09:54:49.595 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-08 09:54:50.461 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-08 09:54:53.104 +08:00 [INF] ==>初始化完成..
2025-07-08 09:54:53.126 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-08 09:54:53.128 +08:00 [INF] 设备启用任务
2025-07-08 09:54:53.129 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-08 09:54:53.509 +08:00 [INF] 【SQL执行耗时:362.6826ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-08 09:54:53.658 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-08 09:54:53.673 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-08 09:54:53.675 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-08 09:54:53.676 +08:00 [INF] Hosting environment: Development
2025-07-08 09:54:53.676 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-08 09:55:01.318 +08:00 [INF] 【SQL执行耗时:357.1768ms】

[Sql]:SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" <> :EqpNoState0 ) 
[Pars]:
[Name]::EqpNoState0 [Value]:2 [Type]:String    

2025-07-08 09:55:05.189 +08:00 [INF] 【SQL执行耗时:357.1368ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-08 09:55:05.620 +08:00 [INF] 【SQL执行耗时:338.7718ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-08 09:55:06.043 +08:00 [INF] 【SQL执行耗时:347.1787ms】

[Sql]:SELECT "HOSPITAL_ID","HOSPITAL_LOGIN","HOSPITAL_CNAME","HOSPITAL_SNAME","HOSPITAL_TYPE","HOSPITAL_LEVEL","LEVEL1_CODE","LEVEL2_CODE","LEVEL3_CODE","HOSPITAL_BARCODE","HOSPITAL_PROPERTY","HOSPITAL_PERSON","HOSPITAL_ADDRESS","HOSPITAL_TEL","HOSPITAL_STATE","FIRST_PERSON","FIRST_RTIME","LAST_MPRESON","LAST_MTIME","REMARK","HOSPITAL_BCODE","HOSPITAL_RANGE","BARCODE_TYPE","BARCODE_RANGE","CHARGE_PROVIDE_TYPE","DATADB_IP","WECHAT_NAME","LAST_MPERSON","FIRST_RPERSON","UPDATE_FLAG","INSERFACE_SERVICE","SAMELEVEL_HOSPITAL","HOSPITAL_CLASS","HOSPITAL_URL","REGISTER_CODE","PACK_BAR_PREFIX","IF_ITEM_MANAGE","IF_TEMPLATE_HOS","MEDICAL_CONGENER","MEDICAL_CONGENER_ROLE","VEST_IN_DC","INSTANCE_ID","HOSPITAL_ATTRIBUTE","DATASYNC_TYPE","HGROUP_ID","CITEM_MANAGE" FROM "XH_SYS"."SYS6_HOSPITAL_INFO" 

2025-07-08 09:55:06.499 +08:00 [INF] 【SQL执行耗时:370.0665ms】

[Sql]:SELECT "LAB_ID","FIRST_RPERSON","LAB_NAME","LAB_ADDRESS","LAB_CODE","LAST_MTIME","LAB_SORT","LAB_RNAME","LAB_CLASS","LAB_PERSON","LINKMAN_TEL","REGISTER_CODE","LAB_LINKMAN","STATE_FLAG","REMARK","LAST_MPERSON","FIRST_RTIME","SPELL_CODE","LAB_TEL","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_LAB" 

2025-07-08 09:55:06.934 +08:00 [INF] 【SQL执行耗时:341.6557ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP" 

2025-07-08 09:55:07.352 +08:00 [INF] 【SQL执行耗时:341.348ms】

[Sql]:SELECT "AREA_ID","AREA_CODE","AREA_SORT","AREA_NAME","AREA_CLASS","AREA_PERSON","AREA_ADDRESS","AREA_TEL","SPELL_CODE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","STATE_FLAG","REMARK","HOSPITAL_ID","TAG_SIGN","AREA_ANAME","PACK_BAR_PREFIX" FROM "XH_SYS"."SYS6_INSPECTION_AREA" 

2025-07-08 09:56:23.036 +08:00 [INF] ==>App Start..2025-07-08 09:56:23
2025-07-08 09:56:23.271 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-08 09:56:23.274 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-08 09:56:24.846 +08:00 [INF] ==>基础连接请求完成.
2025-07-08 09:56:25.216 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-08 09:56:25.603 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-08 09:56:25.912 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-08 09:56:25.914 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-08 09:56:26.251 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-08 09:56:26.843 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-08 09:56:26.932 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-08 09:56:27.358 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-08 09:56:27.358 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-08 09:56:28.323 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-08 09:56:31.034 +08:00 [INF] ==>初始化完成..
2025-07-08 09:56:31.063 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-08 09:56:31.066 +08:00 [INF] 设备启用任务
2025-07-08 09:56:31.067 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-08 09:56:31.457 +08:00 [INF] 【SQL执行耗时:363.3505ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-08 09:56:31.649 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-08 09:56:31.671 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-08 09:56:31.673 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-08 09:56:31.674 +08:00 [INF] Hosting environment: Development
2025-07-08 09:56:31.674 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-08 09:56:41.677 +08:00 [INF] ==>App Start..2025-07-08 09:56:41
2025-07-08 09:56:41.841 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-08 09:56:41.844 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-08 09:56:43.262 +08:00 [INF] ==>基础连接请求完成.
2025-07-08 09:56:43.623 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-08 09:56:43.939 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-08 09:56:44.304 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-08 09:56:44.315 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-08 09:56:44.700 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-08 09:56:45.179 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-08 09:56:45.261 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-08 09:56:45.683 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-08 09:56:45.684 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-08 09:56:46.552 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-08 09:56:49.252 +08:00 [INF] ==>初始化完成..
2025-07-08 09:56:49.274 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-08 09:56:49.277 +08:00 [INF] 设备启用任务
2025-07-08 09:56:49.278 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-08 09:56:49.646 +08:00 [INF] 【SQL执行耗时:349.8277ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-08 09:56:49.785 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-08 09:56:49.801 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-08 09:56:49.803 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-08 09:56:49.804 +08:00 [INF] Hosting environment: Development
2025-07-08 09:56:49.804 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-08 09:57:21.964 +08:00 [INF] 【SQL执行耗时:339.2741ms】

[Sql]:SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" <> :EqpNoState0 ) 
[Pars]:
[Name]::EqpNoState0 [Value]:2 [Type]:String    

2025-07-08 09:57:24.427 +08:00 [INF] 【SQL执行耗时:329.167ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-08 09:57:25.414 +08:00 [INF] 【SQL执行耗时:909.0379ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-08 09:57:25.943 +08:00 [INF] 【SQL执行耗时:349.3745ms】

[Sql]:SELECT "HOSPITAL_ID","HOSPITAL_LOGIN","HOSPITAL_CNAME","HOSPITAL_SNAME","HOSPITAL_TYPE","HOSPITAL_LEVEL","LEVEL1_CODE","LEVEL2_CODE","LEVEL3_CODE","HOSPITAL_BARCODE","HOSPITAL_PROPERTY","HOSPITAL_PERSON","HOSPITAL_ADDRESS","HOSPITAL_TEL","HOSPITAL_STATE","FIRST_PERSON","FIRST_RTIME","LAST_MPRESON","LAST_MTIME","REMARK","HOSPITAL_BCODE","HOSPITAL_RANGE","BARCODE_TYPE","BARCODE_RANGE","CHARGE_PROVIDE_TYPE","DATADB_IP","WECHAT_NAME","LAST_MPERSON","FIRST_RPERSON","UPDATE_FLAG","INSERFACE_SERVICE","SAMELEVEL_HOSPITAL","HOSPITAL_CLASS","HOSPITAL_URL","REGISTER_CODE","PACK_BAR_PREFIX","IF_ITEM_MANAGE","IF_TEMPLATE_HOS","MEDICAL_CONGENER","MEDICAL_CONGENER_ROLE","VEST_IN_DC","INSTANCE_ID","HOSPITAL_ATTRIBUTE","DATASYNC_TYPE","HGROUP_ID","CITEM_MANAGE" FROM "XH_SYS"."SYS6_HOSPITAL_INFO" 

2025-07-08 09:57:26.408 +08:00 [INF] 【SQL执行耗时:383.4799ms】

[Sql]:SELECT "LAB_ID","FIRST_RPERSON","LAB_NAME","LAB_ADDRESS","LAB_CODE","LAST_MTIME","LAB_SORT","LAB_RNAME","LAB_CLASS","LAB_PERSON","LINKMAN_TEL","REGISTER_CODE","LAB_LINKMAN","STATE_FLAG","REMARK","LAST_MPERSON","FIRST_RTIME","SPELL_CODE","LAB_TEL","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_LAB" 

2025-07-08 09:57:26.839 +08:00 [INF] 【SQL执行耗时:345.5152ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP" 

2025-07-08 09:57:27.265 +08:00 [INF] 【SQL执行耗时:349.3248ms】

[Sql]:SELECT "AREA_ID","AREA_CODE","AREA_SORT","AREA_NAME","AREA_CLASS","AREA_PERSON","AREA_ADDRESS","AREA_TEL","SPELL_CODE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","STATE_FLAG","REMARK","HOSPITAL_ID","TAG_SIGN","AREA_ANAME","PACK_BAR_PREFIX" FROM "XH_SYS"."SYS6_INSPECTION_AREA" 

2025-07-08 09:57:27.395 +08:00 [INF] HTTP GET /api/CodeCustom/GetEquipmentCodeCustomDict responded 200 in 9029.8662 ms
2025-07-08 09:57:27.397 +08:00 [INF] 【接口超时阀值预警】 [3055795ee9d8c60f181ddd9e90fa46c4]接口/api/CodeCustom/GetEquipmentCodeCustomDict,耗时:[9036]毫秒
2025-07-08 11:31:35.274 +08:00 [INF] ==>App Start..2025-07-08 11:31:35
2025-07-08 11:31:35.457 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-08 11:31:35.460 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-08 11:31:37.454 +08:00 [INF] ==>基础连接请求完成.
2025-07-08 11:31:37.841 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-08 11:31:38.226 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-08 11:31:38.528 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-08 11:31:38.531 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-08 11:31:38.870 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-08 11:31:40.608 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-08 11:31:41.027 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-08 11:31:41.534 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-08 11:31:41.534 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-08 11:31:42.025 +08:00 [ERR] 查询出错:中文提示 :  连接数据库过程中发生错误，检查服务器是否正常连接字符串是否正确，错误信息：ORA-12545: Network Transport: Unable to resolve connect hostnameDbType="Oracle";ConfigId="".
English Message : Connection open error . ORA-12545: Network Transport: Unable to resolve connect hostnameDbType="Oracle";ConfigId="" :
SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON" FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "LAST_MTIME" IS NULL )
2025-07-08 11:31:53.315 +08:00 [INF] ==>App Start..2025-07-08 11:31:53
2025-07-08 11:31:53.485 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-08 11:31:53.488 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-08 11:31:54.887 +08:00 [INF] ==>基础连接请求完成.
2025-07-08 11:31:55.247 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-08 11:31:55.590 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-08 11:31:55.906 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-08 11:31:55.908 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-08 11:31:56.249 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-08 11:31:56.677 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-08 11:31:56.761 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-08 11:31:57.171 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-08 11:31:57.172 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-08 11:31:58.050 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-08 11:32:00.809 +08:00 [INF] ==>初始化完成..
2025-07-08 11:32:00.866 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-08 11:32:00.868 +08:00 [INF] 设备启用任务
2025-07-08 11:32:00.869 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-08 11:32:01.284 +08:00 [INF] 【SQL执行耗时:397.5949ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-08 11:32:01.448 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-08 11:32:01.461 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-08 11:32:01.462 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-08 11:32:01.463 +08:00 [INF] Hosting environment: Development
2025-07-08 11:32:01.463 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-08 11:32:16.620 +08:00 [INF] HTTP GET /api/EquipmentDoc/GetEquipmentInfo responded 401 in 290.7311 ms
2025-07-08 11:32:26.316 +08:00 [INF] 第三方url地址为：http://************
2025-07-08 11:34:05.721 +08:00 [INF] HTTP GET /api/EquipmentDoc/GetEquipmentInfo responded 200 in 102384.0159 ms
2025-07-08 11:34:05.723 +08:00 [INF] 【接口超时阀值预警】 [921465d373036229ba8fb3bf7efadec7]接口/api/EquipmentDoc/GetEquipmentInfo,耗时:[102384]毫秒
2025-07-08 11:34:07.614 +08:00 [INF] 第三方url地址为：http://************
2025-07-08 11:34:30.264 +08:00 [INF] HTTP GET /api/EquipmentDoc/GetEquipmentInfo responded 200 in 22687.8623 ms
2025-07-08 11:34:30.264 +08:00 [INF] 【接口超时阀值预警】 [c51b9a15e2affea8462e3d9431f65037]接口/api/EquipmentDoc/GetEquipmentInfo,耗时:[22688]毫秒
2025-07-08 11:34:59.550 +08:00 [INF] 第三方url地址为：http://************
2025-07-08 11:35:35.163 +08:00 [ERR] 未处理的异常::System.NullReferenceException: Object reference not set to an instance of an object.
   at XH.H82.Services.EquipmentDocService.GetEquipmentInfo(String equipmentId) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentDocService.cs:line 257
   at Castle.Proxies.Invocations.IEquipmentDocService_GetEquipmentInfo.InvokeMethodOnTarget()
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.UseCacheWhenFailedAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.CacheAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at Castle.Proxies.IEquipmentDocServiceProxy.GetEquipmentInfo(String equipmentId)
   at XH.H82.API.Controllers.EquipmentDocController.GetEquipmentInfo(String equipmentId) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\Controllers\EquipmentDocController.cs:line 122
   at lambda_method910(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-07-08 11:35:35.165 +08:00 [ERR] HTTP GET /api/EquipmentDoc/GetEquipmentInfo responded 500 in 35662.2735 ms
2025-07-08 11:35:35.166 +08:00 [INF] 【接口超时阀值预警】 [a5e08a0ccda3aa6e28ca4ffb415a5a18]接口/api/EquipmentDoc/GetEquipmentInfo,耗时:[35662]毫秒
2025-07-08 11:35:37.226 +08:00 [INF] 第三方url地址为：http://************
2025-07-08 11:36:33.152 +08:00 [ERR] 未处理的异常::System.NullReferenceException: Object reference not set to an instance of an object.
   at XH.H82.Services.EquipmentDocService.GetEquipmentInfo(String equipmentId) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentDocService.cs:line 257
   at Castle.Proxies.Invocations.IEquipmentDocService_GetEquipmentInfo.InvokeMethodOnTarget()
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.UseCacheWhenFailedAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.CacheAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at Castle.Proxies.IEquipmentDocServiceProxy.GetEquipmentInfo(String equipmentId)
   at XH.H82.API.Controllers.EquipmentDocController.GetEquipmentInfo(String equipmentId) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\Controllers\EquipmentDocController.cs:line 122
   at lambda_method910(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-07-08 11:36:33.154 +08:00 [ERR] HTTP GET /api/EquipmentDoc/GetEquipmentInfo responded 500 in 55964.2432 ms
2025-07-08 11:36:33.154 +08:00 [INF] 【接口超时阀值预警】 [ac4aff1ed99190dcaf9fbe44b1cc5bd7]接口/api/EquipmentDoc/GetEquipmentInfo,耗时:[55964]毫秒
2025-07-08 11:36:36.280 +08:00 [INF] 第三方url地址为：http://************
2025-07-08 11:46:39.367 +08:00 [INF] HTTP GET /api/EquipmentDoc/GetEquipmentInfo responded 200 in 603109.2100 ms
2025-07-08 11:46:39.367 +08:00 [INF] 【接口超时阀值预警】 [1d1e27c91af04a9382b470a9f7ba5e67]接口/api/EquipmentDoc/GetEquipmentInfo,耗时:[603109]毫秒
2025-07-08 11:46:43.871 +08:00 [INF] 第三方url地址为：http://************
2025-07-08 11:46:58.700 +08:00 [INF] HTTP GET /api/EquipmentDoc/GetEquipmentInfo responded 200 in 14877.2449 ms
2025-07-08 11:46:58.700 +08:00 [INF] 【接口超时阀值预警】 [e64d13d2a5dc5a827ad7ab78e2d24646]接口/api/EquipmentDoc/GetEquipmentInfo,耗时:[14877]毫秒
2025-07-08 13:56:53.862 +08:00 [INF] ==>App Start..2025-07-08 13:56:53
2025-07-08 13:56:54.035 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-08 13:56:54.039 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-08 13:56:55.963 +08:00 [INF] ==>基础连接请求完成.
2025-07-08 13:56:56.441 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-08 13:56:56.853 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-08 13:56:58.189 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-08 13:56:58.191 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-08 13:56:58.531 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-08 13:56:59.571 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-08 13:57:00.084 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-08 13:57:00.696 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-08 13:57:00.696 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-08 13:57:02.087 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-08 13:57:05.245 +08:00 [INF] ==>初始化完成..
2025-07-08 13:57:05.309 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-08 13:57:05.310 +08:00 [INF] 设备启用任务
2025-07-08 13:57:05.311 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-08 13:57:05.708 +08:00 [INF] 【SQL执行耗时:375.1543ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-08 13:57:05.957 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-08 13:57:05.976 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-08 13:57:05.978 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-08 13:57:05.979 +08:00 [INF] Hosting environment: Development
2025-07-08 13:57:05.980 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-08 13:57:17.199 +08:00 [INF] HTTP GET /api/EquipmentDoc/GetEquipmentInfo responded 401 in 461.4472 ms
2025-07-08 13:57:24.763 +08:00 [INF] 第三方url地址为：http://************
2025-07-08 14:01:10.230 +08:00 [INF] HTTP GET /api/EquipmentDoc/GetEquipmentInfo responded 200 in 229877.8048 ms
2025-07-08 14:01:10.231 +08:00 [INF] 【接口超时阀值预警】 [dd2d48a4a53cd0247a1909520ecbd990]接口/api/EquipmentDoc/GetEquipmentInfo,耗时:[229878]毫秒
2025-07-08 14:07:10.380 +08:00 [INF] 第三方url地址为：http://************
2025-07-08 14:07:41.288 +08:00 [INF] HTTP GET /api/EquipmentDoc/GetEquipmentInfo responded 200 in 30951.3976 ms
2025-07-08 14:07:41.288 +08:00 [INF] 【接口超时阀值预警】 [b3e2f8995a0355e9a3a3e4062de97351]接口/api/EquipmentDoc/GetEquipmentInfo,耗时:[30951]毫秒
2025-07-08 14:32:28.507 +08:00 [INF] ==>App Start..2025-07-08 14:32:28
2025-07-08 14:32:28.673 +08:00 [INF] ==>当前模块:H82,6.25.329
2025-07-08 14:32:28.676 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-08 14:32:30.149 +08:00 [INF] ==>基础连接请求完成.
2025-07-08 14:34:13.407 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-08 14:34:13.783 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-08 14:34:14.191 +08:00 [INF] ==>S01版本写入成功:6.25.329
2025-07-08 14:34:14.902 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-08 14:34:15.003 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-08 14:34:15.443 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-08 14:34:15.443 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-08 14:34:15.620 +08:00 [ERR] 查询出错:中文提示 :  连接数据库过程中发生错误，检查服务器是否正常连接字符串是否正确，错误信息：OracleConnection.ConnectionString is invalidDbType="Oracle";ConfigId="".
English Message : Connection open error . OracleConnection.ConnectionString is invalidDbType="Oracle";ConfigId="" :
SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON" FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "LAST_MTIME" IS NULL )
2025-07-08 14:45:42.990 +08:00 [INF] ==>App Start..2025-07-08 14:45:42
2025-07-08 14:45:43.163 +08:00 [INF] ==>当前模块:H82,6.25.329
2025-07-08 14:45:43.166 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-08 14:45:44.643 +08:00 [INF] ==>基础连接请求完成.
2025-07-08 16:27:14.986 +08:00 [INF] ==>App Start..2025-07-08 16:27:14
2025-07-08 16:27:15.174 +08:00 [INF] ==>当前模块:H82,6.25.329
2025-07-08 16:27:15.178 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-08 16:27:16.897 +08:00 [INF] ==>基础连接请求完成.
2025-07-08 16:27:17.305 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-08 16:27:17.726 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-08 16:27:18.087 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-08 16:27:18.090 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-08 16:27:18.517 +08:00 [INF] ==>版本写入成功:6.25.329
2025-07-08 16:27:19.120 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-08 16:27:19.248 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-08 16:27:19.782 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-08 16:27:19.782 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-08 16:27:21.217 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-08 16:27:24.968 +08:00 [INF] ==>初始化完成..
2025-07-08 16:27:25.002 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-08 16:27:25.003 +08:00 [INF] 设备启用任务
2025-07-08 16:27:25.004 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-08 16:27:25.415 +08:00 [INF] 【SQL执行耗时:392.1259ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-08 16:27:25.593 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-08 16:27:25.611 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-08 16:27:25.613 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-08 16:27:25.613 +08:00 [INF] Hosting environment: Development
2025-07-08 16:27:25.613 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-08 16:30:41.669 +08:00 [INF] HTTP PUT /api/CodeCustom/UpdateEquipmentCodeCustomDict/******************************** responded 401 in 364.8104 ms
2025-07-08 16:30:52.719 +08:00 [ERR] 查询出错:ORA-00936: 缺失表达式:
SELECT * FROM (SELECT "CLASS_LEVEL",ROW_NUMBER() OVER(ORDER BY  ASC) AS RowIndex  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE  ('10001;10002' like '%'||"CLASS_ID"||'%') ) T WHERE RowIndex BETWEEN 1 AND 1
2025-07-08 16:31:02.038 +08:00 [ERR] 未处理的异常::Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00936: 缺失表达式
   at OracleInternal.ServiceObjects.OracleConnectionImpl.VerifyExecution(Int32& cursorId, Boolean bThrowArrayBindRelatedErrors, SqlStatementType sqlStatementType, Int32 arrayBindCount, OracleException& exceptionForArrayBindDML, Boolean& hasMoreRowsInDB, Boolean bFirstIterationDone)
   at OracleInternal.ServiceObjects.OracleCommandImpl.VerifyExecution(OracleConnectionImpl connectionImpl, Int32& cursorId, Boolean bThrowArrayBindRelatedErrors, OracleException& exceptionForArrayBindDML, Boolean& hasMoreRowsInDB, Boolean bFirstIterationDone)
   at OracleInternal.ServiceObjects.OracleCommandImpl.ExecuteReader(String commandText, OracleParameterCollection paramColl, CommandType commandType, OracleConnectionImpl connectionImpl, OracleDataReaderImpl& rdrImpl, Int32 longFetchSize, Int64 clientInitialLOBFS, OracleDependencyImpl orclDependencyImpl, Int64[] scnForExecution, Int64[]& scnFromExecution, OracleParameterCollection& bindByPositionParamColl, Boolean& bBindParamPresent, Int64& internalInitialLOBFS, Int64 internalInitialJSONFS, OracleException& exceptionForArrayBindDML, OracleConnection connection, IEnumerable`1 adrianParsedStmt, Boolean isDescribeOnly, Boolean isFromEF)
   at Oracle.ManagedDataAccess.Client.OracleCommand.ExecuteReader(Boolean requery, Boolean fillRequest, CommandBehavior behavior)
   at Oracle.ManagedDataAccess.Client.OracleCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at SqlSugar.AdoProvider.GetDataReader(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetData[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToList[TResult]()
   at SqlSugar.QueryableProvider`1.ToList()
   at SqlSugar.QueryableProvider`1.First()
   at XH.H82.Services.EquipmentCodeCustom.CustomCodeService.UpdateEquipmentCodeCustomDict(UpdateEquipmentCodeCustomDictDto input)
   at Castle.Proxies.Invocations.ICustomCodeService_UpdateEquipmentCodeCustomDict.InvokeMethodOnTarget()
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.UseCacheWhenFailedAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.CacheAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at Castle.Proxies.ICustomCodeServiceProxy.UpdateEquipmentCodeCustomDict(UpdateEquipmentCodeCustomDictDto input)
   at XH.H82.API.Controllers.EquipmentCodeCustom.CodeCustomController.UpdateEquipmentCodeCustomDict(String EqpNoId, UpdateEquipmentCodeCustomDictDto input) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\Controllers\EquipmentCodeCustom\CodeCustomController.cs:line 84
   at lambda_method910(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-07-08 16:31:02.043 +08:00 [ERR] HTTP PUT /api/CodeCustom/UpdateEquipmentCodeCustomDict/******************************** responded 500 in 16185.4644 ms
2025-07-08 16:31:02.045 +08:00 [INF] 【接口超时阀值预警】 [1c99f7d9f3f79ea90e6d7f93802a04af]接口/api/CodeCustom/UpdateEquipmentCodeCustomDict/********************************,耗时:[16186]毫秒
2025-07-08 16:31:23.931 +08:00 [ERR] 查询出错:ORA-00936: 缺失表达式:
SELECT * FROM (SELECT "CLASS_LEVEL",ROW_NUMBER() OVER(ORDER BY  ASC) AS RowIndex  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE  ('10001;10002' like '%'||"CLASS_ID"||'%') ) T WHERE RowIndex BETWEEN 1 AND 1
2025-07-08 16:32:18.510 +08:00 [INF] ==>App Start..2025-07-08 16:32:18
2025-07-08 16:32:18.774 +08:00 [INF] ==>当前模块:H82,6.25.329
2025-07-08 16:32:18.778 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-08 16:32:20.238 +08:00 [INF] ==>基础连接请求完成.
2025-07-08 16:32:20.609 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-08 16:32:21.025 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-08 16:32:21.356 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-08 16:32:21.358 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-08 16:32:21.743 +08:00 [INF] ==>版本写入成功:6.25.329
2025-07-08 16:32:22.092 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-08 16:32:22.176 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-08 16:32:22.593 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-08 16:32:22.594 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-08 16:32:23.548 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-08 16:32:26.211 +08:00 [INF] ==>初始化完成..
2025-07-08 16:32:26.232 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-08 16:32:26.236 +08:00 [INF] 设备启用任务
2025-07-08 16:32:26.237 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-08 16:32:26.663 +08:00 [INF] 【SQL执行耗时:406.6701ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-08 16:32:26.827 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-08 16:32:26.843 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-08 16:32:26.845 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-08 16:32:26.845 +08:00 [INF] Hosting environment: Development
2025-07-08 16:32:26.845 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-08 16:32:38.426 +08:00 [INF] 【SQL执行耗时:365.7984ms】

[Sql]:SELECT * FROM (SELECT "CLASS_LEVEL",ROW_NUMBER() OVER(ORDER BY "CLASS_LEVEL" ASC) AS RowIndex  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE  (:MethodConst0 like '%'||"CLASS_ID"||'%') ) T WHERE RowIndex BETWEEN 1 AND 1 
[Pars]:
[Name]::MethodConst0 [Value]:10001;10002 [Type]:String    

2025-07-08 16:32:58.768 +08:00 [INF] 【SQL执行耗时:412.0248ms】

[Sql]: SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK"  FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_ID" = :EqpNoId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EqpNoId0 [Value]:******************************** [Type]:String    

2025-07-08 16:32:59.307 +08:00 [INF] 【SQL执行耗时:379.5488ms】

[Sql]:UPDATE "XH_OA"."EMS_EQPNO_FORMAT_DICT"  SET
           "HOSPITAL_ID"=:HOSPITAL_ID,"EQP_NO_NAME"=:EQP_NO_NAME,"EQP_NO_LEVEL"=:EQP_NO_LEVEL,"EQP_NO_CLASS"=:EQP_NO_CLASS,"EQP_DISPLAY_JSON"=:EQP_DISPLAY_JSON,"EQP_NO_APPLYS"=:EQP_NO_APPLYS,"EQP_NO_STATE"=:EQP_NO_STATE,"FIRST_RPERSON"=:FIRST_RPERSON,"FIRST_RTIME"=:FIRST_RTIME,"LAST_MPERSON"=:LAST_MPERSON,"LAST_MTIME"=:LAST_MTIME,"REMARK"=:REMARK  WHERE "EQP_NO_ID"=:EQP_NO_ID 
[Pars]:
[Name]::EQP_NO_ID [Value]:******************************** [Type]:String    
[Name]::HOSPITAL_ID [Value]:33A001 [Type]:String    
[Name]::EQP_NO_NAME [Value]:测试2 [Type]:String    
[Name]::EQP_NO_LEVEL [Value]:1 [Type]:String    
[Name]::EQP_NO_CLASS [Value]:10001;10002 [Type]:String    
[Name]::EQP_DISPLAY_JSON [Value]:{"DisplayContentString":"{院区}_{科室}_{设备名称（中文）}_{设备代号}","DisplayContentCode":"{AREA_NAME}_{LAB_NAME}_{EQUIPMENT_NAME}_{EQUIPMENT_CODE}","FixedFieldDisplays":[{"Key":"{AREA_NAME}","Value":"A"},{"Key":"{LAB_NAME}","Value":"LABID"}]} [Type]:String    
[Name]::EQP_NO_APPLYS [Value]: [Type]:String    
[Name]::EQP_NO_STATE [Value]:1 [Type]:String    
[Name]::FIRST_RPERSON [Value]:gz1_广测重 [Type]:String    
[Name]::FIRST_RTIME [Value]:2025/7/1 10:00:03 [Type]:DateTime    
[Name]::LAST_MPERSON [Value]:fr_李影 [Type]:String    
[Name]::LAST_MTIME [Value]:2025/7/8 16:32:58 [Type]:DateTime    
[Name]::REMARK [Value]: [Type]:String    

2025-07-08 16:32:59.399 +08:00 [INF] HTTP PUT /api/CodeCustom/UpdateEquipmentCodeCustomDict/******************************** responded 200 in 26907.9805 ms
2025-07-08 16:32:59.402 +08:00 [INF] 【接口超时阀值预警】 [732209b84db0b8e4dd06f6d12ec69fc9]接口/api/CodeCustom/UpdateEquipmentCodeCustomDict/********************************,耗时:[26915]毫秒
2025-07-08 16:34:24.355 +08:00 [INF] 【SQL执行耗时:354.3523ms】

[Sql]:SELECT * FROM (SELECT "CLASS_LEVEL",ROW_NUMBER() OVER(ORDER BY "CLASS_LEVEL" ASC) AS RowIndex  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE  (:MethodConst0 like '%'||"CLASS_ID"||'%') ) T WHERE RowIndex BETWEEN 1 AND 1 
[Pars]:
[Name]::MethodConst0 [Value]:0 [Type]:String    

2025-07-08 16:34:27.519 +08:00 [INF] 【SQL执行耗时:358.5944ms】

[Sql]: SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK"  FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_ID" = :EqpNoId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EqpNoId0 [Value]:******************************** [Type]:String    

2025-07-08 16:34:27.971 +08:00 [INF] 【SQL执行耗时:344.242ms】

[Sql]:UPDATE "XH_OA"."EMS_EQPNO_FORMAT_DICT"  SET
           "HOSPITAL_ID"=:HOSPITAL_ID,"EQP_NO_NAME"=:EQP_NO_NAME,"EQP_NO_LEVEL"=:EQP_NO_LEVEL,"EQP_NO_CLASS"=:EQP_NO_CLASS,"EQP_DISPLAY_JSON"=:EQP_DISPLAY_JSON,"EQP_NO_APPLYS"=:EQP_NO_APPLYS,"EQP_NO_STATE"=:EQP_NO_STATE,"FIRST_RPERSON"=:FIRST_RPERSON,"FIRST_RTIME"=:FIRST_RTIME,"LAST_MPERSON"=:LAST_MPERSON,"LAST_MTIME"=:LAST_MTIME,"REMARK"=:REMARK  WHERE "EQP_NO_ID"=:EQP_NO_ID 
[Pars]:
[Name]::EQP_NO_ID [Value]:******************************** [Type]:String    
[Name]::HOSPITAL_ID [Value]:33A001 [Type]:String    
[Name]::EQP_NO_NAME [Value]:测试1231231231232 [Type]:String    
[Name]::EQP_NO_LEVEL [Value]:0 [Type]:String    
[Name]::EQP_NO_CLASS [Value]:0 [Type]:String    
[Name]::EQP_DISPLAY_JSON [Value]:{"DisplayContentString":"{院区}_{科室}_{设备名称（中文）}_{设备代号}","DisplayContentCode":"{AREA_NAME}_{LAB_NAME}_{EQUIPMENT_NAME}_{EQUIPMENT_CODE}","FixedFieldDisplays":[{"Key":"{AREA_NAME}","Value":"A"},{"Key":"{LAB_NAME}","Value":"LABID"}]} [Type]:String    
[Name]::EQP_NO_APPLYS [Value]: [Type]:String    
[Name]::EQP_NO_STATE [Value]:1 [Type]:String    
[Name]::FIRST_RPERSON [Value]:gz1_广测重 [Type]:String    
[Name]::FIRST_RTIME [Value]:2025/7/1 10:00:03 [Type]:DateTime    
[Name]::LAST_MPERSON [Value]:fr_李影 [Type]:String    
[Name]::LAST_MTIME [Value]:2025/7/8 16:34:27 [Type]:DateTime    
[Name]::REMARK [Value]: [Type]:String    

2025-07-08 16:34:28.008 +08:00 [INF] HTTP PUT /api/CodeCustom/UpdateEquipmentCodeCustomDict/******************************** responded 200 in 8129.9679 ms
2025-07-08 16:34:28.008 +08:00 [INF] 【接口超时阀值预警】 [177f8aa32120b18d86bc048457163027]接口/api/CodeCustom/UpdateEquipmentCodeCustomDict/********************************,耗时:[8130]毫秒
2025-07-08 16:35:03.213 +08:00 [INF] 【SQL执行耗时:401.2601ms】

[Sql]:SELECT * FROM (SELECT "CLASS_LEVEL",ROW_NUMBER() OVER(ORDER BY "CLASS_LEVEL" ASC) AS RowIndex  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE  (:MethodConst0 like '%'||"CLASS_ID"||'%') ) T WHERE RowIndex BETWEEN 1 AND 1 
[Pars]:
[Name]::MethodConst0 [Value]:10001;10002;10003 [Type]:String    

2025-07-08 16:35:16.677 +08:00 [INF] 【SQL执行耗时:363.5007ms】

[Sql]: SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK"  FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_ID" = :EqpNoId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EqpNoId0 [Value]:******************************** [Type]:String    

2025-07-08 16:35:17.150 +08:00 [INF] 【SQL执行耗时:364.8763ms】

[Sql]:UPDATE "XH_OA"."EMS_EQPNO_FORMAT_DICT"  SET
           "HOSPITAL_ID"=:HOSPITAL_ID,"EQP_NO_NAME"=:EQP_NO_NAME,"EQP_NO_LEVEL"=:EQP_NO_LEVEL,"EQP_NO_CLASS"=:EQP_NO_CLASS,"EQP_DISPLAY_JSON"=:EQP_DISPLAY_JSON,"EQP_NO_APPLYS"=:EQP_NO_APPLYS,"EQP_NO_STATE"=:EQP_NO_STATE,"FIRST_RPERSON"=:FIRST_RPERSON,"FIRST_RTIME"=:FIRST_RTIME,"LAST_MPERSON"=:LAST_MPERSON,"LAST_MTIME"=:LAST_MTIME,"REMARK"=:REMARK  WHERE "EQP_NO_ID"=:EQP_NO_ID 
[Pars]:
[Name]::EQP_NO_ID [Value]:******************************** [Type]:String    
[Name]::HOSPITAL_ID [Value]:33A001 [Type]:String    
[Name]::EQP_NO_NAME [Value]:测试1231231231232 [Type]:String    
[Name]::EQP_NO_LEVEL [Value]:1 [Type]:String    
[Name]::EQP_NO_CLASS [Value]:10001;10002;10003 [Type]:String    
[Name]::EQP_DISPLAY_JSON [Value]:{"DisplayContentString":"{院区}_{科室}_{设备名称（中文）}_{设备代号}","DisplayContentCode":"{AREA_NAME}_{LAB_NAME}_{EQUIPMENT_NAME}_{EQUIPMENT_CODE}","FixedFieldDisplays":[{"Key":"{AREA_NAME}","Value":"A"},{"Key":"{LAB_NAME}","Value":"LABID"}]} [Type]:String    
[Name]::EQP_NO_APPLYS [Value]: [Type]:String    
[Name]::EQP_NO_STATE [Value]:1 [Type]:String    
[Name]::FIRST_RPERSON [Value]:gz1_广测重 [Type]:String    
[Name]::FIRST_RTIME [Value]:2025/7/1 10:00:03 [Type]:DateTime    
[Name]::LAST_MPERSON [Value]:fr_李影 [Type]:String    
[Name]::LAST_MTIME [Value]:2025/7/8 16:35:16 [Type]:DateTime    
[Name]::REMARK [Value]: [Type]:String    

2025-07-08 16:35:17.192 +08:00 [INF] HTTP PUT /api/CodeCustom/UpdateEquipmentCodeCustomDict/******************************** responded 200 in 15900.7365 ms
2025-07-08 16:35:17.192 +08:00 [INF] 【接口超时阀值预警】 [2e7e47a9df0da68736f2059ebe8e98ba]接口/api/CodeCustom/UpdateEquipmentCodeCustomDict/********************************,耗时:[15901]毫秒
2025-07-08 16:35:43.339 +08:00 [INF] 【SQL执行耗时:372.7927ms】

[Sql]:SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON" FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE  (:MethodConst0 like '%'||"CLASS_ID"||'%')  
[Pars]:
[Name]::MethodConst0 [Value]:10003 [Type]:String    

2025-07-08 16:35:43.751 +08:00 [INF] 【SQL执行耗时:369.0222ms】

[Sql]:SELECT * FROM (SELECT "CLASS_LEVEL",ROW_NUMBER() OVER(ORDER BY "CLASS_LEVEL" ASC) AS RowIndex  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE  (:MethodConst0 like '%'||"CLASS_ID"||'%') ) T WHERE RowIndex BETWEEN 1 AND 1 
[Pars]:
[Name]::MethodConst0 [Value]:10003 [Type]:String    

2025-07-08 16:36:53.176 +08:00 [INF] 【SQL执行耗时:364.3083ms】

[Sql]:SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON" FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE  ("CLASS_ID" IN ('10003')) 

2025-07-08 16:36:58.551 +08:00 [INF] 【SQL执行耗时:334.3649ms】

[Sql]:SELECT * FROM (SELECT "CLASS_LEVEL",ROW_NUMBER() OVER(ORDER BY "CLASS_LEVEL" ASC) AS RowIndex  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE  (:MethodConst0 like '%'||"CLASS_ID"||'%') ) T WHERE RowIndex BETWEEN 1 AND 1 
[Pars]:
[Name]::MethodConst0 [Value]:10003 [Type]:String    

2025-07-08 16:37:14.806 +08:00 [INF] 【SQL执行耗时:373.3311ms】

[Sql]:SELECT * FROM (SELECT "CLASS_LEVEL",ROW_NUMBER() OVER(ORDER BY "CLASS_LEVEL" ASC) AS RowIndex  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE  ("CLASS_ID" IN ('10003')) ) T WHERE RowIndex BETWEEN 1 AND 1

2025-07-08 16:37:17.308 +08:00 [INF] 【SQL执行耗时:358.7793ms】

[Sql]: SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK"  FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_ID" = :EqpNoId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EqpNoId0 [Value]:******************************** [Type]:String    

2025-07-08 16:37:17.774 +08:00 [INF] 【SQL执行耗时:356.7358ms】

[Sql]:UPDATE "XH_OA"."EMS_EQPNO_FORMAT_DICT"  SET
           "HOSPITAL_ID"=:HOSPITAL_ID,"EQP_NO_NAME"=:EQP_NO_NAME,"EQP_NO_LEVEL"=:EQP_NO_LEVEL,"EQP_NO_CLASS"=:EQP_NO_CLASS,"EQP_DISPLAY_JSON"=:EQP_DISPLAY_JSON,"EQP_NO_APPLYS"=:EQP_NO_APPLYS,"EQP_NO_STATE"=:EQP_NO_STATE,"FIRST_RPERSON"=:FIRST_RPERSON,"FIRST_RTIME"=:FIRST_RTIME,"LAST_MPERSON"=:LAST_MPERSON,"LAST_MTIME"=:LAST_MTIME,"REMARK"=:REMARK  WHERE "EQP_NO_ID"=:EQP_NO_ID 
[Pars]:
[Name]::EQP_NO_ID [Value]:******************************** [Type]:String    
[Name]::HOSPITAL_ID [Value]:33A001 [Type]:String    
[Name]::EQP_NO_NAME [Value]:测试1231231231232 [Type]:String    
[Name]::EQP_NO_LEVEL [Value]:2 [Type]:String    
[Name]::EQP_NO_CLASS [Value]:10003 [Type]:String    
[Name]::EQP_DISPLAY_JSON [Value]:{"DisplayContentString":"{院区}_{科室}_{设备名称（中文）}_{设备代号}","DisplayContentCode":"{AREA_NAME}_{LAB_NAME}_{EQUIPMENT_NAME}_{EQUIPMENT_CODE}","FixedFieldDisplays":[{"Key":"{AREA_NAME}","Value":"A"},{"Key":"{LAB_NAME}","Value":"LABID"}]} [Type]:String    
[Name]::EQP_NO_APPLYS [Value]: [Type]:String    
[Name]::EQP_NO_STATE [Value]:1 [Type]:String    
[Name]::FIRST_RPERSON [Value]:gz1_广测重 [Type]:String    
[Name]::FIRST_RTIME [Value]:2025/7/1 10:00:03 [Type]:DateTime    
[Name]::LAST_MPERSON [Value]:fr_李影 [Type]:String    
[Name]::LAST_MTIME [Value]:2025/7/8 16:37:17 [Type]:DateTime    
[Name]::REMARK [Value]: [Type]:String    

2025-07-08 16:37:17.811 +08:00 [INF] HTTP PUT /api/CodeCustom/UpdateEquipmentCodeCustomDict/******************************** responded 200 in 96198.3192 ms
2025-07-08 16:37:17.811 +08:00 [INF] 【接口超时阀值预警】 [504eb86b1cef1621ba20c3e7936ccba8]接口/api/CodeCustom/UpdateEquipmentCodeCustomDict/********************************,耗时:[96198]毫秒
2025-07-08 16:39:14.523 +08:00 [INF] 【SQL执行耗时:589.1115ms】

[Sql]:SELECT * FROM (SELECT "CLASS_LEVEL",ROW_NUMBER() OVER(ORDER BY "CLASS_LEVEL" ASC) AS RowIndex  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE  ("CLASS_ID" IN ('1','10001','10002','10003')) ) T WHERE RowIndex BETWEEN 1 AND 1

2025-07-08 16:39:14.917 +08:00 [INF] 【SQL执行耗时:357.6097ms】

[Sql]: SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK"  FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_ID" = :EqpNoId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EqpNoId0 [Value]:******************************** [Type]:String    

2025-07-08 16:39:15.366 +08:00 [INF] 【SQL执行耗时:338.0513ms】

[Sql]:UPDATE "XH_OA"."EMS_EQPNO_FORMAT_DICT"  SET
           "HOSPITAL_ID"=:HOSPITAL_ID,"EQP_NO_NAME"=:EQP_NO_NAME,"EQP_NO_LEVEL"=:EQP_NO_LEVEL,"EQP_NO_CLASS"=:EQP_NO_CLASS,"EQP_DISPLAY_JSON"=:EQP_DISPLAY_JSON,"EQP_NO_APPLYS"=:EQP_NO_APPLYS,"EQP_NO_STATE"=:EQP_NO_STATE,"FIRST_RPERSON"=:FIRST_RPERSON,"FIRST_RTIME"=:FIRST_RTIME,"LAST_MPERSON"=:LAST_MPERSON,"LAST_MTIME"=:LAST_MTIME,"REMARK"=:REMARK  WHERE "EQP_NO_ID"=:EQP_NO_ID 
[Pars]:
[Name]::EQP_NO_ID [Value]:******************************** [Type]:String    
[Name]::HOSPITAL_ID [Value]:33A001 [Type]:String    
[Name]::EQP_NO_NAME [Value]:测试1231231231232 [Type]:String    
[Name]::EQP_NO_LEVEL [Value]:1 [Type]:String    
[Name]::EQP_NO_CLASS [Value]:1;10001;10002;10003 [Type]:String    
[Name]::EQP_DISPLAY_JSON [Value]:{"DisplayContentString":"{院区}_{科室}_{设备名称（中文）}_{设备代号}","DisplayContentCode":"{AREA_NAME}_{LAB_NAME}_{EQUIPMENT_NAME}_{EQUIPMENT_CODE}","FixedFieldDisplays":[{"Key":"{AREA_NAME}","Value":"A"},{"Key":"{LAB_NAME}","Value":"LABID"}]} [Type]:String    
[Name]::EQP_NO_APPLYS [Value]: [Type]:String    
[Name]::EQP_NO_STATE [Value]:1 [Type]:String    
[Name]::FIRST_RPERSON [Value]:gz1_广测重 [Type]:String    
[Name]::FIRST_RTIME [Value]:2025/7/1 10:00:03 [Type]:DateTime    
[Name]::LAST_MPERSON [Value]:fr_李影 [Type]:String    
[Name]::LAST_MTIME [Value]:2025/7/8 16:39:15 [Type]:DateTime    
[Name]::REMARK [Value]: [Type]:String    

2025-07-08 16:39:15.401 +08:00 [INF] HTTP PUT /api/CodeCustom/UpdateEquipmentCodeCustomDict/******************************** responded 200 in 5298.2494 ms
2025-07-08 16:39:15.402 +08:00 [INF] 【接口超时阀值预警】 [6d489750deb370fececb710f11a66b04]接口/api/CodeCustom/UpdateEquipmentCodeCustomDict/********************************,耗时:[5298]毫秒
2025-07-08 16:45:35.929 +08:00 [INF] 第三方url地址为：http://************
2025-07-08 16:46:13.482 +08:00 [INF] HTTP GET /api/EquipmentDoc/GetEquipmentInfo responded 200 in 38339.3485 ms
2025-07-08 16:46:13.483 +08:00 [INF] 【接口超时阀值预警】 [8e6a8281bb4219be8c28935820e6184f]接口/api/EquipmentDoc/GetEquipmentInfo,耗时:[38339]毫秒
2025-07-08 17:13:43.645 +08:00 [INF] ==>App Start..2025-07-08 17:13:43
2025-07-08 17:13:43.825 +08:00 [INF] ==>当前模块:H82,6.25.329
2025-07-08 17:13:43.828 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-08 17:13:45.327 +08:00 [INF] ==>基础连接请求完成.
2025-07-08 17:13:45.703 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-08 17:13:46.151 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-08 17:13:46.497 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-08 17:13:46.507 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-08 17:13:46.894 +08:00 [INF] ==>版本写入成功:6.25.329
2025-07-08 17:13:47.349 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-08 17:13:47.463 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-08 17:13:47.917 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-08 17:13:47.918 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-08 17:13:49.015 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-08 17:13:51.785 +08:00 [INF] ==>初始化完成..
2025-07-08 17:13:51.809 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-08 17:13:51.812 +08:00 [INF] 设备启用任务
2025-07-08 17:13:51.813 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-08 17:13:52.207 +08:00 [INF] 【SQL执行耗时:375.5311ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-08 17:13:52.371 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-08 17:13:52.385 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-08 17:13:52.387 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-08 17:13:52.387 +08:00 [INF] Hosting environment: Development
2025-07-08 17:13:52.388 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-08 17:23:41.449 +08:00 [INF] HTTP GET /api/EquipmentDoc/GetEquipmentInfo responded 401 in 269.6527 ms
2025-07-08 17:23:55.155 +08:00 [INF] 【SQL执行耗时:474.599ms】

[Sql]:SELECT * FROM (SELECT "CLASS_LEVEL",ROW_NUMBER() OVER(ORDER BY "CLASS_LEVEL" ASC) AS RowIndex  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE  ("CLASS_ID" IN ('1','10001','10002','10003')) ) T WHERE RowIndex BETWEEN 1 AND 1

2025-07-08 17:23:55.577 +08:00 [INF] 【SQL执行耗时:372.2407ms】

[Sql]: SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK"  FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_ID" = :EqpNoId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EqpNoId0 [Value]:******************************** [Type]:String    

2025-07-08 17:23:56.102 +08:00 [INF] 【SQL执行耗时:376.3982ms】

[Sql]:UPDATE "XH_OA"."EMS_EQPNO_FORMAT_DICT"  SET
           "HOSPITAL_ID"=:HOSPITAL_ID,"EQP_NO_NAME"=:EQP_NO_NAME,"EQP_NO_LEVEL"=:EQP_NO_LEVEL,"EQP_NO_CLASS"=:EQP_NO_CLASS,"EQP_DISPLAY_JSON"=:EQP_DISPLAY_JSON,"EQP_NO_APPLYS"=:EQP_NO_APPLYS,"EQP_NO_STATE"=:EQP_NO_STATE,"FIRST_RPERSON"=:FIRST_RPERSON,"FIRST_RTIME"=:FIRST_RTIME,"LAST_MPERSON"=:LAST_MPERSON,"LAST_MTIME"=:LAST_MTIME,"REMARK"=:REMARK  WHERE "EQP_NO_ID"=:EQP_NO_ID 
[Pars]:
[Name]::EQP_NO_ID [Value]:******************************** [Type]:String    
[Name]::HOSPITAL_ID [Value]:33A001 [Type]:String    
[Name]::EQP_NO_NAME [Value]:测试1231231231232 [Type]:String    
[Name]::EQP_NO_LEVEL [Value]:1 [Type]:String    
[Name]::EQP_NO_CLASS [Value]:1;10001;10002;10003 [Type]:String    
[Name]::EQP_DISPLAY_JSON [Value]:{"DisplayContentString":"{院区}_{科室}_{设备名称（中文）}_{设备代号}","DisplayContentCode":"{AREA_NAME}_{LAB_NAME}_{EQUIPMENT_NAME}_{EQUIPMENT_CODE}","FixedFieldDisplays":[{"Key":"{AREA_NAME}","Value":"A"},{"Key":"{LAB_NAME}","Value":"LABID"}]} [Type]:String    
[Name]::EQP_NO_APPLYS [Value]: [Type]:String    
[Name]::EQP_NO_STATE [Value]:1 [Type]:String    
[Name]::FIRST_RPERSON [Value]:gz1_广测重 [Type]:String    
[Name]::FIRST_RTIME [Value]:2025/7/1 10:00:03 [Type]:DateTime    
[Name]::LAST_MPERSON [Value]:fr_李影 [Type]:String    
[Name]::LAST_MTIME [Value]:2025/7/8 17:23:55 [Type]:DateTime    
[Name]::REMARK [Value]: [Type]:String    

2025-07-08 17:23:58.379 +08:00 [INF] 【SQL执行耗时:371.4555ms】

[Sql]:SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" = :EqpNoState0 ) 
[Pars]:
[Name]::EqpNoState0 [Value]:1 [Type]:String    

2025-07-08 18:47:35.270 +08:00 [INF] ==>App Start..2025-07-08 18:47:35
2025-07-08 18:47:35.457 +08:00 [INF] ==>当前模块:H82,6.25.329
2025-07-08 18:47:35.461 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-08 18:47:42.031 +08:00 [INF] ==>App Start..2025-07-08 18:47:42
2025-07-08 18:47:42.203 +08:00 [INF] ==>当前模块:H82,6.25.329
2025-07-08 18:47:42.206 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-08 18:47:43.698 +08:00 [INF] ==>基础连接请求完成.
2025-07-08 18:47:44.072 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-08 18:47:44.443 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-08 18:47:44.777 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-08 18:47:44.782 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-08 18:47:45.117 +08:00 [INF] ==>版本写入成功:6.25.329
2025-07-08 18:47:45.687 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-08 18:47:45.787 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
