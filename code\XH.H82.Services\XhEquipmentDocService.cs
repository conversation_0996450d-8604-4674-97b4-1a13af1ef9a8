﻿using System.Net;
using System.ServiceModel;
using AutoMapper;
using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using ReportService;
using RestSharp;
using Spire.Doc;
using Spire.Doc.Documents;
using Spire.Doc.Fields;
using SqlSugar;
using XH.H82.IServices;
using XH.H82.Models;
using XH.H82.Models.DeviceRelevantInformation;
using XH.H82.Models.Dtos;
using XH.H82.Models.Entities;
using XH.H82.Models.Entities.Common;
using XH.H82.Models.SugarDbContext;
using XH.H82.Services.DeviceDataRefresh;
using XH.LAB.UTILS.Helpers;
using XH.LAB.UTILS.Interface;
using XH.LAB.UTILS.Models;
using XH.LAB.UTILS.Models.Dto;
using SYS6_HOSPITAL_INFO = XH.LAB.UTILS.Models.SYS6_HOSPITAL_INFO;
using UploadFileDto = XH.LAB.UTILS.Models.Dto.UploadFileDto;


namespace XH.H82.Services
{
    public class XhEquipmentDocService : IXhEquipmentDocService
    {
        private readonly IBaseService _baseService;
        private readonly ILogger<XhEquipmentDocService> _logger;
        private readonly ISqlSugarUow<SugarDbContext_Master> _sqlSugarUow;
        private readonly RestClient _clientS13;
        private XingHePrintServiceSoapClient _client;
        private readonly IHostingEnvironment _hostingEnvironment;
        private readonly IMapper _mapper;
        private readonly IHttpContextAccessor _httpContext;
        private readonly string file_preview_address = "";
        private readonly string FileHttpUrl = "/H82pdf/";
        private readonly IUploadFileService _IUploadFileService;
        private readonly IEquipmentDocService _equipmentDocService;
        private readonly IBaseDataServices _basedataService;
        private readonly IAuthorityService2 _authorityService;

        //接口服务地址
        private readonly string _serviceAddress;
        public XhEquipmentDocService(IBaseService baseService, ISqlSugarUow<SugarDbContext_Master> sqlSugarUow, IConfiguration configuration, IHttpContextAccessor httpContext, IHostingEnvironment hostingEnvironment, IMapper mapper, IUploadFileService IUploadFileService, ILogger<XhEquipmentDocService> logger, IEquipmentDocService equipmentDocService, IBaseDataServices basedataService, IAuthorityService2 authorityService)
        {
            _baseService = baseService;
            _sqlSugarUow = sqlSugarUow;
            _httpContext = httpContext;
            _hostingEnvironment = hostingEnvironment;
            _mapper = mapper;
            _logger = logger;
            var binding = new BasicHttpBinding();
            binding.MaxReceivedMessageSize = 241000000;
            _IUploadFileService = IUploadFileService;
            _serviceAddress = configuration["ReportService"];
            _client = new XingHePrintServiceSoapClient(binding, new EndpointAddress(_serviceAddress));

            var addressS13 = configuration["S16"];
            file_preview_address = configuration["S54"];
            //var addressS13 = "https://*************:18706/api/pdf";
            if (addressS13.IsNotNullOrEmpty())
            {
                _clientS13 = new RestClient(new RestClientOptions
                {
                    RemoteCertificateValidationCallback = (Sender, certificate, chain, sslPolicyErrors) => true,
                    BaseUrl = new Uri(addressS13),
                    ThrowOnAnyError = true
                });
            }
            ExecutingChangeSqlHelper.ExecutingChangeSql(_sqlSugarUow);
            _equipmentDocService = equipmentDocService;
            _basedataService = basedataService;
            _authorityService = authorityService;
        }
        public ResultDto AutoDispatchUploadFile(Dictionary<string, object> dataDict, UploadZipDto zipFile)
        {
            //FileFunctionDispatcher dispatcher = RegisterDispatcher();
            //return _IUploadFileService.AutoDispatchUploadFile(dispatcher, zipFile, dataDict);
            return new ResultDto();
        }

        public List<EMS_EQUIPMENT_INFO> GetEquipmentListByName(string hospitalId, string? areaId, string? userNo, string? state, string? type, string? mgroupId, string? equipName, string? labId, string? pgroupId)
        {
            //专业组列表
            var groupList =
                _authorityService.GetUserPermissionPgroup(_sqlSugarUow,new OrgParams()
                {
                    hospital_id = hospitalId,
                    lab_id =  labId,
                    area_id = areaId
                },"H82");
            var q = _sqlSugarUow.Db.Queryable<EMS_EQUIPMENT_INFO>()
                .Where(p => groupList.Select(i => i.PGROUP_ID).Contains(p.UNIT_ID))
                .WhereIF(state.IsNotNullOrEmpty(), p => p.EQUIPMENT_STATE == state)
                .WhereIF(type.IsNotNullOrEmpty(), p => p.EQUIPMENT_CLASS == type)
                .WhereIF(pgroupId.IsNotNullOrEmpty(), p => p.UNIT_ID == pgroupId)
                .WhereIF(equipName.IsNotNullOrEmpty(), p => p.EQUIPMENT_NAME.ToLower().Equals(equipName.ToLower()))
                .Includes(p => p.eMS_WORK_PLAN);
            if (mgroupId.IsNotNullOrEmpty())
            {
                var pList = _sqlSugarUow.Db.Queryable<SYS6_INSPECTION_PGROUP>()
                    .Where(p => p.MGROUP_ID == mgroupId && p.PGROUP_STATE == "1")
                    .Select(i => i.PGROUP_ID).ToList();
                q = q.Where(p => pList.Contains(p.UNIT_ID));
            }
            var res = q.ToList();
            return res;
        }

        public ResultDto HideEquipment(string equipmentId, string userName)
        {
            var result = new ResultDto();
            try
            {
                var old = _sqlSugarUow.Db.Queryable<EMS_EQUIPMENT_INFO>()
                    .Where(p => p.EQUIPMENT_ID == equipmentId)
                    .First();
                if (old == null)
                {
                    return new ResultDto { success = false, msg = "未找到设备信息" };
                }
                old.IS_HIDE = old.IS_HIDE == "1" ? "0" : "1";
                old.LAST_MPERSON = userName;
                old.LAST_MTIME = DateTime.Now;
                _sqlSugarUow.Db.Updateable(old).ExecuteCommand();
                result.success = true;
            }
            catch (Exception ex)
            {
                result.success = false;
                result.msg = $"操作失败:{ex.Message}";
                _logger.LogError("操作失败:\n" + ex.Message);
            }
            return result;
        }


        public string GetAreaIdByGroupId(string pGroupid)
        {

            var group = _sqlSugarUow.Db.Queryable<SYS6_INSPECTION_PGROUP>().First(x => x.PGROUP_ID == pGroupid);

            if (group is null)
            {
                return "";
            }
            else
            {
                return group.AREA_ID;
            }

        }



        public List<NewOATreeDto> GetEquipmentListByInspection(string userNo, string? equipmentNo, string labId, string? areaId, string? mgroupid, string? pgroupid, string? isHide, string? smblFlag)
        {

            var equipmentContext = new EquipmentContext(_sqlSugarUow);
            //基础数据
            var trees = _baseService.GetAllHospitalMgroups(userNo, labId);

            var pgids = new List<string>();

            trees.ForEach(tree =>
            {
                if (areaId.IsNotNullOrEmpty())
                {
                    if (tree.AREA_ID != areaId)
                    {
                        return;
                    }
                }
                tree.SecondStageTree.ForEach(secondStageTree =>
                {
                    if (mgroupid.IsNotNullOrEmpty())
                    {
                        if (secondStageTree.MGROUP_ID != mgroupid)
                        {
                            return;
                        }
                    }
                    secondStageTree.ThirdStageTree.ForEach(x =>
                    {
                        if (pgroupid.IsNotNullOrEmpty())
                        {
                            if (x.PGROUP_ID != pgroupid)
                            {
                                return;
                            }
                        }
                        pgids.Add(x.PGROUP_ID);

                    });
                });
            });


            var equipments =
                _equipmentDocService.GetEquipmentsBycondition(false, labId, areaId, mgroupid,pgroupid, null, null, equipmentNo,
                    smblFlag)
                    .Where(x => pgids.Contains(x.UNIT_ID))
                    .Select(a => new
                    {
                        a.EQUIPMENT_ID,
                        a.EQUIPMENT_CODE,
                        a.EQUIPMENT_STATE,
                        a.EQUIPMENT_CLASS,
                        a.EQUIPMENT_TYPE,
                        a.INSTRUMENT_ID,
                        a.VEST_PIPELINE,
                        a.DEPT_SECTION_NO,
                        a.UNIT_ID,
                        a.IS_HIDE,
                        a.SMBL_FLAG,
                        a.EQUIPMENT_UCODE
                    })
                    .ToList(); 

            if (equipments.Any(x => x.VEST_PIPELINE != null))
            {
                var masterEquipments = new List<EMS_EQUIPMENT_INFO>();
                var pipEquipments = equipments.Where(x=>x.VEST_PIPELINE!=null);
                foreach (var pipEquipment in pipEquipments)
                {
                    var  masterEquipment = _sqlSugarUow.Db.Queryable<EMS_EQUIPMENT_INFO>()
                        .First(x => x.EQUIPMENT_ID == pipEquipment.VEST_PIPELINE);
                    if (masterEquipment is null)
                    {
                        continue;
                    }
                    if (equipments.All(x => x.EQUIPMENT_ID != masterEquipment.EQUIPMENT_ID))
                    {
                        masterEquipments.Add(masterEquipment);
                    }
                }
                equipments.AddRange(masterEquipments.DistinctBy(x => x.EQUIPMENT_ID).Select(a =>new
                {
                    a.EQUIPMENT_ID,
                    a.EQUIPMENT_CODE,
                    a.EQUIPMENT_STATE,
                    a.EQUIPMENT_CLASS,
                    a.EQUIPMENT_TYPE,
                    a.INSTRUMENT_ID,
                    a.VEST_PIPELINE,
                    a.DEPT_SECTION_NO,
                    a.UNIT_ID,
                    a.IS_HIDE,
                    a.SMBL_FLAG,
                    a.EQUIPMENT_UCODE,
                }));
            }
            
            foreach (var tree in trees)
            {
                foreach (var secondStageTree in tree.SecondStageTree)
                {
                    foreach (var thirdStageTree in secondStageTree.ThirdStageTree)
                    {
                        thirdStageTree.FourthStageTree = new List<NewEquipmentTreeDto>();

                        var equipmentsAear = equipments
                            .OrderBy(x => x.EQUIPMENT_CLASS)
                            .ThenBy(x => x.EQUIPMENT_CODE)
                            .Where(x => x.UNIT_ID == thirdStageTree.PGROUP_ID)
                            .Where(x => x.VEST_PIPELINE is null)
                            .WhereIF(isHide == "0", x => x.IS_HIDE == "0" || x.IS_HIDE == null)
                            .WhereIF(isHide == "1", x => x.IS_HIDE == "1");
                        foreach (var equipment in equipmentsAear)
                        {
                            var fourTree = new NewEquipmentTreeDto()
                            {
                                EQUIPMENT_ID = equipment.EQUIPMENT_ID,
                                EQUIPMENT_CODE = equipment.EQUIPMENT_UCODE,
                                EQUIPMENT_STATE = equipment.EQUIPMENT_STATE,
                                IS_HIDE = equipment.IS_HIDE,
                                SMBL_FLAG = equipment.SMBL_FLAG == "1" ? "1":"0",
                                EQUIPMENT_CLASS = equipmentContext.ExchangeEquipmentClass(equipment.EQUIPMENT_CLASS, equipment.EQUIPMENT_TYPE),
                                IsInstrument = equipment.INSTRUMENT_ID != null,
                                FifthStageTree = new List<NewEquipmentTreeDto>()
                            };

                            if (equipments.Count(x => x.VEST_PIPELINE == equipment.EQUIPMENT_ID) > 0)
                            {
                                var specialEquipments = equipments.Where(x => x.VEST_PIPELINE == equipment.EQUIPMENT_ID);
                                foreach (var specialEquipment in specialEquipments)
                                {
                                    fourTree.FifthStageTree.Add(new NewEquipmentTreeDto()
                                    {
                                        EQUIPMENT_ID = specialEquipment.EQUIPMENT_ID,
                                        EQUIPMENT_CODE = specialEquipment.EQUIPMENT_UCODE,
                                        EQUIPMENT_STATE = specialEquipment.EQUIPMENT_STATE,
                                        IS_HIDE = specialEquipment.IS_HIDE,
                                        SMBL_FLAG = specialEquipment.SMBL_FLAG == "1" ? "1":"0",
                                        IsInstrument = equipment.INSTRUMENT_ID != null,
                                        EQUIPMENT_CLASS = equipmentContext.ExchangeEquipmentClass(specialEquipment.EQUIPMENT_CLASS, equipment.EQUIPMENT_TYPE),
                                    });
                                }
                            }
                            thirdStageTree.FourthStageTree.Add(fourTree);
                        }
                        thirdStageTree.PGROUP_EQUIPMENT_AMOUNT = equipments
                            .WhereIF(isHide == "0", x => x.IS_HIDE == "0" || x.IS_HIDE == null)
                            .WhereIF(isHide == "1", x => x.IS_HIDE == "1")
                            .Count(x => x.UNIT_ID == thirdStageTree.PGROUP_ID);
                        thirdStageTree.PGROUP_INSTRUMENT_AMOUNT = equipments
                            .WhereIF(isHide == "0", x => x.IS_HIDE == "0" || x.IS_HIDE == null)
                            .WhereIF(isHide == "1", x => x.IS_HIDE == "1")
                            .Where(x => x.UNIT_ID == thirdStageTree.PGROUP_ID)
                            .Count(x => x.EQUIPMENT_CLASS == "1");

                        secondStageTree.MGROUP_EQUIPMENT_AMOUNT += thirdStageTree.PGROUP_EQUIPMENT_AMOUNT;
                        secondStageTree.MGROUP_INSTRUMENT_AMOUNT += thirdStageTree.PGROUP_INSTRUMENT_AMOUNT;
                        secondStageTree.MGROUP_SUBSCRIBE_AMOUNT += thirdStageTree.PGROUP_SUBSCRIBE_AMOUNT;
                        secondStageTree.MGROUP_SCRAP_AMOUNT += thirdStageTree.PGROUP_SCRAP_AMOUNT;
                    }
                    tree.TOTAL_EQUIPMENT_AMOUNT += secondStageTree.MGROUP_EQUIPMENT_AMOUNT;
                    tree.TOTAL_INSTRUMENT_AMOUNT += secondStageTree.MGROUP_INSTRUMENT_AMOUNT;
                    tree.TOTAL_SUBSCRIBE_AMOUNT += secondStageTree.MGROUP_SUBSCRIBE_AMOUNT;
                    tree.TOTAL_SCRAP_AMOUNT += secondStageTree.MGROUP_SCRAP_AMOUNT;
                }
            }
            return trees;
        }



        public NewOATreeDto GetEquipmentListByMgroup(string userNo, string hospitalId, string equipmentNo, string labId, string areaId, string? ifHide)
        {
            var authContext = new AuthorityContext(_sqlSugarUow, _authorityService);
            authContext.SetUser(_httpContext.HttpContext.User.ToClaimsDto(),labId,areaId);
            var groupList = authContext.GetAccessibleProfessionalGroups(labId, areaId);
            //专业组列表
            List<string> arrPgroupList = new List<string>();
            groupList.ForEach(p =>
            {
                if (p.MGROUP_ID.IsNotNullOrEmpty())
                {
                    arrPgroupList.Add(p.MGROUP_ID);
                }
            });
            //检测仪器同步
            var equipment_insert = new List<EMS_EQUIPMENT_INFO>();
            var equipment_update = new List<EMS_EQUIPMENT_INFO>();
            var workPlan_insert = new List<EMS_WORK_PLAN>();
            //检测仪器列表
            var instrumentList = _sqlSugarUow.Db.Queryable<LIS6_INSTRUMENT_INFO>()
                .LeftJoin<LIS6_SINSTRUMENT_INFO>((a, b) => a.SINSTRUMENT_ID == b.SINSTRUMENT_ID)
                .Where((a, b) =>  a.LAB_ID == labId)
                .Select((a, b) => new { a, b })
                .ToList();
            var oldEquipmentList = _sqlSugarUow.Db.Queryable<EMS_EQUIPMENT_INFO>()
                .Where(p => p.INSTRUMENT_ID != null)
                .ToList();
            var pipeliningList = _sqlSugarUow.Db.Queryable<LIS6_PIPELINING_INSTRUMENT>().ToList();
            //检验单元
            var groupInfo = _sqlSugarUow.Db.Queryable<LIS6_INSPECTION_GROUP>().ToList();
            //基础数据
            var baseData = _sqlSugarUow.Db.Queryable<SYS6_BASE_DATA>()
                .Where(p => p.DATA_STATE == "1" && (p.CLASS_ID == "设备分类" || p.CLASS_ID == "设备类型"))
                .ToList();
            var needSynchronizationUnitId = NeedSynchronizationUnitId(hospitalId);
            //var needSynchronizationUnitId = false;

            instrumentList.ForEach(item =>
            {
                var id  = IDGenHelper.CreateGuid().Substring(0,18);
                var pgroupId = groupInfo.Where(p => p.GROUP_ID == item.a.GROUP_ID).FirstOrDefault()?.PGROUP_ID;
                var equipemntClass = baseData.Where(p => p.DATA_CNAME == "常规检测设备" && p.CLASS_ID == "设备分类").FirstOrDefault()?.DATA_ID;
                if (item.a.EQUIPMENT_ID.IsNullOrEmpty() || item.a.EQUIPMENT_ID == "0")
                {
                    if (equipemntClass != null)
                    {
                        //若系统管理配置了设备型号的自定义型号则取自定义型号
                        string model = item.a.SINSTRUMENT_ID;
                        if (item.b.SINSTRUMENT_MODE.IsNotNullOrEmpty())
                            model = item.b.SINSTRUMENT_MODE;
                        equipment_insert.Add(new EMS_EQUIPMENT_INFO
                        {
                            EQUIPMENT_ID = id,
                            HOSPITAL_ID = item.a.HOSPITAL_ID,
                            UNIT_ID = pgroupId,
                            LAB_ID = item.a.LAB_ID,
                            EQUIPMENT_CLASS = equipemntClass,
                            INSTRUMENT_ID = item.a.INSTRUMENT_ID,
                            EQUIPMENT_NAME = item.a.INSTRUMENT_NAME,
                            EQUIPMENT_MODEL = model,
                            EQUIPMENT_NUM = item.a.INSTRUMENT_SNUM,
                            EQUIPMENT_CODE = item.a.INSTRUMENT_CNAME,
                            EQUIPMENT_TYPE = item.a.INSTRUMENT_TYPE,
                            MANUFACTURER_ID = item.a.INSTRUMENT_FACTORY,
                            EQUIPMENT_STATE = item.a.INSTRUMENT_STATE ?? "1",
                            FIRST_RPERSON = "检测仪器同步数据",
                            FIRST_RTIME = DateTime.Now,
                            LAST_MPERSON = "检测仪器同步数据",
                            LAST_MTIME = DateTime.Now,
                            EQUIPMENT_UCODE = $"{item.a.INSTRUMENT_SNUM??""}_{item.a.INSTRUMENT_NAME??""}_{item.a.INSTRUMENT_CNAME??""}",
                        });
                        workPlan_insert.Add(new EMS_WORK_PLAN
                        {
                            WORK_PLAN_ID = IDGenHelper.CreateGuid().ToString(),
                            WORK_PLAN_STATE = "1",
                            HOSPITAL_ID = item.a.HOSPITAL_ID,
                            EQUIPMENT_ID = id,
                            FIRST_RPERSON = "检测仪器同步数据",
                            FIRST_RTIME = DateTime.Now,
                            LAST_MPERSON = "检测仪器同步数据",
                            LAST_MTIME = DateTime.Now

                        });
                        _sqlSugarUow.Db.Updateable<LIS6_INSTRUMENT_INFO>().SetColumns(p => new LIS6_INSTRUMENT_INFO
                        {
                            EQUIPMENT_ID = id
                        }).Where(p => p.INSTRUMENT_ID == item.a.INSTRUMENT_ID).ExecuteCommand();
                    }
                    else
                    {
                        _logger.LogError($"同步仪器失败:设备分类基础数据缺少常规检测设备");
                    }
                }
                else
                {
                    var equipmentUpdate = oldEquipmentList.Where(p => p.EQUIPMENT_ID == item.a.EQUIPMENT_ID).FirstOrDefault();
                    if (equipmentUpdate != null)
                    {
                        var pipeId = pipeliningList.Where(p => p.INSTRUMENT_ID == equipmentUpdate.INSTRUMENT_ID).FirstOrDefault()?.PIPELINING_ID;
                        if (pipeId.IsNullOrEmpty())
                        {
                            equipmentUpdate.VEST_PIPELINE = null;
                        }
                        else
                        {
                            var vestPipeId = oldEquipmentList.Where(p => p.INSTRUMENT_ID == pipeId).FirstOrDefault()?.EQUIPMENT_ID;
                            if (vestPipeId.IsNotNullOrEmpty())
                            {
                                equipmentUpdate.VEST_PIPELINE = vestPipeId;
                            }
                        }
                        //若系统管理配置了设备型号的自定义型号则取自定义型号
                        string model = item.a.SINSTRUMENT_ID;
                        if (item.b.SINSTRUMENT_MODE.IsNotNullOrEmpty())
                            model = item.b.SINSTRUMENT_MODE;
                        equipmentUpdate.UNIT_ID = needSynchronizationUnitId ? equipmentUpdate.UNIT_ID : pgroupId;
                        equipmentUpdate.LAB_ID = item.a.LAB_ID;
                        equipmentUpdate.EQUIPMENT_MODEL = model;
                        equipmentUpdate.EQUIPMENT_NUM = item.a.INSTRUMENT_SNUM;
                        equipmentUpdate.EQUIPMENT_CODE = item.a.INSTRUMENT_CNAME;
                        equipmentUpdate.EQUIPMENT_TYPE = item.a.INSTRUMENT_TYPE;
                        equipmentUpdate.EQUIPMENT_CLASS = equipemntClass;
                        equipmentUpdate.LAST_MTIME = DateTime.Now;
                        equipmentUpdate.EQUIPMENT_STATE = item.a.INSTRUMENT_STATE ?? equipmentUpdate.EQUIPMENT_STATE;
                        equipmentUpdate.EQUIPMENT_UCODE = equipmentUpdate.EQUIPMENT_UCODE.IsNullOrEmpty()
                            ? $"{item.a.INSTRUMENT_SNUM}_{item.a.INSTRUMENT_NAME}_{item.a.INSTRUMENT_CNAME}"
                            : equipmentUpdate.EQUIPMENT_UCODE;
                        if (pgroupId is not null)
                        {
                            equipment_update.Add(equipmentUpdate);
                        }

                    }
                }
            });
            _sqlSugarUow.Db.Insertable(equipment_insert).ExecuteCommand();
            _sqlSugarUow.Db.Insertable(workPlan_insert).ExecuteCommand();
            _sqlSugarUow.Db.Updateable(equipment_update).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommand();


            var allAearEquiments = GetEquipmentListByInspection(userNo, equipmentNo, labId, areaId, null, null, ifHide ,null);

            var oaTree = allAearEquiments.WhereIF(areaId.IsNotNullOrEmpty(), x => x.AREA_ID == areaId).FirstOrDefault();
            
            return oaTree;
        }


        private bool NeedSynchronizationUnitId(string id)
        {
            const string KEY = "H0701003";
            var result = false;

            var SetUpValue1 = _sqlSugarUow.Db.Queryable<SYS6_SETUP>().Where(x => x.UNIT_ID == id && x.SETUP_NO == KEY).First();
            var SetUpValue2 = _sqlSugarUow.Db.Queryable<SYS6_SETUP_DICT>().Where(x => x.SETUP_NO == KEY && x.SETUP_STATE == "1").First();
            if (SetUpValue1 is not null)
            {
                result = SetUpValue1.SETUP_VALUE == "1";
            }
            if (SetUpValue2 is not null)
            {
                result = SetUpValue2.DEFAULT_VALUE == "1";
            }
            return result;
        }

        public NewEquipClassTreeDto GetEquipmentClassList(string userNo, string hospitalId, string equipmentNo, string labId, string areaId, string ifHide)
        {
            var authorityContext = new AuthorityContext(_sqlSugarUow, _authorityService);
            authorityContext.SetUser(_httpContext.HttpContext.User.ToClaimsDto(),labId,areaId);
            var groupList = authorityContext.GetAccessibleProfessionalGroups(labId, areaId)
                .Where(x => x.MGROUP_ID != null).ToList();
            int number = 0;
            var MGroupIds = groupList.Select(x => x.MGROUP_ID).Distinct().ToList();
            //设备类型列表
            var classList = _sqlSugarUow.Db.Queryable<SYS6_BASE_DATA>()
                .Where(p => p.CLASS_ID == "设备分类" && p.DATA_STATE == "1")
                .ToList();
            //管理专业组列表
            var mgroupList = _sqlSugarUow.Db.Queryable<SYS6_INSPECTION_MGROUP>()
                .Where(p => MGroupIds.Contains(p.MGROUP_ID) && p.MGROUP_STATE == "1")
                .ToList();
            groupList.RemoveAll(x => mgroupList.All(p => p.MGROUP_ID != x.MGROUP_ID));
            
            var pipeliningList = _sqlSugarUow.Db.Queryable<LIS6_PIPELINING_INSTRUMENT>().ToList();
            //基础数据
            var baseData = _sqlSugarUow.Db.Queryable<SYS6_BASE_DATA>().Where(p => p.DATA_STATE == "1" && (p.CLASS_ID == "设备分类" || p.CLASS_ID == "设备类型")).ToList();
            //一级树
            var ClassTree = new NewEquipClassTreeDto();

            //当前院区下所有报废停用信息
            var scrapStopList = _sqlSugarUow.Db.Queryable<EMS_SCRAP_INFO>()
                .InnerJoin<EMS_EQUIPMENT_INFO>((a, b) => a.EQUIPMENT_ID == b.EQUIPMENT_ID)
                .InnerJoin<SYS6_INSPECTION_PGROUP>((a, b, c) => b.UNIT_ID == c.PGROUP_ID && a.SCRAP_STATE == "1")
                .Where((a, b, c) => groupList.Select(i => i.PGROUP_ID).Contains(c.PGROUP_ID))
                .Select((a, b, c) => new
                {
                    b.EQUIPMENT_CLASS,
                    a.APPLY_STATE,
                    a.OPER_PERSON_ID,
                    a.EXAMINE_PERSON_ID,
                    a.APPROVE_PERSON_ID,
                    b.UNIT_ID,
                    c.MGROUP_ID,
                }).ToList();
            ClassTree.TOTAL_SCRAP_AMOUNT = scrapStopList.Count;
            //本人待处理
            var q = new List<ScrapStopListDto>();
            scrapStopList.ForEach(item =>
            {
                var dealPersonId = "";
                if (item.APPLY_STATE == "0" || item.APPLY_STATE == "4" || item.APPLY_STATE == "5")
                {
                    dealPersonId = item.OPER_PERSON_ID;
                }
                if (item.APPLY_STATE == "1")
                {
                    dealPersonId = item.EXAMINE_PERSON_ID;
                }
                if (item.APPLY_STATE == "2")
                {
                    dealPersonId = item.APPROVE_PERSON_ID;
                }
                q.Add(new ScrapStopListDto
                {
                    MGROUP_ID = item.UNIT_ID,
                    DEAL_PERSON_ID = dealPersonId,
                    STATE = item.APPLY_STATE
                });
            });
            var userPgroup = _sqlSugarUow.Db.Queryable<SYS6_USER>().Where(p => p.USER_NO == userNo).First().DEPT_CODE;
            var res = q.ToList();
            res.ForEach(item =>
            {
                if (item.DEAL_PERSON_ID == userNo)
                {
                    ClassTree.SELF_PEND += 1;
                }
            });
            ClassTree.SELF_PGROUP_PEND = res.Where(p => (p.STATE == "0" || p.STATE == "1" || p.STATE == "2") && p.MGROUP_ID == userPgroup).Count();

            var areaInfo = _sqlSugarUow.Db.Queryable<SYS6_INSPECTION_AREA>()
                .OrderBy(p=>p.AREA_ID)
                .WhereIF(areaId.IsNotNullOrEmpty(), p => p.AREA_ID == areaId)
                .First();
            if (areaInfo == null)
            {
                throw new BizException("找不到该院区信息");
            }
            ClassTree.AREA_ID = areaInfo.AREA_ID;
            ClassTree.AREA_NAME = areaInfo.AREA_NAME;
            //当前院区下所有设备
            var equipments = _equipmentDocService.GetEquipmentsBycondition(false,labId,areaId,null,null,null,null,equipmentNo,null)
                .WhereIF(ifHide == "1", (a, b) => a.IS_HIDE == "1")
                .WhereIF(ifHide == "0", (a, b) => a.IS_HIDE == null || a.IS_HIDE != "1")
                .Select((a, b) => new
                {
                    a.EQUIPMENT_ID,
                    a.EQUIPMENT_CODE,
                    a.EQUIPMENT_UCODE,
                    a.EQUIPMENT_STATE,
                    a.EQUIPMENT_CLASS,
                    a.EQUIPMENT_TYPE,
                    a.INSTRUMENT_ID,
                    a.VEST_PIPELINE,
                    a.DEPT_SECTION_NO,
                    a.IS_HIDE,
                    MGROUP_ID = groupList.Where(p => p.PGROUP_ID == a.UNIT_ID).Select(i => i.MGROUP_ID).First(),
                    PGROUP_ID = a.UNIT_ID,
                    a.SMBL_FLAG
                })
                .ToList();
            
            
            if (equipments.Any(x => x.VEST_PIPELINE != null))
            {
                var masterEquipments = new List<EMS_EQUIPMENT_INFO>();
                foreach (var pipEquipment in equipments.Where(x=>x.VEST_PIPELINE!=null))
                {
                    var  masterEquipment = _sqlSugarUow.Db.Queryable<EMS_EQUIPMENT_INFO>()
                        .First(x => x.EQUIPMENT_ID == pipEquipment.VEST_PIPELINE);
                    if (masterEquipment is null)
                    {
                        continue;
                    }
                    if (equipments.All(x => x.EQUIPMENT_ID != masterEquipment.EQUIPMENT_ID))
                    {
                        var count = equipments.Count(x => x.EQUIPMENT_ID == masterEquipment.EQUIPMENT_ID);
                        masterEquipments.Add(masterEquipment);
                    }
                    
                }
                equipments.AddRange(masterEquipments.DistinctBy(x => x.EQUIPMENT_ID).Select(a =>new
                {
                    a.EQUIPMENT_ID,
                    a.EQUIPMENT_CODE,
                    a.EQUIPMENT_UCODE,
                    a.EQUIPMENT_STATE,
                    a.EQUIPMENT_CLASS,
                    a.EQUIPMENT_TYPE,
                    a.INSTRUMENT_ID,
                    a.VEST_PIPELINE,
                    a.DEPT_SECTION_NO,
                    a.IS_HIDE,
                    MGROUP_ID = groupList.FirstOrDefault(p => p.PGROUP_ID == a.UNIT_ID) is null ? null : groupList.First(p => p.PGROUP_ID == a.UNIT_ID).MGROUP_ID ,
                    PGROUP_ID = a.UNIT_ID,
                    a.SMBL_FLAG
                }));
            }
            ClassTree.TOTAL_EQUIPMENT_AMOUNT = equipments.Count;
            ClassTree.TOTAL_INSTRUMENT_AMOUNT = equipments.Where(p => p.EQUIPMENT_CLASS == "1").Count();
            
            //二级树
            var secondStage = new List<NewEquipClassTree>();
            classList.ForEach(item =>
            {
                //三级树
                var thirdStage = new List<NewClassMgroupTree>();
                mgroupList.ForEach(i =>
                {
                    //四级树
                    var fourthStage = new List<NewClassPgroupTree>();
                    var pgroupList = groupList.Where(p => p.MGROUP_ID == i.MGROUP_ID).ToList();
                    pgroupList.ForEach(m =>
                    {
                        //五级树
                        var fifthStage = new List<NewClassEquipmentTree>();
                        var eList = equipments.Where(p => p.PGROUP_ID == m.PGROUP_ID && p.EQUIPMENT_CLASS == item.DATA_ID && p.VEST_PIPELINE == null).OrderBy(i => i.EQUIPMENT_CLASS).ThenBy(i => i.EQUIPMENT_CODE).ToList();
                        eList.ForEach(n =>
                        {
                            //六级树
                            var sixthStage = new List<NewClassEquipmentTree>();
                            var pipeliningInfo = equipments.Where(p => p.VEST_PIPELINE == n.EQUIPMENT_ID).ToList();
                            pipeliningInfo.ForEach(o =>
                            {
                                var equipmentClass = baseData.Where(p => p.DATA_ID == o.EQUIPMENT_CLASS && p.CLASS_ID == "设备分类").FirstOrDefault()?.DATA_CNAME;
                                if (equipmentClass == "常规检测设备")
                                {
                                    if (o.EQUIPMENT_TYPE.IsNotNullOrEmpty())
                                    {
                                        equipmentClass = baseData.Where(p => p.DATA_ID == o.EQUIPMENT_TYPE && p.CLASS_ID == "设备类型").FirstOrDefault()?.DATA_CNAME;
                                    }
                                    else
                                    {
                                        equipmentClass = "检测仪器";
                                    }
                                }
                                sixthStage.Add(new NewClassEquipmentTree
                                {
                                    Num = ++number,
                                    EQUIPMENT_ID = o.EQUIPMENT_ID,
                                    EQUIPMENT_CODE = o.EQUIPMENT_UCODE,
                                    EQUIPMENT_STATE = o.EQUIPMENT_STATE,
                                    IS_HIDE = o.IS_HIDE,
                                    SMBL_FLAG = o.SMBL_FLAG == "1" ? "1" : "0",
                                    EQUIPMENT_CLASS = equipmentClass
                                });
                            });
                            var equipmentClass = baseData.Where(p => p.DATA_ID == n.EQUIPMENT_CLASS && p.CLASS_ID == "设备分类").FirstOrDefault()?.DATA_CNAME;
                            if (equipmentClass == "常规检测设备")
                            {
                                if (n.EQUIPMENT_TYPE.IsNotNullOrEmpty())
                                {
                                    equipmentClass = baseData.Where(p => p.DATA_ID == n.EQUIPMENT_TYPE && p.CLASS_ID == "设备类型").FirstOrDefault()?.DATA_CNAME;
                                }
                                else
                                {
                                    equipmentClass = "检测仪器";
                                }
                            }
                            if (pipeliningList.Select(i => i.INSTRUMENT_ID).Contains(n.INSTRUMENT_ID) == false)
                            {
                                fifthStage.Add(new NewClassEquipmentTree
                                {
                                    Num = ++number,
                                    EQUIPMENT_ID = n.EQUIPMENT_ID,
                                    EQUIPMENT_CODE = n.EQUIPMENT_UCODE,
                                    EQUIPMENT_CLASS = equipmentClass,
                                    IS_HIDE = n.IS_HIDE,
                                    SMBL_FLAG = n.SMBL_FLAG == "1" ? "1" : "0",
                                    SixthStageTree = sixthStage.OrderBy(p => p.EQUIPMENT_CODE).ToList()
                                });
                            }
                        });
                        fourthStage.Add(new NewClassPgroupTree
                        {
                            Num = ++number,
                            PGROUP_ID = m.PGROUP_ID,
                            PGROUP_NAME = m.PGROUP_NAME,
                            PGROUP_EQUIPMENT_AMOUNT = equipments.Where(p => p.PGROUP_ID == m.PGROUP_ID && p.EQUIPMENT_CLASS == item.DATA_ID).Count(),
                            PGROUP_SCRAP_AMOUNT = scrapStopList.Where(p => p.EQUIPMENT_CLASS == item.DATA_ID && p.MGROUP_ID == i.MGROUP_ID && p.UNIT_ID == m.PGROUP_ID).Count(),
                            PGROUP_INSTRUMENT_AMOUNT = equipments.Where(p => p.PGROUP_ID == m.PGROUP_ID && p.EQUIPMENT_CLASS == item.DATA_ID && p.EQUIPMENT_CLASS == "1").Count(),
                            FifthStageTree = fifthStage.OrderBy(p => p.EQUIPMENT_CLASS == "流水线").ToList()
                        }); ;
                    });
                    thirdStage.Add(new NewClassMgroupTree
                    {
                        Num = ++number,
                        MGROUP_ID = i.MGROUP_ID,
                        MGROUP_NAME = i.MGROUP_NAME,
                        MGROUP_EQUIPMENT_AMOUNT = equipments.Where(p => p.MGROUP_ID == i.MGROUP_ID && p.EQUIPMENT_CLASS == item.DATA_ID).Count(),
                        MGROUP_SCRAP_AMOUNT = scrapStopList.Where(p => p.EQUIPMENT_CLASS == item.DATA_ID && p.MGROUP_ID == i.MGROUP_ID).Count(),
                        MGROUP_INSTRUMENT_AMOUNT = equipments.Where(p => p.MGROUP_ID == i.MGROUP_ID && p.EQUIPMENT_CLASS == item.DATA_ID && p.EQUIPMENT_CLASS == "1").Count(),
                        FourthStageTree = fourthStage
                    });
                });
                secondStage.Add(new NewEquipClassTree
                {
                    Num = ++number,
                    EQUIPMENT_CLASS = item.DATA_ID,
                    EQUIPMENT_CLASS_NAME = item.DATA_CNAME == "常规检测设备" ? "检测设备/流水线" : item.DATA_CNAME,
                    EQUIPCLASS_EQUIPMENT_AMOUNT = equipments.Where(p => p.EQUIPMENT_CLASS == item.DATA_ID).Count(),
                    EQUIPCLASS_SCRAP_AMOUNT = scrapStopList.Where(p => p.EQUIPMENT_CLASS == item.DATA_ID).Count(),
                    EQUIPCLASS_INSTRUMENT_AMOUNT = equipments.Where(p => p.EQUIPMENT_CLASS == "1").Count(),
                    ThirdStageTree = thirdStage
                });
            });
            ClassTree.SecondStageTree = secondStage;
            return ClassTree;
        }

        /// <summary>
        /// 生成设备预览信息
        /// </summary>
        /// <param name="equipmentId">设备id</param>
        /// <param name="userName">操作人</param>
        /// <param name="hospitalId">设备id</param>
        /// <returns></returns>

        public ResultDto GenerateEquipmentInfo(string equipmentId, string userName, string hospitalId)
        {

            EMS_EQUIPMENT_INFO equipmentInfo = GetEquipmentInfo(equipmentId);
            OfficeHelper oh = new OfficeHelper();
            string contentRootPath = _hostingEnvironment.ContentRootPath;
            string path = Path.Combine(contentRootPath, "ExampleFile", "DeviceInfo.docx");
            string imgPath = string.Empty;
            EquipmentInfoDto equipmentDto = _mapper.Map<EquipmentInfoDto>(equipmentInfo);
            equipmentDto.INSTALL_DATE = equipmentDto.INSTALL_DATE != null ? Convert.ToDateTime(equipmentDto.INSTALL_DATE).ToString("yyyy/MM/dd") : null;
            equipmentDto.EQ_OUT_TIME = equipmentDto.EQ_OUT_TIME != null ? Convert.ToDateTime(equipmentDto.EQ_OUT_TIME).ToString("yyyy/MM/dd") : null;
            equipmentDto.ENABLE_TIME = equipmentDto.ENABLE_TIME != null ? Convert.ToDateTime(equipmentDto.ENABLE_TIME).ToString("yyyy/MM/dd") : null;
            if (equipmentInfo.DEALER_ID.IsNotNullOrEmpty())
            {
                List<SYS6_COMPANY_CONTACT> listContact = GetCompanyContactList(equipmentInfo.DEALER_ID, equipmentId, "经销商", "", "");
                if (listContact != null && listContact.Count > 0)
                {
                    equipmentDto.CONTACT_NAME = listContact[0].CONTACT_NAME;
                    equipmentDto.PHONE_NO = listContact[0].PHONE_NO;
                }
            }
            var res = _sqlSugarUow.Db.Queryable<EMS_DOC_INFO>()
            .Where(p => p.DOC_CLASS == "外观信息" && p.DOC_INFO_ID == equipmentId && p.DOC_STATE == "1")
            .ToList();
            equipmentDto.ListPath = new List<string>();
            foreach (var item in res)
            {
                equipmentDto.ListPath.Add(item.PDF_PREVIEW_PATH);
            }
            var baseData = _sqlSugarUow.Db.Queryable<SYS6_BASE_DATA>()
            .Where(p => p.CLASS_ID == "设备分类" && p.DATA_STATE == "1")
            .ToList();
            foreach (var item in baseData)
            {
                if (item.DATA_CNAME == "常规检测设备")
                {
                    item.DATA_CNAME = "检测设备/流水线";
                }
            }
            equipmentDto.EQUIPMENT_CLASS = baseData.Where(p => p.DATA_ID == equipmentDto.EQUIPMENT_CLASS).FirstOrDefault()?.DATA_CNAME;
            using (Document document = new Document())
            {
                document.LoadFromFile(path);
                Section section = null;
                string guid = Guid.NewGuid().ToString("N");
                string newFilePath = guid + ".docx";
                string newPdfFilePath = guid + ".pdf";//保存路径
                if (equipmentDto != null)
                {
                    var data = equipmentDto;
                    foreach (var proName in equipmentDto.GetType().GetProperties())
                    {
                        string value = data.GetType().GetProperty(proName.Name).GetValue(data, null) != null ? data.GetType().GetProperty(proName.Name).GetValue(data, null).ToString() : "";
                        document.Replace("{" + proName.Name + "}", value, false, true);
                    }
                    BookmarksNavigator bookmarksNavigator = new BookmarksNavigator(document);
                    if (document.Bookmarks.FindByName("Image") != null)
                    {
                        bookmarksNavigator.MoveToBookmark("Image");
                        TextBodyPart part = bookmarksNavigator.GetBookmarkContent();
                        if (equipmentDto.ListPath != null)
                        {
                            foreach (var item in equipmentDto.ListPath)
                            {
                                string path1 = file_preview_address + item;
                                string file_path1 = path1.Replace(@"\", "/");

                                string aFirstName = item.Substring(item.LastIndexOf("\\") + 1, (item.LastIndexOf(".") - item.LastIndexOf("\\") - 1));
                                string aLastName = item.Substring(imgPath.LastIndexOf(".") + 1, (item.Length - item.LastIndexOf(".") - 1));
                                byte[] img = GetConvertType(file_path1);
                                for (int i = 0; i < part.BodyItems.Count; i++)
                                {
                                    if (part.BodyItems[i].ToString() == "Spire.Doc.Documents.Paragraph")
                                    {
                                        Paragraph paragraph = (Paragraph)part.BodyItems[i];
                                        DocPicture picture = paragraph.AppendPicture(img);
                                        picture.Width = 250f;
                                        picture.Height = 350f;
                                        picture.TextWrappingStyle = TextWrappingStyle.Inline;
                                        picture.HorizontalAlignment = ShapeHorizontalAlignment.Center;
                                    }
                                }
                            }
                            bookmarksNavigator.ReplaceBookmarkContent(part);
                        }

                    }
                }
                document.SaveToFile(contentRootPath + "ExampleFile/" + newFilePath, FileFormat.Docx);
                string filePath = contentRootPath + "ExampleFile/" + newFilePath;
                using var stream = new MemoryStream(File.ReadAllBytes(filePath).ToArray());
                var formFile = new FormFile(stream, 0, stream.Length, "streamFile", filePath.Split(@"\").Last());

                UploadFileDto fileDto = new UploadFileDto();
                fileDto.FILE = formFile;
                fileDto.FILE_NAME = "DeviceInfo.docx";
                fileDto.FILE_SUFFIX = ".docx";
                fileDto.FILE_NAME = newFilePath;
                fileDto.UPLOAD_FOLDER_NAME = OfficeHelper.PathCombine("PMS", equipmentId);
                fileDto.UPLOAD_FILE_NAME = newFilePath;
                fileDto.SAVE_TO_S28 = true;
                fileDto.IFCOVER = true;
                ResultDto result = _IUploadFileService.UploadFileOperate(fileDto);

                string file_path = string.Empty;
                string msg = "生成失败！";
                if (result.success == true)
                {
                    // File.Delete(curPatn);
                    file_path = FileHttpUrl + result.data.ToString();
                    msg = "生成成功！";
                }
                var strResult = new { FILE_PATH = file_path, MSG = msg };
                return strResult.ToResultDto();
            }
        }

        /// <summary>
        /// 下载文件到本地
        /// </summary>
        /// <param name="serviceUrl"></param>
        /// <param name="localUrl"></param>
        private byte[] GetConvertType(string serviceUrl)
        {
            byte[] buffer = null;
            if (FileExists(serviceUrl))
            {
                ServicePointManager.ServerCertificateValidationCallback = delegate { return true; };
                WebClient client = new WebClient();
                ServicePointManager.SecurityProtocol = (SecurityProtocolType)192 | (SecurityProtocolType)768 | (SecurityProtocolType)3072;
                buffer = client.DownloadData(serviceUrl);
            }
            else
            {
                string contentRootPath = _hostingEnvironment.ContentRootPath;
                string path = Path.Combine(contentRootPath + "ExampleFile/default.jpg");
                FileStream fs = new FileStream(path, FileMode.Open);
                buffer = new byte[fs.Length];
                fs.Read(buffer, 0, buffer.Length);
                fs.Close();
            }

            return buffer;
        }

        /// <summary>
        /// 判断远程文件是否存在
        /// </summary>
        /// <param name="url">url地址</param>
        /// <returns></returns>
        public bool FileExists(string url)
        {
            ServicePointManager.ServerCertificateValidationCallback = delegate { return true; };
            ServicePointManager.SecurityProtocol = (SecurityProtocolType)192 | (SecurityProtocolType)768 | (SecurityProtocolType)3072;
            //ture为存在，false为不存在
            bool result = false;
            WebResponse response = null;
            try
            {
                WebRequest req = WebRequest.Create(url);
                response = req.GetResponse();
                result = response == null ? false : true;
            }
            catch (Exception ex)
            {
                result = false;
            }
            finally
            {
                if (response != null)
                {
                    response.Close();
                }
            }
            return result;
        }
        /// <summary>
        /// 获取设备信息
        /// </summary>
        /// <param name="equipmentId"></param>
        /// <returns></returns>
        /// <exception cref="KeyNotFoundException"></exception>
        public EMS_EQUIPMENT_INFO GetEquipmentInfo(string equipmentId)
        {
            var res = _sqlSugarUow.Db.Queryable<EMS_EQUIPMENT_INFO>()
                .Where(p => p.EQUIPMENT_ID == equipmentId)
                .Includes(i => i.WarnRecords)
                .Includes(i => i.eMS_WORK_PLAN)
                .Includes(i => i.eMS_MAINTAIN_INFO.Where(p => p.MAINTAIN_STATE == "1" && p.MAINTAIN_DATE != null).OrderByDescending(m => m.MAINTAIN_DATE).ToList())
                .Includes(i => i.eMS_CORRECT_INFO.Where(p => p.CORRECT_STATE == "1" && p.CORRECT_DATE != null && p.STATE == "已执行").OrderByDescending(m => m.CORRECT_DATE).ToList())
                .Includes(i => i.eMS_COMPARISON_INFO.Where(p => p.COMPARISON_STATE == "1" && p.STATE == "已执行" && p.COMPARISON_DATE != null).OrderByDescending(m => m.COMPARISON_DATE).ToList())
                .Includes(i => i.eMS_VERIFICATION_INFO.Where(p => p.VERIFICATION_STATE == "1" && p.STATE == "已执行" && p.VERIFICATION_DATE != null).OrderByDescending(m => m.VERIFICATION_DATE).ToList())
                .First();
            if (res == null)
            {
                throw new KeyNotFoundException($"设备信息不存在");
            }
            if (res.eMS_WORK_PLAN != null)
            {
                res.MAINTAIN_INTERVALS = res.eMS_WORK_PLAN.MAINTAIN_INTERVALS;
                res.MAINTAIN_WARN_INTERVALS = res.eMS_WORK_PLAN.MAINTAIN_WARN_INTERVALS;
                res.CORRECT_INTERVALS = res.eMS_WORK_PLAN.CORRECT_INTERVALS;
                res.CORRECT_WARN_INTERVALS = res.eMS_WORK_PLAN.CORRECT_WARN_INTERVALS;
                res.COMPARISON_INTERVALS = res.eMS_WORK_PLAN.COMPARISON_INTERVALS;
                res.COMPARISON_WARN_INTERVALS = res.eMS_WORK_PLAN.COMPARISON_WARN_INTERVALS;
                res.VERIFICATION_INTERVALS = res.eMS_WORK_PLAN.VERIFICATION_INTERVALS;
                res.VERIFICATION_WARN_INTERVALS = res.eMS_WORK_PLAN.VERIFICATION_WARN_INTERVALS;
            }
            if (res.eMS_MAINTAIN_INFO.Count > 0)
            {
                res.LAST_MAINTAIN_DATE = res.eMS_MAINTAIN_INFO[0].MAINTAIN_DATE;
                res.NEXT_MAINTAIN_DATE = res.MAINTAIN_INTERVALS != null ? Convert.ToDateTime(res.LAST_MAINTAIN_DATE)
                            .AddDays(int.Parse(res.MAINTAIN_INTERVALS)) : null;
            }
            if (res.eMS_CORRECT_INFO.Count > 0)
            {
                res.LAST_CORRECT_DATE = res.eMS_CORRECT_INFO[0].CORRECT_DATE;
                res.NEXT_CORRECT_DATE = res.CORRECT_INTERVALS != null ? Convert.ToDateTime(res.LAST_CORRECT_DATE)
                            .AddDays(int.Parse(res.CORRECT_INTERVALS)) : null;
            }
            if (res.eMS_COMPARISON_INFO.Count > 0)
            {
                res.LAST_COMPARISON_DATE = res.eMS_COMPARISON_INFO[0].COMPARISON_DATE;
                res.NEXT_COMPARISON_DATE = res.COMPARISON_INTERVALS != null ? Convert.ToDateTime(res.LAST_COMPARISON_DATE)
                            .AddDays(int.Parse(res.COMPARISON_INTERVALS)) : null;
            }
            if (res.eMS_VERIFICATION_INFO.Count > 0)
            {
                res.LAST_VERIFICATION_DATE = res.eMS_VERIFICATION_INFO[0].VERIFICATION_DATE;
                res.NEXT_VERIFICATION_DATE = res.VERIFICATION_INTERVALS != null ? Convert.ToDateTime(res.LAST_VERIFICATION_DATE)
                            .AddDays(int.Parse(res.VERIFICATION_INTERVALS)) : null;
            }
            var hospitalInfo = _sqlSugarUow.Db.Queryable<SYS6_HOSPITAL_INFO>()
                .Where(p => p.HOSPITAL_ID == res.HOSPITAL_ID)
                .First();
            if (hospitalInfo != null)
            {
                res.HOSPITAL_NAME = hospitalInfo.HOSPITAL_CNAME;
            }
            res.EARLIEST_ENABLE_DATE = _sqlSugarUow.Db.Queryable<EMS_START_STOP>()
                .Where(p => p.EQUIPMENT_ID == res.EQUIPMENT_ID && p.START_STOP_STATE != "2" && p.START_CAUSE != "首次启用")
                .OrderBy(i => i.OPER_TIME)
                .Select(m => m.OPER_TIME)
                .First();
            var labInfo = _sqlSugarUow.Db.Queryable<SYS6_INSPECTION_LAB>()
                .Where(p => p.LAB_ID == res.LAB_ID)
                .First();
            res.LAB_NAME = labInfo?.LAB_NAME;
            //res.EQUIPMENT_CLASS = _sqlSugarUow.Db.Queryable<SYS6_BASE_DATA>()
            //    .Where(p => p.CLASS_ID == "设备分类" && p.DATA_STATE == "1" && p.DATA_ID == res.EQUIPMENT_CLASS).First()?.DATA_CNAME;
            //res.PROFESSIONAL_CLASS = _sqlSugarUow.Db.Queryable<SYS6_BASE_DATA>()
            //    .Where(p => p.CLASS_ID == "专业分类" && p.DATA_STATE == "1" && p.DATA_ID == res.PROFESSIONAL_CLASS).First()?.DATA_CNAME;
            res.MAINTAIN_TYPE = "周保养";
            return res;
        }
        /// <summary>
        /// 获取经销商信息
        /// </summary>
        /// <param name="companyId">经销商id</param>
        /// <param name="equipmentId">设备id</param>
        /// <param name="contactType">经销商类型</param>
        /// <param name="contactState">状态</param>
        /// <param name="keyword">关键字</param>
        /// <returns></returns>
        public List<SYS6_COMPANY_CONTACT> GetCompanyContactList(string companyId, string equipmentId, string contactType, string contactState, string keyword)
        {
            //取该设备的所有联系人信息
            var contactList = _sqlSugarUow.Db.Queryable<EMS_EQUIPMENT_CONTACT>()
                .Where(p => p.EQUIPMENT_ID == equipmentId && p.CONTACT_ESTATE == "1" && p.CONTACT_TYPE == contactType)
                .ToList();
            //存放设备联系人ID
            var arrContactId = new string[contactList.Count];
            for (int i = 0; i < contactList.Count; i++)
            {
                arrContactId[i] = contactList[i].CONTACT_ID;
            }
            var res = _sqlSugarUow.Db.Queryable<SYS6_COMPANY_CONTACT>()
                .Where(p => p.COMPANY_ID == companyId && p.CONTACT_STATE == "1")
                .WhereIF(keyword.IsNotNullOrEmpty(), p => p.COMPANY_TYPE.ToLower().Contains(keyword.ToLower()) || p.COMPANY_NAME.ToLower().Contains(keyword.ToLower()))
                .WhereIF(contactState.IsNotNullOrEmpty(), p => p.CONTACT_STATE == contactState)
                .ToList();
            //若当前设备联系人ID包含供应商联系人ID，则标记已选
            res.ForEach(item =>
            {
                if (arrContactId.Contains(item.CONTACT_ID))
                {
                    item.IF_SELECT = "1";
                }
            });
            return res;
        }
        
        public ResultDto ExportEquipmentList(List<ExportEquipmentDto> record, string labId, string hospitalId, string userName)
        {
            ResultDto result = new ResultDto();
            try
            {
                int oldCount = 0;
                int errPgroup = 0;
                var oldEquipment = _sqlSugarUow.Db.Queryable<EMS_EQUIPMENT_INFO>().ToList();
                var companyList = _sqlSugarUow.Db.Queryable<SYS6_COMPANY_INFO>().Where(p => p.COMPANY_STATE == "1").ToList();
                var newEquipment_insert = new List<EMS_EQUIPMENT_INFO>();
                var baseData = _sqlSugarUow.Db.Queryable<SYS6_BASE_DATA>().Where(p => p.DATA_STATE == "1").ToList();
                var baseEquipmentClass = baseData.Where(p => p.CLASS_ID == "设备分类").ToList();
                var repeatCodes = new List<string>();

                record.ForEach(item =>
                {
                    var old = oldEquipment.Where(p => p.EQUIPMENT_CODE == item.EQUIPMENT_CODE || p.EQUIPMENT_NUM == item.EQUIPMENT_NUM).FirstOrDefault();
                    if (old == null)
                    {
                        if (item.MGROUP_NAME != null)
                        {
                            var installData = item.INSTALL_DATE != null ? Convert.ToDateTime(item.INSTALL_DATE).ToString("yyyy-MM-dd") : null;
                            newEquipment_insert.Add(new EMS_EQUIPMENT_INFO
                            {
                                EQUIPMENT_ID = IDGenHelper.CreateGuid().ToString(),
                                HOSPITAL_ID = hospitalId,
                                LAB_ID = labId,
                                UNIT_ID = item.MGROUP_NAME,
                                EQUIPMENT_NUM = item.EQUIPMENT_NUM,
                                EQUIPMENT_NAME = item.EQUIPMENT_NAME,
                                EQUIPMENT_MODEL = item.EQUIPMENT_MODEL,
                                ENABLE_TIME = item.ENABLE_TIME,
                                DEPT_SECTION_NO = item.DEPT_SECTION_NO,
                                SECTION_NO = item.SECTION_NO,
                                SERIAL_NUMBER = item.SERIAL_NUMBER,
                                EQUIPMENT_CLASS = baseEquipmentClass.Where(p => p.DATA_ID.Contains(item.EQUIPMENT_CLASS)).Count() == 0 ? baseEquipmentClass.Where(p => p.DATA_CNAME == item.EQUIPMENT_CLASS).FirstOrDefault()?.DATA_ID : item.EQUIPMENT_CLASS,
                                PROFESSIONAL_CLASS = item.PROFESSIONAL_ClASS,
                                EQUIPMENT_CODE = item.EQUIPMENT_CODE,
                                MANUFACTURER = item.MANUFACTURER,
                                MANUFACTURER_ID = companyList.Where(p => p.COMPANY_NAME == item.MANUFACTURER).FirstOrDefault()?.COMPANY_ID,
                                DEALER = item.DEALER,
                                DEALER_ID = companyList.Where(p => p.COMPANY_NAME == item.DEALER).FirstOrDefault()?.COMPANY_ID,
                                INSTALL_DATE = installData,
                                INSTALL_AREA = item.INSTALL_AREA,
                                KEEP_PERSON = item.KEEP_PERSON,
                                CONTACT_PHONE = item.CONTACT_PHONE,
                                EQUIPMENT_STATE = item.ENABLE_TIME == null ? "0" : "1",
                                REGISTRATION_NUM = item.REGISTRATION_NUM,
                                FIRST_RPERSON = userName,
                                FIRST_RTIME = DateTime.Now,
                                LAST_MPERSON = userName,
                                LAST_MTIME = DateTime.Now,
                                REMARK = DateTime.Now.ToString("yyyy-MM-dd") + "导入"
                            });
                        }
                        else
                        {
                            errPgroup++;
                        }
                    }
                    else
                    {
                        oldCount++;
                        repeatCodes.Add(old.EQUIPMENT_CODE);
                    }
                });
                //var reaptCode = newEquipment_insert.GroupBy(p => p.EQUIPMENT_CODE).Where(i => i.Count() > 1).Select(m => m.Key).ToList();
                //var reaptNum = newEquipment_insert.GroupBy(p => p.EQUIPMENT_NUM).Where(i => i.Count() > 1).Select(m => m.Key).ToList();
                //var strReapt = "";
                //if(reaptCode.Count > 0 )
                //{
                //    strReapt = "代号重复的数据为";
                //    reaptCode.ForEach(item =>
                //    {
                //        strReapt += item + ",";
                //    });
                //}
                //if(reaptNum.Count > 0 )
                //{
                //    strReapt += "序号重复的数据为";
                //    reaptNum.ForEach(item =>
                //    {
                //        strReapt += item + ",";
                //    });
                //}
                var oldAmount = newEquipment_insert.Count;
                newEquipment_insert.RemoveAll(p => p.EQUIPMENT_CLASS == null);
                var newAmount = newEquipment_insert.Count;
                var nullAmount = oldAmount - newAmount;
                _sqlSugarUow.Db.Insertable(newEquipment_insert).ExecuteCommand();
                var newWork_insert = new List<EMS_WORK_PLAN>();
                newEquipment_insert.ForEach(item =>
                {
                    newWork_insert.Add(new EMS_WORK_PLAN
                    {
                        WORK_PLAN_ID = IDGenHelper.CreateGuid().ToString(),
                        EQUIPMENT_ID = item.EQUIPMENT_ID,
                        HOSPITAL_ID = hospitalId,
                        FIRST_RPERSON = userName,
                        FIRST_RTIME = DateTime.Now,
                        LAST_MPERSON = userName,
                        LAST_MTIME = DateTime.Now,
                        WORK_PLAN_STATE = "1",
                        REMARK = item.REMARK
                    });
                });
                _sqlSugarUow.Db.Insertable(newWork_insert).ExecuteCommand();
                result.success = true;
                result.data = newEquipment_insert.Count();
                if (oldCount > 0)
                {
                    result.success = false;
                    result.msg = "存在" + oldCount + "条重复数据，设备代号为:" + string.Join('、', repeatCodes) + "。 ";
                }
                if (errPgroup > 0)
                {
                    result.success = false;
                    result.msg = "存在" + errPgroup + "条专业组错误的数据，" + result.msg;
                }
                if (nullAmount > 0)
                {
                    result.success = false;
                    result.msg = "存在" + nullAmount + "条设备分类的数据，" + result.msg;
                }
                var totalSuccess = record.Count - (oldCount + errPgroup + nullAmount);
                result.msg = result.msg + totalSuccess + "条数据导入成功";
            }
            catch (Exception ex)
            {
                result.success = false;
                result.msg = "设备模板记录导入失败";
                _logger.LogError("设备模板记录导入失败:\n" + ex.Message);
            }
            return result;
        }

        public List<CardTypeDto> GetCardTypeList()
        {
            List<CardTypeDto> result = new List<CardTypeDto>();
            //读取文件获取设置
            string path = OfficeHelper.PathCombine(_hostingEnvironment.ContentRootPath, "configs", "CardTypeSetting.json");
            if (File.Exists(path))
            {
                try
                {
                    // 读取文件内容
                    string content = File.ReadAllText(path);
                    // 将文本内容转换为JSON对象
                    List<CardTypeDto> CardTypeSetting = JsonConvert.DeserializeObject<List<CardTypeDto>>(content);
                    result.AddRange(CardTypeSetting.Where(a => a.IsShow == "1"));
                }
                catch (Exception ex)
                {
                    throw new BizException("设备标识卡模板配置文件CardTypeSetting.json配置有误，请修改后再试！");
                }
            }
            return result;
        }

        /// <summary>
        /// 分配备案实验室到设备
        /// </summary>
        /// <param name="smblLabId"></param>
        /// <param name="equipmentIds"></param>
        public void DistributionEquipmentsToSmblLab(string smblLabId ,  List<string> equipmentIds)
        {
            _sqlSugarUow.Db.Updateable<EMS_EQUIPMENT_INFO>()
                .SetColumns(x => new()
                {
                    SMBL_LAB_ID = smblLabId,
                    SMBL_FLAG = "1"
                })
                .Where(x => equipmentIds.Contains(x.EQUIPMENT_ID))
                .ExecuteCommand();
        }

        /// <summary>
        /// 取消分配备案实验室到设备
        /// </summary>
        /// <param name="equipmentIds"></param>
        public void UnDistributionEquipmentsToSmblLab(List<string> equipmentIds)
        {
            _sqlSugarUow.Db.Updateable<EMS_EQUIPMENT_INFO>()
                .SetColumns(x => new()
                {
                    SMBL_LAB_ID = "",
                })
                .Where(x => equipmentIds.Contains(x.EQUIPMENT_ID))
                .ExecuteCommand();
        }
    }
}
