﻿using AutoMapper;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XH.H82.Models.Entities.Transaction;

namespace XH.H82.Models.Dtos.Implement
{
    [AutoMap(typeof(EMS_IMPLEMENT_INFO))]
    public class ImplementDto
    {
        public string IMPLEMENT_ID { get; set; }

        /// <summary>
        /// 医疗机构ID
        /// </summary>
        public string HOSPITAL_ID { get; set; }

        /// <summary>
        /// 设备ID
        /// </summary>
        public string EQUIPMENT_ID { get; set; }

        /// <summary>
        /// 使用周期;0 日/次  1月
        /// </summary>
        public string? IMPLEMENT_CYCLE { get; set; }

        /// <summary>
        /// 登记人员
        /// </summary>
        public string? IMPLEMENT_PERSON { get; set; }

        /// <summary>
        /// 使用时间
        /// </summary>
        public DateTime? IMPLEMENT_DATA { get; set; }

        /// <summary>
        /// 登记内容
        /// </summary>
        public string? IMPLEMENT_CONTEXT { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public string IMPLEMENT_STATE { get; set; } = "1";
        public string REMARK { get; set; }
        /// <summary>
        /// 数据来源   0 自己   1 监测   2 事务项
        /// </summary>
        public string IMPLEMENT_SOURCE { get; set; } = "0";
    }



    public class ImplementInput
    {
        public string? IMPLEMENT_CYCLE { get; set; }
        public DateTime? IMPLEMENT_DATA { get; set; }
        public string? IMPLEMENT_PERSON { get; set; }
        public string? IMPLEMENT_CONTEXT { get; set; }
        public string REMARK { get; set; }
    }
}
