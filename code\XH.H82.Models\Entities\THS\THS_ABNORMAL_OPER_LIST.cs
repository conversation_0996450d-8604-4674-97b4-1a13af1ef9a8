using H.Utility.SqlSugarInfra;
using SqlSugar;

namespace XH.H82.Models.Entities.THS;

/// <summary>
/// 异常记录
/// </summary>
[DBOwner("XH_DATA")]
[SugarTable("THS_ABNORMAL_OPER_LIST")]
public class THS_ABNORMAL_OPER_LIST : BaseField
{

    /// <summary>
    /// 异常处理id
    /// </summary>
    [SugarColumn(IsPrimaryKey = true)]
    public string  OPER_ID { get; set; }
    /// <summary>
    /// 监测记录ID
    /// </summary>
    public string  MONITOR_ID { get; set; }
    /// <summary>
    /// 异常类型
    /// </summary>
    public string OPER_TYPE { get; set; }
    /// <summary>
    /// 处理人员
    /// </summary>
    public string?  OPER_PERSON { get; set; }
    /// <summary>
    /// 处理时间
    /// </summary>
    public DateTime?  OPER_TIME { get; set; }
    /// <summary>
    /// 处理电脑
    /// </summary>
    public string? OPER_COMPUTER { get; set; }
    /// <summary>
    /// 原因分类
    /// </summary>
    public string  OPER_CAUSE_CLASS { get; set; }
    /// <summary>
    /// 原因
    /// </summary>
    public string  OPER_CAUSE { get; set; }
    /// <summary>
    /// 状态(0未处理1已处理)
    /// </summary>
    public string OPER_STATE { get; set; }
    /// <summary>
    /// ths 设备ID
    /// </summary>
    public string  EQUIPMENT_ID { get; set; }
    /// <summary>
    /// 报警时间
    /// </summary>
    public DateTime?  ALARM_TIME { get; set; }
    /// <summary>
    /// 监测点ID
    /// </summary>
    public string POINT_ID { get; set; }

    /// <summary>
    /// 报警时长
    /// </summary>
    public int ALARM_CONTINUOUS_TIME { get; set; } = 0;

}