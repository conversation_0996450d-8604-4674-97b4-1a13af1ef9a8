# 请参阅 https://aka.ms/customizecontainer 以了解如何自定义调试容器，以及 Visual Studio 如何使用此 Dockerfile 生成映像以更快地进行调试。

# 此阶段用于在快速模式(默认为调试配置)下从 VS 运行时
FROM mcr.microsoft.com/dotnet/aspnet:6.0 AS base
ADD sources.list /etc/apt/
WORKDIR /
RUN mkdir -p /usr/share/fonts
RUN mkdir -p /app
COPY ./ChineseFont/* /usr/share/fonts
RUN apt-get update && \
    apt-get upgrade -y
RUN    apt-get install -y fontconfig
RUN    apt-get install -y xfonts-utils
RUN    apt-get clean
#ENV FONTCONFIG_FILE=/etc/fonts/fonts.conf
#RUN echo "<fontconfig><dir>/usr/share/fonts</dir></fontconfig>" > /etc/fonts/local.conf
ENV LANG en_US.utf8
ENV TZ=Asia/Shanghai
RUN fc-cache -fv
VOLUME /app/logs
WORKDIR /app
EXPOSE 18482

# 此阶段用于生成服务项目
FROM mcr.microsoft.com/dotnet/sdk:6.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["XH.H82.API/XH.H82.API.csproj", "XH.H82.API/"]
COPY ["XH.H82.Base/XH.H82.Base.csproj", "XH.H82.Base/"]
COPY ["XH.H82.Models/XH.H82.Models.csproj", "XH.H82.Models/"]
COPY ["XH.H82.IServices/XH.H82.IServices.csproj", "XH.H82.IServices/"]
COPY ["XH.H82.Services/XH.H82.Services.csproj", "XH.H82.Services/"]
RUN dotnet restore "./XH.H82.API/XH.H82.API.csproj"
COPY . .
WORKDIR "/src/XH.H82.API"
RUN dotnet build "./XH.H82.API.csproj" -c $BUILD_CONFIGURATION -o /app/build

# 此阶段用于发布要复制到最终阶段的服务项目
FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./XH.H82.API.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

# 此阶段在生产中使用，或在常规模式下从 VS 运行时使用(在不使用调试配置时为默认值)
FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "XH.H82.API.dll"]