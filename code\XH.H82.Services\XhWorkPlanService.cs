﻿using AutoMapper;
using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Serilog;
using SqlSugar;
using XH.H82.IServices;
using XH.H82.Models.DeviceRelevantInformation;
using XH.H82.Models.Dtos;
using XH.H82.Models.Dtos.Tim;
using XH.H82.Models.Entities;
using XH.H82.Models.Entities.Common;
using XH.H82.Models.Entities.OperationLog;
using XH.H82.Models.Entities.Tim;
using XH.H82.Models.SugarDbContext;
using XH.H82.Services.DeviceDataRefresh;
using XH.H82.Services.OperationLog;
using XH.LAB.UTILS.Helpers;
using XH.LAB.UTILS.Interface;
using XH.LAB.UTILS.Models;
using ClaimsDto = H.Utility.ClaimsDto;

namespace XH.H82.Services
{
    public class XhWorkPlanService : IXhWorkPlanService
    {
        private readonly ISqlSugarUow<SugarDbContext_Master> _sqlSugarUow;
        private readonly ILogger<OperationRecordService> _logger;
        private readonly IBaseService _baseService;
        private readonly IAuthorityService2 _authorityService;
        private readonly IEquipmentDocService _equipmentDocService;

        private readonly IMapper _mapper;
        private ClaimsDto _user;
        public XhWorkPlanService(ISqlSugarUow<SugarDbContext_Master> sqlSugarUow, IHttpContextAccessor httpContext, IMapper mapper, IBaseService baseService, IAuthorityService2 authorityService, IEquipmentDocService equipmentDocService)
        {
            _user = httpContext.HttpContext.User.ToClaimsDto();
            _user.USER_NAME = _user.HIS_NAME;
            _sqlSugarUow = sqlSugarUow;
            sqlSugarUow.SetCreateTimeAndCreatePersonData(_user);
            _sqlSugarUow.IsDeleted<EMS_MAINTAIN_INFO>(x => x.MAINTAIN_STATE != "2")
                .IsDeleted<EMS_CORRECT_INFO>(x => x.CORRECT_STATE != "2");
            _mapper = mapper;
            _baseService = baseService;
            _authorityService = authorityService;
            _equipmentDocService = equipmentDocService;
            //ExecutingChangeSqlHelper.ExecutingChangeSql(_sqlSugarUow);
        }


        public List<WorkPlanDto> GetWorkPlanList(string userNo, string hospitalId, string keyword, string mgroupId, string equipmentClass, string labId, string pgroupId, string areaId)
        {
            var workPlanList = new List<WorkPlanDto>();
            //专业组列表
            var groupList =
                _authorityService.GetUserPermissionPgroup(_sqlSugarUow,new OrgParams()
                {
                    hospital_id = hospitalId,
                    lab_id =  labId,
                    area_id = areaId
                },"H82");
    
            var q = _sqlSugarUow.Db.Queryable<EMS_EQUIPMENT_INFO>()
                .LeftJoin<EMS_WORK_PLAN>((a, b) => a.EQUIPMENT_ID == b.EQUIPMENT_ID)
                .Where((a, b) => groupList.Select(i => i.PGROUP_ID).Contains(a.UNIT_ID) && b.WORK_PLAN_STATE == "1" && (a.IS_HIDE == null || a.IS_HIDE != "1"))//不显示 0-隐藏设备
                .WhereIF(pgroupId.IsNotNullOrEmpty(), (a, b) => a.UNIT_ID == pgroupId)
                .WhereIF(equipmentClass.IsNotNullOrEmpty(), (a, b) => a.EQUIPMENT_CLASS == equipmentClass)
                .WhereIF(keyword.IsNotNullOrEmpty(), (a, b) => a.EQUIPMENT_NUM.ToLower().Contains(keyword.ToLower())
                || a.EQUIPMENT_NAME.ToLower().Contains(keyword.ToLower())
                || a.EQUIPMENT_MODEL.ToLower().Contains(keyword.ToLower()))
                .Select((a, b) => new
                {
                    b.WORK_PLAN_ID,
                    a.EQUIPMENT_ID,
                    a.EQUIPMENT_NAME,
                    a.EQUIPMENT_MODEL,
                    a.EQUIPMENT_CODE,
                    b.MAINTAIN_INTERVALS,
                    b.MAINTAIN_WARN_INTERVALS,
                    b.YEARLY_MAINTAIN,
                    b.YEARLY_MAINTAIN_WARN,
                    b.MONTHLY_MAINTAIN,
                    b.MONTHLY_MAINTAIN_WARN,
                    b.COMPARISON_INTERVALS,
                    b.COMPARISON_WARN_INTERVALS,
                    b.VERIFICATION_INTERVALS,
                    b.VERIFICATION_WARN_INTERVALS,
                    b.CORRECT_INTERVALS,
                    b.CORRECT_WARN_INTERVALS,
                    b.OPER_PERSON,
                    b.OPER_TIME,
                    b.SUBMIT_TIME,
                    b.SUBMIT_USER_ID,
                    b.CURRENT_STATE,
                    b.QUARTERLY_MAINTAIN,
                    b.QUARTERLY_MAINTAIN_WARN,
                    a.REGISTRATION_NUM,
                    a.MANUFACTURER,
                    a.UNIT_ID,
                    a.SMBL_LAB_ID,
                    a.SMBL_FLAG
                });
            if (mgroupId.IsNotNullOrEmpty())
            {
                var pList = _sqlSugarUow.Db.Queryable<SYS6_INSPECTION_PGROUP>()
                    .Where(p => p.MGROUP_ID == mgroupId && p.PGROUP_STATE == "1")
                    .Select(i => i.PGROUP_ID).ToList();
                q = q.Where(a => pList.Contains(a.UNIT_ID));
            }
            var res = q.ToList();
            res.ForEach(item =>
            {
                workPlanList.Add(new WorkPlanDto
                {
                    WORK_PLAN_ID = item.WORK_PLAN_ID,
                    EQUIPMENT_ID = item.EQUIPMENT_ID,
                    EQUIPMENT_NAME = item.EQUIPMENT_NAME,
                    EQUIPMENT_MODEL = item.EQUIPMENT_MODEL,
                    EQUIPMENT_CODE = item.EQUIPMENT_CODE,
                    MAINTAIN_INTERVALS = item.MAINTAIN_INTERVALS,
                    MAINTAIN_WARN_INTERVALS = item.MAINTAIN_WARN_INTERVALS,
                    YEARLY_MAINTAIN = item.YEARLY_MAINTAIN,
                    YEARLY_MAINTAIN_WARN = item.YEARLY_MAINTAIN_WARN,
                    COMPARISON_INTERVALS = item.COMPARISON_INTERVALS,
                    MONTHLY_MAINTAIN = item.MONTHLY_MAINTAIN,
                    MONTHLY_MAINTAIN_WARN = item.MONTHLY_MAINTAIN_WARN,
                    QUARTERLY_MAINTAIN = item.QUARTERLY_MAINTAIN,
                    QUARTERLY_MAINTAIN_WARN = item.QUARTERLY_MAINTAIN_WARN,
                    COMPARISON_WARN_INTERVALS = item.COMPARISON_WARN_INTERVALS,
                    VERIFICATION_INTERVALS = item.VERIFICATION_INTERVALS,
                    CORRECT_INTERVALS = item.CORRECT_INTERVALS,
                    CORRECT_WARN_INTERVALS = item.CORRECT_WARN_INTERVALS,
                    VERIFICATION_WARN_INTERVALS = item.VERIFICATION_WARN_INTERVALS,
                    OPER_PERSON = item.OPER_PERSON,
                    OPER_TIME = item.OPER_TIME,
                    REGISTRATION_NUM = item.REGISTRATION_NUM,
                    REGISTRATION_NAME = item.MANUFACTURER,
                    MGROUP_ID = item.UNIT_ID,
                    SUBMIT_TIME = item.SUBMIT_TIME,
                    SUBMIT_USER_ID = item.SUBMIT_USER_ID,
                    CURRENT_STATE = item.CURRENT_STATE,
                    CURRENT_STATE_NAME = EnumUtils.FromID<OperationStateEnum>(item.CURRENT_STATE).Value.ToDesc(),
                });
            });
            return workPlanList;
        }


        public List<EmsWorkPlanDto> GetWorkPlans(ClaimsDto user, string keyword, string mgroupId, string equipmentClass, string labId, string pgroupId, string areaId)
        {
            var transactions = _sqlSugarUow.Db.Queryable<TIM_FORM_MAIN_INFO>()
                .InnerJoin<TIM_WORK_FORM>((main, form) => main.FORM_ID == form.FORM_ID)
                .InnerJoin<TIM_WORK_FORM_VER>((main, form, formVer) => form.FORM_ID == formVer.FORM_ID)
                .Where((main, form, formVer) => form.FORM_STATE == "1")
                .Where((main, form, formVer) => formVer.FORM_VER_STATE == "6")
                .Select<TransactionForm>()
                .ToList();
            var transactionFormItmes = _sqlSugarUow.Db.Queryable<TIM_WORK_INFO>().Select<TransactionItem>().ToList();
            
            foreach (var form in transactions)
            {
                form.CLASS_NAME = form.CLASS_ID switch
                {
                    "1" => "使用",
                    "2" => "保养",
                    "3" => "维修",
                    "4" => "校准",
                    _ => "其他"
                };

                var equipmentTransactionFormItmes =
                    transactionFormItmes.Where(x => x.FORM_VER_ID == form.FORM_VER_ID).ToList();
                foreach (var equipmentTransactionFormItme in equipmentTransactionFormItmes)
                {
                    equipmentTransactionFormItme.WORK_NAME = equipmentTransactionFormItme.WORK_NAME.Replace("\r\n", "").Trim();
                    
                    equipmentTransactionFormItme.WORK_PLAN_TYPE = equipmentTransactionFormItme.WORK_PLAN_TYPE switch
                    {
                        "1" => "日保养",
                        "2" => "月保养",
                        "3" => "季度保养",
                        "4" => "年保养",
                        _ => ""
                    };
                    equipmentTransactionFormItme.TRANSACTION_ITEM_CLASS =  equipmentTransactionFormItme.WORK_PLAN_TYPE.IsNullOrEmpty()? $"【{form.CLASS_NAME}】"  : $"【{equipmentTransactionFormItme.WORK_PLAN_TYPE}】"  ;
                }
                form.TransactionItems.AddRange(equipmentTransactionFormItmes);
            }
            
            try
            {
                var authContext = new AuthorityContext(_sqlSugarUow, _authorityService);
                authContext.SetUser(user,labId,areaId);
                var pGroups = authContext.GetAccessibleProfessionalGroups(labId, areaId).Where(x => x.MGROUP_ID != null);
                var pGroupsIds = pGroups
                    .WhereIF(mgroupId.IsNotNullOrEmpty(), p => p.MGROUP_ID == mgroupId)
                    .Select(x => x.PGROUP_ID);

                var equipments = _sqlSugarUow.Db.Queryable<EMS_EQUIPMENT_INFO>()
                    .Includes(x => x.eMS_WORK_PLAN)
                    .Includes(i => i.eMS_MAINTAIN_INFO)
                    .Includes(i => i.eMS_CORRECT_INFO)
                    .Includes(i => i.eMS_COMPARISON_INFO)
                    .Includes(i => i.eMS_VERIFICATION_INFO)
                    .Where(e => e.EQUIPMENT_STATE == "1")  //报废和停用的不查询
                    .Where(e => e.IS_HIDE == null || e.IS_HIDE != "1")//隐藏的不查
                    .Where(e => pGroupsIds.Contains(e.UNIT_ID))
                    .WhereIF(pgroupId.IsNotNullOrEmpty(), a => a.UNIT_ID == pgroupId)
                    .WhereIF(equipmentClass.IsNotNullOrEmpty(), a => a.EQUIPMENT_CLASS == equipmentClass)
                    .WhereIF(keyword.IsNotNullOrEmpty(), a => a.EQUIPMENT_CODE.ToLower().Contains(keyword.ToLower())
                    || a.EQUIPMENT_NAME.ToLower().Contains(keyword.ToLower())
                    || a.EQUIPMENT_MODEL.ToLower().Contains(keyword.ToLower()))
                    .OrderBy(x => x.EQUIPMENT_CODE)
                    .ToList();

                var equipmentContext = new EquipmentContext(_sqlSugarUow);

                if (equipments.Count(x => x.LAST_MTIME == null) > 0)
                {
                    _sqlSugarUow.Db.Updateable<EMS_EQUIPMENT_INFO>().SetColumns(x => new()
                    {
                        LAST_MTIME = DateTime.Now,
                    }).Where(x => x.LAST_MTIME == null).ExecuteCommand();
                }

                equipmentContext.Injection(equipments);
                var result = equipmentContext.equipments.Where(x => x.eMS_WORK_PLAN != null)
                    .Select(x => new EmsWorkPlanDto()
                    {
                        WORK_PLAN_ID = x.eMS_WORK_PLAN.WORK_PLAN_ID,
                        EQUIPMENT_ID = x.EQUIPMENT_ID,
                        CURRENT_STATE = x.eMS_WORK_PLAN.CURRENT_STATE,
                        CURRENT_STATE_NAME = EnumUtils.FromID<OperationStateEnum>(x.eMS_WORK_PLAN.CURRENT_STATE).Value.ToDesc(),
                        EQUIPMENT_NAME = x.EQUIPMENT_NAME,
                        EQUIPMENT_MODEL = x.EQUIPMENT_MODEL,
                        EQUIPMENT_CODE = x.EQUIPMENT_CODE,
                        MAINTAIN_INTERVALS = x.eMS_WORK_PLAN.MAINTAIN_INTERVALS,
                        MAINTAIN_WARN_INTERVALS = x.eMS_WORK_PLAN.MAINTAIN_WARN_INTERVALS,
                        LAST_MAINTAIN_DATE = null,
                        MONTHLY_MAINTAIN = x.eMS_WORK_PLAN.MONTHLY_MAINTAIN,
                        MONTHLY_MAINTAIN_WARN = x.eMS_WORK_PLAN.MONTHLY_MAINTAIN_WARN,
                        LAST_MONTHLY_MAINTAIN_DATE = null,
                        QUARTERLY_MAINTAIN = x.eMS_WORK_PLAN.QUARTERLY_MAINTAIN,
                        QUARTERLY_MAINTAIN_WARN = x.eMS_WORK_PLAN.QUARTERLY_MAINTAIN_WARN,
                        LAST_QUARTERLY_MAINTAIN_DATE = null,
                        YEARLY_MAINTAIN = x.eMS_WORK_PLAN.YEARLY_MAINTAIN,
                        YEARLY_MAINTAIN_WARN = x.eMS_WORK_PLAN.YEARLY_MAINTAIN_WARN,
                        LAST_YEARLY_MAINTAIN_DATE = null,
                        CORRECT_INTERVALS = x.eMS_WORK_PLAN.CORRECT_INTERVALS,
                        CORRECT_WARN_INTERVALS = x.eMS_WORK_PLAN.CORRECT_WARN_INTERVALS,
                        LAST_CORRECT_INTERVALS_DATE = x.LAST_CORRECT_DATE,
                        NEXT_CORRECT_INTERVALS_DATE = x.NEXT_CORRECT_DATE,
                        CORRECT_UNIT = x.LAST_CORRECT_DEPT,
                        EQ_IN_PERSON = x.KEEP_PERSON,
                        COMPARISON_INTERVALS = x.eMS_WORK_PLAN.COMPARISON_INTERVALS,
                        COMPARISON_WARN_INTERVALS = x.eMS_WORK_PLAN.COMPARISON_WARN_INTERVALS,
                        LAST_COMPARISON_INTERVALS_DATE = x.LAST_COMPARISON_DATE,
                        NEXT_COMPARISON_INTERVALS_DATE = x.NEXT_COMPARISON_DATE,
                        OPER_PERSON = x.eMS_WORK_PLAN.OPER_PERSON,
                        OPER_TIME = x.eMS_WORK_PLAN.OPER_TIME,
                        AUDITOR_USER_NAME = "",
                        AUDITOR_TIME = null,
                        AUDITOR_CONTEXT = "",
                        REGISTRATION_NUM = x.REGISTRATION_NUM,
                        REGISTRATION_NAME = x.MANUFACTURER,
                        SMBL_FLAG = x.SMBL_FLAG =="1" ? "1":"0" 
                    }).ToList();

                CalculateMaintainNextTime(equipmentContext, result);
                GetAuditRecode(result);

                foreach (var item in result)
                {
                    item.IS_ASSOCIATED = transactions.Count(x => x.WORK_MAINID == item.EQUIPMENT_ID) > 0;
                    item.TRANSANCTIO_FORMS.AddRange(transactions.Where(x => x.WORK_MAINID == item.EQUIPMENT_ID).ToList());
                }
                
                return result;
            }
            catch (Exception e)
            {
                Log.Error(e.Message);
                throw e;
            }

        }


        public List<EmsWorkPlanCirculationRecordDto> GetEmsWordPlanCirculationRecords(string planId)
        {

            var recodes = GetDataCirculationRecord(planId);

            var result = _mapper.Map<List<EmsWorkPlanCirculationRecordDto>>(recodes);

            foreach (var item in result)
            {
                item.InitiatorStateValue = ShowCirculationRecordStateValue(item.InitiatorStateValue, item.InitiatorState);
            }

            return result;
        }


        private string ShowCirculationRecordStateValue(string stataName, OperationStateEnum state)
        {
            switch (state)
            {
                case OperationStateEnum.Overruled:
                    return "审核/驳回";
                case OperationStateEnum.NotSubmitted:
                    return "未提交";
                case OperationStateEnum.Submitted:
                    return stataName;
                case OperationStateEnum.Audited:
                    return "审核/通过";
                default:
                    return stataName;
            }
        }


        private List<EMS_OPER_LOG> GetDataCirculationRecord(string dataId)
        {

            var result = _sqlSugarUow.Db.Queryable<EMS_OPER_LOG>()
                .Where(x => x.OPER_MAIN_ID == dataId)
                .OrderBy(x => new { x.LAST_MTIME, x.OPER_STATE })

                .ToList();

            if (result is null)
            {
                result = new List<EMS_OPER_LOG>();
            }

            return result;

        }



        public void SubmitPlan(ClaimsDto submitUser, string planId, string AuditorName, string AuditorId)
        {
            OperationalWorkPlan(submitUser, AuditorId, AuditorName, planId, OperationStateEnum.Submitted);
        }

        public void AuditPlan(ClaimsDto AuditorUser, string planId, string? content)
        {
            OperationalWorkPlan(AuditorUser, AuditorUser.USER_NO, AuditorUser.USER_NAME, planId, OperationStateEnum.Audited, content);
        }

        public void OverrulPlan(ClaimsDto AuditorUser, string planId, string? content)
        {
            //驳回操作会直接返回上一条记录的提交者手上
            OperationalWorkPlan(AuditorUser, "", "", planId, OperationStateEnum.Overruled, content);
        }


        /// <summary>
        /// 创建流转记录通用方法
        /// </summary>
        /// <param name="CurrentUser">当前操作人</param>
        /// <param name="nextUserId">下一个操作人</param>
        /// <param name="nextUserName">下一个操作人名</param>
        /// <param name="planId">工作计划ID</param>
        /// <param name="state">操作状态</param>
        /// <param name="content">操作内容</param>
        /// <exception cref="NotImplementedException"></exception>
        private void OperationalWorkPlan(ClaimsDto CurrentUser, string nextUserId, string nextUserName, string planId, OperationStateEnum state, string? content = null)
        {
            switch (state)
            {
                case OperationStateEnum.Overruled:
                    var overruledContext = new DataRecordCentext(CurrentUser, planId, _sqlSugarUow);
                    overruledContext.CreateOverruledRecode(content);
                    break;
                case OperationStateEnum.NotSubmitted:
                    var notSubmittedContext = new DataRecordCentext(CurrentUser, planId, _sqlSugarUow);
                    notSubmittedContext.CreateNotSubmitedRecode(nextUserId, nextUserName, content);
                    break;
                case OperationStateEnum.Submitted:
                    var submittedContext = new DataRecordCentext(CurrentUser, planId, _sqlSugarUow);
                    submittedContext.CreateSubmitedRecode(nextUserId, nextUserName, content);
                    break;
                case OperationStateEnum.Audited:
                    var AuditedContext = new DataRecordCentext(CurrentUser, planId, _sqlSugarUow);
                    AuditedContext.CreateAuditdRecode(nextUserId, nextUserName, content);
                    break;
                default:
                    throw new NotImplementedException();
            }
            _sqlSugarUow.Db.Updateable<EMS_WORK_PLAN>()
               .SetColumns(x => new EMS_WORK_PLAN()
               {
                   CURRENT_STATE = state.ToID(),
                   OPER_PERSON = CurrentUser.HIS_NAME,
                   OPER_TIME = DateTime.Now,
                   LAST_MTIME = DateTime.Now,
                   LAST_MPERSON = CurrentUser.USER_NAME,
               })
               .Where(x => x.WORK_PLAN_ID == planId)
               .ExecuteCommand();
        }

        /// <summary>
        /// 计算下一次保养时间
        /// </summary>
        /// <param name="emsWorkPlan"></param>
        private void CalculateMaintainNextTime(EquipmentContext equipmentContext, List<EmsWorkPlanDto> plans)
        {

            foreach (var plan in plans)
            {
                var equipment = equipmentContext.GetEquipment(plan.EQUIPMENT_ID);
                var maintenanceRecodes = equipment.eMS_MAINTAIN_INFO;
                var week = maintenanceRecodes.Where(x => x.MAINTAIN_CYCLE.Contains("周"))
               .OrderByDescending(x => x.MAINTAIN_DATE)
               .FirstOrDefault(x => x.EQUIPMENT_ID == plan.EQUIPMENT_ID);

                var month = maintenanceRecodes.Where(x => x.MAINTAIN_CYCLE.Contains("月"))
                    .OrderByDescending(x => x.MAINTAIN_DATE).FirstOrDefault(x => x.EQUIPMENT_ID == plan.EQUIPMENT_ID);

                var quarter = maintenanceRecodes.Where(x => x.MAINTAIN_CYCLE.Contains("季度"))
                    .OrderByDescending(x => x.MAINTAIN_DATE).FirstOrDefault(x => x.EQUIPMENT_ID == plan.EQUIPMENT_ID);

                var year = maintenanceRecodes.Where(x => x.MAINTAIN_CYCLE.Contains("年"))
                    .OrderByDescending(x => x.MAINTAIN_DATE).FirstOrDefault(x => x.EQUIPMENT_ID == plan.EQUIPMENT_ID);

                //最近一次保养时间
                plan.LAST_MAINTAIN_DATE = week is not null ? week.MAINTAIN_DATE : null;
                plan.LAST_MONTHLY_MAINTAIN_DATE = month is not null ? month.MAINTAIN_DATE : null;
                plan.LAST_QUARTERLY_MAINTAIN_DATE = quarter is not null ? quarter.MAINTAIN_DATE : null;
                plan.LAST_YEARLY_MAINTAIN_DATE = year is not null ? year.MAINTAIN_DATE : null;

                //下一次保养时间
                plan.NEXT_MAINTAIN_DATE = week is not null ? week.MAINTAIN_DATE.Value.AddDays(Convert.ToDouble(plan.MAINTAIN_INTERVALS)) : null;
                plan.NEXT_MONTHLY_MAINTAIN_DATE = month is not null ? month.MAINTAIN_DATE.Value.AddDays(Convert.ToDouble(plan.MONTHLY_MAINTAIN)) : null;
                plan.NEXT_QUARTERLY_MAINTAIN_DATE = quarter is not null ? quarter.MAINTAIN_DATE.Value.AddDays(Convert.ToDouble(plan.QUARTERLY_MAINTAIN)) : null;
                plan.NEXT_YEARLY_MAINTAIN_DATE = year is not null ? year.MAINTAIN_DATE.Value.AddDays(Convert.ToDouble(plan.YEARLY_MAINTAIN)) : null;
            }
        }

        /// <summary>
        /// 计算下一次校准时间
        /// </summary>
        private void CalculateCorrectNextTime(List<EmsWorkPlanDto> plans)
        {
            var equipmentIds = plans.Select(x => x.EQUIPMENT_ID);

            var correctRecodes = _sqlSugarUow.Db.Queryable<EMS_CORRECT_INFO>()
                .Where(x => equipmentIds.Contains(x.EQUIPMENT_ID))
                .ToList();

            foreach (var plan in plans)
            {
                var correct = correctRecodes
               .OrderByDescending(x => x.CORRECT_DATE)
               .FirstOrDefault(x => x.EQUIPMENT_ID == plan.EQUIPMENT_ID);

                if (correct is not null)
                {
                    if (plan.CORRECT_INTERVALS.IsNotNullOrEmpty())
                    {
                        plan.NEXT_CORRECT_INTERVALS_DATE = correct.CORRECT_DATE.HasValue ? correct.CORRECT_DATE.Value.AddDays(Convert.ToDouble(plan.CORRECT_INTERVALS)) : DateTime.Now.AddDays(Convert.ToDouble(plan.CORRECT_INTERVALS));
                    }

                    plan.LAST_CORRECT_INTERVALS_DATE = correct.CORRECT_DATE.HasValue ? correct.CORRECT_DATE.Value : null;
                    plan.CORRECT_UNIT = correct.CORRECT_DEPT;
                }

            }

        }

        /// <summary>
        /// 计算下一次比对时间
        /// </summary>
        /// <param name="plans"></param>
        private void CalculateComparisonNextTime(List<EmsWorkPlanDto> plans)
        {

            var equipmentIds = plans.Select(x => x.EQUIPMENT_ID);


            var comparisonRecodes = _sqlSugarUow.Db.Queryable<EMS_COMPARISON_INFO>()
                .Where(x => equipmentIds.Contains(x.EQUIPMENT_ID))
                .ToList();


            foreach (var plan in plans)
            {
                var comparison = comparisonRecodes
               .OrderByDescending(x => x.COMPARISON_DATE)
               .FirstOrDefault(x => x.EQUIPMENT_ID == plan.EQUIPMENT_ID);

                if (comparison is not null)
                {
                    if (plan.COMPARISON_INTERVALS.IsNotNullOrEmpty())
                    {
                        plan.NEXT_COMPARISON_INTERVALS_DATE = comparison.COMPARISON_DATE.HasValue ? comparison.COMPARISON_DATE.Value.AddDays(Convert.ToDouble(plan.COMPARISON_INTERVALS)) : DateTime.Now.AddDays(Convert.ToDouble(plan.COMPARISON_INTERVALS));
                    }
                    plan.LAST_COMPARISON_INTERVALS_DATE = comparison.COMPARISON_DATE.HasValue ? comparison.COMPARISON_DATE.Value : null;
                }
            }


        }

        /// <summary>
        /// 查询工作计划审核记录
        /// </summary>
        /// <param name="plans"></param>
        private void GetAuditRecode(List<EmsWorkPlanDto> plans)
        {

            var workPlanIds = plans.Select(x => x.WORK_PLAN_ID);

            var recodes = _sqlSugarUow.Db.Queryable<EMS_OPER_LOG>()
                .Where(x => workPlanIds.Contains(x.OPER_MAIN_ID))
                .Where(x => x.OPER_STATE == OperationStateEnum.Audited)
                .ToList();

            foreach (var plan in plans)
            {
                var recode = recodes
                    .Where(x => x.OPER_MAIN_ID == plan.WORK_PLAN_ID)
                    .OrderByDescending(x => x.LAST_MTIME)
                    .FirstOrDefault();
                if (recode is not null)
                {
                    plan.AUDITOR_USER_NAME = recode.OPER_PERSON;
                    plan.AUDITOR_TIME = recode.FIRST_RTIME;
                    plan.AUDITOR_CONTEXT = recode.OPER_CONTENT;
                }
            }

        }



        public List<NewOATreeDto> GetMgroupList(string userNo, string hospitalId, string labId, string areaId)
        {

            var equipmentContext = new EquipmentContext(_sqlSugarUow);

            var pipeliningList = _sqlSugarUow.Db.Queryable<LIS6_PIPELINING_INSTRUMENT>().ToList();
            //基础数据
            var trees = _baseService.GetAllHospitalMgroups(userNo, labId);

            var pgids = new List<string>();

            trees.ForEach(tree =>
            {
                if (areaId.IsNotNullOrEmpty())
                {
                    if (tree.AREA_ID != areaId)
                    {
                        return;
                    }
                }
                tree.SecondStageTree.ForEach(secondStageTree =>
                {

                    secondStageTree.ThirdStageTree.ForEach(x =>
                    {

                        pgids.Add(x.PGROUP_ID);

                    });
                });
            });

            var equipments = _sqlSugarUow.Db.Queryable<EMS_EQUIPMENT_INFO>()
            .Where(x => pgids.Contains(x.UNIT_ID))
            .Where(x => x.EQUIPMENT_STATE == "1")
            .Where(e => e.IS_HIDE == null || e.IS_HIDE != "1")
            .Select(a => new
            {
                a.EQUIPMENT_ID,
                a.EQUIPMENT_CODE,
                a.EQUIPMENT_STATE,
                a.EQUIPMENT_CLASS,
                a.EQUIPMENT_TYPE,
                a.INSTRUMENT_ID,
                a.VEST_PIPELINE,
                a.DEPT_SECTION_NO,
                a.UNIT_ID,
                a.IS_HIDE,
            }).ToList();

            foreach (var tree in trees)
            {
                foreach (var secondStageTree in tree.SecondStageTree)
                {
                    foreach (var thirdStageTree in secondStageTree.ThirdStageTree)
                    {
                        thirdStageTree.FourthStageTree = new List<NewEquipmentTreeDto>();

                        var equipmentsAear = equipments
                            .OrderBy(x => x.EQUIPMENT_CLASS)
                            .ThenBy(x => x.EQUIPMENT_CODE)
                            .Where(x => x.UNIT_ID == thirdStageTree.PGROUP_ID)
                            .Where(x => x.VEST_PIPELINE is null)
                            .Where(x => x.IS_HIDE == "0" || x.IS_HIDE == null);

                        foreach (var equipment in equipmentsAear
                            .OrderBy(x => x.EQUIPMENT_CLASS)
                            .ThenBy(x => x.EQUIPMENT_CODE)
                            .Where(x => x.UNIT_ID == thirdStageTree.PGROUP_ID)
                            .Where(x => x.VEST_PIPELINE is null))
                        {
                            var fourTree = new NewEquipmentTreeDto()
                            {
                                EQUIPMENT_ID = equipment.EQUIPMENT_ID,
                                EQUIPMENT_CODE = equipment.EQUIPMENT_CODE,
                                EQUIPMENT_STATE = equipment.EQUIPMENT_STATE,
                                IS_HIDE = equipment.IS_HIDE,
                                EQUIPMENT_CLASS = equipmentContext.ExchangeEquipmentClass(equipment.EQUIPMENT_CLASS, equipment.EQUIPMENT_TYPE),
                                FifthStageTree = new List<NewEquipmentTreeDto>()
                            };

                            if (equipments.Count(x => x.VEST_PIPELINE == equipment.EQUIPMENT_ID) > 0)
                            {
                                var specialEquipments = equipments.Where(x => x.VEST_PIPELINE == equipment.EQUIPMENT_ID);
                                foreach (var specialEquipment in specialEquipments)
                                {
                                    fourTree.FifthStageTree.Add(new NewEquipmentTreeDto()
                                    {
                                        EQUIPMENT_ID = specialEquipment.EQUIPMENT_ID,
                                        EQUIPMENT_CODE = specialEquipment.EQUIPMENT_CODE,
                                        EQUIPMENT_STATE = specialEquipment.EQUIPMENT_STATE,
                                        IS_HIDE = specialEquipment.IS_HIDE,
                                        EQUIPMENT_CLASS = equipmentContext.ExchangeEquipmentClass(specialEquipment.EQUIPMENT_CLASS, equipment.EQUIPMENT_TYPE),
                                    });
                                }
                            }

                            thirdStageTree.FourthStageTree.Add(fourTree);
                        }

                        thirdStageTree.PGROUP_EQUIPMENT_AMOUNT = equipments.Where(x => x.IS_HIDE == "0" || x.IS_HIDE == null).Where(x => x.UNIT_ID == thirdStageTree.PGROUP_ID).Count();
                        thirdStageTree.PGROUP_INSTRUMENT_AMOUNT = equipments.Where(x => x.IS_HIDE == "0" || x.IS_HIDE == null).Where(x => x.UNIT_ID == thirdStageTree.PGROUP_ID).Count(x => x.EQUIPMENT_CLASS == "1");

                        secondStageTree.MGROUP_EQUIPMENT_AMOUNT += thirdStageTree.PGROUP_EQUIPMENT_AMOUNT;
                        secondStageTree.MGROUP_INSTRUMENT_AMOUNT += thirdStageTree.PGROUP_INSTRUMENT_AMOUNT;
                        secondStageTree.MGROUP_SUBSCRIBE_AMOUNT += thirdStageTree.PGROUP_SUBSCRIBE_AMOUNT;
                        secondStageTree.MGROUP_SCRAP_AMOUNT += thirdStageTree.PGROUP_SCRAP_AMOUNT;
                    }
                    tree.TOTAL_EQUIPMENT_AMOUNT += secondStageTree.MGROUP_EQUIPMENT_AMOUNT;
                    tree.TOTAL_INSTRUMENT_AMOUNT += secondStageTree.MGROUP_INSTRUMENT_AMOUNT;
                    tree.TOTAL_SUBSCRIBE_AMOUNT += secondStageTree.MGROUP_SUBSCRIBE_AMOUNT;
                    tree.TOTAL_SCRAP_AMOUNT += secondStageTree.MGROUP_SCRAP_AMOUNT;
                }
            }
            return trees;

        }


        public NewEquipClassTreeDto GetEquipmentClassList(string userNo, string hospitalId, string labId, string areaId)
        {
            int number = 0;
            AuthorityContext authorityContext = new AuthorityContext(_sqlSugarUow, _authorityService);
            authorityContext.SetUser(_user,labId,areaId);
            //专业组列表
            var groupList = authorityContext.GetAccessibleProfessionalGroups(labId, areaId)
                .Where(x => x.MGROUP_ID != null).ToList();
            var  MGroupIds = groupList.Select(x => x.MGROUP_ID).ToList();
            //设备类型列表
            var classList = _sqlSugarUow.Db.Queryable<SYS6_BASE_DATA>()
                .Where(p => p.CLASS_ID == "设备分类" && p.DATA_STATE == "1")
                .ToList();
            //管理专业组列表
            var mgroupList = _sqlSugarUow.Db.Queryable<SYS6_INSPECTION_MGROUP>()
                .Where(p => MGroupIds.Contains(p.MGROUP_ID) && p.MGROUP_STATE == "1")
                .ToList();
            groupList.RemoveAll(x => mgroupList.All(p => p.MGROUP_ID != x.MGROUP_ID));
            
            var pipeliningList = _sqlSugarUow.Db.Queryable<LIS6_PIPELINING_INSTRUMENT>().ToList();
            //基础数据
            var baseData = _sqlSugarUow.Db.Queryable<SYS6_BASE_DATA>().Where(p => p.DATA_STATE == "1" && (p.CLASS_ID == "设备分类" || p.CLASS_ID == "设备类型")).ToList();
            //一级树
            var ClassTree = new NewEquipClassTreeDto();

            //当前院区下所有报废停用信息
            var scrapStopList = _sqlSugarUow.Db.Queryable<EMS_SCRAP_INFO>()
                .InnerJoin<EMS_EQUIPMENT_INFO>((a, b) => a.EQUIPMENT_ID == b.EQUIPMENT_ID)
                .InnerJoin<SYS6_INSPECTION_PGROUP>((a, b, c) => b.UNIT_ID == c.PGROUP_ID && a.SCRAP_STATE == "1")
                .Where((a, b, c) => groupList.Select(i => i.PGROUP_ID).Contains(c.PGROUP_ID))
                .Select((a, b, c) => new
                {
                    b.EQUIPMENT_CLASS,
                    a.APPLY_STATE,
                    a.OPER_PERSON_ID,
                    a.EXAMINE_PERSON_ID,
                    a.APPROVE_PERSON_ID,
                    b.UNIT_ID,
                    c.MGROUP_ID,
                }).ToList();
            ClassTree.TOTAL_SCRAP_AMOUNT = scrapStopList.Count();
            //本人待处理
            var q = new List<ScrapStopListDto>();
            scrapStopList.ForEach(item =>
            {
                var dealPersonId = "";
                if (item.APPLY_STATE == "0" || item.APPLY_STATE == "4" || item.APPLY_STATE == "5")
                {
                    dealPersonId = item.OPER_PERSON_ID;
                }
                if (item.APPLY_STATE == "1")
                {
                    dealPersonId = item.EXAMINE_PERSON_ID;
                }
                if (item.APPLY_STATE == "2")
                {
                    dealPersonId = item.APPROVE_PERSON_ID;
                }
                q.Add(new ScrapStopListDto
                {
                    MGROUP_ID = item.UNIT_ID,
                    DEAL_PERSON_ID = dealPersonId,
                    STATE = item.APPLY_STATE
                });
            });
            var userPgroup = _sqlSugarUow.Db.Queryable<SYS6_USER>().Where(p => p.USER_NO == userNo).First().DEPT_CODE;
            var res = q.ToList();
            res.ForEach(item =>
            {
                if (item.DEAL_PERSON_ID == userNo)
                {
                    ClassTree.SELF_PEND += 1;
                }
            });
            ClassTree.SELF_PGROUP_PEND = res.Where(p => (p.STATE == "0" || p.STATE == "1" || p.STATE == "2") && p.MGROUP_ID == userPgroup).Count();

            var areaInfo = _sqlSugarUow.Db.Queryable<SYS6_INSPECTION_AREA>().Where(p => p.AREA_ID == areaId).First();
            if (areaInfo == null)
            {
                throw new BizException("找不到该院区信息");
            }
            ClassTree.AREA_ID = areaInfo.AREA_ID;
            ClassTree.AREA_NAME = areaInfo.AREA_NAME;
            //当前院区下所有设备
            var equipmentList = _equipmentDocService.GetEquipmentsBycondition(false,labId, areaId, null, null, "1", null, null, null)
                .Where((a, b) => a.IS_HIDE == null || a.IS_HIDE == "0")
                .Select((a, b) => new
                {
                    a.EQUIPMENT_ID,
                    a.EQUIPMENT_CODE,
                    a.EQUIPMENT_STATE,
                    a.EQUIPMENT_CLASS,
                    a.EQUIPMENT_TYPE,
                    a.INSTRUMENT_ID,
                    a.VEST_PIPELINE,
                    a.DEPT_SECTION_NO,
                    a.IS_HIDE,
                    MGROUP_ID = groupList.Where(p => p.PGROUP_ID == a.UNIT_ID).Select(i => i.MGROUP_ID).First(),
                    PGROUP_ID = a.UNIT_ID,
                })
                .ToList();
            ClassTree.TOTAL_EQUIPMENT_AMOUNT = equipmentList.Count;
            ClassTree.TOTAL_INSTRUMENT_AMOUNT = equipmentList.Where(p => p.EQUIPMENT_CLASS == "1").Count();

            //二级树
            var secondStage = new List<NewEquipClassTree>();
            classList.ForEach(item =>
            {
                //三级树
                var thirdStage = new List<NewClassMgroupTree>();
                mgroupList.ForEach(i =>
                {
                    //四级树
                    var fourthStage = new List<NewClassPgroupTree>();
                    var pgroupList = groupList.Where(p => p.MGROUP_ID == i.MGROUP_ID).ToList();
                    pgroupList.ForEach(m =>
                    {
                        //五级树
                        var fifthStage = new List<NewClassEquipmentTree>();
                        var eList = equipmentList.Where(p => p.PGROUP_ID == m.PGROUP_ID && p.EQUIPMENT_CLASS == item.DATA_ID && p.VEST_PIPELINE == null).OrderBy(i => i.EQUIPMENT_CLASS).ThenBy(i => i.EQUIPMENT_CODE).ToList();
                        eList.ForEach(n =>
                        {
                            //六级树
                            var sixthStage = new List<NewClassEquipmentTree>();
                            var pipeliningInfo = equipmentList.Where(p => p.VEST_PIPELINE == n.EQUIPMENT_ID).ToList();
                            pipeliningInfo.ForEach(o =>
                            {
                                var equipmentClass = baseData.Where(p => p.DATA_ID == o.EQUIPMENT_CLASS && p.CLASS_ID == "设备分类").FirstOrDefault()?.DATA_CNAME;
                                if (equipmentClass == "常规检测设备")
                                {
                                    if (o.EQUIPMENT_TYPE.IsNotNullOrEmpty())
                                    {
                                        equipmentClass = baseData.Where(p => p.DATA_ID == o.EQUIPMENT_TYPE && p.CLASS_ID == "设备类型").FirstOrDefault()?.DATA_CNAME;
                                    }
                                    else
                                    {
                                        equipmentClass = "检测仪器";
                                    }
                                }
                                sixthStage.Add(new NewClassEquipmentTree
                                {
                                    Num = ++number,
                                    EQUIPMENT_ID = o.EQUIPMENT_ID,
                                    EQUIPMENT_CODE = o.EQUIPMENT_CODE, //EQUIPMENT_CODE = o.EQUIPMENT_CODE + (n.DEPT_SECTION_NO.IsNullOrEmpty() ? "" : $"({o.DEPT_SECTION_NO})"),//树上设备节点加上科室编号
                                    EQUIPMENT_STATE = o.EQUIPMENT_STATE,
                                    IS_HIDE = o.IS_HIDE,
                                    EQUIPMENT_CLASS = equipmentClass
                                });
                            });
                            var equipmentClass = baseData.Where(p => p.DATA_ID == n.EQUIPMENT_CLASS && p.CLASS_ID == "设备分类").FirstOrDefault()?.DATA_CNAME;
                            if (equipmentClass == "常规检测设备")
                            {
                                if (n.EQUIPMENT_TYPE.IsNotNullOrEmpty())
                                {
                                    equipmentClass = baseData.Where(p => p.DATA_ID == n.EQUIPMENT_TYPE && p.CLASS_ID == "设备类型").FirstOrDefault()?.DATA_CNAME;
                                }
                                else
                                {
                                    equipmentClass = "检测仪器";
                                }
                            }
                            if (pipeliningList.Select(i => i.INSTRUMENT_ID).Contains(n.INSTRUMENT_ID) == false)
                            {
                                fifthStage.Add(new NewClassEquipmentTree
                                {
                                    Num = ++number,
                                    EQUIPMENT_ID = n.EQUIPMENT_ID,
                                    EQUIPMENT_CODE = n.EQUIPMENT_CODE, //EQUIPMENT_CODE = n.EQUIPMENT_CODE + (n.DEPT_SECTION_NO.IsNullOrEmpty() ? "" : $"({n.DEPT_SECTION_NO})"),//树上设备节点加上科室编号
                                    EQUIPMENT_CLASS = equipmentClass,
                                    IS_HIDE = n.IS_HIDE,
                                    SixthStageTree = sixthStage.OrderBy(p => p.EQUIPMENT_CODE).ToList()
                                });
                            }
                        });
                        fourthStage.Add(new NewClassPgroupTree
                        {
                            Num = ++number,
                            PGROUP_ID = m.PGROUP_ID,
                            PGROUP_NAME = m.PGROUP_NAME,
                            PGROUP_EQUIPMENT_AMOUNT = equipmentList.Where(p => p.PGROUP_ID == m.PGROUP_ID && p.EQUIPMENT_CLASS == item.DATA_ID).Count(),
                            PGROUP_SCRAP_AMOUNT = scrapStopList.Where(p => p.EQUIPMENT_CLASS == item.DATA_ID && p.MGROUP_ID == i.MGROUP_ID && p.UNIT_ID == m.PGROUP_ID).Count(),
                            PGROUP_INSTRUMENT_AMOUNT = equipmentList.Where(p => p.PGROUP_ID == m.PGROUP_ID && p.EQUIPMENT_CLASS == item.DATA_ID && p.EQUIPMENT_CLASS == "1").Count(),
                            FifthStageTree = fifthStage.OrderBy(p => p.EQUIPMENT_CLASS == "流水线").ToList()
                        }); ;
                    });
                    thirdStage.Add(new NewClassMgroupTree
                    {
                        Num = ++number,
                        MGROUP_ID = i.MGROUP_ID,
                        MGROUP_NAME = i.MGROUP_NAME,
                        MGROUP_EQUIPMENT_AMOUNT = equipmentList.Where(p => p.MGROUP_ID == i.MGROUP_ID && p.EQUIPMENT_CLASS == item.DATA_ID).Count(),
                        MGROUP_SCRAP_AMOUNT = scrapStopList.Where(p => p.EQUIPMENT_CLASS == item.DATA_ID && p.MGROUP_ID == i.MGROUP_ID).Count(),
                        MGROUP_INSTRUMENT_AMOUNT = equipmentList.Where(p => p.MGROUP_ID == i.MGROUP_ID && p.EQUIPMENT_CLASS == item.DATA_ID && p.EQUIPMENT_CLASS == "1").Count(),
                        FourthStageTree = fourthStage
                    });
                });
                secondStage.Add(new NewEquipClassTree
                {
                    Num = ++number,
                    EQUIPMENT_CLASS = item.DATA_ID,
                    EQUIPMENT_CLASS_NAME = item.DATA_CNAME == "常规检测设备" ? "检测设备/流水线" : item.DATA_CNAME,
                    EQUIPCLASS_EQUIPMENT_AMOUNT = equipmentList.Where(p => p.EQUIPMENT_CLASS == item.DATA_ID).Count(),
                    EQUIPCLASS_SCRAP_AMOUNT = scrapStopList.Where(p => p.EQUIPMENT_CLASS == item.DATA_ID).Count(),
                    EQUIPCLASS_INSTRUMENT_AMOUNT = equipmentList.Where(p => p.EQUIPMENT_CLASS == "1").Count(),
                    ThirdStageTree = thirdStage
                });
            });
            ClassTree.SecondStageTree = secondStage;
            return ClassTree;
        }

    }
}

