﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.Entities.FileTemplate
{
    public class EquipmentInfo
    {
        public int NO { get; set; } = 1;
        public string EQUIPMENT_ID { get; set; } = "";
        public string EQUIPMENT_STATE { get; set; } = "";
        public string EQUIPMENT_NAME { get; set; } = "";
        public string EQUIPMENT_CODE { get; set; } = "";
        public string EQUIPMENT_CLASS { get; set; } = "";
        public string LAB_NAME { get; set; } = "";

        public DateTime? INSTALL_DATE { get; set; }
        public DateTime? NEXT_MAINTAIN_DATE { get; set; }
        public DateTime? NEXT_CORRECT_DATE { get; set; }
        public DateTime? NEXT_COMPARISON_DATE { get; set; }
        public DateTime? NEXT_VERIFICATION_DATE { get; set; }

    }
}
