﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using H.Utility.SqlSugarInfra;
using SqlSugar;
using SqlSugar.DbConvert;

namespace XH.H82.Models.Entities.THS
{
    /// <summary>
    /// 设备检测点监测记录表
    /// </summary>
    [DBOwner("XH_DATA")]
    [Table("THS_MONITOR_RESULT")]
    [SugarTable("THS_MONITOR_RESULT")]
    public class THS_MONITOR_RESULT
    {
        /// <summary>
        /// 监测记录ID
        /// </summary>
        [Key]
        [SugarColumn(IsPrimaryKey = true)]
        public string? MONITOR_ID { get; set; }
        /// <summary>
        /// 监测指标ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string? ITEM_ID { get; set; }
        /// <summary>
        /// 监测值
        /// </summary>
        public string? ITEM_VALUE { get; set; }

        /// <summary>
        /// 判断状态L偏低H偏高Z正常
        /// </summary>
        public string? ITEM_STATUS { get; set; }
        /// <summary>
        /// 状态1正常2异常
        /// </summary>
        public string? ITEM_STATE { get; set; }
        /// <summary>
        /// 首次登记人
        /// </summary>
        public string? FIRST_RPERSON { get; set; }
        /// <summary>
        /// 首次登记时间
        /// </summary>
        public DateTime? FIRST_RTIME { get; set; }
        /// <summary>
        /// 最后修改人员
        /// </summary>
        public string? LAST_MPERSON { get; set; }
        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime? LAST_MTIME { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string? REMARK { get; set; }
        /// <summary>
        /// 报警范围
        /// </summary>
        public string? ALARM_RANGE { get; set; }
        /// <summary>
        /// 报警规则ID
        /// </summary>
        public string? RULE_ID { get; set; }
        /// <summary>
        /// 图表展示状态(1正常2异常)
        /// </summary>
        public string? CHART_STATE { get; set; }

        [SugarColumn(SqlParameterDbType = typeof(NClobPropertyConvert))]
        public string? ITEM_VALUE_JSON { get; set; } = null;
    }
}
