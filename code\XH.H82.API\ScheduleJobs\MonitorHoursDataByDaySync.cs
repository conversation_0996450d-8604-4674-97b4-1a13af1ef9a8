﻿using System.Timers;
using EasyCaching.Core;
using H.BASE.SqlSugarInfra.Uow;
using Microsoft.Extensions.Caching.Memory;
using Serilog;
using XH.H82.IServices.IoTDevice;
using XH.H82.Models.SugarDbContext;
using Timer = System.Timers.Timer;

namespace XH.H82.API.ScheduleJobs;

/// <summary>
/// 检测数据日统计服务
/// </summary>
public class MonitorHoursDataByDaySync :BackgroundService
{
    private readonly IIoTDeviceService _deviceService;
    private readonly IMemoryCache _cache;
    private Timer _timer;

    public MonitorHoursDataByDaySync(IIoTDeviceService deviceService,  IMemoryCache cache)
    {
        _deviceService = deviceService;
        _cache = cache;
        _timer = new Timer(1000 * 60 * 30)
        {
            AutoReset = true,
            Enabled = true
        };
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken) 
    {
        OnTimedEvent(null, null);
        _timer.Elapsed += OnTimedEvent;
        _timer.Start();
    }
    private void OnTimedEvent(object? source, ElapsedEventArgs e)
    {
        Log.Information("统计的任务开始执行");
        Task.Factory.StartNew( () =>
        {
            try
            {
                var countDate = DateTime.Now.Date.Add(TimeSpan.FromDays(-1));
                if (_cache.TryGetValue("MonitorHoursDataByDaySync", out DateTime recordDate))
                {
                    if (countDate.Date != recordDate.Date)
                    {
                        Log.Information($"{countDate.Date}获取监控数据开始");
                        _deviceService.ConutEquipmentMonitoringItemsDay(countDate);
                        Log.Information($"{countDate.Date}统计监控数据结束！");
                    }
                }
                else
                {
                    var timespan = DateTime.Now.AddDays(1).Date - DateTime.Now;
                    _cache.Set("MonitorHoursDataByDaySync", countDate.Date, timespan);
                    Log.Information($"{countDate.Date}获取监控数据开始");
                    _deviceService.ConutEquipmentMonitoringItemsDay(countDate);
                    Log.Information($"{countDate.Date}统计监控数据结束！");
                }
            }
            catch (Exception ex)
            {
                Log.Error($"统计监控数据结束失败：{ex}");
            }
        });
    }
}