﻿using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using XH.H82.Models.Entities;
using XH.H82.Models.SugarDbContext;

namespace XH.H82.Services.EquipmentEnabled
{
    public class EquipmentEnabledContext
    {
        private readonly ISqlSugarUow<SugarDbContext_Master> _dbContext;
        public EMS_EQUIPMENT_INFO _equipment = new();

        public EquipmentEnabledContext(ISqlSugarUow<SugarDbContext_Master> dbcontext) =>
            _dbContext = dbcontext;

        public void Injection(string id)
        {
            _equipment = _dbContext
                .Db.Queryable<EMS_EQUIPMENT_INFO>()
                .Where(x => id == x.EQUIPMENT_ID)
                .Includes(x => x.eMS_START_STOP)
                .First();

            if (_equipment is null)
            {
                throw new BizException("当前设备以被删除，请刷新页面");
            }
        }

        public void Injection(EMS_EQUIPMENT_INFO equipment)
        {
            if (_equipment is null)
            {
                throw new BizException("当前设备以被删除，请刷新页面");
            }
            if (equipment.eMS_START_STOP is null)
            {
                equipment.eMS_START_STOP = new List<EMS_START_STOP>();
            }
            if (equipment.eMS_START_STOP.Count() == 0)
            {
                equipment.eMS_START_STOP = _dbContext
                    .Db.Queryable<EMS_START_STOP>()
                    .Where(x => x.EQUIPMENT_ID == equipment.EQUIPMENT_ID)
                    .ToList();
                if (equipment.eMS_START_STOP is null)
                {
                    equipment.eMS_START_STOP = new();
                }
            }
            _equipment = equipment;
        }

        /// <summary>
        /// 初次启用设备
        /// </summary>
        private void FirstEnabled(string userName, DateTime startDate)
        {
            _equipment.ENABLE_TIME = startDate;
            _equipment.EQUIPMENT_STATE =
                startDate <= DateTime.Now ? "1" : _equipment.EQUIPMENT_STATE;
            _equipment.LAST_MTIME = DateTime.Now;
            _equipment.LAST_MPERSON = userName;
            _dbContext
                .Db.Updateable(_equipment)
                .IgnoreColumns(ignoreAllNullColumns: true)
                .ExecuteCommand();

            var firstRecord = _equipment
                .eMS_START_STOP.Where(x => x.START_CAUSE == "首次启用")
                .FirstOrDefault();
            if (firstRecord is null)
            {
                AddStartRecord(userName, startDate, "首次启用");
            }
            else
            {
                UpdateFirstRecord(userName, startDate, "首次启用");
            }
        }

        /// <summary>
        /// 再次启用设备
        /// </summary>
        private void ReEnabled(string userName, DateTime startDate, string? reason)
        {
            var lastStop = _equipment
                .eMS_START_STOP.OrderByDescending(x => x.OPER_TIME)
                .Where(x => x.START_STOP_TYPE == "0")
                .FirstOrDefault();
            var lastStart = _equipment
                .eMS_START_STOP.OrderByDescending(x => x.OPER_TIME)
                .Where(x => x.START_STOP_TYPE == "1")
                .FirstOrDefault();
            if (_equipment.EQUIPMENT_STATE == "0")
            {
                _equipment.EQUIPMENT_STATE =
                    startDate <= DateTime.Now ? "1" : _equipment.EQUIPMENT_STATE;
            }
            else
            {
                if (lastStop is not null)
                {
                    if (startDate > lastStop.START_DATE)
                    {
                        _equipment.ENABLE_TIME = lastStart.START_DATE;
                    }
                }
            }

            _equipment.LAST_MTIME = DateTime.Now;
            _equipment.LAST_MPERSON = userName;
            _dbContext
                .Db.Updateable(_equipment)
                .IgnoreColumns(ignoreAllNullColumns: true)
                .ExecuteCommand();
            var firstRecord = _equipment
                .eMS_START_STOP.OrderByDescending(x => x.OPER_TIME)
                .Where(x => x.START_STOP_TYPE == "1")
                .Where(x => x.START_CAUSE != "首次启用")
                .FirstOrDefault();
            if (firstRecord is null)
            {
                AddStartRecord(userName, startDate, reason);
            }
            else
            {
                UpdateFirstRecord(userName, startDate, reason);
            }
        }

        /// <summary>
        /// 启用设备
        /// </summary>
        public void SaveEnabled(string userName, DateTime startDate)
        {
            var lastRecord = _equipment
                .eMS_START_STOP.Where(x => x.START_CAUSE == "首次启用")
                .FirstOrDefault();
            //没有待启用记录
            if (lastRecord is null)
            {
                FirstEnabled(userName, startDate);
            }
            else
            {
                lastRecord.LAST_MTIME = DateTime.Now;
                lastRecord.START_DATE = startDate;
                lastRecord.LAST_MPERSON = userName;
                _dbContext
                    .Db.Updateable(lastRecord)
                    .IgnoreColumns(ignoreAllNullColumns: true)
                    .ExecuteCommand();
            }
        }

        /// <summary>
        /// 启用设备
        /// </summary>
        public void Enabled(string userName, DateTime startDate, string? reason)
        {
            var lastRecord = _equipment
                .eMS_START_STOP.OrderByDescending(x => x.OPER_TIME)
                .Where(x => x.START_STOP_TYPE == "1")
                .FirstOrDefault();

            //没有待启用记录
            if (lastRecord is null)
            {
                FirstEnabled(userName, startDate);
            }
            else
            {
                if (lastRecord.START_STOP_STATE == "0")
                {
                    //最后一条启用记录是首次启用
                    if (lastRecord.START_CAUSE == "首次启用")
                    {
                        ReEnabled(userName, startDate, reason);
                    }
                    else //最后一条启用记录不是首次启用
                    {
                        FirstEnabled(userName, startDate);
                    }
                }
                else
                {
                    //最后一条启用记录是首次启用
                    if (lastRecord.START_CAUSE == "首次启用")
                    {
                        FirstEnabled(userName, startDate);
                    }
                    else //最后一条启用记录不是首次启用
                    {
                        ReEnabled(userName, startDate, reason);
                    }
                }
            }
        }

        private void AddStartRecord(string userName, DateTime startDate, string? reason)
        {
            if (reason == "首次启用")
            {
                var firstStart = new EMS_START_STOP()
                {
                    START_STOP_ID = IDGenHelper.CreateGuid().ToString(),
                    HOSPITAL_ID = _equipment.HOSPITAL_ID,
                    EQUIPMENT_ID = _equipment.EQUIPMENT_ID,
                    START_DATE = startDate,
                    OPER_PERSON = userName,
                    OPER_TIME = DateTime.Now,
                    START_CAUSE = "首次启用",
                    START_STOP_STATE = startDate <= DateTime.Now ? "0" : "1",
                    FIRST_RPERSON = userName,
                    FIRST_RTIME = DateTime.Now,
                    LAST_MPERSON = userName,
                    LAST_MTIME = DateTime.Now,
                    START_STOP_TYPE = "1",
                };
                _dbContext.Db.Insertable(firstStart).ExecuteCommand();
            }
            else
            {
                var firstStart = new EMS_START_STOP()
                {
                    START_STOP_ID = IDGenHelper.CreateGuid().ToString(),
                    HOSPITAL_ID = _equipment.HOSPITAL_ID,
                    EQUIPMENT_ID = _equipment.EQUIPMENT_ID,
                    START_DATE = startDate,
                    OPER_PERSON = userName,
                    OPER_TIME = DateTime.Now,
                    START_CAUSE = reason,
                    START_STOP_STATE = startDate <= DateTime.Now ? "0" : "1",
                    FIRST_RPERSON = userName,
                    FIRST_RTIME = DateTime.Now,
                    LAST_MPERSON = userName,
                    LAST_MTIME = DateTime.Now,
                    START_STOP_TYPE = "1",
                };
                _dbContext.Db.Insertable(firstStart).ExecuteCommand();
            }
        }

        private void UpdateFirstRecord(string userName, DateTime startDate, string reson = "")
        {
            var lastRecord = _equipment
                .eMS_START_STOP.Where(x => x.START_STOP_TYPE == "1")
                .OrderByDescending(x => x.OPER_TIME)
                .FirstOrDefault();
            lastRecord.LAST_MTIME = DateTime.Now;
            lastRecord.START_DATE = startDate;
            lastRecord.LAST_MPERSON = userName;
            lastRecord.START_CAUSE = reson;
            _dbContext.Db.Updateable(lastRecord).ExecuteCommand();
        }
    }
}
