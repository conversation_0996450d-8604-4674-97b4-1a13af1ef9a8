using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using Microsoft.AspNetCore.Http;
using XH.H82.Base.Tree;
using XH.H82.Models.DeviceRelevantInformation;
using XH.H82.Models.Entities;
using XH.H82.Models.Entities.Common;
using XH.H82.Models.Smbl;
using XH.H82.Models.Smbl.Dto;
using XH.H82.Models.SugarDbContext;
using XH.LAB.UTILS.Interface;
using XH.LAB.UTILS.Models;

namespace XH.H82.Services.DeviceDataRefresh;

public class EquipmentTreeContext
{
    #region 缓存数据
    private List<EMS_EQUIPMENT_INFO> _equipments = new();

    private List<EMS_SUBSCRIBE_INFO> _subscribes = new();

    private List<EMS_SCRAP_INFO> _scraps = new();
    
    #endregion
    
    private readonly ISqlSugarUow<SugarDbContext_Master> _dbContext;
    private readonly IAuthorityService2 _authorityService;
    private readonly IHttpContextAccessor _httpContext;
    private AuthorityContext _authority;
    private EquipmentContext _equipmentContext;
    public EquipmentTreeContext(ISqlSugarUow<SugarDbContext_Master> dbContext, IAuthorityService2 authorityService, IHttpContextAccessor httpContext)
    {
        _dbContext = dbContext;
        _authorityService = authorityService;
        _httpContext = httpContext;
        _authority = new AuthorityContext(_dbContext, _authorityService);
        _equipmentContext = new EquipmentContext(_dbContext);
    }



    /// <summary>
    /// 设备生安类型树
    /// </summary>
    /// <param name="tree"></param>
    /// <param name="inlet"></param>
    /// <returns></returns>
    public List<ITreeNode> SmblEquipmentsClassTree((RootNode rootNode, List<ITreeNode> nodes) tree, SmblInletEnum inlet ,string equipmentCode = null ,bool  onlySmbl = false,bool  showHide = false)
    {
        switch(inlet)
        {
           case SmblInletEnum.Hospital:
               AsEquipmentContext(tree.nodes.FirstOrDefault(x => x.NODE_TYPE == TreeNodeTypeEnum.HOSPITAL)?.SOURCE_ID,
                   null, null, equipmentCode, onlySmbl, showHide);
               break;
           case SmblInletEnum.RecordLab :
               var smblLab = _dbContext.Db.Queryable<SMBL_LAB>()
                   .Where(x => x.SMBL_LAB_STATE == "1")
                   .First(x=>x.SMBL_LAB_ID == tree.nodes.FirstOrDefault(x => x.NODE_TYPE == TreeNodeTypeEnum.SMBLLAB).SOURCE_ID);
               if (smblLab is null)
               {
                   AsEquipmentContext(null, null,
                       null, equipmentCode, onlySmbl, showHide);
               }
               else
               {
                   AsEquipmentContext(null, null,
                       null, equipmentCode, onlySmbl, showHide,null,new List<string>(){smblLab.SMBL_LAB_ID});
               }
               
               break;
           case SmblInletEnum.Lab : 
               var smblLabs = _dbContext.Db.Queryable<SMBL_LAB>()
                   .Where(x => x.SMBL_LAB_STATE == "1")
                   .Where(x=>x.LAB_ID == tree.nodes.FirstOrDefault(x => x.NODE_TYPE == TreeNodeTypeEnum.LAB).SOURCE_ID)
                   .ToList();
                AsEquipmentContext(null, null,
                    null, equipmentCode, onlySmbl, showHide,null,smblLabs.Select(x=>x.SMBL_LAB_ID).ToList());
               break;
        };
        BuildSmblEquipmentClassByContext(tree, inlet);
        return tree.rootNode.CHILDREN;
    }
    
    /// <summary>
    /// 仪器生安类型树
    /// </summary>
    /// <param name="tree"></param>
    /// <param name="inlet"></param>
    /// <returns></returns>
    public List<ITreeNode> SmblInstrumentsClassTree((RootNode rootNode, List<ITreeNode> nodes) tree, SmblInletEnum inlet ,string equipmentCode = null ,bool  onlySmbl = false,bool  showHide = false)
    {
        switch(inlet)
        {
            case SmblInletEnum.Hospital:
                AsInstrumentContext(tree.nodes.FirstOrDefault(x => x.NODE_TYPE == TreeNodeTypeEnum.HOSPITAL)?.SOURCE_ID,
                    null, null, equipmentCode, onlySmbl, showHide);
                break;
            case SmblInletEnum.RecordLab :
                var smblLab = _dbContext.Db.Queryable<SMBL_LAB>()
                    .Where(x => x.SMBL_LAB_STATE == "1")
                    .First(x=>x.SMBL_LAB_ID == tree.nodes.FirstOrDefault(x => x.NODE_TYPE == TreeNodeTypeEnum.SMBLLAB).SOURCE_ID);
                if (smblLab is null)
                {
                    AsInstrumentContext(null, null,
                        null, equipmentCode, onlySmbl, showHide);
                }
                else
                {
                    AsInstrumentContext(null, null,
                        null, equipmentCode, onlySmbl, showHide, new List<string>(){smblLab.SMBL_LAB_ID});
                }
               
                break;
            case SmblInletEnum.Lab : 
                 var smblLabs = _dbContext.Db.Queryable<SMBL_LAB>()
                   .Where(x => x.SMBL_LAB_STATE == "1")
                   .Where(x=>x.LAB_ID == tree.nodes.FirstOrDefault(x => x.NODE_TYPE == TreeNodeTypeEnum.LAB).SOURCE_ID)
                   .ToList();
                 AsInstrumentContext(null, null,
                     null, equipmentCode, onlySmbl, showHide,smblLabs.Select(x=>x.SMBL_LAB_ID).ToList());
                break;
        };
        BuildSmblEquipmentClassByContext(tree, inlet);
        return tree.rootNode.CHILDREN;
    }
    

    /// <summary>
    /// ISO 设备申购树
    /// </summary>
    /// <param name="tree"></param>
    /// <returns></returns>
    public List<ITreeNode> IsoSubscriptionTree((RootNode rootNode,List<ITreeNode> nodes) tree)
    {
        var pgroupNodes = tree.nodes.Where(x => x.NODE_TYPE is TreeNodeTypeEnum.PGROUP);
        var subscriptions =  GetSubscriptions(null, null, new());
        foreach (var pgroupNode in pgroupNodes)
        {
            pgroupNode.NUM = subscriptions.Count(x => pgroupNode.SOURCE_ID == x.MGROUP_ID);
        }
        
        return tree.rootNode.CHILDREN;
    }

    
    /// <summary>
    /// 生安 设备申购树
    /// </summary>
    /// <param name="tree"></param>
    /// <returns></returns>
    public List<ITreeNode> SmblSubscriptionTree((RootNode rootNode,List<ITreeNode> nodes) tree)
    {
        var smblLabNodes = tree.nodes.Where(x => x.NODE_TYPE is TreeNodeTypeEnum.SMBLLAB);
        var subscriptions =  GetSubscriptions(null, null, new());
        // #region faker
        // var smblLabs =  _dbContext.Db.Queryable<SMBL_LAB>()
        //     .Where(x=>x.PGROUP_SID!=null)
        //     .Where(x => x.SMBL_LAB_STATE == "1")
        //     .ToList();
        //
        // foreach (var smblLabNode in smblLabNodes)
        // {
        //     var smblLab = smblLabs.FirstOrDefault(x => x.SMBL_LAB_ID == smblLabNode.SOURCE_ID);
        //     if (smblLab is not null)
        //     {
        //         var pgids = smblLab.PGROUP_SID.Split(",");
        //         smblLabNode.NUM = subscriptions.Count(x => pgids.Contains(x.MGROUP_ID));
        //     }
        // }
        // #endregion
        #region faker
        foreach (var smblLabNode in smblLabNodes)
        {
          smblLabNode.NUM = subscriptions.Count(x => smblLabNode.SOURCE_ID == x.SMBL_LAB_ID);
        }
        #endregion
        
        return tree.rootNode.CHILDREN;
    }
    
    
    /// <summary>
    /// ISO 报废停用树
    /// </summary>
    /// <param name="tree"></param>
    /// <returns></returns>
    public List<ITreeNode> IsoScrapsOrStopTree((RootNode rootNode,List<ITreeNode> nodes) tree)
    {
        var pgroupNodes = tree.nodes.Where(x => x.NODE_TYPE is TreeNodeTypeEnum.PGROUP);
        GetScraps(null, new ());
        AsEquipmentContext(null, null, null ,null,false,true,_scraps.Select(x=>x.EQUIPMENT_ID).ToList());
       
        foreach (var pgroupNode in pgroupNodes)
        {
            var equipments = _equipments.Where(equipment => pgroupNode.SOURCE_ID == equipment.EQUIPMENT_ID)
                .Select(equipment => equipment.EQUIPMENT_ID);
            var num = _scraps.Count(scrap => equipments.Contains(scrap.EQUIPMENT_ID));
            pgroupNode.NUM = num;
        }
        return tree.rootNode.CHILDREN;
    }

    
    
    /// <summary>
    /// 生安 报废停用树
    /// </summary>
    /// <param name="tree"></param>
    /// <returns></returns>
    public List<ITreeNode> SmblScrapsOrStopTree((RootNode rootNode,List<ITreeNode> nodes) tree)
    {
        var smblLabNodes = tree.nodes.Where(x => x.NODE_TYPE is TreeNodeTypeEnum.SMBLLAB);
        GetScraps(null, new ());
        AsEquipmentContext(null, null, null , null,false,true,_scraps.Select(x=>x.EQUIPMENT_ID).ToList());
        #region faker
        
        foreach (var smblLabNode in smblLabNodes)
        {
            var equipments = _equipments.Where(equipment => smblLabNode.SOURCE_ID == equipment.SMBL_LAB_ID)
                .Select(equipment => equipment.EQUIPMENT_ID);
            var num = _scraps.Count(scrap => equipments.Contains(scrap.EQUIPMENT_ID));
            smblLabNode.NUM = num;
        }
        #endregion
        return tree.rootNode.CHILDREN;
    }


    /// <summary>
    /// 生安 报废停用生安设备类型树
    /// </summary>
    /// <param name="tree"></param>
    /// <param name="inlet"></param>
    /// <returns></returns>
    public List<ITreeNode> SmblScrapsOrStopClassTree((RootNode rootNode,List<ITreeNode> nodes) tree , SmblInletEnum inlet)
    {
        var classes = GetSmblEquipmentClassPull();
        var nodes = inlet switch
        {
            SmblInletEnum.Hospital=>  tree.nodes.Where(x=>x.NODE_TYPE == TreeNodeTypeEnum.HOSPITAL),
            SmblInletEnum.Lab=>  tree.nodes.Where(x=>x.NODE_TYPE == TreeNodeTypeEnum.LAB),
            SmblInletEnum.RecordLab=>  tree.nodes.Where(x=>x.NODE_TYPE == TreeNodeTypeEnum.SMBLLAB)
        };
        GetScraps(null, new ());
        switch(inlet)
        {
            case SmblInletEnum.Hospital:
                AsEquipmentContext(tree.nodes.FirstOrDefault(x => x.NODE_TYPE == TreeNodeTypeEnum.HOSPITAL)?.SOURCE_ID,
                    null, null,null,false,true,_scraps.Select(x=>x.EQUIPMENT_ID).ToList());
                break;
            case SmblInletEnum.RecordLab :
                var smblLab = _dbContext.Db.Queryable<SMBL_LAB>()
                    .Where(x => x.PGROUP_SID != null)
                    .Where(x => x.SMBL_LAB_STATE == "1")
                    .First(x=>x.SMBL_LAB_ID == tree.nodes.FirstOrDefault(x => x.NODE_TYPE == TreeNodeTypeEnum.SMBLLAB).SOURCE_ID);
                if (smblLab is null)
                {
                    AsEquipmentContext(null, null,
                        null,null,false,true,_scraps.Select(x=>x.EQUIPMENT_ID).ToList());
                }
                else
                {
                    AsEquipmentContext(null, null,
                        null,null,false,true,_scraps.Select(x=>x.EQUIPMENT_ID).ToList(),new List<string>(){smblLab.SMBL_LAB_ID});
                }
               
                break;
            case SmblInletEnum.Lab : 
                var smblLabs = _dbContext.Db.Queryable<SMBL_LAB>()
                    .Where(x => x.SMBL_LAB_STATE == "1")
                    .Where(x=>x.LAB_ID == tree.nodes.FirstOrDefault(x => x.NODE_TYPE == TreeNodeTypeEnum.LAB).SOURCE_ID)
                    .ToList();
                AsEquipmentContext(null, null,
                    null,null,false,true,_scraps.Select(x=>x.EQUIPMENT_ID).ToList(),smblLabs.Select(x=>x.SMBL_LAB_ID).ToList());
                break;
        };
        int num = tree.nodes.Max(x => x.NODE_NO)+1;
        #region faker
        foreach (var node in nodes.ToList())
        {
            node.CHILDREN.Clear();
            foreach (var classs in classes)
            {
                var classNode = _authority.AddEquipmentSmblClassNode(node, classs, false, ref num);
                tree.nodes.Add(classNode);
                var equipments = _equipments.Where(equipment => (classs.DATA_ID == "0"?  null : classs.DATA_ID ) == equipment.SMBL_CLASS && equipment.SMBL_FLAG =="1" )
                    .Select(equipment => equipment.EQUIPMENT_ID);
                classNode.NUM = _scraps.Count(scrap => equipments.Contains(scrap.EQUIPMENT_ID));
            }
        }
        #endregion
        return tree.rootNode.CHILDREN;
    }

    
    
    /// <summary>
    /// ISO工作计划树
    /// </summary>
    /// <param name="tree"></param>
    /// <returns></returns>
    public List<ITreeNode> IsoWorkPlanTree((RootNode rootNode,List<ITreeNode> nodes) tree)
    {
        var pgroupNodes = tree.nodes.Where(x => x.NODE_TYPE is TreeNodeTypeEnum.PGROUP);
        AsEquipmentContext(null, null, null);
        foreach (var pgroupNode in pgroupNodes)
        {
            pgroupNode.NUM = _equipments
                .Where(x=>pgroupNode.SOURCE_ID == x.UNIT_ID)
                .Where(x=>x.EQUIPMENT_STATE == "1" &&(x.IS_HIDE == null || x.IS_HIDE != "1"))
                .ToList().Count;
        }
        return tree.rootNode.CHILDREN;
    }
    
    
    /// <summary>
    ///  生安 工作计划树
    /// </summary>
    /// <param name="tree"></param>
    /// <returns></returns>
    public List<ITreeNode> SmblWorkPlanTree((RootNode rootNode,List<ITreeNode> nodes) tree)
    {
        var smblLabNodes = tree.nodes.Where(x => x.NODE_TYPE is TreeNodeTypeEnum.SMBLLAB);
        AsEquipmentContext(null, null, null);
        // #region faker
        // var smblLabs =  _dbContext.Db.Queryable<SMBL_LAB>()
        //     .Where(x=>x.PGROUP_SID!=null)
        //     .Where(x => x.SMBL_LAB_STATE == "1")
        //     .ToList();
        // foreach (var smblLabNode in smblLabNodes)
        // {
        //     var smblLab = smblLabs.FirstOrDefault(x => x.SMBL_LAB_ID == smblLabNode.SOURCE_ID);
        //     if (smblLab is not null)
        //     {
        //         var pgids = smblLab.PGROUP_SID.Split(",");
        //         smblLabNode.NUM = _equipments
        //             .Where(x=>pgids.Contains(x.UNIT_ID))
        //             .Where(x=>x.EQUIPMENT_STATE == "1" &&(x.IS_HIDE == null || x.IS_HIDE != "1"))
        //             .ToList().Count;
        //     }
        // }
        // #endregion
        #region faker
        
        foreach (var smblLabNode in smblLabNodes)
        {
            smblLabNode.NUM = _equipments
                .Where(x=>smblLabNode.SOURCE_ID == x.SMBL_LAB_ID)
                .Where(x=>x.EQUIPMENT_STATE == "1" &&(x.IS_HIDE == null || x.IS_HIDE != "1"))
                .ToList().Count;
        }
        #endregion
        
        return tree.rootNode.CHILDREN;
    }

    /// <summary>
    ///  生安 工作计生安设备类型划树
    /// </summary>
    /// <param name="tree"></param>
    /// <param name="inlet"></param>
    /// <returns></returns>
    public List<ITreeNode> SmblWorkPlanClassTree((RootNode rootNode,List<ITreeNode> nodes) tree ,SmblInletEnum inlet)
    {
        var classes = GetSmblEquipmentClassPull();
        var nodes = inlet switch
        {
            SmblInletEnum.Hospital=>  tree.nodes.Where(x=>x.NODE_TYPE == TreeNodeTypeEnum.HOSPITAL),
            SmblInletEnum.Lab=>  tree.nodes.Where(x=>x.NODE_TYPE == TreeNodeTypeEnum.LAB),
            SmblInletEnum.RecordLab=>  tree.nodes.Where(x=>x.NODE_TYPE == TreeNodeTypeEnum.SMBLLAB)
        };
        switch(inlet)
        {
            case SmblInletEnum.Hospital:
                AsEquipmentContext(tree.nodes.FirstOrDefault(x => x.NODE_TYPE == TreeNodeTypeEnum.HOSPITAL)?.SOURCE_ID,
                    null, null);
                break;
            case SmblInletEnum.RecordLab :
                var smblLab = _dbContext.Db.Queryable<SMBL_LAB>()
                    .Where(x => x.SMBL_LAB_STATE == "1")
                    .First(x=>x.SMBL_LAB_ID == tree.nodes.FirstOrDefault(x => x.NODE_TYPE == TreeNodeTypeEnum.SMBLLAB).SOURCE_ID);
                if (smblLab is null)
                {
                    AsEquipmentContext(null, null,
                        null);
                }
                else
                {
                    AsEquipmentContext(null, null,
                        null,null,false,false,null,smblLabs:new List<string>(){smblLab.SMBL_LAB_ID});
                }
               
                break;
            case SmblInletEnum.Lab : 
                var smblLabs = _dbContext.Db.Queryable<SMBL_LAB>()
                    .Where(x => x.SMBL_LAB_STATE == "1")
                    .Where(x=>x.LAB_ID == tree.nodes.FirstOrDefault(x => x.NODE_TYPE == TreeNodeTypeEnum.LAB).SOURCE_ID)
                    .ToList();
                 AsEquipmentContext(null, null,
                     null,null,false,false,null,smblLabs.Select(x=>x.SMBL_LAB_ID).ToList());
                break;
        };
        
        int num = tree.nodes.Max(x => x.NODE_NO)+1;
        foreach (var node in nodes.ToList())
        {
            node.CHILDREN.Clear();
            foreach (var classs in classes)
            {
                var classNode = _authority.AddEquipmentSmblClassNode(node, classs, false, ref num);
                tree.nodes.Add(classNode);
                classNode.NUM =  _equipments
                    .Where(x=>x.SMBL_CLASS == (classNode.SOURCE_ID == "0" ? null : classNode.SOURCE_ID) && x.SMBL_FLAG == "1")
                    .Where(x=>x.EQUIPMENT_STATE == "1" &&(x.IS_HIDE == null || x.IS_HIDE != "1"))
                    .ToList().Count;
            }
        }
        return tree.rootNode.CHILDREN;
    }

    
    /// <summary>
    /// ISO 设备档案树
    /// </summary>
    /// <param name="tree"></param>
    /// <param name="equipmentCode"></param>
    /// <param name="onlySmbl"></param>
    /// <param name="showHide"></param>
    /// <param name="hasClass"></param>
    /// <returns></returns>
    public List<ITreeNode> IsoEquipmentsTree((RootNode rootNode, List<ITreeNode> nodes) tree,string?  equipmentCode = null ,bool  onlySmbl = false,bool  showHide = false, bool hasClass = false)
    {
        AsEquipmentContext(null, null, null,equipmentCode,onlySmbl,showHide);
        BuildIsoEquipmentByContext(tree, hasClass);
        return tree.rootNode.CHILDREN;
    }


    /// <summary>
    /// 生安 设备档案树
    /// </summary>
    /// <param name="tree"></param>
    /// <param name="equipmentCode"></param>
    /// <param name="onlySmbl"></param>
    /// <param name="showHide"></param>
    /// <param name="hasClass"></param>
    /// <returns></returns>
    public List<ITreeNode> SmblEquipmentsTree((RootNode rootNode, List<ITreeNode> nodes) tree, string?  equipmentCode = null , bool  onlySmbl = false,  bool  showHide = false,  bool hasClass = false)
    {
        AsEquipmentContext(null, null, null,equipmentCode,onlySmbl,showHide);
        BuildSmblEquipmentByContext(tree, hasClass);
        return tree.rootNode.CHILDREN;
    }

    /// <summary>
    /// ISO 仪器设备树
    /// </summary>
    /// <param name="tree"></param>
    /// <param name="equipmentCode"></param>
    /// <param name="onlySmbl"></param>
    /// <param name="showHide"></param>
    /// <param name="hasClass"></param>
    /// <returns></returns>
    public List<ITreeNode> IsoInstrumentEquipmentsTree((RootNode rootNode, List<ITreeNode> nodes) tree,string?  equipmentCode = null , bool  onlySmbl = false, bool  showHide= false, bool hasClass = false)
    {
        AsInstrumentContext(null, null, null,equipmentCode,onlySmbl,showHide);
        BuildIsoEquipmentByContext(tree, hasClass);
        return tree.rootNode.CHILDREN;
    }


    /// <summary>
    ///  生安 仪器设备树
    /// </summary>
    /// <param name="tree"></param>
    /// <param name="equipmentCode"></param>
    /// <param name="onlySmbl"></param>
    /// <param name="showHide"></param>
    /// <param name="hasClass"></param>
    /// <returns></returns>
    public List<ITreeNode> SmblInstrumentEquipmentsTree((RootNode rootNode, List<ITreeNode> nodes) tree,string?  equipmentCode = null , bool onlySmbl = false, bool  showHide= false, bool hasClass = false)
    {
        AsInstrumentContext(null, null, null,equipmentCode,onlySmbl,showHide);
        BuildSmblEquipmentByContext(tree, hasClass);
        return tree.rootNode.CHILDREN;
    }


    /// <summary>
    /// 生安 分配生安设备树
    /// </summary>
    /// <param name="tree"></param>
    /// <returns></returns>
    public List<ITreeNode> DistributionEquipmentTree((RootNode rootNode, List<ITreeNode> nodes) tree)
    {
        AsEquipmentContext(null, null, null);
        var smblLabNodes = tree.nodes.Where(x => x.NODE_TYPE is TreeNodeTypeEnum.SMBLLAB);
        foreach (var smblLabNode in smblLabNodes)
        {
            smblLabNode.NUM = _equipments
                    .Where(x=>x.SMBL_LAB_ID == smblLabNode.SOURCE_ID)
                    .Where(x=>x.IS_HIDE == null || x.IS_HIDE != "1")
                    .ToList().Count;
        }
        return tree.rootNode.CHILDREN;
    }

    
    /// <summary>
    /// 根据当前上下文构建设备类型树节点
    /// </summary>
    /// <param name="tree"></param>
    /// <param name="hasClass"></param>
    private void BuildSmblEquipmentClassByContext((RootNode rootNode, List<ITreeNode> nodes) tree ,SmblInletEnum inlet)
    {
        var classes = GetSmblEquipmentClassPull();
        var nodes = inlet switch
        {
            SmblInletEnum.Hospital=>  tree.nodes.Where(x=>x.NODE_TYPE == TreeNodeTypeEnum.HOSPITAL),
            SmblInletEnum.Lab=>  tree.nodes.Where(x=>x.NODE_TYPE == TreeNodeTypeEnum.LAB),
            SmblInletEnum.RecordLab=>  tree.nodes.Where(x=>x.NODE_TYPE == TreeNodeTypeEnum.SMBLLAB)
        };
        
        int num = tree.nodes.Max(x => x.NODE_NO)+1;
        //TODO 后续直接采用设备生安字段绑定到树节点
        foreach (var node in nodes.ToList())
        {
            node.CHILDREN.Clear();
            foreach (var classs in classes)
            {
                var classNode = _authority.AddEquipmentSmblClassNode(node, classs, false, ref num);
                tree.nodes.Add(classNode);
                switch(inlet)
                {
                    case SmblInletEnum.Hospital:
                        InjectEquipmentNode(classNode,tree.nodes,x=>(classs.DATA_ID == "0" ? null : classs.DATA_ID) == x.SMBL_CLASS && x.HOSPITAL_ID == node.SOURCE_ID && x.SMBL_FLAG =="1");
                        break;
                    case SmblInletEnum.RecordLab :
                        InjectEquipmentNode(classNode,tree.nodes,x=>(classs.DATA_ID == "0" ? null : classs.DATA_ID) == x.SMBL_CLASS && x.SMBL_LAB_ID == node.SOURCE_ID&& x.SMBL_FLAG =="1");
               
                        break;
                    case SmblInletEnum.Lab : 
                        var smblLabs = _dbContext.Db.Queryable<SMBL_LAB>()
                            .Where(x => x.SMBL_LAB_STATE == "1")
                            .Where(x=>x.LAB_ID == node.SOURCE_ID)
                            .Select(x=>x.SMBL_LAB_ID)
                            .ToList()
                            .Distinct();
                        InjectEquipmentNode(classNode,tree.nodes,x=>(classs.DATA_ID == "0" ? null : classs.DATA_ID) == x.SMBL_CLASS && smblLabs.Contains(x.SMBL_LAB_ID) && x.SMBL_FLAG =="1");
                        break;
                };
                //InjectEquipmentNode(classNode,tree.nodes,x=>classs.DATA_ID == x.SMBL_CLASS);
            }
        }
        //InjectPipEquipmentNode(tree.nodes);
    }
    
    /// <summary>
    /// 根据当前上下文构建设备树节点
    /// </summary>
    /// <param name="tree"></param>
    /// <param name="hasClass"></param>
    private void BuildSmblEquipmentByContext((RootNode rootNode, List<ITreeNode> nodes) tree ,bool hasClass = false)
    {
        // #region faker
        // var smblLabNodes = tree.nodes.Where(x => x.NODE_TYPE is TreeNodeTypeEnum.SMBLLAB).ToList();
        // int num = tree.nodes.Max(x => x.NODE_NO)+1;
        // var ids =  smblLabNodes.Select(x => x.SOURCE_ID);
        // var smblLabs =  _dbContext.Db.Queryable<SMBL_LAB>()
        //     .Where(x => ids.Contains(x.SMBL_LAB_ID))
        //     .Where(x => x.PGROUP_SID != null)
        //     .Where(x => x.SMBL_LAB_STATE == "1")
        //     .ToList();
        // //TODO 后续直接采用设备生安字段绑定到树节点
        // if (hasClass)
        // {
        //     var classes =  _equipmentContext.GetEquipmentClasses()
        //         .Where(x=>x.CLASS_ID == "设备分类")
        //         .OrderBy(x=>x.DATA_SORT);
        //     foreach (var smblLabNode in smblLabNodes)
        //     {
        //         var smblLab = smblLabs.FirstOrDefault(x => x.SMBL_LAB_ID == smblLabNode.SOURCE_ID);
        //         foreach (var classs in classes)
        //         {
        //             var classNode =   _authority.AddEquipmentClassNode(smblLabNode,classs,true,ref num);
        //             tree.nodes.Add(classNode);
        //             InjectEquipmentNode(classNode,tree.nodes,x=>smblLab.PGROUP_SID.Contains(x.UNIT_ID) && x.EQUIPMENT_CLASS == classs.DATA_ID);
        //         }
        //     }
        // }
        // else
        // {
        //     foreach (var smblLabNode in smblLabNodes)
        //     {
        //         var smblLab = smblLabs.FirstOrDefault(x => x.SMBL_LAB_ID == smblLabNode.SOURCE_ID);
        //         InjectEquipmentNode(smblLabNode,tree.nodes,x=>smblLab.PGROUP_SID.Contains(x.UNIT_ID));
        //     }
        // }
        //
        //
        // #endregion
        
        var smblLabNodes = tree.nodes.Where(x => x.NODE_TYPE is TreeNodeTypeEnum.SMBLLAB).ToList();
        int num = tree.nodes.Max(x => x.NODE_NO)+1;
        if (hasClass)
        {
            var classes =  _equipmentContext.GetEquipmentClasses()
                .Where(x=>x.CLASS_ID == "设备分类")
                .OrderBy(x=>x.DATA_SORT);
            foreach (var smblLabNode in smblLabNodes)
            {
                foreach (var classs in classes)
                {
                    var classNode =   _authority.AddEquipmentClassNode(smblLabNode,classs,true,ref num);
                    tree.nodes.Add(classNode);
                    InjectEquipmentNode(classNode,tree.nodes,x=>smblLabNode.SOURCE_ID == x.SMBL_LAB_ID && x.EQUIPMENT_CLASS == classs.DATA_ID);
                }
            }
        }
        else
        {
            foreach (var smblLabNode in smblLabNodes)
            {
                InjectEquipmentNode(smblLabNode,tree.nodes,x=>smblLabNode.SOURCE_ID == x.SMBL_LAB_ID);
            }
        }
        //InjectPipEquipmentNode(tree.nodes);
    }
    
    /// <summary>
    /// 根据当前上下文构建设备树节点
    /// </summary>
    /// <param name="tree"></param>
    /// <param name="hasClass"></param>
    private void BuildIsoEquipmentByContext((RootNode rootNode, List<ITreeNode> nodes) tree ,bool hasClass = false)
    {
        var pgroupNodes = tree.nodes.Where(x => x.NODE_TYPE is TreeNodeTypeEnum.PGROUP).ToList();
        int num = tree.nodes.Max(x => x.NODE_NO)+1;
        if (hasClass)
        {
            var classes =  _equipmentContext.GetEquipmentClasses().Where(x=>x.CLASS_ID == "设备分类");
            foreach (var pgroupNode in pgroupNodes)
            {
                foreach (var classs in classes)
                {
                    var classNode =   _authority.AddEquipmentClassNode(pgroupNode,classs,true,ref num);
                    tree.nodes.Add(classNode);
                    InjectEquipmentNode(classNode,tree.nodes,x=>x.UNIT_ID == pgroupNode.SOURCE_ID && x.EQUIPMENT_CLASS == classs.DATA_ID);
                }
            }
        }
        else
        {
            foreach (var pgroupNode in pgroupNodes.OrderBy(x=>x.SOURCE_ID))
            {
                InjectEquipmentNode(pgroupNode,tree.nodes,x=>x.UNIT_ID == pgroupNode.SOURCE_ID );
            }
        }
        //InjectPipEquipmentNode(tree.nodes);
    }

    
    /// <summary>
    /// 构建设备节点
    /// </summary>
    /// <param name="node"></param>
    /// <param name="nodes"></param>
    /// <param name="expression"></param>
    private void InjectEquipmentNode(ITreeNode node, List<ITreeNode> nodes , Func<EMS_EQUIPMENT_INFO,bool> expression)
    {
        var num = nodes.Max(x => x.NODE_NO)+1;
        var equipments = _equipments
            //.Where(x=>x.VEST_PIPELINE == null)
            .Where(expression)
            .ToList();
        
        foreach (var equipment in equipments)
        {
           var equipmentNode =  _authority.AddEquipmentNode(node, equipment, false,ref num);
           equipmentNode.SOURCE = new
           { 
               equipment.EQUIPMENT_ID,
               equipment.EQUIPMENT_CODE,
               EQUIPMENT_STATE =_equipmentContext.ExchangeEquipmentState(equipment.EQUIPMENT_STATE),
               EQUIPMENT_CLASS = _equipmentContext.ExchangeEquipmentClass(equipment.EQUIPMENT_CLASS,equipment.EQUIPMENT_TYPE) ,
               equipment.INSTRUMENT_ID,
               equipment.VEST_PIPELINE,
               equipment.IS_HIDE, 
               IS_CNAS = false,
               IS_CONTROL = false,
               SMBL_FLAG = equipment.SMBL_FLAG == "1" ? "1":"0",
           };
           nodes.Add(equipmentNode);
        }
 
    }
    /// <summary>
    /// 构建附属于流水线设备节点的设备节点
    /// </summary>
    /// <param name="nodes"></param>
    private void InjectPipEquipmentNode(List<ITreeNode> nodes)
    {
        var num = nodes.Max(x => x.NODE_NO)+1;
        foreach (var equipment in _equipments.Where(x=>x.VEST_PIPELINE != null))
        {
            var masterEquipment = _equipments.FirstOrDefault(x => x.EQUIPMENT_ID == equipment.VEST_PIPELINE);
            if (masterEquipment is not null && nodes.Any(x=>x.SOURCE_ID == masterEquipment.EQUIPMENT_ID))
            {
                var masterNode = nodes.First(x => x.SOURCE_ID == equipment.VEST_PIPELINE);
                var equipmentNode =  _authority.AddEquipmentNode(masterNode, equipment, false,ref num);
                equipmentNode.SOURCE = new
                {
                    equipment.EQUIPMENT_ID,
                    equipment.EQUIPMENT_CODE,
                    EQUIPMENT_STATE =_equipmentContext.ExchangeEquipmentState(equipment.EQUIPMENT_STATE),
                    EQUIPMENT_CLASS = _equipmentContext.ExchangeEquipmentClass(equipment.EQUIPMENT_CLASS,equipment.EQUIPMENT_TYPE) ,
                    equipment.INSTRUMENT_ID,
                    equipment.VEST_PIPELINE,
                    equipment.IS_HIDE,
                    IS_CNAS = false,
                    Is_CONTROL = false
                };
                nodes.Add(equipmentNode);
            }
        }
    }

    /// <summary>
    /// 获取全部的申购信息
    /// </summary>
    /// <returns></returns>
    public List<EMS_SUBSCRIBE_INFO> GetSubscriptions(string? hosptalId , string? labId , List<string> equipmentIds)
    {
        if (!_subscribes.Any() )
        {
            var subs = _dbContext.Db.Queryable<EMS_SUBSCRIBE_INFO>()
                .WhereIF(equipmentIds.Any(),x=>equipmentIds.Contains(x.EQUIPMENT_ID))
                .WhereIF(hosptalId.IsNotNullOrEmpty(),x=>x.HOSPITAL_ID == hosptalId)
                .WhereIF(labId.IsNotNullOrEmpty(), x => x.LAB_ID == labId)
                .ToList();
            _subscribes.Clear();
            _subscribes.AddRange(subs);
        }
        return _subscribes;
    }

    /// <summary>
    /// 根据条件返回设备信息
    /// </summary>
    /// <param name="hosptalId"></param>
    /// <param name="labId"></param>
    /// <param name="pgroupIds"></param>
    /// <param name="equipmentCode"></param>
    /// <param name="onlySmbl"></param>
    /// <returns></returns>
    public List<EMS_EQUIPMENT_INFO> AsEquipmentContext(string? hosptalId , string? labId , List<string>? pgroupIds ,string?  equipmentCode = null , bool onlySmbl = false, bool showHide = false, List<string> equipmentIds = null , List<string>? smblLabs = null)
    {
        if (!_equipments.Any())
        {
            _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>()
                .Where(x=>x.EQUIPMENT_CODE != null)
                .Where(x=>x.UNIT_ID!=null)
                .WhereIF(smblLabs is not  null, x=> smblLabs!.Contains(x.SMBL_LAB_ID))
                .WhereIF(equipmentCode.IsNotNullOrEmpty(),x=> x.EQUIPMENT_CODE.ToLower().Contains(equipmentCode!.ToLower()))
                .WhereIF(onlySmbl,x=>x.SMBL_FLAG == "1")
                .WhereIF(!showHide,x=>x.IS_HIDE == null || x.IS_HIDE != "1")
                .WhereIF(equipmentIds is not null,x=>equipmentIds!.Contains(x.EQUIPMENT_ID))
                .WhereIF(hosptalId.IsNotNullOrEmpty(), x => x.HOSPITAL_ID == hosptalId)
                .WhereIF(labId.IsNotNullOrEmpty(), x => x.LAB_ID == labId)
                .WhereIF(pgroupIds is not  null, x => pgroupIds!.Contains(x.UNIT_ID))
                .OrderBy(x => new { x.EQUIPMENT_CLASS,x.EQUIPMENT_CODE})
                .ForEach(it => _equipments.Add(it), 300); //每次查询300条
        }
        

        return _equipments;
    }
    /// <summary>
    /// 返回仪器相关的设备信息
    /// </summary>
    /// <param name="hosptalId"></param>
    /// <param name="labId"></param>
    /// <param name="pgroupIds"></param>
    /// <param name="equipmentCode"></param>
    /// <param name="onlySmbl"></param>
    /// <param name="showHide"></param>
    /// <returns></returns>
    public List<EMS_EQUIPMENT_INFO> AsInstrumentContext(string? hosptalId , string? labId , List<string>? pgroupIds,string?  equipmentCode = null ,bool onlySmbl = false , bool showHide = false , List<string>? smblLabs = null)
    {
        if (!_equipments.Any())
        {
            _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>()
                .Where(x=>x.EQUIPMENT_CODE != null)
                .Where(x=>x.UNIT_ID!=null) 
                //仪器同步或有其他对照的设备信息
                .WhereIF(smblLabs is not  null, x=> smblLabs!.Contains(x.SMBL_LAB_ID))
                .WhereIF(equipmentCode.IsNotNullOrEmpty(),x=> x.EQUIPMENT_CODE.ToLower().Contains(equipmentCode!.ToLower()))
                .WhereIF(onlySmbl,x=>x.SMBL_FLAG == "1")
                .WhereIF(!showHide,x=>x.IS_HIDE == null || x.IS_HIDE != "1")
                .WhereIF(hosptalId.IsNotNullOrEmpty(), x => x.HOSPITAL_ID == hosptalId)
                .WhereIF(labId.IsNotNullOrEmpty(), x => x.LAB_ID == labId)
                .WhereIF(pgroupIds is not null, x => pgroupIds!.Contains(x.UNIT_ID))
                .OrderBy(x => new { x.EQUIPMENT_CLASS,x.EQUIPMENT_CODE})
                .ForEach(it => _equipments.Add(it), 300); //每次查询200条
        }
        _equipments.RemoveAll(x => x.INSTRUMENT_ID == null);
        return _equipments;
    }
    /// <summary>
    /// 查询设备报废停用信息
    /// </summary>
    /// <param name="hosptalId"></param>
    /// <param name="equipmentIds"></param>
    /// <returns></returns>
    public List<EMS_SCRAP_INFO> GetScraps(string? hosptalId,List<string> equipmentIds)
    {
        if (!_scraps.Any())
        {
            _dbContext.Db.Queryable<EMS_SCRAP_INFO>()
                .WhereIF(hosptalId.IsNotNullOrEmpty(), x => x.HOSPITAL_ID == hosptalId)
                .WhereIF(equipmentIds.Any(),x=>equipmentIds.Contains(x.EQUIPMENT_ID))
                .ForEach(it => _scraps.Add(it), 200); //每次查询200条
        }
        return _scraps;
    }

    /// <summary>
    /// 按DTO返回设备数据
    /// </summary>
    /// <param name="hosptalId"></param>
    /// <param name="labId"></param>
    /// <param name="pgroupIds"></param>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    public List<SmblEquipmentDto> GetActiveEquipments(string? hosptalId , string? labId , List<string> pgroupIds )
    {
        var result = new List<SmblEquipmentDto>();
            _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>()
                .Where(x=>x.UNIT_ID!=null)
                .Where(x=>x.EQUIPMENT_CODE!= null)
                .Where(x=>x.EQUIPMENT_NAME!= null)
                .WhereIF(hosptalId.IsNotNullOrEmpty(), x => x.HOSPITAL_ID == hosptalId)
                .WhereIF(labId.IsNotNullOrEmpty(), x => x.LAB_ID == labId)
                .WhereIF(pgroupIds.Any(), x => pgroupIds.Contains(x.UNIT_ID))
                .Select<SmblEquipmentDto>()
                .ForEach(it => result.Add(it), 300); //每次查询300条

            foreach (var item in result)
            {
                item.SMBL_CLASS = _equipmentContext.ExchangeEquipmentSmblClass(item.SMBL_CLASS);
                item.SMBL_LAB_NAME = _equipmentContext.ExchangeSmblLabName(item.SMBL_LAB_ID);
                item.IS_HIDE = item.IS_HIDE == "1" ? "1" : "0";
                item.SMBL_FLAG = item.SMBL_FLAG == "1" ? "1" : "0";
            }
            
        return result;
    }
    
    
    /// <summary>
    /// 查询生安设备类型
    /// </summary>
    /// <returns></returns>
    public List<SYS6_BASE_DATA> GetSmblEquipmentClassPull()
    {
        const string SmblEquipmentClass = "生安设备类型";
        var result = _dbContext.Db.Queryable<SYS6_BASE_DATA>().Where(x => x.CLASS_ID == SmblEquipmentClass)
            .Where(x => x.DATA_STATE == "1")
            .ToList();
        if (!result.Any())
        {
            result.Add(new SYS6_BASE_DATA()
            {
                DATA_ID = "1",
                HOSPITAL_ID = "H0000",
                LAB_ID = "L000",
                CLASS_ID = "生安设备类型",
                DATA_SORT = "001",
                DATA_CNAME = "生物安全柜",
                DATA_ENAME = "生物安全柜",
                HIS_ID = null,
                CUSTOM_CODE = "smblEqClassInit",
                SPELL_CODE = null,
                DATA_STATE = "1",
                FIRST_RPERSON = "管理员",
                FIRST_RTIME = DateTime.Now,
                LAST_MPERSON = "初始化",
                LAST_MTIME = DateTime.Now,
                REMARK = null,
                DATA_SNAME = null,
                DATA_SOURCE = null,
                ONE_CLASS = "设备管理",
                DATA_UNAME = null,
                IF_REPEAT = null,
                SYSTEM_ID = "LIS",
            });
            
            result.Add(new SYS6_BASE_DATA()
                {
                    DATA_ID = "2",
                    HOSPITAL_ID = "H0000",
                    LAB_ID = "L000",
                    CLASS_ID="生安设备类型",
                    DATA_SORT = "002",
                    DATA_CNAME="高压灭菌器",
                    DATA_ENAME="高压灭菌器",
                    HIS_ID = null,
                    CUSTOM_CODE="smblEqClassInit",
                    SPELL_CODE = null,
                    DATA_STATE= "1",
                    FIRST_RPERSON = "管理员",
                    FIRST_RTIME = DateTime.Now,
                    LAST_MPERSON="初始化",
                    LAST_MTIME = DateTime.Now,
                    REMARK = null,
                    DATA_SNAME = null,
                    DATA_SOURCE=null,
                    ONE_CLASS = "设备管理",
                    DATA_UNAME=null,
                    IF_REPEAT= null,
                    SYSTEM_ID="LIS",
                });
            result.Add(new SYS6_BASE_DATA()
                {
                    DATA_ID = "3",
                    HOSPITAL_ID = "H0000",
                    LAB_ID = "L000",
                    CLASS_ID="生安设备类型",
                    DATA_SORT = "003",
                    DATA_CNAME="离心机",
                    DATA_ENAME="离心机",
                    HIS_ID = null,
                    CUSTOM_CODE="smblEqClassInit",
                    SPELL_CODE = null,
                    DATA_STATE= "1",
                    FIRST_RPERSON = "管理员",
                    FIRST_RTIME = DateTime.Now,
                    LAST_MPERSON="初始化",
                    LAST_MTIME = DateTime.Now,
                    REMARK = null,
                    DATA_SNAME = null,
                    DATA_SOURCE=null,
                    ONE_CLASS = "设备管理",
                    DATA_UNAME=null,
                    IF_REPEAT= null,
                    SYSTEM_ID="LIS",
                });
            result.Add(new SYS6_BASE_DATA()
            {
                DATA_ID = "4",
                HOSPITAL_ID = "H0000",
                LAB_ID = "L000",
                CLASS_ID = "生安设备类型",
                DATA_SORT = "004",
                DATA_CNAME = "洗眼装置",
                DATA_ENAME = "洗眼装置",
                HIS_ID = null,
                CUSTOM_CODE = "smblEqClassInit",
                SPELL_CODE = null,
                DATA_STATE = "1",
                FIRST_RPERSON = "管理员",
                FIRST_RTIME = DateTime.Now,
                LAST_MPERSON = "初始化",
                LAST_MTIME = DateTime.Now,
                REMARK = null,
                DATA_SNAME = null,
                DATA_SOURCE = null,
                ONE_CLASS = "设备管理",
                DATA_UNAME = null,
                IF_REPEAT = null,
                SYSTEM_ID = "LIS",
            });
            result.Add(new SYS6_BASE_DATA()
                {
                    DATA_ID = "5",
                    HOSPITAL_ID = "H0000",
                    LAB_ID = "L000",
                    CLASS_ID="生安设备类型",
                    DATA_SORT = "005",
                    DATA_CNAME="消毒装置",
                    DATA_ENAME="消毒装置",
                    HIS_ID = null,
                    CUSTOM_CODE="smblEqClassInit",
                    SPELL_CODE = null,
                    DATA_STATE= "1",
                    FIRST_RPERSON = "管理员",
                    FIRST_RTIME = DateTime.Now,
                    LAST_MPERSON="初始化",
                    LAST_MTIME = DateTime.Now,
                    REMARK = null,
                    DATA_SNAME = null,
                    DATA_SOURCE=null,
                    ONE_CLASS = "设备管理",
                    DATA_UNAME=null,
                    IF_REPEAT= null,
                    SYSTEM_ID="LIS",
                });
            result.Add(new SYS6_BASE_DATA()
            {
                DATA_ID = "6",
                HOSPITAL_ID = "H0000",
                LAB_ID = "L000",
                CLASS_ID="生安设备类型",
                DATA_SORT = "099",
                DATA_CNAME="其他",
                DATA_ENAME="其他",
                HIS_ID = null,
                CUSTOM_CODE="smblEqClassInit",
                SPELL_CODE = null,
                DATA_STATE= "1",
                FIRST_RPERSON = "管理员",
                FIRST_RTIME = DateTime.Now,
                LAST_MPERSON="初始化",
                LAST_MTIME = DateTime.Now,
                REMARK = null,
                DATA_SNAME = null,
                DATA_SOURCE=null,
                ONE_CLASS = "设备管理",
                DATA_UNAME=null,
                IF_REPEAT= null,
                SYSTEM_ID="LIS",
            });            
            result.Add(new SYS6_BASE_DATA()
            {
                DATA_ID = "7",
                HOSPITAL_ID = "H0000",
                LAB_ID = "L000",
                CLASS_ID="生安设备类型",
                DATA_SORT = "007",
                DATA_CNAME="环境监测设备",
                DATA_ENAME="环境监测设备",
                HIS_ID = null,
                CUSTOM_CODE="smblEqClassInit",
                SPELL_CODE = null,
                DATA_STATE= "1",
                FIRST_RPERSON = "管理员",
                FIRST_RTIME = DateTime.Now,
                LAST_MPERSON="初始化",
                LAST_MTIME = DateTime.Now,
                REMARK = null,
                DATA_SNAME = null,
                DATA_SOURCE=null,
                ONE_CLASS = "设备管理",
                DATA_UNAME=null,
                IF_REPEAT= null,
                SYSTEM_ID="LIS",
            });
        }
        result.Add(new SYS6_BASE_DATA()
        {
            DATA_ID = "0",
            HOSPITAL_ID = "H0000",
            LAB_ID = "L000",
            CLASS_ID = "生安设备类型",
            DATA_SORT = "000",
            DATA_CNAME = "未分配生安设备类型",
            DATA_ENAME = "未分配生安设备类型",
            HIS_ID = null,
            CUSTOM_CODE = "smblEqClassInit",
            SPELL_CODE = null,
            DATA_STATE = "1",
            FIRST_RPERSON = "管理员",
            FIRST_RTIME = DateTime.Now,
            LAST_MPERSON = "初始化",
            LAST_MTIME = DateTime.Now,
            REMARK = null,
            DATA_SNAME = null,
            DATA_SOURCE = null,
            ONE_CLASS = "设备管理",
            DATA_UNAME = null,
            IF_REPEAT = null,
            SYSTEM_ID = "LIS",
        });
        if (!result.Any(x=>x.DATA_ID == "7"))
        {
            result.Add(new SYS6_BASE_DATA()
            {
                DATA_ID = "7",
                HOSPITAL_ID = "H0000",
                LAB_ID = "L000",
                CLASS_ID = "生安设备类型",
                DATA_SORT = "007",
                DATA_CNAME = "环境监测设备",
                DATA_ENAME = "环境监测设备",
                HIS_ID = null,
                CUSTOM_CODE = "smblEqClassInit",
                SPELL_CODE = null,
                DATA_STATE = "1",
                FIRST_RPERSON = "管理员",
                FIRST_RTIME = DateTime.Now,
                LAST_MPERSON = "初始化",
                LAST_MTIME = DateTime.Now,
                REMARK = null,
                DATA_SNAME = null,
                DATA_SOURCE = null,
                ONE_CLASS = "设备管理",
                DATA_UNAME = null,
                IF_REPEAT = null,
                SYSTEM_ID = "LIS",
            });
        }
        if (!result.Any(x=>x.DATA_ID == "8"))
        {
            result.Add(new SYS6_BASE_DATA()
            {
                DATA_ID = "8",
                HOSPITAL_ID = "H0000",
                LAB_ID = "L000",
                CLASS_ID = "生安设备类型",
                DATA_SORT = "008",
                DATA_CNAME = "门禁设备",
                DATA_ENAME = "门禁",
                HIS_ID = null,
                CUSTOM_CODE = "smblEqClassInit",
                SPELL_CODE = null,
                DATA_STATE = "1",
                FIRST_RPERSON = "管理员",
                FIRST_RTIME = DateTime.Now,
                LAST_MPERSON = "初始化",
                LAST_MTIME = DateTime.Now,
                REMARK = null,
                DATA_SNAME = null,
                DATA_SOURCE = null,
                ONE_CLASS = "设备管理",
                DATA_UNAME = null,
                IF_REPEAT = null,
                SYSTEM_ID = "LIS",
            });
        }
        return result;
    }
}