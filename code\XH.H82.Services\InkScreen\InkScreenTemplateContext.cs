﻿using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using Spire.Doc;
using SqlSugar;
using XH.H82.IServices;
using XH.H82.Models.Card;
using XH.H82.Models.Entities.InkScreen;
using XH.H82.Models.InkScreenTemplate;
using XH.H82.Models.SugarDbContext;
using XH.LAB.UTILS.Interface;

namespace XH.H82.Services.InkScreen
{
    public class InkScreenTemplateContext
    {
        private readonly ISqlSugarUow<SugarDbContext_Master> _dbContext;
        private readonly IAuthorityService2 _authorityService;
        private ClaimsDto _user { get; set; }

        private readonly List<EMS_INKSCREEN_TEMPLATE> _templates = new();
        private readonly IInkScreenTemplateService _inkScreenTemplateService;
        private AuthorityContext _authorityContext { get; set; }

        public InkScreenTemplateContext(ISqlSugarUow<SugarDbContext_Master> dbContext, IHttpContextAccessor httpContext, IInkScreenTemplateService inkScreenTemplateService, IAuthorityService2 authorityService)
        {
            _authorityService = authorityService;
            _dbContext = dbContext;
            _user = httpContext.HttpContext.User.ToClaimsDto();
            _authorityContext = new(_dbContext,authorityService);
            _inkScreenTemplateService = inkScreenTemplateService;
        }

        public void Init()
        {
            var contentInit = TemplateAContentInit.Default();
            for (int i = 0; i < 5; i++)
            {
                var template = new EMS_INKSCREEN_TEMPLATE()
                {
                    TEMPLATE_ID = IDGenHelper.CreateGuid(),
                    TEMPLATE_NAME = "测试数据1",
                    REMARK = "",
                    TEMPLATE_CONTENT = JsonConvert.SerializeObject(contentInit),
                    TEMPLATE_TITLE = "",
                    PGROUP_SID = ""
                };
                _templates.Add(template);
            }
        }

        /// <summary>
        /// 返回基础信息属性
        /// </summary>
        /// <returns></returns>
        public List<TemplateAttribute> GetAllTemplateAttributes()
        {
            return _inkScreenTemplateService.GetAllTemplateAttributes();
        }

        /// <summary>
        /// 添加模板
        /// </summary>
        /// <param name="TemplateName"></param>
        /// <param name="reamrk"></param>
        public void AddTemplate(string TemplateName, string? reamrk)
        {
            var contentInit = TemplateAContentInit.Init();
            var template = new EMS_INKSCREEN_TEMPLATE()
            {
                TEMPLATE_ID = IDGenHelper.CreateGuid(),
                TEMPLATE_NAME = TemplateName,
                REMARK = reamrk,
                TEMPLATE_CONTENT = JsonConvert.SerializeObject(contentInit),
                TEMPLATE_TITLE = "",
                PGROUP_SID = ""
            };
            _templates.Add(template);
        }

        public List<EMS_INKSCREEN_TEMPLATE> GetTemplates(string labId, string? groupId, string? name)
        {
            _authorityContext.SetUser(_user,  labId);
            var groups = _authorityContext.GetAccessibleProfessionalGroups(labId, null).Select(x => x.PGROUP_ID);
            Init();
            var templates = _templates
                .WhereIF(groupId.IsNotNullOrEmpty(), x => x.PGROUP_SID.Contains(groupId!))
                .WhereIF(name.IsNotNullOrEmpty(), x => x.TEMPLATE_NAME.Contains(name!))
                .ToList()
                .Where(x => x.GetGroups().Count == 0 || x.GetGroups().Any(id => groups.Contains(id)))
                .ToList();
            if (templates is null)
            {
                return new();
            }
            return _templates;
        }

    }
}
