﻿//using System;
//using System.Collections.Generic;
//using System.ComponentModel.DataAnnotations;
//using System.ComponentModel.DataAnnotations.Schema;
//using System.Linq;
//using System.Text;
//using System.Threading.Tasks;
//using H.Utility.SqlSugarInfra;
//using SqlSugar;

//namespace XH.H82.Models.Entities
//{
//    [DBOwner("XH_SYS")]
//    [SugarTable("SYS6_SETUP_DICT")]
//    public class SYS6_SETUP_DICT
//    {
//        [SugarColumn(IsPrimaryKey = true)]
//        public string SETUP_NO { get; set; }
//        public string HOSPITAL_ID { get; set; }
//        public string UNIT_ID { get; set; }
//        public string UNIT_TYPE { get; set; }
//        public string MODULE_NO { get; set; }
//        public string SETUP_NAME { get; set; }
//        public string SETUP_CLASS { get; set; }
//        public string SETUP_SORT { get; set; }
//        public string DEFAULT_VALUE { get; set; }
//        public string CHOICE_VALUE { get; set; }
//        public string SETUP_CONTENT { get; set; }
//        public string SETUP_DESC { get; set; }
//        public string SETUP_STATE { get; set; }
//        public string FIRST_RPERSON { get; set; }
//        public DateTime? FIRST_RTIME { get; set; }
//        public string LAST_MPERSON { get; set; }
//        public DateTime? LAST_MTIME { get; set; }
//        public string REMARK { get; set; }
//        public string SETUP_SNAME { get; set; }
//        public string DATA_CLASS_ID { get; set; }
//        public string SETUP_TYPE { get; set; }
//        public string SETUP_ONECLASS { get; set; }
//    }
//}
