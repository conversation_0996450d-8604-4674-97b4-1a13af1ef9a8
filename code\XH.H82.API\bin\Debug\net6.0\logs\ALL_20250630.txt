2025-06-30 09:49:01.889 +08:00 [INF] ==>App Start..2025-06-30 09:49:01
2025-06-30 09:49:02.192 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-06-30 09:49:02.195 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-06-30 09:49:06.215 +08:00 [INF] ==>基础连接请求完成.
2025-06-30 09:49:06.838 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-06-30 09:49:07.357 +08:00 [INF]  正在通过S01写入版本信息...
2025-06-30 09:49:07.778 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-06-30 09:49:07.783 +08:00 [INF]  正在通过H04写入版本信息...
2025-06-30 09:49:08.157 +08:00 [INF] ==>版本写入成功:6.25.300
2025-06-30 09:49:10.466 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-06-30 09:49:10.863 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-06-30 09:49:11.563 +08:00 [INF] 正在检查旧数据是否完整...
2025-06-30 09:49:11.564 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-06-30 09:49:12.916 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-06-30 09:49:16.034 +08:00 [INF] ==>初始化完成..
2025-06-30 09:49:16.104 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-06-30 09:49:16.106 +08:00 [INF] 设备启用任务
2025-06-30 09:49:16.106 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-06-30 09:49:16.510 +08:00 [INF] 【SQL执行耗时:379.3764ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-06-30 09:49:16.705 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-06-30 09:49:16.722 +08:00 [INF] Now listening on: https://[::]:18482
2025-06-30 09:49:16.723 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-30 09:49:16.724 +08:00 [INF] Hosting environment: Development
2025-06-30 09:49:16.724 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-06-30 09:49:36.879 +08:00 [INF] HTTP GET /api/Base/GetMenuInfo responded 401 in 364.9626 ms
2025-06-30 09:49:46.364 +08:00 [INF] HTTP GET /favicon.ico responded 404 in 1.5039 ms
2025-06-30 09:49:56.196 +08:00 [INF] 调用H07模块[/api/External/GetUserUnitInfo?moduleId=H82],耗时:860ms
2025-06-30 09:50:07.007 +08:00 [ERR] 未处理的异常::H.Utility.BizException: 请求Header里，需按登录入口选择的单元添加参数"Xh-lab-labid"，不能为空！
   at XH.LAB.UTILS.Implement.AuthorityService2.GetUserMenuList(Object soa, String moduleId, MenuClassEnum menuClassStr, String parentMenuId)
   at Castle.Proxies.Invocations.IAuthorityService2_GetUserMenuList.InvokeMethodOnTarget()
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.UseCacheWhenFailedAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.CacheAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at Castle.Proxies.IAuthorityService2Proxy.GetUserMenuList(Object soa, String moduleId, MenuClassEnum menuClassStr, String parentMenuId)
   at XH.H82.Services.SystemService.HasButtonPermissions(String promiss) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\SystemService.cs:line 744
   at Castle.Proxies.Invocations.ISystemService_HasButtonPermissions.InvokeMethodOnTarget()
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.UseCacheWhenFailedAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.CacheAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at Castle.Proxies.ISystemServiceProxy.HasButtonPermissions(String promiss)
   at XH.H82.API.Controllers.SystemController.HasButtonPermissions(String buttonPermissionId) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\Controllers\SystemController.cs:line 411
   at lambda_method908(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-06-30 09:50:07.010 +08:00 [INF] HTTP GET /api/System/HasButtonPermissions responded 200 in 18386.5943 ms
2025-06-30 09:50:07.013 +08:00 [INF] 【接口超时阀值预警】 [2a755193eee8177f0343b71cc7f2e1dd]接口/api/System/HasButtonPermissions,耗时:[18387]毫秒
2025-06-30 09:50:18.594 +08:00 [INF] 调用H07模块[/api/External/GetUserMenuInfoPost?moduleId=H82&unitClass=2&unitId=PG006&specialJson=[{"unitClass":"2","unitId":"PG005"},{"unitClass":"2","unitId":"PG095"},{"unitClass":"2","unitId":"PG013"},{"unitClass":"2","unitId":"PG055"},{"unitClass":"2","unitId":"PG054"},{"unitClass":"2","unitId":"PG001"},{"unitClass":"11","unitId":"L001"},{"unitClass":"19","unitId":"MG014"},{"unitClass":"19","unitId":"MG012"},{"unitClass":"19","unitId":"MG013"},{"unitClass":"19","unitId":"MG025"},{"unitClass":"19","unitId":"MG002"},{"unitClass":"19","unitId":"MG034"},{"unitClass":"19","unitId":"MG026"},{"unitClass":"19","unitId":"MG033"},{"unitClass":"19","unitId":"MG031"}]],耗时:1224ms
2025-06-30 09:51:00.579 +08:00 [INF] HTTP GET /api/System/HasButtonPermissions responded 200 in 43329.3797 ms
2025-06-30 09:51:00.580 +08:00 [INF] 【接口超时阀值预警】 [114d1410c08b76dfcc5a731feeafdbf9]接口/api/System/HasButtonPermissions,耗时:[43329]毫秒
2025-06-30 10:21:05.132 +08:00 [INF] ==>App Start..2025-06-30 10:21:05
2025-06-30 10:21:05.317 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-06-30 10:21:05.321 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-06-30 10:21:09.144 +08:00 [INF] ==>基础连接请求完成.
2025-06-30 10:21:09.852 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-06-30 10:21:10.255 +08:00 [INF]  正在通过S01写入版本信息...
2025-06-30 10:21:10.595 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-06-30 10:21:10.602 +08:00 [INF]  正在通过H04写入版本信息...
2025-06-30 10:21:10.935 +08:00 [INF] ==>版本写入成功:6.25.300
2025-06-30 10:21:11.277 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-06-30 10:21:11.341 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-06-30 10:21:11.794 +08:00 [INF] 正在检查旧数据是否完整...
2025-06-30 10:21:11.794 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-06-30 10:21:13.322 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-06-30 10:21:18.824 +08:00 [INF] ==>初始化完成..
2025-06-30 10:21:18.849 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-06-30 10:21:18.850 +08:00 [INF] 设备启用任务
2025-06-30 10:21:18.851 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-06-30 10:21:19.312 +08:00 [INF] 【SQL执行耗时:430.462ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-06-30 10:21:19.473 +08:00 [INF] Now listening on: https://[::]:18482
2025-06-30 10:43:29.489 +08:00 [INF] ==>App Start..2025-06-30 10:43:29
2025-06-30 10:43:29.661 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-06-30 10:43:29.664 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-06-30 10:43:35.902 +08:00 [INF] ==>基础连接请求完成.
2025-06-30 10:43:36.283 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-06-30 10:43:36.668 +08:00 [INF]  正在通过S01写入版本信息...
2025-06-30 10:43:37.316 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-06-30 10:43:37.318 +08:00 [INF]  正在通过H04写入版本信息...
2025-06-30 10:43:37.668 +08:00 [INF] ==>版本写入成功:6.25.300
2025-06-30 10:43:37.959 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-06-30 10:43:38.018 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-06-30 10:43:38.450 +08:00 [INF] 正在检查旧数据是否完整...
2025-06-30 10:43:38.451 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-06-30 10:43:39.484 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-06-30 10:43:42.889 +08:00 [INF] ==>初始化完成..
2025-06-30 10:43:42.909 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-06-30 10:43:42.910 +08:00 [INF] 设备启用任务
2025-06-30 10:43:42.910 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-06-30 10:43:43.308 +08:00 [INF] 【SQL执行耗时:377.4929ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-06-30 10:43:43.444 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-06-30 10:43:43.458 +08:00 [INF] Now listening on: https://[::]:18482
2025-06-30 10:43:43.459 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-30 10:43:43.459 +08:00 [INF] Hosting environment: Development
2025-06-30 10:43:43.460 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-06-30 16:10:58.850 +08:00 [INF] ==>App Start..2025-06-30 16:10:58
2025-06-30 16:10:59.043 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-06-30 16:10:59.046 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-06-30 16:11:02.701 +08:00 [INF] ==>基础连接请求完成.
2025-06-30 16:11:03.084 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-06-30 16:11:03.504 +08:00 [INF]  正在通过S01写入版本信息...
2025-06-30 16:11:03.801 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-06-30 16:11:03.803 +08:00 [INF]  正在通过H04写入版本信息...
2025-06-30 16:11:04.176 +08:00 [INF] ==>版本写入成功:6.25.300
2025-06-30 16:11:05.945 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-06-30 16:11:06.137 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-06-30 16:11:06.633 +08:00 [INF] 正在检查旧数据是否完整...
2025-06-30 16:11:06.633 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-06-30 16:11:07.685 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-06-30 16:11:11.578 +08:00 [INF] ==>初始化完成..
2025-06-30 16:11:11.636 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-06-30 16:11:11.637 +08:00 [INF] 设备启用任务
2025-06-30 16:11:11.638 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-06-30 16:11:12.051 +08:00 [INF] 【SQL执行耗时:388.0379ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-06-30 16:11:12.212 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-06-30 16:11:12.225 +08:00 [INF] Now listening on: https://[::]:18482
2025-06-30 16:11:12.226 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-30 16:11:12.227 +08:00 [INF] Hosting environment: Development
2025-06-30 16:11:12.227 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-06-30 16:20:49.196 +08:00 [INF] ==>App Start..2025-06-30 16:20:49
2025-06-30 16:20:49.367 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-06-30 16:20:49.370 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-06-30 16:20:53.057 +08:00 [INF] ==>基础连接请求完成.
2025-06-30 16:20:53.415 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-06-30 16:20:53.777 +08:00 [INF]  正在通过S01写入版本信息...
2025-06-30 16:20:54.212 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-06-30 16:20:54.215 +08:00 [INF]  正在通过H04写入版本信息...
2025-06-30 16:20:54.551 +08:00 [INF] ==>版本写入成功:6.25.300
2025-06-30 16:20:54.799 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-06-30 16:20:54.874 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-06-30 16:20:55.302 +08:00 [INF] 正在检查旧数据是否完整...
2025-06-30 16:20:55.302 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-06-30 16:20:57.324 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-06-30 16:21:01.450 +08:00 [INF] ==>初始化完成..
2025-06-30 16:21:01.509 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-06-30 16:21:01.512 +08:00 [INF] 设备启用任务
2025-06-30 16:21:01.512 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-06-30 16:21:01.905 +08:00 [INF] 【SQL执行耗时:366.2687ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-06-30 16:21:02.077 +08:00 [INF] Now listening on: https://[::]:18482
2025-06-30 16:24:42.808 +08:00 [INF] ==>App Start..2025-06-30 16:24:42
2025-06-30 16:24:42.983 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-06-30 16:24:42.986 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-06-30 16:24:48.048 +08:00 [INF] ==>基础连接请求完成.
2025-06-30 16:24:48.625 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-06-30 16:24:48.976 +08:00 [INF]  正在通过S01写入版本信息...
2025-06-30 16:24:49.294 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-06-30 16:24:49.296 +08:00 [INF]  正在通过H04写入版本信息...
2025-06-30 16:24:49.659 +08:00 [INF] ==>版本写入成功:6.25.300
2025-06-30 16:24:50.000 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-06-30 16:24:50.070 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-06-30 16:24:50.669 +08:00 [INF] 正在检查旧数据是否完整...
2025-06-30 16:24:50.708 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-06-30 16:24:52.737 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-06-30 16:24:57.023 +08:00 [INF] ==>初始化完成..
2025-06-30 16:24:57.044 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-06-30 16:24:57.045 +08:00 [INF] 设备启用任务
2025-06-30 16:24:57.046 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-06-30 16:24:57.709 +08:00 [INF] 【SQL执行耗时:639.9712ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-06-30 16:24:57.875 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-06-30 16:24:57.889 +08:00 [INF] Now listening on: https://[::]:18482
2025-06-30 16:24:57.891 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-30 16:24:57.891 +08:00 [INF] Hosting environment: Development
2025-06-30 16:24:57.891 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-06-30 16:25:07.223 +08:00 [ERR] 未处理的异常::System.NotImplementedException: The method or operation is not implemented.
   at XH.H82.Services.EquipmentCodeCustom.CustomCodeService.GetEquipmentCodeCustomDict() in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentCodeCustom\CustomCodeService.cs:line 20
   at Castle.Proxies.Invocations.ICustomCodeService_GetEquipmentCodeCustomDict.InvokeMethodOnTarget()
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.UseCacheWhenFailedAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.CacheAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at Castle.Proxies.ICustomCodeServiceProxy.GetEquipmentCodeCustomDict()
   at XH.H82.API.Controllers.EquipmentCodeCustom.CodeCustomController.GetEquipmentCodeCustomDict() in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\Controllers\EquipmentCodeCustom\CodeCustomController.cs:line 34
   at lambda_method909(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-06-30 16:25:07.248 +08:00 [ERR] HTTP GET /api/CodeCustom/GetEquipmentCodeCustomDict responded 500 in 7854.9456 ms
2025-06-30 16:25:07.252 +08:00 [INF] 【接口超时阀值预警】 [f3df0c2d101461b673c79459bef7b186]接口/api/CodeCustom/GetEquipmentCodeCustomDict,耗时:[7865]毫秒
2025-06-30 16:51:26.237 +08:00 [INF] ==>App Start..2025-06-30 16:51:26
2025-06-30 16:51:26.422 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-06-30 16:51:26.425 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-06-30 16:51:33.471 +08:00 [INF] ==>基础连接请求完成.
2025-06-30 16:51:34.110 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-06-30 16:51:34.483 +08:00 [INF]  正在通过S01写入版本信息...
2025-06-30 16:51:34.824 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-06-30 16:51:34.833 +08:00 [INF]  正在通过H04写入版本信息...
2025-06-30 16:51:35.176 +08:00 [INF] ==>版本写入成功:6.25.300
2025-06-30 16:51:35.701 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-06-30 16:51:35.810 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-06-30 16:51:36.275 +08:00 [INF] 正在检查旧数据是否完整...
2025-06-30 16:51:36.275 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-06-30 16:51:37.434 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-06-30 16:51:40.651 +08:00 [INF] ==>初始化完成..
2025-06-30 16:51:40.671 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-06-30 16:51:40.672 +08:00 [INF] 设备启用任务
2025-06-30 16:51:40.673 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-06-30 16:51:41.081 +08:00 [INF] 【SQL执行耗时:386.1994ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-06-30 16:51:41.233 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-06-30 16:51:41.246 +08:00 [INF] Now listening on: https://[::]:18482
2025-06-30 16:51:41.247 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-30 16:51:41.248 +08:00 [INF] Hosting environment: Development
2025-06-30 16:51:41.248 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-06-30 16:53:11.289 +08:00 [INF] HTTP GET /api/CodeCustom/GetEquipmentCodeCustomDict responded 401 in 154.7523 ms
2025-06-30 16:53:28.686 +08:00 [INF] 【SQL执行耗时:755.7905ms】

[Sql]:SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" <> :EqpNoState0 ) 
[Pars]:
[Name]::EqpNoState0 [Value]:2 [Type]:String    

2025-06-30 16:53:28.769 +08:00 [INF] HTTP GET /api/CodeCustom/GetEquipmentCodeCustomDict responded 200 in 5683.2919 ms
2025-06-30 16:53:28.771 +08:00 [INF] 【接口超时阀值预警】 [8df884d4a7856cec20832d21051154f2]接口/api/CodeCustom/GetEquipmentCodeCustomDict,耗时:[5684]毫秒
2025-06-30 17:03:34.101 +08:00 [INF] ==>App Start..2025-06-30 17:03:34
2025-06-30 17:03:34.272 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-06-30 17:03:34.275 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-06-30 17:03:38.219 +08:00 [INF] ==>基础连接请求完成.
2025-06-30 17:03:38.596 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-06-30 17:03:38.974 +08:00 [INF]  正在通过S01写入版本信息...
2025-06-30 17:03:39.323 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-06-30 17:03:39.330 +08:00 [INF]  正在通过H04写入版本信息...
2025-06-30 17:03:39.680 +08:00 [INF] ==>版本写入成功:6.25.300
2025-06-30 17:03:40.157 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-06-30 17:03:40.266 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-06-30 17:03:40.763 +08:00 [INF] 正在检查旧数据是否完整...
2025-06-30 17:03:40.764 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-06-30 17:03:41.702 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-06-30 17:03:45.312 +08:00 [INF] ==>初始化完成..
2025-06-30 17:03:45.332 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-06-30 17:03:45.332 +08:00 [INF] 设备启用任务
2025-06-30 17:03:45.333 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-06-30 17:03:45.727 +08:00 [INF] 【SQL执行耗时:373.7968ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-06-30 17:03:45.855 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-06-30 17:03:45.866 +08:00 [INF] Now listening on: https://[::]:18482
2025-06-30 17:03:45.868 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-30 17:03:45.868 +08:00 [INF] Hosting environment: Development
2025-06-30 17:03:45.868 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-06-30 17:05:01.164 +08:00 [INF] ==>App Start..2025-06-30 17:05:01
2025-06-30 17:05:01.336 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-06-30 17:05:01.339 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-06-30 17:05:11.403 +08:00 [INF] ==>基础连接请求完成.
2025-06-30 17:05:11.778 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-06-30 17:05:12.136 +08:00 [INF]  正在通过S01写入版本信息...
2025-06-30 17:05:12.495 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-06-30 17:05:12.498 +08:00 [INF]  正在通过H04写入版本信息...
2025-06-30 17:05:12.854 +08:00 [INF] ==>版本写入成功:6.25.300
2025-06-30 17:05:13.324 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-06-30 17:05:13.421 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-06-30 17:05:13.867 +08:00 [INF] 正在检查旧数据是否完整...
2025-06-30 17:05:13.867 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-06-30 17:05:14.751 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-06-30 17:05:17.650 +08:00 [INF] ==>初始化完成..
2025-06-30 17:05:17.669 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-06-30 17:05:17.670 +08:00 [INF] 设备启用任务
2025-06-30 17:05:17.671 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-06-30 17:05:18.426 +08:00 [INF] 【SQL执行耗时:734.3218ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-06-30 17:05:18.571 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-06-30 17:05:18.586 +08:00 [INF] Now listening on: https://[::]:18482
2025-06-30 17:05:18.587 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-30 17:05:18.588 +08:00 [INF] Hosting environment: Development
2025-06-30 17:05:18.588 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-06-30 17:06:22.672 +08:00 [INF] ==>App Start..2025-06-30 17:06:22
2025-06-30 17:06:22.842 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-06-30 17:06:22.845 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-06-30 17:06:27.027 +08:00 [INF] ==>基础连接请求完成.
2025-06-30 17:06:28.429 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-06-30 17:06:28.781 +08:00 [INF]  正在通过S01写入版本信息...
2025-06-30 17:06:29.171 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-06-30 17:06:29.173 +08:00 [INF]  正在通过H04写入版本信息...
2025-06-30 17:06:29.830 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-06-30 17:06:29.918 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-06-30 17:06:30.359 +08:00 [INF] 正在检查旧数据是否完整...
2025-06-30 17:06:30.359 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-06-30 17:06:31.259 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-06-30 17:06:34.879 +08:00 [INF] ==>初始化完成..
2025-06-30 17:06:34.899 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-06-30 17:06:34.900 +08:00 [INF] 设备启用任务
2025-06-30 17:06:34.900 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-06-30 17:06:35.536 +08:00 [INF] 【SQL执行耗时:615.0679ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-06-30 17:06:35.679 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-06-30 17:06:35.692 +08:00 [INF] Now listening on: https://[::]:18482
2025-06-30 17:06:35.694 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-30 17:06:35.694 +08:00 [INF] Hosting environment: Development
2025-06-30 17:06:35.695 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-06-30 17:10:04.735 +08:00 [INF] ==>App Start..2025-06-30 17:10:04
2025-06-30 17:10:04.928 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-06-30 17:10:04.931 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-06-30 17:10:06.709 +08:00 [INF] ==>基础连接请求完成.
2025-06-30 17:10:07.120 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-06-30 17:10:07.572 +08:00 [INF]  正在通过S01写入版本信息...
2025-06-30 17:10:07.913 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-06-30 17:10:07.915 +08:00 [INF]  正在通过H04写入版本信息...
2025-06-30 17:10:08.508 +08:00 [INF] ==>版本写入成功:6.25.300
2025-06-30 17:10:08.622 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-06-30 17:10:08.683 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-06-30 17:10:09.116 +08:00 [INF] 正在检查旧数据是否完整...
2025-06-30 17:10:09.116 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-06-30 17:10:10.408 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-06-30 17:10:13.619 +08:00 [INF] ==>初始化完成..
2025-06-30 17:10:13.640 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-06-30 17:10:13.643 +08:00 [INF] 设备启用任务
2025-06-30 17:10:13.643 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-06-30 17:10:15.191 +08:00 [INF] 【SQL执行耗时:1526.9865ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-06-30 17:10:15.359 +08:00 [INF] Now listening on: https://[::]:18482
2025-06-30 17:12:12.401 +08:00 [INF] 【SQL执行耗时:368.8ms】

[Sql]:INSERT INTO "XH_OA"."EMS_EQPNO_FORMAT_DICT"  
           ("EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK")
     VALUES
           (:EQP_NO_ID,:HOSPITAL_ID,:EQP_NO_NAME,:EQP_NO_LEVEL,:EQP_NO_CLASS,:EQP_DISPLAY_JSON,:EQP_NO_APPLYS,:EQP_NO_STATE,:FIRST_RPERSON,:FIRST_RTIME,:LAST_MPERSON,:LAST_MTIME,:REMARK)  
[Pars]:
[Name]::EQP_NO_ID [Value]:90EE1F628B1B4EE9BBD7D297CB381B53 [Type]:String    
[Name]::HOSPITAL_ID [Value]:33A001 [Type]:String    
[Name]::EQP_NO_NAME [Value]:测试模板 [Type]:String    
[Name]::EQP_NO_LEVEL [Value]:0 [Type]:String    
[Name]::EQP_NO_CLASS [Value]:1 [Type]:String    
[Name]::EQP_DISPLAY_JSON [Value]:{"DisplayContentString":"{设备名称}_{设备编号}","DisplayContentCode":"EQUIPMENT_NAME;EQUIPMENT_CODE"} [Type]:String    
[Name]::EQP_NO_APPLYS [Value]:PG001 [Type]:String    
[Name]::EQP_NO_STATE [Value]:1 [Type]:String    
[Name]::FIRST_RPERSON [Value]:gz1_广测重 [Type]:String    
[Name]::FIRST_RTIME [Value]:2025/6/30 17:12:11 [Type]:DateTime    
[Name]::LAST_MPERSON [Value]:gz1_广测重 [Type]:String    
[Name]::LAST_MTIME [Value]:2025/6/30 17:12:11 [Type]:DateTime    
[Name]::REMARK [Value]: [Type]:String    

2025-06-30 17:12:12.479 +08:00 [INF] HTTP POST /api/CodeCustom/AddEquipmentCodeCustomDict responded 200 in 4475.0785 ms
2025-06-30 17:12:12.482 +08:00 [INF] 【接口超时阀值预警】 [4025d186835507e952254f38912df7d8]接口/api/CodeCustom/AddEquipmentCodeCustomDict,耗时:[4482]毫秒
2025-06-30 17:12:26.815 +08:00 [INF] 【SQL执行耗时:346.78ms】

[Sql]:SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" <> :EqpNoState0 ) 
[Pars]:
[Name]::EqpNoState0 [Value]:2 [Type]:String    

2025-06-30 17:12:26.938 +08:00 [INF] HTTP GET /api/CodeCustom/GetEquipmentCodeCustomDict responded 200 in 516.1624 ms
2025-06-30 17:12:26.939 +08:00 [INF] 【接口超时阀值预警】 [8c3dd340bc82591b28f4b57a9a696b4e]接口/api/CodeCustom/GetEquipmentCodeCustomDict,耗时:[516]毫秒
2025-06-30 17:14:49.945 +08:00 [INF] ==>App Start..2025-06-30 17:14:49
2025-06-30 17:14:50.130 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-06-30 17:14:50.133 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-06-30 17:14:58.669 +08:00 [INF] ==>基础连接请求完成.
2025-06-30 17:14:59.072 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-06-30 17:14:59.435 +08:00 [INF]  正在通过S01写入版本信息...
2025-06-30 17:14:59.773 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-06-30 17:14:59.782 +08:00 [INF]  正在通过H04写入版本信息...
2025-06-30 17:15:00.153 +08:00 [INF] ==>版本写入成功:6.25.300
2025-06-30 17:15:00.584 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-06-30 17:15:00.694 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-06-30 17:15:01.155 +08:00 [INF] 正在检查旧数据是否完整...
2025-06-30 17:15:01.156 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-06-30 17:15:02.288 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-06-30 17:15:05.019 +08:00 [INF] ==>初始化完成..
2025-06-30 17:15:05.039 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-06-30 17:15:05.041 +08:00 [INF] 设备启用任务
2025-06-30 17:15:05.042 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-06-30 17:15:05.678 +08:00 [INF] 【SQL执行耗时:615.7933ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-06-30 17:15:06.075 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-06-30 17:15:06.089 +08:00 [INF] Now listening on: https://[::]:18482
2025-06-30 17:15:06.090 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-30 17:15:06.090 +08:00 [INF] Hosting environment: Development
2025-06-30 17:15:06.091 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-06-30 17:15:42.745 +08:00 [INF] 【SQL执行耗时:736.6737ms】

[Sql]:INSERT INTO "XH_OA"."EMS_EQPNO_FORMAT_DICT"  
           ("EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK")
     VALUES
           (:EQP_NO_ID,:HOSPITAL_ID,:EQP_NO_NAME,:EQP_NO_LEVEL,:EQP_NO_CLASS,:EQP_DISPLAY_JSON,:EQP_NO_APPLYS,:EQP_NO_STATE,:FIRST_RPERSON,:FIRST_RTIME,:LAST_MPERSON,:LAST_MTIME,:REMARK)  
[Pars]:
[Name]::EQP_NO_ID [Value]:0D0B444E66E64EFFB796A500DF606833 [Type]:String    
[Name]::HOSPITAL_ID [Value]:33A001 [Type]:String    
[Name]::EQP_NO_NAME [Value]:测试模板 [Type]:String    
[Name]::EQP_NO_LEVEL [Value]:0 [Type]:String    
[Name]::EQP_NO_CLASS [Value]:1 [Type]:String    
[Name]::EQP_DISPLAY_JSON [Value]:{"DisplayContentString":"{设备名称}_{设备编号}","DisplayContentCode":"EQUIPMENT_NAME;EQUIPMENT_CODE"} [Type]:String    
[Name]::EQP_NO_APPLYS [Value]:PG001 [Type]:String    
[Name]::EQP_NO_STATE [Value]:1 [Type]:String    
[Name]::FIRST_RPERSON [Value]:gz1_广测重 [Type]:String    
[Name]::FIRST_RTIME [Value]:2025/6/30 17:15:41 [Type]:DateTime    
[Name]::LAST_MPERSON [Value]:gz1_广测重 [Type]:String    
[Name]::LAST_MTIME [Value]:2025/6/30 17:15:41 [Type]:DateTime    
[Name]::REMARK [Value]: [Type]:String    

2025-06-30 17:15:42.822 +08:00 [INF] HTTP POST /api/CodeCustom/AddEquipmentCodeCustomDict responded 200 in 5082.7776 ms
2025-06-30 17:15:42.824 +08:00 [INF] 【接口超时阀值预警】 [9157c23ce8f92e4821f980d54bd76e2d]接口/api/CodeCustom/AddEquipmentCodeCustomDict,耗时:[5089]毫秒
2025-06-30 17:15:54.818 +08:00 [INF] 【SQL执行耗时:1089.2042ms】

[Sql]:SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" <> :EqpNoState0 ) 
[Pars]:
[Name]::EqpNoState0 [Value]:2 [Type]:String    

2025-06-30 17:15:54.975 +08:00 [INF] HTTP GET /api/CodeCustom/GetEquipmentCodeCustomDict responded 200 in 1290.4047 ms
2025-06-30 17:15:54.975 +08:00 [INF] 【接口超时阀值预警】 [bb2db47d4ad87ae60f54327fb8e2366e]接口/api/CodeCustom/GetEquipmentCodeCustomDict,耗时:[1290]毫秒
