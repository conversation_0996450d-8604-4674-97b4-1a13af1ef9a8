using Microsoft.AspNetCore.Mvc.ModelBinding;
using XH.H82.Base.Tree;
using XH.H82.Models.Dtos;
using XH.H82.Models.Entities;
using XH.H82.Models.Entities.Common;
using XH.LAB.UTILS.Models;

namespace XH.H82.IServices.Sbml;

public interface ISmblServicve
{

    /// <summary>
    /// 确定生安数据范围
    /// </summary>
    /// <param name="labId"></param>
    /// <param name="smblLabId"></param>
    public void AsSmblforPgroups(string? labId, string? smblLabId);
    /// <summary>
    /// 机构入口-生安树基本结构
    /// </summary>
    /// <param name="hospitalId"></param>
    /// <returns></returns>
    public (RootNode rootNode, List<ITreeNode> nodes) GetSmblHospitalBaseTree(string hospitalId);
    /// <summary>
    /// 科室入口-生安树基本结构
    /// </summary>
    /// <param name="labId"></param>
    /// <returns></returns>
    public (RootNode rootNode, List<ITreeNode> nodes) GetLabBaseTree(string labId);
    /// <summary>
    /// 备案实验室入口-生安基本结构
    /// </summary>
    /// <param name="smblLabId"></param>
    /// <returns></returns>
    public (RootNode rootNode, List<ITreeNode> nodes) GetSmblLabBaseTree(string smblLabId);


    /// <summary>
    /// 备案实验室下拉数据
    /// </summary>
    /// <param name="hospitalId">机构Id</param>
    /// <returns></returns>
    public (RootNode rootNode, List<ITreeNode> nodes) GetSmblLabPullTree(string hospitalId);
    
    /// <summary>
    /// 查询生安设备类型
    /// </summary>
    /// <returns></returns>
    public List<SYS6_BASE_DATA> GetSmblEquipmentClassPull();

    /// <summary>
    /// 生安-申购记录查询
    /// </summary>
    /// <param name="startTime"> 开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="state">状态</param>
    /// <param name="search">模糊查询关键字</param>
    /// <returns></returns>
    public List<EMS_SUBSCRIBE_INFO> GetSubscrs(DateTime startTime, DateTime endTime, string state,
        string search);

    /// <summary>
    /// 查询生安设备档案列表
    /// </summary>
    /// <param name="state">设备状态</param>
    /// <param name="type">设备类型</param>
    /// <param name="keyWord">查询关键字</param>
    /// <param name="smblEquipmentClass">生安设备类型</param>
    /// <returns></returns>
    public List<EquipmentInfoDto> Getequipments(string state, string type, string keyWord , string smblEquipmentClass);


    /// <summary>
    /// 查询生安设备工作计划列表
    /// </summary>
    /// <param name="keyword"></param>
    /// <param name="equipmentClass"></param>
    /// <param name="smblEquipmentClass">生安设备类型</param>
    /// <returns></returns>
    public List<EmsWorkPlanDto> GetWorkPlanDtos(string keyword, string equipmentClass,string smblEquipmentClass);


    /// <summary>
    ///   获取报废停用列表
    /// </summary>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="smblEquipmentClass">生安设备类型</param>
    /// <param name="equipmentClass">设备类型</param>
    /// <param name="state">状态</param>
    /// <param name="applyClass">申请类型</param>
    /// <param name="equipmentKey">设备检索</param>
    /// <param name="personKey">人物检索</param>
    /// <param name="userNo">前用户处理</param>
    /// <returns></returns>
    public List<ScrapStopListDto> GetScrapStopList(DateTime startTime, DateTime endTime, string smblEquipmentClass,string equipmentClass,
        string state,
        string applyClass, string equipmentKey, string personKey,
         string ? userNo);

    /// <summary>
    /// 获取生安待报废停用的设备列表
    /// </summary>
    /// <param name="equipmentClass">设备类型</param>
    /// <param name="keyword">查询</param>
    /// <returns></returns>
    public List<EMS_EQUIPMENT_INFO> GetEquipmentApplyList(string equipmentClass, string keyword );

    /// <summary>
    /// 更新备案实验室时间
    /// </summary>
    /// <param name="smblLabId"></param>
    void UpdateSmblLabTime(string smblLabId);
}