﻿//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//
//     对此文件的更改可能导致不正确的行为，并在以下条件下丢失:
//     代码重新生成。
// </auto-generated>
//------------------------------------------------------------------------------

namespace XingHePlatform
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.ServiceContractAttribute(ConfigurationName="XingHePlatform.XingHePlatformSoap")]
    public interface XingHePlatformSoap
    {
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/CallLisInterface", ReplyAction="*")]
        System.Threading.Tasks.Task<XingHePlatform.CallLisInterfaceResponse> CallLisInterfaceAsync(XingHePlatform.CallLisInterfaceRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/CallInterface", ReplyAction="*")]
        System.Threading.Tasks.Task<XingHePlatform.CallInterfaceResponse> CallInterfaceAsync(XingHePlatform.CallInterfaceRequest request);
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class CallLisInterfaceRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="CallLisInterface", Namespace="http://tempuri.org/", Order=0)]
        public XingHePlatform.CallLisInterfaceRequestBody Body;
        
        public CallLisInterfaceRequest()
        {
        }
        
        public CallLisInterfaceRequest(XingHePlatform.CallLisInterfaceRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class CallLisInterfaceRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string methodCode;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string inParam;
        
        public CallLisInterfaceRequestBody()
        {
        }
        
        public CallLisInterfaceRequestBody(string methodCode, string inParam)
        {
            this.methodCode = methodCode;
            this.inParam = inParam;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class CallLisInterfaceResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="CallLisInterfaceResponse", Namespace="http://tempuri.org/", Order=0)]
        public XingHePlatform.CallLisInterfaceResponseBody Body;
        
        public CallLisInterfaceResponse()
        {
        }
        
        public CallLisInterfaceResponse(XingHePlatform.CallLisInterfaceResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class CallLisInterfaceResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string CallLisInterfaceResult;
        
        public CallLisInterfaceResponseBody()
        {
        }
        
        public CallLisInterfaceResponseBody(string CallLisInterfaceResult)
        {
            this.CallLisInterfaceResult = CallLisInterfaceResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class CallInterfaceRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="CallInterface", Namespace="http://tempuri.org/", Order=0)]
        public XingHePlatform.CallInterfaceRequestBody Body;
        
        public CallInterfaceRequest()
        {
        }
        
        public CallInterfaceRequest(XingHePlatform.CallInterfaceRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class CallInterfaceRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string headXml;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string bodyXml;
        
        public CallInterfaceRequestBody()
        {
        }
        
        public CallInterfaceRequestBody(string headXml, string bodyXml)
        {
            this.headXml = headXml;
            this.bodyXml = bodyXml;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class CallInterfaceResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="CallInterfaceResponse", Namespace="http://tempuri.org/", Order=0)]
        public XingHePlatform.CallInterfaceResponseBody Body;
        
        public CallInterfaceResponse()
        {
        }
        
        public CallInterfaceResponse(XingHePlatform.CallInterfaceResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class CallInterfaceResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string CallInterfaceResult;
        
        public CallInterfaceResponseBody()
        {
        }
        
        public CallInterfaceResponseBody(string CallInterfaceResult)
        {
            this.CallInterfaceResult = CallInterfaceResult;
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    public interface XingHePlatformSoapChannel : XingHePlatform.XingHePlatformSoap, System.ServiceModel.IClientChannel
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    public partial class XingHePlatformSoapClient : System.ServiceModel.ClientBase<XingHePlatform.XingHePlatformSoap>, XingHePlatform.XingHePlatformSoap
    {
        
        /// <summary>
        /// 实现此分部方法，配置服务终结点。
        /// </summary>
        /// <param name="serviceEndpoint">要配置的终结点</param>
        /// <param name="clientCredentials">客户端凭据</param>
        static partial void ConfigureEndpoint(System.ServiceModel.Description.ServiceEndpoint serviceEndpoint, System.ServiceModel.Description.ClientCredentials clientCredentials);
        
        public XingHePlatformSoapClient(EndpointConfiguration endpointConfiguration) : 
                base(XingHePlatformSoapClient.GetBindingForEndpoint(endpointConfiguration), XingHePlatformSoapClient.GetEndpointAddress(endpointConfiguration))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public XingHePlatformSoapClient(EndpointConfiguration endpointConfiguration, string remoteAddress) : 
                base(XingHePlatformSoapClient.GetBindingForEndpoint(endpointConfiguration), new System.ServiceModel.EndpointAddress(remoteAddress))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public XingHePlatformSoapClient(EndpointConfiguration endpointConfiguration, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(XingHePlatformSoapClient.GetBindingForEndpoint(endpointConfiguration), remoteAddress)
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public XingHePlatformSoapClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress)
        {
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<XingHePlatform.CallLisInterfaceResponse> XingHePlatform.XingHePlatformSoap.CallLisInterfaceAsync(XingHePlatform.CallLisInterfaceRequest request)
        {
            return base.Channel.CallLisInterfaceAsync(request);
        }
        
        public System.Threading.Tasks.Task<XingHePlatform.CallLisInterfaceResponse> CallLisInterfaceAsync(string methodCode, string inParam)
        {
            XingHePlatform.CallLisInterfaceRequest inValue = new XingHePlatform.CallLisInterfaceRequest();
            inValue.Body = new XingHePlatform.CallLisInterfaceRequestBody();
            inValue.Body.methodCode = methodCode;
            inValue.Body.inParam = inParam;
            return ((XingHePlatform.XingHePlatformSoap)(this)).CallLisInterfaceAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<XingHePlatform.CallInterfaceResponse> XingHePlatform.XingHePlatformSoap.CallInterfaceAsync(XingHePlatform.CallInterfaceRequest request)
        {
            return base.Channel.CallInterfaceAsync(request);
        }
        
        public System.Threading.Tasks.Task<XingHePlatform.CallInterfaceResponse> CallInterfaceAsync(string headXml, string bodyXml)
        {
            XingHePlatform.CallInterfaceRequest inValue = new XingHePlatform.CallInterfaceRequest();
            inValue.Body = new XingHePlatform.CallInterfaceRequestBody();
            inValue.Body.headXml = headXml;
            inValue.Body.bodyXml = bodyXml;
            return ((XingHePlatform.XingHePlatformSoap)(this)).CallInterfaceAsync(inValue);
        }
        
        public virtual System.Threading.Tasks.Task OpenAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
        }
        
        public virtual System.Threading.Tasks.Task CloseAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginClose(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndClose));
        }
        
        private static System.ServiceModel.Channels.Binding GetBindingForEndpoint(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.XingHePlatformSoap))
            {
                System.ServiceModel.BasicHttpBinding result = new System.ServiceModel.BasicHttpBinding();
                result.MaxBufferSize = int.MaxValue;
                result.ReaderQuotas = System.Xml.XmlDictionaryReaderQuotas.Max;
                result.MaxReceivedMessageSize = int.MaxValue;
                result.AllowCookies = true;
                return result;
            }
            if ((endpointConfiguration == EndpointConfiguration.XingHePlatformSoap12))
            {
                System.ServiceModel.Channels.CustomBinding result = new System.ServiceModel.Channels.CustomBinding();
                System.ServiceModel.Channels.TextMessageEncodingBindingElement textBindingElement = new System.ServiceModel.Channels.TextMessageEncodingBindingElement();
                textBindingElement.MessageVersion = System.ServiceModel.Channels.MessageVersion.CreateVersion(System.ServiceModel.EnvelopeVersion.Soap12, System.ServiceModel.Channels.AddressingVersion.None);
                result.Elements.Add(textBindingElement);
                System.ServiceModel.Channels.HttpTransportBindingElement httpBindingElement = new System.ServiceModel.Channels.HttpTransportBindingElement();
                httpBindingElement.AllowCookies = true;
                httpBindingElement.MaxBufferSize = int.MaxValue;
                httpBindingElement.MaxReceivedMessageSize = int.MaxValue;
                result.Elements.Add(httpBindingElement);
                return result;
            }
            throw new System.InvalidOperationException(string.Format("找不到名称为“{0}”的终结点。", endpointConfiguration));
        }
        
        private static System.ServiceModel.EndpointAddress GetEndpointAddress(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.XingHePlatformSoap))
            {
                return new System.ServiceModel.EndpointAddress("http://************:16700/XingHePlatform.asmx");
            }
            if ((endpointConfiguration == EndpointConfiguration.XingHePlatformSoap12))
            {
                return new System.ServiceModel.EndpointAddress("http://************:16700/XingHePlatform.asmx");
            }
            throw new System.InvalidOperationException(string.Format("找不到名称为“{0}”的终结点。", endpointConfiguration));
        }
        
        public enum EndpointConfiguration
        {
            
            XingHePlatformSoap,
            
            XingHePlatformSoap12,
        }
    }
}
