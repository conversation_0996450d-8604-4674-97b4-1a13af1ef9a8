using H.Utility;
using Microsoft.AspNetCore.Http;
using XH.H82.Models.BusinessModuleClient;
using XH.H82.Models.Dtos;
using XH.H82.Models.Dtos.Certificate;
using XH.H82.Models.Entities;
using XH.H82.Models.Entities.Certificate;

namespace XH.H82.IServices.Certificate;

public interface ICertificateService
{
    /// <summary>
    /// 添加设备证书信息
    /// </summary>
    /// <param name="equipmentId"></param>
    /// <param name="cretificateType"></param>
    /// <param name="expiryDate"></param>
    /// <param name="cerDate"></param>
    /// <param name="cerWanrDate"></param>
    /// <returns></returns>
    public EMS_CERTIFICATE_INFO AddEquipmentCertificate(string equipmentId, string cretificateType,
        DateTime? expiryDate, DateTime cerDate, DateTime? cerWanrDate);
    /// <summary>
    /// 证书信息上传附件
    /// </summary>
    /// <param name="cretificateId"></param>
    /// <param name="file"></param>
    /// <returns></returns>
    public EMS_CERTIFICATE_INFO AddEquipmentCertificateAttachments(string cretificateId, UploadFileDto file);
    /// <summary>
    /// 编辑设备证书信息
    /// </summary>
    /// <param name="cretificateId"></param>
    /// <param name="cretificateType"></param>
    /// <param name="expiryDate"></param>
    /// <param name="cerDate"></param>
    /// <param name="cerWanrDate"></param>
    /// <returns></returns>
    /// <exception cref="BizException"></exception>
    public EMS_CERTIFICATE_INFO EditEquipmentCertificate(string cretificateId, string cretificateType,
        DateTime? expiryDate, DateTime cerDate, DateTime? cerWanrDate);
    /// <summary>
    /// 删除设备证书信息
    /// </summary>
    /// <param name="cretificateId"></param>
    public void DeleteEquipmentCertificate(string cretificateId);
    /// <summary>
    /// 删除证书信息附件
    /// </summary>
    /// <param name="id"></param>
    public void DeleteEquipmentCertificateAttachment(string id);
    /// <summary>
    /// 查询设备证书信息列表
    /// </summary>
    /// <param name="equipmentId"></param>
    /// <returns></returns>
    public List<EMS_CERTIFICATE_INFO> GetEquipmentCertificates(string equipmentId);
    public List<OA_BASE_DATA> GetEquipmentCertificateTypes(string? CertificateTypeName, string? state);
    public void DisableOrEnableCertificateType(string id);
    public void DeleteCertificateType(string id);
    public OA_BASE_DATA EditCertificateType(string id, string certificateTypeName, string? remark);
    public OA_BASE_DATA AddCertificateType(string certificateTypeName, string? remark);


    /// <summary>
    /// 查询名称
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public string GetCertificateTypeName(string id);


    /// <summary>
    /// 查询设备关联的供应商证书列表
    /// </summary>
    /// <param name="equipmentId"></param>
    /// <returns></returns>
    public List<CompanyCertificatDto> GetEquipmentCompanyCertificates(string equipmentId);


    /// <summary>
    /// 证书初始化
    /// </summary>
    void InItCertificates();

    /// <summary>
    /// 证书数据还原
    /// </summary>
    void ReductionInItCertificates();
}