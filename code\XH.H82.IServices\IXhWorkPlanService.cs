﻿using H.Utility;
using XH.H82.Models.Dtos;
using XH.H82.Models.Entities;

namespace XH.H82.IServices
{
    public interface IXhWorkPlanService
    {
        List<WorkPlanDto> GetWorkPlanList(string userNo, string hospitalId, string keyword, string mgroupId, string equipmentClass, string labId, string pgroupId, string areaId);
        List<EmsWorkPlanDto> GetWorkPlans(H.Utility.ClaimsDto user, string keyword, string mgroupId, string equipmentClass, string labId, string pgroupId, string areaId);


        /// <summary>
        /// 提交工作计划
        /// </summary>
        /// <param name="submitUser">提交人</param>
        /// <param name="planId">工作计划ID</param>
        /// <param name="AuditorName">审核人</param>
        /// <param name="AuditorId">审核人ID</param>
        void SubmitPlan(H.Utility.ClaimsDto submitUser, string planId, string AuditorName, string AuditorId);

        /// <summary>
        /// 审核工作计划
        /// </summary>
        /// <param name="AuditorUser">审核人</param>
        /// <param name="planId">工作计划id</param>
        /// <param name="content">审核意见</param>
        void AuditPlan(H.Utility.ClaimsDto AuditorUser, string planId, string? content);

        /// <summary>
        /// 工作计划被驳回
        /// </summary>
        /// <param name="AuditorUser">审核人</param>
        /// <param name="planId">工作计划id</param>
        /// <param name="content">驳回内容</param>
        void OverrulPlan(H.Utility.ClaimsDto AuditorUser, string planId, string? content);

        /// <summary>
        /// 查询工作计划操作记录列表
        /// </summary>
        /// <param name="planId">工作计划id</param>
        /// <returns></returns>
        List<EmsWorkPlanCirculationRecordDto> GetEmsWordPlanCirculationRecords(string planId);

        /// <summary>
        /// 工作计划树
        /// </summary>
        /// <param name="userNo"></param>
        /// <param name="hospitalId"></param>
        /// <param name="labId"></param>
        /// <param name="areaId"></param>
        /// <returns></returns>
        List<NewOATreeDto> GetMgroupList(string userNo, string hospitalId, string labId, string areaId);


        /// <summary>
        /// 工作计划设备类型节点树
        /// </summary>
        /// <param name="userNo"></param>
        /// <param name="hospitalId"></param>
        /// <param name="labId"></param>
        /// <param name="areaId"></param>
        /// <returns></returns>
        public NewEquipClassTreeDto GetEquipmentClassList(string userNo, string hospitalId,string labId, string areaId);




    }

}
