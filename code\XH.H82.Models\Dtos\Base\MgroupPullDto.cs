﻿using AutoMapper;
using AutoMapper.Configuration.Annotations;

namespace XH.H82.Models.Dtos.Base
{
    [AutoMap(typeof(MgroupNode))]
    public class MgroupPullDto
    {
        [SourceMember("mkey")]
        public string MGROUP_ID { get; set; }
        [SourceMember("mvalue")]
        public string MGROUP_NAME { get; set; }
        [SourceMember("PDropDowDto")]
        public List<PgroupPullDto> PgroupPull { get; set; }
    }

    [AutoMap(typeof(PDropDowDto))]
    public class PgroupPullDto
    {
        [SourceMember("key")]
        public string PGROUP_ID { get; set; }
        [SourceMember("value")]
        public string PGROUP_NAME { get; set; }
    }

    public class MgroupNode
    {
        public string mkey { get; set; }
        public string mvalue { get; set; }
        public string TYPE_NAME { get; set; }
        public List<PDropDowDto> PDropDowDto { get; set; }
    }

    public class PDropDowDto
    {
        public string key { get; set; }
        public string value { get; set; }
        public string TYPE_NAME { get; set; }
    }
}
