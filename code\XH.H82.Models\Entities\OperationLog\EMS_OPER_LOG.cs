﻿using H.Utility.SqlSugarInfra;
using SqlSugar;

namespace XH.H82.Models.Entities.OperationLog
{
    /// <summary>
    /// 操作记录表
    /// </summary>
    [SugarTable("EMS_OPER_LOG")]
    [DBOwner("XH_OA")]
    public class EMS_OPER_LOG
    {
        [SugarColumn(IsPrimaryKey = true)]
        /// <summary>
        /// 操作记录主键
        /// </summary>
        public string OPER_LOGID { get; set; }
        /// <summary>
        /// 数据id
        /// </summary>
        public string OPER_MAIN_ID { get; set; }
        /// <summary>
        /// 操作名称
        /// </summary>
        public string OPER_NAME { get; set; }
        /// <summary>
        /// 操作人主键
        /// </summary>
        public string OPER_PERSON_ID { get; set; }
        /// <summary>
        /// 操作人名
        /// </summary>
        public string OPER_PERSON { get; set; }
        /// <summary>
        /// 下一个操作人id
        /// </summary>
        public string NEXT_OPER_PERSON_ID { get; set; }
        /// <summary>
        /// 下一个操作人名
        /// </summary>
        public string NEXT_PER_PERSON { get; set; }
        /// <summary>
        /// 前一个操作记录id    操作id为0时  代表是第一次操作
        /// </summary>
        public string PREV_OPER_ID { get; set; }
        /// <summary>
        /// 操作状态
        /// </summary>

        public OperationStateEnum OPER_STATE { get; set; }
        /// <summary>
        /// 操作意见
        /// </summary>
        public string? OPER_CONTENT { get; set; }

        public string FIRST_RPERSON { get; set; }
        public DateTime FIRST_RTIME { get; set; }
        public string LAST_MPERSON { get; set; }
        public DateTime LAST_MTIME { get; set; }
        public string? REMARK { get; set; }

        public EMS_OPER_LOG()
        {
        }




    }
}
