--2024/8/28 cxp  增加墨水屏设计模板表
CREATE TABLE XH_OA.EMS_INKSCREEN_TEMPLATE(
    TEMPLATE_ID VARCHAR2(50) NOT NULL,
    TEMPLATE_NAME VARCHAR2(200) NOT NULL,
    TEMPLATE_CONTENT CLOB,
    TEMPLATE_TITLE VARCHAR2(200),
    SET_ABNORMAL NUMBER(1) DEFAULT  1 NOT NULL,
    SET_QR_CODE NUMBER(1) DEFAULT  0 NOT NULL,
    SET_WIREFRAME NUMBER(1) DEFAULT  1 NOT NULL,
    TEMPLATE_STATE VARCHAR2(10) DEFAULT  '1' NOT NULL,
    PGROUP_SID VARCHAR2(1000),
    FIRST_RPERSON VARCHAR2(50),
    FIRST_RTIME DATE,
    LAST_MPERSON VARCHAR2(50),
    LAST_MTIME DATE,
    REMARK VARCHAR2(200)
);

alter table XH_OA.EMS_INKSCREEN_TEMPLATE add constraint PK_OA_EMS_INKSCREEN_TEMPLATE primary key (TEMPLATE_ID);

COMMENT ON TABLE XH_OA.EMS_INKSCREEN_TEMPLATE IS 'EMS_INKSCREEN_TEMPLATE';
COMMENT ON COLUMN XH_OA.EMS_INKSCREEN_TEMPLATE.TEMPLATE_ID IS '模板id';
COMMENT ON COLUMN XH_OA.EMS_INKSCREEN_TEMPLATE.TEMPLATE_NAME IS '模板名称';
COMMENT ON COLUMN XH_OA.EMS_INKSCREEN_TEMPLATE.TEMPLATE_CONTENT IS '模板内容';
COMMENT ON COLUMN XH_OA.EMS_INKSCREEN_TEMPLATE.TEMPLATE_TITLE IS '标题';
COMMENT ON COLUMN XH_OA.EMS_INKSCREEN_TEMPLATE.SET_ABNORMAL IS '是否设置异常状态亮灯;0否  1是';
COMMENT ON COLUMN XH_OA.EMS_INKSCREEN_TEMPLATE.SET_QR_CODE IS '是否设置二维码;0否  1是';
COMMENT ON COLUMN XH_OA.EMS_INKSCREEN_TEMPLATE.SET_WIREFRAME IS '是否设置表格线框;0否  1是';
COMMENT ON COLUMN XH_OA.EMS_INKSCREEN_TEMPLATE.TEMPLATE_STATE IS '模板状态 0禁用，1在用，2删除';
COMMENT ON COLUMN XH_OA.EMS_INKSCREEN_TEMPLATE.PGROUP_SID IS '应用的专业组的id';
COMMENT ON COLUMN XH_OA.EMS_INKSCREEN_TEMPLATE.FIRST_RPERSON IS '首次操作人';
COMMENT ON COLUMN XH_OA.EMS_INKSCREEN_TEMPLATE.FIRST_RTIME IS '首次创建时间';
COMMENT ON COLUMN XH_OA.EMS_INKSCREEN_TEMPLATE.LAST_MPERSON IS '最后一次操作人';
COMMENT ON COLUMN XH_OA.EMS_INKSCREEN_TEMPLATE.LAST_MTIME IS '最后一次操作时间';
COMMENT ON COLUMN XH_OA.EMS_INKSCREEN_TEMPLATE.REMARK IS '备注';

grant select, insert, update, delete on XH_OA.EMS_INKSCREEN_TEMPLATE to XH_COM;
