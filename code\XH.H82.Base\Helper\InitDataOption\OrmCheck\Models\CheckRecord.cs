﻿using H.Utility;
using SqlSugar;

namespace XH.H82.Base.Helper.InitDataOption.OrmCheck.Models
{
    public class CheckRecord
    {
        private Dictionary<Type, TableCheck> _TableChecks { get; set; }

        public List<TableCheck> TableChecks
        {
            get
            {
                return _TableChecks.Values.ToList();
            }
        }

        public CheckRecord()
        {
            _TableChecks = new Dictionary<Type, TableCheck>();
        }

        public IEnumerable<KeyValuePair<Type, TableCheck>> GetPreCheckTableTypes()
        {
            var perCheckTables = _TableChecks.Where(x => x.Value.Check.checkResult);

            return perCheckTables;
        }


        public void AddTableCheckRecord(Type modelType, string table, string result, bool isPass = false)
        {
            _TableChecks.TryAdd(modelType, new TableCheck(table, result, isPass));
        }

        public void AddColumnCheckRecord(Type modelType, string table, string column, string result, bool isPass = false)
        {
            var tableCheck = _TableChecks.GetOrAdd(modelType, () =>
              {
                  var newTableCheck = new TableCheck(table, $"校验成功：模型{table}存在表", true);
                  return newTableCheck;
              });
            tableCheck.ColumnsChecks.Add(new ColumnCheck(column, result, isPass));
        }
    }
}
