﻿using H.Utility.SqlSugarInfra;
using SqlSugar;

namespace XH.H82.Models.Entities.SMBL;

[DBOwner("XH_SMBL", "")]
public class SMBL_LAB
{
    [SugarColumn(IsPrimaryKey = true)]
    public string SMBL_LAB_ID { get; set; }

    public string HOSPITAL_ID { get; set; }

    public string SMBL_LAB_CODE { get; set; }

    public string SMBL_LAB_NAME { get; set; }

    public string SMBL_LAB_CNAME { get; set; }

    public string SMBL_LAB_TYPE { get; set; }

    public int IF_ANIMAL { get; set; }

    public string PROTECTION_LEVEL { get; set; }

    public string SMBL_LAB_LICENSE { get; set; }

    public string SMBL_LAB_PERSON { get; set; }

    public string SMBL_LAB_PHONE { get; set; }

    public string HOSPITAL_TYPE { get; set; }

    public string AREA_ID { get; set; }

    public string LAB_ID { get; set; }

    public string PGROUP_SID { get; set; }

    public DateTime CHECK_TIME { get; set; }

    public string CHECK_DEPT { get; set; }

    public DateTime APPLY_RECORD_TIME { get; set; }

    public string APPLY_RECORD_PERSON { get; set; }

    public string APPLY_HOSPITAL { get; set; }

    public string APPLY_HOSPITAL_ADDRESS { get; set; }

    public string RECORD_STATE { get; set; }

    public string SMBL_LAB_STATE { get; set; }

    public string FIRST_RPERSON { get; set; }

    public DateTime FIRST_RTIME { get; set; }

    public string LAST_MPERSON { get; set; }

    public DateTime LAST_MTIME { get; set; }

    public string REMARK { get; set; }
    public DateTime UPDATE_TIME { get; set; }
}