﻿using System.Diagnostics;
using H.<PERSON>;
using H.Utility;
using H.Utility.Helper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using RestSharp;
using Serilog;

namespace XH.H82.Models.BusinessModuleClient.H04;

/// <summary>
/// 工具箱客户端
/// </summary>
public class H04Client
{
    private readonly IHttpContextAccessor _httpContext;
    private readonly IConfiguration _configuration;
    private RestClient _clientH04;

    public H04Client(IConfiguration configuration, IHttpContextAccessor httpContext)
    {
        _configuration = configuration;
        _httpContext = httpContext;
    }
    private RestClient ClientH04
    {
        get
        {
            if (_clientH04 == null)
            {
                try
                {
                    var addressH04 = _configuration["H04-13"];
                    _clientH04 = new RestClient(new RestClientOptions()
                    {
                        RemoteCertificateValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true,
                        BaseUrl = new Uri(addressH04),
                        ThrowOnAnyError = true
                    });
                }
                catch (Exception ex)
                {
                    Log.Error(ex.ToString());
                    throw  new BizException($"获取地址异常:{ex.Message}");
                }
            }
            return _clientH04;
        }
    }
    
    private T H04Request<T>(string url  ,Method method , object? body = null) where T : class
    { 
        Stopwatch sw = new Stopwatch();
        sw.Start();
        var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();
        var request = new RestRequest(url);
        request.AddHeader("Authorization", token);
        if (body is  not null)
        {
            request.AddBody(body);
        }
        var response =  method switch
        {
            Method.Get => ClientH04.ExecuteGet<ResultDto<T>>(request),
            Method.Post => ClientH04.ExecutePost<ResultDto<T>>(request),
            Method.Put => ClientH04.ExecutePut<ResultDto<T>>(request),
            Method.Delete => ClientH04.ExecuteDelete<ResultDto<T>>(request),
            _ => throw new BizException("不支持的请求方法")
        };
        sw.Stop();
        Log.ForContext("elapsed", sw.ElapsedMilliseconds)
            .Information($"调用H04模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
        if (!response.IsSuccessful)
        {
            Log.Error($"调用H04模块[{url}]发生错误:{response.ErrorException}");
            throw new BizException($"调用H04模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
        }
        if (response.Data.success)
        {
            Log.Information($"调用H04模块{url}请求完成,返回了数据:" + JsonConvert.SerializeObject(response.Data.data, Formatting.Indented));
            return response.Data.data;
        }
        Log.Error($"调用H04模块[{url}]请求完成,但是返回了错误:" + response.Data.msg);
        throw new BizException($"调用H04模块[{url}]请求完成,但是返回了错误:" + response.Data.msg);
    }
    
    /// <summary>
    /// 清空工具箱缓存
    /// </summary>
    /// <returns></returns>
    public object ClearCache() 
    {
        var user = _httpContext.HttpContext.User.ToClaimsDto();
        var  url =
            $"/externalapi/External/ClearCache?hospitalId={user.HOSPITAL_ID}&moduleId={AppSettingsProvider.CurrModuleId}";
        return H04Request<object>(url, Method.Get);
    }


    /// <summary>
    /// 保存工具库字段
    /// </summary>
    /// <param name="body"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    public object SaveFieldDict(FieldDictDto body)
    {
        var url = $"/externalapi/External/SaveFieldDict";
        return H04Request<object>(url,Method.Post,body);
    }
    /// <summary>
    /// 创建表单模板
    /// </summary>
    /// <param name="json"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    public object CreateComplexForms(TagTemplateDto body)
    {
        var url = $"/externalapi/External/CreateComplexForms";
        return H04Request<object>(url,Method.Post,body);
    }
    /// <summary>
    /// 获取工具库表单字段
    /// </summary>
    /// <param name="filedClass"></param>
    /// <returns></returns>
    public List<object> GetFieldDicts(string filedClass)
    {
        var user = _httpContext.HttpContext.User.ToClaimsDto();
        var  url = $"/externalapi/External/GetFieldDicts?hospitalId={user.HOSPITAL_ID}&moduleId={AppSettingsProvider.CurrModuleId}&fieldClass={filedClass}";
        return H04Request<List<object>>(url,Method.Get);
    }
    /// <summary>
    /// 获取复杂表单信息
    /// </summary>
    /// <param name="setUpId"></param>
    /// <param name="merge"></param>
    /// <param name="qunitId"></param>
    /// <returns></returns>
    public ComplexFormDto GetComplexForms(string setUpId, string merge, string qunitId)
    {
        var user = _httpContext.HttpContext.User.ToClaimsDto();
        var  url = $"/externalapi/External/GetComplexForms?hospitalId={user.HOSPITAL_ID}&moduleId={AppSettingsProvider.CurrModuleId}&setupId={setUpId}&merge={merge}&qunitID={qunitId}";
        return H04Request<ComplexFormDto>(url,Method.Get);
    }
}