﻿using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using H.Utility.SqlSugarInfra;
using SqlSugar;
using System.Reflection;
using XH.H82.Base.Helper.InitDataOption.OrmCheck.Models;

namespace XH.H82.Base.Helper.InitDataOption.OrmCheck.Provider
{
    public class OrmCheckProvider<Context> where Context : SugarUnitOfWork
    {
        ISqlSugarUow<Context> DbContext { get; set; }

        public OrmCheckProvider(ISqlSugarUow<Context> dbContext) => DbContext = dbContext;

        /// <summary>
        /// 播种函数
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="dataSeeds"></param>
        /// <returns></returns>
        public async Task Seed<T>(List<T> dataSeeds) where T : class, new()
        {
            using (var uow = DbContext.Db.CreateContext())
            {
                var result = await DbContext.Db.GetSimpleClient<T>().InsertOrUpdateAsync(dataSeeds);
                if (result)
                {
                    uow.Commit();
                }
            }
        }

        /// <summary>
        /// 检查执行的程序集或命名空间下的sqlsugar模型与数据库是否一致
        /// </summary>
        /// <param name="dllName"> 程序集名称 </param>
        /// <param name="nameSpace"> 程序集下面的命名空间 </param>
        /// <param name="isCommit"> 是否提交事务 默认不提交</param>
        /// <returns></returns>
        public async Task<CheckRecord> CheckDllOrms(string dllName, string nameSpace = null, bool isCommit = false)
        {

            var assembly = Assembly.Load(dllName);
            var types = assembly.GetTypes()
                .Where(x => x.CustomAttributes.Any(attributeData => attributeData.AttributeType == typeof(DBOwnerAttribute)))
                .Where(x => !x.CustomAttributes.Any(attributeData => attributeData.ConstructorArguments.Any(x => x.Value.ToString() == "XH_SYS")))
                .WhereIF(nameSpace is not null, x => x.Namespace!.Contains($"{dllName}.{nameSpace}"));

            var checkRecord = new CheckRecord();
            // 遍历所有类型
            foreach (Type type in types)
            {
                CheckOrmModel(checkRecord, type);
            }

            using (var uow = DbContext.Db.CreateContext())
            {
                await CheckOrmModelCoulmnsIsRightAsync(checkRecord);

                if (isCommit)
                {
                    uow.Commit();
                }

            };
            return checkRecord;
        }

        /// <summary>
        /// 检查指定sqlsugar实体类型与数据库是否匹配
        /// </summary>
        /// <param name="checkRecord"> 检查结果对象 </param>
        /// <param name="modelType">模型的type </param>
        public void CheckOrmModel(CheckRecord checkRecord, Type modelType)
        {
            CheckOrmModelIsExistTable(checkRecord, modelType);
        }

        /// <summary>
        /// 检查指定sqlsugar实体类型与数据库是否匹配
        /// </summary>
        /// <typeparam name="T"> sqlsugarTable </typeparam>
        /// <param name="checkRecord"> 检查结果对象 </param>
        public void CheckOrmModelIsExistTable<T>(CheckRecord checkRecord)
        {
            CheckOrmModelIsExistTable(checkRecord, typeof(T));
        }

        /// <summary>
        /// 检查指定sqlsugar实体类型与数据库是否匹配
        /// </summary>
        /// <param name="checkRecord"> 检查结果对象 </param>
        /// <param name="modelInfo">sqlsugarTable 类型的实体</param>
        private void CheckOrmModelIsExistTable(CheckRecord checkRecord, Type modelInfo)
        {
            var ormAttribute = modelInfo.Name;
            var tableOwner = modelInfo.CustomAttributes.FirstOrDefault(x => x.AttributeType == typeof(DBOwnerAttribute));
            if (ormAttribute is null)
            {
                checkRecord.AddTableCheckRecord(modelInfo, modelInfo.Name, $"校验失败：模型{modelInfo.Name}不存在数据库映射特性");
                return;
            }
            var sugarTableName = modelInfo.Name;
            var dbOwnerName = tableOwner!.ConstructorArguments.FirstOrDefault().Value as string;
            var fullName = dbOwnerName is null ? $"{sugarTableName}" : $"{dbOwnerName}.{sugarTableName}";

            var entityInfo = DbContext.Db.EntityMaintenance.GetEntityInfoNoCache(modelInfo);
            var tableName = entityInfo.DbTableName;

            if (tableName.IsNullOrEmpty())
            {
                checkRecord.AddTableCheckRecord(modelInfo, fullName, $"校验失败：数据库缺失{fullName}表");
                return;
            }
            if (fullName != tableName)
            {
                checkRecord.AddTableCheckRecord(modelInfo, fullName, $"校验失败：模型{modelInfo.Name}与数据表名不匹配");
                return;
            }
            checkRecord.AddTableCheckRecord(modelInfo, fullName, $"校验成功：模型{modelInfo.Name}存在表", true);
        }

        /// <summary>
        /// 通过插入数据并删除数据来检测业务数据执行是否满足表结构
        /// </summary>
        /// <param name="checkRecord"></param>
        /// <returns></returns>
        public async Task CheckOrmModelCoulmnsIsRightAsync(CheckRecord checkRecord)
        {
            var tables = checkRecord.GetPreCheckTableTypes();
            foreach (var table in tables)
            {
                try
                {
                    var testData = CreateInitObjectData(table.Key);
                    var result = await DbContext.Db.InsertableByObject(testData).AS(table.Value.TableName).ExecuteCommandAsync() > 0;
                    if (result)
                    {
                        table.Value.Check.checkMsg = "成功插入测试数据！";
                        DbContext.Db.DeleteableByObject(testData).AS(table.Value.TableName).ExecuteCommand();
                    }
                    else
                    {
                        throw new Exception("插入失败");
                    }
                }
                catch (Exception e)
                {
                    table.Value.Check.checkMsg = e.InnerException is null ? e.Message : e.InnerException!.Message;
                    table.Value.Check.checkResult = false;
                }
            }

        }

        /// <summary>
        /// 遍历实体属性检查与数据库列名是否一致
        /// </summary>
        /// <param name="checkRecord"></param>
        /// <returns></returns>
        public async Task CheckOrmModelCoulmnsIsExistAsync(CheckRecord checkRecord)
        {
            var tables = checkRecord.GetPreCheckTableTypes();
            foreach (var table in tables)
            {
                try
                {
                    var entity = DbContext.Db.EntityMaintenance.GetEntityInfo(table.Key);
                    foreach (var columInfo in entity.Columns)
                    {
                        CheckModelColumnIsExist(table, columInfo.PropertyName);
                    }

                }
                catch (Exception e)
                {
                    table.Value.Check.checkMsg = e.InnerException is null ? e.Message : e.InnerException!.Message;
                    table.Value.Check.checkResult = false;
                }


            }

        }
        /// <summary>
        /// 检查表中的列与实体是否一致   需要链接账号权限够高
        /// </summary>
        /// <param name="table"></param>
        /// <param name="propertyName"></param>
        public void CheckModelColumnIsExist(KeyValuePair<Type, TableCheck> table, string propertyName)
        {
            try
            {
                var isExistColumn = DbContext.Db.DbMaintenance.IsAnyColumn(table.Value.TableName, propertyName);
                table.Value.ColumnsChecks.Add(new ColumnCheck(propertyName, isExistColumn ? $"{propertyName}正确" : $"实体属性{propertyName}与数据表列名不匹配", isExistColumn));
            }
            catch (Exception e)
            {
                table.Value.ColumnsChecks.Add(new ColumnCheck(table.Value.TableName, e.Message, false));
                if (table.Value.Check.checkResult)
                {
                    table.Value.Check.checkResult = false;
                    table.Value.Check.checkMsg = "当前数据表存在列缺失";
                }
            }
        }

        /// <summary>
        /// 构造初始化对象
        /// </summary>
        /// <param name="type"> class type</param>
        /// <returns></returns>
        private object CreateInitObjectData(Type type)
        {
            var instance = Activator.CreateInstance(type);
            SetDefaultPropertyValues(instance);
            return instance;
        }

        /// <summary>
        /// 给初始化对象的属性添加自定义默认值值
        /// </summary>
        /// <param name="instance"> Init ocject </param>
        private void SetDefaultPropertyValues(object instance)
        {
            var properties = instance.GetType().GetProperties();
            foreach (PropertyInfo property in properties)
            {
                if (property.CanWrite)
                {
                    object defaultValue = GetDefaultValue(property.PropertyType);
                    property.SetValue(instance, defaultValue);
                }
            }
        }
        /// <summary>
        /// 获取属性类型的默认值
        /// </summary>
        /// <param name="type"> property type </param>
        /// <returns></returns>
        private object GetDefaultValue(Type type)
        {
            if (type == typeof(int) || type == typeof(int?))
            {
                return 1;
            }
            else if (type == typeof(string))
            {
                return "0";
            }
            else if (type == typeof(bool) || type == typeof(bool?))
            {
                return false;
            }
            else if (type == typeof(DateTime) || type == typeof(DateTime?))
            {
                return DateTime.Now;
            }

            else if (type == typeof(double) || type == typeof(double?))
            {
                return (double)1;
            }
            else if (type == typeof(float) || type == typeof(float?))
            {
                return (float)1;
            }
            else if (type == typeof(decimal) || type == typeof(decimal?))
            {
                return 1.00m;
            }
            else if (type == typeof(char) || type == typeof(char?))
            {
                return '0';
            }
            else if (type == typeof(byte) || type == typeof(byte?))
            {
                return (byte)1;
            }
            else if (type == typeof(sbyte) || type == typeof(sbyte?))
            {
                return (sbyte)1;
            }
            else if (type == typeof(short) || type == typeof(short?))
            {
                return (short)1;
            }
            else if (type == typeof(ushort) || type == typeof(ushort?))
            {
                return (ushort)1;
            }
            else if (type == typeof(uint) || type == typeof(uint?))
            {
                return (uint)1;
            }
            else if (type == typeof(long) || type == typeof(long?))
            {
                return (long)1;
            }
            else if (type == typeof(ulong))
            {
                return (ulong)1;
            }
            else
            {
                return null;
            }
        }
    }
}
