﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using H.Utility;
using H.Utility.Helper;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Serilog;

namespace XH.H82.API.ScheduleJobs
{

    public class AutoClearLogFile : IHostedService, IDisposable
    {
        private readonly IConfiguration _configuration;
        private Timer _timer;
        public AutoClearLogFile(IConfiguration configuration)
        {
            _configuration = configuration;
        }
        public void Dispose()
        {
            _timer?.Dispose();
        }

        public Task StartAsync(CancellationToken cancellationToken)
        {

            var clearDayBefore =int.Parse(_configuration["LogKeepDays"]) ;
            if (clearDayBefore==0)
            {
                Log.Information("==>[自动日志清理]未开启.");

                return Task.CompletedTask;
            }
            else
            {
                Log.Information("==>[自动日志清理]已开启.日志保留天数:"+clearDayBefore);

                _timer = new Timer(DoWork, clearDayBefore, TimeSpan.Zero,
                    TimeSpan.FromDays(1));
            }
            return Task.CompletedTask;
        }

        public Task StopAsync(CancellationToken cancellationToken)
        {
            _timer?.Change(Timeout.Infinite, 0);
            return Task.CompletedTask;
        }

        private async void DoWork(object dayBefore)
        {
            string path = Path.Combine(AppContext.BaseDirectory, "logs");
            Log.Information("==>[当前日志文件路径]:"+path);
            DirectoryInfo root = new DirectoryInfo(path);
            if (root.Exists)
            {
                DateTime dtNow = DateTime.Now;
                int day = Convert.ToInt32(dayBefore);
                try
                {
                    foreach (var folder in root.GetDirectories())
                    {
                        //遍历文件
                        foreach (FileInfo NextFile in folder.GetFiles())
                        {
                            if ((dtNow - NextFile.CreationTime).TotalDays > day)
                            {
                                FileHelper.DeleteIfExists(NextFile.FullName);
                            }
                        }
                    }

                }
                catch (Exception e)
                {
                    Log.Error(e, "==>清理日志时发生错误" + e.Message);
                }

            }
            else
            {
                Log.Warning("日志路径不存在" );
            }

        }
    }
}
