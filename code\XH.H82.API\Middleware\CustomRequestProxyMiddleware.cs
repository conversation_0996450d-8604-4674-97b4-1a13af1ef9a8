﻿using H.Utility;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using RestSharp;
using Serilog;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;

namespace XH.H82.API.Middleware
{
    public class CustomRequestProxyMiddleware
    {

        private readonly RequestDelegate _next;
        private readonly string _module;
        private readonly string _router;
        private readonly string _replace;

        public CustomRequestProxyMiddleware(RequestDelegate next, string module, string router, string replace)
        {
            _next = next;
            _module = module;
            _router = router;
            _replace = replace;
        }

        public async Task Invoke(HttpContext context, IConfiguration configuration, IHttpContextAccessor httpContextAccessor)
        {
            if (context.Request.Path.StartsWithSegments(_router))
            {
                var _targetBaseUrl = configuration.GetSection(_module).Value;
                // 获取原始请求路径
                var originalPath = context.Request.Path.Value.Replace(_router, _replace);

                // 构建目标请求的完整 URL
                var targetUrl = $"{_targetBaseUrl}{originalPath}{context.Request.QueryString.Value}";

                // 创建 RestRequest
                var restRequest = new RestRequest($"{targetUrl}", ConvertMethod(context.Request.Method));

                //复制请求头
                foreach (var item in context.Request.Headers)
                {
                    restRequest.AddOrUpdateHeader(item.Key.Replace(":", ""), item.Value.ToString());
                }
                // 复制请求正文（如果有）
                if (context.Request.ContentLength != null && context.Request.ContentLength > 0)
                {
                    restRequest.AddParameter("application/json", await context.Request.ReadFormAsync(), ParameterType.RequestBody);
                }

                using RestClient client = new RestClient(new RestClientOptions
                {
                    RemoteCertificateValidationCallback = (object sender, X509Certificate? certificate, X509Chain? chain, SslPolicyErrors sslPolicyErrors) => true,
                    ThrowOnAnyError = true
                });

                try
                {
                    var response = await client.ExecuteAsync(restRequest);
                    context.Response.StatusCode = (int)response.StatusCode;
                    context.Response.ContentType = response.ContentType;
                    await context.Response.Body.WriteAsync(response.RawBytes, 0, response.RawBytes.Length);
                    return;
                }
                catch (Exception e)
                {
                    var result = new ResultDto();
                    result.success = false;
                    result.msg = $"请求H115：{context.Request.Path},异常：{e.Message}";
                    context.Response.StatusCode = 200;
                    Log.Error($"{result.msg}，完整url：{targetUrl}");
                    await context.Response.WriteAsJsonAsync(result);
                    return;
                }
                // 发送请求并获取响应
            }
            await _next(context);
        }

        private Method ConvertMethod(string method)
        {
            return method.ToUpper() switch
            {
                "GET" => Method.Get,
                "POST" => Method.Post,
                "PUT" => Method.Put,
                "DELETE" => Method.Delete,
                "PATCH" => Method.Patch,
                _ => throw new NotImplementedException($"HTTP method {method} is not supported.")
            };
        }
    }
}
