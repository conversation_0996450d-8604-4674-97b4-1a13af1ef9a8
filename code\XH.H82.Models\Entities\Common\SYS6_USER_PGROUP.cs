﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Security.Cryptography;
using H.Utility.SqlSugarInfra;
using SqlSugar;

namespace XH.H82.Models.Entities.Common
{
    [DBOwner("XH_SYS")]
    [SugarTable("SYS6_USER_PGROUP")]
    public class SYS6_USER_PGROUP
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string? USER_NO { get; set; }
        public string? PGROUP_ID { get; set; }
        public string? LAB_ID { get; set; }
        public string? PGROUP_USORT { get; set; }
        public string? USER_PSTATE { get; set; }
        public string? REMARK { get; set; }
    }
}
