﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.Demo
{

    /// <summary>
    /// 用户学习记录
    /// </summary>
    public class UserLearningRecords
    {
        /// <summary>
        /// 岗位名称
        /// </summary>
        public string PostName { get; set; }

        /// <summary>
        /// 岗位Id
        /// </summary>
        public string PostId { get; set; }

        /// <summary>
        /// 学习任务列表
        /// </summary>
        public List<LearningTask> Tasks { get; set; } = new();
    }

    /// <summary>
    /// 学习任务
    /// </summary>
    public class LearningTask
    {
        /// <summary>
        /// 任务名称
        /// </summary>
        public string TaskName { get; set; }

        /// <summary>
        /// 任务内容列表
        /// </summary>
        public List<TaskContent> TaskContents { get; set; } = new();
    }

    /// <summary>
    /// 任务内容
    /// </summary>
    public class TaskContent
    {
        /// <summary>
        /// 当前状态
        /// </summary>
        public string State { get; set; } = "学习中";
        /// <summary>
        /// 学习文件名
        /// </summary>
        public string FileName { get; set; } = "";
        /// <summary>
        /// 已学习的时长
        /// </summary>
        public long LearnedTime { get; set; } = 0;

        /// <summary>
        /// 总时长
        /// </summary>
        public long TotalTime { get; set; } = 0;

    }

    public class LearningDto
    {
        public string PostName { get; set; } = "";
        public string PostId { get; set; } = "";
        [JsonProperty("LEARNING_STATE")]
        public bool LearningState { get; set; }

        [JsonProperty("DOC_TYPE")]
        public long DocType { get; set; }

        [JsonProperty("DOC_TYPE_NAME")]
        public string DocTypeName { get; set; }

        [JsonProperty("DOC_ID")]
        public long DocId { get; set; }

        [JsonProperty("DOC_NAME")]
        public string DocName { get; set; }

        [JsonProperty("STUDY_DURATION")]
        public long StudyDuration { get; set; }

        [JsonProperty("LEARNING_TIME")]
        public long LearningTime { get; set; }

        [JsonProperty("BROWSE_START_TIME")]
        public DateTime? BrowseStartTime { get; set; }

        [JsonProperty("BROWSE_END_TIME")]
        public DateTime? BrowseEndTime { get; set; }

        [JsonProperty("PGROUP_ID")]
        public string PgroupId { get; set; }

        [JsonProperty("PGROUP_NAME")]
        public string PgroupName { get; set; }

        [JsonProperty("CLASS_ID")]
        public string ClassId { get; set; }

        [JsonProperty("CLASS_NAME")]
        public string ClassName { get; set; }

        [JsonProperty("LEARNING_STATE_NAME")]
        public string LearningStateName { get; set; }

    }

}
