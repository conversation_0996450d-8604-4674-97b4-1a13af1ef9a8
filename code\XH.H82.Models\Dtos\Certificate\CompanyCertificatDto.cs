using XH.H82.Models.BusinessModuleClient;

namespace XH.H82.Models.Dtos.Certificate;

public class CompanyCertificatDto 
{
    /// <summary>
    /// id
    /// </summary>
    public string Id { get; set; }
    /// <summary>
    /// 供应商ID
    /// </summary>
    public string CompanyId { get; set; }
    /// <summary>
    /// 供应商名称
    /// </summary>
    public string  CompanyName { get; set; }

    /// <summary>
    /// 证书类型
    /// </summary>
    public string CertificateTypeName { get; set; }

    /// <summary>
    /// 天数（长期为0） 正常、临期则为剩余多少天过期   超期则为超期天数  报废直接忽略
    /// </summary>
    public int Day { get; set; } = 0;

    /// <summary>
    /// 当前状态
    /// </summary>
    public string CertificateStatus { get; set; }
    /// <summary>
    /// 过去时间
    /// </summary>
    public DateTime? ExpiryDate { get; set; }

    /// <summary>
    /// 发证日期
    /// </summary>
    public DateTime? CerDate { get; set; }
    
    /// <summary>
    /// 提醒时间
    /// </summary>
    public DateTime? CerWarnDate { get; set; }

    /// <summary>
    /// 证书附件
    /// </summary>
    public List<FileRecordInfo> Attachments { get; set; } = new List<FileRecordInfo>();


    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="dto"></param>
    public CompanyCertificatDto(string companyName, CertificatDto dto)
    {
        Id = dto.Id;
        CompanyId = dto.CompanyId;
        CompanyName = companyName;
        CertificateTypeName = dto.FileTypeName;
        CertificateStatus = dto.StateCer switch
        {
            "ACTIVE"=>"正常",
            "PRE_EXPIRE"=>"临近过期",
            "EXPIRE" => "超期",
            "INVALID"=>"作废",
            _=>"正常"
        };;
        ExpiryDate = dto.FileExpiryDate;
        CerDate = dto.cerData;
        CerWarnDate = dto.CerWarnDate;
        Day = dto.FileExpiryDate.HasValue ? Math.Abs((dto.FileExpiryDate.Value - DateTime.Now).Days) : 0;
        Attachments = dto.GetFileRecords();
    }
}