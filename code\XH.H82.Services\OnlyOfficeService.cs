﻿using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using RestSharp;
using Serilog;
using System.Diagnostics;
using XH.H82.IServices;
using XH.H82.Models.Dtos;
using XH.H82.Models.Dtos.FileTemplate;
using XH.H82.Models.Entities;
using XH.H82.Models.Entities.Common;
using XH.H82.Models.Entities.FileTemplate;
using XH.H82.Models.SugarDbContext;
using XH.LAB.UTILS.Models;

namespace XH.H82.Services
{
    public class OnlyOfficeService : IOnlyOfficeService
    {
        private readonly IHttpContextAccessor _httpContext;
        private readonly RestClient _clientH115;
        private readonly ISqlSugarUow<SugarDbContext_Master> _dbContext;
        private readonly IBaseService _baseService;
        private readonly IEquipmentDocService _equipmentDocService;
        private readonly ICurrencyService _currencyService;
        private string addressH115 { get; set; } = "";

        public List<OA_FIELD_DICT> _allNeedDataNodes { get; set; } = new List<OA_FIELD_DICT>();



        public OnlyOfficeService(IConfiguration configuration, IHttpContextAccessor httpContext, ISqlSugarUow<SugarDbContext_Master> DbContext, IBaseService baseService, IEquipmentDocService equipmentDocService, ICurrencyService currencyService)
        {
            addressH115 = configuration["H115"];
            _httpContext = httpContext;
            _dbContext = DbContext;
            if (addressH115.IsNotNullOrEmpty())
            {
                _clientH115 = new RestClient(new RestClientOptions()
                {
                    RemoteCertificateValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true,
                    BaseUrl = new Uri(addressH115),
                    ThrowOnAnyError = true
                });
            }
            _baseService = baseService;
            _equipmentDocService = equipmentDocService;
            _currencyService = currencyService;
        }

        /// <summary>
        /// 调用H115获取导出文件
        /// </summary>
        /// <param name="templateFillDataDto"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public byte[] ExportStyleFile(StyleTemplateFillDataDto templateFillDataDto)
        {
            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();
            Stopwatch sw = new Stopwatch();
            string url = $"api/OO/ExportStyleFile";
            sw.Start();
            RestRequest request = new RestRequest(url);
            //request.AddHeader("Content-Type", "application/octet-stream");
            request.AddHeader("Authorization", token);
            request.AddBody(templateFillDataDto);
            try
            {
                var response = _clientH115.ExecutePost(request);

                sw.Stop();
                Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用H115模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
                if (response.ErrorException != null)
                {
                    Log.Error($"调用H115模块[{url}]发生错误:{response.ErrorException}");
                    throw new BizException($"调用H115模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
                }
                else
                {
                    var bytes = response.RawBytes;
                    return bytes;
                }
            }
            catch (Exception ex)
            {
                Log.Error($"调用H115模块[{url}]发生错误:{ex}");
                throw new BizException("导出失败!");
            }


        }

        /// <summary>
        /// 调用H115获取导出pdf文件
        /// </summary>
        /// <param name="templateFillDataDto"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public byte[] ExportStylePDFFile(StyleTemplateFillDataDto templateFillDataDto)
        {
            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();
            Stopwatch sw = new Stopwatch();
            string url = $"api/OO/ExportStylePDFFile";
            sw.Start();
            RestRequest request = new RestRequest(url);
            //request.AddHeader("Content-Type", "application/octet-stream");
            request.AddHeader("Authorization", token);
            request.AddBody(templateFillDataDto);
            try
            {
                var response = _clientH115.ExecutePost(request);

                sw.Stop();
                Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用H115模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
                if (response.ErrorException != null)
                {
                    Log.Error($"调用H115模块[{url}]发生错误:{response.ErrorException}");
                    throw new BizException($"调用H115模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
                }
                else
                {
                    var bytes = response.RawBytes;
                    return bytes;
                }

            }
            catch (Exception ex)
            {
                Log.Error($"调用H115模块[{url}]发生错误:{ex}");
                throw new BizException("导出失败!");
            }


        }

        /// <summary>
        /// 调用H115获取预览文件
        /// </summary>
        /// <param name="templateFillDataDto"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public byte[] PreviewFile(StyleTemplateFillDataDto templateFillDataDto)
        {
            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();
            Stopwatch sw = new Stopwatch();
            string url = $"api/OO/PreviewFile";
            sw.Start();
            RestRequest request = new RestRequest(url);
            //request.AddHeader("Content-Type", "application/octet-stream");
            request.AddHeader("Authorization", token);
            request.AddBody(templateFillDataDto);
            try
            {
                var response = _clientH115.ExecutePost(request);

                sw.Stop();
                Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用H115模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
                if (response.ErrorException != null)
                {
                    Log.Error($"调用H115模块[{url}]发生错误:{response.ErrorException}");
                    throw new BizException($"调用H115模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
                }
                else
                {
                    var bytes = response.RawBytes;
                    return bytes;
                }
            }
            catch (Exception ex)
            {
                Log.Error($"调用H115模块[{url}]发生错误:{ex}");
                throw new BizException("预览失败!");
            }
        }

        public ResultDto LoadExcelData(OaExcelFillDataDto requestBody)
        {
            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();
            Stopwatch sw = new Stopwatch();
            string url = $"api/OO/LoadExcelData";
            sw.Start();
            RestRequest request = new RestRequest(url);
            //request.AddHeader("Content-Type", "application/octet-stream");
            request.AddHeader("Authorization", token);
            request.AddJsonBody(requestBody);
            try
            {
                var response = _clientH115.ExecutePost<ResultDto>(request);
                sw.Stop();
                Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用H115模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
                if (response.ErrorException != null)
                {
                    Log.Error($"调用H115模块[{url}]发生错误:{response.ErrorException}");
                    throw new BizException($"调用H115模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
                }
                else
                {
                    return response.Data;
                }
            }
            catch (Exception ex)
            {
                Log.Error($"调用H115模块[{url}]发生错误:{ex}");
                throw new BizException("预览失败!");
            }
        }

        public List<WorkPlansInfo> ExchangeEquipmentsWorkPlan(List<EmsWorkPlanDto> emsWorkPlans)
        {
            if (emsWorkPlans is null)
            {
                return null;
            }
            var workPlansInfo = JsonConvert.DeserializeObject<List<WorkPlansInfo>>(JsonConvert.SerializeObject(emsWorkPlans));
            if (workPlansInfo is null)
            {
                workPlansInfo = new List<WorkPlansInfo>();
            }

            SortTheList(workPlansInfo);

            return workPlansInfo;
        }

        public EquipmentArchivesInfo ExchangeEquipmentArchivesInfo(EMS_EQUIPMENT_INFO equipmentInfo, EMS_ENVI_REQUIRE_INFO environmentInfo, List<SYS6_COMPANY_CONTACT> dealercontacts, List<SYS6_COMPANY_CONTACT> manufacturercontacts)
        {
            if (equipmentInfo is null)
            {
                return null;
            }
            if (environmentInfo is null)
            {
                environmentInfo = new EMS_ENVI_REQUIRE_INFO();
            }
            if (dealercontacts is null)
            {
                dealercontacts = new List<SYS6_COMPANY_CONTACT>();
            }
            if (manufacturercontacts is null)
            {
                manufacturercontacts = new List<SYS6_COMPANY_CONTACT>();
            }

            var archivesInfo = JsonConvert.DeserializeObject<EquipmentArchivesInfo>(JsonConvert.SerializeObject(equipmentInfo));
            archivesInfo!.DealerContact = JsonConvert.DeserializeObject<List<EquipmentDealerContact>>(JsonConvert.SerializeObject(dealercontacts.Where(x => x.IF_SELECT == "1")));
            archivesInfo.ManufacturerContact = JsonConvert.DeserializeObject<List<EquipmentManufacturerContact>>(JsonConvert.SerializeObject(manufacturercontacts.Where(x => x.IF_SELECT == "1")));
            archivesInfo.EnvironmentInfo = JsonConvert.DeserializeObject<EnvironmentInfo>(JsonConvert.SerializeObject(environmentInfo));
            archivesInfo.UNIT_ID = ExchangeProfessionalGroupName(archivesInfo.UNIT_ID);
            archivesInfo.EQUIPMENT_CLASS = ExchangeEquipmentClass(archivesInfo.EQUIPMENT_CLASS);
            archivesInfo.PROFESSIONAL_CLASS = ExchangeProfessionalClass(archivesInfo.PROFESSIONAL_CLASS);
            SortTheList(archivesInfo.DealerContact);
            SortTheList(archivesInfo.ManufacturerContact);
            return archivesInfo;
        }

        public List<EquipmentInfo> ExchangeEquipments(List<EquipmentInfoDto> equipmentInfos)
        {

            if (equipmentInfos is null)
            {
                return null;
            }

            var infos = JsonConvert.DeserializeObject<List<EquipmentInfo>>(JsonConvert.SerializeObject(equipmentInfos));
            if (infos is null)
            {
                infos = new List<EquipmentInfo>();
            }

            SortTheList(infos);

            return infos;

        }

        public StyleTemplateFillDataDto FillEquipmentInfo(string styleId, EquipmentArchivesInfo archivesInfo)
        {
            var result = new StyleTemplateFillDataDto() { STYLE_ID = styleId, DATAS = new List<StyleTemplateClassDataDto>() };

            result.DATAS.Add(FillTextNode("eq", archivesInfo));
            result.DATAS.Add(FillTextNode("run", archivesInfo));
            result.DATAS.Add(FillTextNode("deal", archivesInfo));
            result.DATAS.Add(FillTextNode("manu", archivesInfo));
            result.DATAS.Add(FillTextNode("evn", archivesInfo));
            result.DATAS.Add(FillTextNode("evn", archivesInfo.EnvironmentInfo));
            result.DATAS.Add(FillRecordNode("deal", archivesInfo.DealerContact));
            result.DATAS.Add(FillRecordNode("manu", archivesInfo.ManufacturerContact));
            return result;
        }

        public StyleTemplateFillDataDto FillEquipmentsWorkPlanInfo(string styleId, List<WorkPlansInfo> workPlans)
        {
            var result = new StyleTemplateFillDataDto() { STYLE_ID = styleId, DATAS = new List<StyleTemplateClassDataDto>() };
            var keys = new List<string>() { "eq", "plan", "week", "moon", "year", "corr", "com" };
            foreach (var key in keys)
            {
                result.DATAS.Add(FillRecordNode(key, workPlans));
            }
            return result;
        }

        public StyleTemplateFillDataDto FillEquipments(string styleId, List<EquipmentInfo> equipmentInfos)
        {
            var result = new StyleTemplateFillDataDto() { STYLE_ID = styleId, DATAS = new List<StyleTemplateClassDataDto>() };
            result.DATAS.Add(FillRecordNode("eq", equipmentInfos));
            result.DATAS.Add(FillRecordNode("run", equipmentInfos));
            return result;
        }


        private StyleTemplateClassDataDto FillTextNode<T>(string fromDataNode, T t)
        {
            var result = new StyleTemplateClassDataDto() { CLASSE_CODE = fromDataNode };
            if (t is null)
            {
                return result;
            }
            var properties = t.GetType().GetProperties();
            var fromNode = FindDataNodes(fromDataNode);
            foreach (var node in fromNode)
            {
                var propertie = properties.Where(x => x.Name == node.FIELD_CODE).FirstOrDefault();
                if (propertie is not null)
                {
                    result.FIELDS.Add(node.FIELD_CODE, propertie.GetValue(t)?.ToString());
                }
            }
            return result;
        }

        private StyleTemplateClassDataDto FillRecordNode<T>(string recordDataNode, List<T> list)
        {
            var result = new StyleTemplateClassDataDto() { CLASSE_CODE = recordDataNode };
            if (list is null)
            {
                return result;
            }
            if (list.Count == 0)
            {
                return result;
            }
            var properties = typeof(T).GetProperties();
            var recordNode = FindDataNodes(recordDataNode);
            foreach (var item in list)
            {
                Dictionary<string, string> recordDic = new Dictionary<string, string>();

                foreach (var node in recordNode)
                {
                    var propertie = properties.Where(x => x.Name == node.FIELD_CODE).FirstOrDefault();
                    if (propertie is not null)
                    {
                        recordDic.Add(node.FIELD_CODE, propertie.GetValue(item)?.ToString());
                    }
                }
                result.ARRAYS.Add(recordDic);
            }
            return result;
        }

        private List<OA_FIELD_DICT> FindNeedDataNodes(string classeCode)
        {
            var nodes = _allNeedDataNodes.Where(x => x.CLASSE_CODE == classeCode).ToList();
            if (nodes is null)
            {
                var dist = _dbContext.Db.Queryable<OA_FIELD_DICT>()
                    .Where(w => w.MODULE_ID == "H82")
                    .ToList();
                if (dist is not null)
                {
                    _allNeedDataNodes.AddRange(dist);
                }
                var result = _allNeedDataNodes.Where(x => x.CLASSE_CODE == classeCode).ToList();
                return result is null ? new List<OA_FIELD_DICT>() : result;
            }
            else
            {
                if (nodes.Count() == 0)
                {
                    var dist = _dbContext.Db.Queryable<OA_FIELD_DICT>()
                        .Where(w => w.MODULE_ID == "H82")
                        .ToList();
                    if (dist is not null)
                    {
                        _allNeedDataNodes.AddRange(dist);
                    }
                    var result = _allNeedDataNodes.Where(x => x.CLASSE_CODE == classeCode).ToList();
                    return result is null ? new List<OA_FIELD_DICT>() : result;
                }
                else
                {
                    return nodes.ToList();
                }


            }
        }
        private List<OA_FIELD_DICT> FindDataNodes(string classeCode)
        {
            var result = FindNeedDataNodes(classeCode);
            return result;
        }

        private void SortTheList<T>(List<T>? list, string sortProp = "NO")
        {
            if (list is null)
            {
                return;
            }
            for (int i = 1; i <= list.Count(); i++)
            {
                var prop = list[i - 1].GetType().GetProperty(sortProp);
                if (prop is not null)
                {
                    prop.SetValue(list[i - 1], i);
                }
            }
        }

        private string ExchangeAssemblyLineName(string assemblylineCode)
        {
            var result = "";
            if (assemblylineCode.IsNullOrEmpty())
            {
                return result;
            }
            var pipeline = _dbContext.Db.Queryable<SYS6_BASE_DATA>()
                    .Where(baseData => baseData.CLASS_ID == "设备类型" && baseData.DATA_CNAME == "流水线" && baseData.DATA_STATE == "1")
                    .Where(x => x.DATA_ID == assemblylineCode)
                    .First();
            if (pipeline is not null)
            {
                result = pipeline.DATA_SNAME;
            }
            return result!;

        }
        private string ExchangeProfessionalGroupName(string? unitId)
        {
            var result = "";
            if (unitId.IsNullOrEmpty())
            {
                return result;
            }
            var pGroup = _dbContext.Db.Queryable<SYS6_INSPECTION_PGROUP>()
                          .First(x => x.PGROUP_ID == unitId);
            if (pGroup is not null)
            {
                result = pGroup.PGROUP_NAME;
            }

            return result!;
        }
        private string ExchangeEquipmentClass(string? classCode)
        {

            var result = "";
            if (classCode.IsNullOrEmpty())
            {
                return result;
            }

            var baseData = _equipmentDocService.GetCompanyClass()
                .FirstOrDefault(x => x.BasciId == classCode);
            if (baseData is not null)
            {
                result = baseData.BasicName;
            }
            return result;

        }

        private string ExchangeEquipmentState(string? state)
        {
            var result = "";
            if (state.IsNullOrEmpty())
            {
                return result;
            }

            result = state == "3" ? "报废" : state == "2" ? "停用" :
                     state == "1" ? "启用" : state == "0" ? "未启用" : "";
            return result;

        }
        private string ExchangeProfessionalClass(string? professionalClassCode)
        {

            var result = "";
            if (professionalClassCode.IsNullOrEmpty())
            {
                return result;
            }

            var baseData = _currencyService.GetProfessionalClass()
                .FirstOrDefault(x => x.BasciId == professionalClassCode);
            if (baseData is not null)
            {
                result = baseData.BasicName;
            }
            return result;

        }

        public OaExcelFillDataDto FillExcelModelEquipments(string styleId, List<EquipmentInfo> equipmentInfos)
        {

            const string onlyKey = "EQUIPMENT_ID";
            var keys = _allNeedDataNodes.Where(x => x.FIELD_TYPE == "1").Select(x => x.CLASSE_CODE).Distinct();

            var result = new OaExcelFillDataDto(styleId, onlyKey);
            if (equipmentInfos is null)
            {
                return result;
            }

            var ids = equipmentInfos.Select(x => x.EQUIPMENT_ID).ToList();
            var equipmentBaseInfos = _equipmentDocService.FindBaseEquipmentInfo(ids);
            if (equipmentBaseInfos is null)
            {
                return result;
            }
            var archivesInfos = JsonConvert.DeserializeObject<List<EquipmentArchivesInfo>>(JsonConvert.SerializeObject(equipmentBaseInfos));
            foreach (var archivesInfo in archivesInfos)
            {
                archivesInfo.UNIT_ID = ExchangeProfessionalGroupName(archivesInfo.UNIT_ID);
                archivesInfo.EQUIPMENT_STATE = ExchangeEquipmentState(archivesInfo.EQUIPMENT_STATE);
                archivesInfo.EQUIPMENT_CLASS = ExchangeEquipmentClass(archivesInfo.EQUIPMENT_CLASS);
                archivesInfo.PROFESSIONAL_CLASS = ExchangeProfessionalClass(archivesInfo.PROFESSIONAL_CLASS);
            }

            SortTheList(archivesInfos);
            foreach (var equipmentInfo in equipmentInfos)
            {
                var excelData = new StyleTemplateClassExcelDataDto();
                var archivesInfo = archivesInfos.FirstOrDefault(x => x.EQUIPMENT_ID == equipmentInfo.EQUIPMENT_ID);



                excelData.FIELDS = FillExcelTextNode("eq", archivesInfo, onlyKey);

                foreach (var key in keys)
                {
                    excelData.CLASS_DATAS.Add(FillExcelRecordNode(key, equipmentInfo));
                }
                result.ALL_CLASS_DATAS.Add(excelData);
            }
#if DEBUG
            Console.WriteLine(JsonConvert.SerializeObject(result));
#endif
            return result;

        }

        public OaExcelFillDataDto FillExcelModelEquipmentsWorkPlan(string styleId, List<WorkPlansInfo> workplans)
        {
            const string onlyKey = "EQUIPMENT_ID";
            var keys = _allNeedDataNodes.Where(x => x.FIELD_TYPE == "1").Select(x => x.CLASSE_CODE).Distinct();
            var result = new OaExcelFillDataDto(styleId, onlyKey);
            if (workplans is null)
            {
                return result;
            }
            var ids = workplans.Select(x => x.EQUIPMENT_ID).ToList();
            var equipmentBaseInfos = _equipmentDocService.FindBaseEquipmentInfo(ids);
            if (equipmentBaseInfos is null)
            {
                return result;
            }
            var archivesInfos = JsonConvert.DeserializeObject<List<EquipmentArchivesInfo>>(JsonConvert.SerializeObject(equipmentBaseInfos));


            foreach (var archivesInfo in archivesInfos)
            {
                archivesInfo.EQUIPMENT_ARCHIVE = archivesInfo.EQUIPMENT_NAME;
                archivesInfo.UNIT_ID = ExchangeProfessionalGroupName(archivesInfo.UNIT_ID);
                archivesInfo.EQUIPMENT_STATE = ExchangeEquipmentState(archivesInfo.EQUIPMENT_STATE);
                archivesInfo.EQUIPMENT_CLASS = ExchangeEquipmentClass(archivesInfo.EQUIPMENT_CLASS);
                archivesInfo.PROFESSIONAL_CLASS = ExchangeProfessionalClass(archivesInfo.PROFESSIONAL_CLASS);
            }

            SortTheList(archivesInfos);
            foreach (var workplan in workplans)
            {
                var excelData = new StyleTemplateClassExcelDataDto();
                var archivesInfo = archivesInfos.FirstOrDefault(x => x.EQUIPMENT_ID == workplan.EQUIPMENT_ID);

                excelData.FIELDS = FillExcelTextNode("eq", archivesInfo, onlyKey);

                foreach (var key in keys)
                {
                    excelData.CLASS_DATAS.Add(FillExcelRecordNode(key, workplan));
                }
                result.ALL_CLASS_DATAS.Add(excelData);
            }

#if DEBUG
            Console.WriteLine(JsonConvert.SerializeObject(result));
#endif
            return result;

        }

        private ClassData FillExcelRecordNode<T>(string recordDataNode, T item, string key = null)
        {
            var result = new ClassData() { CLASSE_CODE = recordDataNode };
            var properties = typeof(T).GetProperties();
            var recordNode = FindDataNodes(recordDataNode);
            Dictionary<string, string> recordDic = new Dictionary<string, string>();
            foreach (var node in recordNode)
            {
                var propertie = properties.Where(x => x.Name == node.FIELD_CODE).FirstOrDefault();
                if (propertie is not null)
                {
                    recordDic.Add(node.FIELD_CODE, propertie.GetValue(item)?.ToString());
                }
            }
            if (key.IsNotNullOrEmpty())
            {
                var propertie = properties.Where(x => x.Name == key).FirstOrDefault();
                if (propertie is not null)
                {
                    recordDic.Add(key, propertie.GetValue(item)?.ToString());
                }
            }
            if (recordDic.Count == 0)
            {
                return result;
            }
            result.ARRAYS.Add(recordDic);
            return result;
        }

        private Dictionary<string, string> FillExcelTextNode<T>(string fromDataNode, T t, string key = null)
        {
            var result = new Dictionary<string, string>();
            if (t is null)
            {
                return result;
            }
            var properties = t.GetType().GetProperties();
            var fromNode = FindDataNodes(fromDataNode);
            foreach (var node in fromNode)
            {
                var propertie = properties.Where(x => x.Name == node.FIELD_CODE).FirstOrDefault();
                if (propertie is not null)
                {
                    result.Add(node.FIELD_CODE, propertie.GetValue(t)?.ToString());
                }
            }
            if (key.IsNotNullOrEmpty())
            {
                var propertie = properties.Where(x => x.Name == key).FirstOrDefault();
                if (propertie is not null)
                {
                    result.Add(key, propertie.GetValue(t)?.ToString());
                }
            }

            return result;
        }

    }
}
