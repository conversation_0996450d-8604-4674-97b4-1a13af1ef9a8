﻿using H.Utility;
using Newtonsoft.Json;
using Spire.Additions.Xps.Schema;
using XH.H82.Models.Dtos;

namespace XH.H82.Models.EquipmentCodeCustom;

public class EquipmentCodeCustomDict
{
    /// <summary>
    /// 自定义名称模板id
    /// </summary>
    public string? EqpNoId{ get; set; }
    
    /// <summary>
    /// 医疗机构ID
    /// </summary>
    public string HospitalId{ get; set; }
    
    /// <summary>
    /// 设备自定义名称模板名;模板名称
    /// </summary>
    public string EqpNoName{ get; set; }

    /// <summary>
    /// 模板优先级;模板优先级、数值越大、优先级越高
    /// </summary>
    public string? EqpNoLevel { get; set; } = "0";

    /// <summary>
    /// 应用设备类型;0 全部 设备类型（多个类型;拼接）
    /// </summary>
    public string? EqpNoClass { get; set; } = "0";
    
    /// <summary>
    /// 应用设备类型;0 全部 设备类型（多个类型;拼接）
    /// </summary>
    public List<string> EqpNoClassList => EqpNoClass?.Split(";").ToList();

    /// <summary>
    ///  应用设备类型
    /// </summary>
    public string? EqpNoClassName { get; set; }

    /// <summary>
    /// 设备代号配置
    /// </summary>
    public string? EqpDisplayJson => JsonConvert.SerializeObject(DisplayContent);
    /// <summary>
    /// 设备展示内容
    /// </summary>
    public DisplayContent DisplayContent { get; set; }
    
    /// <summary>
    /// 显示内容
    /// </summary>
    public string? DisplayContentName { get; set; }
    /// <summary>
    /// 应用范围（专业组id ;拼接）
    /// </summary>
    public string? EqpNoApplys{ get; set; }
    
    /// <summary>
    /// 应用范围（专业组id ;拼接）
    /// </summary>
    public List<string> EqpNoApplyList => EqpNoApplys.IsNullOrEmpty() ? new List<string>() :   EqpNoApplys?.Split(";").ToList();

    /// <summary>
    /// 应用范围中文;分割
    /// </summary>
    public string? EqpNoApplysName  { get; set; }
    
    /// <summary>
    /// 数据状态;0 禁用   1在用   2删除
    /// </summary>
    public string EqpNoState{ get; set; }

    /// <summary>
    /// 数据状态名字; 禁用   在用   删除
    /// </summary>
    public string EqpNoStateName => EqpNoState switch
    {
        "0" => "禁用",
        "1" => "在用",
        "2" => "删除",
        _ => "未知"
    };
}

/// <summary>
/// 添加自定义编号字典模型
/// </summary>
public class AddEquipmentCodeCustomDictDto
{

    
    /// <summary>
    /// 设备自定义名称模板名;模板名称
    /// </summary>
    public string EqpNoName{ get; set; }

    /// <summary>
    /// 模板优先级;模板优先级、数值越大、优先级越高
    /// </summary>
    public string? EqpNoLevel { get; set; } = "0";

    /// <summary>
    /// 应用设备类型;0 全部 设备类型（多个类型;拼接）
    /// </summary>
    public string EqpNoClass { get; set; } = "0";
    /// <summary>
    /// 设备代号配置
    /// </summary>
    public string? EqpDisplayJson => JsonConvert.SerializeObject(DisplayContent);
    /// <summary>
    /// 设备展示内容
    /// </summary>
    public DisplayContent DisplayContent { get; set; }
    /// <summary>
    /// 应用范围（专业组id ;拼接）
    /// </summary>
    public string? EqpNoApplys{ get; set; }

    /// <summary>
    /// 设备状态
    /// </summary>
    public string EqpNoState { get; init; } = "1";
    public static AddEquipmentCodeCustomDictDto Create()
    {
        return new AddEquipmentCodeCustomDictDto
        {
            EqpNoName = "默认全科模板",
            EqpNoLevel = "0",
            EqpNoClass = "0",
            DisplayContent = new DisplayContent()
            {
                FixedFieldDisplays = new List<KeyValueDto>(),
                DisplayContentString = "{设备序号}_{设备名称}_{设备型号}",//设备序号+设备名称+设备型号
                DisplayContentCode = "{EQUIPMENT_NUM}_{EQUIPMENT_NAME}_{EQUIPMENT_MODEL}"
            },
            EqpNoApplys = "",
        };
    }

}

/// <summary>
/// 更新自定义编号字典模型
/// </summary>
public class UpdateEquipmentCodeCustomDictDto
{ 
    public string EqpNoId{ get; set; }
    public string EqpNoName{ get; set; }
    public string EqpNoClass { get; set; } 
    public DisplayContent DisplayContent { get; set; }
    public string? EqpNoApplys{ get; set; }
    
    public string? GetEqpDisplayJson => JsonConvert.SerializeObject(DisplayContent);
}