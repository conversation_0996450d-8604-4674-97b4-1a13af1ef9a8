﻿namespace XH.H82.Models.Dtos;

public class CountWorkPlanDto
{
    /// <summary>
    /// No
    /// </summary>
    public int No { get; set; }
    /// <summary>
    /// 备案实验室名称
    /// </summary>
    public string SbmlLabName { get; set; }
    /// <summary>
    /// 生安设备类型
    /// </summary>
    public string SmblClassName { get; set; }
    /// <summary>
    /// 设备名称
    /// </summary>
    public string EquipmentName { get; set; }
    /// <summary>
    /// 上次年检时间
    /// </summary>
    public DateTime? LastInspectionTime { get; set;}
    /// <summary>
    /// 下次年检时间
    /// </summary>
    public DateTime? NextInspectionTime { get; set; }
    /// <summary>
    /// 周期
    /// </summary>
    public double Cycle { get; set; }
}