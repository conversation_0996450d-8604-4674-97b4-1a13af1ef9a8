## 6.25.329
`2025-07-11`
- 【新增】设备自定义名称模板

## 6.25.300
`2025-06-24`
- 【缺陷】【安装信息】添加授权记录，设置完成之后，先选中添加附件，造成数据保存不了
- 【缺陷】【安装信息】配件信息新增页面数据显示过慢，造成上传文件数据被其他配件进行保存成功
- 【缺陷】【设备档案】环境要求内左边温度能大于右边温度
- 【缺陷】【设备档案】环境要求内尺寸、重量、称重要求、空间等未做负数验证
- 【缺陷】【安装信息】开机性能验证报告，开始时间大于结束日期

## 6.25.225
`2025-06-13`
- 【优化】【设备档案/工作计划/申购管理的设备档案树展示】设备-人岗位权调整
- 【优化】【设备sop文件关联文档】设备sop文件关联
- 【优化】【设备类型展示优化】设备类型树展示自动剔除叶子节点数为0的专业组节点
- 【缺陷】【设备清单功能中的附件展示】设备-清单功能-附件展示-pdf文件渲染异常

## 6.25.223
`2025-05-30`
- 【新增】设备档案基础信息支持自定义配置，包括动态决定必填项，是否显示，位置顺序（基础数据管理界面设置）
- 【新增】设备档案添加去污事务项归档记录单展示
- 【缺陷】设备档案-授权记录检索bug
- 【缺陷】设备档案-授权记录被授权人姓名记录不正确
- 【缺陷】设备档案-授权记录以及培训记录补充悬浮框（列表右击` 预览 `按钮）
- 【优化】人员、专业组等基础信息查询速度

## 6.25.219
`2025-05-08`
- 修复iso里面的生安设置报错
- 修复设备档案-生安版本下生安设备类型下拉框数据展示问题
- 修复生安科室层级的树查询的bug
- 人岗权职规则更新
- 更新设备导入模板样式，支持现有设备类型下拉
- 补充生安设备告警信息对接接口

## 6.25.201
`2025-04-08`
- 修复校准记录-保存后有效期显示bug问题
- 修复设备使用记录-浏览-点开后白屏问题
- 修复设备档案-供应商/服务商/制造商信息维护检索bug问题
- 修复设备档案-校准记录-鉴定人员只展示拥有权限的人员展示
- 补充了设备导入专业组校验优化

## 6.25.200
`2025-03-21`
- 升级统一版本6.25.200
- 修复事务项归自动归档单子展示不出来的bug

## 6.24.9
`2025-03-07`
- 支持设备服务商信息维护
- 调整授权记录人员选择下拉效果

## 6.24.8
`2025-03-05`
- iso设备导入支持`注册证号`字段（不支持旧模板上传）
- 生安相关设备 `生物安全柜子`监控相关功能
- 生安相关设备 `环境一体机`监控相关功能
- 生安设备添加`房间位置`所属
- 生安设备添加`门禁类型`以及门禁进出方向维护
- 上传附件支持高拍仪截图合并相关操作，暂存区可进行图片合并操作，合并后删除原图片，并生成新的图片；



## 6.24.7
`2024-12-25`
- 修复设备档案中，校准记录产生其他事件时，导致的关联事件id重复bug，现规则为一天内产生关联事件编号上限为9999；
- 新增设备档案删除权限判断；
- 新增设备档案编辑页面（每一台是设备的编辑页面）都需要进行密码访问；
- 设备资质证书补充默认类型：医疗器械注册证，后续可自行维护；
- 支持浏览设备关联供应商的相关证书;
- 添加迎检生安设备关联
- 添加设备生安页面
- 设备授权记录补充字典功能，手动输入记录会自动记录到字典中，历史记录可以重新保存提取授权权限字典

## 6.24.6
`2024-11-14`
- 兼容工作计划不审核等相关功能、配置需要示例级别的系统数据管理的设置导入相关文件;
- 补充手动添加设备使用记录相关信息;
- 新增文件暂存区，支持使用记录的图片暂存提供多个记录选择;
- 设备使用记录填写内容支持字典化，可先把常用的用语预存到字典中，后续填写时选择填入;
- 新增使用记录中可以直接查看此设备相关的事务项填写记录内容;
- 优化工作计划中的值修改后，保存失效bug;
- 添加工作计划与事务项关联，可以在工作计划中查看事务项信息（以依赖H86事务项6.24.11版本）;
- 修复工作提交审核撤回后，限定审核人提交审核的bug，驳回后允许其他用户提交或审核;
- 修复设备档案详细信息页面相关专业类型、设备类型等下拉数据展示为数字bug;
- 修复设备停用申请，提交至申请人下拉数据丢失bug;


## 6.24.5
`2024-11-04`
- 添加工作计划季度保养填写
- 设备保养周期计算更新，容纳季度保养参与计算下次保养时间
- 设备同步主体仪器单元组可根据主体业务配置开启是否固定绑定设备所属检验专业组为检验单元所属的检验专业组，如开启，则初次同步绑定为检验单元所属的检验专业组，后续可自行修改设备所属检验专业组
- 修复设备保养、维修、校准事件触发的质控事件异常bug
- 限制由主动事件（如手动维护设备的保养、维修记录后，勾选校准事件，产生的校准事件称为被动事件，前者为主动事件）信息触发的被动事件（如校准事件）后，还能继续触发新的被动事件（禁止维护被动事件的记录的其他被动事件）
- 设备列表支持"设备保养记录"、"校准记录"、"比对记录"、"性能验证记录"清单功能预设
- 修复初始用户加载设备列表时无响应bug
- 清单功能更新：支持设备附件显示（依赖是H115模块6.24.8以上版本）
- 设备列表支持"维修"清单功能预设
- 修复设备数据被覆盖问题
- 设备档案导出功能优化，设备保养、维修、使用、校准 支持按XX记录日期筛选
- 
## 6.24.4
`2024-9-10`
- 链接文档系统功能需要依赖文档系统（H91）6.24.2版本及以上
- 链接文档功能支持全部文件关联选择需要依赖文档系统（H91）6.24.2版本及以上
- 优化大文件pdf预览速度
- 优化迎检中设备页面左侧树重复、反复刷新bug
- 修复设备档案（包括迎检页面的设备档案页面）左侧树数量与右边设备列表对应
- 优化墨水屏刷新规则，存在默认模板同步刷新，如有相关墨水屏设备关联到专业组下，则自动转为应用当前专业组相关墨水屏模板
- 新增墨水屏设计模式页面，自定义模板、专业组应用、墨水屏预览等功能
- 设备标识卡字段调整：补充工程师电话
- 修复个oracle数据库打开维修、保养页面报错问题

## 6.24.3
`2024-8-16`
- 新增墨水屏功能，通过appsetting文件配置开关
- 如"IsAutoRefresh": "1",为开启，“0”为关闭，此功能需配合墨水屏基站使用，
- 新增设备报警信息功能
- 修复清单功能导出word文档异常展示问题
- 修复设备档案使用清单功能预览buga
- 补充程序启动时校验数据完整性功能
- 新增迎检核查表链接相关功能
- 调整在不同操作系统下，上传附件或word文档或pdf文件生成预览图bug
- 补充多了两个设备类型“特种设备、监测设备”
- 修复sop、文档说明说链接到文档系统bug
- 优化左侧专业组树节点会存在消失问题
- 优化设备左侧设备节点树丢失bug
- 修复设备反复停用，在申请停用/报废列表找不到已停用的bug
- 更正工作计划节点数数量与列表展示数量不对，以及只显示启用状态的设备的工作计划
- 修复流水线下拉偶尔没数据的bug
- 修复设备的启用记录会重复的bug
- 更新设备启用相关逻辑

## 6.24.2
`2024-8-3`
- 标识卡增加数据源

## 6.24.1
`2024-7-29`
- 修复清单功能展示数据不全
- 补充设备基础信息浏览功能全节点信息浏览
- 补充验收报告文件上传|编辑|删除功能
- 新增设备预警信息信息接口 
- 链接到文档系统文件反选功能完善


## 1.0.23
`2024-7-24`
- 处理现场反馈的“设备校准/检定记录修改bug”

## 1.0.22
`2024-7-22`
- 处理设备申购信息存储会保存多余的","，已经修正并兼容，同时处理null字段的查询bug

## 1.0.21
`2024-7-19`
- 修复设备新增|模板导入|同步设备|的主键id不匹配问题，以及前端点击菜单树节点/点击具体设备出现不显示问题
- 添加图片|PDF预览加载中间件
- 添加H115模块相关查询接口调用处理接口
- 修复缩略图不显示bug
## 1.0.20
`2024-7-16`
- 修复链接到文档系统找不到文件bug
- 修复缩略图显示缺失，旧数据不做处理，以新关联的附件效果为准
- 修复word文档内容上传bug

## 1.0.19
`2024-7-12`
- 修复启动异常bug


## 1.0.18
`2024-7-12`
- 修复工作计划列表数据异常问题


## 1.0.17
`2024-7-05`
- 设备档案信息_设备说明说书链接到文档系统
- 设备档案信息清单功能
- 设备档案信息_浏览功能（结合清单的模板选择）
- 设备工作计划清单功能
- 设备工作计划流转流程功能（提交、审核、驳回）


## 1.0.16
`2024-6-17`
- 完善设备停用人列表逻辑
- 修复sqlServer数据库仪器同步失败问题

## 1.0.15
`2024-6-3`
- 增加多库协调相关接口
- 修复sqlserver环境下设备报废的报错
- “校准鉴定”字眼更改为：“校准检定”
- 设备报废停用增加权限控制，申请时只允许提交到有权限者(有脚本)
- 设备档案运行记录更改维修人员记录为手工录入
- 设备档案的备注字段中增加鼠标悬停显示全部内容
- 增加自定义设备标识卡功能
- 增加自动状态和几个手动状态标识卡模板

## 1.0.14
`2024-4-25`
- 修复sqlserver特殊环境下无法登录的问题
- 修复试剂信息-设备分类存在树显示异常的问题
- 设备档案等页面“所属实验室”统一改为“所属专业组”
- 修复设备档案-设备标识卡无法切换的问题
- 修复设备档案导入医院设备编号字段空白问题
- 设备档案导入增加重复设备代码提示
- 修复所属实验室为空提示
- 修复设备档案导入日期校验问题
- 设备档案增加填入首次启用时间后左侧树结构状态自动启用

## 1.0.13
`2024-4-16`
- 修复excel导入设备所属实验室的提示问题
- 修复添加授权记录人员过多的异常
- 提供设备的状态数据返回

## 1.0.12
`2024-4-11`
- 设备代号从系统维护的自定义名称同步
- 设备型号从系统维护的自定义代号同步
- 序号、序列号加到列表
- 培训人员可手工录入
- 选择设备时，中间区域空白问题修复
- 设备详情区域加载数据时的优化
- 去除设备标识卡打印输出中的null字样
- 修复问题汇总的问题
- 修复oracle11g问题
- 修复sql没法记录日志问题

## 1.0.11
`2024-4-10`
- 设备代号从系统维护的自定义名称同步
- 设备型号从系统维护的自定义代号同步
- 序号、序列号加到列表
- 培训人员可手工录入
- 选择设备时，中间区域空白问题修复
- 设备详情区域加载数据时的优化
- 去除设备标识卡打印输出中的null字样
- 修复问题汇总的问题


## 1.0.10
`2024-3-22`
- 设备档案表格增加悬停显示完整信息的功能
- 解决设备档案隐藏设备在试剂信息、工作计划里的显示问题
- 解决设备档案的设备关键字搜索问题
- 解决设备档案的设备附件浏览弹窗过小问题
- 解决设备档案页面过滤条件里的实验室下拉空白的问题

## 1.0.9
`2024-3-11`
-  压缩文件上传并自动识别保存为各类设备附件功能
-  修复问题汇总若干问题

## 1.0.8
`2024-1-26`
-  文档关联事件预览报错的修复
-  关联事件能否取消的判定
`2024-1-29`
-  报废流程输入密码报错的修复
`2024-2-21`
-  下次验证日期不匹配的问题修复
-  修改设备树排序逻辑
	
## 1.0.7
`2024-1-8`
-  大文件上传修改
	
## 1.0.6
`2023-11-29`
-  无权限用户登录失败后地址刷新
-  设备树结构新增隐藏/显示条件
-  新增设备导入模板的下载按钮
-  设备分类树结构选中bug修改
	
## 1.0.5
`2023-11-13`
-  新增流水线配置，校准关联事件

## 1.0.4
`2023-10-27`
-  新增院区字段，树结构调整

## 1.0.3
`2023-09-18`
-  设备导入逻辑修改

## 1.0.2
`2023-09-12`
-  文档关联附件名称丢失问题修改
-  新增设备导入
	
## 1.0.1
`2023-09-07`
-  linux环境下图片处理类库不兼容的问题修改

## 1.0.0
`2023-08-11`
-  现场上线版本