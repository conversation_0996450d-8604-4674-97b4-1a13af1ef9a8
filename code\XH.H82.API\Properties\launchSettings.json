{"profiles": {"XH.H82.API": {"commandName": "Project", "launchBrowser": true, "launchUrl": "", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "applicationUrl": "https://localhost:18482", "dotnetRunMessages": true}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "dotnetRunMessages": true}, "Docker": {"commandName": "<PERSON>er", "launchBrowser": true, "launchUrl": "{Scheme}://{ServiceHost}:{ServicePort}/", "publishAllPorts": true, "useSSL": true}, "WSL": {"commandName": "WSL2", "launchBrowser": true, "launchUrl": "https://localhost:18482/", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "ASPNETCORE_URLS": "https://localhost:18482"}, "distributionName": ""}}, "$schema": "https://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:64535", "sslPort": 44397}}}