﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.Dtos
{
    /// <summary>
    /// 设备分类树
    /// </summary>
    public class NewEquipClassTreeDto
    {
        /// <summary>
        /// 院区ID
        /// </summary>
        public string AREA_ID { get; set; }
        /// <summary>
        /// 院区名称
        /// </summary>
        public string AREA_NAME { get; set; }
        /// <summary>
        /// 设备总数
        /// </summary>
        public int TOTAL_EQUIPMENT_AMOUNT { get; set; }
        /// <summary>
        /// 报废停用数量
        /// </summary>
        public int TOTAL_SCRAP_AMOUNT { get; set; }
        /// <summary>
        /// 检测仪器总数
        /// </summary>
        public int TOTAL_INSTRUMENT_AMOUNT { get;set; }
        /// <summary>
        /// 本人待处理
        /// </summary>
        public int SELF_PEND { get; set; }
        /// <summary>
        /// 本实验室待处理
        /// </summary>
        public int SELF_PGROUP_PEND { get; set; }
        /// <summary>
        /// 二级树
        /// </summary>
        public List<NewEquipClassTree> SecondStageTree { get; set; }
    }
    /// <summary>
    /// 设备分类结点
    /// </summary>
    public class NewEquipClassTree
    {
        /// <summary>
        /// 自然增长数
        /// </summary>
        public int Num { get; set; }
        /// <summary>
        /// 设备分类
        /// </summary>
        public string EQUIPMENT_CLASS { get; set; }
        /// <summary>
        /// 设备分类名称
        /// </summary>
        public string EQUIPMENT_CLASS_NAME { get;set; }
        /// <summary>
        /// 设备总数
        /// </summary>
        public int EQUIPCLASS_EQUIPMENT_AMOUNT { get; set; }
        /// <summary>
        /// 报废总数
        /// </summary>
        public int EQUIPCLASS_SCRAP_AMOUNT { get; set; }
        /// <summary>
        /// 检测仪器总数
        /// </summary>
        public int EQUIPCLASS_INSTRUMENT_AMOUNT { get; set; }
        /// <summary>
        /// 三级树
        /// </summary>
        public List<NewClassMgroupTree> ThirdStageTree { get; set; }
    }
    /// <summary>
    /// 管理专业组结点
    /// </summary>
    public class NewClassMgroupTree
    {
        /// <summary>
        /// 自然增长数
        /// </summary>
        public int Num { get; set; }
        /// <summary>
        /// 管理专业组ID
        /// </summary>
        public string MGROUP_ID { get; set; }
        /// <summary>
        /// 管理专业组名称
        /// </summary>
        public string MGROUP_NAME { get; set; }
        /// <summary>
        /// 设备总数
        /// </summary>
        public int MGROUP_EQUIPMENT_AMOUNT { get;set; }
        /// <summary>
        /// 报废总数
        /// </summary>
        public int MGROUP_SCRAP_AMOUNT { get; set; }
        /// <summary>
        /// 检测仪器总数
        /// </summary>
        public int MGROUP_INSTRUMENT_AMOUNT { get; set; }
        /// <summary>
        /// 四级树
        /// </summary>
        public List<NewClassPgroupTree> FourthStageTree { get; set; }

    }
    /// <summary>
    /// 检验专业组结点
    /// </summary>
    public class NewClassPgroupTree
    {
        /// <summary>
        /// 自然增长数
        /// </summary>
        public int Num { get; set; }
        /// <summary>
        /// 检验专业组ID
        /// </summary>
        public string PGROUP_ID { get; set; }
        /// <summary>
        /// 检验专业组名称
        /// </summary>
        public string PGROUP_NAME { get; set; }
        /// <summary>
        /// 设备总数
        /// </summary>
        public int PGROUP_EQUIPMENT_AMOUNT { get; set; }
        /// <summary>
        /// 报废总数
        /// </summary>
        public int PGROUP_SCRAP_AMOUNT { get; set; }
        /// <summary>
        /// 检测仪器总数
        /// </summary>
        public int PGROUP_INSTRUMENT_AMOUNT { get; set; }
        /// <summary>
        /// 五级树
        /// </summary>
        public List<NewClassEquipmentTree> FifthStageTree { get;set; }
    }
    public class NewClassEquipmentTree
    {
        /// <summary>
        /// 自然增长数
        /// </summary>
        public int Num { get; set; }
        /// <summary>
        /// 设备ID
        /// </summary>
        public string EQUIPMENT_ID { get; set; }
        /// <summary>
        /// 设备代号
        /// </summary>
        public string EQUIPMENT_CODE { get; set; }
        /// <summary>
        /// 设备分类
        /// </summary>
        public string EQUIPMENT_CLASS { get; set; }
        /// <summary>
        /// 设备状态
        /// </summary>
        public string EQUIPMENT_STATE { get; set; }
        /// <summary>
        /// 是否隐藏
        /// </summary>
        public string IS_HIDE { get; set; }
        public string SMBL_FLAG { get; set; }
        /// <summary>
        /// 六级树
        /// </summary>
        public List<NewClassEquipmentTree> SixthStageTree { get; set; }
    }
}
