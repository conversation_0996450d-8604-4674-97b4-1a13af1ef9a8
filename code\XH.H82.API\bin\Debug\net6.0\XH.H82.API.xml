<?xml version="1.0"?>
<doc>
    <assembly>
        <name>XH.H82.API</name>
    </assembly>
    <members>
        <member name="T:XH.H82.API.Controllers.AuthorizationRecord.AuthorizationRecordController">
            <summary>
            设备授权记录相关
            </summary>
        </member>
        <member name="M:XH.H82.API.Controllers.AuthorizationRecord.AuthorizationRecordController.AddAuthorizationDict(XH.H82.Models.Dtos.AuthorizationRecord.AuthorizationRecordDictInput)">
            <summary>
            添加授权权限字典
            </summary>
            <param name="input">{Content : "" , Remark : ""}</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.AuthorizationRecord.AuthorizationRecordController.UpdateAuthorizationDict(System.String,XH.H82.Models.Dtos.AuthorizationRecord.AuthorizationRecordDictInput)">
            <summary>
            修改授权权限字典
            </summary>
            <param name="id"></param>
            <param name="input">{Content : "" , Remark : ""}</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.AuthorizationRecord.AuthorizationRecordController.DeleteContentDict(System.String)">
            <summary>
            删除授权权限字典
            </summary>
            <param name="Id"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.AuthorizationRecord.AuthorizationRecordController.GetAuthorizeRecordDicts(System.String)">
            <summary>
            查询授权权限字典列表
            </summary>
            <param name="content">字典内容</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.AuthorizationRecord.AuthorizationRecordController.AddAuthorizeRecord(System.String,XH.H82.Models.Dtos.AuthorizationRecord.AuthorizationRecordDto)">
            <summary>
            添加授权记录
            </summary>
            <param name="equipmentId">设备id</param>
            <param name="input"></param>
            <remarks>
            input: {
            "AUTHORIZE_PERSON_ID": "string", //用户id PERSON_ID
            "AUTHORIZE_PERSON": "string", login_userno 授权用户
            "AUTHORIZED_PERSON": "string", 多个被授权用户
            "AUTHORIZED_ROLE": "string",  授权权限
            "AUTHORIZE_DATE": "2024-12-30"  授权时间
            }
            </remarks>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.AuthorizationRecord.AuthorizationRecordController.UpdateAuthorizeRecord(System.String,XH.H82.Models.Dtos.AuthorizationRecord.AuthorizationRecordDto)">
            <summary>
            修改授权记录
            </summary>
            <param name="id">授权记录id  AUTHORIZE_ID </param>
            <param name="input"></param>
            <remarks>
            input: {
            "AUTHORIZE_PERSON_ID": "string", //用户id PERSON_ID
            "AUTHORIZE_PERSON": "string", login_userno 授权用户
            "AUTHORIZED_PERSON": "string", 多个被授权用户
            "AUTHORIZED_ROLE": "string",  授权权限
            "AUTHORIZE_DATE": "2024-12-30"  授权时间
            }
            </remarks>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.AuthorizationRecord.AuthorizationRecordController.GetTableUsers(System.String,System.String)">
            <summary>
            获取悬浮表格中的用户信息
            </summary>
            <param name="names"></param>
            <param name="ids"></param>
            <returns></returns>
            <exception cref="T:H.Utility.BizException"></exception>
        </member>
        <member name="M:XH.H82.API.Controllers.BaseController.GetUserLabList">
            <summary>
            获取用户科室列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.BaseController.GetGlobalLabId(System.String)">
            <summary>
            获取科室ID
            </summary>
            <param name="labId">科室ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.BaseController.GetMgroupList(System.String,System.String)">
            <summary>
            专业组树结构
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.BaseController.GetAreaPullList(System.String)">
            <summary>
            获取院区下拉
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.BaseController.GetEnclosureInfo(System.String,System.String)">
            <summary>
              获取附件
            </summary>
            <param name="DOC_CLASS">附件分类（如：申购信息）</param>
            <param name="DOC_INFO_ID">附件关联ID（如：申购ID）</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.BaseController.DeleteEnclosure(System.String)">
            <summary>
              删除附件信息
            </summary>
            <param name="doc_id"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.BaseController.UploadEnclosure(XH.H82.Models.Dtos.UploadFileDto)">
            <summary>
            上传附件
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.BaseController.EditEnclosureInfo(System.String,System.String,System.String)">
            <summary>
              修改附件信息
            </summary>
            <param name="doc_id"></param>
            <param name="doc_name"></param>
            <param name="remark"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.BaseController.BatchEditEnclosureInfo(System.Collections.Generic.List{XH.H82.Models.Entities.EMS_DOC_INFO})">
            <summary>
              批量修改附件信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.BaseController.BatchInsertEnclosureInfo(System.Collections.Generic.List{XH.H82.Models.Entities.EMS_DOC_INFO})">
            <summary>
             复制操作批量添加附件信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.BaseController.GetAdverseEventInfo(System.String)">
            <summary>
              获取不良事件信息
            </summary>
            <param name="equipment_id">设备ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.BaseController.GetMenuInfo">
            <summary>
            获取菜单
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.BaseController.GetFuncDictInfo(System.String)">
            <summary>
            获取表格表单设置
            </summary>
            <param name="menuId"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.BaseController.GetCompanyClassList">
            <summary>
            获取供应商类型
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.BaseController.UploadStagingAreaFile(XH.H82.Models.Dtos.UploadStagingAreaFileDto)">
            <summary>
            上传暂存区文件
            </summary>
            <param name="uploadFile"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.BaseController.GetStagingAreaFiles(System.String)">
            <summary>
            查询暂存区文件
            </summary>
            <param name="docClass"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.BaseController.UseStagingAreaFile(XH.H82.Models.Dtos.Base.AreaFileDto)">
            <summary>
            使用暂存区文件
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.BaseController.UnUserStagingAreaFile(System.String)">
            <summary>
            已使用文件回归暂存区
            </summary>
            <param name="docId"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.Certificate.CertificateController.GetEquipmentCertificates(System.String)">
            <summary>
            查询设备相关证书
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.Certificate.CertificateController.CreateEquipmentCertificate(System.String,XH.H82.Models.Dtos.Certificate.EquipmentCertificateInput)">
            <summary>
            添加设备的证书信息
            </summary>
            <param name="equipmentId"></param>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.Certificate.CertificateController.AddCertificateAttachment(System.String,XH.H82.Models.Dtos.UploadFileDto)">
            <summary>
            证书信息添加附件
            </summary>
            <param name="cretificateId">正式信息ID</param>
            <param name="file">模型内的DOC_INFO_ID 不需要传参</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.Certificate.CertificateController.EditEquipmentCertificate(System.String,XH.H82.Models.Dtos.Certificate.EquipmentCertificateInput)">
            <summary>
            编辑证书信息
            </summary>
            <param name="cretificateId"></param>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.Certificate.CertificateController.DeleteEquipmentCertificate(System.String)">
            <summary>
            删除证书信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.Certificate.CertificateController.DeleteEquipmentCertificateAttachment(System.String)">
            <summary>
            删除附件
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.Certificate.CertificateController.GetEquipmentCompanyCertificates(System.String)">
            <summary>
            获取设备关联供应商列表
            </summary>
            <param name="equipmentId"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.Certificate.CertificateController.GetCertificateTypes(System.String,System.String)">
            <summary>
            获取证书类型列表
            </summary>
            <param name="typeName">类型名称模糊查询</param>
            <param name="state">状态：0禁用、1启用 不填则都 返回</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.Certificate.CertificateController.EditCertificateType(System.String,System.String,System.String)">
            <summary>
            编辑证书类型数据
            </summary>
            <param name="id"></param>
            <param name="certificateTypeName"></param>
            <param name="remark"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.Certificate.CertificateController.CreateCertificateType(System.String,System.String)">
            <summary>
            创建证书类型
            </summary>
            <param name="certificateTypeName"></param>
            <param name="remark"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.Certificate.CertificateController.DeleteCertificateType(System.String)">
            <summary>
            删除证书类型
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.Certificate.CertificateController.DisableOrEnableCertificateType(System.String)">
            <summary>
            禁用/启用证书类型
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.CurrencyController.GetPersonList(System.String,System.String)">
            <summary>
              获取人事列表
            </summary>
            <param name="areaId">院区ID</param>
            <param name="mgroupId">专业组ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.CurrencyController.GetScrapStopPersonList(System.String)">
            <summary>
              获取人事列表
            </summary>
            <param name="areaId">院区ID</param>
            <param name="mgroupId">专业组ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.CurrencyController.GetLabList(System.String)">
            <summary>
              获取科室列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.CurrencyController.GetPersonTypeList">
            <summary>
              获取人员类型列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.CurrencyController.GetPostList">
            <summary>
              获取人员职务列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.CurrencyController.GetProfessionalClass">
            <summary>
              获取专业分类列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.CurrencyController.AddPostInfo(System.String)">
            <summary>
              添加职务岗位信息
            </summary>
            <param name="input">输入</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.CurrencyController.DeletePostInfo(System.String)">
            <summary>
              删除职务岗位信息
            </summary>
            <param name="BasciId">ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.CurrencyController.AddPersonTypeInfo(System.String)">
            <summary>
              添加人员类型信息
            </summary>
            <param name="input">输入</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.CurrencyController.DeletePersonTypeInfo(System.String)">
            <summary>
              删除人员类型信息
            </summary>
            <param name="BasciId">ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.CurrencyController.GetPgroupPullList(System.String)">
            <summary>
            获取实验室下拉
            </summary>
            <param name="areaId"></param>
            <returns></returns>
        </member>
        <member name="T:XH.H82.API.Controllers.EquipmentClassNew.EquipmentArchivesController">
            <summary>
            档案记录
            </summary>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentClassNew.EquipmentArchivesController.#ctor(XH.H82.IServices.EquipmentClassNew.IEquipmentClassService,XH.H82.IServices.EquipmentClassNew.IEquipomentArchivesService)">
            <summary>
            构造方法
            </summary>
            <param name="equipmentClassNewService"></param>
            <param name="equipomentArchivesService"></param>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentClassNew.EquipmentArchivesController.GetEquipmentArchives">
            <summary>
            查询档案记录字典
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentClassNew.EquipmentArchivesController.AddEquipmentArchives(XH.H82.IServices.EquipmentClassNew.AddEquipmentArchivesDto)">
             <summary>
             新增设备档案记录
             </summary>
             <param name="input"></param>
             <returns></returns>
            
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentClassNew.EquipmentArchivesController.UpdateEquipmentArchives(System.String,XH.H82.IServices.EquipmentClassNew.AddEquipmentArchivesDto)">
            <summary>
            更新设备档案记录
            </summary>
            <param name="eqpArchivesId">档案记录id</param>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentClassNew.EquipmentArchivesController.DeleteEquipmentArchives(System.String)">
            <summary>
            删除档案记录
            </summary>
            <param name="EqpArchivesId">档案记录id</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentClassNew.EquipmentArchivesController.EnableOrDisableEquipmentArchives(System.String)">
            <summary>
            停用或启用设备档案记录
            </summary>
            <param name="EqpArchivesId">档案记录id</param>
            <returns></returns>
        </member>
        <member name="T:XH.H82.API.Controllers.EquipmentClassNew.EquipmentClassNewController">
            <summary>
            设备类型新版本
            </summary>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentClassNew.EquipmentClassNewController.GetEquipmentClassDictTree(System.Boolean,System.Boolean)">
            <summary>
            查询设备类字典树
            </summary>
            <param name="hasAll">是否需要“全部设备节点” 默认需要</param>
            <param name="isEdit">是否编辑</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentClassNew.EquipmentClassNewController.AddEquipmentClassDict(XH.H82.Models.EquipmengtClassNew.AddEquipmentClassDto)">
            <summary>
            添加细分类设备类型
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentClassNew.EquipmentClassNewController.UpdateEquipmentClassDict(System.String,XH.H82.Models.EquipmengtClassNew.AddEquipmentClassDto)">
            <summary>
            更新细分类设备类型
            </summary>
            <param name="classId">设备类型id</param>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentClassNew.EquipmentClassNewController.EnableOrDisableEquipmentClassDict(System.String)">
            <summary>
            停用/启用设备类型
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentClassNew.EquipmentClassNewController.DeleteEquipmentCodeCustomDict(System.String)">
            <summary>
            删除设备类型字典
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentClassNew.EquipmentClassNewController.GetEquipmentArchivesLinkByClassId(System.String)">
            <summary>
            查询设备类型字典已关联的档案
            </summary>
            <param name="classId"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentClassNew.EquipmentClassNewController.GetEquipmentArchivesNotLinkByClassId(System.String)">
            <summary>
            查询设备类型字典未关联的档案
            </summary>
            <param name="classId"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentClassNew.EquipmentClassNewController.LinkArchivesByClassId(System.String,System.String[])">
            <summary>
            建立关联设备类型和档案记录
            </summary>
            <param name="classId">设备类型id</param>
            <param name="archivesIds">档案记录ids</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentClassNew.EquipmentClassNewController.UnLinkArchivesByClassId(System.String,System.String[])">
            <summary>
            取消建立关联设备类型和档案记录
            </summary>
            <param name="classId">设备类型id</param>
            <param name="archivesIds">档案记录ids</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentClassNew.EquipmentClassNewController.GetFieldDicts(System.String)">
            <summary>
            复杂表单的属性字典
            </summary>
            <param name="filedClass"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentClassNew.EquipmentClassNewController.CreateComplexForms(System.String)">
            <summary>
            添加设备类型字典的复杂表单模板
            </summary>
            <param name="classId"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentClassNew.EquipmentClassNewController.GetComplexForms(System.String,System.String,System.String)">
            <summary>
            查询复杂表单
            </summary>
            <param name="classId"></param>
            <param name="merge"></param>
            <param name="qunitId"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentClassNew.EquipmentClassNewController.InitBaseInfoFieldDict">
            <summary>
            初始化字段库数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentClassNew.EquipmentClassNewController.SaveFieldDict(XH.H82.Models.BusinessModuleClient.H04.FieldDictDto)">
            <summary>
            复杂表单基础模板新增/保存
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentClassNew.EquipmentClassNewController.DeleteFieldDict(XH.H82.Models.BusinessModuleClient.H04.FieldDictDto)">
            <summary>
            删除复杂表单基础模板
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentClassNew.EquipmentClassNewController.GetToolBoxUrl(System.String)">
            <summary>
            获取工具箱的页面设备url
            </summary>
            <param name="setupId"></param>
            <returns></returns>
        </member>
        <member name="T:XH.H82.API.Controllers.EquipmentCodeCustom.CodeCustomController">
            <summary>
            设备自定义编码字段方法
            </summary>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentCodeCustom.CodeCustomController.GetEquipmentCodeCustomDict">
            <summary>
            获取自定义名称模板列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentCodeCustom.CodeCustomController.AddEquipmentCodeCustomDict(XH.H82.Models.EquipmentCodeCustom.AddEquipmentCodeCustomDictDto)">
            <summary>
            新增自定义名称模板
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentCodeCustom.CodeCustomController.EnableOrDisableEquipmentCodeCustomDict(System.String)">
            <summary>
            停用/启用自定义名称模板列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentCodeCustom.CodeCustomController.UpdateEquipmentCodeCustomDict(System.String,XH.H82.Models.EquipmentCodeCustom.UpdateEquipmentCodeCustomDictDto)">
            <summary>
            更新自定义名称模板
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentCodeCustom.CodeCustomController.DeleteEquipmentCodeCustomDict(System.String)">
            <summary>
            删除字典模板列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentCodeCustom.CodeCustomController.GetEquipmentUCodePreview(System.String,XH.H82.Models.EquipmentCodeCustom.DisplayContent)">
            <summary>
            获取设备下预览模板名字
            </summary>
            <param name="dictContent">自定义模板</param>
            <param name="eqpNoClass">一个设备类型的ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentCodeCustom.CodeCustomController.GetCustomDictCode">
            <summary>
            查询可选的设备自定义属性字段
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.GetEquipmentList(System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
              获取设备信息列表
            </summary>
            <param name="areaId">院区ID</param>
            <param name="state">设备状态</param>
            <param name="type">设备类型</param>
            <param name="mgroupId">管理专业组id</param>
            <param name="keyWord">检索关键字</param>
            <param name="pgroupId">检验专业组ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.GetEquipmentInfo(System.String)">
            <summary>
              获取设备信息
            </summary>
            <param name="equipmentId">设备ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.AddEquipmentInfo(XH.H82.Models.Entities.EMS_EQUIPMENT_INFO)">
            <summary>
              添加设备信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.UpdateEquipmentInfo(XH.H82.Models.Entities.EMS_EQUIPMENT_INFO)">
            <summary>
              修改设备信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.DeleteEquipmentInfo(System.String)">
            <summary>
              删除设备信息
            </summary>
            <param name="equipmentId">设备ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.GetCompanyList(System.String,System.String,System.String)">
            <summary>
              获取供应商列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.GetCompanyContactList(System.String,System.String,System.String,System.String,System.String)">
            <summary>
              获取联系人列表
            </summary>
            <param name="companyId">公司ID</param>
            <param name="equipmentId">设备ID</param>
            <param name="contactType">制造商类型</param>
            <param name="keyword">检索</param>
            <param name="contactState">联系人状态</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.UpdateCompanyContact(System.Collections.Generic.List{XH.H82.Models.Entities.SYS6_COMPANY_CONTACT},System.String,System.String)">
            <summary>
              修改联系人信息
            </summary>
            <param name="equipmentId">设备ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.GetEnviRequireInfo(System.String)">
            <summary>
              获取设备环境要求
            </summary>
            <param name="equipmentId">设备ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.AddEnviRequireInfo(XH.H82.Models.Entities.EMS_ENVI_REQUIRE_INFO)">
            <summary>
              添加设备环境要求
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.UpdateEnviRequireInfo(XH.H82.Models.Entities.EMS_ENVI_REQUIRE_INFO)">
            <summary>
              修改设备环境要求
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.GetSubscribeInfo(System.String)">
            <summary>
              获取设备申购信息
            </summary>
            <param name="equipmentId">设备ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.SaveSubscribeInfo(XH.H82.Models.Entities.EMS_SUBSCRIBE_INFO)">
            <summary>
              商务信息页面保存申购信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.GetPurchaseInfo(System.String)">
            <summary>
              获取设备采购信息
            </summary>
            <param name="equipmentId">设备ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.AddPurchaseInfo(XH.H82.Models.Entities.EMS_PURCHASE_INFO)">
            <summary>
              添加设备采购信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.UpdatePurchaseInfo(XH.H82.Models.Entities.EMS_PURCHASE_INFO)">
            <summary>
              修改设备采购信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.GetInstallInfo(System.String)">
            <summary>
              获取设备安装信息
            </summary>
            <param name="equipmentId">设备ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.AddInstallInfo(XH.H82.Models.Entities.EMS_INSTALL_INFO)">
            <summary>
              添加设备安装信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.UpdateInstallInfo(XH.H82.Models.Entities.EMS_INSTALL_INFO)">
            <summary>
              修改设备安装信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.GetUnpackInfo(System.String)">
            <summary>
              获取设备开箱记录
            </summary>
            <param name="equipmentId">设备ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.AddUnpackInfo(XH.H82.Models.Entities.EMS_UNPACK_INFO)">
            <summary>
              添加设备开箱信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.UpdateUnpackInfo(XH.H82.Models.Entities.EMS_UNPACK_INFO)">
            <summary>
              修改设备开箱信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.GetPartsList(System.String)">
            <summary>
              获取设备配件列表
            </summary>
            <param name="equipmentId">设备ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.AddPartsInfo(XH.H82.Models.Entities.EMS_PARTS_INFO)">
            <summary>
              添加设备配件信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.UpdatePartsInfo(XH.H82.Models.Entities.EMS_PARTS_INFO)">
            <summary>
              修改设备配件信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.DeletePartsInfo(System.String)">
            <summary>
              删除设备配件信息
            </summary>
            <param name="partsId">设备配件ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.GetTrainList(System.String)">
            <summary>
              获取设备培训记录列表
            </summary>
            <param name="equipmentId">设备ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.AddTrainInfo(XH.H82.Models.Entities.EMS_TRAIN_INFO)">
            <summary>
              添加设备培训记录信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.UpdateTrainInfo(XH.H82.Models.Entities.EMS_TRAIN_INFO)">
            <summary>
              修改设备培训记录信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.DeleteTrainInfo(System.String)">
            <summary>
              删除设备培训记录信息
            </summary>
            <param name="trainId">设备培训记录ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.GetDebugInfo(System.String)">
            <summary>
              获取设备调试和运行记录
            </summary>
            <param name="equipmentId">设备ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.AddDebugInfo(XH.H82.Models.Entities.EMS_DEBUG_INFO)">
            <summary>
              添加设备调试和运行记录
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.UpdateDebugInfo(XH.H82.Models.Entities.EMS_DEBUG_INFO)">
            <summary>
              修改设备调试和运行记录
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.GetAuthorizeList(System.String)">
            <summary>
              获取授权记录列表
            </summary>
            <param name="equipmentId">设备ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.AddAuthorizeInfo(XH.H82.Models.Entities.EMS_AUTHORIZE_INFO)">
            <summary>
              添加授权记录
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.UpdateAuthorizeInfo(XH.H82.Models.Entities.EMS_AUTHORIZE_INFO)">
            <summary>
              修改授权记录
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.DeleteAuthorizeInfo(System.String)">
            <summary>
              删除授权记录
            </summary>
            <param name="authorizeId">设备授权ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.GetStartupInfo(System.String)">
            <summary>
              获取开机性能验证信息
            </summary>
            <param name="equipmentId">设备ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.AddStartupInfo(XH.H82.Models.Entities.EMS_STARTUP_INFO)">
            <summary>
              添加开机性能验证信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.UpdateStartupInfo(XH.H82.Models.Entities.EMS_STARTUP_INFO)">
            <summary>
              修改开机性能验证信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.GetStartStopList(System.String)">
            <summary>
              获取设备启停列表
            </summary>
            <param name="equipmentId">设备ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.AddStartInfo(XH.H82.Models.Entities.EMS_START_STOP)">
            <summary>
              添加设备启用信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.UpdateStartInfo(XH.H82.Models.Entities.EMS_START_STOP)">
            <summary>
              修改设备启用信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.GetScrapInfo(System.String)">
            <summary>
              获取设备报废信息
            </summary>
            <param name="equipmentId">设备ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.GetContactInfoList(System.String,System.String,System.String,System.String)">
            <summary>
              获取供应商信息列表
            </summary>
            <param name="equipmentId">设备ID</param>
            <param name="state">状态</param>
            <param name="supplier">供应商类型</param>
            <param name="keyWord">检索</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.AddContactInfo(XH.H82.Models.Entities.SYS6_COMPANY_CONTACT)">
            <summary>
              添加联系人信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.SaveContactInfo(XH.H82.Models.Entities.SYS6_COMPANY_CONTACT)">
            <summary>
              修改联系人信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.DeleteContactInfo(System.String)">
            <summary>
              删除供应商联系信息
            </summary>
            <param name="contactId">供应商联系id</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.GetCardTemplate(System.String,System.Collections.Generic.List{System.String})">
            <summary>
              获取设备标识卡
            </summary>
            <param name="cardType">标识卡类型（0：标准版；1：二维码版）</param>
            <param name="equipmentId">设备id列表</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.CreatCardTemplate(System.String,System.Collections.Generic.List{System.String})">
            <summary>
              生成标识卡
            </summary>
            <param name="cardType">标识卡类型（0：标准版；1：二维码版）</param>
            <param name="equipmentId">设备id</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.CardPrint(System.String,System.Collections.Generic.List{System.String})">
            <summary>
            标识卡打印
            </summary>
            <param name="equipmentId">设备ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.GetCompanyClassList">
            <summary>
            获取设备类型
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.ParseExcel(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            解析excel文件
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.GetEquipmentTemplate">
            <summary>
            获取设备导入模板
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.GetPipelineList(System.String,System.String)">
            <summary>
            获取流水线下拉
            </summary>
            <param name="pgroupId">检验专业组ID</param>
            <param name="areaId">院区ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.ExportDeviceList(System.Collections.Generic.List{XH.H82.Models.Entities.EMS_EQUIPMENT_INFO},System.String)">
            <summary>
            导出设备清单
            </summary>
            <param name="record"></param>
            <param name="exportType">导出类型（1：word，2：pdf）</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.DownloadEquipmentCard(System.Collections.Generic.List{System.String})">
            <summary>
            下载设备标识卡
            </summary>
            <param name="imageUrls"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.DistributionEquipmentsToSmblLab(System.String,System.Collections.Generic.List{System.String})">
            <summary>
            分配备案实验室
            </summary>
            <param name="smblLabId">备案实验室id</param>
            <param name="equipmentIds">设备ids</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentDocController.UnDistributionEquipmentsToSmblLab(System.Collections.Generic.List{System.String})">
            <summary>
            取消分配备案实验室
            </summary>
            <param name="equipmentIds">设备ids</param>
            <returns></returns>
        </member>
        <member name="T:XH.H82.API.Controllers.EquipmentWarning.EquipmentWarningController">
            <summary>
            设备预警相关信息
            </summary>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentWarning.EquipmentWarningController.GetGroups(System.String)">
            <summary>
            获取医疗机构下的专业组下拉
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentWarning.EquipmentWarningController.GetEquipmentWarnInfo(System.String)">
            <summary>
            在线接口-获取当前用户所能查看到的设备预警
            </summary>
            <param name="areaId">院区id</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentWarning.EquipmentWarningController.GetInkScreenDevices">
            <summary>
            获取水墨屏下拉列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentWarning.EquipmentWarningController.GetInkScreens(System.String,System.Nullable{XH.H82.Models.DeviceRelevantInformation.Enum.LightStateEnum},System.Nullable{XH.H82.Models.DeviceRelevantInformation.Enum.LineStateEnum},System.Nullable{XH.H82.Models.DeviceRelevantInformation.Enum.EquipmentStateEnum},System.String,System.String)">
            <summary>
            获取设备墨水屏列表
            </summary>
            <param name="lightState">亮灯状态</param>
            <param name="lineState">在线状态</param>
            <param name="equipmentState">设备状态</param>
            <param name="pGourpId"></param>
            <param name="macOrEquipmentCode">mac或者设备代号</param>
            <param name="hosptialId">医疗机构id</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentWarning.EquipmentWarningController.GetWquipmentWarnRecords(System.String,System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            获取设备历史报警信息
            </summary>
            <param name="equipmentId">设备id</param>
            <param name="warnMsg">报警信息</param>
            <param name="startTime">报警记录开始时间</param>
            <param name="endTime">报警记录结束时间</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentWarning.EquipmentWarningController.GetInkScreenBinds(System.String,System.Nullable{System.Boolean},System.String,System.String)">
            <summary>
            墨水屏绑定信息列表
            </summary>
            <param name="isBand">是否绑定 </param>
            <param name="pGourpId">检验专业组id</param>
            <param name="mac">mac地址</param>
            <param name="hosptialId">医疗机构id</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentWarning.EquipmentWarningController.GetInkScreenBindEquipments(System.String,System.String,System.String,System.String)">
            <summary>
            查询可绑定墨水屏的设备列表
            </summary>
            <param name="pGourpId">专业组Id</param>
            <param name="codeOrModelOrnName">设备代号/型号/名称检索</param>
            <param name="hosptialId">医疗机构id</param>
            <param name="labId">科室id</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentWarning.EquipmentWarningController.AddInkScreen(XH.H82.Models.DeviceRelevantInformation.Dto.AddInkScreenInput)">
            <summary>
            新增墨水屏信息
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentWarning.EquipmentWarningController.DeleteInkScreen(System.String)">
            <summary>
            删除墨水屏信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentWarning.EquipmentWarningController.UpdateInkScreen(System.String,XH.H82.Models.DeviceRelevantInformation.Dto.UpdateInkScreenInput)">
            <summary>
            保存墨水屏信息
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentWarning.EquipmentWarningController.BindEquipment(System.String,XH.H82.Models.DeviceRelevantInformation.Dto.BindInkScreen)">
            <summary>
            绑定设备设备信息
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentWarning.EquipmentWarningController.PowerCheck">
            <summary>
            电量检测
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentWarning.EquipmentWarningController.InkScreenlighted(System.String,XH.H82.Models.DeviceRelevantInformation.Enum.LightStateEnum)">
            <summary>
            控灯操作
            </summary>
            <param name="inkScreenId">水墨屏id</param>
            <param name="lightStateEnum">0熄灯，1亮灯</param>
            <returns></returns>
        </member>
        <member name="T:XH.H82.API.Controllers.EquipmentWarning.InkScreenController">
            <summary>
            墨水屏设计
            </summary>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentWarning.InkScreenController.GetAllTemplateAttributes">
            <summary>
            获取所有基础属性
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentWarning.InkScreenController.GetTemplatesTest1(System.String,System.String,System.String)">
            <summary>
            查询当权科室下，用户可以查询的墨水屏模板
            </summary>
            <param name="labId">科室id</param>
            <param name="groupId">检验专业组id</param>
            <param name="templateName">模板名称</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentWarning.InkScreenController.AddTemplate(XH.H82.Models.InkScreenTemplate.Dto.AddTemplateInput)">
            <summary>
            添加新的墨水屏模板
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentWarning.InkScreenController.UpdateTemplate(System.String,XH.H82.Models.InkScreenTemplate.Dto.UpdateTemplateInput)">
            <summary>
            修改墨水屏模板名称及备注
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentWarning.InkScreenController.SaveTemplate(System.String,XH.H82.Models.InkScreenTemplate.Dto.SaveTemplateInput)">
            <summary>
            保存模板内容
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentWarning.InkScreenController.DeleteTemplate(System.String)">
            <summary>
            软删除墨水屏模板名称及备注
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentWarning.InkScreenController.CopyTemplate(System.String)">
            <summary>
            复制模板
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentWarning.InkScreenController.GetTemplates(System.String,System.String,System.String)">
            <summary>
            查询当权科室下，用户可以查询的墨水屏模板
            </summary>
            <param name="labId">科室id</param>
            <param name="groupId">检验专业组id</param>
            <param name="templateName">模板名称</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentWarning.InkScreenController.GetGroups(System.String)">
            <summary>
            专业组下拉选项
            </summary>
            <param name="labId"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentWarning.InkScreenController.GetGroupsTree(System.String,System.String)">
            <summary>
            返回专业组树
            </summary>
            <param name="labId"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentWarning.InkScreenController.ApplicationGroups(System.String,System.Collections.Generic.List{System.String})">
            <summary>
            模板应用至专业组
            </summary>
            <param name="templateId">模板id</param>
            <param name="groups">专业组（复数）</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentWarning.InkScreenController.GetTemplatePreview(System.String,System.String)">
            <summary>
            模板预览接口
            </summary>
            <param name="templateId">模板id</param>
            <param name="parameters">基础信息参数aaa;bbb</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.EquipmentWarning.InkScreenController.SetInkScreen(System.String,System.Collections.Generic.List{XH.H82.Models.InkScreenTemplate.Dto.TemplateDataAndValue},System.Boolean,System.Boolean,System.String,System.String)">
            <summary>
            墨水屏自定义模板下发功能
            </summary>
            <param name="mac">设备mac地址</param>
            <param name="values">模板信息于值</param>
            <param name="nextMaintainDateStatus">下次保养时间是否添加红色边框</param>
            <param name="nextCorrectDateStatus">下次校准时间是否添加红色边框</param>
            <param name="circumstance">设备运行状态：启用，停用，报废，未启用,待报废，待停用</param>
            <param name="meassage">异常信息 ： 只能放 设备需要保养 、 设备需要校准、证书需要维护</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.FileManageController.GetFileTree(System.String,System.Boolean)">
            <summary>
            文档分类树结构
            </summary>
            <param name="labId">科室id</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.FileManageController.GetPgroupList(System.String)">
            <summary>
            文档管理页面专业组列表
            </summary>
            <param name="labId">当前所在的科室id</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.FileManageController.GetDocInfoList(System.String,System.String,System.String,System.String,System.String)">
            <summary>
              获取文档信息列表
            </summary>
            <param name="classId">文档树结构字段id</param>
            <param name="equipmentId">设备ID</param>
            <param name="pgroupId">专业组id</param>
            <param name="docType">文档类型（前端写死：项目SOP;仪器SOP）</param>
            <param name="keyword">检索</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.FileManageController.GetDocFile(System.String)">
            <summary>
              获取文档附件信息
            </summary>
            <param name="docId">文档id</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.FileManageController.UploadDocFile(System.Collections.Generic.List{XH.H82.Models.Dtos.DocFileDto},System.String,System.String,System.String)">
            <summary>
            链接文档系统文件应用到设备系统
            </summary>
            <param name="filePath">文件信息</param>
            <param name="equipmentId"> 设备Id </param>
            <param name="docInfoId"> 如果是记录类型的链接文档  保养记录、维修记录、变更记录、校准记录、比对记录、性能验证记录、使用记录 需要传入记录Id ，SOP档案、设备说明书 侧传空  </param> 
            <param name="docClass"> SOP档案、设备说明书、保养记录、维修记录、变更记录、校准记录、比对记录、性能验证记录、使用记录</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.FileManageController.FindDocumentNodesAndDocuments(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.Nullable{XH.H82.Models.Dtos.SysDocDtos.SOPType})">
            <summary>
            查询文件系统内部的节点及文件列表
            </summary>
            <param name="labId">科室id</param>
            <param name="classId">文档菜单类型id</param>
            <param name="equipmentId">设备id</param>
            <param name="docClass">当前类型</param>
            <param name="docType"></param>
            <param name="pgroupId">专业组id</param>
            <param name="docName">文件名</param>
            <param name="docClassId">SOP分类</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.FileManageController.AddEquipmentSopFiles(System.String,System.Collections.Generic.List{XH.H82.Models.Dtos.DocFileDto})">
            <summary>
            添加设备SOP档案   链接文档系统
            </summary>
            <param name="equipmentId"></param>
            <param name="filePath"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.FileManageController.GetEquipmentSopFiles(System.String)">
            <summary>
            获取设备sop文件列表
            </summary>
            <param name="equipmentId"></param>
            <returns></returns>
        </member>
        <member name="T:XH.H82.API.Controllers.FileTemplate.FileTemplateController">
            <summary>
            onlyoffic相关接口
            </summary>
        </member>
        <member name="M:XH.H82.API.Controllers.FileTemplate.FileTemplateController.ExportEquipmentPreview(System.String,System.String)">
            <summary>
            根据模板预览设备基本信息（浏览功能及预览pdf）
            </summary>
            <param name="equipmentId">设备ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.FileTemplate.FileTemplateController.ExportEquipmentsPreview(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            根据模板预览设备档案清单（设备列表）
            </summary>
            <param name="styleId"></param>
            <param name="areaId"></param>
            <param name="hospitalId"></param>
            <param name="state"></param>
            <param name="type"></param>
            <param name="mgroupId"></param>
            <param name="keyWord"></param>
            <param name="labId"></param>
            <param name="pgroupId"></param>
            <returns></returns>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="M:XH.H82.API.Controllers.FileTemplate.FileTemplateController.ExportEquipmentsWorkPlanPreview(System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            根据模板预览工作计划列表
            </summary>
            <param name="styleId"></param>
            <param name="labId"></param>
            <param name="keyword"></param>
            <param name="mgroupId"></param>
            <param name="equipmentClass"></param>
            <param name="pgroupId"></param>
            <param name="areaId"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.FileTemplate.FileTemplateController.ExportEquipmentWord(System.String,System.String)">
            <summary>
            根据模板导出设备基础信息word文件
            </summary>
            <param name="equipmentId"></param>
            <param name="styleId"></param>
            <returns></returns>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="M:XH.H82.API.Controllers.FileTemplate.FileTemplateController.ExportEquipmentsWorkPlanWord(System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            根据模板导出工作计划列表word文件
            </summary>
            <param name="styleId"></param>
            <param name="labId"></param>
            <param name="keyword"></param>
            <param name="mgroupId"></param>
            <param name="equipmentClass"></param>
            <param name="pgroupId"></param>
            <param name="areaId"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.FileTemplate.FileTemplateController.ExportEquipmentsWord(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            根据模板导出设备档案清单（设备列表）word文件
            </summary>
            <param name="styleId"></param>
            <param name="areaId"></param>
            <param name="hospitalId"></param>
            <param name="state"></param>
            <param name="type"></param>
            <param name="mgroupId"></param>
            <param name="keyWord"></param>
            <param name="labId"></param>
            <param name="pgroupId"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.FileTemplate.FileTemplateController.GetTemplateSettingsUrl">
            <summary>
            获取编辑文档的iframe路径
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.FileTemplate.FileTemplateController.ExportEquipmentsExcelData(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            根据模板导出设备列表excel数据
            </summary>
            <param name="styleId"></param>
            <param name="areaId"></param>
            <param name="hospitalId"></param>
            <param name="state"></param>
            <param name="type"></param>
            <param name="mgroupId"></param>
            <param name="keyWord"></param>
            <param name="labId"></param>
            <param name="pgroupId"></param>
            <returns></returns>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="M:XH.H82.API.Controllers.FileTemplate.FileTemplateController.ExportEquipmentsWorkPlanExcelData(System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            根据模板导出工作计划列表excel数据
            </summary>
            <param name="styleId"></param>
            <param name="labId"></param>
            <param name="keyword"></param>
            <param name="mgroupId"></param>
            <param name="equipmentClass"></param>
            <param name="pgroupId"></param>
            <param name="areaId"></param>
            <returns></returns>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="M:XH.H82.API.Controllers.Inspection.InspectionController.GetEquipmentListByMgroup(System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
              设备树结构（专业组）
            </summary>
            <param name="labId">科室id</param>
            <param name="pgroupId">专业组id</param>
            <param name="equipmentNo">设备代号</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.Inspection.InspectionController.GetEquipmentList(System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
              获取设备信息列表
            </summary>
            <param name="labId">科室id</param>
            <param name="type">设备类型</param>
            <param name="keyWord">检索关键字</param>
            <param name="pgroupId">检验专业组ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.Inspection.InspectionController.GetHospitalProfessionalGroups(System.String)">
            <summary>
            获取当前科室所有院区的管理专业组下的检验专业组
            </summary>
            <param name="labId"></param>
            <returns></returns>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.CalendarRecordDto.Id">
            <summary>
            记录id
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.CalendarRecordDto.Type">
            <summary>
            记录类型
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.CalendarRecordDto.TypeName">
            <summary>
            记录类型 中文
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.CalendarRecordDto.Title">
            <summary>
            标题（设备名称）
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.CalendarRecordDto.BeginDate">
            <summary>
            开始时间 （yyyy-MM-dd）
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.CalendarRecordDto.EndDate">
            <summary>
            结束时间（yyyy-MM-dd）
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.CalendarRecordsDto.Type">
            <summary>
            记录类型
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.CalendarRecordsDto.TypeName">
            <summary>
            记录类型 中文
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.ConutAnnualEquipmentInspectionDto.Month">
            <summary>
            月份
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.ConutAnnualEquipmentInspectionDto.Count">
            <summary>
            数量
            </summary>
        </member>
        <member name="F:XH.H82.API.Controllers.IoTDevice.ConutAnnualEquipmentInspectionDto.CountWorkPlans">
            <summary>
            需要年检的设备数量
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.ConutHosptailEquipmentDistributionDto.Month">
            <summary>
            月份
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.ConutHosptailEquipmentDistributionDto.Duration">
            <summary>
            时长（小时）
            </summary>
        </member>
        <member name="T:XH.H82.API.Controllers.IoTDevice.CountTheTotalUseEquipmentCategoriesDto">
            <summary>
            设备运行情况单个设备分类模型
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.CountTheTotalUseEquipmentCategoriesDto.SmblLabId">
            <summary>
            备案实验室Id
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.CountTheTotalUseEquipmentCategoriesDto.SmblLabName">
            <summary>
            备案实验室名称
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.CountTheTotalUseEquipmentCategoriesDto.SmblClass">
            <summary>
            生安设备类型
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.CountTheTotalUseEquipmentCategoriesDto.EquipmentName">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.CountTheTotalUseEquipmentCategoriesDto.DayUsageRate">
            <summary>
            当日使用率
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.CountTheTotalUseEquipmentCategoriesDto.CurrentUseDayHours">
            <summary>
            当日使用小时
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.CountTheTotalUseEquipmentCategoriesDto.CurrentAvgUseMoonHours">
            <summary>
            当月平均每天使用小时
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.CountTheTotalUseEquipmentCategoriesDto.CurrentAvgUseMoonUseDay">
            <summary>
            当月使用天数
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.CountTheTotalUseEquipmentCategoriesDto.IsAccess">
            <summary>
            是否接入
            </summary>
        </member>
        <member name="T:XH.H82.API.Controllers.IoTDevice.CountTheTotalUseEquipmentDto">
            <summary>
            设备运行情况信息模型
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.CountTheTotalUseEquipmentDto.SmblClass">
            <summary>
            生安设备类型
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.CountTheTotalUseEquipmentDto.SmblClassName">
            <summary>
            生安设备类型名
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.CountTheTotalUseEquipmentDto.TotalEquipmentCount">
            <summary>
            当日使用总台数
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.CountTheTotalUseEquipmentDto.CurrentEquipmentCount">
            <summary>
            当日使用台数
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.CountTheTotalUseEquipmentDto.CurrentUseDayHours">
            <summary>
            当日使用小时数/全部的设备的使用时间加起来
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.CountTheTotalUseEquipmentDto.AvgMoonthUseCount">
            <summary>
            当月平均每台使用天数
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.CountTheTotalUseEquipmentDto.AvgMoonthUseHoursCount">
            <summary>
            当月平均每台使用小时
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.CountTheTotalUseEquipmentDto.IsAccess">
            <summary>
            是否接入
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.CountTheTotalUseEquipmentDto.Categories">
            <summary>
            使用情况明细
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.Dtos.OverviewMaintainedDto.SmblClass">
            <summary>
            生安设备类型id
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.Dtos.OverviewMaintainedDto.SmblClassName">
            <summary>
            生安设备类型名称
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.Dtos.OverviewMaintainedDto.PreMaintained">
            <summary>
            带维护数量
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.Dtos.OverviewMaintainedDto.TotalMaintained">
            <summary>
            设备数量总数
            </summary>
        </member>
        <member name="T:XH.H82.API.Controllers.IoTDevice.EquipmentDistributionDto">
            <summary>
            设备大各个状态大概情况
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.EquipmentDistributionDto.AllNumList">
            <summary>
            总数 重点设备/全部设备
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.EquipmentDistributionDto.equipmentStateList">
            <summary>
            各个状态总数
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.DistributionDto.Num">
            <summary>
            数量
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.DistributionDto.name">
            <summary>
            状态名称
            </summary>
        </member>
        <member name="M:XH.H82.API.Controllers.IoTDevice.IoTDeviceController.EquipmentInit(System.String)">
            <summary>
            智能插座监测记录测试
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.IoTDevice.IoTDeviceController.EquipmentInit1111(System.String)">
            <summary>
            智能插座监测记录测试
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.IoTDevice.IoTDeviceController.GetIoTDevieces">
            <summary>
            获取智能插座列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.IoTDevice.IoTDeviceController.GetChuanqganqi">
            <summary>
            获取传感器列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.IoTDevice.IoTDeviceController.GetCalendarRecords(System.Nullable{System.DateTime},System.String)">
            <summary>
            获取生安设备日历各种记录
            </summary>
            <param name="time">时间范围 yyyy-MM 精确到月</param>
            <param name="smblLabId">备案实验室id 选填</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.IoTDevice.IoTDeviceController.GetDviceMonitorsStatisticsByLineChartAndMrak(System.String,System.Nullable{System.DateTime},System.Double,System.String,System.String)">
            <summary>
            生安监测设备-折现和状态矩形
            </summary>
            <param name="equipmentId"></param>
            <param name="date"></param>
            <param name="timeSpan"></param>
            <param name="smblClass">生安设备类型</param>
            <param name="ItemId">监测项</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.IoTDevice.IoTDeviceController.GetDeviceMonitorUseRecoreds(System.String,System.DateTime,System.DateTime,System.String,System.String)">
            <summary>
            重点监测设备的使用记录-日汇总  
            </summary>
            <param name="equipmentId">设备id</param>
            <param name="startDate">开始时间</param>
            <param name="endDate">结束时间</param>
            <param name="smblClass">生安设备类型</param>
            <param name="itemId">监测项id</param>
            <returns> 生物安全柜、水压、洗眼器  环境一体机 </returns>
        </member>
        <member name="M:XH.H82.API.Controllers.IoTDevice.IoTDeviceController.AnalyzeRecordsByDay(System.DateTime,System.Collections.Generic.List{XH.H82.Models.Entities.THS.THS_MONITOR_RESULT},XH.H82.Models.BusinessModuleClient.Dto.IoTDevices.DevicesStatusValue)">
            <summary>
            统计使用状态的列表
            </summary>
            <param name="date"></param>
            <param name="records"></param>
            <param name="devicesStatusValue"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.IoTDevice.IoTDeviceController.CountTheTotalUseEquipmentCategories(System.String,System.String,System.String)">
            <summary>
            统计生安重点设备类的总使用情况
            </summary>
            <param name="hosiptalId">机构id</param>
            <param name="labId">科室id</param>
            <param name="smblLabId">备案实验室id</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.IoTDevice.IoTDeviceController.ConutReminders(System.String)">
            <summary>
            看板提醒事项
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.IoTDevice.IoTDeviceController.GetEquipmentYearCheckCount(XH.H82.Models.Smbl.SmblInletEnum,System.String)">
            <summary>
            获取近两年的需要年检的设备数量
            </summary>
            <param name="inlet">入口类型  0机构 1科室 2备案实验室</param>
            <param name="id">入口的id  机构id 或者科室id 或者备案实验室id</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.IoTDevice.IoTDeviceController.GetEquipmentYearCheck(System.Nullable{System.Int32},XH.H82.Models.Smbl.SmblInletEnum,System.String)">
            <summary>
            获取当前 年 的需要年检的设备数量  前端实现每个月的分组计算
            </summary>
            <param name="year">年份</param>
            <param name="inlet">入口类型  0机构 1科室 2备案实验室</param>
            <param name="id">入口的id  机构id 或者科室id 或者备案实验室id</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.IoTDevice.IoTDeviceController.GetEquipmentsDistribution(XH.H82.Models.Smbl.SmblInletEnum,System.String)">
            <summary>
            查询设备大概情况
            </summary>
            <param name="inlet">入口类型  0机构 1科室 2备案实验室</param>
            <param name="id">入口的id  机构id 或者科室id 或者备案实验室id</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.IoTDevice.IoTDeviceController.ConutHosptailEquipmentDistribution(System.String,System.String,System.String,System.String,System.String,System.Boolean)">
            <summary>
            获取机构层级下的设备使用趋势
            </summary>
            <param name="hospitalId">机构id</param>
            <param name="labId">科室id</param>
            <param name="smblLabId">备案实验室id</param>
            <param name="smblClass">生安设备类型id</param>
            <param name="equipmentName">设备名称</param>
            <param name="isSamePeriod">是否同期  默认false</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.IoTDevice.IoTDeviceController.ConutOverviewMaintained(System.String)">
            <summary>
            设备待维护按生安设备类型统计
            </summary>
            <param name="smblLabId">备案实验室</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.IoTDevice.IoTDeviceController.ConutOverviewMaintainedDetal(System.String)">
            <summary>
            设备待维护按具体档案部分统计
            </summary>
            <param name="smblLabId"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.IoTDevice.IoTDeviceController.ChangeThsEquipmentSn(System.String,System.String)">
            <summary>
            修改设备的sn码
            </summary>
            <param name="id"></param>
            <param name="sn"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.IoTDevice.IoTDeviceController.ChangeThsEquipmentDid(System.String,System.String)">
            <summary>
            修改设备的did
            </summary>
            <param name="thsEquipmentId"></param>
            <param name="emsEquipmentId"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.IoTDevice.IoTDeviceController.GetAccessControls">
            <summary>
            电信门禁设备列表
            </summary>
            <returns></returns>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.IotDeviceUsingRecordDto.No">
            <summary>
            序号，唯一
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.IotDeviceUsingRecordDto.Date">
            <summary>
            日期 2025-01-02
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.IotDeviceUsingRecordDto.EarliestStartTime">
            <summary>
            最早开机时间
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.IotDeviceUsingRecordDto.LatestShutdownTime">
            <summary>
            最晚关机时间
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.IotDeviceUsingRecordDto.TotalIdleTime">
            <summary>
            待机时长汇总，分钟
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.IotDeviceUsingRecordDto.TotalUsageTime">
            <summary>
            使用时长汇总，分钟
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.IotDeviceUsingRecordDto.TotalShutdownTime">
            <summary>
            关机时长汇总，分钟
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.IotDeviceUsingRecordDto.FirstWarningTime">
            <summary>
            最早报警时间
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.IotDeviceUsingRecordDto.HandleCont">
            <summary>
            异常处理次数
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.IoTDviceMonitorRecordDto.MonitorTime">
            <summary>
            监测时间
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.IoTDviceMonitorRecordDto.MonitorType">
            <summary>
            监测类型
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.IoTDviceMonitorRecordDto.Voltage">
            <summary>
            电压
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.IoTDviceMonitorRecordDto.Current">
            <summary>
            电流
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.IoTDviceMonitorRecordDto.Power">
            <summary>
            功率
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.IoTDviceMonitorRecordDto.Electricity">
            <summary>
            用电量
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.IoTDviceMonitorRecordDto.AcquisitionTime">
            <summary>
            采集时间
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.IoTDviceMonitorRecordDto.OverTime">
            <summary>
            状态结束时间
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.LineChartDto.Value">
            <summary>
            值
            </summary>
        </member>
        <member name="P:XH.H82.API.Controllers.IoTDevice.LineChartDto.Status">
            <summary>
            对应状态
            </summary>
        </member>
        <member name="M:XH.H82.API.Controllers.IoTDevice.LineChartDto.GetStatus(System.Double,System.String,System.String)">
            <summary>
            根据不同设备类型以及监测项 计算开机关机待机消毒状态
            </summary>
            <param name="value">监测记录中的item_value</param>
            <param name="monitoringProject">不同类型用于判断使用状态的监测项item_id</param>
            <param name="smblClass">生安设备类型</param>
            <returns></returns>
        </member>
        <member name="T:XH.H82.API.Controllers.IoTDevice.PushAlertsController">
            <summary>
            外部入参接口
            </summary>
        </member>
        <member name="M:XH.H82.API.Controllers.IoTDevice.PushAlertsController.PushMedicalEquipmentAlerts(XH.H82.API.Controllers.IoTDevice.Dto.MedicalEquipmentAlertDto)">
            <summary>
            医疗设备告警推送接口
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.IoTDevice.PushAlertsController.PushCameraAlerts(XH.H82.API.Controllers.IoTDevice.Dto.CameraAlertDto)">
            <summary>
            AI、IPC摄像头推送接口
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.IoTDevice.PushAlertsController.InitCameraAlerts">
            <summary>
            AI告警类型初始化
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.IoTDevice.PushAlertsController.InitCamera(System.String,System.String,System.String,System.Boolean)">
            <summary>
            摄像头数据初始化
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.IoTDevice.PushAlertsController.ExportToBeMaintainedEquipments">
            <summary>
            导出设备待维护信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.LoginController.UserLogin(System.Object)">
            <summary>
            用户登录
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OfficeExcelController.GetLabPGroupList(System.String)">
            <summary>
            H115 GetLabPGroupList  接口
            </summary>
            <param name="LAB_ID">科室ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OfficeExcelController.GetTemplateList(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            H115 GetTemplateList 接口
            </summary>
            <param name="HOSPITAL_ID">机构ID</param>
            <param name="LAB_ID">科室ID</param>
            <param name="AREA_ID">院区ID</param>
            <param name="PGROUP_ID">检验专业组ID</param>
            <param name="LAB_PGROUP_TYPE">专业组科室分类，0科室，1专业组 ，默认1</param>
            <param name="STYLE_CLASS_CODE">模板分类code</param>
            <param name="DATA_ID">数据id</param>
            <param name="STYLE_NAME">模板名称</param>
            <param name="CATALOG_NAME">目录名称</param>
            <param name="STYLE_IDS">模板styleId集合 “，”分割</param>
            <remarks>STYLE_IDS = "123,1233,46555"</remarks>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OfficeExcelController.GetLastVisLocation(System.String,System.String,System.String,System.String)">
            <summary>
            获取上次页面访问记录
            </summary>
            <param name="labId">科室id</param>
            <param name="styleClassCode">分类code</param>
            <param name="flag">前端一个页面多个清单模板</param>
            <param name="loadMode">web传1</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OfficeExcelController.AddVisLocation(XH.H82.API.Controllers.OfficeExcelController.VisLocationInput)">
            <summary>
            新增清单功能上次记录页面
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="T:XH.H82.API.Controllers.OfficeExcelController.VisLocationInput">
            <summary>
            添加访问记录模型
            </summary>
            <param name="labId">科室id</param>
            <param name="styleClassCode">分类code</param>
            <param name="loadMode">web 传1</param>
            <param name="flag">前端有需要可传</param>
            <param name="locationInfo">页面信息</param>
            <param name="extInfo">拓展信息</param>
        </member>
        <member name="M:XH.H82.API.Controllers.OfficeExcelController.VisLocationInput.#ctor(System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            添加访问记录模型
            </summary>
            <param name="labId">科室id</param>
            <param name="styleClassCode">分类code</param>
            <param name="loadMode">web 传1</param>
            <param name="flag">前端有需要可传</param>
            <param name="locationInfo">页面信息</param>
            <param name="extInfo">拓展信息</param>
        </member>
        <member name="P:XH.H82.API.Controllers.OfficeExcelController.VisLocationInput.labId">
            <summary>科室id</summary>
        </member>
        <member name="P:XH.H82.API.Controllers.OfficeExcelController.VisLocationInput.styleClassCode">
            <summary>分类code</summary>
        </member>
        <member name="P:XH.H82.API.Controllers.OfficeExcelController.VisLocationInput.loadMode">
            <summary>web 传1</summary>
        </member>
        <member name="P:XH.H82.API.Controllers.OfficeExcelController.VisLocationInput.flag">
            <summary>前端有需要可传</summary>
        </member>
        <member name="P:XH.H82.API.Controllers.OfficeExcelController.VisLocationInput.locationInfo">
            <summary>页面信息</summary>
        </member>
        <member name="P:XH.H82.API.Controllers.OfficeExcelController.VisLocationInput.extInfo">
            <summary>拓展信息</summary>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.GetWorkSequentialList(System.String,System.String)">
            <summary>
              运行时序图
            </summary>
            <param name="equipmentId">设备id</param>
            <param name="year">年份</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.GetImplements(System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.String)">
            <summary>
            查询设备使用记录
            </summary>
            <param name="equipmentId"></param>
            <param name="startTime"></param>
            <param name="endTime"></param>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.EditImplement(System.String,XH.H82.Models.Dtos.Implement.ImplementInput)">
            <summary>
            修改设备使用记录
            </summary>
            <param name="ImplementId"></param>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.CreateImplement(System.String,XH.H82.Models.Dtos.Implement.ImplementInput)">
            <summary>
            添加设备使用记录
            </summary>
            <param name="equipmentId"></param>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.DeleteImplement(System.String)">
            <summary>
            删除设备使用记录
            </summary>
            <param name="ImplementId"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.GetUsingFile(System.String,System.Int32,System.Nullable{System.Int32})">
            <summary>
            获取设备使用记录归档
            </summary>
            <param name="equipmentId">设备ID</param>
            <param name="month">月份</param>
            <param name="year">年份</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.GetTransactionFillingRecords(System.String,System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            获取事务填写记录
            </summary>
            <param name="equipmentId">设备id</param>
            <param name="transactionClass"> 记录单类型 1使用、2保养、3维修、4校准 </param>
            <param name="startTime">执行时间-开始时间</param>
            <param name="endTime">执行时间-结束时间</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.GetTransactions(System.String)">
            <summary>
            工作计划-查询设备相关的记录单及事务信息
            </summary>
            <param name="equipmentId"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.GetDocInfos(System.String,System.String)">
            <summary>
            获取设备tab页手动维护的记录附件列表
            </summary>
            <param name="docInfoId"> 对应记录的id</param>
            <param name="docClass"> 保养记录、校准记录、性能验证记录、维修记录、变更记录、使用记录、比对记录</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.GetOperFiles(System.String,System.String)">
            <summary>
              获取运行记录附件
            </summary>
            <param name="module">运行模块（0：保养；1：维修；2：校准；3：比对；4：性能验证；5：变更  6 : 去污）</param>
            <param name="operId">id</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.GetMaintainList(System.String)">
            <summary>
             获取设备保养信息列表
            </summary>
            <param name="equipmentId">设备ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.GetMaintains(System.String,System.String,System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            获取设备保养信息列表(6.24.8 版本后的接口)
            </summary>
            <param name="equipmentId">设备ID</param>
            <param name="maintainCycle">保养周期</param>
            <param name="content">保养内容</param>
            <param name="startTime">保养时间开始</param>
            <param name="endTime">保养时间结束</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.AddMaintainInfo(XH.H82.Models.Entities.EMS_MAINTAIN_INFO)">
            <summary>
              添加设备保养信息 
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.AddMaintain(System.String,XH.H82.Models.Dtos.Maintain.MaintainInput)">
            <summary>
            添加设备保养信息(6.24.8 版本后的接口)
            </summary>
            <param name="equipmentId">设备id</param>
            <param name="input">表单模型</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.UpdateMaintainInfo(XH.H82.Models.Entities.EMS_MAINTAIN_INFO)">
            <summary>
              修改设备保养信息 
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.UpdateMaintainInfo(System.String,XH.H82.Models.Dtos.Maintain.MaintainInput)">
            <summary>
             修改设备保养信息 (6.24.8 版本后的接口)
            </summary>
            <param name="id">保养记录id  对应字段 MAINTAIN_ID</param>
            <param name="input"> 表单模型 </param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.DeleteMaintainInfo(System.String)">
            <summary>
              删除设备保养信息
            </summary>
            <param name="maintainId">设备保养ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.DeleteMaintain(System.String)">
            <summary>
              删除设备保养信息(6.24.8 版本后的接口)
            </summary>
            <param name="id">设备保养记录ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.GetMaintainFile(System.String,System.Int32,System.Int32)">
            <summary>
            获取设备保养记录归档
            </summary>
            <param name="equipmentId">设备ID</param>
            <param name="month">月份</param>
            <param name="year">年</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.GetDecontaminationFile(System.String,System.Int32)">
            <summary>
            获取去污归档记录文件
            </summary>
            <param name="equipmentId"></param>
            <param name="year"></param>
            <param name="month"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.GetRepairList(System.String)">
            <summary>
            获取设备维修信息列表 
            </summary>
            <param name="equipmentId">设备ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.GetRepairs(System.String,System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            获取设备维修信息列表 (6.24.8 版本后的接口)
            </summary>
            <param name="equipmentId">设备ID</param>
            <param name="content">维修内容 模糊搜索</param>
            <param name="startTime">维修时间开始</param>
            <param name="endTime">维修时间结束</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.AddRepairInfo(XH.H82.Models.Entities.EMS_REPAIR_INFO)">
            <summary>
              添加设备维修信息 
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.AddRepairInfo(System.String,XH.H82.Models.Dtos.Repair.RepairInput)">
            <summary>
             添加设备维修信息 (6.24.8 版本后的接口)
            </summary>
            <param name="equipmentId">设备id</param>
            <param name="input">表单模型</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.UpdateRepairInfo(XH.H82.Models.Entities.EMS_REPAIR_INFO)">
            <summary>
              修改设备维修信息
            </summary> 
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.UpdateRepair(System.String,XH.H82.Models.Dtos.Repair.RepairInput)">
            <summary>
            修改设备维修信息(6.24.8 版本后的接口)
            </summary>
            <param name="id">设备维修记录id 对应 REPAIR_ID</param>
            <param name="input">表单模型</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.DeleteRepairInfo(System.String)">
            <summary>
              删除设备维修信息
            </summary>
            <param name="repairId">设备维修ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.DeleteRepair(System.String)">
            <summary>
              删除设备维修信息(6.24.8 版本后的接口)
            </summary>
            <param name="id">设备维修记录ID 对应 REPAIR_ID </param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.GetRepairFile(System.String,System.Int32)">
            <summary>
            获取设备维修记录归档
            </summary>
            <param name="equipmentId">设备ID</param>
            <param name="year">年份</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.GetCorrectList(System.String)">
            <summary>
              获取设备校准信息列表
            </summary>
            <param name="equipmentId">设备ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.GetCorrects(System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            查询设备校准记录(6.24.8 版本后的接口)
            </summary>
            <param name="equipmentId"></param>
            <param name="startTime">设备校准日期开始</param>
            <param name="endTime">设备校准日期结束</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.AddCorrectInfo(XH.H82.Models.Entities.EMS_CORRECT_INFO)">
            <summary>
            添加设备校准信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.AddCorrect(System.String,XH.H82.Models.Dtos.Correct.CorrectInput)">
            <summary>
            添加设备校准记录 (6.24.8 版本后的接口)
            </summary>
            <param name="equipmentId"></param>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.DeleteCorrectInfo(System.String)">
            <summary>
            删除设备校准信息 
            </summary>
            <param name="correctId"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.DeleteCorrect(System.String)">
            <summary>
            删除设备校准信息 (6.24.8 版本后的接口)
            </summary>
            <param name="id">  校准记录id 对应 CORRECT_ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.UpdateCorrectInfo(XH.H82.Models.Entities.EMS_CORRECT_INFO)">
            <summary>
            修改设备校准信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.UpdateCorrect(System.String,XH.H82.Models.Dtos.Correct.CorrectInput)">
            <summary>
            修改设备校准信息(6.24.8 版本后的接口)
            </summary>
            <param name="id">校准记录id 对应 CORRECT_ID</param>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.GetCorrectFile(System.String,System.Int32)">
            <summary>
            获取设备校准记录归档
            </summary>
            <param name="equipmentId">设备ID</param>
            <param name="year">年份</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.GetComparisonList(System.String)">
            <summary>
            获取设备比对信息列表
            </summary>
            <param name="equipmentId">设备ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.GetComparisons(System.String)">
            <summary>
            获取设备比对信息列表(6.24.8 版本后的接口)
            </summary>
            <param name="equipmentId">设备ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.AddComparisonInfo(XH.H82.Models.Entities.EMS_COMPARISON_INFO)">
            <summary>
            添加设备比对信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.AddComparison(System.String,XH.H82.Models.Dtos.Comparison.ComparisonInput)">
            <summary>
            添加设备比对信息(6.24.8 版本后的接口)
            </summary>
            <param name="equipmentId">设备id </param>
            <param name="input">比对表达模型</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.UpdateComparisonInfo(XH.H82.Models.Entities.EMS_COMPARISON_INFO)">
            <summary>
            修改设备比对信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.UpdateComparison(System.String,XH.H82.Models.Dtos.Comparison.ComparisonInput)">
            <summary>
            修改设备比对信息(6.24.8 版本后的接口)
            </summary>
            <param name="id">比对记录ID  对应 COMPARISON_ID</param>
            <param name="input">对比表单入参模型</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.DeleteComparisonInfo(System.String)">
            <summary>
            删除设备比对信息
            </summary>
            <param name="comparisonId">比对ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.DeleteComparison(System.String)">
            <summary>
            删除设备比对信息(6.24.8 版本后的接口)
            </summary>
            <param name="id">比对记录ID  对应 COMPARISON_ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.GetVerificationList(System.String)">
            <summary>
              获取设备性能验证信息列表
            </summary>
            <param name="equipmentId">设备ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.GetVerifications(System.String)">
            <summary>
            查询设备性能验证记录 (6.24.8 版本后的接口)
            </summary>
            <param name="equipmentId"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.AddVerificationInfo(XH.H82.Models.Entities.EMS_VERIFICATION_INFO)">
            <summary>
              添加设备性能验证
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.AddVerification(System.String,XH.H82.Models.Dtos.Verification.VerificationInput)">
            <summary>
            添加设备性能验证(6.24.8 版本后的接口)
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.UpdateVerificationInfo(XH.H82.Models.Entities.EMS_VERIFICATION_INFO)">
            <summary>
              修改设备性能验证
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.UpdateVerification(System.String,XH.H82.Models.Dtos.Verification.VerificationInput)">
            <summary>
            修改设备性能验证(6.24.8 版本后的接口)
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.DeleteVerificationInfo(System.String)">
            <summary>
            删除设备性能验证信息
            </summary>
            <param name="verificationInfoId">性能验证ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.DeleteVerification(System.String)">
            <summary>
            删除设备性能验证信息(6.24.8 版本后的接口)
            </summary>
            <param name="id">性能验证ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.GetChangeList(System.String)">
            <summary>
              获取设备变更信息列表
            </summary>
            <param name="equipmentId">设备ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.GetChanges(System.String)">
            <summary>
            获取设备变更信息列表(6.24.8 版本后的接口)
            </summary>
            <param name="equipmentId">设备ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.AddChangeInfo(XH.H82.Models.Entities.EMS_CHANGE_INFO)">
            <summary>
              添加设备变更信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.AddChangeInfo(System.String,XH.H82.Models.Dtos.Change.ChangeInput)">
            <summary>
             添加设备变更信息(6.24.8 版本后的接口)
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.UpdateChangeInfo(XH.H82.Models.Entities.EMS_CHANGE_INFO)">
            <summary>
            修改设备变更信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.UpdateChange(System.String,XH.H82.Models.Dtos.Change.ChangeInput)">
            <summary>
            修改设备变更信息(6.24.8 版本后的接口)
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.DeleteChangeInfo(System.String)">
            <summary>
             删除设备变更信息
            </summary>
            <param name="changeId">设备变更ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.DeleteChange(System.String)">
            <summary>
            删除设备变更信息 (6.24.8 版本后的接口)
            </summary>
            <param name="id">设备变更ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.OperationRecordController.GetRelationEventInfo(System.String)">
            <summary>
            获取关联事件信息
            </summary>
            <param name="relationNo"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.Organization.OrganizationPullController.GenericPullAreaPGroups(System.String,System.String)">
            <summary>
            院区-专业组下拉
            </summary>
            <param name="labId"></param>
            <param name="areaId"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.Organization.OrganizationPullController.GenericPullPersons(System.String,System.String,System.String,System.String,System.String)">
            <summary>
            人员树下拉
            </summary>
            <param name="labId"></param>
            <param name="areaId"></param>
            <param name="mgroupId"></param>
            <param name="pgroupId"></param>
            <param name="modelId"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.Organization.OrganizationPullController.SmblPullAreaPGroups">
            <summary>
            生安-备案实验室下拉
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.Organization.OrganizationPullController.SmblEquipmentClassPull">
            <summary>
            生安设备类型列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.Organization.OrganizationTreeController.SubscriptionTree(System.String,System.String,System.String)">
            <summary>
            ISO入口-申购记录页面左侧树
            </summary>
            <param name="labId"></param>
            <param name="areaId"> 院区Id </param>
            <param name="pgroupId"> 检验专业组Id</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.Organization.OrganizationTreeController.EquipmentsTree(System.String,System.String,System.String,System.Boolean,System.Boolean,System.Boolean,System.String)">
            <summary>
            ISO入口-设备档案页面左侧树
            </summary>
            <param name="labId">科室id</param>
            <param name="areaId"> 院区Id </param>
            <param name="pgroupId"> 检验专业组Id</param>
            <param name="onlySmbl">是否只显示生安设备</param>
            <param name="showHide">是否显示隐藏设备</param>
            <param name="hasClass">是否显示设备类型层级</param>
            <param name="equipmentCode">设备代号（自定义名称）</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.Organization.OrganizationTreeController.InstrumentEquipmentTree(System.String,System.String,System.String,System.Boolean,System.Boolean,System.Boolean,System.String)">
            <summary>
            ISO入口-试剂信息页面左侧树
            </summary>
            <param name="labId"> 科室id</param>
            <param name="areaId"> 院区Id </param>
            <param name="pgroupId"> 检验专业组Id</param>
            <param name="onlySmbl">是否只显示生安设备</param>
            <param name="showHide">是否显示隐藏设备</param>
            <param name="hasClass">是否显示设备类型层级</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.Organization.OrganizationTreeController.WorkPlanTree(System.String,System.String,System.String)">
            <summary>
            ISO入口-工作计划页面左侧树
            </summary>
            <param name="labId"></param>
            <param name="areaId"> 院区Id </param>
            <param name="pgroupId"> 检验专业组Id</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.Organization.OrganizationTreeController.ScrapsOrStopTree(System.String,System.String,System.String)">
            <summary>
            ISO入口-报废停用页面左侧树
            </summary>
            <param name="labId"> 科室id</param>
            <param name="areaId"> 院区Id </param>
            <param name="pgroupId"> 检验专业组Id</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.Organization.OrganizationTreeController.GetISOLabBaseOrganizationTree(System.String)">
            <summary>
            查询科室-管理专业组-检验专业组树
            </summary>
            <param name="labId">科室id</param>
            <returns></returns>
        </member>
        <member name="T:XH.H82.API.Controllers.Organization.OrgnizationTreeForSmblController">
            <inheritdoc />
        </member>
        <member name="M:XH.H82.API.Controllers.Organization.OrgnizationTreeForSmblController.SubscriptionTree(XH.H82.Models.Smbl.SmblInletEnum,System.String,System.String)">
            <summary>
            生安入口-申购记录页面左侧树
            </summary>
            <param name="inlet"> 入口枚举  0机构  1科室 2备案实验室 </param>
            <param name="id"> 对应的id  入口是机构就是机构id   入口是科室就是科室id  入口是备案实验室就是备案实验室id</param>
            <param name="smblLabId"> 备案实验室id</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.Organization.OrgnizationTreeForSmblController.EquipmentTree(XH.H82.Models.Smbl.SmblInletEnum,System.String,System.String,System.Boolean,System.Boolean,System.Boolean,System.String)">
            <summary>
            生安入口-设备档案页面左侧树
            </summary>
            <param name="inlet"> 入口枚举  0机构  1科室 2备案实验室 </param>
            <param name="id"> 对应的id  入口是机构就是机构id   入口是科室就是科室id  入口是备案实验室就是备案实验室id</param>
            <param name="smblLabId"> 备案实验室id</param>
            <param name="onlySmbl">是否只显示生安类型设备</param>
            <param name="showHide"> 是否显示隐藏  true显示 false 不显示 </param>
            <param name="hasClass">是否显示设备类型层级</param>
            <param name="equipmentCode">设备代号（自定义名称）模糊查询</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.Organization.OrgnizationTreeForSmblController.EquipmentClassTree(XH.H82.Models.Smbl.SmblInletEnum,System.String,System.Boolean,System.Boolean,System.String)">
            <summary>
            生安入口-设备档案生安设备类型页面左侧树
            </summary>
            <param name="inlet"> 入口枚举  0机构  1科室 2备案实验室 </param>
            <param name="id"> 对应的id  入口是机构就是机构id   入口是科室就是科室id  入口是备案实验室就是备案实验室id</param>
            <param name="onlySmbl">是否只显示生安类型设备</param>
            <param name="showHide"> 是否显示隐藏  true显示 false 不显示 </param>
            <param name="equipmentCode">设备代号（自定义名称）模糊查询</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.Organization.OrgnizationTreeForSmblController.InstrumentsClassTree(XH.H82.Models.Smbl.SmblInletEnum,System.String,System.Boolean,System.Boolean,System.String)">
            <summary>
            生安入口-仪器生安设备类型页面左侧树
            </summary>
            <param name="inlet"> 入口枚举  0机构  1科室 2备案实验室 </param>
            <param name="id"> 对应的id  入口是机构就是机构id   入口是科室就是科室id  入口是备案实验室就是备案实验室id</param>
            <param name="onlySmbl">是否只显示生安类型设备</param>
            <param name="showHide"> 是否显示隐藏  true显示 false 不显示 </param>
            <param name="equipmentCode">设备代号（自定义名称）模糊查询</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.Organization.OrgnizationTreeForSmblController.InstrumentEquipmentTree(XH.H82.Models.Smbl.SmblInletEnum,System.String,System.String,System.Boolean,System.Boolean,System.Boolean,System.String)">
            <summary>
            生安入口-试剂信息页面左侧树
            </summary>
            <param name="inlet"> 入口枚举  0机构  1科室 2备案实验室 </param>
            <param name="id"> 对应的id  入口是机构就是机构id   入口是科室就是科室id  入口是备案实验室就是备案实验室id</param>
            <param name="smblLabId"> 备案实验室id</param>
            <param name="onlySmbl">是否只显示生安设备标识设备</param>
            <param name="showHide">是否显示隐藏设备</param>
            <param name="hasClass">是否显示设备类型层级</param>
            <param name="equipmentCode">设备代号</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.Organization.OrgnizationTreeForSmblController.WorkPlanTree(XH.H82.Models.Smbl.SmblInletEnum,System.String,System.String)">
            <summary>
            生安入口-工作计划页面左侧树
            </summary>
            <param name="inlet"> 入口枚举  0机构  1科室 2备案实验室 </param>
            <param name="id"> 对应的id  入口是机构就是机构id   入口是科室就是科室id  入口是备案实验室就是备案实验室id</param>
            <param name="smblLabId"> 备案实验室id</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.Organization.OrgnizationTreeForSmblController.WorkPlanClassTree(XH.H82.Models.Smbl.SmblInletEnum,System.String)">
            <summary>
            生安入口-工作计划页面生安设备类型左侧树
            </summary>
            <param name="inlet"> 入口枚举  0机构  1科室 2备案实验室 </param>
            <param name="id"> 对应的id  入口是机构就是机构id   入口是科室就是科室id  入口是备案实验室就是备案实验室id</param>
            <param name="smblLabId"> 备案实验室id</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.Organization.OrgnizationTreeForSmblController.ScrapsOrStopTree(XH.H82.Models.Smbl.SmblInletEnum,System.String,System.String)">
            <summary>
            生安入口-报废停用页面左侧树
            </summary>
            <param name="inlet"> 入口枚举  0机构  1科室 2备案实验室 </param>
            <param name="id"> 对应的id  入口是机构就是机构id   入口是科室就是科室id  入口是备案实验室就是备案实验室id</param>
            <param name="smblLabId"> 备案实验室id</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.Organization.OrgnizationTreeForSmblController.ScrapsOrStopClassTree(XH.H82.Models.Smbl.SmblInletEnum,System.String,System.String)">
            <summary>
            生安入口-报废停用生安设备类型页面左侧树
            </summary>
            <param name="inlet"> 入口枚举  0机构  1科室 2备案实验室 </param>
            <param name="id"> 对应的id  入口是机构就是机构id   入口是科室就是科室id  入口是备案实验室就是备案实验室id</param>
            <param name="smblLabId"> 备案实验室id</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.Organization.OrgnizationTreeForSmblController.GetDistributionEquipmentTree(XH.H82.Models.Smbl.SmblInletEnum,System.String)">
            <summary>
            生安-备案实验室选择设备树
            </summary>
            <param name="inlet"> 入口枚举  0机构  1科室 2备案实验室 </param>
            <param name="id"> 对应的id  入口是机构就是机构id   入口是科室就是科室id  入口是备案实验室就是备案实验室id</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.Organization.OrgnizationTreeForSmblController.GetDistributionEquipments(System.String,System.String,System.String,System.String)">
            <summary>
            查询已分配备案实验室的设备
            </summary>
            <param name="smblLabId">备案实验室id</param>
            <param name="smblEquipmentClass">生安设备类型id</param>
            <param name="smblState">生安设备状态  0 不及格  1 及格</param>
            <param name="nameOrCode">设备名称、设备代号（自定义名称）</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.Organization.OrgnizationTreeForSmblController.GetPerDistributionEquipments(System.String,System.String,System.String,System.String)">
            <summary>
            查询待分配备案实验室的设备
            </summary>
            <param name="smblLabId">备案实验室id</param>
            <param name="smblEquipmentClass">生安设备类型id</param>
            <param name="smblState">生安设备状态  0 不及格  1 及格</param>
            <param name="nameOrCode">设备名称、设备代号（自定义名称）</param>
            <returns></returns>
        </member>
        <member name="T:XH.H82.API.Controllers.ReagentInfoController">
            <summary>
            试剂信息
            </summary>
        </member>
        <member name="M:XH.H82.API.Controllers.ReagentInfoController.GetInstrumentItemList(System.String)">
            <summary>
            专属试剂-项目列表
            </summary>
            <param name="equipmentNo">设备ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.ReagentInfoController.GetInstrumentItemReagentList(System.String,System.String,System.String)">
            <summary>
            专属试剂-专属试剂列表
            </summary>
            <param name="itemId">项目ID</param>
            <param name="channelId">项目通道ID（不用传了）</param>
            <param name="equipmentId">设备ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.ReagentInfoController.GetInstrumentItemReagentHistoryeList(System.String,System.String,System.String)">
            <summary>
            专属试剂-历史记录
            </summary>
            <param name="itemId">项目ID</param>
            <param name="channelId">项目通道ID（不用传了）</param>
            <param name="equipmentId">设备ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.ReagentInfoController.GetInstrumentReagentCommonList(System.String)">
            <summary>
             仪器公共试剂
            </summary>
            <param name="equipmentId">设备ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.ReagentInfoController.GetCalibratorList(System.String)">
            <summary>
            校准品-校准品列表
            </summary>
            <param name="equipmentId">设备ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.ReagentInfoController.GetCalibratorItemList(System.String,System.String,System.String)">
            <summary>
            校准品-校准品项目列表
            </summary>
            <param name="itemId">项目ID(不用传)</param>
            <param name="equipmentId">设备ID</param>
            <param name="materialId">校准品ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.ScrapStopController.GetScrapStopList(System.DateTime,System.DateTime,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
              获取报废停用列表
            </summary>
            <param name="startTime">开始时间</param>
            <param name="endTime">结束时间</param>
            <param name="mgroupId">管理专业组id</param>
            <param name="equipmentClass">设备类型</param>
            <param name="state">状态</param>
            <param name="applyClass">申请类型</param>
            <param name="equipmentKey">设备检索</param>
            <param name="personKey">人物检索</param>
            <param name="areaId">院区ID</param>
            <param name="pgroupId">检验专业组ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.ScrapStopController.GetEquipmentApplyList(System.String,System.String,System.String,System.String)">
            <summary>
              获取设备列表
            </summary>
            <param name="mgroupId">专业组id</param>
            <param name="equipmentClass">设备类型</param>
            <param name="keyword">检索</param>
            <param name="areaId">院区ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.ScrapStopController.SaveScrapStopApply(System.Collections.Generic.List{XH.H82.Models.Dtos.ScrapStopListDto})">
            <summary>
              报废停用申请保存
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.ScrapStopController.SubmitScrapStopApply(System.Collections.Generic.List{XH.H82.Models.Dtos.ScrapStopListDto},System.String)">
            <summary>
              报废停用申请提交
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.ScrapStopController.SaveApply(System.Collections.Generic.List{XH.H82.Models.Dtos.ScrapStopListDto})">
            <summary>
              报废停用修改(保存)
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.ScrapStopController.BatchSubmit(System.Collections.Generic.List{XH.H82.Models.Dtos.ScrapStopListDto},System.String)">
            <summary>
              批量提交
            </summary>
            <param name="password">密码</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.ScrapStopController.SubmitApply(System.Collections.Generic.List{XH.H82.Models.Dtos.ScrapStopListDto},System.String)">
            <summary>
              报废停用修改(提交)
            </summary>
            <param name="password">密码</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.ScrapStopController.DeleteAwaitSubmit(System.Collections.Generic.List{XH.H82.Models.Dtos.ScrapStopListDto})">
            <summary>
              删除申请信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.ScrapStopController.ApplyRevoke(System.Collections.Generic.List{XH.H82.Models.Dtos.ScrapStopListDto},System.String)">
            <summary>
              撤销
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.ScrapStopController.ApplyAdopt(System.Collections.Generic.List{XH.H82.Models.Dtos.ScrapStopListDto},System.String)">
            <summary>
              申请通过
            </summary>
            <param name="password">密码</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.ScrapStopController.ApplyReject(System.Collections.Generic.List{XH.H82.Models.Dtos.ScrapStopListDto},System.String)">
            <summary>
              申请驳回
            </summary>
            <param name="password">密码</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.ScrapStopController.ScrapStopProcess(System.String)">
            <summary>
              报废停用流程图
            </summary>
            <param name="scrapId">报废停用ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.SetUpController.GetSysSetUpList(System.String)">
            <summary>
              获取系统设置列表
            </summary>
            <param name="mgroupId">管理专业组ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.SetUpController.UpdateSysSetUpInfo(XH.LAB.UTILS.Models.SYS6_SETUP_DICT)">
            <summary>
              修改系统设置信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.SetUpController.ApplyAllSetUpInfo(System.String)">
            <summary>
              系统设置应用全部
            </summary>
            <param name="choiceValue">选择值</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.SetUpController.IsNeedReviewProcess">
            <summary>
            查询工作计划是否需要审核流程   返回结果的data：true   需要，false不需要
            不需要审核流程时,设备工作计划表格需隐藏“状态”列和“提交”,“审核”，“撤销”按钮
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.SetUpController.IsNeedCheckPasswordByEditEquipment">
            <summary>
            检查当前机构是否需要输入密码验证才能编辑设备数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.SetUpController.GetPositionInfo">
            <summary>
            查询房间位置列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.SetUpController.GetBaseDatas">
            <summary>
            返回全部下拉分类以及分类选项
            </summary>
            <returns></returns>
        </member>
        <member name="T:XH.H82.API.Controllers.SmblEquipmentController">
            <inheritdoc />
        </member>
        <member name="M:XH.H82.API.Controllers.SmblEquipmentController.#ctor(Microsoft.AspNetCore.Http.IHttpContextAccessor,XH.H82.Services.DeviceDataRefresh.EquipmentTreeContext,XH.H82.IServices.Sbml.ISmblServicve)">
            <inheritdoc />
        </member>
        <member name="M:XH.H82.API.Controllers.SmblEquipmentController.GetSubscribeInfoList(System.DateTime,System.DateTime,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            生安-查询设备申购记录
            </summary>
            <param name="startTime">开始时间</param>
            <param name="endTime">结束时间</param>
            <param name="state">待审、通过、驳回、仪器已安装、合同已签订</param>
            <param name="search">申购项目|申购人</param>
            <param name="labId">科室id</param>
            <param name="smblLabId">备案实验室id</param>
            <param name="smblFlag">生安标识过滤  0非生安   1生安</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.SmblEquipmentController.GetEquipmentList(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            生安-设备档案列表查询
            </summary>
            <param name="state"> 0 未启用  1启用   2停用  3报废</param>
            <param name="smblEquipmentClass">生安设备类型id</param>
            <param name="type">设备类型id</param>
            <param name="keyWord">设备名称/代号/制造商/供应商/医院设备编号检索</param>
            <param name="isHidd">是否显示隐藏设备 0 不显示隐藏  1 显示隐藏 不填为不显示隐藏</param>
            <param name="labId"></param>
            <param name="smblLabId"></param>
            <param name="smblFlag"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.SmblEquipmentController.GetWorkPlans(System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            生安-获取工作计划列表
            </summary>
            <param name="labId">科室ID</param>
            <param name="keyword">设备名称/型号/代号</param>
            <param name="smblEquipmentClass">生安设备类型</param>
            <param name="equipmentClass">设备分类</param>
            <param name="smblLabId">备案实验室id</param>
            <param name="smblFlag">生安标识</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.SmblEquipmentController.GetScrapStopList(System.DateTime,System.DateTime,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
             生安-获取报废停用列表
            </summary>
            <param name="dtBeginTime">开始时间</param>
            <param name="dtEndTime">结束时间</param>
            <param name="smblEquipmentClass">生安设备类型</param>
            <param name="equipmentClass">设备类型</param>
            <param name="szLastDispose">状态</param>
            <param name="szEventClass">申请类型</param>
            <param name="equipmentKey">设备检索</param>
            <param name="personKey">人物检索</param>
            <param name="person_select">过滤本人处理</param>
            <param name="labId">科室id</param>
            <param name="smblLabId">备案实验室id</param>
            <param name="smblFlag">生安标识</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.SmblEquipmentController.GetEquipmentApplyList(System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
              生安-获取生安待报废停用的设备列表
            </summary>
            <param name="equipmentClass">设备类型</param>
            <param name="keyword">查询</param>
            <param name="pGourpId">检验专业组id</param>
            <param name="labId">科室id</param>
            <param name="smblLabId">备案实验室id</param>
            <param name="smblFlag">生安标识</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.SubscribeController.GetSubscribeInfoList(System.DateTime,System.DateTime,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
              获取申购信息列表
            </summary>
            <param name="startTime">开始日期</param>
            <param name="endTime">结束日期</param>
            <param name="mgroupId">管理专业组ID</param>
            <param name="state">状态</param>
            <param name="search">检索内容</param>
            <param name="pgroupId">检验专业组ID</param>
            <param name="smblFlag">生安安全标识</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.SubscribeController.AddSubscribeInfo(XH.H82.Models.Entities.EMS_SUBSCRIBE_INFO)">
            <summary>
              添加申购信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.SubscribeController.UpdateSubscribeInfo(XH.H82.Models.Entities.EMS_SUBSCRIBE_INFO)">
            <summary>
              修改申购信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.SubscribeController.DeleteSubscribeInfo(System.String)">
            <summary>
              删除申购信息
            </summary>
            <param name="subscribeId">申购项目编号</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.SubscribeController.GetSubscribeProcess(System.String)">
            <summary>
              申购流程
            </summary>
            <param name="subscribeId">申购项目id</param>
            <returns></returns>
        </member>
        <member name="T:XH.H82.API.Controllers.SuperController">
            <summary>
            基础控制器
            </summary>
        </member>
        <member name="M:XH.H82.API.Controllers.SystemController.GetIssueTokenInfo(System.String)">
            <summary>
            获取其他系统的token
            </summary>
            <param name="moduleId">模块id</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.SystemController.HealthCheck">
            <summary>
            健康检查接口
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.SystemController.ExportMenuApiList(System.String)">
            <summary>
            导出API列表,需标记ExportInfo描述器
            参考 BaseDataDemoController/ApiExportTest
            ApiID根据模块+路径计算哈希值生成,只要路径保持不变,则哈希值保持不变
            MenuId需要先约定,多个菜单用逗号隔开
            </summary>
            <param name="format">EXCEL|JSON</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.SystemController.GetUnitLoginUrl">
            <summary>
            获取统一登录地址
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.SystemController.GetLinkDocumentationSystemUrl(System.String)">
            <summary>
            获取链接文档url地址
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.SystemController.GetUserLabGroup">
            <summary>
            获取科室信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.SystemController.GetToken(System.String,System.String)">
            <summary>
            测试token
            </summary>
            <param name="logId"></param>
            <param name="password"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.SystemController.UpdateLog">
            <summary>
            读取更新日志
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.SystemController.AboutSystem">
            <summary>
            获取关于系统
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.SystemController.UserVerify(System.String)">
            <summary>
            用户验证
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.SystemController.GetHospitalInfo(System.String)">
            <summary>
            获取机构信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.SystemController.HasEquipmentDeletedPromiss">
            <summary>
            查询当前用户是否拥有设备档案删除权限
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.SystemController.HasButtonPermissions(System.String)">
            <summary>
            查询当前用户是否拥有某按钮权限
            </summary>
            <param name="buttonPermissionId"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.SystemController.GetLogonHospitalLablist">
            <summary>
            生安相关接口
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.SystemController.ServiceAreaList(System.String)">
            <summary>
            根据供应商类型查询供应商服务范围
            </summary>
            <param name="type">供应商类型</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.TestController.Equipment_ziwaideng(System.String)">
            <summary>
            定时任务紫外灯测试
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.TestController.Equipment_xiyanqi(System.String)">
            <summary>
            定时任务洗眼器测试
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.TestController.Equipment_zinengkaiguan(System.String)">
            <summary>
            只智能开关测试
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.TestController.Equipment_shuiya(System.String)">
            <summary>
            水压传感器开关测试
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.TestController.GetEnvironmentDevicesDto(System.String)">
            <summary>
            环境一体机测试
            </summary>
            <param name="sn"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.TestController.EquipmentWaterInit(System.String)">
            <summary>
            水压监测记录测试
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.TestController.EquipmentlabmInit(System.String)">
            <summary>
            紫外灯监测记录测试
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.TestController.EquipmentEvnInit(System.String)">
            <summary>
            环境一体机监测记录测试
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.TestController.EquipmentInit(System.String)">
            <summary>
            智能插座监测记录测试
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.TestController.GetEquipmentMoitorHoursData(System.String,System.DateTime)">
            <summary>
            提供小时监测表测试数据
            </summary>
            <param name="id"></param>
            <param name="date"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.TestController.ConutEquipmentMonitoringItemsDay(System.DateTime)">
            <summary>
            提天统计
            </summary>
            <param name="id"></param>
            <param name="date"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.TestController.GetEquipmentMoitorHoursDataByDay(System.String,System.DateTime)">
            <summary>
            按天查询
            </summary>
            <param name="id"></param>
            <param name="date"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.Transaction.RecordContentController.GetContentDicts(System.String,System.String)">
            <summary>
            查询字段内容
            </summary>
            <param name="classId"> XX记录 </param>
            <param name="content">字典内容</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.Transaction.RecordContentController.AddContentDict(XH.H82.Models.Dtos.Transaction.AddContentDictInput)">
            <summary>
            新增字典
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.Transaction.RecordContentController.UpdateContentDict(System.String,XH.H82.Models.Dtos.Transaction.UpdateContentDictInput)">
            <summary>
            修改字典内容
            </summary>
            <param name="Id"></param>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.Transaction.RecordContentController.DeleteContentDict(System.String)">
            <summary>
            删除字典
            </summary>
            <param name="Id"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.WorkPlanController.SaveWorkPlan(System.Collections.Generic.List{XH.H82.Models.Dtos.WorkPlanDto})">
            <summary>
              保存工作计划
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.WorkPlanController.GetClassList">
            <summary>
              设备类型树结构
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.XhEquipmentDocController.HideEquipment(System.String)">
            <summary>
            隐藏/显示设备
            </summary>
            <param name="equipmentId">设备ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.XhEquipmentDocController.GetEquipmentListByMgroup(System.String,System.String,System.String,System.String,System.String)">
            <summary>
              设备树结构（专业组）
            </summary>
            <param name="areaId">院区ID</param>
            <param name="equipmentNo">设备代号</param>
            <param name="ifHide">是否隐藏（0：显示；1：隐藏；空：全部）</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.XhEquipmentDocController.GetEquipmentClassList(System.String,System.String,System.String,System.String)">
            <summary>
              设备类型树结构（显示设备）
            </summary>
            <param name="areaId">院区ID</param>
            <param name="equipmentNo">设备代号</param>
            <param name="ifHide">是否隐藏（0：显示；1：隐藏；空：全部）</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.XhEquipmentDocController.MatchEquipmentFunc(XH.LAB.UTILS.Models.FileDispatcherContext)">
            <summary>
            第一级目录匹配设备，并往上下文中写入设备对象
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.XhEquipmentDocController.MatchFirstFunc(XH.LAB.UTILS.Models.FileDispatcherContext)">
            <summary>
            调试专用 后续清理
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.XhEquipmentDocController.MatchEquipmentFileClassFunc(XH.LAB.UTILS.Models.FileDispatcherContext)">
            <summary>
            SOP档案、设备说明书、资质证书类型路径判断
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.XhEquipmentDocController.UpLoadEquipmentFileFunc(XH.LAB.UTILS.Models.FileDispatcherContext)">
            <summary>
            SOP档案、设备说明书、资质证书类型文件上传
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.XhEquipmentDocController.MatchWorkSequentialFunc(XH.LAB.UTILS.Models.FileDispatcherContext)">
            <summary>
            运行记录路径判断
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.XhEquipmentDocController.MatchWorkSequentialListClassFunc(XH.LAB.UTILS.Models.FileDispatcherContext)">
            <summary>
            运行记录之下的类型判断
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.XhEquipmentDocController.MatchInstallInfoFunc(XH.LAB.UTILS.Models.FileDispatcherContext)">
            <summary>
            安装信息处理（第二层）
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.XhEquipmentDocController.MatchBaseInfoFunc(XH.LAB.UTILS.Models.FileDispatcherContext)">
            <summary>
            基本处理（第二层）
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.XhEquipmentDocController.MatchBaseInfoNextFunc(XH.LAB.UTILS.Models.FileDispatcherContext)">
            <summary>
            基本信息下一层级（第三层）
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.XhEquipmentDocController.MatchEquipmentSecondFunc(XH.LAB.UTILS.Models.FileDispatcherContext)">
            <summary>
            通用二级目录
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.XhEquipmentDocController.MatchInstallInfoNextFunc(XH.LAB.UTILS.Models.FileDispatcherContext)">
            <summary>
            安装信息下一层级（第三层）
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.XhEquipmentDocController.UploadFile(XH.LAB.UTILS.Models.FileDispatcherContext)">
            <summary>
            通用上传附件(第3层目录)
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.XhEquipmentDocController.UploadInstallInfoFile(XH.LAB.UTILS.Models.FileDispatcherContext)">
            <summary>
            上传安装信息记录附件（第4层）
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.XhEquipmentDocController.GenerateEquipmentInfo(System.String)">
            <summary>
            生成预览文件
            </summary>
            <param name="equipmentId"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.XhEquipmentDocController.CardPrint(System.String,System.Collections.Generic.List{System.String})">
            <summary>
            标识卡打印
            </summary>
            <param name="equipmentId">设备ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.XhEquipmentDocController.ExportEquipmentList(System.Collections.Generic.List{XH.H82.Models.Dtos.ExportEquipmentDto})">
            <summary>
            导入设备列表
            </summary>
            <param name="record"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.XhEquipmentDocController.GetCardTypeList">
            <summary>
            获取标识卡打印模板
            </summary>
            <param name="equipmentId"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.XhEquipmentDocController.SetEquipmentIsIn(System.String,System.Int32)">
            <summary>
            设置门禁设备的门禁方向
            </summary>
            <param name="equipmentId"></param>
            <param name="isIn"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.XH.H82.API.Controllers.XhWorkPlanController.GetWorkPlanList(System.String,System.String,System.String,System.String,System.String)">
            <summary>
              获取工作计划列表（停用）
            </summary>
            <param name="keyword">检索</param>
            <param name="mgroupId">专业组id</param>
            <param name="equipmentClass">设备类型</param>
            <param name="pgroupId">检验专业组ID</param>
            <param name="areaId">病区ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.XH.H82.API.Controllers.XhWorkPlanController.GetWorkPlans(System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            获取工作计划列表
            </summary>
            <param name="labId">科室ID</param>
            <param name="keyword">设备名称/型号/代号</param>
            <param name="mgroupId">管理专业组</param>
            <param name="equipmentClass">设备分类</param>
            <param name="pgroupId">检验专业组</param>
            <param name="areaId">院区</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.XH.H82.API.Controllers.XhWorkPlanController.SubmitWorkPlans(XH.H82.Services.OperationLog.SubmitWorkPlanInput)">
            <summary>
            提交工作计划
            </summary>
            <param name="input">输入模型</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.XH.H82.API.Controllers.XhWorkPlanController.AuditWorkPlans(XH.H82.Services.OperationLog.AuditWorkPlanInput)">
            <summary>
            审核工作计划
            </summary>
            <param name="input">输入模型</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.XH.H82.API.Controllers.XhWorkPlanController.OverruledWorkPlans(XH.H82.Services.OperationLog.OverruledWorkPlanInput)">
            <summary>
            驳回工作计划
            </summary>
            <param name="input">输入模型</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.XH.H82.API.Controllers.XhWorkPlanController.GetEmsWordPlanCirculationRecords(System.String)">
            <summary>
            获取工作计划操作记录列表
            </summary>
            <param name="workPlanId">工作计划id</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.XH.H82.API.Controllers.XhWorkPlanController.GetMgroupList(System.String,System.String)">
            <summary>
              专业组树结构
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers.XH.H82.API.Controllers.XhWorkPlanController.GetEquipmentClassList(System.String,System.String)">
            <summary>
              设备类型树结构（显示设备）
            </summary>
            <param name="areaId">院区ID</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers._Demos.BaseDataDemoController.GetBaseData">
            <summary>
            固定基础数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers._Demos.BaseDataDemoController.GetOrganizeData">
            <summary>
            组织机构
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers._Demos.BaseDataDemoController.GetMenuButtonApi">
            <summary>
            菜单按钮
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers._Demos.BaseDataDemoController.AccountInfo">
            <summary>
            获取用户
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers._Demos.BaseDataDemoController.GetLis5SetupDicts">
            <summary>
            获取系统设置
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers._Demos.BaseDataDemoController.GetTableMax">
            <summary>
            获取最大值
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers._Demos.BaseDataDemoController.ApiExportTest">
            <summary>
            单个菜单导出样式控制器 无功能
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers._Demos.BaseDataDemoController.ApiExportTest2">
            <summary>
            多个菜单导出样式控制器 无功能
            </summary>
            <returns></returns>
        </member>
        <member name="T:XH.H82.API.Controllers._Demos.CacheTestController">
            <summary>
            缓存读写DEMO
            </summary>
        </member>
        <member name="M:XH.H82.API.Controllers._Demos.CacheTestController.CacheAOPTest(System.String)">
            <summary>
            缓存AOP写法
            </summary>
            <returns></returns>
        </member>
        <member name="T:XH.H82.API.Controllers._Demos.DemoController">
            <summary>
            使用样例
            </summary>
        </member>
        <member name="M:XH.H82.API.Controllers._Demos.DemoController.EFDemoFromDefaultDb">
            <summary>
            默认数据库EF 样例
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers._Demos.DemoController.EFDemoFromDb2">
            <summary>
            数据库2访问样例
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers._Demos.DemoController.BulkInsertDemo">
            <summary>
            批量插入demo
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers._Demos.DemoController.BulkDeleteDemo">
            <summary>
            批量删除
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers._Demos.DemoController.DeleteByPredicate">
            <summary>
            按条件删除数
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers._Demos.DemoController.UpdateByPredicate">
            <summary>
            按条件将Value小于50的数据改成5000
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers._Demos.DemoController.QueryBySql">
            <summary>
            使用原生sql语句查询 
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers._Demos.DemoController.AutoMapperTest">
            <summary>
            测试automapper
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers._Demos.DemoController.ModelValidateTest(XH.H82.Models.Dtos.StartTemplateDto)">
            <summary>
            自动模型验证测试
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers._Demos.DemoGetConfigController.GetModuleAllConfig">
            <summary>
            获取所有设置
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers._Demos.DemoGetConfigController.GetSingleConfig">
            <summary>
            获取单个设置
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers._Demos.TokenTestController.CreateToken">
            <summary>
            模拟统一登录请求S01颁发token
            注意:此方法仅演示使用,除特殊项目外生产环境严禁向外部暴露token获取接口
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers._Demos.TokenTestController.NonRoleAuthorizeTest">
            <summary>
            不需要角色验证的接口
            </summary>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers._Demos.XinghePlatformController.CallXingheInterface(System.String,System.String)">
            <summary>
            访问接口并直接将返回结果映射成实体类测试 主要需要自行建立映射实体
            </summary>
            <param name="headXml"></param>
            <param name="bodyXml"></param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Controllers._Demos.XinghePlatformController.CallXhPlatformInterfaceSource(System.String,System.String)">
            <summary>
            调用接口返回原始数据
            </summary>
            <returns></returns>
        </member>
        <member name="T:XH.H82.API.Examples.GetEquipmentCodeCustomDictTest">
            <summary>
            GetEquipmentCodeCustomDict 方法测试示例
            </summary>
        </member>
        <member name="M:XH.H82.API.Examples.GetEquipmentCodeCustomDictTest.TestLevel1EquipmentClass">
            <summary>
            测试1级设备类型的查询
            </summary>
        </member>
        <member name="M:XH.H82.API.Examples.GetEquipmentCodeCustomDictTest.TestLevel2EquipmentClass">
            <summary>
            测试2级设备类型的查询
            </summary>
        </member>
        <member name="M:XH.H82.API.Examples.GetEquipmentCodeCustomDictTest.TestNonExistentEquipmentClass">
            <summary>
            测试不存在的设备类型
            </summary>
        </member>
        <member name="M:XH.H82.API.Examples.GetEquipmentCodeCustomDictTest.RunAllTests">
            <summary>
            运行所有测试
            </summary>
        </member>
        <member name="M:XH.H82.API.Extensions.DateCheckContext.CheckEquipments">
            <summary>
            检查设备信息数据
            </summary>
        </member>
        <member name="T:XH.H82.API.Extensions.IpRestrictionFilter">
            <inheritdoc />
        </member>
        <member name="M:XH.H82.API.Extensions.IpRestrictionFilter.#ctor(Microsoft.Extensions.Configuration.IConfiguration)">
            <inheritdoc />
        </member>
        <member name="M:XH.H82.API.Extensions.ResultDtoExt.Success``1(``0,System.String,System.Object,System.Object)">
            <summary>
            成功 200 返回数据
            </summary>
            <param name="data"></param>
            <param name="msg"></param>
            <param name="data1"></param>
            <param name="data2"></param>
            <typeparam name="T"></typeparam>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Extensions.ResultDtoExt.Failure``1(``0,System.String,System.Object,System.Object)">
            <summary>
            失败 500 返回错误
            </summary>
            <param name="data"></param>
            <param name="msg"></param>
            <param name="data1"></param>
            <param name="data2"></param>
            <typeparam name="T"></typeparam>
            <returns></returns>
        </member>
        <member name="T:XH.H82.API.Middleware.AddrResposeHeadMiddleware">
            <summary>
            当前服务ip地址中间件  辅助诊断负载到哪个服务器（临时方案）
            </summary>
        </member>
        <member name="M:XH.H82.API.Middleware.FragmentReturnsFileStream.#ctor(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            构造
            </summary>
            <param name="ctx"></param>
        </member>
        <member name="M:XH.H82.API.Middleware.FragmentReturnsFileStream.WriteToMemoryStream(System.IO.Stream,System.Int64,System.Int64)">
            <summary>
            分流截取文件流
            </summary>
            <param name="stream">文件流</param>
            <param name="fromIndex">开始下标</param>
            <param name="toIndex">结束下标</param>
            <returns></returns>
        </member>
        <member name="M:XH.H82.API.Middleware.FragmentReturnsFileStream.SetPdfDownloadHeaders(System.Boolean,System.Int64,System.Int64,System.Int64)">
            <summary>
            设置pdf流下载请求头为range
            </summary>
            <param name="isRange">是否开启range协议</param>
            <param name="fromIndex">开始下标</param>
            <param name="toIndex">结束下标</param>
            <param name="fileLength">总长度</param>
        </member>
        <member name="M:XH.H82.API.Middleware.FragmentReturnsFileStream.TryAnalyseRange(Microsoft.AspNetCore.Http.HttpRequest,System.Int64,System.ValueTuple{System.Int64,System.Int64}@)">
            <summary>
            尝试从请求头解析是否有range参数
            </summary>
            <param name="request">请求</param>
            <param name="fileLength">文件总长度</param>
            <param name="indexes"> FromIndex 开始的下标   ToIndex结束的下标 </param>
            <returns></returns>
        </member>
        <member name="T:XH.H82.API.ScheduleJobs.MonitorHoursDataByDaySync">
            <summary>
            检测数据日统计服务
            </summary>
        </member>
    </members>
</doc>
