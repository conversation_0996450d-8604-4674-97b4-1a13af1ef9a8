﻿"restore":{"projectUniqueName":"C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Base\\XH.H82.Base.csproj","projectName":"XH.H82.Base","projectPath":"C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Base\\XH.H82.Base.csproj","outputPath":"C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Base\\obj\\","projectStyle":"PackageReference","fallbackFolders":["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"],"originalTargetFrameworks":["net6.0"],"sources":{"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\":{},"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net6.0":{"targetAlias":"net6.0","projectReferences":{"C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Models\\XH.H82.Models.csproj":{"projectPath":"C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Models\\XH.H82.Models.csproj"}}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"},"SdkAnalysisLevel":"9.0.300"}"frameworks":{"net6.0":{"targetAlias":"net6.0","dependencies":{"EPPlus":{"target":"Package","version":"[6.2.6, )"},"EasyCaching.Serialization.Protobuf":{"target":"Package","version":"[1.7.0, )"},"FireflySoft.RateLimit.AspNetCore":{"target":"Package","version":"[3.0.0, )"},"Microsoft.AspNetCore.Authentication.JwtBearer":{"target":"Package","version":"[6.0.15, )"},"RestSharp":{"target":"Package","version":"[112.1.0, )"},"Serilog.Sinks.FastConsole":{"target":"Package","version":"[2.2.0, )"},"Serilog.Sinks.SpectreConsole":{"target":"Package","version":"[0.3.3, )"},"SkiaSharp":{"target":"Package","version":"[2.88.9, )"},"Swashbuckle.AspNetCore":{"target":"Package","version":"[6.2.3, )"},"Swashbuckle.AspNetCore.Filters":{"target":"Package","version":"[7.0.5, )"},"Swashbuckle.AspNetCore.Filters.Abstractions":{"target":"Package","version":"[7.0.5, )"},"Unchase.Swashbuckle.AspNetCore.Extensions":{"target":"Package","version":"[2.6.12, )"},"iTextSharp":{"target":"Package","version":"[5.5.13.3, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"frameworkReferences":{"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}