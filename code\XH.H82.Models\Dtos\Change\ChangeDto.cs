﻿using H.Utility;
using XH.H82.Models.Entities;

namespace XH.H82.Models.Dtos.Change;


public class ChangeDto
{
    public static EMS_CHANGE_INFO CreateAddModule(string equipmentId, string? hospitalId  , ChangeInput input)
    {
        var record = new EMS_CHANGE_INFO();
        record.EQUIPMENT_ID = equipmentId;
        record.CHANGE_ID = IDGenHelper.CreateGuid();
        record.HOSPITAL_ID = hospitalId ?? "H0000";
        record.CHANGE_DATE = input.CHANGE_DATE;
        record.CHANGE_CONTENT = input.CHANGE_CONTENT;
        record.CHANGE_PERSON = input.CHANGE_PERSON;
        if (record.CHANGE_PERSON.IsNotNullOrEmpty() && record.CHANGE_PERSON.Contains("_"))
        {
            record.CHANGE_PERSON = record.CHANGE_PERSON.Split('_')[1];
        }
        record.REMARK = input.REMARK;
        record.CHANGE_STATE = "1";
        return record;
    }
}

/// <summary>
/// 变更记录入参表单模型
/// </summary>
/// <param name="CHANGE_DATE">变更日期</param>
/// <param name="CHANGE_PERSON">变更人员</param>
/// <param name="CHANGE_CONTENT">变更内容</param>
/// <param name="REMARK">备注</param>
public record ChangeInput(
    DateTime? CHANGE_DATE,
    string? CHANGE_PERSON,
    string? CHANGE_CONTENT,
    string? REMARK);