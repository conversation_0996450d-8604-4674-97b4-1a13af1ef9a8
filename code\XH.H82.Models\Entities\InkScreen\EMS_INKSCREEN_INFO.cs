﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using XH.H82.Models.DeviceRelevantInformation.Enum;

namespace XH.H82.Models.Entities.InkScreen
{
    [DBOwner("XH_OA")]
    [SugarTable("EMS_INKSCREEN_INFO")]
    public class EMS_INKSCREEN_INFO : BaseField
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string INKSCREEN_ID { get; set; }

        public string HOSPITAL_ID { get; set; }

        public string EQUIPMENT_ID { get; set; }

        public string INKSCREEN_NAME { get; set; }

        public string INKSCREEN_SORT { get; set; }

        public string MAC { get; set; }

        public LineStateEnum ONLINE_STATE { get; set; }

        public LightStateEnum LIGHT_STATE { get; set; }

        public double POWER { get; set; } = 100;

        public int POWER_THRESHOLD { get; set; } = 20;

        public DateTime? OPER_TIME { get; set; }

        public string OPER_PERSON { get; set; }

        public string? DELIVERY { get; set; }



    }
}
