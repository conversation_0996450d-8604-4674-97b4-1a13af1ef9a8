﻿using H.Utility;
using XH.H82.Models.Dtos;
using XH.H82.Models.Dtos.Base;
using XH.H82.Models.Dtos.SysDocDtos;
using XH.H82.Models.Entities;

namespace XH.H82.IServices
{
    public interface IFileManageService
    {
        List<DocumentSystemMenu> GetFileTree(string labId, bool isSmbl = false, string meanId = "");
        List<MgroupPullDto> GetPgroupList(string labId);
        List<DocInfoDto> GetDocInfoList(string classId, string pgroupId, string docType, string keyword, string equipmentId);
        List<string> GetDocFile(string docId, string file_upload_address);
        ResultDto UploadDocFile(List<DocFileDto> filePath, string docInfoId, string docClass, string equipmentId, string userName, string hospitalId);


        DocumentSystemDto FindDoccumentNodesAndDocuments(string labId, string firstmenukey, string equipmentId, string docClass, string? classId, string? docType, string? pgroupId, string? docName, SOPType? docClassId);


        /// <summary>
        /// 查询设备相关sop文件
        /// </summary>
        /// <param name="equipmentId"></param>
        /// <returns></returns>
        public List<EMS_DOC_INFO> GetEquipmentSopFils(string equipmentId);
        /// <summary>
        /// 添加设备sop证书
        /// </summary>
        /// <param name="equipmentId">设备id</param>
        /// <param name="files">文件系统证书模型</param>
        void AddEquipmentSopFiles(string equipmentId,List<DocFileDto> files);
    }
}
