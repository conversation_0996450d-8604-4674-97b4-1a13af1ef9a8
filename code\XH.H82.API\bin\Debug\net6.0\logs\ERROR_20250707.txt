2025-07-07 09:26:04.441 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-07 10:15:04.316 +08:00 [ERR] ==>请求基础连接时发生错误:不知道这样的主机。 (d01.lis-china.com:18801)
System.Net.Http.HttpRequestException: 不知道这样的主机。 (d01.lis-china.com:18801)
 ---> System.Net.Sockets.SocketException (11001): 不知道这样的主机。
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|277_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at RestSharp.ResponseThrowExtension.ThrowIfError(RestResponse response)
   at RestSharp.RestClient.ExecuteAsync(RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.ExecuteAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.GetAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.AsyncHelpers.<>c__DisplayClass1_0`1.<<RunSync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.<Run>g__PostCallback|7_0(Object _)
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.Run()
   at RestSharp.AsyncHelpers.RunSync(Func`1 task)
   at RestSharp.AsyncHelpers.RunSync[T](Func`1 task)
   at RestSharp.RestClientExtensions.Get[T](IRestClient client, RestRequest request)
   at H.BASE.Infrastructure.BaseConfigRequestHelper.GetBaseConfigFromBaseService(String urlS01, String hospitalId, String moduleId)
2025-07-07 10:15:06.408 +08:00 [ERR] ==>请求基础连接时发生错误:不知道这样的主机。 (d01.lis-china.com:18801)
System.Net.Http.HttpRequestException: 不知道这样的主机。 (d01.lis-china.com:18801)
 ---> System.Net.Sockets.SocketException (11001): 不知道这样的主机。
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|277_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at RestSharp.ResponseThrowExtension.ThrowIfError(RestResponse response)
   at RestSharp.RestClient.ExecuteAsync(RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.ExecuteAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.GetAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.AsyncHelpers.<>c__DisplayClass1_0`1.<<RunSync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.<Run>g__PostCallback|7_0(Object _)
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.Run()
   at RestSharp.AsyncHelpers.RunSync(Func`1 task)
   at RestSharp.AsyncHelpers.RunSync[T](Func`1 task)
   at RestSharp.RestClientExtensions.Get[T](IRestClient client, RestRequest request)
   at H.BASE.Infrastructure.BaseConfigRequestHelper.GetBaseConfigFromBaseService(String urlS01, String hospitalId, String moduleId)
2025-07-07 10:15:08.431 +08:00 [ERR] ==>请求基础连接时发生错误:不知道这样的主机。 (d01.lis-china.com:18801)
System.Net.Http.HttpRequestException: 不知道这样的主机。 (d01.lis-china.com:18801)
 ---> System.Net.Sockets.SocketException (11001): 不知道这样的主机。
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|277_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at RestSharp.ResponseThrowExtension.ThrowIfError(RestResponse response)
   at RestSharp.RestClient.ExecuteAsync(RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.ExecuteAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.GetAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.AsyncHelpers.<>c__DisplayClass1_0`1.<<RunSync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.<Run>g__PostCallback|7_0(Object _)
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.Run()
   at RestSharp.AsyncHelpers.RunSync(Func`1 task)
   at RestSharp.AsyncHelpers.RunSync[T](Func`1 task)
   at RestSharp.RestClientExtensions.Get[T](IRestClient client, RestRequest request)
   at H.BASE.Infrastructure.BaseConfigRequestHelper.GetBaseConfigFromBaseService(String urlS01, String hospitalId, String moduleId)
2025-07-07 10:15:12.683 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-07 10:16:53.630 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-07 10:17:12.355 +08:00 [ERR] 未处理的异常::System.NullReferenceException: Object reference not set to an instance of an object.
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService.GetEquipmentArchivesLinkByClassId(String classId) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 172
   at Castle.Proxies.Invocations.IEquipmentClassService_GetEquipmentArchivesLinkByClassId.InvokeMethodOnTarget()
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.UseCacheWhenFailedAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.CacheAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at Castle.Proxies.IEquipmentClassServiceProxy.GetEquipmentArchivesLinkByClassId(String classId)
   at XH.H82.API.Controllers.EquipmentClassNew.EquipmentClassNewController.GetEquipmentArchivesLinkByClassId(String classId) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\Controllers\EquipmentClassNew\EquipmentClassNewController.cs:line 96
   at lambda_method910(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-07-07 10:17:12.380 +08:00 [ERR] HTTP GET /api/EquipmentClassNew/GetEquipmentArchivesLinkByClassId/1 responded 500 in 7456.7313 ms
2025-07-07 10:22:20.363 +08:00 [ERR] 未处理的异常::System.NullReferenceException: Object reference not set to an instance of an object.
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService.GetEquipmentArchivesLinkByClassId(String classId)
   at Castle.Proxies.Invocations.IEquipmentClassService_GetEquipmentArchivesLinkByClassId.InvokeMethodOnTarget()
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.UseCacheWhenFailedAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.CacheAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at Castle.Proxies.IEquipmentClassServiceProxy.GetEquipmentArchivesLinkByClassId(String classId)
   at XH.H82.API.Controllers.EquipmentClassNew.EquipmentClassNewController.GetEquipmentArchivesLinkByClassId(String classId) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\Controllers\EquipmentClassNew\EquipmentClassNewController.cs:line 96
   at lambda_method910(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-07-07 10:22:20.365 +08:00 [ERR] HTTP GET /api/EquipmentClassNew/GetEquipmentArchivesLinkByClassId/1 responded 500 in 5631.4114 ms
2025-07-07 10:23:44.784 +08:00 [ERR] ==>请求基础连接时发生错误:不知道这样的主机。 (d01.lis-china.com:18801)
System.Net.Http.HttpRequestException: 不知道这样的主机。 (d01.lis-china.com:18801)
 ---> System.Net.Sockets.SocketException (11001): 不知道这样的主机。
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|277_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at RestSharp.ResponseThrowExtension.ThrowIfError(RestResponse response)
   at RestSharp.RestClient.ExecuteAsync(RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.ExecuteAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.GetAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.AsyncHelpers.<>c__DisplayClass1_0`1.<<RunSync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.<Run>g__PostCallback|7_0(Object _)
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.Run()
   at RestSharp.AsyncHelpers.RunSync(Func`1 task)
   at RestSharp.AsyncHelpers.RunSync[T](Func`1 task)
   at RestSharp.RestClientExtensions.Get[T](IRestClient client, RestRequest request)
   at H.BASE.Infrastructure.BaseConfigRequestHelper.GetBaseConfigFromBaseService(String urlS01, String hospitalId, String moduleId)
2025-07-07 10:23:46.885 +08:00 [ERR] ==>请求基础连接时发生错误:不知道这样的主机。 (d01.lis-china.com:18801)
System.Net.Http.HttpRequestException: 不知道这样的主机。 (d01.lis-china.com:18801)
 ---> System.Net.Sockets.SocketException (11001): 不知道这样的主机。
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|277_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at RestSharp.ResponseThrowExtension.ThrowIfError(RestResponse response)
   at RestSharp.RestClient.ExecuteAsync(RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.ExecuteAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.GetAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.AsyncHelpers.<>c__DisplayClass1_0`1.<<RunSync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.<Run>g__PostCallback|7_0(Object _)
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.Run()
   at RestSharp.AsyncHelpers.RunSync(Func`1 task)
   at RestSharp.AsyncHelpers.RunSync[T](Func`1 task)
   at RestSharp.RestClientExtensions.Get[T](IRestClient client, RestRequest request)
   at H.BASE.Infrastructure.BaseConfigRequestHelper.GetBaseConfigFromBaseService(String urlS01, String hospitalId, String moduleId)
2025-07-07 10:23:48.901 +08:00 [ERR] ==>请求基础连接时发生错误:不知道这样的主机。 (d01.lis-china.com:18801)
System.Net.Http.HttpRequestException: 不知道这样的主机。 (d01.lis-china.com:18801)
 ---> System.Net.Sockets.SocketException (11001): 不知道这样的主机。
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|277_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at RestSharp.ResponseThrowExtension.ThrowIfError(RestResponse response)
   at RestSharp.RestClient.ExecuteAsync(RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.ExecuteAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.GetAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.AsyncHelpers.<>c__DisplayClass1_0`1.<<RunSync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.<Run>g__PostCallback|7_0(Object _)
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.Run()
   at RestSharp.AsyncHelpers.RunSync(Func`1 task)
   at RestSharp.AsyncHelpers.RunSync[T](Func`1 task)
   at RestSharp.RestClientExtensions.Get[T](IRestClient client, RestRequest request)
   at H.BASE.Infrastructure.BaseConfigRequestHelper.GetBaseConfigFromBaseService(String urlS01, String hospitalId, String moduleId)
2025-07-07 10:23:53.161 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-07 10:24:43.026 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-07 10:25:36.797 +08:00 [ERR] 未处理的异常::Autofac.Core.DependencyResolutionException: An exception was thrown while activating XH.H82.Services.EquipmentClassNew.EquipmentClassService.
 ---> Autofac.Core.DependencyResolutionException: An exception was thrown while invoking the constructor 'Void .ctor(Microsoft.AspNetCore.Http.IHttpContextAccessor, H.BASE.SqlSugarInfra.Uow.ISqlSugarUow`1[XH.H82.Models.SugarDbContext.SugarDbContext_Master])' on type 'EquipmentClassService'.
 ---> System.NullReferenceException: Object reference not set to an instance of an object.
   at SqlSugar.NavigatManager`1.ExecuteByLay(Expression expression, List`1 list, Func`2 selector)
   at SqlSugar.NavigatManager`1.ExecuteByLay(Int32 i, Expression item)
   at SqlSugar.NavigatManager`1.Execute()
   at SqlSugar.QueryableProvider`1._InitNavigat[TResult](List`1 result)
   at SqlSugar.QueryableProvider`1._ToList[TResult]()
   at SqlSugar.QueryableProvider`1.ToList()
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService.GetEquipmentClassDictByCondition(Expression`1 filter) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 37
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService.GetEquipmentClassDict() in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 55
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService..ctor(IHttpContextAccessor httpContextAccessor, ISqlSugarUow`1 dbContext) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 29
   at lambda_method150(Closure , Object[] )
   at Autofac.Core.Activators.Reflection.BoundConstructor.Instantiate()
   --- End of inner exception stack trace ---
   at Autofac.Core.Activators.Reflection.BoundConstructor.Instantiate()
   at Autofac.Core.Activators.Reflection.ReflectionActivator.<>c__DisplayClass12_0.<UseSingleConstructorActivation>b__0(ResolveRequestContext ctxt, Action`1 next)
   at Autofac.Core.Resolving.Middleware.DelegateMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.DisposalTrackingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Extras.DynamicProxy.RegistrationExtensions.<>c__DisplayClass8_0`3.<EnableInterfaceInterceptors>b__1(ResolveRequestContext ctxt, Action`1 next)
   at Autofac.Core.Resolving.Middleware.DelegateMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.ActivatorErrorHandlingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   --- End of inner exception stack trace ---
   at Autofac.Core.Resolving.Middleware.ActivatorErrorHandlingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Pipeline.ResolvePipeline.Invoke(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.RegistrationPipelineInvokeMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.SharingMiddleware.<>c__DisplayClass5_0.<Execute>b__0()
   at Autofac.Core.Lifetime.LifetimeScope.CreateSharedInstance(Guid id, Func`1 creator)
   at Autofac.Core.Lifetime.LifetimeScope.CreateSharedInstance(Guid primaryId, Nullable`1 qualifyingId, Func`1 creator)
   at Autofac.Core.Resolving.Middleware.SharingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.ScopeSelectionMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.CircularDependencyDetectorMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Pipeline.ResolvePipeline.Invoke(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.ResolveOperation.GetOrCreateInstance(ISharingLifetimeScope currentOperationScope, ResolveRequest request)
   at Autofac.Core.Resolving.ResolveOperation.ExecuteOperation(ResolveRequest request)
   at Autofac.Core.Resolving.ResolveOperation.Execute(ResolveRequest request)
   at Autofac.Core.Lifetime.LifetimeScope.ResolveComponent(ResolveRequest request)
   at Autofac.ResolutionExtensions.TryResolveService(IComponentContext context, Service service, IEnumerable`1 parameters, Object& instance)
   at Autofac.ResolutionExtensions.ResolveOptionalService(IComponentContext context, Service service, IEnumerable`1 parameters)
   at Autofac.ResolutionExtensions.ResolveOptional(IComponentContext context, Type serviceType, IEnumerable`1 parameters)
   at Autofac.ResolutionExtensions.ResolveOptional(IComponentContext context, Type serviceType)
   at Autofac.Extensions.DependencyInjection.AutofacServiceProvider.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ActivatorUtilities.GetService(IServiceProvider sp, Type type, Type requiredBy, Boolean isDefaultParameterRequired)
   at lambda_method911(Closure , IServiceProvider , Object[] )
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerActivatorProvider.<>c__DisplayClass7_0.<CreateActivator>b__0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)Autofac.Core.DependencyResolutionException: An exception was thrown while invoking the constructor 'Void .ctor(Microsoft.AspNetCore.Http.IHttpContextAccessor, H.BASE.SqlSugarInfra.Uow.ISqlSugarUow`1[XH.H82.Models.SugarDbContext.SugarDbContext_Master])' on type 'EquipmentClassService'.
 ---> System.NullReferenceException: Object reference not set to an instance of an object.
   at SqlSugar.NavigatManager`1.ExecuteByLay(Expression expression, List`1 list, Func`2 selector)
   at SqlSugar.NavigatManager`1.ExecuteByLay(Int32 i, Expression item)
   at SqlSugar.NavigatManager`1.Execute()
   at SqlSugar.QueryableProvider`1._InitNavigat[TResult](List`1 result)
   at SqlSugar.QueryableProvider`1._ToList[TResult]()
   at SqlSugar.QueryableProvider`1.ToList()
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService.GetEquipmentClassDictByCondition(Expression`1 filter) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 37
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService.GetEquipmentClassDict() in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 55
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService..ctor(IHttpContextAccessor httpContextAccessor, ISqlSugarUow`1 dbContext) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 29
   at lambda_method150(Closure , Object[] )
   at Autofac.Core.Activators.Reflection.BoundConstructor.Instantiate()
   --- End of inner exception stack trace ---
   at Autofac.Core.Activators.Reflection.BoundConstructor.Instantiate()
   at Autofac.Core.Activators.Reflection.ReflectionActivator.<>c__DisplayClass12_0.<UseSingleConstructorActivation>b__0(ResolveRequestContext ctxt, Action`1 next)
   at Autofac.Core.Resolving.Middleware.DelegateMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.DisposalTrackingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Extras.DynamicProxy.RegistrationExtensions.<>c__DisplayClass8_0`3.<EnableInterfaceInterceptors>b__1(ResolveRequestContext ctxt, Action`1 next)
   at Autofac.Core.Resolving.Middleware.DelegateMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.ActivatorErrorHandlingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
2025-07-07 10:25:36.805 +08:00 [ERR] HTTP GET /api/EquipmentClassNew/GetEquipmentArchivesLinkByClassId/1 responded 500 in 27086.6532 ms
2025-07-07 10:26:30.028 +08:00 [ERR] 未处理的异常::Autofac.Core.DependencyResolutionException: An exception was thrown while activating XH.H82.Services.EquipmentClassNew.EquipmentClassService.
 ---> Autofac.Core.DependencyResolutionException: An exception was thrown while invoking the constructor 'Void .ctor(Microsoft.AspNetCore.Http.IHttpContextAccessor, H.BASE.SqlSugarInfra.Uow.ISqlSugarUow`1[XH.H82.Models.SugarDbContext.SugarDbContext_Master])' on type 'EquipmentClassService'.
 ---> System.NullReferenceException: Object reference not set to an instance of an object.
   at SqlSugar.NavigatManager`1.ExecuteByLay(Expression expression, List`1 list, Func`2 selector)
   at SqlSugar.NavigatManager`1.ExecuteByLay(Int32 i, Expression item)
   at SqlSugar.NavigatManager`1.Execute()
   at SqlSugar.QueryableProvider`1._InitNavigat[TResult](List`1 result)
   at SqlSugar.QueryableProvider`1._ToList[TResult]()
   at SqlSugar.QueryableProvider`1.ToList()
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService.GetEquipmentClassDictByCondition(Expression`1 filter) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 37
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService.GetEquipmentClassDict() in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 55
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService..ctor(IHttpContextAccessor httpContextAccessor, ISqlSugarUow`1 dbContext) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 29
   at lambda_method150(Closure , Object[] )
   at Autofac.Core.Activators.Reflection.BoundConstructor.Instantiate()
   --- End of inner exception stack trace ---
   at Autofac.Core.Activators.Reflection.BoundConstructor.Instantiate()
   at Autofac.Core.Activators.Reflection.ReflectionActivator.<>c__DisplayClass12_0.<UseSingleConstructorActivation>b__0(ResolveRequestContext ctxt, Action`1 next)
   at Autofac.Core.Resolving.Middleware.DelegateMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.DisposalTrackingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Extras.DynamicProxy.RegistrationExtensions.<>c__DisplayClass8_0`3.<EnableInterfaceInterceptors>b__1(ResolveRequestContext ctxt, Action`1 next)
   at Autofac.Core.Resolving.Middleware.DelegateMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.ActivatorErrorHandlingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   --- End of inner exception stack trace ---
   at Autofac.Core.Resolving.Middleware.ActivatorErrorHandlingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Pipeline.ResolvePipeline.Invoke(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.RegistrationPipelineInvokeMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.SharingMiddleware.<>c__DisplayClass5_0.<Execute>b__0()
   at Autofac.Core.Lifetime.LifetimeScope.CreateSharedInstance(Guid id, Func`1 creator)
   at Autofac.Core.Lifetime.LifetimeScope.CreateSharedInstance(Guid primaryId, Nullable`1 qualifyingId, Func`1 creator)
   at Autofac.Core.Resolving.Middleware.SharingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.ScopeSelectionMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.CircularDependencyDetectorMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Pipeline.ResolvePipeline.Invoke(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.ResolveOperation.GetOrCreateInstance(ISharingLifetimeScope currentOperationScope, ResolveRequest request)
   at Autofac.Core.Resolving.ResolveOperation.ExecuteOperation(ResolveRequest request)
   at Autofac.Core.Resolving.ResolveOperation.Execute(ResolveRequest request)
   at Autofac.Core.Lifetime.LifetimeScope.ResolveComponent(ResolveRequest request)
   at Autofac.ResolutionExtensions.TryResolveService(IComponentContext context, Service service, IEnumerable`1 parameters, Object& instance)
   at Autofac.ResolutionExtensions.ResolveOptionalService(IComponentContext context, Service service, IEnumerable`1 parameters)
   at Autofac.ResolutionExtensions.ResolveOptional(IComponentContext context, Type serviceType, IEnumerable`1 parameters)
   at Autofac.ResolutionExtensions.ResolveOptional(IComponentContext context, Type serviceType)
   at Autofac.Extensions.DependencyInjection.AutofacServiceProvider.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ActivatorUtilities.GetService(IServiceProvider sp, Type type, Type requiredBy, Boolean isDefaultParameterRequired)
   at lambda_method911(Closure , IServiceProvider , Object[] )
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerActivatorProvider.<>c__DisplayClass7_0.<CreateActivator>b__0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)Autofac.Core.DependencyResolutionException: An exception was thrown while invoking the constructor 'Void .ctor(Microsoft.AspNetCore.Http.IHttpContextAccessor, H.BASE.SqlSugarInfra.Uow.ISqlSugarUow`1[XH.H82.Models.SugarDbContext.SugarDbContext_Master])' on type 'EquipmentClassService'.
 ---> System.NullReferenceException: Object reference not set to an instance of an object.
   at SqlSugar.NavigatManager`1.ExecuteByLay(Expression expression, List`1 list, Func`2 selector)
   at SqlSugar.NavigatManager`1.ExecuteByLay(Int32 i, Expression item)
   at SqlSugar.NavigatManager`1.Execute()
   at SqlSugar.QueryableProvider`1._InitNavigat[TResult](List`1 result)
   at SqlSugar.QueryableProvider`1._ToList[TResult]()
   at SqlSugar.QueryableProvider`1.ToList()
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService.GetEquipmentClassDictByCondition(Expression`1 filter) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 37
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService.GetEquipmentClassDict() in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 55
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService..ctor(IHttpContextAccessor httpContextAccessor, ISqlSugarUow`1 dbContext) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 29
   at lambda_method150(Closure , Object[] )
   at Autofac.Core.Activators.Reflection.BoundConstructor.Instantiate()
   --- End of inner exception stack trace ---
   at Autofac.Core.Activators.Reflection.BoundConstructor.Instantiate()
   at Autofac.Core.Activators.Reflection.ReflectionActivator.<>c__DisplayClass12_0.<UseSingleConstructorActivation>b__0(ResolveRequestContext ctxt, Action`1 next)
   at Autofac.Core.Resolving.Middleware.DelegateMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.DisposalTrackingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Extras.DynamicProxy.RegistrationExtensions.<>c__DisplayClass8_0`3.<EnableInterfaceInterceptors>b__1(ResolveRequestContext ctxt, Action`1 next)
   at Autofac.Core.Resolving.Middleware.DelegateMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.ActivatorErrorHandlingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
2025-07-07 10:26:30.035 +08:00 [ERR] HTTP GET /api/EquipmentClassNew/GetEquipmentArchivesLinkByClassId/1 responded 500 in 26700.0958 ms
2025-07-07 10:27:37.821 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-07 10:27:53.637 +08:00 [ERR] 未处理的异常::Autofac.Core.DependencyResolutionException: An exception was thrown while activating XH.H82.Services.EquipmentClassNew.EquipmentClassService.
 ---> Autofac.Core.DependencyResolutionException: An exception was thrown while invoking the constructor 'Void .ctor(Microsoft.AspNetCore.Http.IHttpContextAccessor, H.BASE.SqlSugarInfra.Uow.ISqlSugarUow`1[XH.H82.Models.SugarDbContext.SugarDbContext_Master])' on type 'EquipmentClassService'.
 ---> System.NullReferenceException: Object reference not set to an instance of an object.
   at SqlSugar.NavigatManager`1.ExecuteByLay(Expression expression, List`1 list, Func`2 selector)
   at SqlSugar.NavigatManager`1.ExecuteByLay(Int32 i, Expression item)
   at SqlSugar.NavigatManager`1.Execute()
   at SqlSugar.QueryableProvider`1._InitNavigat[TResult](List`1 result)
   at SqlSugar.QueryableProvider`1._ToList[TResult]()
   at SqlSugar.QueryableProvider`1.ToList()
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService.GetEquipmentClassDictByCondition(Expression`1 filter) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 37
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService.GetEquipmentClassDict() in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 55
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService..ctor(IHttpContextAccessor httpContextAccessor, ISqlSugarUow`1 dbContext) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 29
   at lambda_method150(Closure , Object[] )
   at Autofac.Core.Activators.Reflection.BoundConstructor.Instantiate()
   --- End of inner exception stack trace ---
   at Autofac.Core.Activators.Reflection.BoundConstructor.Instantiate()
   at Autofac.Core.Activators.Reflection.ReflectionActivator.<>c__DisplayClass12_0.<UseSingleConstructorActivation>b__0(ResolveRequestContext ctxt, Action`1 next)
   at Autofac.Core.Resolving.Middleware.DelegateMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.DisposalTrackingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Extras.DynamicProxy.RegistrationExtensions.<>c__DisplayClass8_0`3.<EnableInterfaceInterceptors>b__1(ResolveRequestContext ctxt, Action`1 next)
   at Autofac.Core.Resolving.Middleware.DelegateMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.ActivatorErrorHandlingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   --- End of inner exception stack trace ---
   at Autofac.Core.Resolving.Middleware.ActivatorErrorHandlingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Pipeline.ResolvePipeline.Invoke(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.RegistrationPipelineInvokeMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.SharingMiddleware.<>c__DisplayClass5_0.<Execute>b__0()
   at Autofac.Core.Lifetime.LifetimeScope.CreateSharedInstance(Guid id, Func`1 creator)
   at Autofac.Core.Lifetime.LifetimeScope.CreateSharedInstance(Guid primaryId, Nullable`1 qualifyingId, Func`1 creator)
   at Autofac.Core.Resolving.Middleware.SharingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.ScopeSelectionMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.CircularDependencyDetectorMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Pipeline.ResolvePipeline.Invoke(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.ResolveOperation.GetOrCreateInstance(ISharingLifetimeScope currentOperationScope, ResolveRequest request)
   at Autofac.Core.Resolving.ResolveOperation.ExecuteOperation(ResolveRequest request)
   at Autofac.Core.Resolving.ResolveOperation.Execute(ResolveRequest request)
   at Autofac.Core.Lifetime.LifetimeScope.ResolveComponent(ResolveRequest request)
   at Autofac.ResolutionExtensions.TryResolveService(IComponentContext context, Service service, IEnumerable`1 parameters, Object& instance)
   at Autofac.ResolutionExtensions.ResolveOptionalService(IComponentContext context, Service service, IEnumerable`1 parameters)
   at Autofac.ResolutionExtensions.ResolveOptional(IComponentContext context, Type serviceType, IEnumerable`1 parameters)
   at Autofac.ResolutionExtensions.ResolveOptional(IComponentContext context, Type serviceType)
   at Autofac.Extensions.DependencyInjection.AutofacServiceProvider.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ActivatorUtilities.GetService(IServiceProvider sp, Type type, Type requiredBy, Boolean isDefaultParameterRequired)
   at lambda_method911(Closure , IServiceProvider , Object[] )
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerActivatorProvider.<>c__DisplayClass7_0.<CreateActivator>b__0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)Autofac.Core.DependencyResolutionException: An exception was thrown while invoking the constructor 'Void .ctor(Microsoft.AspNetCore.Http.IHttpContextAccessor, H.BASE.SqlSugarInfra.Uow.ISqlSugarUow`1[XH.H82.Models.SugarDbContext.SugarDbContext_Master])' on type 'EquipmentClassService'.
 ---> System.NullReferenceException: Object reference not set to an instance of an object.
   at SqlSugar.NavigatManager`1.ExecuteByLay(Expression expression, List`1 list, Func`2 selector)
   at SqlSugar.NavigatManager`1.ExecuteByLay(Int32 i, Expression item)
   at SqlSugar.NavigatManager`1.Execute()
   at SqlSugar.QueryableProvider`1._InitNavigat[TResult](List`1 result)
   at SqlSugar.QueryableProvider`1._ToList[TResult]()
   at SqlSugar.QueryableProvider`1.ToList()
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService.GetEquipmentClassDictByCondition(Expression`1 filter) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 37
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService.GetEquipmentClassDict() in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 55
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService..ctor(IHttpContextAccessor httpContextAccessor, ISqlSugarUow`1 dbContext) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 29
   at lambda_method150(Closure , Object[] )
   at Autofac.Core.Activators.Reflection.BoundConstructor.Instantiate()
   --- End of inner exception stack trace ---
   at Autofac.Core.Activators.Reflection.BoundConstructor.Instantiate()
   at Autofac.Core.Activators.Reflection.ReflectionActivator.<>c__DisplayClass12_0.<UseSingleConstructorActivation>b__0(ResolveRequestContext ctxt, Action`1 next)
   at Autofac.Core.Resolving.Middleware.DelegateMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.DisposalTrackingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Extras.DynamicProxy.RegistrationExtensions.<>c__DisplayClass8_0`3.<EnableInterfaceInterceptors>b__1(ResolveRequestContext ctxt, Action`1 next)
   at Autofac.Core.Resolving.Middleware.DelegateMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.ActivatorErrorHandlingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
2025-07-07 10:27:53.649 +08:00 [ERR] HTTP GET /api/EquipmentClassNew/GetEquipmentArchivesLinkByClassId/1 responded 500 in 7947.5494 ms
2025-07-07 10:28:19.876 +08:00 [ERR] 未处理的异常::Autofac.Core.DependencyResolutionException: An exception was thrown while activating XH.H82.Services.EquipmentClassNew.EquipmentClassService.
 ---> Autofac.Core.DependencyResolutionException: An exception was thrown while invoking the constructor 'Void .ctor(Microsoft.AspNetCore.Http.IHttpContextAccessor, H.BASE.SqlSugarInfra.Uow.ISqlSugarUow`1[XH.H82.Models.SugarDbContext.SugarDbContext_Master])' on type 'EquipmentClassService'.
 ---> System.NullReferenceException: Object reference not set to an instance of an object.
   at SqlSugar.NavigatManager`1.ExecuteByLay(Expression expression, List`1 list, Func`2 selector)
   at SqlSugar.NavigatManager`1.ExecuteByLay(Int32 i, Expression item)
   at SqlSugar.NavigatManager`1.Execute()
   at SqlSugar.QueryableProvider`1._InitNavigat[TResult](List`1 result)
   at SqlSugar.QueryableProvider`1._ToList[TResult]()
   at SqlSugar.QueryableProvider`1.ToList()
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService.GetEquipmentClassDictByCondition(Expression`1 filter) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 37
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService.GetEquipmentClassDict() in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 55
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService..ctor(IHttpContextAccessor httpContextAccessor, ISqlSugarUow`1 dbContext) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 29
   at lambda_method150(Closure , Object[] )
   at Autofac.Core.Activators.Reflection.BoundConstructor.Instantiate()
   --- End of inner exception stack trace ---
   at Autofac.Core.Activators.Reflection.BoundConstructor.Instantiate()
   at Autofac.Core.Activators.Reflection.ReflectionActivator.<>c__DisplayClass12_0.<UseSingleConstructorActivation>b__0(ResolveRequestContext ctxt, Action`1 next)
   at Autofac.Core.Resolving.Middleware.DelegateMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.DisposalTrackingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Extras.DynamicProxy.RegistrationExtensions.<>c__DisplayClass8_0`3.<EnableInterfaceInterceptors>b__1(ResolveRequestContext ctxt, Action`1 next)
   at Autofac.Core.Resolving.Middleware.DelegateMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.ActivatorErrorHandlingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   --- End of inner exception stack trace ---
   at Autofac.Core.Resolving.Middleware.ActivatorErrorHandlingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Pipeline.ResolvePipeline.Invoke(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.RegistrationPipelineInvokeMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.SharingMiddleware.<>c__DisplayClass5_0.<Execute>b__0()
   at Autofac.Core.Lifetime.LifetimeScope.CreateSharedInstance(Guid id, Func`1 creator)
   at Autofac.Core.Lifetime.LifetimeScope.CreateSharedInstance(Guid primaryId, Nullable`1 qualifyingId, Func`1 creator)
   at Autofac.Core.Resolving.Middleware.SharingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.ScopeSelectionMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.CircularDependencyDetectorMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Pipeline.ResolvePipeline.Invoke(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.ResolveOperation.GetOrCreateInstance(ISharingLifetimeScope currentOperationScope, ResolveRequest request)
   at Autofac.Core.Resolving.ResolveOperation.ExecuteOperation(ResolveRequest request)
   at Autofac.Core.Resolving.ResolveOperation.Execute(ResolveRequest request)
   at Autofac.Core.Lifetime.LifetimeScope.ResolveComponent(ResolveRequest request)
   at Autofac.ResolutionExtensions.TryResolveService(IComponentContext context, Service service, IEnumerable`1 parameters, Object& instance)
   at Autofac.ResolutionExtensions.ResolveOptionalService(IComponentContext context, Service service, IEnumerable`1 parameters)
   at Autofac.ResolutionExtensions.ResolveOptional(IComponentContext context, Type serviceType, IEnumerable`1 parameters)
   at Autofac.ResolutionExtensions.ResolveOptional(IComponentContext context, Type serviceType)
   at Autofac.Extensions.DependencyInjection.AutofacServiceProvider.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ActivatorUtilities.GetService(IServiceProvider sp, Type type, Type requiredBy, Boolean isDefaultParameterRequired)
   at lambda_method911(Closure , IServiceProvider , Object[] )
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerActivatorProvider.<>c__DisplayClass7_0.<CreateActivator>b__0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)Autofac.Core.DependencyResolutionException: An exception was thrown while invoking the constructor 'Void .ctor(Microsoft.AspNetCore.Http.IHttpContextAccessor, H.BASE.SqlSugarInfra.Uow.ISqlSugarUow`1[XH.H82.Models.SugarDbContext.SugarDbContext_Master])' on type 'EquipmentClassService'.
 ---> System.NullReferenceException: Object reference not set to an instance of an object.
   at SqlSugar.NavigatManager`1.ExecuteByLay(Expression expression, List`1 list, Func`2 selector)
   at SqlSugar.NavigatManager`1.ExecuteByLay(Int32 i, Expression item)
   at SqlSugar.NavigatManager`1.Execute()
   at SqlSugar.QueryableProvider`1._InitNavigat[TResult](List`1 result)
   at SqlSugar.QueryableProvider`1._ToList[TResult]()
   at SqlSugar.QueryableProvider`1.ToList()
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService.GetEquipmentClassDictByCondition(Expression`1 filter) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 37
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService.GetEquipmentClassDict() in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 55
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService..ctor(IHttpContextAccessor httpContextAccessor, ISqlSugarUow`1 dbContext) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 29
   at lambda_method150(Closure , Object[] )
   at Autofac.Core.Activators.Reflection.BoundConstructor.Instantiate()
   --- End of inner exception stack trace ---
   at Autofac.Core.Activators.Reflection.BoundConstructor.Instantiate()
   at Autofac.Core.Activators.Reflection.ReflectionActivator.<>c__DisplayClass12_0.<UseSingleConstructorActivation>b__0(ResolveRequestContext ctxt, Action`1 next)
   at Autofac.Core.Resolving.Middleware.DelegateMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.DisposalTrackingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Extras.DynamicProxy.RegistrationExtensions.<>c__DisplayClass8_0`3.<EnableInterfaceInterceptors>b__1(ResolveRequestContext ctxt, Action`1 next)
   at Autofac.Core.Resolving.Middleware.DelegateMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.ActivatorErrorHandlingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
2025-07-07 10:28:19.881 +08:00 [ERR] HTTP GET /api/EquipmentClassNew/GetEquipmentArchivesLinkByClassId/1 responded 500 in 18227.1355 ms
2025-07-07 10:28:32.161 +08:00 [ERR] 未处理的异常::Autofac.Core.DependencyResolutionException: An exception was thrown while activating XH.H82.Services.EquipmentClassNew.EquipmentClassService.
 ---> Autofac.Core.DependencyResolutionException: An exception was thrown while invoking the constructor 'Void .ctor(Microsoft.AspNetCore.Http.IHttpContextAccessor, H.BASE.SqlSugarInfra.Uow.ISqlSugarUow`1[XH.H82.Models.SugarDbContext.SugarDbContext_Master])' on type 'EquipmentClassService'.
 ---> System.NullReferenceException: Object reference not set to an instance of an object.
   at SqlSugar.NavigatManager`1.ExecuteByLay(Expression expression, List`1 list, Func`2 selector)
   at SqlSugar.NavigatManager`1.ExecuteByLay(Int32 i, Expression item)
   at SqlSugar.NavigatManager`1.Execute()
   at SqlSugar.QueryableProvider`1._InitNavigat[TResult](List`1 result)
   at SqlSugar.QueryableProvider`1._ToList[TResult]()
   at SqlSugar.QueryableProvider`1.ToList()
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService.GetEquipmentClassDictByCondition(Expression`1 filter) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 37
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService.GetEquipmentClassDict() in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 55
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService..ctor(IHttpContextAccessor httpContextAccessor, ISqlSugarUow`1 dbContext) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 29
   at lambda_method150(Closure , Object[] )
   at Autofac.Core.Activators.Reflection.BoundConstructor.Instantiate()
   --- End of inner exception stack trace ---
   at Autofac.Core.Activators.Reflection.BoundConstructor.Instantiate()
   at Autofac.Core.Activators.Reflection.ReflectionActivator.<>c__DisplayClass12_0.<UseSingleConstructorActivation>b__0(ResolveRequestContext ctxt, Action`1 next)
   at Autofac.Core.Resolving.Middleware.DelegateMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.DisposalTrackingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Extras.DynamicProxy.RegistrationExtensions.<>c__DisplayClass8_0`3.<EnableInterfaceInterceptors>b__1(ResolveRequestContext ctxt, Action`1 next)
   at Autofac.Core.Resolving.Middleware.DelegateMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.ActivatorErrorHandlingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   --- End of inner exception stack trace ---
   at Autofac.Core.Resolving.Middleware.ActivatorErrorHandlingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Pipeline.ResolvePipeline.Invoke(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.RegistrationPipelineInvokeMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.SharingMiddleware.<>c__DisplayClass5_0.<Execute>b__0()
   at Autofac.Core.Lifetime.LifetimeScope.CreateSharedInstance(Guid id, Func`1 creator)
   at Autofac.Core.Lifetime.LifetimeScope.CreateSharedInstance(Guid primaryId, Nullable`1 qualifyingId, Func`1 creator)
   at Autofac.Core.Resolving.Middleware.SharingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.ScopeSelectionMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.CircularDependencyDetectorMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Pipeline.ResolvePipeline.Invoke(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.ResolveOperation.GetOrCreateInstance(ISharingLifetimeScope currentOperationScope, ResolveRequest request)
   at Autofac.Core.Resolving.ResolveOperation.ExecuteOperation(ResolveRequest request)
   at Autofac.Core.Resolving.ResolveOperation.Execute(ResolveRequest request)
   at Autofac.Core.Lifetime.LifetimeScope.ResolveComponent(ResolveRequest request)
   at Autofac.ResolutionExtensions.TryResolveService(IComponentContext context, Service service, IEnumerable`1 parameters, Object& instance)
   at Autofac.ResolutionExtensions.ResolveOptionalService(IComponentContext context, Service service, IEnumerable`1 parameters)
   at Autofac.ResolutionExtensions.ResolveOptional(IComponentContext context, Type serviceType, IEnumerable`1 parameters)
   at Autofac.ResolutionExtensions.ResolveOptional(IComponentContext context, Type serviceType)
   at Autofac.Extensions.DependencyInjection.AutofacServiceProvider.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ActivatorUtilities.GetService(IServiceProvider sp, Type type, Type requiredBy, Boolean isDefaultParameterRequired)
   at lambda_method911(Closure , IServiceProvider , Object[] )
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerActivatorProvider.<>c__DisplayClass7_0.<CreateActivator>b__0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)Autofac.Core.DependencyResolutionException: An exception was thrown while invoking the constructor 'Void .ctor(Microsoft.AspNetCore.Http.IHttpContextAccessor, H.BASE.SqlSugarInfra.Uow.ISqlSugarUow`1[XH.H82.Models.SugarDbContext.SugarDbContext_Master])' on type 'EquipmentClassService'.
 ---> System.NullReferenceException: Object reference not set to an instance of an object.
   at SqlSugar.NavigatManager`1.ExecuteByLay(Expression expression, List`1 list, Func`2 selector)
   at SqlSugar.NavigatManager`1.ExecuteByLay(Int32 i, Expression item)
   at SqlSugar.NavigatManager`1.Execute()
   at SqlSugar.QueryableProvider`1._InitNavigat[TResult](List`1 result)
   at SqlSugar.QueryableProvider`1._ToList[TResult]()
   at SqlSugar.QueryableProvider`1.ToList()
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService.GetEquipmentClassDictByCondition(Expression`1 filter) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 37
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService.GetEquipmentClassDict() in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 55
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService..ctor(IHttpContextAccessor httpContextAccessor, ISqlSugarUow`1 dbContext) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 29
   at lambda_method150(Closure , Object[] )
   at Autofac.Core.Activators.Reflection.BoundConstructor.Instantiate()
   --- End of inner exception stack trace ---
   at Autofac.Core.Activators.Reflection.BoundConstructor.Instantiate()
   at Autofac.Core.Activators.Reflection.ReflectionActivator.<>c__DisplayClass12_0.<UseSingleConstructorActivation>b__0(ResolveRequestContext ctxt, Action`1 next)
   at Autofac.Core.Resolving.Middleware.DelegateMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.DisposalTrackingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Extras.DynamicProxy.RegistrationExtensions.<>c__DisplayClass8_0`3.<EnableInterfaceInterceptors>b__1(ResolveRequestContext ctxt, Action`1 next)
   at Autofac.Core.Resolving.Middleware.DelegateMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.ActivatorErrorHandlingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
2025-07-07 10:28:32.165 +08:00 [ERR] HTTP GET /api/EquipmentClassNew/GetEquipmentArchivesLinkByClassId/1 responded 500 in 10759.4243 ms
2025-07-07 10:28:59.553 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-07 10:32:41.313 +08:00 [ERR] 未处理的异常::System.NullReferenceException: Object reference not set to an instance of an object.
   at XH.H82.Services.EquipmentCodeCustom.CustomCodeService.GetEquipmentInfoByCache(String eqpNoClass) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentCodeCustom\CustomCodeService.cs:line 295
   at XH.H82.Services.EquipmentCodeCustom.CustomCodeService.GetEquipmentCodeCustomDictByCondetion(Expression`1 expression) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentCodeCustom\CustomCodeService.cs:line 86
   at XH.H82.Services.EquipmentCodeCustom.CustomCodeService.GetEquipmentCodeCustomDict() in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentCodeCustom\CustomCodeService.cs:line 94
   at Castle.Proxies.Invocations.ICustomCodeService_GetEquipmentCodeCustomDict.InvokeMethodOnTarget()
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.UseCacheWhenFailedAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.CacheAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at Castle.Proxies.ICustomCodeServiceProxy.GetEquipmentCodeCustomDict()
   at XH.H82.API.Controllers.EquipmentCodeCustom.CodeCustomController.GetEquipmentCodeCustomDict() in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\Controllers\EquipmentCodeCustom\CodeCustomController.cs:line 37
   at lambda_method968(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-07-07 10:32:41.316 +08:00 [ERR] HTTP GET /api/CodeCustom/GetEquipmentCodeCustomDict responded 500 in 29609.4196 ms
2025-07-07 10:43:36.275 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-07 11:04:31.466 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-07 11:34:23.906 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-07 11:35:11.524 +08:00 [ERR] 未处理的异常::System.InvalidOperationException: Unable to resolve service for type 'XH.H82.IServices.EquipmentClassNew.IEquipomentArchivesService' while attempting to activate 'XH.H82.API.Controllers.EquipmentClassNew.EquipmentArchivesController'.
   at Microsoft.Extensions.DependencyInjection.ActivatorUtilities.GetService(IServiceProvider sp, Type type, Type requiredBy, Boolean isDefaultParameterRequired)
   at lambda_method911(Closure , IServiceProvider , Object[] )
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerActivatorProvider.<>c__DisplayClass7_0.<CreateActivator>b__0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-07-07 11:35:11.552 +08:00 [ERR] HTTP GET /api/EquipmentArchives/GetEquipmentArchives responded 500 in 4233.8825 ms
2025-07-07 11:35:13.301 +08:00 [ERR] 未处理的异常::System.InvalidOperationException: Unable to resolve service for type 'XH.H82.IServices.EquipmentClassNew.IEquipomentArchivesService' while attempting to activate 'XH.H82.API.Controllers.EquipmentClassNew.EquipmentArchivesController'.
   at Microsoft.Extensions.DependencyInjection.ActivatorUtilities.GetService(IServiceProvider sp, Type type, Type requiredBy, Boolean isDefaultParameterRequired)
   at lambda_method911(Closure , IServiceProvider , Object[] )
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerActivatorProvider.<>c__DisplayClass7_0.<CreateActivator>b__0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-07-07 11:35:13.304 +08:00 [ERR] HTTP GET /api/EquipmentArchives/GetEquipmentArchives responded 500 in 49.5437 ms
2025-07-07 11:35:14.707 +08:00 [ERR] 未处理的异常::System.InvalidOperationException: Unable to resolve service for type 'XH.H82.IServices.EquipmentClassNew.IEquipomentArchivesService' while attempting to activate 'XH.H82.API.Controllers.EquipmentClassNew.EquipmentArchivesController'.
   at Microsoft.Extensions.DependencyInjection.ActivatorUtilities.GetService(IServiceProvider sp, Type type, Type requiredBy, Boolean isDefaultParameterRequired)
   at lambda_method911(Closure , IServiceProvider , Object[] )
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerActivatorProvider.<>c__DisplayClass7_0.<CreateActivator>b__0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-07-07 11:35:14.709 +08:00 [ERR] HTTP GET /api/EquipmentArchives/GetEquipmentArchives responded 500 in 158.0060 ms
2025-07-07 11:35:15.946 +08:00 [ERR] 未处理的异常::System.InvalidOperationException: Unable to resolve service for type 'XH.H82.IServices.EquipmentClassNew.IEquipomentArchivesService' while attempting to activate 'XH.H82.API.Controllers.EquipmentClassNew.EquipmentArchivesController'.
   at Microsoft.Extensions.DependencyInjection.ActivatorUtilities.GetService(IServiceProvider sp, Type type, Type requiredBy, Boolean isDefaultParameterRequired)
   at lambda_method911(Closure , IServiceProvider , Object[] )
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerActivatorProvider.<>c__DisplayClass7_0.<CreateActivator>b__0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-07-07 11:35:15.947 +08:00 [ERR] HTTP GET /api/EquipmentArchives/GetEquipmentArchives responded 500 in 42.6073 ms
2025-07-07 11:35:51.914 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-07 14:54:07.150 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-07 15:44:59.911 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-07 15:45:11.578 +08:00 [ERR] An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Ambiguous HTTP method for action - XH.H82.API.Controllers.EquipmentClassNew.EquipmentArchivesController.AddEquipmentArchives (XH.H82.API). Actions require an explicit HttpMethod binding for Swagger/OpenAPI 3.0
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperations(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePaths(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwagger(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)
2025-07-07 15:45:16.808 +08:00 [ERR] An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Ambiguous HTTP method for action - XH.H82.API.Controllers.EquipmentClassNew.EquipmentArchivesController.AddEquipmentArchives (XH.H82.API). Actions require an explicit HttpMethod binding for Swagger/OpenAPI 3.0
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperations(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePaths(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwagger(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)
2025-07-07 15:47:01.218 +08:00 [ERR] An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Ambiguous HTTP method for action - XH.H82.API.Controllers.EquipmentClassNew.EquipmentArchivesController.AddEquipmentArchives (XH.H82.API). Actions require an explicit HttpMethod binding for Swagger/OpenAPI 3.0
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperations(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePaths(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwagger(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)
2025-07-07 15:47:51.248 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-07 15:48:01.977 +08:00 [ERR] An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Ambiguous HTTP method for action - XH.H82.API.Controllers.EquipmentClassNew.EquipmentArchivesController.AddEquipmentArchives (XH.H82.API). Actions require an explicit HttpMethod binding for Swagger/OpenAPI 3.0
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperations(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePaths(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwagger(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)
2025-07-07 15:50:20.192 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-07 15:50:32.700 +08:00 [ERR] An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Ambiguous HTTP method for action - XH.H82.API.Controllers.EquipmentClassNew.EquipmentArchivesController.AddEquipmentArchives (XH.H82.API). Actions require an explicit HttpMethod binding for Swagger/OpenAPI 3.0
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperations(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePaths(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwagger(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)
2025-07-07 15:50:55.367 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
