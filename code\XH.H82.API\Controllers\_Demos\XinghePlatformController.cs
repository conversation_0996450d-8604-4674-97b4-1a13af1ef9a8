﻿using H.Utility;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using XH.H82.IServices;

namespace XH.H82.API.Controllers._Demos
{

    //接口直接返回实体类测试
    public class IntefaceDemoDto1
    {
        public string COMPANY_ID { get; set; }
        public string COMPANY_NAME { get; set; }
        public string LOGIN_ID { get; set; }
        public string NAME { get; set; }
    }
    [Route("api/[controller]/[action]")]
    [ApiController]
    [ApiExplorerSettings(GroupName = "DEMO")]

    public class XinghePlatformController : ControllerBase
    {
        private readonly ISystemService _systemService;

        public XinghePlatformController(ISystemService systemService)
        {
            _systemService = systemService;
        }

        /// <summary>
        /// 访问接口并直接将返回结果映射成实体类测试 主要需要自行建立映射实体
        /// </summary>
        /// <param name="headXml"></param>
        /// <param name="bodyXml"></param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult CallXingheInterface(string headXml, string bodyXml)
        {
            headXml = headXml ??
                      "<Root><MethodCode>GetPomparyList</MethodCode><ProductCode>LIS5</ProductCode><HospitalCode>A001</HospitalCode></Root>";
            bodyXml = bodyXml ??
                      "<DATA><ITEM MODULE_ID=\"B02\" AREA_ID=\"A001\" START_TIME=\"2022-08-09\" END_TIME=\"2022-11-09\"></ITEM></DATA>";
            var res = _systemService.CallXhPlatformInterface<List<IntefaceDemoDto1>>(headXml, bodyXml);

            return Ok(res.ToResultDto());
        }

        /// <summary>
        /// 调用接口返回原始数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult CallXhPlatformInterfaceSource(string headXml, string bodyXml)
        {
            headXml = headXml ??
                      "<Root><MethodCode>GetPomparyList</MethodCode><ProductCode>LIS5</ProductCode><HospitalCode>A001</HospitalCode></Root>";
            bodyXml = bodyXml ??
                      "<DATA><ITEM MODULE_ID=\"B02\" AREA_ID=\"A001\" START_TIME=\"2022-08-09\" END_TIME=\"2022-11-09\"></ITEM></DATA>";
            var res = _systemService.CallXhPlatformInterfaceSource(headXml, bodyXml, out string dataformat);
            return Ok(res.ToResultDto());
        }

    }
}
