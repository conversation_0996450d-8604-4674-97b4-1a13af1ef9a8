﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.Dtos
{
    public class ScrapStopListDto
    {
        public string ScrapStop_ID { get; set; }
        public string STATE { get; set; }
        public string APPLY_CLASS { get; set; }
        public string MGROUP_NAME { get; set; }
        public string MGROUP_ID { get; set; }
        public string EQUIPMENT_CLASS { get; set; }
        public string EQUIPMENT_ID { get; set; }
        public string EQUIPMENT_NAME { get; set; }
        public string EQUIPMENT_MODAL { get; set; }
        public string EQUIPMENT_CODE { get; set; }
        public string MANUFACTURER { get; set; }
        public string DEALER { get; set; }
        public string APPLY_PERSON { get; set; }
        public string APPLY_PERSON_ID { get; set; }
        public DateTime? APPLY_DATE { get; set; }
        public string APPLY_REASON { get; set; }
        public string EXAMINE_PERSON { get; set; }
        public string EXAMINE_PERSON_ID { get; set; }
        public DateTime? EXAMINE_DATE { get; set; }
        public string EXAMINE_OPINION { get; set; }
        public string APPROVE_PERSON { get; set; }
        public string APPROVE_PERSON_ID { get; set; }
        public DateTime? APPROVE_DATE { get; set; }
        public string APPROVE_OPINION { get; set; }
        public string IF_RE_APPLY { get; set; }
        public DateTime OPER_TIME { get; set; }
        public string DEAL_PERSON_ID { get; set; }
        public string EQUIPMENT_STATE { get; set; }
        public string REVOKE_PERSON_ID { get; set; }

        public string FILE_PATH { get; set; }

        public DateTime? SCRAP_DATE { get; set; }
        public string? SMBL_FLAG  { get; set; }
        public string? SMBL_LAB_ID { get; set; }
    }
}
