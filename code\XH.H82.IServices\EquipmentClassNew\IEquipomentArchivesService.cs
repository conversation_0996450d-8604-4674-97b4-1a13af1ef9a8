﻿using XH.H82.Models.EquipmengtClassNew;

namespace XH.H82.IServices.EquipmentClassNew;

public interface IEquipomentArchivesService
{
    /// <summary>
    /// 新增档案记录
    /// </summary>
    /// <param name="archivesDto"></param>
    /// <returns></returns>
    EMS_EQP_ARCHIVES_DICT AddEquipmentArchives(AddEquipmentArchivesDto archivesDto);
    
    /// <summary>
    /// 更新档案记录
    /// </summary>
    /// <param name="archivesId"></param>
    /// <param name="archivesDto"></param>
    /// <returns></returns>
    EMS_EQP_ARCHIVES_DICT UpdateEquipmentArchives(string archivesId, AddEquipmentArchivesDto archivesDto);


    /// <summary>
    /// 删除档案记录
    /// </summary>
    /// <param name="archivesId"></param>
    /// <returns></returns>
    bool DeleteEquipmentArchives(string archivesId);

    /// <summary>
    /// 禁用启用档案记录
    /// </summary>
    /// <param name="archivesId"></param>
    /// <returns></returns>
    bool EnableOrDisableEquipmentArchives(string archivesId);
}