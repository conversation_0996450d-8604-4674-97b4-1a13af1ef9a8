﻿using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using iTextSharp.text;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NPOI.HSSF.Record.AutoFilter;
using NPOI.POIFS.Crypt.Dsig;
using NuGet.Packaging;
using RestSharp;
using Serilog;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XH.H82.IServices;
using XH.H82.IServices.EquipmentOnlyOffice;
using XH.H82.Models.BusinessModuleClient.Dto;
using XH.H82.Models.DeviceRelevantInformation;
using XH.H82.Models.Dtos.FileTemplate;
using XH.H82.Models.Entities;
using XH.H82.Models.Entities.Common;
using XH.H82.Models.Entities.FileTemplate;
using XH.H82.Models.Entities.OperationLog;
using XH.H82.Models.SugarDbContext;
using XH.H82.Services.DeviceDataRefresh;
using XH.LAB.UTILS.Helpers;
using XH.LAB.UTILS.Models;
using static iTextSharp.text.pdf.AcroFields;
using static XH.H82.Models.Entities.FileTemplate.EquipmentInfoData;

namespace XH.H82.Services.EquipmentOnlyOffice
{
    public class EquipmentOnlyOfficeService : IEquipmentOnlyOfficeService
    {
        private readonly IHttpContextAccessor _httpContext;
        private readonly RestClient _clientH115;
        private readonly IConfiguration _configuration;
        private readonly ILogger<EquipmentOnlyOfficeService> _logger;
        private readonly ISqlSugarUow<SugarDbContext_Master> _dbContext;
        private readonly IBaseService _baseService;
        private readonly IEquipmentDocService _equipmentDocService;
        private readonly ICurrencyService _currencyService;
        private readonly IncludesConfig _includesConfig;

        private readonly EquipmentContext equipmentContext;
        private string addressH115 { get; set; } = "";
        public List<OA_FIELD_DICT> _allNeedDataNodes { get; set; } = new List<OA_FIELD_DICT>();
        private string _nodesFilters { get; set; } = string.Empty;

        public EquipmentOnlyOfficeService(IHttpContextAccessor httpContext, ILogger<EquipmentOnlyOfficeService> logger, ISqlSugarUow<SugarDbContext_Master> dbContext, IBaseService baseService, IEquipmentDocService equipmentDocService, ICurrencyService currencyService, IConfiguration configuration)
        {
            _includesConfig = IncludesConfig.InitFalse();
            addressH115 = configuration["H115"];
            _httpContext = httpContext;
            _logger = logger;
            _dbContext = dbContext;
            _baseService = baseService;
            _equipmentDocService = equipmentDocService;
            _currencyService = currencyService;
            if (addressH115.IsNotNullOrEmpty())
            {
                _clientH115 = new RestClient(new RestClientOptions()
                {
                    RemoteCertificateValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true,
                    BaseUrl = new Uri(addressH115),
                    ThrowOnAnyError = true
                });
            }

            LoadNodes();
            _configuration = configuration;
            equipmentContext = new(_dbContext);

        }

        /// <summary>
        /// 调用H115获取导出文件
        /// </summary>
        /// <param name="templateFillDataDto"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public byte[] ExportStyleFile(StyleTemplateFillDataDto templateFillDataDto)
        {
            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();
            Stopwatch sw = new Stopwatch();
            string url = $"api/OO/ExportStyleFile";
            sw.Start();
            RestRequest request = new RestRequest(url);
            //request.AddHeader("Content-Type", "application/octet-stream");
            request.AddHeader("Authorization", token);
            request.AddBody(templateFillDataDto);
            try
            {
                var response = _clientH115.ExecutePost(request);

                sw.Stop();
                Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用H115模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
                if (response.ErrorException != null)
                {
                    Log.Error($"调用H115模块[{url}]发生错误:{response.ErrorException}");
                    throw new BizException($"调用H115模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
                }
                else
                {
                    var bytes = response.RawBytes;
                    return bytes;
                }
            }
            catch (Exception ex)
            {
                Log.Error($"调用H115模块[{url}]发生错误:{ex}");
                throw new BizException("导出失败!");
            }
        }

        /// <summary>
        /// 调用H115获取导出pdf文件
        /// </summary>
        /// <param name="templateFillDataDto"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public byte[] ExportStylePDFFile(StyleTemplateFillDataDto templateFillDataDto)
        {
            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();
            Stopwatch sw = new Stopwatch();
            string url = $"api/OO/ExportStylePDFFile";
            sw.Start();
            RestRequest request = new RestRequest(url);
            //request.AddHeader("Content-Type", "application/octet-stream");
            request.AddHeader("Authorization", token);
            request.AddBody(templateFillDataDto);
            try
            {
                var response = _clientH115.ExecutePost(request);

                sw.Stop();
                Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用H115模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
                if (response.ErrorException != null)
                {
                    Log.Error($"调用H115模块[{url}]发生错误:{response.ErrorException}");
                    throw new BizException($"调用H115模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
                }
                else
                {
                    var bytes = response.RawBytes;
                    return bytes;
                }
            }
            catch (Exception ex)
            {
                Log.Error($"调用H115模块[{url}]发生错误:{ex}");
                throw new BizException("导出失败!");
            }
        }

        /// <summary>
        /// 调用H115获取预览文件
        /// </summary>
        /// <param name="templateFillDataDto"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public byte[] PreviewFile(StyleTemplateFillDataDto templateFillDataDto)
        {
            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();
            Stopwatch sw = new Stopwatch();
            string url = $"api/OO/PreviewFile";
            sw.Start();
            RestRequest request = new RestRequest(url);
            //request.AddHeader("Content-Type", "application/octet-stream");
            request.AddHeader("Authorization", token);
            request.AddBody(templateFillDataDto);
            try
            {
                var response = _clientH115.ExecutePost(request);

                sw.Stop();
                Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用H115模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
                if (response.ErrorException != null)
                {
                    Log.Error($"调用H115模块[{url}]发生错误:{response.ErrorException}");
                    throw new BizException($"调用H115模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
                }
                else
                {
                    var bytes = response.RawBytes;
                    return bytes;
                }
            }
            catch (Exception ex)
            {
                Log.Error($"调用H115模块[{url}]发生错误:{ex}");
                throw new BizException("预览失败!");
            }
        }

        public ResultDto LoadExcelData(OaExcelFillDataDto requestBody)
        {
            Log.Information("LoadExcelData");
            Log.Information(JsonConvert.SerializeObject(requestBody));
            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();
            Stopwatch sw = new Stopwatch();
            string url = $"api/OO/LoadExcelData";
            sw.Start();
            RestRequest request = new RestRequest(url);
            //request.AddHeader("Content-Type", "application/octet-stream");
            request.AddHeader("Authorization", token);
            request.AddJsonBody(requestBody);
            try
            {
                var response = _clientH115.ExecutePost<ResultDto>(request);
                sw.Stop();
                Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用H115模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
                if (response.ErrorException != null)
                {
                    Log.Error($"调用H115模块[{url}]发生错误:{response.ErrorException}");
                    throw new BizException($"调用H115模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
                }
                else
                {
                    return response.Data;
                }
            }
            catch (Exception ex)
            {
                Log.Error($"调用H115模块[{url}]发生错误:{ex}");
                throw new BizException("预览失败!");
            }
        }

        public StyleTemplateFillDataDto FillEquipmentInfo(string styleId, EquipmentInfoData data)
        {
            var filter = _dbContext.Db.Queryable<OA_OFFICE_STYLE_TEMPLATE>().Where(x => x.STYLE_ID == styleId).First();
            if (filter is not null)
            {
                _nodesFilters = filter.CLASS_COL_JSON;
            }

            var result = new StyleTemplateFillDataDto() { STYLE_ID = styleId, DATAS = new List<StyleTemplateClassDataDto>() };

            result.DATAS.Add(FillWordText("eq", data));
            if(_includesConfig.IsxistsConfig(data.WeekWorkPlans.ClassCode)) result.DATAS.Add(FillWordText(data.WeekWorkPlans.ClassCode, data.WeekWorkPlans.Records[0]));
            if(_includesConfig.IsxistsConfig(data.MonthWorkPlans.ClassCode)) result.DATAS.Add(FillWordText(data.MonthWorkPlans.ClassCode, data.MonthWorkPlans.Records[0]));
            if(_includesConfig.IsxistsConfig(data.YearWorkPlans.ClassCode)) result.DATAS.Add(FillWordText(data.YearWorkPlans.ClassCode, data.YearWorkPlans.Records[0]));
            if(_includesConfig.IsxistsConfig(data.RunRecords.ClassCode)) result.DATAS.Add(FillWordText(data.RunRecords.ClassCode, data.RunRecords.Records[0]));
            if(_includesConfig.IsxistsConfig(data.Environments.ClassCode)) result.DATAS.Add(FillWordText(data.Environments.ClassCode, data.Environments.Records[0]));
            if(_includesConfig.IsxistsConfig(data.Dealers.ClassCode)) result.DATAS.Add(FillWordText(data.Dealers.ClassCode, data.Dealers.Records[0]));
            if(_includesConfig.IsxistsConfig(data.Manufacturers.ClassCode)) result.DATAS.Add(FillWordText(data.Manufacturers.ClassCode, data.Manufacturers.Records[0]));
            if(_includesConfig.IsxistsConfig(data.Subscriptions.ClassCode)) result.DATAS.Add(FillWordText(data.Subscriptions.ClassCode, data.Subscriptions.Records[0]));
            if(_includesConfig.IsxistsConfig(data.TrainingRecords.ClassCode)) result.DATAS.Add(FillWordText(data.TrainingRecords.ClassCode, data.TrainingRecords.Records[0]));
            if(_includesConfig.IsxistsConfig(data.Procurements.ClassCode)) result.DATAS.Add(FillWordText(data.Procurements.ClassCode, data.Procurements.Records[0]));
            if(_includesConfig.IsxistsConfig(data.Startupinfos.ClassCode)) result.DATAS.Add(FillWordText(data.Startupinfos.ClassCode, data.Startupinfos.Records[0]));
            if(_includesConfig.IsxistsConfig(data.PlanRecords.ClassCode)) result.DATAS.Add(FillWordText(data.PlanRecords.ClassCode, data.PlanRecords.Records[0]));
            if(_includesConfig.IsxistsConfig(data.OpenRecords.ClassCode)) result.DATAS.Add(FillWordText(data.OpenRecords.ClassCode, data.OpenRecords.Records[0]));
            if(_includesConfig.IsxistsConfig(data.OpenPartRecords.ClassCode)) result.DATAS.Add(FillWordText(data.OpenPartRecords.ClassCode, data.OpenPartRecords.Records[0]));
            if(_includesConfig.IsxistsConfig(data.InstallRecords.ClassCode)) result.DATAS.Add(FillWordText(data.InstallRecords.ClassCode, data.InstallRecords.Records[0]));
            if(_includesConfig.IsxistsConfig(data.CorrectReocrds.ClassCode)) result.DATAS.Add(FillWordText(data.CorrectReocrds.ClassCode, data.CorrectReocrds.Records[0]));
            if(_includesConfig.IsxistsConfig(data.ComparisonReocrds.ClassCode)) result.DATAS.Add(FillWordText(data.ComparisonReocrds.ClassCode, data.ComparisonReocrds.Records[0]));
            if(_includesConfig.IsxistsConfig(data.DebugeRecords.ClassCode)) result.DATAS.Add(FillWordText(data.DebugeRecords.ClassCode, data.DebugeRecords.Records[0]));
            if(_includesConfig.IsxistsConfig(data.AuthRecords.ClassCode)) result.DATAS.Add(FillWordText(data.AuthRecords.ClassCode, data.AuthRecords.Records[0]));
            if(_includesConfig.IsxistsConfig(data.Appearances.ClassCode)) result.DATAS.Add(FillWordText(data.Appearances.ClassCode, data.Appearances.Records[0]));
            if(_includesConfig.IsxistsConfig(data.AcceptanceReports.ClassCode)) result.DATAS.Add(FillWordText(data.AcceptanceReports.ClassCode, data.AcceptanceReports.Records[0]));
            if(_includesConfig.IsxistsConfig(data.MaintenanceRecords.ClassCode)) result.DATAS.Add(FillWordText(data.MaintenanceRecords.ClassCode, data.MaintenanceRecords.Records[0]));
            if(_includesConfig.IsxistsConfig(data.VerificationRecords.ClassCode)) result.DATAS.Add(FillWordText(data.VerificationRecords.ClassCode, data.VerificationRecords.Records[0]));
            if(_includesConfig.IsxistsConfig(data.FixRecords.ClassCode)) result.DATAS.Add(FillWordText(data.FixRecords.ClassCode, data.FixRecords.Records[0]));

            if(_includesConfig.IsxistsConfig(data.WeekWorkPlans.ClassCode)) result.DATAS.Add(FillWordRecord(data.WeekWorkPlans));
            if(_includesConfig.IsxistsConfig(data.MonthWorkPlans.ClassCode)) result.DATAS.Add(FillWordRecord(data.MonthWorkPlans));
            if(_includesConfig.IsxistsConfig(data.YearWorkPlans.ClassCode)) result.DATAS.Add(FillWordRecord(data.YearWorkPlans));
            if(_includesConfig.IsxistsConfig(data.RunRecords.ClassCode)) result.DATAS.Add(FillWordRecord(data.RunRecords));
            if(_includesConfig.IsxistsConfig(data.Environments.ClassCode)) result.DATAS.Add(FillWordRecord(data.Environments));
            if(_includesConfig.IsxistsConfig(data.Dealers.ClassCode)) result.DATAS.Add(FillWordRecord(data.Dealers));
            if(_includesConfig.IsxistsConfig(data.Manufacturers.ClassCode)) result.DATAS.Add(FillWordRecord(data.Manufacturers));
            if(_includesConfig.IsxistsConfig(data.Subscriptions.ClassCode)) result.DATAS.Add(FillWordRecord(data.Subscriptions));
            if(_includesConfig.IsxistsConfig(data.TrainingRecords.ClassCode)) result.DATAS.Add(FillWordRecord(data.TrainingRecords));
            if(_includesConfig.IsxistsConfig(data.Procurements.ClassCode)) result.DATAS.Add(FillWordRecord(data.Procurements));
            if(_includesConfig.IsxistsConfig(data.Startupinfos.ClassCode)) result.DATAS.Add(FillWordRecord(data.Startupinfos));
            if(_includesConfig.IsxistsConfig(data.PlanRecords.ClassCode)) result.DATAS.Add(FillWordRecord(data.PlanRecords));
            if(_includesConfig.IsxistsConfig(data.OpenRecords.ClassCode)) result.DATAS.Add(FillWordRecord(data.OpenRecords));
            if(_includesConfig.IsxistsConfig(data.OpenPartRecords.ClassCode)) result.DATAS.Add(FillWordRecord(data.OpenPartRecords));
            if(_includesConfig.IsxistsConfig(data.InstallRecords.ClassCode)) result.DATAS.Add(FillWordRecord(data.InstallRecords));
            if(_includesConfig.IsxistsConfig(data.CorrectReocrds.ClassCode)) result.DATAS.Add(FillWordRecord(data.CorrectReocrds));
            if(_includesConfig.IsxistsConfig(data.ComparisonReocrds.ClassCode)) result.DATAS.Add(FillWordRecord(data.ComparisonReocrds));
            if(_includesConfig.IsxistsConfig(data.DebugeRecords.ClassCode)) result.DATAS.Add(FillWordRecord(data.DebugeRecords));
            if(_includesConfig.IsxistsConfig(data.AuthRecords.ClassCode)) result.DATAS.Add(FillWordRecord(data.AuthRecords));
            if(_includesConfig.IsxistsConfig(data.Appearances.ClassCode)) result.DATAS.Add(FillWordRecord(data.Appearances));
            if(_includesConfig.IsxistsConfig(data.AcceptanceReports.ClassCode)) result.DATAS.Add(FillWordRecord(data.AcceptanceReports));
            if(_includesConfig.IsxistsConfig(data.MaintenanceRecords.ClassCode)) result.DATAS.Add(FillWordRecord(data.MaintenanceRecords));
            if(_includesConfig.IsxistsConfig(data.VerificationRecords.ClassCode)) result.DATAS.Add(FillWordRecord(data.VerificationRecords));
            if(_includesConfig.IsxistsConfig(data.FixRecords.ClassCode)) result.DATAS.Add(FillWordRecord(data.FixRecords));

            return result;
        }

        
        public StyleTemplateFillDataDto FillEquipmentTextAndRecords(string styleId, EquipmentInfoData data)
        {
            var filter = _dbContext.Db.Queryable<OA_OFFICE_STYLE_TEMPLATE>().Where(x => x.STYLE_ID == styleId).First();
            if (filter is not null)
            {
                _nodesFilters = filter.CLASS_COL_JSON;
            }
            var result = new StyleTemplateFillDataDto() { STYLE_ID = styleId, DATAS = new List<StyleTemplateClassDataDto>() };
            result.DATAS.Add(FillWordText("eq", data));
            if(_includesConfig.IsxistsConfig(data.WeekWorkPlans.ClassCode)) result.DATAS.Add(FillWordTextAndRecord(data.WeekWorkPlans));
            if(_includesConfig.IsxistsConfig(data.MonthWorkPlans.ClassCode)) result.DATAS.Add(FillWordTextAndRecord(data.MonthWorkPlans));
            if(_includesConfig.IsxistsConfig(data.YearWorkPlans.ClassCode)) result.DATAS.Add(FillWordTextAndRecord(data.YearWorkPlans));
            if(_includesConfig.IsxistsConfig(data.RunRecords.ClassCode)) result.DATAS.Add(FillWordTextAndRecord(data.RunRecords));
            if(_includesConfig.IsxistsConfig(data.Environments.ClassCode)) result.DATAS.Add(FillWordTextAndRecord(data.Environments));
            if(_includesConfig.IsxistsConfig(data.Dealers.ClassCode)) result.DATAS.Add(FillWordTextAndRecord(data.Dealers));
            if(_includesConfig.IsxistsConfig(data.Manufacturers.ClassCode)) result.DATAS.Add(FillWordTextAndRecord(data.Manufacturers));
            if(_includesConfig.IsxistsConfig(data.Subscriptions.ClassCode)) result.DATAS.Add(FillWordTextAndRecord(data.Subscriptions));
            if(_includesConfig.IsxistsConfig(data.TrainingRecords.ClassCode)) result.DATAS.Add(FillWordTextAndRecord(data.TrainingRecords));
            if(_includesConfig.IsxistsConfig(data.Procurements.ClassCode)) result.DATAS.Add(FillWordTextAndRecord(data.Procurements));
            if(_includesConfig.IsxistsConfig(data.Startupinfos.ClassCode)) result.DATAS.Add(FillWordTextAndRecord(data.Startupinfos));
            if(_includesConfig.IsxistsConfig(data.PlanRecords.ClassCode)) result.DATAS.Add(FillWordTextAndRecord(data.PlanRecords));
            if(_includesConfig.IsxistsConfig(data.OpenRecords.ClassCode)) result.DATAS.Add(FillWordTextAndRecord(data.OpenRecords));
            if(_includesConfig.IsxistsConfig(data.OpenPartRecords.ClassCode)) result.DATAS.Add(FillWordTextAndRecord(data.OpenPartRecords));
            if(_includesConfig.IsxistsConfig(data.InstallRecords.ClassCode)) result.DATAS.Add(FillWordTextAndRecord(data.InstallRecords));
            if(_includesConfig.IsxistsConfig(data.CorrectReocrds.ClassCode)) result.DATAS.Add(FillWordTextAndRecord(data.CorrectReocrds));
            if(_includesConfig.IsxistsConfig(data.ComparisonReocrds.ClassCode)) result.DATAS.Add(FillWordTextAndRecord(data.ComparisonReocrds));
            if(_includesConfig.IsxistsConfig(data.DebugeRecords.ClassCode)) result.DATAS.Add(FillWordTextAndRecord(data.DebugeRecords));
            if(_includesConfig.IsxistsConfig(data.AuthRecords.ClassCode)) result.DATAS.Add(FillWordTextAndRecord(data.AuthRecords));
            if(_includesConfig.IsxistsConfig(data.Appearances.ClassCode)) result.DATAS.Add(FillWordTextAndRecord(data.Appearances));
            if(_includesConfig.IsxistsConfig(data.AcceptanceReports.ClassCode)) result.DATAS.Add(FillWordTextAndRecord(data.AcceptanceReports));
            if(_includesConfig.IsxistsConfig(data.MaintenanceRecords.ClassCode)) result.DATAS.Add(FillWordTextAndRecord(data.MaintenanceRecords));
            if(_includesConfig.IsxistsConfig(data.VerificationRecords.ClassCode)) result.DATAS.Add(FillWordTextAndRecord(data.VerificationRecords));
            if(_includesConfig.IsxistsConfig(data.FixRecords.ClassCode)) result.DATAS.Add(FillWordTextAndRecord(data.FixRecords));
            return result;
        }



        public List<EquipmentInfoData> FillEquipmentInfoDatas(string styleId, List<string> ids , bool isExcel = false)
        {

            var result = new List<EquipmentInfoData>();
            var equipmentBaseInfos = FindBaseEquipmentInfo(styleId, ids ,isExcel);

            foreach (var equipment in equipmentBaseInfos)
            {
                equipmentContext.CalculateEquipmentWorkPlanNextTime(equipment);

                var emsStartOrStopRecord = equipment.eMS_START_STOP.OrderBy(x => x.OPER_TIME)
                     .Where(p => p.START_STOP_STATE != "2")
                     .Where(p => p.START_CAUSE != "首次启用")
                     .FirstOrDefault(p => p.EQUIPMENT_ID == equipment.EQUIPMENT_ID);
                equipment.EARLIEST_ENABLE_DATE = emsStartOrStopRecord is null ? null : emsStartOrStopRecord.OPER_TIME;
                equipment.HOSPITAL_NAME = equipmentContext.ExchangeHosptailName(equipment.HOSPITAL_ID);
                equipment.LAB_NAME = equipmentContext.ExchangeLabName(equipment.LAB_ID);
                equipment.EQUIPMENT_STATE = equipmentContext.ExchangeEquipmentState(equipment.EQUIPMENT_STATE);
                equipment.UNIT_ID = equipmentContext.ExchangeProfessionalGroupName(equipment.UNIT_ID);
                equipment.EQUIPMENT_CLASS = equipmentContext.ExchangeEquipmentClass(equipment.EQUIPMENT_CLASS, equipment.EQUIPMENT_TYPE);
                equipment.PROFESSIONAL_CLASS = equipmentContext.ExchangeProfessionalClass(equipment.PROFESSIONAL_CLASS);
            }
            var archivesInfos = JsonConvert.DeserializeObject<List<EquipmentInfoData>>(JsonConvert.SerializeObject(equipmentBaseInfos));
            if (archivesInfos is not null)
            {
                result = archivesInfos;
            }

            foreach (var archivesInfo in archivesInfos)
            {
                var equipment = equipmentBaseInfos.FirstOrDefault(x => x.EQUIPMENT_ID == archivesInfo.EQUIPMENT_ID);
                archivesInfo.EQUIPMENT_ARCHIVE = equipment.EQUIPMENT_UCODE;
                
                if(_includesConfig.IsxistsConfig(archivesInfo.RunRecords.ClassCode)) RunRecordExchange(archivesInfo.RunRecords, equipment);
                if(_includesConfig.IsxistsConfig(archivesInfo.WeekWorkPlans.ClassCode))WordPlandExchange(archivesInfo.WeekWorkPlans, archivesInfo.MonthWorkPlans, archivesInfo.YearWorkPlans, equipment);
                if(_includesConfig.IsxistsConfig(archivesInfo.Environments.ClassCode))EnvironmentExchange(archivesInfo.Environments, equipment);
                if(_includesConfig.IsxistsConfig(archivesInfo.Dealers.ClassCode)) DealersExchange(archivesInfo.Dealers, equipment);
                if(_includesConfig.IsxistsConfig(archivesInfo.Manufacturers.ClassCode)) ManufacturerExchange(archivesInfo.Manufacturers, equipment);
                if(_includesConfig.IsxistsConfig(archivesInfo.Subscriptions.ClassCode)) SubscriptionsExchange(archivesInfo.Subscriptions, equipment);
                if(_includesConfig.IsxistsConfig(archivesInfo.TrainingRecords.ClassCode)) TrainingRecordsExchange(archivesInfo.TrainingRecords, equipment);
                if(_includesConfig.IsxistsConfig(archivesInfo.Procurements.ClassCode)) ProcurementsExchange(archivesInfo.Procurements, equipment);
                if(_includesConfig.IsxistsConfig(archivesInfo.Startupinfos.ClassCode)) StartupinfosExchange(archivesInfo.Startupinfos, equipment);
                if(_includesConfig.IsxistsConfig(archivesInfo.PlanRecords.ClassCode)) PlanRecordsExchange(archivesInfo.PlanRecords, equipment);
                if(_includesConfig.IsxistsConfig(archivesInfo.OpenRecords.ClassCode)) OpenRecordsExchange(archivesInfo.OpenRecords, equipment);
                if(_includesConfig.IsxistsConfig(archivesInfo.OpenPartRecords.ClassCode)) OpenPartRecordsExchange(archivesInfo.OpenPartRecords, equipment);
                if(_includesConfig.IsxistsConfig(archivesInfo.InstallRecords.ClassCode))InstallRecordsExchange(archivesInfo.InstallRecords, equipment);
                if(_includesConfig.IsxistsConfig(archivesInfo.CorrectReocrds.ClassCode)) CorrectReocrdsExchange(archivesInfo.CorrectReocrds, equipment);
                if(_includesConfig.IsxistsConfig(archivesInfo.ComparisonReocrds.ClassCode)) ComparisonReocrdsExchange(archivesInfo.ComparisonReocrds, equipment);
                if(_includesConfig.IsxistsConfig(archivesInfo.DebugeRecords.ClassCode)) DebugeRecordsExchange(archivesInfo.DebugeRecords, equipment);
                if(_includesConfig.IsxistsConfig(archivesInfo.AuthRecords.ClassCode)) AuthRecordsExchange(archivesInfo.AuthRecords, equipment);
                if(_includesConfig.IsxistsConfig(archivesInfo.AcceptanceReports.ClassCode)) AcceptanceReportsExchange(archivesInfo.AcceptanceReports, equipment);
                if(_includesConfig.IsxistsConfig(archivesInfo.Appearances.ClassCode))AppearancesExchange(archivesInfo.Appearances, equipment);
                if(_includesConfig.IsxistsConfig(archivesInfo.MaintenanceRecords.ClassCode)) MaintenanceRecordsExchange(archivesInfo.MaintenanceRecords, equipment);
                if(_includesConfig.IsxistsConfig(archivesInfo.VerificationRecords.ClassCode))VerificationRecordsExchange(archivesInfo.VerificationRecords, equipment);
                if(_includesConfig.IsxistsConfig(archivesInfo.FixRecords.ClassCode)) FixRecordExchange(archivesInfo.FixRecords, equipment);
            }
            return result;
        }

        private List<EMS_EQUIPMENT_INFO> FindBaseEquipmentInfo(string styleId, List<string> equipmentIds , bool isExcel = false)
        {
            var start = DateTime.Now;
            var equipments = new List<EMS_EQUIPMENT_INFO>();
            var query = _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>()
                        .Where(p => equipmentIds.Contains(p.EQUIPMENT_ID));
            InclusdesFilter(styleId, query , isExcel)
                        .OrderBy(i => i.DEPT_SECTION_NO)
                        .ForEach(i => equipments.Add(i), 200); //每次查询200条

            var end = DateTime.Now;
            Console.WriteLine((end - start).TotalSeconds);
            return equipments;
        }

        private ISugarQueryable<EMS_EQUIPMENT_INFO> InclusdesFilter(string styleId, ISugarQueryable<EMS_EQUIPMENT_INFO> query, bool isExcel = false)
        {
            var config = new IncludesConfig();

            if (isExcel)
            {
                var excelStyleTemplate = _dbContext.Db.Queryable<OA_EXCEL_STYLE_TEMPLATE>().Where(x => x.STYLE_ID == styleId).First();
                if (excelStyleTemplate is not null)
                {
                    var classCols = JObject.Parse(excelStyleTemplate.ITEM_SETUP_JSON);
                    foreach (var classCol in classCols)
                    {
                        config.SetIncludes(classCol.Key);
                        _includesConfig.SetIncludes(classCol.Key);
                    }
                }
                else
                {
                    query.Includes(i => i.eMS_PURCHASE_INFO)
                        .Includes(i => i.eMS_INSTALL_INFO)
                        .Includes(i => i.eMS_COMPARISON_INFO)
                        .Includes(i => i.eMS_CORRECT_INFO)
                        .Includes(i => i.eMS_MAINTAIN_INFO)
                        .Includes(i => i.eMS_VERIFICATION_INFO)
                        .Includes(i => i.eMS_AUTHORIZE_INFO)
                        .Includes(i => i.eMS_CHANGE_INFO)
                        .Includes(i => i.eMS_DEBUG_INFO)
                        .Includes(i => i.eMS_PARTS_INFO)
                        .Includes(i => i.eMS_REPAIR_INFO)
                        .Includes(i => i.eMS_SCRAP_INFO)
                        .Includes(i => i.eMS_START_STOP)
                        .Includes(i => i.eMS_STARTUP_INFO)
                        .Includes(i => i.eMS_TRAIN_INFO)
                        .Includes(i => i.eMS_UNPACK_INFO)
                        .Includes(i => i.eMS_WORK_PLAN)
                        .Includes(i => i.eMS_ENVI_REQUIRE_INFO)
                        .Includes(i => i.Contacts);
                    return query;
                }
            }
            else
            {
                var officeStyleTemplate = _dbContext.Db.Queryable<OA_OFFICE_STYLE_TEMPLATE>().Where(x => x.STYLE_ID == styleId).First();
                if (officeStyleTemplate.CLASS_COL_JSON is not null)
                {
                    var classCols = JObject.Parse(officeStyleTemplate.CLASS_COL_JSON);
                    foreach (var classCol in classCols)
                    {
                        config.SetIncludes(classCol.Key);
                        _includesConfig.SetIncludes(classCol.Key);
                    }
                }
                else
                {
                    query.Includes(i => i.eMS_PURCHASE_INFO)
                        .Includes(i => i.eMS_INSTALL_INFO)
                        .Includes(i => i.eMS_COMPARISON_INFO)
                        .Includes(i => i.eMS_CORRECT_INFO)
                        .Includes(i => i.eMS_MAINTAIN_INFO)
                        .Includes(i => i.eMS_VERIFICATION_INFO)
                        .Includes(i => i.eMS_AUTHORIZE_INFO)
                        .Includes(i => i.eMS_CHANGE_INFO)
                        .Includes(i => i.eMS_DEBUG_INFO)
                        .Includes(i => i.eMS_PARTS_INFO)
                        .Includes(i => i.eMS_REPAIR_INFO)
                        .Includes(i => i.eMS_SCRAP_INFO)
                        .Includes(i => i.eMS_START_STOP)
                        .Includes(i => i.eMS_STARTUP_INFO)
                        .Includes(i => i.eMS_TRAIN_INFO)
                        .Includes(i => i.eMS_UNPACK_INFO)
                        .Includes(i => i.eMS_WORK_PLAN)
                        .Includes(i => i.eMS_ENVI_REQUIRE_INFO)
                        .Includes(i => i.Contacts);
                    return query;
                }
            }
            if (config.INCLUDE_PURCHASE_INFO)
            {
                query = query.Includes(x => x.eMS_PURCHASE_INFO);
            }
            if (config.INCLUDE_INSTALL_INFO)
            {
                query = query.Includes(x => x.eMS_INSTALL_INFO);
            }
            if (config.INCLUDE_COMPARISON_INFO)
            {
                query = query.Includes(x => x.eMS_COMPARISON_INFO);
            }
            if (config.INCLUDE_CORRECT_INFO)
            {
                query = query.Includes(x => x.eMS_CORRECT_INFO);
            }
            if (config.INCLUDE_MAINTAIN_INFO)
            {
                query = query.Includes(x => x.eMS_MAINTAIN_INFO);
            }
            if (config.INCLUDE_VERIFICATION_INFO)
            {
                query = query.Includes(x => x.eMS_VERIFICATION_INFO);
            }
            if (config.INCLUDE_AUTHORIZE_INFO)
            {
                query = query.Includes(x => x.eMS_AUTHORIZE_INFO);
            }
            if (config.INCLUDE_CHANGE_INFO)
            {
                query = query.Includes(x => x.eMS_CHANGE_INFO);
            }
            if (config.INCLUDE_DEBUG_INFO)
            {
                query = query.Includes(x => x.eMS_DEBUG_INFO);
            }
            if (config.INCLUDE_PARTS_INFO)
            {
                query = query.Includes(x => x.eMS_PARTS_INFO);
            }
            if (config.INCLUDE_REPAIR_INFO)
            {
                query = query.Includes(x => x.eMS_REPAIR_INFO);
            }
            if (config.INCLUDE_SCRAP_INFO)
            {
                query = query.Includes(x => x.eMS_SCRAP_INFO);
            }
            if (config.INCLUDE_START_STOP)
            {
                query = query.Includes(x => x.eMS_START_STOP);
            }
            if (config.INCLUDE_STARTUP_INFO)
            {
                query = query.Includes(x => x.eMS_STARTUP_INFO);
            }
            if (config.INCLUDE_TRAIN_INFO)
            {
                query = query.Includes(x => x.eMS_TRAIN_INFO);
            }
            if (config.INCLUDE_UNPACK_INFO)
            {
                query = query.Includes(x => x.eMS_UNPACK_INFO);
            }
            if (config.INCLUDE_WORK_PLAN)
            {
                query = query.Includes(x => x.eMS_WORK_PLAN);
            }
            if (config.INCLUDE_ENVI_REQUIRE_INFO)
            {
                query = query.Includes(x => x.eMS_ENVI_REQUIRE_INFO);
            }
            if (config.INCLUDE_CONTACTS_INFO)
            {
                query = query.Includes(x => x.Contacts);
            }
            return query;
        }
        
        private StyleTemplateClassDataDto FillWordTextAndRecord<T>(EquipmentInfoRecord<T> t) where T : class, new()
        {
            var result = new StyleTemplateClassDataDto()
            {
                CLASSE_CODE = t.ClassCode
            };
            FillTextNode(result, t.ClassCode,t.Records[0]);
            FillRecordNode(result, t.ClassCode,t.Records);
            return result;
        }

        private StyleTemplateClassDataDto FillWordRecord<T>(EquipmentInfoRecord<T> t) where T : class, new()
        {
            return FillRecordNode(t.ClassCode, t.Records);
        }

        private StyleTemplateClassDataDto FillWordText<T>(string key, T t)
        {
            return FillTextNode(key, t);
        }

        private void SortTheList<T>(List<T>? list, string sortProp = "NO")
        {
            if (list is null)
            {
                return;
            }
            for (int i = 1; i <= list.Count(); i++)
            {
                var prop = list[i - 1].GetType().GetProperty(sortProp);
                if (prop is not null)
                {
                    prop.SetValue(list[i - 1], i);
                }
            }
        }

        private void FillTextNode<T>(StyleTemplateClassDataDto result, string fromDataNode, T t)
        {
            if (t is null)
            {
                return;
            }
            var properties = t.GetType().GetProperties();
            var fromNode = FindDataNodes(fromDataNode);
            foreach (var node in fromNode)
            {
                var propertie = properties.Where(x => x.Name == node.FIELD_CODE).FirstOrDefault();
                if (propertie is not null)
                {
                    var value = propertie.GetValue(t);
                    if (value is not null)
                    {
                        if ("" != value.ToString())
                        {
                            result.FIELDS.Add(node.FIELD_CODE,value.ToString());
                        }
                    }
                }
               
            }

            return;
        }

        private void FillRecordNode<T>(StyleTemplateClassDataDto result,string recordDataNode, List<T> list)
        {
            SortTheList(list);
            if (list is null)
            {
                return;
            }
            if (list.Count == 0)
            {
                return;
            }
            var properties = typeof(T).GetProperties();
            var recordNode = FindDataNodes(recordDataNode);
            foreach (var item in list)
            {
                Dictionary<string, string> recordDic = new Dictionary<string, string>();
                foreach (var node in recordNode)
                {
                    var propertie = properties.Where(x => x.Name == node.FIELD_CODE).FirstOrDefault();
                    if (propertie is not null)
                    {
                        var value = propertie.GetValue(item);
                        if (value is  not null)
                        {
                            if ("" != value.ToString())
                            {
                                recordDic.Add(node.FIELD_CODE, value.ToString());
                            }
                        }
                        
                    }
                }
                if (recordDic.Any())
                {
                    result.ARRAYS.Add(recordDic);
                }
            }
            return ;
        }

        
        private StyleTemplateClassDataDto FillTextNode<T>(string fromDataNode, T t)
        {
            var result = new StyleTemplateClassDataDto() { CLASSE_CODE = fromDataNode };
            if (t is null)
            {
                return result;
            }
            var properties = t.GetType().GetProperties();
            var fromNode = FindDataNodes(fromDataNode);
            foreach (var node in fromNode)
            {
                var propertie = properties.Where(x => x.Name == node.FIELD_CODE).FirstOrDefault();
                if (propertie is not null)
                {
                    var value = propertie.GetValue(t);
                    if (value is not null)
                    {
                        if ("" != value.ToString())
                        {
                            result.FIELDS.Add(node.FIELD_CODE,value.ToString());
                        }
                    }
                }
                // else
                // {
                //     result.FIELDS.Add(node.FIELD_CODE, "");
                // }
            }
            return result;
        }

        private StyleTemplateClassDataDto FillRecordNode<T>(string recordDataNode, List<T> list)
        {
            SortTheList(list);
            var result = new StyleTemplateClassDataDto() { CLASSE_CODE = recordDataNode };
            if (list is null)
            {
                return result;
            }
            if (list.Count == 0)
            {
                return result;
            }
            var properties = typeof(T).GetProperties();
            var recordNode = FindDataNodes(recordDataNode);
            foreach (var item in list)
            {
                Dictionary<string, string> recordDic = new Dictionary<string, string>();
                foreach (var node in recordNode)
                {
                    var propertie = properties.Where(x => x.Name == node.FIELD_CODE).FirstOrDefault();
                    if (propertie is not null)
                    {
                        var value = propertie.GetValue(item);
                        if (value is  not null)
                        {
                            recordDic.Add(node.FIELD_CODE, value.ToString());
                            // if ("" != value.ToString())
                            // {
                            //     recordDic.Add(node.FIELD_CODE, value.ToString());
                            // }
                        }
                        else
                        {
                            recordDic.Add(node.FIELD_CODE, "");
                        }

                    }
                    // else
                    // {
                    //     recordDic.Add(node.FIELD_CODE, "");
                    // }
                }
                if (recordDic.Any())
                {
                    result.ARRAYS.Add(recordDic);
                }
            }

            return result;
        }

        
        
        private List<OA_FIELD_DICT> FindNeedDataNodes(string classeCode)
        {
            var nodes = _allNeedDataNodes.Where(x => x.CLASSE_CODE == classeCode).ToList();
            if (nodes is null)
            {
                LoadNodes();
                var result = _allNeedDataNodes.Where(x => x.CLASSE_CODE == classeCode).ToList();
                return result is null ? new List<OA_FIELD_DICT>() : result;
            }
            else
            {
                if (nodes.Count() == 0)
                {
                    LoadNodes();
                    var result = _allNeedDataNodes.Where(x => x.CLASSE_CODE == classeCode).ToList();
                    return result is null ? new List<OA_FIELD_DICT>() : result;
                }
                else
                {
                    return nodes.ToList();
                }
            }
        }

        private List<OA_FIELD_DICT> FindDataNodes(string classeCode)
        {
            Log.Information($"classeCode:{classeCode}");
            var result = FindNeedDataNodes(classeCode);
            Log.Information($"result:{JsonConvert.SerializeObject(result)}");
            Log.Information($"_nodesFilters:{JsonConvert.SerializeObject(_nodesFilters)}");
            if (_nodesFilters is not null)
            {
                result.RemoveAll(x => !_nodesFilters.Contains(x.FIELD_CODE));
            }
            return result;
        }

        private List<ClassData> FillExcelRecordsNode<T>(string recordDataNode, List<T> list, object? keyEntity, string key = null)
        {
            SortTheList(list);
            var result = new List<ClassData>();
            result.Add(FillExcelRecordNode(recordDataNode, list, keyEntity, key));

            //foreach (var item in list)
            //{
            //    result.Add(FillExcelRecordNode(recordDataNode, item, key));
            //}
            return result;
        }

        private ClassData FillExcelRecordNode<T>(string recordDataNode, List<T> list, object? keyEntity, string key = null)
        {
            var result = new ClassData() { CLASSE_CODE = recordDataNode };
            var properties = typeof(T).GetProperties();
            var recordNode = FindDataNodes(recordDataNode);
            foreach (var item in list)
            {
                Dictionary<string, string> recordDic = new Dictionary<string, string>();

                foreach (var node in recordNode)
                {
                    var propertie = properties.Where(x => x.Name == node.FIELD_CODE).FirstOrDefault();
                    if (propertie is not null)
                    {
                        recordDic.Add(node.FIELD_CODE, propertie.GetValue(item)?.ToString());
                    }
                }
                if (key.IsNotNullOrEmpty())
                {
                    var properties1 = keyEntity.GetType().GetProperties();
                    var propertie = properties1.Where(x => x.Name == key).FirstOrDefault();
                    if (propertie is not null)
                    {
                        recordDic.Add(key, propertie.GetValue(keyEntity)?.ToString());
                    }
                }
                result.ARRAYS.Add(recordDic);
            }
            return result;
        }


        private Dictionary<string, string> FillExcelDataNode<T>(string fromDataNode, T t, string key = null)
        {
            var result = new Dictionary<string, string>();
            if (t is null)
            {
                return result;
            }
            var properties = t.GetType().GetProperties();
            var fromNode = FindDataNodes(fromDataNode);
            foreach (var node in fromNode)
            {
                var propertie = properties.Where(x => x.Name == node.FIELD_CODE).FirstOrDefault();
                if (propertie is not null)
                {
                    result.Add(node.FIELD_CODE, propertie.GetValue(t)?.ToString());
                }
            }
            if (key.IsNotNullOrEmpty())
            {
                var propertie = properties.Where(x => x.Name == key).FirstOrDefault();
                if (propertie is not null)
                {
                    result.Add(key, propertie.GetValue(t)?.ToString());
                }
            }

            return result;
        }

        private void LoadNodes()
        {

            var dist = _dbContext.Db.Queryable<OA_FIELD_DICT>()
            .Where(w => w.MODULE_ID == "H82")
            .ToList();
            if (dist is not null)
            {
                Init(dist);
                _allNeedDataNodes.AddRange(dist);
            }
        }

        private void Init(List<OA_FIELD_DICT> dist)
        {
            var fixData = dist.Where(x => x.FIELD_CODE == "APPEARANCE" && x.CLASSE_CODE == "open").FirstOrDefault();
            if (fixData is not null)
            {
                fixData.CLASSE_CODE = "part";
                _dbContext.Db.Updateable(fixData).ExecuteCommand();
            }


            var fileDomians = dist.Where(x => x.FIELD_CODE == "APPEARANCE").Where(x => x.FIELD_DOMAIN == null).ToList();
            if (fileDomians.Count() > 0)
            {

                foreach (var fileDomian in fileDomians)
                {
                    fileDomian.FIELD_DOMAIN = "FILE";
                }
                _dbContext.Db.Updateable(fileDomians).ExecuteCommand();
            }

        }

        public StyleTemplateFillDataDto FillEquipmentInfos(string styleId, List<EquipmentInfoData> datas)
        {
            var filter = _dbContext.Db.Queryable<OA_OFFICE_STYLE_TEMPLATE>().Where(x => x.STYLE_ID == styleId).First();
            if (filter is not null)
            {
                _nodesFilters = filter.CLASS_COL_JSON;
            }

            var result = new StyleTemplateFillDataDto() { STYLE_ID = styleId, DATAS = new List<StyleTemplateClassDataDto>() };

            var equipmentInfos = new EquipmentInfoRecord<EquipmentInfoData>("eqinfo");
            var runRecords = new EquipmentInfoRecord<RunRecord>("run");
            var weekPlans = new EquipmentInfoRecord<WeekWorkPlan>("week");
            var monthWorkPlans = new EquipmentInfoRecord<MonthWorkPlan>("moon");
            var yearWorkPlans = new EquipmentInfoRecord<YearWorkPlan>("year");
            var evns = new EquipmentInfoRecord<EquipmentEnvironment>("evn");
            var dealers = new EquipmentInfoRecord<Dealer>("deal");
            var msanufacturers = new EquipmentInfoRecord<Manufacturer>("manu");
            var subscriptions = new EquipmentInfoRecord<SubscriptionRecord>("subs");
            var trainingRecords = new EquipmentInfoRecord<TrainingRecord>("train");
            var procurements = new EquipmentInfoRecord<Procurement>("purc");
            var startupinfos = new EquipmentInfoRecord<Startupinfo>("prop");
            var plans = new EquipmentInfoRecord<PlanRecord>("plan");
            var openRecords = new EquipmentInfoRecord<OpenRecord>("open");
            var openPartRecords = new EquipmentInfoRecord<OpenPartRecord>("part");
            var installRecords = new EquipmentInfoRecord<InstallRecord>("install");
            var correctReocrds = new EquipmentInfoRecord<CorrectReocrd>("corr");
            var comparisonReocrds = new EquipmentInfoRecord<ComparisonReocrd>("com");
            var debugeRecords = new EquipmentInfoRecord<DebugeRecord>("bug");
            var authRecords = new EquipmentInfoRecord<AuthRecord>("auth");
            var appearances = new EquipmentInfoRecord<Appearance>("appe");
            var acceptanceReports = new EquipmentInfoRecord<AcceptanceReport>("acce");
            var mainteRecords = new EquipmentInfoRecord<MaintenanceRecord>("mainte");
            var verificaeRecords = new EquipmentInfoRecord<Verification>("verificae");
            var fixRecords = new EquipmentInfoRecord<FixRecord>("fix");
            foreach (var data in datas)
            {
                var conut = 1;
                if(_includesConfig.IsxistsConfig(data.RunRecords.ClassCode)) conut = Math.Max(conut, data.RunRecords.Records.Count);
                if(_includesConfig.IsxistsConfig(data.WeekWorkPlans.ClassCode)) conut = Math.Max(conut, data.WeekWorkPlans.Records.Count);
                if(_includesConfig.IsxistsConfig(data.MonthWorkPlans.ClassCode)) conut = Math.Max(conut, data.MonthWorkPlans.Records.Count);
                if(_includesConfig.IsxistsConfig(data.Environments.ClassCode)) conut = Math.Max(conut, data.Environments.Records.Count);
                if(_includesConfig.IsxistsConfig(data.YearWorkPlans.ClassCode)) conut = Math.Max(conut, data.YearWorkPlans.Records.Count);
                if(_includesConfig.IsxistsConfig(data.Dealers.ClassCode)) conut = Math.Max(conut, data.Dealers.Records.Count);
                if(_includesConfig.IsxistsConfig(data.Manufacturers.ClassCode)) conut = Math.Max(conut, data.Manufacturers.Records.Count);
                if(_includesConfig.IsxistsConfig(data.Subscriptions.ClassCode)) conut = Math.Max(conut, data.Subscriptions.Records.Count);
                if(_includesConfig.IsxistsConfig(data.TrainingRecords.ClassCode)) conut = Math.Max(conut, data.TrainingRecords.Records.Count);
                if(_includesConfig.IsxistsConfig(data.Procurements.ClassCode)) conut = Math.Max(conut, data.Procurements.Records.Count);
                if(_includesConfig.IsxistsConfig(data.Startupinfos.ClassCode)) conut = Math.Max(conut, data.Startupinfos.Records.Count);
                if(_includesConfig.IsxistsConfig(data.PlanRecords.ClassCode)) conut = Math.Max(conut, data.PlanRecords.Records.Count);
                if(_includesConfig.IsxistsConfig(data.OpenRecords.ClassCode)) conut = Math.Max(conut, data.OpenRecords.Records.Count);
                if(_includesConfig.IsxistsConfig(data.OpenPartRecords.ClassCode)) conut = Math.Max(conut, data.OpenPartRecords.Records.Count);
                if(_includesConfig.IsxistsConfig(data.InstallRecords.ClassCode)) conut = Math.Max(conut, data.InstallRecords.Records.Count);
                if(_includesConfig.IsxistsConfig(data.CorrectReocrds.ClassCode)) conut = Math.Max(conut, data.CorrectReocrds.Records.Count);
                if(_includesConfig.IsxistsConfig(data.ComparisonReocrds.ClassCode)) conut = Math.Max(conut, data.ComparisonReocrds.Records.Count);
                if(_includesConfig.IsxistsConfig(data.DebugeRecords.ClassCode)) conut = Math.Max(conut, data.DebugeRecords.Records.Count);
                if(_includesConfig.IsxistsConfig(data.AuthRecords.ClassCode)) conut = Math.Max(conut, data.AuthRecords.Records.Count);
                if(_includesConfig.IsxistsConfig(data.Appearances.ClassCode)) conut = Math.Max(conut, data.Appearances.Records.Count);
                if(_includesConfig.IsxistsConfig(data.AcceptanceReports.ClassCode)) conut = Math.Max(conut, data.AcceptanceReports.Records.Count);
                if(_includesConfig.IsxistsConfig(data.MaintenanceRecords.ClassCode)) conut = Math.Max(conut, data.MaintenanceRecords.Records.Count);
                if(_includesConfig.IsxistsConfig(data.VerificationRecords.ClassCode)) conut = Math.Max(conut, data.VerificationRecords.Records.Count);
                if(_includesConfig.IsxistsConfig(data.FixRecords.ClassCode)) conut = Math.Max(conut, data.FixRecords.Records.Count);
                
                for (int i = 0; i < conut ; i++)
                {
                    equipmentInfos.Records.Add(data);
                }
                
                if (data.RunRecords.Records.Count < conut)
                {
                    for (int i = 0; i < conut - data.RunRecords.Records.Count; i++)
                    {
                        data.RunRecords.Records.Add(new ());
                    }
                }
                if (data.WeekWorkPlans.Records.Count < conut)
                {
                    for (int i = 0; i < conut - data.WeekWorkPlans.Records.Count; i++)
                    {
                        data.WeekWorkPlans.Records.Add(new ());
                    }
                }
                if (data.MonthWorkPlans.Records.Count < conut)
                {
                    for (int i = 0; i < conut - data.MonthWorkPlans.Records.Count; i++)
                    {
                        data.MonthWorkPlans.Records.Add(new ());
                    }
                }
                if (data.Environments.Records.Count < conut)
                {
                    for (int i = 0; i < conut - data.Environments.Records.Count; i++)
                    {
                        data.Environments.Records.Add(new ());
                    }
                }
                if (data.YearWorkPlans.Records.Count < conut)
                {
                    for (int i = 0; i < conut - data.YearWorkPlans.Records.Count; i++)
                    {
                        data.YearWorkPlans.Records.Add(new ());
                    }
                }
                if (data.Dealers.Records.Count < conut)
                {
                    for (int i = 0; i < conut - data.Dealers.Records.Count; i++)
                    {
                        data.Dealers.Records.Add(new ());
                    }
                }
                if (data.Manufacturers.Records.Count < conut)
                {
                    for (int i = 0; i < conut - data.Manufacturers.Records.Count; i++)
                    {
                        data.Manufacturers.Records.Add(new ());
                    }
                }
                if (data.Subscriptions.Records.Count < conut)
                {
                    for (int i = 0; i < conut - data.Subscriptions.Records.Count; i++)
                    {
                        data.Subscriptions.Records.Add(new ());
                    }
                }
                if (data.TrainingRecords.Records.Count < conut)
                {
                    for (int i = 0; i < conut - data.TrainingRecords.Records.Count; i++)
                    {
                        data.TrainingRecords.Records.Add(new ());
                    }
                }
                if (data.Procurements.Records.Count < conut)
                {
                    for (int i = 0; i < conut - data.Procurements.Records.Count; i++)
                    {
                        data.Procurements.Records.Add(new ());
                    }
                }
                if (data.Startupinfos.Records.Count < conut)
                {
                    for (int i = 0; i < conut - data.Startupinfos.Records.Count; i++)
                    {
                        data.Startupinfos.Records.Add(new ());
                    }
                }
                if (data.PlanRecords.Records.Count < conut)
                {
                    for (int i = 0; i < conut - data.PlanRecords.Records.Count; i++)
                    {
                        data.PlanRecords.Records.Add(new ());
                    }
                }
                if (data.OpenRecords.Records.Count < conut)
                {
                    for (int i = 0; i < conut - data.OpenRecords.Records.Count; i++)
                    {
                        data.OpenRecords.Records.Add(new ());
                    }
                }   
                if (data.OpenPartRecords.Records.Count < conut)
                {
                    for (int i = 0; i < conut - data.OpenPartRecords.Records.Count; i++)
                    {
                        data.OpenPartRecords.Records.Add(new ());
                    }
                }   
                if (data.InstallRecords.Records.Count < conut)
                {
                    for (int i = 0; i < conut - data.InstallRecords.Records.Count; i++)
                    {
                        data.InstallRecords.Records.Add(new ());
                    }
                }   
                if (data.CorrectReocrds.Records.Count < conut)
                {
                    for (int i = 0; i < conut - data.CorrectReocrds.Records.Count; i++)
                    {
                        data.CorrectReocrds.Records.Add(new ());
                    }
                }   
                if (data.ComparisonReocrds.Records.Count < conut)
                {
                    for (int i = 0; i < conut - data.ComparisonReocrds.Records.Count; i++)
                    {
                        data.ComparisonReocrds.Records.Add(new ());
                    }
                }   
                if (data.DebugeRecords.Records.Count < conut)
                {
                    for (int i = 0; i < conut - data.DebugeRecords.Records.Count; i++)
                    {
                        data.DebugeRecords.Records.Add(new ());
                    }
                }   
                if (data.AuthRecords.Records.Count < conut)
                {
                    for (int i = 0; i < conut - data.AuthRecords.Records.Count; i++)
                    {
                        data.AuthRecords.Records.Add(new ());
                    }
                }   
                if (data.Appearances.Records.Count < conut)
                {
                    for (int i = 0; i < conut - data.Appearances.Records.Count; i++)
                    {
                        data.Appearances.Records.Add(new ());
                    }
                }   
                if (data.AcceptanceReports.Records.Count < conut)
                {
                    for (int i = 0; i < conut - data.AcceptanceReports.Records.Count; i++)
                    {
                        data.AcceptanceReports.Records.Add(new ());
                    }
                }   
                if (data.MaintenanceRecords.Records.Count < conut)
                {
                    for (int i = 0; i < conut - data.MaintenanceRecords.Records.Count; i++)
                    {
                        data.MaintenanceRecords.Records.Add(new ());
                    }
                }   
                if (data.VerificationRecords.Records.Count < conut)
                {
                    for (int i = 0; i < conut - data.VerificationRecords.Records.Count; i++)
                    {
                        data.VerificationRecords.Records.Add(new ());
                    }
                }   
                if (data.FixRecords.Records.Count < conut)
                {
                    for (int i = 0; i < conut - data.FixRecords.Records.Count; i++)
                    {
                        data.FixRecords.Records.Add(new ());
                    }
                }   
                
                runRecords.Records.AddRange(data.RunRecords.Records);
                weekPlans.Records.AddRange(data.WeekWorkPlans.Records);
                monthWorkPlans.Records.AddRange(data.MonthWorkPlans.Records);
                yearWorkPlans.Records.AddRange(data.YearWorkPlans.Records);
                evns.Records.AddRange(data.Environments.Records);
                dealers.Records.AddRange(data.Dealers.Records);
                msanufacturers.Records.AddRange(data.Manufacturers.Records);
                subscriptions.Records.AddRange(data.Subscriptions.Records);
                trainingRecords.Records.AddRange(data.TrainingRecords.Records);
                procurements.Records.AddRange(data.Procurements.Records);
                startupinfos.Records.AddRange(data.Startupinfos.Records);
                plans.Records.AddRange(data.PlanRecords.Records);
                openRecords.Records.AddRange(data.OpenRecords.Records);
                openPartRecords.Records.AddRange(data.OpenPartRecords.Records);
                installRecords.Records.AddRange(data.InstallRecords.Records);
                correctReocrds.Records.AddRange(data.CorrectReocrds.Records);
                comparisonReocrds.Records.AddRange(data.ComparisonReocrds.Records);
                debugeRecords.Records.AddRange(data.DebugeRecords.Records);
                authRecords.Records.AddRange(data.AuthRecords.Records);
                appearances.Records.AddRange(data.Appearances.Records);
                acceptanceReports.Records.AddRange(data.AcceptanceReports.Records);
                mainteRecords.Records.AddRange(data.MaintenanceRecords.Records);
                verificaeRecords.Records.AddRange(data.VerificationRecords.Records);
                fixRecords.Records.AddRange(data.FixRecords.Records);
            }
                if(_includesConfig.IsxistsConfig(equipmentInfos.ClassCode)) result.DATAS.Add(FillWordRecord(equipmentInfos));
                if(_includesConfig.IsxistsConfig(runRecords.ClassCode)) result.DATAS.Add(FillWordRecord(runRecords));
                if(_includesConfig.IsxistsConfig(weekPlans.ClassCode)) result.DATAS.Add(FillWordRecord(weekPlans));
                if(_includesConfig.IsxistsConfig(monthWorkPlans.ClassCode)) result.DATAS.Add(FillWordRecord(monthWorkPlans));
                if(_includesConfig.IsxistsConfig(yearWorkPlans.ClassCode)) result.DATAS.Add(FillWordRecord(yearWorkPlans));
                if(_includesConfig.IsxistsConfig(evns.ClassCode)) result.DATAS.Add(FillWordRecord(evns));
                if(_includesConfig.IsxistsConfig(dealers.ClassCode)) result.DATAS.Add(FillWordRecord(dealers));
                if(_includesConfig.IsxistsConfig(msanufacturers.ClassCode)) result.DATAS.Add(FillWordRecord(msanufacturers));
                if(_includesConfig.IsxistsConfig(subscriptions.ClassCode)) result.DATAS.Add(FillWordRecord(subscriptions));
                if(_includesConfig.IsxistsConfig(trainingRecords.ClassCode)) result.DATAS.Add(FillWordRecord(trainingRecords));
                if(_includesConfig.IsxistsConfig(procurements.ClassCode)) result.DATAS.Add(FillWordRecord(procurements));
                if(_includesConfig.IsxistsConfig(startupinfos.ClassCode)) result.DATAS.Add(FillWordRecord(startupinfos));
                if(_includesConfig.IsxistsConfig(plans.ClassCode)) result.DATAS.Add(FillWordRecord(plans));
                if(_includesConfig.IsxistsConfig(openRecords.ClassCode)) result.DATAS.Add(FillWordRecord(openRecords));
                if(_includesConfig.IsxistsConfig(openPartRecords.ClassCode)) result.DATAS.Add(FillWordRecord(openPartRecords));
                if(_includesConfig.IsxistsConfig(installRecords.ClassCode)) result.DATAS.Add(FillWordRecord(installRecords));
                if(_includesConfig.IsxistsConfig(correctReocrds.ClassCode)) result.DATAS.Add(FillWordRecord(correctReocrds));
                if(_includesConfig.IsxistsConfig(comparisonReocrds.ClassCode)) result.DATAS.Add(FillWordRecord(comparisonReocrds));
                if(_includesConfig.IsxistsConfig(debugeRecords.ClassCode)) result.DATAS.Add(FillWordRecord(debugeRecords));
                if(_includesConfig.IsxistsConfig(authRecords.ClassCode)) result.DATAS.Add(FillWordRecord(authRecords));
                if(_includesConfig.IsxistsConfig(appearances.ClassCode)) result.DATAS.Add(FillWordRecord(appearances));
                if(_includesConfig.IsxistsConfig(acceptanceReports.ClassCode)) result.DATAS.Add(FillWordRecord(acceptanceReports));
                if(_includesConfig.IsxistsConfig(mainteRecords.ClassCode)) result.DATAS.Add(FillWordRecord(mainteRecords));
                if(_includesConfig.IsxistsConfig(verificaeRecords.ClassCode)) result.DATAS.Add(FillWordRecord(verificaeRecords));
                if(_includesConfig.IsxistsConfig(fixRecords.ClassCode)) result.DATAS.Add(FillWordRecord(fixRecords));

                result.DATAS.RemoveAll(x => !x.ARRAYS.Any());  
            return result;
        }
        #region Excel数据处理
        public OaExcelFillDataDto FillExcelModelEquipments(string styleId, List<EquipmentInfoData> equipmentInfos)
        {
            var filter = _dbContext.Db.Queryable<OA_EXCEL_STYLE_TEMPLATE>().Where(x => x.STYLE_ID == styleId).First();
            if (filter is not null)
            {
                _nodesFilters = filter.ITEM_SETUP_JSON;
            }
            const string onlyKey = "EQUIPMENT_ID";
            var result = new OaExcelFillDataDto(styleId, onlyKey);
            if (equipmentInfos is null)
            {
                return result;
            }
            SortTheList(equipmentInfos);
            foreach (var equipmentInfo in equipmentInfos)
            {
                var excelData = new StyleTemplateClassExcelDataDto();
                excelData.FIELDS = FillExcelDataNode("eq", equipmentInfo, onlyKey);
                excelData.CLASS_DATAS.AddRange(FillExcelRecordsNode(equipmentInfo.WeekWorkPlans.ClassCode, equipmentInfo.WeekWorkPlans.Records, equipmentInfo, onlyKey));
                excelData.CLASS_DATAS.AddRange(FillExcelRecordsNode(equipmentInfo.MonthWorkPlans.ClassCode, equipmentInfo.MonthWorkPlans.Records, equipmentInfo, onlyKey));
                excelData.CLASS_DATAS.AddRange(FillExcelRecordsNode(equipmentInfo.YearWorkPlans.ClassCode, equipmentInfo.YearWorkPlans.Records, equipmentInfo, onlyKey));
                excelData.CLASS_DATAS.AddRange(FillExcelRecordsNode(equipmentInfo.RunRecords.ClassCode, equipmentInfo.RunRecords.Records, equipmentInfo, onlyKey));
                excelData.CLASS_DATAS.AddRange(FillExcelRecordsNode(equipmentInfo.Environments.ClassCode, equipmentInfo.Environments.Records, equipmentInfo, onlyKey));
                excelData.CLASS_DATAS.AddRange(FillExcelRecordsNode(equipmentInfo.Dealers.ClassCode, equipmentInfo.Dealers.Records, equipmentInfo, onlyKey));
                excelData.CLASS_DATAS.AddRange(FillExcelRecordsNode(equipmentInfo.Manufacturers.ClassCode, equipmentInfo.Manufacturers.Records, equipmentInfo, onlyKey));
                excelData.CLASS_DATAS.AddRange(FillExcelRecordsNode(equipmentInfo.Subscriptions.ClassCode, equipmentInfo.Subscriptions.Records, equipmentInfo, onlyKey));
                excelData.CLASS_DATAS.AddRange(FillExcelRecordsNode(equipmentInfo.TrainingRecords.ClassCode, equipmentInfo.TrainingRecords.Records, equipmentInfo, onlyKey));
                excelData.CLASS_DATAS.AddRange(FillExcelRecordsNode(equipmentInfo.Procurements.ClassCode, equipmentInfo.Procurements.Records, equipmentInfo, onlyKey));
                excelData.CLASS_DATAS.AddRange(FillExcelRecordsNode(equipmentInfo.Startupinfos.ClassCode, equipmentInfo.Startupinfos.Records, equipmentInfo, onlyKey));
                excelData.CLASS_DATAS.AddRange(FillExcelRecordsNode(equipmentInfo.PlanRecords.ClassCode, equipmentInfo.PlanRecords.Records, equipmentInfo, onlyKey));
                excelData.CLASS_DATAS.AddRange(FillExcelRecordsNode(equipmentInfo.OpenRecords.ClassCode, equipmentInfo.OpenRecords.Records, equipmentInfo, onlyKey));
                excelData.CLASS_DATAS.AddRange(FillExcelRecordsNode(equipmentInfo.OpenPartRecords.ClassCode, equipmentInfo.OpenPartRecords.Records, equipmentInfo, onlyKey));
                excelData.CLASS_DATAS.AddRange(FillExcelRecordsNode(equipmentInfo.InstallRecords.ClassCode, equipmentInfo.InstallRecords.Records, equipmentInfo, onlyKey));
                excelData.CLASS_DATAS.AddRange(FillExcelRecordsNode(equipmentInfo.CorrectReocrds.ClassCode, equipmentInfo.CorrectReocrds.Records, equipmentInfo, onlyKey));
                excelData.CLASS_DATAS.AddRange(FillExcelRecordsNode(equipmentInfo.ComparisonReocrds.ClassCode, equipmentInfo.ComparisonReocrds.Records, equipmentInfo, onlyKey));
                excelData.CLASS_DATAS.AddRange(FillExcelRecordsNode(equipmentInfo.DebugeRecords.ClassCode, equipmentInfo.DebugeRecords.Records, equipmentInfo, onlyKey));
                excelData.CLASS_DATAS.AddRange(FillExcelRecordsNode(equipmentInfo.AuthRecords.ClassCode, equipmentInfo.AuthRecords.Records, equipmentInfo, onlyKey));
                excelData.CLASS_DATAS.AddRange(FillExcelRecordsNode(equipmentInfo.Appearances.ClassCode, equipmentInfo.Appearances.Records, equipmentInfo, onlyKey));
                excelData.CLASS_DATAS.AddRange(FillExcelRecordsNode(equipmentInfo.AcceptanceReports.ClassCode, equipmentInfo.AcceptanceReports.Records, equipmentInfo, onlyKey));
                excelData.CLASS_DATAS.AddRange(FillExcelRecordsNode(equipmentInfo.MaintenanceRecords.ClassCode, equipmentInfo.MaintenanceRecords.Records, equipmentInfo, onlyKey));
                excelData.CLASS_DATAS.AddRange(FillExcelRecordsNode(equipmentInfo.VerificationRecords.ClassCode, equipmentInfo.VerificationRecords.Records, equipmentInfo, onlyKey));
                excelData.CLASS_DATAS.AddRange(FillExcelRecordsNode(equipmentInfo.FixRecords.ClassCode, equipmentInfo.FixRecords.Records, equipmentInfo, onlyKey));
                result.ALL_CLASS_DATAS.Add(excelData);
            }
#if DEBUG
            Console.WriteLine(JsonConvert.SerializeObject(result));
#endif

            return result;

        }
        #endregion

        #region 数据处理

        private void RunRecordExchange(EquipmentInfoRecord<RunRecord> equipmentInfoRunRecord, EMS_EQUIPMENT_INFO equipment)
        {
            equipmentInfoRunRecord.Records.Add(new RunRecord()
            {
                LAST_MAINTAIN_DATE = equipment.LAST_MAINTAIN_DATE,
                MAINTAIN_TYPE = equipment.MAINTAIN_TYPE,
                NEXT_MAINTAIN_DATE = equipment.NEXT_MAINTAIN_DATE,
                LAST_CORRECT_DATE = equipment.LAST_CORRECT_DATE,
                CORRECT_INTERVALS = equipment.CORRECT_INTERVALS,
                NEXT_CORRECT_DATE = equipment.NEXT_CORRECT_DATE,
                LAST_COMPARISON_DATE = equipment.LAST_COMPARISON_DATE,
                COMPARISON_INTERVALS = equipment.COMPARISON_INTERVALS,
                NEXT_COMPARISON_DATE = equipment.NEXT_COMPARISON_DATE,
                LAST_VERIFICATION_DATE = equipment.LAST_VERIFICATION_DATE,
                VERIFICATION_INTERVALS = equipment.VERIFICATION_INTERVALS,
                NEXT_VERIFICATION_DATE = equipment.NEXT_VERIFICATION_DATE
            });
        }

        private void WordPlandExchange(EquipmentInfoRecord<WeekWorkPlan> weekWorkPlan, EquipmentInfoRecord<MonthWorkPlan> monthWorkPlan, EquipmentInfoRecord<YearWorkPlan> yearWorkPlan, EMS_EQUIPMENT_INFO equipment)
        {
            var workPlan = equipment.eMS_WORK_PLAN;
            if (workPlan is null)
            {
                weekWorkPlan.Records.Add(new());
                monthWorkPlan.Records.Add(new());
                yearWorkPlan.Records.Add(new());
                return;
            }
            var maintenanceRecodes = equipment.eMS_MAINTAIN_INFO
               .OrderByDescending(x => x.MAINTAIN_DATE)
               .Where(x => x.MAINTAIN_DATE != null)
               .Where(x => x.MAINTAIN_CYCLE != null)
               .ToList();

            var week = maintenanceRecodes.Where(x => x.MAINTAIN_CYCLE.Contains("周"))
                  .OrderByDescending(x => x.MAINTAIN_DATE)
                  .FirstOrDefault();
            var month = maintenanceRecodes.Where(x => x.MAINTAIN_CYCLE.Contains("月"))
                .OrderByDescending(x => x.MAINTAIN_DATE).FirstOrDefault();

            var quarter = maintenanceRecodes.Where(x => x.MAINTAIN_CYCLE.Contains("季度"))
                .OrderByDescending(x => x.MAINTAIN_DATE).FirstOrDefault();

            var year = maintenanceRecodes.Where(x => x.MAINTAIN_CYCLE.Contains("年"))
                .OrderByDescending(x => x.MAINTAIN_DATE).FirstOrDefault();

            var weekAddDay = workPlan.MAINTAIN_INTERVALS;
            var monthAddDay = workPlan.MONTHLY_MAINTAIN;
            var quarterDay = workPlan.QUARTERLY_MAINTAIN;
            var yearAddDay = workPlan.YEARLY_MAINTAIN;

            var weekRemindAddDay = workPlan.MAINTAIN_WARN_INTERVALS;
            var monthRemindAddDay = workPlan.MONTHLY_MAINTAIN_WARN;
            var yearRemindAddDay = workPlan.YEARLY_MAINTAIN_WARN;

            DateTime? weekDate = week is not null ? week.MAINTAIN_DATE!.Value.AddDays(Convert.ToDouble(weekAddDay)) : null;
            DateTime? monthDate = month is not null ? month.MAINTAIN_DATE!.Value.AddDays(Convert.ToDouble(monthAddDay)) : null;
            DateTime? quarterDate = quarter is not null ? quarter.MAINTAIN_DATE!.Value.AddDays(Convert.ToDouble(quarterDay)) : null;
            DateTime? yearDate = year is not null ? year.MAINTAIN_DATE!.Value.AddDays(Convert.ToDouble(yearAddDay)) : null;

            weekWorkPlan.Records.Add(new WeekWorkPlan()
            {
                MAINTAIN_INTERVALS = workPlan.MAINTAIN_INTERVALS,
                MAINTAIN_WARN_INTERVALS = workPlan.MAINTAIN_WARN_INTERVALS,
                LAST_MAINTAIN_DATE = weekDate
            });

            monthWorkPlan.Records.Add(new MonthWorkPlan()
            {
                MONTHLY_MAINTAIN = workPlan.MONTHLY_MAINTAIN,
                MONTHLY_MAINTAIN_WARN = workPlan.MONTHLY_MAINTAIN_WARN,
                LAST_MONTHLY_MAINTAIN_DATE = monthDate
            });

            yearWorkPlan.Records.Add(new YearWorkPlan()
            {
                YEARLY_MAINTAIN = workPlan.YEARLY_MAINTAIN,
                YEARLY_MAINTAIN_WARN = workPlan.YEARLY_MAINTAIN_WARN,
                LAST_YEARLY_MAINTAIN_DATE = yearDate
            });
        }

        private void EnvironmentExchange(EquipmentInfoRecord<EquipmentEnvironment> environment, EMS_EQUIPMENT_INFO equipment)
        {

            var environmentInfo = equipment.eMS_ENVI_REQUIRE_INFO;


            if (environmentInfo is null)
            {
                environment.Records.Add(new EquipmentEnvironment());
                return;
            }

            if (environmentInfo.EQUIPMENT_SIZE != null)
            {
                environmentInfo.LENGTH = environmentInfo.EQUIPMENT_SIZE.Split(",")[0];
                environmentInfo.WIDTH = environmentInfo.EQUIPMENT_SIZE.Split(",")[1];
                environmentInfo.HEIGHT = environmentInfo.EQUIPMENT_SIZE.Split(",")[2];
            }
            if (environmentInfo.TEMPERATURE_REQUIRE != null)
            {
                environmentInfo.TEMP_MIN = environmentInfo.TEMPERATURE_REQUIRE.Split(",")[0];
                environmentInfo.TEMP_MAX = environmentInfo.TEMPERATURE_REQUIRE.Split(",")[1];
            }
            if (environmentInfo.HUMIDITY_REQUIRE != null)
            {
                environmentInfo.HUMI_MIN = environmentInfo.HUMIDITY_REQUIRE.Split(",")[0];
                environmentInfo.HUMI_MAX = environmentInfo.HUMIDITY_REQUIRE.Split(",")[1];
            }

            environment.Records.Add(new EquipmentEnvironment()
            {
                AIR_REQUIRE = environmentInfo.AIR_REQUIRE,
                SPACE_REQUIRE = environmentInfo.SPACE_REQUIRE,
                WATER_REQUIRE = environmentInfo.WATER_REQUIRE,
                TEMP_MIN = environmentInfo.TEMP_MIN,
                TEMP_MAX = environmentInfo.TEMP_MAX,
                HUMI_MIN = environmentInfo.HUMI_MIN,
                HUMI_MAX = environmentInfo.HUMI_MAX,
                AIR_PRESSURE_REQUIRE = environmentInfo.AIR_PRESSURE_REQUIRE,
                POWER_REQUIRE = environmentInfo.POWER_REQUIRE,
                VOLTAGE_REQUIRE = environmentInfo.VOLTAGE_REQUIRE,
                ELECTRICITY_REQUIRE = environmentInfo.ELECTRICITY_REQUIRE,
                OTHER_REQUIRE = environmentInfo.OTHER_REQUIRE,
                LENGTH = environmentInfo.LENGTH,
                WIDTH = environmentInfo.WIDTH,
                HEIGHT = environmentInfo.HEIGHT,
                EQUIPMENT_WEIGHT = environmentInfo.EQUIPMENT_WEIGHT,
                BEARING_REQUIRE = environmentInfo.BEARING_REQUIRE
            });
        }

        private void DealersExchange(EquipmentInfoRecord<Dealer> dealer, EMS_EQUIPMENT_INFO equipment)
        {
            var dealerContacts = new List<SYS6_COMPANY_CONTACT>();
            if (equipment.DEALER_ID is not null)
            {

                var contacts = equipmentContext.GetCompanyContact(equipment.DEALER_ID);
                if (contacts.Count() == 0)
                {
                    dealer.Records.Add(new Dealer());
                    return;
                }

                var manu = equipment.Contacts.Where(x => x.CONTACT_TYPE == "经销商" && x.CONTACT_ESTATE == "1").Select(x => x.CONTACT_ID).ToList();
                foreach (var contact in contacts)
                {
                    contact.IF_SELECT = "0";
                    if (manu is not null)
                    {
                        if (manu.Contains(contact.CONTACT_ID))
                        {
                            contact.IF_SELECT = "1";
                        }
                    }
                    dealerContacts.Add(contact);
                }

                foreach (var contact in dealerContacts)
                {
                    if (contact.IF_SELECT == "1")
                    {
                        dealer.Records.Add(new Dealer()
                        {
                            CONTACT_TYPE = contact.CONTACT_TYPE,
                            CONTACT_POST = contact.CONTACT_POST,
                            CONTACT_NAME = contact.CONTACT_NAME,
                            PHONE_NO = contact.PHONE_NO,
                            CONTACT_WX = contact.CONTACT_WX,
                            E_MAIL = contact.E_MAIL,
                            REMARK = contact.REMARK,
                            DEALER = equipment.DEALER,
                            DEALER_ENAME = equipment.DEALER_ENAME
                        });
                    }
                }

                if (dealer.Records.Count() == 0)
                {
                    dealer.Records.Add(new Dealer());
                }
            }
            else
            {
                dealer.Records.Add(new Dealer());
            }
        }

        private void ManufacturerExchange(EquipmentInfoRecord<Manufacturer> manufacturer, EMS_EQUIPMENT_INFO equipment)
        {
            var manufacturerContacts = new List<SYS6_COMPANY_CONTACT>();
            if (equipment.MANUFACTURER_ID is not null)
            {
                var contacts = equipmentContext.GetCompanyContact(equipment.MANUFACTURER_ID);
                if (contacts.Count() == 0)
                {
                    manufacturer.Records.Add(new Manufacturer());
                    return;
                }

                var manu = equipment.Contacts.Where(x => x.CONTACT_TYPE == "制造商" && x.CONTACT_ESTATE == "1").Select(x => x.CONTACT_ID).ToList();
                foreach (var contact in contacts)
                {
                    contact.IF_SELECT = "0";
                    if (manu is not null)
                    {
                        if (manu.Contains(contact.CONTACT_ID))
                        {
                            contact.IF_SELECT = "1";
                        }
                    }
                    manufacturerContacts.Add(contact);
                }

                foreach (var contact in manufacturerContacts)
                {
                    if (contact.IF_SELECT == "1")
                    {
                        manufacturer.Records.Add(new Manufacturer()
                        {
                            CONTACT_TYPE = contact.CONTACT_TYPE,
                            CONTACT_POST = contact.CONTACT_POST,
                            CONTACT_NAME = contact.CONTACT_NAME,
                            PHONE_NO = contact.PHONE_NO,
                            CONTACT_WX = contact.CONTACT_WX,
                            E_MAIL = contact.E_MAIL,
                            REMARK = contact.REMARK,
                            MANUFACTURER = equipment.MANUFACTURER,
                            MANUFACTURER_ENAME = equipment.MANUFACTURER_ENAME
                        });
                    }
                }
                if (manufacturer.Records.Count() == 0)
                {
                    manufacturer.Records.Add(new Manufacturer());
                }
            }
            else
            {
                manufacturer.Records.Add(new Manufacturer());
            }
        }

        private void SubscriptionsExchange(EquipmentInfoRecord<SubscriptionRecord> subscription, EMS_EQUIPMENT_INFO equipment)
        {
            var sub = equipmentContext.GetSubscribe(equipment.EQUIPMENT_ID);
            if (sub is not null)
            {
                subscription.Records.Add(new SubscriptionRecord()
                {
                    SUBSCRIBE_NAME = sub.SUBSCRIBE_NAME,
                    SUBSCRIBE_PERSON = sub.SUBSCRIBE_PERSON,
                    SUBSCRIBE_DATE = sub.SUBSCRIBE_DATE,
                    MGROUP_ID = equipmentContext.ExchangeProfessionalGroupName(sub.MGROUP_ID),
                    APPROVE_PERSON = sub.APPROVE_PERSON,
                    APPROVE_TIME = sub.APPROVE_TIME,
                    APPROVE_OPINION = sub.APPROVE_OPINION,
                    SUBSCRIBE_STATE = sub.SUBSCRIBE_STATE,
                    REMARK = sub.REMARK,
                });

            }
            else
            {
                subscription.Records.Add(new SubscriptionRecord());
            }

        }

        private void TrainingRecordsExchange(EquipmentInfoRecord<TrainingRecord> training, EMS_EQUIPMENT_INFO equipment)
        {

            if (equipment.eMS_TRAIN_INFO is null)
            {
                training.Records.Add(new TrainingRecord());
                return;
            }
            if (equipment.eMS_TRAIN_INFO.Count() == 0)
            {
                training.Records.Add(new TrainingRecord());
                return;
            }

            foreach (var train in equipment.eMS_TRAIN_INFO)
            {

                var docPaths = GetEmsDocPaths(train.TRAIN_ID, "培训记录");
                var paths = "";
                foreach (var path in docPaths)
                {
                    paths += $"{path}+";
                }
                paths = paths.TrimEnd('+');

                training.Records.Add(new TrainingRecord()
                {
                    APPEARANCE = paths,
                    TRAIN_TIME = train.TRAIN_TIME,
                    TRAIN_NAME = train.TRAIN_NAME,
                    TRAIN_ADDR = train.TRAIN_ADDR,
                    TRAIN_HOUR = train.TRAIN_HOUR,
                    JOIN_PERSON = train.JOIN_PERSON,
                    TRAIN_TEACHER = train.TRAIN_TEACHER,
                    REMARK = train.REMARK,
                });
            }
        }


        private void ProcurementsExchange(EquipmentInfoRecord<Procurement> procurements, EMS_EQUIPMENT_INFO equipment)
        {

            if (equipment.eMS_PURCHASE_INFO is null)
            {
                procurements.Records.Add(new Procurement());
                return;
            }

            var docPaths = GetEmsDocPaths(equipment.eMS_PURCHASE_INFO.PURCHASE_ID, "申购信息");
            var paths = "";
            foreach (var path in docPaths)
            {
                paths += $"{path}+";
            }
            paths = paths.TrimEnd('+');

            procurements.Records.Add(new Procurement()
            {
                CALL_BIDS_NAME = equipment.eMS_PURCHASE_INFO.CALL_BIDS_NAME,
                CALL_BIDS_NO = equipment.eMS_PURCHASE_INFO.CALL_BIDS_NO,
                CALL_BIDS_DATE = equipment.eMS_PURCHASE_INFO.CALL_BIDS_DATE,
                CONTRACT_NAME = equipment.eMS_PURCHASE_INFO.CONTRACT_NAME,
                CONTRACT_NO = equipment.eMS_PURCHASE_INFO.CONTRACT_NO,
                CONTRACT_DATE = equipment.eMS_PURCHASE_INFO.CONTRACT_DATE,
                APPEARANCE = paths,
            });
        }

        private void StartupinfosExchange(EquipmentInfoRecord<Startupinfo> startupinfos, EMS_EQUIPMENT_INFO equipment)
        {
            if (equipment.eMS_STARTUP_INFO is null)
            {
                startupinfos.Records.Add(new());
                return;
            }
            if (equipment.eMS_STARTUP_INFO.Count() == 0)
            {
                startupinfos.Records.Add(new());
                return;
            }

            var docPaths = GetEmsDocPaths(equipment.EQUIPMENT_ID, "开机性能验证报告");
            var paths = "";
            foreach (var path in docPaths)
            {
                paths += $"{path}+";
            }
            paths = paths.TrimEnd('+');

            foreach (var startupinfo in equipment.eMS_STARTUP_INFO)
            {

                startupinfos.Records.Add(new()
                {
                    APPEARANCE = paths,
                    START_DATE = startupinfo.START_DATE,
                    END_DATE = startupinfo.END_DATE,
                    VERIFY_PERSON = startupinfo.VERIFY_PERSON,
                    VERIFY_RESULT = equipmentContext.VerifyresultName(startupinfo.VERIFY_RESULT),
                    REMARK = startupinfo.REMARK,
                });
            }
        }

        private void PlanRecordsExchange(EquipmentInfoRecord<PlanRecord> planRecords, EMS_EQUIPMENT_INFO equipment)
        {
            if (equipment.eMS_WORK_PLAN is null)
            {
                planRecords.Records.Add(new());
                return;
            }

            if (equipment.eMS_WORK_PLAN.WORK_PLAN_ID is null)
            {
                planRecords.Records.Add(new());
                return;
            }
            var openRecords = equipmentContext.GetPlanWorkOperLogs(equipment);

            if (openRecords.Count() == 0)
            {
                planRecords.Records.Add(new());
                return;
            }
            var plan = new PlanRecord()
            {

                REGISTRATION_NUM = equipment.REGISTRATION_NUM,
                REGISTRATION_NAME = equipment.MANUFACTURER,
            };

            foreach (var openRecord in openRecords)
            {

                if (openRecord.OPER_STATE is OperationStateEnum.Submitted)
                {
                    plan.OPER_PERSON = openRecord.OPER_PERSON;
                    plan.OPER_TIME = openRecord.LAST_MTIME;
                }
                if (openRecord.OPER_STATE is OperationStateEnum.Audited)
                {
                    plan.AUDITOR_TIME = openRecord.LAST_MTIME;
                    plan.AUDITOR_USER_NAME = openRecord.OPER_PERSON;
                    plan.AUDITOR_CONTEXT = openRecord.OPER_CONTENT;
                }
            }
            planRecords.Records.Add(plan);
        }

        private void OpenRecordsExchange(EquipmentInfoRecord<OpenRecord> openRecords, EMS_EQUIPMENT_INFO equipment)
        {

            if (equipment.eMS_UNPACK_INFO is null)
            {
                openRecords.Records.Add(new());
                return;
            }
            if (equipment.eMS_UNPACK_INFO.Count() == 0)
            {
                openRecords.Records.Add(new());
                return;
            }
            foreach (var openRecord in equipment.eMS_UNPACK_INFO)
            {
                openRecords.Records.Add(new()
                {
                    UNPACK_DATE = openRecord.UNPACK_DATE,
                    LAB_PERSON = openRecord.LAB_PERSON,
                    HOSPITAL_PERSON = openRecord.HOSPITAL_PERSON,
                    UNPACK_PERSON = openRecord.UNPACK_PERSON,
                    UNPACK_INSPECT = openRecord.APPEARANCE_INSPECT,
                    UNPACK_CONDITION = openRecord.UNPACK_CONDITION,
                    ENGINEER = openRecord.ENGINEER,
                    RELATION_WAY = openRecord.RELATION_WAY,
                    REMARK = openRecord.REMARK
                });
            }
        }


        private void OpenPartRecordsExchange(EquipmentInfoRecord<OpenPartRecord> openPartRecords, EMS_EQUIPMENT_INFO equipment)
        {

            if (equipment.eMS_PARTS_INFO is null)
            {
                openPartRecords.Records.Add(new OpenPartRecord());
                return;
            }
            if (equipment.eMS_PARTS_INFO.Count() == 0)
            {
                openPartRecords.Records.Add(new OpenPartRecord());
                return;
            }

            foreach (var openPartRecord in equipment.eMS_PARTS_INFO.Where(x => x.PARTS_STATE == "1"))
            {
                var docPaths = GetEmsDocPaths(openPartRecord.PARTS_ID, "开箱记录");
                var paths = "";
                foreach (var path in docPaths)
                {
                    paths += $"{path}+";
                }
                paths = paths.TrimEnd('+');

                openPartRecords.Records.Add(new OpenPartRecord()
                {
                    PARTS_NAME = openPartRecord.PARTS_NAME,
                    PARTS_MODEL = openPartRecord.PARTS_MODEL,
                    PARTS_SPEC = openPartRecord.PARTS_SPEC,
                    PARTS_AMOUNT = $"{openPartRecord.PARTS_AMOUNT}",
                    PARTS_SNUM = openPartRecord.PARTS_SNUM,
                    PARTS_ORIGIN = openPartRecord.PARTS_ORIGIN,
                    PARTS_BRAND = openPartRecord.PARTS_BRAND,
                    PARTS_POSITION = openPartRecord.PARTS_POSITION,
                    APPEARANCE = paths
                });
            }

        }
        private void InstallRecordsExchange(EquipmentInfoRecord<InstallRecord> installRecords, EMS_EQUIPMENT_INFO equipment)
        {
            if (equipment.eMS_INSTALL_INFO is null)
            {
                installRecords.Records.Add(new());
                return;
            }

            var docPaths = GetEmsDocPaths(equipment.EQUIPMENT_ID, "安装记录");
            var paths = "";
            foreach (var path in docPaths)
            {
                paths += $"{path}+";
            }
            paths = paths.TrimEnd('+');

            installRecords.Records.Add(new()
            {
                APPEARANCE = "",
                INSTALL_DATE = equipment.eMS_INSTALL_INFO.INSTALL_DATE,
                INSTALL_AREA = equipment.eMS_INSTALL_INFO.INSTALL_AREA,
                LAB_PERSON = equipment.eMS_INSTALL_INFO.LAB_PERSON,
                ENGINEER = equipment.eMS_INSTALL_INFO.ENGINEER,
                INSTALL_CONDITION = equipment.eMS_INSTALL_INFO.INSTALL_CONDITION,
                HOSPITAL_PERSON = equipment.eMS_INSTALL_INFO.HOSPITAL_PERSON,
                RELATION_WAY = equipment.eMS_INSTALL_INFO.RELATION_WAY,
                REMARK = equipment.eMS_INSTALL_INFO.REMARK
            });
        }

        private void ComparisonReocrdsExchange(EquipmentInfoRecord<ComparisonReocrd> comparisonReocrds, EMS_EQUIPMENT_INFO equipment)
        {

            if (equipment.eMS_COMPARISON_INFO is null)
            {
                comparisonReocrds.Records.Add(new ComparisonReocrd()
                {

                    COMPARISON_INTERVALS = equipment.eMS_WORK_PLAN is null ? "" : equipment.eMS_WORK_PLAN.CORRECT_INTERVALS,
                    COMPARISON_WARN_INTERVALS = equipment.eMS_WORK_PLAN is null ? "" : equipment.eMS_WORK_PLAN.COMPARISON_WARN_INTERVALS,
                    LAST_COMPARISON_INTERVALS_DATE = equipment.LAST_COMPARISON_DATE,
                    NEXT_COMPARISON_INTERVALS_DATE = equipment.NEXT_COMPARISON_DATE,

                });
                return;
            }
            
            if (equipment.eMS_COMPARISON_INFO.Where(x => x.COMPARISON_STATE == "1").Count() > 0)
            {
                foreach (var comparison in equipment.eMS_COMPARISON_INFO.Where(x => x.COMPARISON_STATE == "1"))
                {
                    var comparisonRecord = new ComparisonReocrd()
                    {

                        COMPARISON_INTERVALS = equipment.eMS_WORK_PLAN is null ? "" : equipment.eMS_WORK_PLAN.CORRECT_INTERVALS,
                        COMPARISON_WARN_INTERVALS = equipment.eMS_WORK_PLAN is null ? "" : equipment.eMS_WORK_PLAN.COMPARISON_WARN_INTERVALS,
                        LAST_COMPARISON_INTERVALS_DATE = equipment.LAST_COMPARISON_DATE,
                        NEXT_COMPARISON_INTERVALS_DATE = equipment.NEXT_COMPARISON_DATE,
                        COMPARISON_ID = comparison.COMPARISON_ID,
                        COMPARISON_DATE = comparison.COMPARISON_DATE,
                        COMPARISON_OBJECT = comparison.COMPARISON_OBJECT,
                        COMPARISON_RESULT = comparison.COMPARISON_RESULT,
                        RELATION_EVENT = comparison.RELATION_EVENT,
                        COMPARISON_NO = comparison.COMPARISON_NO,
                        STATE = comparison.STATE,
                        COMPARISON_PERSON = comparison.COMPARISON_PERSON,
                        REMARK = comparison.REMARK
                    };
                    comparisonReocrds.Records.Add(comparisonRecord);
                }
            }
            else
            {

                comparisonReocrds.Records.Add(new ComparisonReocrd()
                {

                    COMPARISON_INTERVALS = equipment.eMS_WORK_PLAN is null ? "" : equipment.eMS_WORK_PLAN.CORRECT_INTERVALS,
                    COMPARISON_WARN_INTERVALS = equipment.eMS_WORK_PLAN is null ? "" : equipment.eMS_WORK_PLAN.COMPARISON_WARN_INTERVALS,
                    LAST_COMPARISON_INTERVALS_DATE = equipment.LAST_COMPARISON_DATE,
                    NEXT_COMPARISON_INTERVALS_DATE = equipment.NEXT_COMPARISON_DATE,

                });
            }
        }

        private void CorrectReocrdsExchange(EquipmentInfoRecord<CorrectReocrd> correctReocrds, EMS_EQUIPMENT_INFO equipment)
        {
            if (equipment.eMS_CORRECT_INFO is null)
            {
                correctReocrds.Records.Add(new CorrectReocrd()
                {
                    CORRECT_INTERVALS = equipment.eMS_WORK_PLAN is null ? "" : equipment.eMS_WORK_PLAN.CORRECT_INTERVALS,
                    CORRECT_WARN_INTERVALS = equipment.eMS_WORK_PLAN is null ? "" : equipment.eMS_WORK_PLAN.CORRECT_WARN_INTERVALS,
                    LAST_CORRECT_INTERVALS_DATE = equipment.LAST_CORRECT_DATE,
                    NEXT_CORRECT_INTERVALS_DATE = equipment.NEXT_CORRECT_DATE,
                    EQ_IN_PERSON = equipment.KEEP_PERSON,
                    CORRECT_UNIT = "",
                    CORRECT_ID = "",
                    CORRECT_PERSON = "",
                    CORRECT_RESULT = "",
                    RELATION_EVENT = "",
                    STATE = "",
                    CORRECT_NO = "",
                    OCCUR_EVENT = "",
                    REMARK = ""
                });
                return;
            }
            
            if (equipment.eMS_CORRECT_INFO.Where(x => x.CORRECT_STATE == "1").Count() > 0)
            {
                foreach (var correct in equipment.eMS_CORRECT_INFO.OrderByDescending(x => x.LAST_MTIME).Where(x => x.CORRECT_STATE == "1"))
                {

                    var correctReocrd = new CorrectReocrd()
                    {
                        CORRECT_INTERVALS = equipment.eMS_WORK_PLAN is null ? "" : equipment.eMS_WORK_PLAN.CORRECT_INTERVALS,
                        CORRECT_WARN_INTERVALS = equipment.eMS_WORK_PLAN is null ? "" : equipment.eMS_WORK_PLAN.CORRECT_WARN_INTERVALS,
                        LAST_CORRECT_INTERVALS_DATE = equipment.LAST_CORRECT_DATE,
                        NEXT_CORRECT_INTERVALS_DATE = equipment.NEXT_CORRECT_DATE,
                        EQ_IN_PERSON = equipment.KEEP_PERSON,
                        CORRECT_UNIT = correct.CORRECT_DEPT,
                        CORRECT_ID = correct.CORRECT_ID,
                        CORRECT_PERSON = correct.CORRECT_PERSON,
                        CORRECT_RESULT = correct.CORRECT_RESULT,
                        RELATION_EVENT = correct.RELATION_EVENT,
                        STATE = correct.STATE,
                        CORRECT_NO = correct.CORRECT_NO,
                        OCCUR_EVENT = correct.OCCUR_EVENT,
                        CORRECT_DATE = correct.CORRECT_DATE,
                        REMARK = correct.REMARK
                    };
                    correctReocrds.Records.Add(correctReocrd);
                }
            }
            else
            {
                correctReocrds.Records.Add(new CorrectReocrd()
                {
                    CORRECT_INTERVALS = equipment.eMS_WORK_PLAN is null ? "" : equipment.eMS_WORK_PLAN.CORRECT_INTERVALS,
                    CORRECT_WARN_INTERVALS = equipment.eMS_WORK_PLAN is null ? "" : equipment.eMS_WORK_PLAN.CORRECT_WARN_INTERVALS,
                    LAST_CORRECT_INTERVALS_DATE = equipment.LAST_CORRECT_DATE,
                    NEXT_CORRECT_INTERVALS_DATE = equipment.NEXT_CORRECT_DATE,
                    EQ_IN_PERSON = equipment.KEEP_PERSON,
                    CORRECT_UNIT = "",
                    CORRECT_ID = "",
                    CORRECT_PERSON = "",
                    CORRECT_RESULT = "",
                    RELATION_EVENT = "",
                    STATE = "",
                    CORRECT_NO = "",
                    OCCUR_EVENT = "",
                    REMARK = ""
                });
            }
        }

        private void DebugeRecordsExchange(EquipmentInfoRecord<DebugeRecord> debugeRecords, EMS_EQUIPMENT_INFO equipment)
        {
            if (equipment.eMS_DEBUG_INFO is null)
            {
                debugeRecords.Records.Add(new());
                return;
            }
            if (equipment.eMS_DEBUG_INFO.Count() == 0)
            {
                debugeRecords.Records.Add(new());
                return;
            }
            foreach (var debugeRecord in equipment.eMS_DEBUG_INFO)
            {
                debugeRecords.Records.Add(new()
                {
                    DEBUG_DATE = debugeRecord.DEBUG_DATE,
                    HOSPITAL_PERSON = debugeRecord.HOSPITAL_PERSON,
                    ENGINEER = debugeRecord.ENGINEER,
                    DEBUG_CONDITION = equipmentContext.DebugConditionName(debugeRecord.DEBUG_CONDITION),
                    LAB_PERSON = debugeRecord.LAB_PERSON,
                    RELATION_WAY = debugeRecord.RELATION_WAY,
                    APPEARANCE = ""
                });
            }
        }

        private void AuthRecordsExchange(EquipmentInfoRecord<AuthRecord> authRecords, EMS_EQUIPMENT_INFO equipment)
        {
            if (equipment.eMS_AUTHORIZE_INFO is null)
            {
                authRecords.Records.Add(new());
                return;
            }
            if (equipment.eMS_AUTHORIZE_INFO.Count() == 0)
            {
                authRecords.Records.Add(new());
                return;
            }

            foreach (var authRecord in equipment.eMS_AUTHORIZE_INFO)
            {
                var docPaths = GetEmsDocPaths(authRecord.AUTHORIZE_ID, "授权记录");
                var paths = "";
                foreach (var path in docPaths)
                {
                    paths += $"{path}+";
                }
                paths = paths.TrimEnd('+');

                authRecords.Records.Add(new()
                {
                    APPEARANCE = paths,
                    AUTHORIZE_PERSON = authRecord.AUTHORIZE_PERSON,
                    AUTHORIZED_PERSON = authRecord.AUTHORIZED_PERSON,
                    AUTHORIZED_ROLE = authRecord.AUTHORIZED_ROLE,
                    AUTHORIZED_PERSON_POST = authRecord.AUTHORIZED_PERSON_POST,
                    AUTHORIZE_DATE = authRecord.AUTHORIZE_DATE
                });
            }
        }

        private void AppearancesExchange(EquipmentInfoRecord<Appearance> appearances, EMS_EQUIPMENT_INFO equipment)
        {

            var docPaths = GetEmsDocPaths(equipment.EQUIPMENT_ID, "外观信息");
            var paths = "";
            foreach (var path in docPaths)
            {
                paths += $"{path}+";
            }
            paths = paths.TrimEnd('+');

            appearances.Records.Add(new()
            {

                APPEARANCE = paths

            });
        }

        private void AcceptanceReportsExchange(EquipmentInfoRecord<AcceptanceReport> acceptanceReports, EMS_EQUIPMENT_INFO equipment)
        {

            var docPaths = GetEmsDocPaths(equipment.EQUIPMENT_ID, "验收报告");
            var paths = "";
            foreach (var path in docPaths)
            {
                paths += $"{path}+";
            }
            paths = paths.TrimEnd('+');


            acceptanceReports.Records.Add(new()
            {

                APPEARANCE = paths

            });
        }


        private void MaintenanceRecordsExchange(EquipmentInfoRecord<MaintenanceRecord> maintenanceRecords, EMS_EQUIPMENT_INFO equipment)
        {

            if (equipment.eMS_MAINTAIN_INFO is null)
            {
                maintenanceRecords.Records.Add(new MaintenanceRecord());
                return;
            }
            if (equipment.eMS_MAINTAIN_INFO.OrderByDescending(x => x.LAST_MTIME).Where(x => x.MAINTAIN_STATE == "1").Count() > 0)
            {
                foreach (var maintain in equipment.eMS_MAINTAIN_INFO.OrderByDescending(x => x.LAST_MTIME).Where(x => x.MAINTAIN_STATE == "1"))
                {
                    var maintainRecord = new MaintenanceRecord()
                    {
                        MAINTAIN_ID = maintain.MAINTAIN_ID,
                        MAINTAIN_CYCLE = maintain.MAINTAIN_CYCLE,
                        MAINTAIN_DATE = maintain.MAINTAIN_DATE,
                        MAINTAIN_PERSON = maintain.MAINTAIN_PERSON,
                        MAINTAIN_CONTENT = maintain.MAINTAIN_CONTENT,
                        OCCUR_EVENT = maintain.OCCUR_EVENT,
                        MAINTAIN_NO = maintain.MAINTAIN_NO,
                        REMARK = maintain.REMARK
                    };
                    maintenanceRecords.Records.Add(maintainRecord);
                }
            }
            else
            {
                maintenanceRecords.Records.Add(new MaintenanceRecord());
            }

        }

        private void VerificationRecordsExchange(EquipmentInfoRecord<Verification> verificationRecords, EMS_EQUIPMENT_INFO equipment)
        {
            if (equipment.eMS_REPAIR_INFO is null)
            {
                verificationRecords.Records.Add(new Verification());
                return;
            }
            if (equipment.eMS_VERIFICATION_INFO.Where(x => x.VERIFICATION_STATE == "1").Count() > 0)
            {
                foreach (var verification in equipment.eMS_VERIFICATION_INFO.OrderByDescending(x => x.LAST_MTIME).Where(x => x.VERIFICATION_STATE == "1"))
                {
                    var verificationRecord = new Verification()
                    {
                        VERIFICATION_ID = verification.VERIFICATION_ID,
                        VERIFICATION_DATE = verification.VERIFICATION_DATE,
                        RELATION_EVENT = verification.RELATION_EVENT,
                        VERIFICATION_PERSON = verification.VERIFICATION_PERSON,
                        VERIFICATION_RESULT = verification.VERIFICATION_RESULT,
                        STATE = verification.STATE,
                        VERIFICATION_NO = verification.VERIFICATION_NO,
                        REMARK = verification.REMARK
                    };
                    verificationRecords.Records.Add(verificationRecord);
                }
            }
            else
            {
                verificationRecords.Records.Add(new Verification() { });
            }
        }

        private void FixRecordExchange(EquipmentInfoRecord<FixRecord>  fixRecords, EMS_EQUIPMENT_INFO equipment)
        {
            if (equipment.eMS_REPAIR_INFO is null)
            {
                fixRecords.Records.Add(new FixRecord());
                return;
            }
            if (equipment.eMS_REPAIR_INFO.Where(x => x.REPAIR_STATE == "1").Count() > 0)
            {
                foreach (var fix in equipment.eMS_REPAIR_INFO.OrderByDescending(x => x.LAST_MTIME).Where(x => x.REPAIR_STATE == "1"))
                {
                    var fixRecord = new FixRecord()
                    {
                        REPAIR_ID = fix.REPAIR_ID,
                        REPAIR_NO = fix.REPAIR_NO,
                        REPAIR_DATE = fix.REPAIR_DATE,
                        REPAIR_PERSON = fix.REPAIR_PERSON,
                        REPAIR_CONTENT = fix.REPAIR_CONTENT,
                        OCCUR_EVENT = fix.OCCUR_EVENT,
                        REPAIR_RESULT = fix.REPAIR_RESULT,
                        REMARK = fix.REMARK
                    };
                    fixRecords.Records.Add(fixRecord);
                }
            }
            else
            {
                fixRecords.Records.Add(new FixRecord());
            }
        }

        private List<string> GetEmsDocPaths(string docInfoId, string? docClass)
        {
            return equipmentContext.GetEmsDocPaths(docInfoId, docClass);
        }
        #endregion

    }





}
