using AutoMapper;
using AutoMapper.Configuration.Annotations;
using XH.H82.Models.BusinessModuleClient;

namespace XH.H82.Models.Dtos.Certificate;


[AutoMap(typeof(OA_BASE_DATA))]
public class CertificateTypeDto
{
        [SourceMember("DATA_ID")]
        public string Id { get; set; }
        [SourceMember("HOSPITAL_ID")]
        public string HospitalId { get; set; }
        [SourceMember("MODULE_ID")]
        public string ModuleId { get; set; }
        [SourceMember("FATHER_ID")]
        public string? FatherId { get; set; }
        [SourceMember("CLASS_ID")]
        public string? ClassId { get; set; }
        [SourceMember("DATA_SORT")]
        public string? DataSort { get; set; }
        [SourceMember("DATA_NAME")]
        public string? CertificateTypeName { get; set; }
        [SourceMember("DATA_SNAME")]
        public string? CertificateTypeCustomName { get; set; }
        [SourceMember("DATA_ENAME")]
        public string? CertificateTypeEnglishName { get; set; }
        [SourceMember("STANDART_ID")]
        public string? StandartId { get; set; }
        [SourceMember("CUSTOM_CODE")]
        public string? CustomCode { get; set; }
        [SourceMember("SPELL_CODE")]
        public string? SpellCode { get; set; }
        [SourceMember("STATE_FLAG")]
        public string? State { get; set; }
        [SourceMember("FIRST_RPERSON")]
        public string? FirstRperson { get; set; }
        [SourceMember("FIRST_RTIME")]
        public DateTime? FirstRtime { get; set; }
        [SourceMember("LAST_MPERSON")]
        public string? LastMperson { get; set; }
        [SourceMember("LAST_MTIME")]
        public DateTime? LastMtime { get; set; }
        [SourceMember("REMARK")]
        public string? Remark { get; set; }
        [SourceMember("ADDN_CONFIG_JSON")]
        public string? AddnConfigJson { get; set; }
}