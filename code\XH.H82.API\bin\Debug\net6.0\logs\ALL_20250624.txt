2025-06-24 17:29:44.605 +08:00 [INF] ==>App Start..2025-06-24 17:29:44
2025-06-24 17:29:44.810 +08:00 [INF] ==>当前模块:H82,6.25.225
2025-06-24 17:29:44.814 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-06-24 17:29:46.704 +08:00 [INF] ==>基础连接请求完成.
2025-06-24 17:29:47.125 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-06-24 17:29:47.608 +08:00 [INF]  正在通过S01写入版本信息...
2025-06-24 17:29:47.917 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-06-24 17:29:47.921 +08:00 [INF]  正在通过H04写入版本信息...
2025-06-24 17:29:48.266 +08:00 [INF] ==>版本写入成功:6.25.225
2025-06-24 17:29:50.164 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-06-24 17:29:50.319 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-06-24 17:29:50.985 +08:00 [INF] 正在检查旧数据是否完整...
2025-06-24 17:29:50.986 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-06-24 17:29:53.541 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-06-24 17:29:56.159 +08:00 [INF] ==>初始化完成..
2025-06-24 17:29:56.224 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-06-24 17:29:56.226 +08:00 [INF] 设备启用任务
2025-06-24 17:29:56.227 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-06-24 17:29:56.655 +08:00 [INF] 【SQL执行耗时:399.0543ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-06-24 17:29:56.860 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-06-24 17:29:56.881 +08:00 [INF] Now listening on: https://[::]:18482
2025-06-24 17:29:56.883 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-24 17:29:56.884 +08:00 [INF] Hosting environment: Development
2025-06-24 17:29:56.884 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-06-24 17:30:06.675 +08:00 [INF] HTTP GET /favicon.ico responded 404 in 206.0310 ms
2025-06-24 17:30:47.812 +08:00 [INF] HTTP POST /api/FileManage/UploadDocFile responded 401 in 174.1447 ms
2025-06-24 17:30:54.556 +08:00 [INF] 【SQL执行耗时:366.2315ms】

[Sql]:SELECT "DOC_ID","DOC_NAME","DOC_PATH","PDF_PREVIEW_PATH","DOC_SUFFIX","DOC_TYPE","EMS_INFO_ID","HOSPITAL_ID","DOC_CLASS","DOC_INFO_ID","EQUIPMENT_ID","DOC_FILE","UPLOAD_PERSON","UPLOAD_TIME","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","DOC_STATE","REMARK" FROM "XH_OA"."EMS_DOC_INFO"  WHERE ( "EQUIPMENT_ID" = :EQUIPMENT_ID0 )  AND ( "DOC_STATE" = :DOC_STATE1 )  AND ( "DOC_CLASS" = :DOC_CLASS2 ) 
[Pars]:
[Name]::EQUIPMENT_ID0 [Value]:5E2CB139A7A44773A1934C2D7B3092BB [Type]:String    
[Name]::DOC_STATE1 [Value]:1 [Type]:String    
[Name]::DOC_CLASS2 [Value]:设备说明书 [Type]:String    

2025-06-24 17:30:55.243 +08:00 [ERR] pdf转图片报错:Invalid/Unknown/Unsupported format
2025-06-24 17:30:55.251 +08:00 [INF] 调用S28模块前[/api/Common/SetUploadFile?folderName=EMS/datafile],耗时:3ms
2025-06-24 17:30:55.435 +08:00 [INF] 调用S28模块[/api/Common/SetUploadFile?folderName=EMS/datafile],耗时:184ms
2025-06-24 17:30:55.436 +08:00 [ERR] 调用S28模块[/api/Common/SetUploadFile?folderName=EMS/datafile]请求完成,但是返回了错误:没有找到需要上传的数据！！
2025-06-24 17:30:55.510 +08:00 [ERR] 操作失败:
没有找到需要上传的数据！！
2025-06-24 17:30:55.523 +08:00 [INF] HTTP POST /api/FileManage/UploadDocFile responded 200 in 5839.1165 ms
2025-06-24 17:30:55.524 +08:00 [INF] 【接口超时阀值预警】 [16a119be3ae064c9a06df33f33789cbb]接口/api/FileManage/UploadDocFile,耗时:[5839]毫秒
2025-06-24 17:31:06.920 +08:00 [INF] 【SQL执行耗时:400.875ms】

[Sql]:SELECT "DOC_ID","DOC_NAME","DOC_PATH","PDF_PREVIEW_PATH","DOC_SUFFIX","DOC_TYPE","EMS_INFO_ID","HOSPITAL_ID","DOC_CLASS","DOC_INFO_ID","EQUIPMENT_ID","DOC_FILE","UPLOAD_PERSON","UPLOAD_TIME","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","DOC_STATE","REMARK" FROM "XH_OA"."EMS_DOC_INFO"  WHERE ( "EQUIPMENT_ID" = :EQUIPMENT_ID0 )  AND ( "DOC_STATE" = :DOC_STATE1 )  AND ( "DOC_CLASS" = :DOC_CLASS2 ) 
[Pars]:
[Name]::EQUIPMENT_ID0 [Value]:5E2CB139A7A44773A1934C2D7B3092BB [Type]:String    
[Name]::DOC_STATE1 [Value]:1 [Type]:String    
[Name]::DOC_CLASS2 [Value]:设备说明书 [Type]:String    

2025-06-24 17:31:20.107 +08:00 [ERR] pdf转图片报错:Invalid/Unknown/Unsupported format
2025-06-24 17:31:35.670 +08:00 [INF] 调用S28模块前[/api/Common/SetUploadFile?folderName=EMS/datafile],耗时:0ms
2025-06-24 17:31:35.882 +08:00 [INF] 调用S28模块[/api/Common/SetUploadFile?folderName=EMS/datafile],耗时:212ms
2025-06-24 17:31:35.882 +08:00 [ERR] 调用S28模块[/api/Common/SetUploadFile?folderName=EMS/datafile]请求完成,但是返回了错误:没有找到需要上传的数据！！
2025-06-24 17:31:38.820 +08:00 [ERR] 操作失败:
没有找到需要上传的数据！！
2025-06-24 17:31:38.822 +08:00 [INF] HTTP POST /api/FileManage/UploadDocFile responded 200 in 32354.0271 ms
2025-06-24 17:31:38.822 +08:00 [INF] 【接口超时阀值预警】 [dda321942b0f7aa17f6756e811cb3e0b]接口/api/FileManage/UploadDocFile,耗时:[32354]毫秒
2025-06-24 17:32:48.572 +08:00 [INF] 【SQL执行耗时:465.2546ms】

[Sql]:SELECT "DOC_ID","DOC_NAME","DOC_PATH","PDF_PREVIEW_PATH","DOC_SUFFIX","DOC_TYPE","EMS_INFO_ID","HOSPITAL_ID","DOC_CLASS","DOC_INFO_ID","EQUIPMENT_ID","DOC_FILE","UPLOAD_PERSON","UPLOAD_TIME","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","DOC_STATE","REMARK" FROM "XH_OA"."EMS_DOC_INFO"  WHERE ( "EQUIPMENT_ID" = :EQUIPMENT_ID0 )  AND ( "DOC_STATE" = :DOC_STATE1 )  AND ( "DOC_CLASS" = :DOC_CLASS2 ) 
[Pars]:
[Name]::EQUIPMENT_ID0 [Value]:5E2CB139A7A44773A1934C2D7B3092BB [Type]:String    
[Name]::DOC_STATE1 [Value]:1 [Type]:String    
[Name]::DOC_CLASS2 [Value]:设备说明书 [Type]:String    

2025-06-24 17:32:53.006 +08:00 [ERR] pdf转图片报错:Invalid/Unknown/Unsupported format
2025-06-24 17:33:00.613 +08:00 [INF] 调用S28模块前[/api/Common/SetUploadFile?folderName=EMS/datafile],耗时:0ms
2025-06-24 17:33:00.947 +08:00 [INF] 调用S28模块[/api/Common/SetUploadFile?folderName=EMS/datafile],耗时:333ms
2025-06-24 17:33:05.076 +08:00 [INF] 【SQL执行耗时:52.3115ms】

[Sql]:INSERT INTO "XH_OA"."EMS_DOC_INFO"  
           ("DOC_ID","DOC_NAME","DOC_PATH","PDF_PREVIEW_PATH","DOC_SUFFIX","DOC_TYPE","EMS_INFO_ID","HOSPITAL_ID","DOC_CLASS","DOC_INFO_ID","EQUIPMENT_ID","DOC_FILE","UPLOAD_PERSON","UPLOAD_TIME","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","DOC_STATE","REMARK")
     VALUES
           (:DOC_ID,:DOC_NAME,:DOC_PATH,:PDF_PREVIEW_PATH,:DOC_SUFFIX,:DOC_TYPE,:EMS_INFO_ID,:HOSPITAL_ID,:DOC_CLASS,:DOC_INFO_ID,:EQUIPMENT_ID,:DOC_FILE,:UPLOAD_PERSON,:UPLOAD_TIME,:FIRST_RPERSON,:FIRST_RTIME,:LAST_MPERSON,:LAST_MTIME,:DOC_STATE,:REMARK)  
[Pars]:
[Name]::DOC_ID [Value]:073DB89C111C4663B9C9B39D77970C8A [Type]:String    
[Name]::DOC_NAME [Value]:1a62b4d1effffa8d557a7aee23ab1b54 [Type]:String    
[Name]::DOC_PATH [Value]:/33A001/H91/dmis/sys_file/2025/b1e27134094f41b7a3e0cbdf44c05ee9.png [Type]:String    
[Name]::PDF_PREVIEW_PATH [Value]:/EMS/datafile/1a62b4d1effffa8d557a7aee23ab1b54_21509cfbcd2b4cb69bac0a27ed0a40d3预览.jpg [Type]:String    
[Name]::DOC_SUFFIX [Value]:.png [Type]:String    
[Name]::DOC_TYPE [Value]:IMG [Type]:String    
[Name]::EMS_INFO_ID [Value]: [Type]:String    
[Name]::HOSPITAL_ID [Value]:33A001 [Type]:String    
[Name]::DOC_CLASS [Value]:设备说明书 [Type]:String    
[Name]::DOC_INFO_ID [Value]: [Type]:String    
[Name]::EQUIPMENT_ID [Value]:5E2CB139A7A44773A1934C2D7B3092BB [Type]:String    
[Name]::DOC_FILE [Value]:4505 [Type]:String    
[Name]::UPLOAD_PERSON [Value]:fr_李影 [Type]:String    
[Name]::UPLOAD_TIME [Value]:2025/6/24 17:32:48 [Type]:DateTime    
[Name]::FIRST_RPERSON [Value]:fr_李影 [Type]:String    
[Name]::FIRST_RTIME [Value]:2025/6/24 17:32:48 [Type]:DateTime    
[Name]::LAST_MPERSON [Value]:fr_李影 [Type]:String    
[Name]::LAST_MTIME [Value]:2025/6/24 17:32:48 [Type]:DateTime    
[Name]::DOC_STATE [Value]:1 [Type]:String    
[Name]::REMARK [Value]: [Type]:String    

2025-06-24 17:33:05.156 +08:00 [INF] 【SQL执行耗时:40.8472ms】

[Sql]:Begin
UPDATE "XH_OA"."EMS_DOC_INFO" SET "DOC_NAME"=N'1a62b4d1effffa8d557a7aee23ab1b54' ,"DOC_PATH"=N'/33A001/H91/dmis/sys_file/2025/b1e27134094f41b7a3e0cbdf44c05ee9.png' ,"PDF_PREVIEW_PATH"=N'/EMS/datafile/1a62b4d1effffa8d557a7aee23ab1b54_21509cfbcd2b4cb69bac0a27ed0a40d3预览.jpg' ,"DOC_SUFFIX"=N'.png' ,"DOC_TYPE"=N'IMG' ,"EMS_INFO_ID"=NULL ,"HOSPITAL_ID"=N'33A001' ,"DOC_CLASS"=N'设备说明书' ,"DOC_INFO_ID"=NULL ,"EQUIPMENT_ID"=N'5E2CB139A7A44773A1934C2D7B3092BB' ,"DOC_FILE"=N'4505' ,"UPLOAD_PERSON"=N'fr_李影' ,"UPLOAD_TIME"=to_timestamp('2025-06-24 17:32:48.613782', 'YYYY-MM-DD HH24:MI:SS.FF')  ,"FIRST_RPERSON"=N'fr_李影' ,"FIRST_RTIME"=to_timestamp('2025-06-24 17:32:48.613783', 'YYYY-MM-DD HH24:MI:SS.FF')  ,"LAST_MPERSON"=N'fr_李影' ,"LAST_MTIME"=to_timestamp('2025-06-24 17:32:48.613783', 'YYYY-MM-DD HH24:MI:SS.FF')  ,"DOC_STATE"=N'1' ,"REMARK"=NULL  WHERE  "DOC_ID"='073DB89C111C4663B9C9B39D77970C8A' ;
End;
 
[Pars]:
[Name]::DOC_ID [Value]:073DB89C111C4663B9C9B39D77970C8A [Type]:String    
[Name]::DOC_NAME [Value]:1a62b4d1effffa8d557a7aee23ab1b54 [Type]:String    
[Name]::DOC_PATH [Value]:/33A001/H91/dmis/sys_file/2025/b1e27134094f41b7a3e0cbdf44c05ee9.png [Type]:String    
[Name]::PDF_PREVIEW_PATH [Value]:/EMS/datafile/1a62b4d1effffa8d557a7aee23ab1b54_21509cfbcd2b4cb69bac0a27ed0a40d3预览.jpg [Type]:String    
[Name]::DOC_SUFFIX [Value]:.png [Type]:String    
[Name]::DOC_TYPE [Value]:IMG [Type]:String    
[Name]::EMS_INFO_ID [Value]: [Type]:String    
[Name]::HOSPITAL_ID [Value]:33A001 [Type]:String    
[Name]::DOC_CLASS [Value]:设备说明书 [Type]:String    
[Name]::DOC_INFO_ID [Value]: [Type]:String    
[Name]::EQUIPMENT_ID [Value]:5E2CB139A7A44773A1934C2D7B3092BB [Type]:String    
[Name]::DOC_FILE [Value]:4505 [Type]:String    
[Name]::UPLOAD_PERSON [Value]:fr_李影 [Type]:String    
[Name]::UPLOAD_TIME [Value]:2025/6/24 17:32:48 [Type]:DateTime    
[Name]::FIRST_RPERSON [Value]:fr_李影 [Type]:String    
[Name]::FIRST_RTIME [Value]:2025/6/24 17:32:48 [Type]:DateTime    
[Name]::LAST_MPERSON [Value]:fr_李影 [Type]:String    
[Name]::LAST_MTIME [Value]:2025/6/24 17:32:48 [Type]:DateTime    
[Name]::DOC_STATE [Value]:1 [Type]:String    
[Name]::REMARK [Value]: [Type]:String    

2025-06-24 17:33:05.240 +08:00 [INF] HTTP POST /api/FileManage/UploadDocFile responded 200 in 17179.4704 ms
2025-06-24 17:33:05.240 +08:00 [INF] 【接口超时阀值预警】 [0de292779ed4bf5a0b1ca003e17ada28]接口/api/FileManage/UploadDocFile,耗时:[17179]毫秒
2025-06-24 18:22:01.250 +08:00 [INF] ==>App Start..2025-06-24 18:22:01
2025-06-24 18:22:01.427 +08:00 [INF] ==>当前模块:H82,6.25.225
2025-06-24 18:22:01.430 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-06-24 18:22:02.965 +08:00 [INF] ==>基础连接请求完成.
2025-06-24 18:22:03.333 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-06-24 18:22:03.802 +08:00 [INF]  正在通过S01写入版本信息...
2025-06-24 18:22:04.150 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-06-24 18:22:04.152 +08:00 [INF]  正在通过H04写入版本信息...
2025-06-24 18:22:04.500 +08:00 [INF] ==>版本写入成功:6.25.225
2025-06-24 18:22:05.121 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-06-24 18:22:05.248 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-06-24 18:22:05.693 +08:00 [INF] 正在检查旧数据是否完整...
2025-06-24 18:22:05.694 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-06-24 18:22:06.831 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-06-24 18:22:09.135 +08:00 [INF] ==>初始化完成..
2025-06-24 18:22:09.157 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-06-24 18:22:09.160 +08:00 [INF] 设备启用任务
2025-06-24 18:22:09.161 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-06-24 18:22:09.582 +08:00 [INF] 【SQL执行耗时:398.5921ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-06-24 18:22:09.775 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-06-24 18:22:09.791 +08:00 [INF] Now listening on: https://[::]:18482
2025-06-24 18:22:09.793 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-24 18:22:09.793 +08:00 [INF] Hosting environment: Development
2025-06-24 18:22:09.794 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-06-24 18:26:23.999 +08:00 [INF] HTTP POST /api/AuthorizationRecord/AddAuthorizeRecord/DBA5C9027E7F463F99B46673F5D6ECFA responded 401 in 292.9203 ms
2025-06-24 18:26:29.849 +08:00 [INF] 【SQL执行耗时:397.6854ms】

[Sql]:SELECT "DATA_ID","DATA_CNAME","HOSPITAL_ID","LAB_ID","CLASS_ID","DATA_SORT","DATA_ENAME","HIS_ID","CUSTOM_CODE","SPELL_CODE","DATA_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","DATA_SNAME","DATA_SOURCE","ONE_CLASS","DATA_UNAME","IF_REPEAT","SYSTEM_ID" FROM "XH_SYS"."SYS6_BASE_DATA"  WHERE ( "CLASS_ID" = :CLASS_ID0 ) 
[Pars]:
[Name]::CLASS_ID0 [Value]:职务 [Type]:String    

2025-06-24 18:26:30.267 +08:00 [INF] 【SQL执行耗时:352.6341ms】

[Sql]: SELECT  "LOGID" AS "LOGID" , "USERNAME" AS "USERNAME" , "POWER" AS "POWER" , "DEPT_CODE" AS "DEPT_CODE"   FROM "XH_SYS"."SYS6_USER"  WHERE ( "USER_NO" = :USER_NO0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::KWUSER_NO0_01 [Value]:LIS510360064 [Type]:String    

2025-06-24 18:26:37.266 +08:00 [ERR] 未处理的异常::System.NullReferenceException: Object reference not set to an instance of an object.
   at XH.H82.Services.AuthorizationRecord.AuthorizationRecordService.AddAuthorizationRecord(String equipmentId, AuthorizationRecordDto input) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\AuthorizationRecord\AuthorizationRecordService.cs:line 152
   at Castle.Proxies.Invocations.IAuthorizationRecordService_AddAuthorizationRecord.InvokeMethodOnTarget()
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.UseCacheWhenFailedAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.CacheAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at Castle.Proxies.IAuthorizationRecordServiceProxy.AddAuthorizationRecord(String equipmentId, AuthorizationRecordDto input)
   at XH.H82.API.Controllers.AuthorizationRecord.AuthorizationRecordController.AddAuthorizeRecord(String equipmentId, AuthorizationRecordDto input) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\Controllers\AuthorizationRecord\AuthorizationRecordController.cs:line 111
   at lambda_method908(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-06-24 18:26:37.270 +08:00 [ERR] HTTP POST /api/AuthorizationRecord/AddAuthorizeRecord/DBA5C9027E7F463F99B46673F5D6ECFA responded 500 in 10674.4972 ms
2025-06-24 18:26:37.272 +08:00 [INF] 【接口超时阀值预警】 [6435ecbd0df1c29192ccac65fb749ffc]接口/api/AuthorizationRecord/AddAuthorizeRecord/DBA5C9027E7F463F99B46673F5D6ECFA,耗时:[10675]毫秒
2025-06-24 18:29:16.350 +08:00 [INF] 第三方url地址为：http://************
2025-06-24 18:30:54.520 +08:00 [INF] ==>App Start..2025-06-24 18:30:54
2025-06-24 18:30:54.702 +08:00 [INF] ==>当前模块:H82,6.25.225
2025-06-24 18:30:54.706 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-06-24 18:30:55.337 +08:00 [ERR] ==>请求基础连接时发生错误:不知道这样的主机。 (d01.lis-china.com:18801)
System.Net.Http.HttpRequestException: 不知道这样的主机。 (d01.lis-china.com:18801)
 ---> System.Net.Sockets.SocketException (11001): 不知道这样的主机。
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|277_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at RestSharp.ResponseThrowExtension.ThrowIfError(RestResponse response)
   at RestSharp.RestClient.ExecuteAsync(RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.ExecuteAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.GetAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.AsyncHelpers.<>c__DisplayClass1_0`1.<<RunSync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.<Run>g__PostCallback|7_0(Object _)
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.Run()
   at RestSharp.AsyncHelpers.RunSync(Func`1 task)
   at RestSharp.AsyncHelpers.RunSync[T](Func`1 task)
   at RestSharp.RestClientExtensions.Get[T](IRestClient client, RestRequest request)
   at H.BASE.Infrastructure.BaseConfigRequestHelper.GetBaseConfigFromBaseService(String urlS01, String hospitalId, String moduleId)
2025-06-24 18:30:55.411 +08:00 [WRN] 调用[S01]获取基础连接配置失败 将在：2秒后重试
2025-06-24 18:30:57.424 +08:00 [ERR] ==>请求基础连接时发生错误:不知道这样的主机。 (d01.lis-china.com:18801)
System.Net.Http.HttpRequestException: 不知道这样的主机。 (d01.lis-china.com:18801)
 ---> System.Net.Sockets.SocketException (11001): 不知道这样的主机。
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|277_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at RestSharp.ResponseThrowExtension.ThrowIfError(RestResponse response)
   at RestSharp.RestClient.ExecuteAsync(RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.ExecuteAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.GetAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.AsyncHelpers.<>c__DisplayClass1_0`1.<<RunSync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.<Run>g__PostCallback|7_0(Object _)
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.Run()
   at RestSharp.AsyncHelpers.RunSync(Func`1 task)
   at RestSharp.AsyncHelpers.RunSync[T](Func`1 task)
   at RestSharp.RestClientExtensions.Get[T](IRestClient client, RestRequest request)
   at H.BASE.Infrastructure.BaseConfigRequestHelper.GetBaseConfigFromBaseService(String urlS01, String hospitalId, String moduleId)
2025-06-24 18:30:57.429 +08:00 [WRN] 调用[S01]获取基础连接配置失败 将在：2秒后重试
2025-06-24 18:30:59.448 +08:00 [ERR] ==>请求基础连接时发生错误:不知道这样的主机。 (d01.lis-china.com:18801)
System.Net.Http.HttpRequestException: 不知道这样的主机。 (d01.lis-china.com:18801)
 ---> System.Net.Sockets.SocketException (11001): 不知道这样的主机。
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|277_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at RestSharp.ResponseThrowExtension.ThrowIfError(RestResponse response)
   at RestSharp.RestClient.ExecuteAsync(RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.ExecuteAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.GetAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.AsyncHelpers.<>c__DisplayClass1_0`1.<<RunSync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.<Run>g__PostCallback|7_0(Object _)
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.Run()
   at RestSharp.AsyncHelpers.RunSync(Func`1 task)
   at RestSharp.AsyncHelpers.RunSync[T](Func`1 task)
   at RestSharp.RestClientExtensions.Get[T](IRestClient client, RestRequest request)
   at H.BASE.Infrastructure.BaseConfigRequestHelper.GetBaseConfigFromBaseService(String urlS01, String hospitalId, String moduleId)
2025-06-24 18:30:59.453 +08:00 [WRN] 调用[S01]获取基础连接配置失败 将在：2秒后重试
2025-06-24 18:31:02.655 +08:00 [INF] ==>基础连接请求完成.
2025-06-24 18:31:03.009 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-06-24 18:31:03.330 +08:00 [INF]  正在通过S01写入版本信息...
2025-06-24 18:31:03.655 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-06-24 18:31:03.656 +08:00 [INF]  正在通过H04写入版本信息...
2025-06-24 18:31:03.998 +08:00 [INF] ==>版本写入成功:6.25.225
2025-06-24 18:31:04.371 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-06-24 18:31:04.459 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-06-24 18:31:04.872 +08:00 [INF] 正在检查旧数据是否完整...
2025-06-24 18:31:04.872 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-06-24 18:31:05.750 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-06-24 18:31:07.834 +08:00 [INF] ==>初始化完成..
2025-06-24 18:31:07.854 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-06-24 18:31:07.856 +08:00 [INF] 设备启用任务
2025-06-24 18:31:07.857 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-06-24 18:31:08.268 +08:00 [INF] 【SQL执行耗时:391.4396ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-06-24 18:31:08.403 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-06-24 18:31:08.417 +08:00 [INF] Now listening on: https://[::]:18482
2025-06-24 18:31:08.418 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-24 18:31:08.418 +08:00 [INF] Hosting environment: Development
2025-06-24 18:31:08.419 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-06-24 18:31:14.676 +08:00 [INF] 第三方url地址为：http://************
2025-06-24 18:31:28.606 +08:00 [ERR] 未处理的异常::System.Exception: No support value(XH.H82.Services.EquipmentDocService+<>c__DisplayClass44_1).authPerson[1] Exception has been thrown by the target of an invocation.
   at SqlSugar.ExpressionTool.DynamicInvoke(Expression expression, MemberExpression memberExpression)
   at SqlSugar.BinaryExpressionResolve.Other(ExpressionParameter parameter)
   at SqlSugar.BinaryExpressionResolve..ctor(ExpressionParameter parameter)
   at SqlSugar.BaseResolve.Start()
   at SqlSugar.BinaryExpressionResolve.Right(ExpressionParameter parameter, String operatorValue, Boolean isEqual, Expression rightExpression, Boolean lsbs)
   at SqlSugar.BinaryExpressionResolve.DefaultBinary(ExpressionParameter parameter, BinaryExpression expression, String operatorValue)
   at SqlSugar.BinaryExpressionResolve.Other(ExpressionParameter parameter)
   at SqlSugar.BinaryExpressionResolve..ctor(ExpressionParameter parameter)
   at SqlSugar.BaseResolve.Start()
   at SqlSugar.LambdaExpressionResolve..ctor(ExpressionParameter parameter)
   at SqlSugar.BaseResolve.Start()
   at SqlSugar.ExpressionContext.Resolve(Expression expression, ResolveExpressType resolveType)
   at SqlSugar.QueryBuilder.GetExpressionValue(Expression expression, ResolveExpressType resolveType)
   at SqlSugar.QueryableProvider`1._Where(Expression expression)
   at SqlSugar.QueryableProvider`1.Where(Expression`1 expression)
   at XH.H82.Services.EquipmentDocService.GetAuthorizeList(String equipmentId) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentDocService.cs:line 1063
   at Castle.Proxies.Invocations.IEquipmentDocService_GetAuthorizeList.InvokeMethodOnTarget()
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.UseCacheWhenFailedAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.CacheAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at Castle.Proxies.IEquipmentDocServiceProxy.GetAuthorizeList(String equipmentId)
   at XH.H82.API.Controllers.EquipmentDocController.GetAuthorizeList(String equipmentId) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\Controllers\EquipmentDocController.cs:line 612
   at lambda_method908(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-06-24 18:31:28.629 +08:00 [ERR] HTTP GET /api/EquipmentDoc/GetAuthorizeList responded 500 in 17340.8449 ms
2025-06-24 18:31:28.631 +08:00 [INF] 【接口超时阀值预警】 [7324eec166d419f62b0ccc2947f85ce8]接口/api/EquipmentDoc/GetAuthorizeList,耗时:[17359]毫秒
2025-06-24 18:31:33.467 +08:00 [INF] 第三方url地址为：http://************
2025-06-24 18:36:48.103 +08:00 [INF] ==>App Start..2025-06-24 18:36:48
2025-06-24 18:36:48.268 +08:00 [INF] ==>当前模块:H82,6.25.225
2025-06-24 18:36:48.271 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-06-24 18:36:49.073 +08:00 [ERR] ==>请求基础连接时发生错误:不知道这样的主机。 (d01.lis-china.com:18801)
System.Net.Http.HttpRequestException: 不知道这样的主机。 (d01.lis-china.com:18801)
 ---> System.Net.Sockets.SocketException (11001): 不知道这样的主机。
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|277_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at RestSharp.ResponseThrowExtension.ThrowIfError(RestResponse response)
   at RestSharp.RestClient.ExecuteAsync(RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.ExecuteAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.GetAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.AsyncHelpers.<>c__DisplayClass1_0`1.<<RunSync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.<Run>g__PostCallback|7_0(Object _)
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.Run()
   at RestSharp.AsyncHelpers.RunSync(Func`1 task)
   at RestSharp.AsyncHelpers.RunSync[T](Func`1 task)
   at RestSharp.RestClientExtensions.Get[T](IRestClient client, RestRequest request)
   at H.BASE.Infrastructure.BaseConfigRequestHelper.GetBaseConfigFromBaseService(String urlS01, String hospitalId, String moduleId)
2025-06-24 18:36:49.182 +08:00 [WRN] 调用[S01]获取基础连接配置失败 将在：2秒后重试
2025-06-24 18:36:51.196 +08:00 [ERR] ==>请求基础连接时发生错误:不知道这样的主机。 (d01.lis-china.com:18801)
System.Net.Http.HttpRequestException: 不知道这样的主机。 (d01.lis-china.com:18801)
 ---> System.Net.Sockets.SocketException (11001): 不知道这样的主机。
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|277_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at RestSharp.ResponseThrowExtension.ThrowIfError(RestResponse response)
   at RestSharp.RestClient.ExecuteAsync(RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.ExecuteAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.GetAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.AsyncHelpers.<>c__DisplayClass1_0`1.<<RunSync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.<Run>g__PostCallback|7_0(Object _)
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.Run()
   at RestSharp.AsyncHelpers.RunSync(Func`1 task)
   at RestSharp.AsyncHelpers.RunSync[T](Func`1 task)
   at RestSharp.RestClientExtensions.Get[T](IRestClient client, RestRequest request)
   at H.BASE.Infrastructure.BaseConfigRequestHelper.GetBaseConfigFromBaseService(String urlS01, String hospitalId, String moduleId)
2025-06-24 18:36:51.201 +08:00 [WRN] 调用[S01]获取基础连接配置失败 将在：2秒后重试
2025-06-24 18:36:53.214 +08:00 [ERR] ==>请求基础连接时发生错误:不知道这样的主机。 (d01.lis-china.com:18801)
System.Net.Http.HttpRequestException: 不知道这样的主机。 (d01.lis-china.com:18801)
 ---> System.Net.Sockets.SocketException (11001): 不知道这样的主机。
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|277_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at RestSharp.ResponseThrowExtension.ThrowIfError(RestResponse response)
   at RestSharp.RestClient.ExecuteAsync(RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.ExecuteAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.GetAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.AsyncHelpers.<>c__DisplayClass1_0`1.<<RunSync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.<Run>g__PostCallback|7_0(Object _)
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.Run()
   at RestSharp.AsyncHelpers.RunSync(Func`1 task)
   at RestSharp.AsyncHelpers.RunSync[T](Func`1 task)
   at RestSharp.RestClientExtensions.Get[T](IRestClient client, RestRequest request)
   at H.BASE.Infrastructure.BaseConfigRequestHelper.GetBaseConfigFromBaseService(String urlS01, String hospitalId, String moduleId)
2025-06-24 18:36:53.219 +08:00 [WRN] 调用[S01]获取基础连接配置失败 将在：2秒后重试
2025-06-24 18:36:57.523 +08:00 [INF] ==>基础连接请求完成.
2025-06-24 18:36:57.990 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-06-24 18:36:58.366 +08:00 [INF]  正在通过S01写入版本信息...
2025-06-24 18:36:58.684 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-06-24 18:36:58.686 +08:00 [INF]  正在通过H04写入版本信息...
2025-06-24 18:36:59.646 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-06-24 18:36:59.745 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-06-24 18:37:00.209 +08:00 [INF] 正在检查旧数据是否完整...
2025-06-24 18:37:00.217 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-06-24 18:37:01.150 +08:00 [INF] ==>版本写入成功:6.25.225
2025-06-24 18:37:01.238 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-06-24 18:37:04.538 +08:00 [INF] ==>初始化完成..
2025-06-24 18:37:04.560 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-06-24 18:37:04.561 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-06-24 18:37:04.561 +08:00 [INF] 设备启用任务
2025-06-24 18:37:04.965 +08:00 [INF] 【SQL执行耗时:390.6207ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-06-24 18:37:05.349 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-06-24 18:37:05.364 +08:00 [INF] Now listening on: https://[::]:18482
2025-06-24 18:37:05.365 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-24 18:37:05.365 +08:00 [INF] Hosting environment: Development
2025-06-24 18:37:05.366 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-06-24 18:37:15.687 +08:00 [INF] 第三方url地址为：http://************
2025-06-24 18:37:18.881 +08:00 [INF] HTTP GET /api/EquipmentDoc/GetAuthorizeList responded 200 in 6932.5259 ms
2025-06-24 18:37:18.885 +08:00 [INF] 【接口超时阀值预警】 [f0c1ec7f2fb1d428c679e4c58ef4fe67]接口/api/EquipmentDoc/GetAuthorizeList,耗时:[6939]毫秒
2025-06-24 18:42:36.862 +08:00 [INF] ==>App Start..2025-06-24 18:42:36
2025-06-24 18:42:37.053 +08:00 [INF] ==>当前模块:H82,6.25.225
2025-06-24 18:42:37.056 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-06-24 18:42:38.769 +08:00 [INF] ==>基础连接请求完成.
2025-06-24 18:42:39.149 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-06-24 18:42:39.505 +08:00 [INF]  正在通过S01写入版本信息...
2025-06-24 18:42:39.859 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-06-24 18:42:39.861 +08:00 [INF]  正在通过H04写入版本信息...
2025-06-24 18:42:40.200 +08:00 [INF] ==>版本写入成功:6.25.225
2025-06-24 18:42:40.730 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-06-24 18:42:40.838 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-06-24 18:42:41.310 +08:00 [INF] 正在检查旧数据是否完整...
2025-06-24 18:42:41.310 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-06-24 18:42:42.256 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-06-24 18:42:44.808 +08:00 [INF] ==>初始化完成..
2025-06-24 18:42:44.829 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-06-24 18:42:44.831 +08:00 [INF] 设备启用任务
2025-06-24 18:42:44.832 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-06-24 18:42:45.234 +08:00 [INF] 【SQL执行耗时:381.7566ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-06-24 18:42:45.369 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-06-24 18:42:45.383 +08:00 [INF] Now listening on: https://[::]:18482
2025-06-24 18:42:45.384 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-24 18:42:45.384 +08:00 [INF] Hosting environment: Development
2025-06-24 18:42:45.384 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
