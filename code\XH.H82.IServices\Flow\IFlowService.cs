﻿using XH.H82.Models.Flow;

namespace XH.H82.Services;

public interface IFlowService
{
    /// <summary>
    /// 根据流程编号获取流程模板详情
    /// </summary>
    /// <param name="flowCode"></param>
    /// <returns></returns>
    public FlowDto GetFlowDetailByCode(string flowCode);
    
    /// <summary>
    /// 根据实例编号获取流程详情
    /// </summary>
    /// <param name="flowInstanceId"></param>
    /// <returns></returns>
    public FlowInstanceDto GetFlowInstanceDetail(string flowInstanceId);
    
    /// <summary>
    /// 初始化流程
    /// </summary>
    /// <param name="flowCode"></param>
    /// <param name="userNos"></param>
    /// <returns> 实例id </returns>
    public string InitFlow(string flowCode, List<string> userNos);

    /// <summary>
    /// 执行到下一个流程节点
    /// </summary>
    /// <param name="flowInstanceId">实例id</param>
    /// <param name="flowInstanceNodeId">当前节点流程id</param>
    /// <param name="approvalInput">相关操作输入</param>
    /// <returns></returns>
    public bool NextFlow(string flowInstanceId, string flowInstanceNodeId, ApprovalInput approvalInput);

    
    /// <summary>
    /// 审核通过
    /// </summary>
    /// <param name="flowInstanceId">实例id</param>
    /// <param name="approvedRemark">审核意见</param>
    /// <param name="approvedUserNos"> 下一级 审核人（多个） </param>
    public bool ApprovedFlow(string flowInstanceId, string approvedRemark, List<string> approvedUserNos);

    /// <summary>
    /// 驳回
    /// </summary>
    /// <param name="flowInstanceId">流程实例id</param>
    /// <param name="rejectInfo">驳回原因</param>
    /// <param name="nodeId">指定的节点id（固定的数据）</param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    bool RejectFlow(string flowInstanceId, string rejectInfo, string nodeId);

    /// <summary>
    /// 拒绝
    /// </summary>
    /// <param name="flowInstanceId">流程实例id</param>
    /// <param name="refusedInfo">不通过理由</param>
    /// <param name="userNos">审批人</param>
    /// <returns></returns>
    bool RefusedFlow( string flowInstanceId, string refusedInfo , List<string> userNos);
}