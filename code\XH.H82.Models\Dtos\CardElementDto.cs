﻿using AutoMapper;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XH.H82.Models.Entities;

namespace XH.H82.Models.Dtos
{
    [AutoMap(typeof(EMS_EQUIPMENT_INFO))]
    public class CardElementDto
    {
        public string EQUIPMENT_ID { get; set; }
        public string UNIT_ID { get; set; }
        public string HOSPITAL_ID { get; set; }
        public string LAB_ID { get; set; }
        public string PROFESSIONAL_CLASS { get; set; }
        public string INSTRUMENT_ID { get; set; }
        public string ESERIES_ID { get; set; }
        public string EQUIPMENT_NUM { get; set; }
        public string EQUIPMENT_NAME { get; set; }
        public string DEPT_SECTION_NO { get; set; }
        public string EQUIPMENT_ENAME { get; set; }
        public string EQUIPMENT_MODEL { get; set; }
        public string VEST_PIPELINE { get; set; }
        public string EQUIPMENT_CLASS { get; set; }
        public string DEPT_NAME { get; set; }
        public string SECTION_NO { get; set; }
        public string EQUIPMENT_SORT { get; set; }
        public string FACTORY_NUM { get; set; }
        public string EQUIPMENT_FEATURE { get; set; }
        public string BUY_DATE { get; set; }
        public double? SELL_PRICE { get; set; }
        public string KEEP_PERSON { get; set; }
        public string INSTALL_DATE { get; set; }
        public string INSTALL_AREA { get; set; }
        public string DEPRECIATION_TIME { get; set; }
        public string ANNUAL_SURVEY_DATE { get; set; }
        public string MANUFACTURER { get; set; }
        public string DEALER { get; set; }
        public string REPAIR_COMPANY { get; set; }
        public string APPLY_STATE { get; set; }
        public string CERTIFICATE_STATE { get; set; }
        public string ACCEPT_REPORT_STATE { get; set; }
        public string EQUIPMENT_GRAPH_STATE { get; set; }
        public string MANUAL_STATE { get; set; }
        public string REPAIR_PERSON { get; set; }
        public string REPAIR_PERSON_STATE { get; set; }
        public string CONTACT_PHONE { get; set; }
        public string REGISTER_PERSON { get; set; }
        public string REGISTER_TIME { get; set; }
        public string REGISTRATION_NUM { get; set; }
        public string REGISTRATION_ENUM { get; set; }

        /// <summary>
        /// 设备状态：0-未启用；1-启用；2-停用 3-报废
        /// </summary>
        public string EQUIPMENT_STATE { get; set; }
        public string FIRST_RPERSON { get; set; }
        public string FIRST_RTIME { get; set; }
        public string LAST_MPERSON { get; set; }
        public string LAST_MTIME { get; set; }
        public string REMARK { get; set; }
        public string MANUFACTURER_ID { get; set; }
        public string DEALER_ID { get; set; }
        public string EQUIPMENT_SIZE { get; set; }
        public string EQUIPMENT_POWER { get; set; }
        public string EQUIPMENT_VOLTAGE { get; set; }
        public string EQUIPMENT_TEMP { get; set; }
        public string EQUIPMENT_TEMP_RANGE { get; set; }
        /// <summary>
        /// 设备负责人
        /// </summary>
        public string EQ_IN_PERSON { get; set; }
        public string EQ_IN_TIME { get; set; }
        public string EQ_OUT_PERSON { get; set; }
        public string EQ_OUT_TIME { get; set; }
        public string EQ_SCRAP_PERSON { get; set; }
        public string EQ_SCRAP_TIME { get; set; }
        public string SERIAL_NUMBER { get; set; }
        public string EQUIPMENT_CODE { get; set; }
        public string DEALER_ENAME { get; set; }
        public string MANUFACTURER_ENAME { get; set; }
        public string EQUIPMENT_TYPE { get; set; }
        public string ENABLE_TIME { get; set; }
        public string? IS_HIDE { get; set; }
     
        public string MAINTAIN_INTERVALS { get; set; }
     
        public string MAINTAIN_WARN_INTERVALS { get; set; }
     
        public string COMPARISON_INTERVALS { get; set; }
     
        public string COMPARISON_WARN_INTERVALS { get; set; }
     
        public string VERIFICATION_INTERVALS { get; set; }
     
        public string VERIFICATION_WARN_INTERVALS { get; set; }
     
        public string CORRECT_INTERVALS { get; set; }
     
        public string CORRECT_WARN_INTERVALS { get; set; }
     
        public string FIRST_START_TIME { get; set; }
     
        public string LAB_NAME { get; set; }
     
        public string LAST_MAINTAIN_DATE { get; set; }
     
        public string LAST_COMPARISON_DATE { get; set; }
     
        public string LAST_VERIFICATION_DATE { get; set; }
     
        public string LAST_CORRECT_DATE { get; set; }
     
        public string NEXT_MAINTAIN_DATE { get; set; }
     
        public string NEXT_COMPARISON_DATE { get; set; }
     
        public string NEXT_VERIFICATION_DATE { get; set; }
     
        public string NEXT_CORRECT_DATE { get; set; }
     
        public string MGROUP_NAME { get; set; }
     
        public string EARLIEST_ENABLE_DATE { get; set; }
     
        public string HOSPITAL_NAME { get; set; }
     
        public string MAINTAIN_TYPE { get; set; }


        //////////////以下是环境信息///////////////////////////////////////////////////////////////////////////////////////////  <summary>
         

        public string EQUIPMENT_WEIGHT { get; set; }
        public string BEARING_REQUIRE { get; set; }
        public string SPACE_REQUIRE { get; set; }
        public string AIR_REQUIRE { get; set; }
        public string WATER_REQUIRE { get; set; }
        public string TEMPERATURE_REQUIRE { get; set; }
        public string HUMIDITY_REQUIRE { get; set; }
        public string AIR_PRESSURE_REQUIRE { get; set; }
        public string POWER_REQUIRE { get; set; }
        public string VOLTAGE_REQUIRE { get; set; }
        public string ELECTRICITY_REQUIRE { get; set; }
        public string OTHER_REQUIRE { get; set; }
        public string REQUIRE_STATE { get; set; }
        
        public string LENGTH { get; set; }
        
        public string WIDTH { get; set; }
        
        public string HEIGHT { get; set; }
        
        public string TEMP_MIN { get; set; }
        
        public string TEMP_MAX { get; set; }
        
        public string HUMI_MIN { get; set; }
        
        public string HUMI_MAX { get; set; }

        //////////////以下是原标识卡独有信息//////////////////////////////////////////////////////////////////////////////////////////
        /// 
        /// <summary>
        /// 设备状态：0-未启用；1-启用；2-停用 3-报废
        /// </summary>
        public string EQUIPMENT_STATE_NAME { get; set; }
        /// <summary>
        ///  上次校准时间
        /// </summary>
        public string LAST_CORRECT { get; set; }
        /// <summary>
        ///  下次校准时间
        /// </summary>
        public string NEXT_CORRECT { get; set; }
        /// <summary>
        ///  经销商维修人员
        /// </summary>
        public string DEALER_REPAIR_PERSON { get; set; }
        /// <summary>
        ///  制造商维修人员
        /// </summary>
        public string MANUFACTURER_REPAIR_PERSON { get; set; }
        /// <summary>
        ///  启用时间
        /// </summary>
        public string E_TIME { get; set; }

        //////////////以下是需求增加信息//////////////////////////////////////////////////////////////////////////////////////////
        /// <summary>
        /// 上次校准检定结果
        /// </summary>
        public string LAST_CORRECT_DEPT { get; set; }
        /// <summary>
        /// 上次校准检定单位
        /// </summary>
        public string LAST_CORRECT_RESULT { get; set; }

        /// <summary>
        /// 检验分组
        /// </summary>
        public string GROUP_ID { get; set; }
        /// <summary>
        /// 检验分组
        /// </summary>
        public string GROUP_NAME { get; set; }
        /// <summary>
        /// 供应商电话
        /// </summary>
        public string DEALER_PHONE { get; set; }
        /// <summary>
        /// 制造商电话
        /// </summary>
        public string MANUFACTURER_PHONE { get; set; }
        /// <summary>
        /// 调试工程师
        /// </summary>
        public string DEBUG_ENGINEER { get; set; }
        /// <summary>
        /// 调试工程师电话
        /// </summary>
        public string DEBUG_ENGINEER_PHONE { get; set; }
        /// <summary>
        /// 安装工程师
        /// </summary>
        public string INSTALL_ENGINEER { get; set; }
        /// <summary>
        /// 安装工程师电话
        /// </summary>
        public string INSTALL_ENGINEER_PHONE { get; set; }
        /// <summary>
        /// 校正有效期
        /// </summary>
        public string VALID_CORRECT_DATE { get; set; }
    }

    //public class CardElementDto
    //{
    //    /// <summary>
    //    /// 设备ID
    //    /// </summary>
    //    public string EQUIPMENT_ID { get; set; }
    //    /// <summary>
    //    /// 设备中文名
    //    /// </summary>
    //    public string EQUIPMENT_NAME { get; set; }
    //    /// <summary>
    //    /// 设备英文名
    //    /// </summary>
    //    public string EQUIPMENT_ENAME { get; set; }
    //    /// <summary>
    //    /// 设备型号
    //    /// </summary>
    //    public string EQUIPMENT_MODEL { get; set; }
    //    /// <summary>
    //    /// 设备代号
    //    /// </summary>
    //    public string EQUIPMENT_CODE { get; set; }
    //    /// <summary>
    //    /// 设备分类
    //    /// </summary>
    //    public string EQUIPMENT_CLASS { get; set; }
    //    /// <summary>
    //    /// 专业分类
    //    /// </summary>
    //    public string PROFESSIONAL_CLASS { get; set; }
    //    /// <summary>
    //    /// 设备序号
    //    /// </summary>
    //    public string EQUIPMENT_NUM { get; set; }
    //    /// <summary>
    //    /// 设备序列号
    //    /// </summary>
    //    public string SERIAL_NUMBER { get; set; }
    //    /// <summary>
    //    /// 注册证号中文
    //    /// </summary>
    //    public string REGISTRATION_NUM { get;set; }
    //    /// <summary>
    //    /// 注册证号英文
    //    /// </summary>
    //    public string REGISTRATION_ENUM { get; set; }
    //    /// <summary>
    //    /// 安装位置
    //    /// </summary>
    //    public string INSTALL_AREA { get; set; }
    //    /// <summary>
    //    /// 医院设备编号
    //    /// </summary>
    //    public string SECTION_NO { get; set; }
    //    /// <summary>
    //    /// 科室设备编号
    //    /// </summary>
    //    public string DEPT_SECTION_NO { get; set; }
    //    /// <summary>
    //    /// 所属实验组
    //    /// </summary>
    //    public string MGROUP_NAME { get; set; }
    //    /// <summary>
    //    /// 所属科室
    //    /// </summary>
    //    public string LAB_NAME { get; set; }
    //    /// <summary>
    //    ///  设备负责人
    //    /// </summary>
    //    public string KEEP_PERSON { get; set; }

    //    /// <summary>
    //    /// 设备状态：0-未启用；1-启用；2-停用 3-报废
    //    /// </summary>
    //    public string EQUIPMENT_STATE { get; set; }

    //    /// <summary>
    //    /// 设备状态：0-未启用；1-启用；2-停用 3-报废
    //    /// </summary>
    //    public string EQUIPMENT_STATE_NAME { get; set; }

    //    /// <summary>
    //    ///  出厂日期
    //    /// </summary>
    //    public string EQ_OUT_TIME { get; set; }
    //    /// <summary>
    //    ///  维修工程师
    //    /// </summary>
    //    public string REPAIR_PERSON { get; set; }
    //    /// <summary>
    //    ///  到货日期
    //    /// </summary>
    //    public string EQ_IN_TIME { get; set; }
    //    /// <summary>
    //    ///  首次启用时间
    //    /// </summary>
    //    public string ENABLE_TIME { get; set; }
    //    /// <summary>
    //    ///  折旧年限
    //    /// </summary>
    //    public string DEPRECIATION_TIME { get; set; }
    //    /// <summary>
    //    ///  报废日期
    //    /// </summary>
    //    public string EQ_SCRAP_TIME { get; set; }
    //    /// <summary>
    //    ///  上次校准时间
    //    /// </summary>
    //    public string LAST_CORRECT { get; set; }
    //    /// <summary>
    //    ///  下次校准时间
    //    /// </summary>
    //    public string NEXT_CORRECT { get; set; }
    //    /// <summary>
    //    ///  经销商中文名
    //    /// </summary>
    //    public string DEALER { get; set; }
    //    /// <summary>
    //    ///  经销商英文名
    //    /// </summary>
    //    public string DEALER_ENAME { get; set; }
    //    /// <summary>
    //    ///  经销商维修人员
    //    /// </summary>
    //    public string DEALER_REPAIR_PERSON { get; set; }
    //    /// <summary>
    //    ///  制造商中文名
    //    /// </summary>
    //    public string MANUFACTURER { get; set; }
    //    /// <summary>
    //    ///  制造商英文名
    //    /// </summary>
    //    public string MANUFACTURER_ENAME { get; set; }
    //    /// <summary>
    //    ///  制造商维修人员
    //    /// </summary>
    //    public string MANUFACTURER_REPAIR_PERSON { get; set; }
    //    /// <summary>
    //    ///  尺寸（mm）
    //    /// </summary>
    //    public string EQUIPMENT_SIZE { get; set; }
    //    /// <summary>
    //    ///  重量（kg）
    //    /// </summary>
    //    public string EQUIPMENT_WEIGHT { get; set; }
    //    /// <summary>
    //    ///  承重要求（kg）
    //    /// </summary>
    //    public string BEARING_REQUIRE { get;set; }
    //    /// <summary>
    //    ///  空间（m³）
    //    /// </summary>
    //    public string SPACE_REQUIRE { get;set; }
    //    /// <summary>
    //    ///  空气洁净度（级）
    //    /// </summary>
    //    public string AIR_REQUIRE { get; set; }
    //    /// <summary>
    //    ///  水质要求（Ω）
    //    /// </summary>
    //    public string WATER_REQUIRE { get; set; }
    //    /// <summary>
    //    ///  温度（℃）
    //    /// </summary>
    //    public string TEMPERATURE_REQUIRE { get; set; }
    //    /// <summary>
    //    ///  湿度（%）
    //    /// </summary>
    //    public string HUMIDITY_REQUIRE { get; set; }
    //    /// <summary>
    //    ///  气压（Pa）
    //    /// </summary>
    //    public string AIR_PRESSURE_REQUIRE { get; set; }
    //    /// <summary>
    //    ///  功率（W）
    //    /// </summary>
    //    public string POWER_REQUIRE { get; set; }
    //    /// <summary>
    //    ///  电压（V）
    //    /// </summary>
    //    public string VOLTAGE_REQUIRE { get; set; }
    //    /// <summary>
    //    ///  电流（A）
    //    /// </summary>
    //    public string ELECTRICITY_REQUIRE { get; set; }
    //    public string CORRECT_INTERVALS { get; set; }
    //    public List<EMS_CORRECT_INFO> eMS_CORRECT_INFO { get; set; }
    //    public EMS_WORK_PLAN eMS_WORK_PLAN { get;set; }
    //    public string E_TIME { get; set; }
    //}
}
