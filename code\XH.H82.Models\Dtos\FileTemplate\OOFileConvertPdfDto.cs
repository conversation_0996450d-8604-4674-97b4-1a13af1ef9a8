﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.Dtos.FileTemplate
{
    /// <summary>
    /// 文件转换上传类
    /// </summary>
    public class OOFileConvertPdfDto
    {
        ///// <summary>
        /////根目录 如果ROOT_PATH传  则路径为 ROOT_PATH/DIR_NAME  如果不传,就是MODULE_ID/DIR_NAME  由UpLoadStyleFile固定为H115
        ///// </summary>
        //public string? ROOT_PATH { get; set; }
        /// <summary>
        /// 文件目录 规则是  MODULE_ID/DIR_NAME  或者 ROOT_PATH/DIR_NAME
        /// </summary>
        public string DIR_NAME { get; set; }
        /// <summary>
        /// 是否使用UUID作为文件命名
        /// </summary>
        public bool? IF_USE_UUID_AS_FILE_NAME { get; set; } = true;

        /// <summary>
        /// 是否添加水印
        /// </summary>
        public bool? IF_ADD_WATER_MARK { get; set; } = true;


        //public List<IFormFile> FILES { get; set; }
        /// <summary>
        /// 采用multipart/form-data方式上传文件
        /// </summary>
        public IFormFile? FILE { get; set; }
        /// <summary>
        /// 是否转换成pdf
        /// </summary>
        public bool? IF_CONVERT_PDF { get; set; } = true;
        /// <summary>
        /// 原始文件是否也上传上S28
        /// </summary>

        public bool? ORIGIN_TO_S28 { get; set; } = true;

        /// <summary>
        /// 扩展信息字段  用于业务模块需要做些啥后续操作
        /// </summary>
        public string? ADDN_REMARK { get; set; }
        /// <summary>
        /// 是否获取封面base64字符串,仅在最终上传的是pdf文件的情况下才有
        /// </summary>
        public bool? IF_GET_COVER_IMG { get; set; } = true;
        /// <summary>
        /// 封面图片是否也存到S28(如果上传S28,则),仅在最终上传的是pdf文件的情况下才有
        /// </summary>
        public bool? COVER_IMG_TO_S28 { get; set; } = true;
    }
}
