﻿using H.Utility;
using XH.H82.Models.Entities;

namespace XH.H82.Models.Dtos.Correct;

public class CorrectDto
{
    public static EMS_CORRECT_INFO CreateAddModule(string equipmentId, string? hospitalId  , CorrectInput input)
    {
        var record = new EMS_CORRECT_INFO();
        record.EQUIPMENT_ID = equipmentId;
        record.CORRECT_ID = IDGenHelper.CreateGuid();
        record.HOSPITAL_ID = hospitalId ?? "H0000";
        record.CORRECT_DEPT = input.CORRECT_DEPT;
        record.CORRECT_DATE = input.CORRECT_DATE;
        record.CORRECT_PERSON = input.CORRECT_PERSON;
        if (record.CORRECT_PERSON.IsNotNullOrEmpty() && record.CORRECT_PERSON.Contains("_"))
        {
            record.CORRECT_PERSON = record.CORRECT_PERSON.Split('_')[1];
        }
        record.CORRECT_RESULT = input.CORRECT_RESULT;
        record.REMARK = input.REMARK;
        record.OCCUR_EVENT = input.OCCUR_EVENT;
        record.CORRECT_STATE = "1";
        record.STATE = "已执行";
        return record;
    }
}


/// <summary>
/// 校准记录表单模型
/// </summary>
/// <param name="CORRECT_DATE">校准日期</param>
/// <param name="CORRECT_DEPT">校准部门</param>
/// <param name="CORRECT_PERSON">校准人员</param>
/// <param name="CORRECT_RESULT">校准结果</param>
/// <param name="OCCUR_EVENT">产生事件</param>
/// <param name="REMARK">备注</param>
public record CorrectInput(
    DateTime? CORRECT_DATE,
    string? CORRECT_DEPT,
    string? CORRECT_PERSON,
    string? CORRECT_RESULT,
    string? OCCUR_EVENT,
    string? REMARK);