﻿using System.Diagnostics;
using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using H.Utility.Helper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RestSharp;
using Serilog;
using XH.H82.IServices;
using XH.H82.Models.BusinessModuleClient;
using XH.H82.Models.Dtos.S28;
using XH.H82.Models.Entities;
using XH.H82.Models.SugarDbContext;

namespace XH.H82.Services
{
    public class BaseDataServices : IBaseDataServices
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<SystemService> _logger;
        private readonly IHttpContextAccessor _httpContext;
        private readonly RestClient _clientH07;
        private readonly RestClient _clientS28;
        private readonly ISqlSugarUow<SugarDbContext_Master> _dbContext;
        public BaseDataServices(IConfiguration configuration, ILogger<BaseDataServices> logger, IHttpContextAccessor httpContext, ISqlSugarUow<SugarDbContext_Master> dbContext)
        {

            var addressH07 = configuration["H07"];//系统数据管理
            var addressS28 = configuration["S28"];

            //忽略证书错误
            _clientH07 = new RestClient(new RestClientOptions()
            {
                RemoteCertificateValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true,
                BaseUrl = new Uri(addressH07),
                ThrowOnAnyError = true
            });
            _clientS28 = new RestClient(new RestClientOptions()
            {
                RemoteCertificateValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true,
                BaseUrl = new Uri(addressS28),
                ThrowOnAnyError = true
            });
            _configuration = configuration;
            _httpContext = httpContext;
            _dbContext = dbContext;
        }


        /// <summary>
        /// 固定基础数据
        /// </summary>
        /// <param name="systemId">系统ID</param>
        /// <param name="moduleId">模块ID</param>
        /// <param name="oneClass">一级分类</param>
        /// <param name="classId">分类ID</param>
        /// <param name="getType">1,缓存数据，2,表数据</param>
        /// <returns></returns>
        public List<Lis5BaseDataDto> GetLis5BaseData(string classId, string systemId, string moduleId, string oneClass, EnumBaseDataGetType getType)
        {
            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();

            Stopwatch sw = new Stopwatch();
            sw.Start();
            string url = $"/api/External/GetLis5BaseData?classId={classId}&systemId={systemId}&moduleId={moduleId}&oneClass={oneClass}&getType={getType.ToString()}";

            RestRequest request = new RestRequest(url);
            request.AddHeader("Authorization", token);
            var response = _clientH07.ExecuteGet<ResultDto>(request);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用H07模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
            if (response.ErrorException != null)
            {
                Log.Error($"调用H07模块[{url}]发生错误:{response.ErrorException}");
                throw new BizException($"调用H07模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
            }
            else
            {
                if (response.Data.success)
                {
                    return JsonHelper.FromJson<List<Lis5BaseDataDto>>(response.Data.data.ToString());
                }
                else
                {
                    Log.Error($"调用H07模块[{url}]请求完成,但是返回了错误:" + response.Data.msg);
                    return null;
                }
            }
        }

        /// <summary>
        /// 获取组织机构数据
        /// 组织机构数据包括：系统实例,外部系统,医疗机构,院区、分院/检验科室/位置字典/检验专业组/管理专业组
        /// </summary>
        /// <param name="hospitalId">机构ID</param>
        /// <param name="getType">1,缓存数据，2,表数据</param>
        /// <returns></returns>
        public OrganizeDataDto GetOrganizeData(string hospitalId, EnumBaseDataGetType getType)
        {
            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();

            Stopwatch sw = new Stopwatch();
            sw.Start();
            string url = $"/api/External/GetOrganizeData?hospitalId={hospitalId}&getType={getType.ToString()}";

            RestRequest request = new RestRequest(url);
            request.AddHeader("Authorization", token);
            var response = _clientH07.ExecuteGet<ResultDto>(request);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用H07模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
            if (response.ErrorException != null)
            {
                Log.Error($"调用H07模块[{url}]发生错误:{response.ErrorException}");
                throw new BizException($"调用H07模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
            }
            else
            {
                if (response.Data.success)
                {
                    return JsonHelper.FromJson<OrganizeDataDto>(response.Data.data.ToString());
                }
                else
                {
                    Log.Error($"调用H07模块[{url}]请求完成,但是返回了错误:" + response.Data.msg);
                    return null;
                }
            }
        }

        /// <summary>
        /// 基础数据
        /// 病人科别,病人病区,护理单元,医院护工表,医院医生表,医院护士表,公司信息,公司联系人
        /// </summary>
        /// <param name="classId">多个用英文逗号隔开</param>
        /// <param name="hospitalId">医疗机构ID</param>
        /// <param name="labId">科室ID</param>
        /// <param name="getType">1,缓存数据，2,表数据</param>
        /// <returns></returns>
        public LisBaseDataDto GetLisBaseData(string classId, string hospitalId, string labId, EnumBaseDataGetType getType)
        {
            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();

            Stopwatch sw = new Stopwatch();
            sw.Start();
            string url = $"/api/External/GetLisBaseData?classId={classId}&hospitalId={hospitalId}&labId={labId}&getType={getType.ToString()}";

            RestRequest request = new RestRequest(url);
            request.AddHeader("Authorization", token);
            var response = _clientH07.ExecuteGet<ResultDto>(request);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用H07模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
            if (response.ErrorException != null)
            {
                Log.Error($"调用H07模块[{url}]发生错误:{response.ErrorException}");
                throw new BizException($"调用H07模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
            }
            else
            {
                if (response.Data.success)
                {
                    return JsonHelper.FromJson<LisBaseDataDto>(response.Data.data.ToString());
                }
                else
                {
                    Log.Error($"调用H07模块[{url}]请求完成,但是返回了错误:" + response.Data.msg);
                    return null;
                }
            }
        }

        /// <summary>
        /// 菜单按钮API权限
        /// </summary>
        /// <param name="moduleId"></param>
        /// <param name="getType">1,缓存数据，2,表数据</param>
        /// <returns></returns>
        public MenuButtonApiDto GetMenuButtonApi(string moduleId, EnumBaseDataGetType getType)
        {
            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();

            Stopwatch sw = new Stopwatch();
            sw.Start();
            string url = $"/api/External/GetMenuButtonApi?moduleId={moduleId}&getType={getType.ToString()}";

            RestRequest request = new RestRequest(url);
            request.AddHeader("Authorization", token);
            var response = _clientH07.ExecuteGet<ResultDto>(request);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用H07模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
            if (response.ErrorException != null)
            {
                Log.Error($"调用H07模块[{url}]发生错误:{response.ErrorException}");
                throw new BizException($"调用H07模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
            }
            else
            {
                if (response.Data.success)
                {
                    return JsonHelper.FromJson<MenuButtonApiDto>(response.Data.data.ToString());
                }
                else
                {
                    Log.Error($"调用H07模块[{url}]请求完成,但是返回了错误:" + response.Data.msg);
                    return null;
                }
            }
        }

        /// <summary>
        /// 账号信息
        /// </summary>
        /// <param name="hospitalId">医疗机构ID</param>
        /// <param name="getType">1,缓存数据，2,表数据</param>
        /// <returns></returns>
        public AccountInfoDto AccountInfo(string hospitalId, EnumBaseDataGetType getType)
        {
            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();

            Stopwatch sw = new Stopwatch();
            sw.Start();
            string url = $"/api/External/AccountInfo?hospitalId={hospitalId}&getType={getType.ToString()}";

            RestRequest request = new RestRequest(url);
            request.AddHeader("Authorization", token);
            var response = _clientH07.ExecuteGet<ResultDto>(request);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用H07模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
            if (response.ErrorException != null)
            {
                Log.Error($"调用H07模块[{url}]发生错误:{response.ErrorException}");
                throw new BizException($"调用H07模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
            }
            else
            {
                if (response.Data.success)
                {
                    return JsonHelper.FromJson<AccountInfoDto>(response.Data.data.ToString());
                }
                else
                {
                    Log.Error($"调用H07模块[{url}]请求完成,但是返回了错误:" + response.Data.msg);
                    return null;
                }
            }
        }

        /// <summary>
        /// 获取设置值
        /// </summary>
        /// <param name="moduleId">模块ID</param>
        /// <param name="unitId">单元ID</param>
        /// <param name="unitType">单元类型</param>
        /// <param name="setupClass">设置分类</param>
        /// <param name="hospitalId">医疗机构ID</param>
        /// <param name="setupNo">设置ID</param>
        /// <param name="getType">1,缓存数据，2,表数据</param>
        /// <returns></returns>
        public List<Lis5SetupDictCacheDto> GetLis5SetupDicts(string moduleId, string hospitalId, string unitId, string unitType, string setupClass, string setupNo, EnumBaseDataGetType getType)
        {
            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();

            Stopwatch sw = new Stopwatch();
            sw.Start();
            string url = $"/api/External/GetLis5SetupDicts?moduleId={moduleId}&hospitalId={hospitalId}&unitId={unitId}&unitType={unitType}&setupClass={setupClass}&setupNo={setupNo}&getType={getType.ToString()}";

            RestRequest request = new RestRequest(url);
            request.AddHeader("Authorization", token);
            var response = _clientH07.ExecuteGet<ResultDto>(request);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用H07模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
            if (response.ErrorException != null)
            {
                Log.Error($"调用H07模块[{url}]发生错误:{response.ErrorException}");
                throw new BizException($"调用H07模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
            }
            else
            {
                if (response.Data.success)
                {
                    return JsonHelper.FromJson<List<Lis5SetupDictCacheDto>>(response.Data.data.ToString());
                }
                else
                {
                    Log.Error($"调用H07模块[{url}]请求完成,但是返回了错误:" + response.Data.msg);
                    return null;
                }
            }
        }


        /// 获取主键最大值（精确到字段）
        /// </summary>
        /// <param name="tableName"></param>
        /// <param name="addCount"></param>
        /// <param name="ifTableMax">1.默认redis最大值;2、表中最大值</param>
        /// <param name="fieldName">主键字段名</param>
        /// <returns></returns>
        public ResultDto GetTableMax(string tableName, string fieldName = "", int addCount = 1, int ifTableMax = 1, string dBOwner = "XH_OA")
        {
            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();

            Stopwatch sw = new Stopwatch();
            sw.Start();
            string url = $"/api/External/GetTableMax?tableName={tableName}&addCount={addCount}&ifTableMax={ifTableMax}&fieldName={fieldName}&dBOwner={dBOwner}";

            RestRequest request = new RestRequest(url);
            request.AddHeader("Authorization", token);
            var response = _clientH07.ExecuteGet<ResultDto>(request);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用H07模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
            if (response.ErrorException != null)
            {
                Log.Error($"调用H07模块[{url}]发生错误:{response.ErrorException}");
                throw new BizException($"调用H07模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
            }
            else
            {
                if (response.Data.success)
                {
                    return response.Data;
                }
                else
                {
                    Log.Error($"调用H07模块[{url}]请求完成,但是返回了错误:" + response.Data.msg);
                    return null;
                }
            }
        }

        public ResultDto UploadPathFile(string jsonStr, string serviceUrl)
        {
            ResultDto res = new ResultDto();
            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();
            //byte[] fileBytes = System.Text.Encoding.UTF8.GetBytes(jsonStr);
            Stopwatch sw = new Stopwatch();
            sw.Start();
            string url = $"/api/Common/SetUploadPdf";
            if (serviceUrl.IsNotNullOrEmpty())
            {
                url = serviceUrl;
            }
            RestRequest request = new RestRequest(url);
            request.AddHeader("Authorization", token);
            request.AddJsonBody(jsonStr);
            var response = _clientS28.ExecutePost<ResultDto>(request);
            Log.Information(response.Content);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
            .Information($"调用S28模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
            if (response.ErrorException != null)
            {
                Log.Error($"调用S28模块[{url}]发生错误:{response.ErrorException}");
                throw new BizException($"调用S28模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
            }
            else
            {
                if (response.Data.success)
                {
                    dynamic json = JsonConvert.DeserializeObject(Convert.ToString(response.Content));
                    res.success = json["success"];
                    res.data = json["data"].ToString();
                }
                else
                {
                    Log.Error($"调用S28模块[{url}]请求完成,但是返回了错误:" + response.Data.msg);
                    return null;
                }
            }
            return res;
        }
        /// <summary>
        /// 上传文件
        /// </summary>
        /// <param name="jsonStr"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public ResultDto UploadPathFormDataFile(string doc_folder, string if_cover, string doc_name, byte[] bytes)
        {
            ResultDto res = new ResultDto();
            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();

            Stopwatch sw = new Stopwatch();
            sw.Start();
            string url = $"/api/Common/SetUploadFile?folderName={doc_folder}";
            RestRequest restRequest = new RestRequest(url);
            restRequest.AddParameter("ifCover", if_cover, ParameterType.UrlSegment);
            restRequest.RequestFormat = DataFormat.Json;
            restRequest.Method = Method.Post;
            restRequest.AddHeader("Authorization", token);
            restRequest.AddHeader("Content-Type", "multipart/form-data");
            restRequest.AddFile("formFiles", bytes, doc_name);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
            .Information($"调用S28模块前[{url}],耗时:{sw.ElapsedMilliseconds}ms");
            sw = new Stopwatch();
            sw.Start();
            var response = _clientS28.ExecutePost<ResultDto>(restRequest);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
            .Information($"调用S28模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
            if (!response.IsSuccessful)
            {
                Log.Error($"调用S28模块[{url}]发生错误:{response.ErrorException}");
                throw new BizException($"调用S28模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
            }
            else
            {
                if (response.Data.success)
                {
                    dynamic json = JsonConvert.DeserializeObject(Convert.ToString(response.Content));
                    res.success = json["success"];
                    res.data = json["data"].ToString();
                }
                else
                {
                    Log.Error($"调用S28模块[{url}]请求完成,但是返回了错误:" + response.Data.msg);
                    res.success = false;
                    res.msg = response.Data.msg;
                }
            }
            return res;
        }

        public string UploadToS28(string doc_folder, string if_cover, string doc_name, byte[] bytes)
        {

            var result = "";
            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();
            Stopwatch sw = new Stopwatch();
            sw.Start();
            string url = $"/api/Common/SetUploadFile?folderName={doc_folder}";
            RestRequest restRequest = new RestRequest(url);
            restRequest.AddParameter("ifCover", if_cover, ParameterType.UrlSegment);
            restRequest.RequestFormat = DataFormat.Json;
            restRequest.Method = Method.Post;
            restRequest.AddHeader("Authorization", token);
            restRequest.AddHeader("Content-Type", "multipart/form-data");
            restRequest.AddFile("formFiles", bytes, doc_name);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
            .Information($"调用S28模块前[{url}],耗时:{sw.ElapsedMilliseconds}ms");
            sw = new Stopwatch();
            sw.Start();
            var response = _clientS28.ExecutePost<ResultDto>(restRequest);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds).Information($"调用S28模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
            if (!response.IsSuccessful)
            {
                Log.Error($"调用S28模块[{url}]发生错误:{response.ErrorException}");
                throw new BizException($"调用S28模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
            }
            else
            {
                if (response != null && response.Data.success)
                {
                    var jarray = JArray.Parse(response.Data.data.ToString());
                    foreach (var item in jarray)
                    {
                        result = item["UploadPath"].ToString();
                    }
                }
                else
                {
                    Log.Error($"调用S28模块[{url}]请求完成,但是返回了错误:" + response.Data.msg);
                    throw new BizException($"调用S28模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
                }
            }
            return result;
        }
        /// <summary>
        /// 无账号体系token上传S28
        /// </summary>
        /// <param name="doc_folder"></param>
        /// <param name="if_cover"></param>
        /// <param name="doc_name"></param>
        /// <param name="bytes"></param>
        /// <param name="token"></param>
        /// <returns></returns>
        /// <exception cref="BizException"></exception>
        public string  UploadToS28ByToken(string doc_folder, string if_cover, string doc_name, byte[] bytes , string token)
        {
            var result = "";
            Stopwatch sw = new Stopwatch();
            sw.Start();
            string url = $"/api/Common/SetUploadFile?folderName={doc_folder}";
            RestRequest restRequest = new RestRequest(url);
            restRequest.AddParameter("ifCover", if_cover, ParameterType.UrlSegment);
            restRequest.RequestFormat = DataFormat.Json;
            restRequest.Method = Method.Post;
            restRequest.AddHeader("Authorization", "Bearer "+token);
            restRequest.AddHeader("Content-Type", "multipart/form-data");
            restRequest.AddFile("formFiles", bytes, doc_name);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
            .Information($"调用S28模块前[{url}],耗时:{sw.ElapsedMilliseconds}ms");
            sw = new Stopwatch();
            sw.Start();
            var response = _clientS28.ExecutePost<ResultDto>(restRequest);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds).Information($"调用S28模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
            if (!response.IsSuccessful)
            {
                Log.Error($"调用S28模块[{url}]发生错误:{response.ErrorException}");
                throw new BizException($"调用S28模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
            }
            else
            {
                if (response != null && response.Data.success)
                {
                    var jarray = JArray.Parse(response.Data.data.ToString());
                    foreach (var item in jarray)
                    {
                        result = item["UploadPath"].ToString();
                    }
                }
                else
                {
                    Log.Error($"调用S28模块[{url}]请求完成,但是返回了错误:" + response.Data.msg);
                    throw new BizException($"调用S28模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
                }
            }
            return result;
        }

        /// <summary>
        /// 无账号体系外部模块信息查询
        /// </summary>
        /// <param name="moduleId"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public ThirdVerifyInfoDto GetCustomTokenInfo(string moduleId)
        {
            var sys6_interface_module = _dbContext.Db.Queryable<SYS6_INTERFACE_MODULE>().First(p => p.MODULE_ID == moduleId);
            if (sys6_interface_module != null)
            {
                string app_key = sys6_interface_module.APP_KEY;
                string app_secret = sys6_interface_module.APP_SECRET;
                ContentKeyDto contentKey = new ContentKeyDto()
                {
                    callModuleId = moduleId,
                    moduleId = "S28",
                    requestTime = DateTime.Now,
                    tokenGuid = IDGenHelper.CreateGuid(),
                    userNo = moduleId,
                    isInteriorUser = false,
                    obj = ""
                };
                string strKey = JsonHelper.ToJson(contentKey);
                string secretKey = SmxUtilsHelper.SM4UtilsEncrypt(app_secret, strKey);
                ThirdVerifyInfoDto infoDto = new ThirdVerifyInfoDto()
                {
                    key = secretKey,
                    appKey = app_key
                };
                return infoDto;
            }
            else
            {
                throw new Exception($"未找到模块[{moduleId}]");
            }
        }

        /// <summary>
        /// 获取自定义token 无账号体系
        /// </summary>
        /// <param name="infoDto"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public ResultDto GetCustomToken(ThirdVerifyInfoDto infoDto)
        {

            var ip = _configuration["UrlModuleS01"];
            var _clientS01 = new RestClient(
                new RestClientOptions()
                {
                    RemoteCertificateValidationCallback = (
                        sender,
                        certificate,
                        chain,
                        sslPolicyErrors
                    ) => true,
                    BaseUrl = new Uri(_configuration["UrlModuleS01"]),
                    ThrowOnAnyError = true,
                }
            );
            Stopwatch sw = new Stopwatch();
            sw.Start();
            string url = $"/api/Account/GetCustomToken";
            RestRequest request = new RestRequest(url);
            request.AddJsonBody(infoDto);
            var response = _clientS01.ExecutePost<ResultDto>(request);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用S01模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
            if (response.ErrorException != null)
            {
                Log.Error($"调用S01模块[{url}]发生错误:{response.ErrorException}");
                throw new Exception($"调用S01模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
            }
            else
            {
                if (response.Data.success)
                {
                    return response.Data;
                }
                else
                {
                    Log.Error($"调用S01模块[{url}]请求完成,但是返回了错误:" + response.Data.msg);
                    return null;
                }
            }
        }
    }
}
