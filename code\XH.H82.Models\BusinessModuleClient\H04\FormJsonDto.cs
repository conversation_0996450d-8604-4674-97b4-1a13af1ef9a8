﻿namespace XH.H82.Models.BusinessModuleClient.H04;

public class FormJsonDto
{
    /// <summary>
    /// 
    /// </summary>
    public Layout layout { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public List<Rows> rows { get; set; }
}

public class Rows
{
    /// <summary>
    /// 
    /// </summary>
    public string rowId { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public List<Cols> cols { get; set; }
}

public class Cols
{
    /// <summary>
    /// 
    /// </summary>
    public string colId { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public int span { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public List<Items> items { get; set; }
}

public class Layout
{
    /// <summary>
    /// 
    /// </summary>
    public string type { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public int widthPercent { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public int labelCol { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public int verticalGap { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public string size { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public Style style { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public bool showRowNum { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public string labelLayout { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public bool labelWrap { get; set; }
}

public class Style
{
    /// <summary>
    /// 
    /// </summary>
    public int paddingTop { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public int paddingBottom { get; set; }
}
public class Items
{
    /// <summary>
    /// 
    /// </summary>
    public string itemId { get; set; }
    /// <summary>
    /// 基本
    /// </summary>
    public string group { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public string formId { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public string icon { get; set; }
    /// <summary>
    /// 基本信息
    /// </summary>
    public string formName { get; set; }

    /// <summary>
    /// 自定义名称
    /// </summary>
    public string formCname { get; set; }
    /// <summary>
    /// 代码
    /// </summary>
    public string formCode { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public bool isForm { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public string dataType { get; set; }


    public string? widgetType { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public string dataClass { get; set; }

    public bool labelHide { get; set; }

    public Rules rules { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public WidgetProps WidgetProps { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public List<string> propslist { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public LabelStyle labelStyle { get; set; }
}

public class WidgetProps
{
    /// <summary>
    /// 
    /// </summary>
    public bool button { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public string groupKey { get; set; }
}

public class LabelStyle
{
    /// <summary>
    /// 
    /// </summary>
    public string background { get; set; }
}

public class PropslistJson
{
    /// <summary>
    /// 基本
    /// </summary>
    public string group { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public string formId { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public string icon { get; set; }
    /// <summary>
    /// 单行文本
    /// </summary>
    public string formName { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public string dataType { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public bool isForm { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public List<Rules> rules { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public object wigetProps { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public List<string> propslist { get; set; }
}

public class Rules
{
    /// <summary>
    /// 
    /// </summary>
    public bool required { get; set; }
}

public class StyleJson
{

    /// <summary>
    /// 
    /// </summary>
    public string dataClass { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public string dataType { get; set; }


    public bool labelHide { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public LabelStyle labelStyle { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public object rules { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public object widgetProps { get; set; }


}