using H.Utility.SqlSugarInfra;
using Microsoft.EntityFrameworkCore;
using SqlSugar;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace XH.H82.Models.Entities.Tim
{
    [DBOwner("XH_OA")]
    public class TIM_WORK_FORM_VER
    {
        /// <summary>
        /// 记录单版本ID
        /// </summary>
        [Key]
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        [Column("FORM_VER_ID")]
        [Required(ErrorMessage = "记录单版本ID不允许为空")]

        [StringLength(50, ErrorMessage = "记录单版本ID长度不能超出50字符")]
        [Unicode(false)]
        public string FORM_VER_ID { get; set; }

        /// <summary>
        /// 记录单ID
        /// </summary>

        [Column("FORM_ID")]
        [Required(ErrorMessage = "记录单ID不允许为空")]
        [StringLength(50, ErrorMessage = "记录单ID长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string FORM_ID { get; set; }

        /// <summary>
        /// 发布电脑
        /// </summary>
        [Column("ISSUED_COMPUTER")]
        [StringLength(50, ErrorMessage = "发布电脑长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string ISSUED_COMPUTER { get; set; }

        /// <summary>
        /// 首次登记人
        /// </summary>
        [Column("FIRST_RPERSON")]
        [StringLength(50, ErrorMessage = "首次登记人长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string FIRST_RPERSON { get; set; }

        /// <summary>
        /// 审核者
        /// </summary>
        [Column("CHECK_PERSON")]
        [StringLength(50, ErrorMessage = "审核者长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string CHECK_PERSON { get; set; }

        /// <summary>
        /// 提交者
        /// </summary>
        [Column("SUBMIT_PERSON")]
        [StringLength(50, ErrorMessage = "提交者长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string SUBMIT_PERSON { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        [Column("LAST_MTIME")]
        [Unicode(false)]
        public DateTime? LAST_MTIME { get; set; }

        /// <summary>
        /// 修订号
        /// </summary>
        [Column("FORM_REVISE_NO")]
        [StringLength(50, ErrorMessage = "修订号长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string FORM_REVISE_NO { get; set; }

        /// <summary>
        /// 批准者
        /// </summary>
        [Column("APPROVAL_PERSON")]
        [StringLength(50, ErrorMessage = "批准者长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string APPROVAL_PERSON { get; set; }

        /// <summary>
        /// 数据项JSON
        /// </summary>
        [Column("ITEM_SETUP_JSON", TypeName = "CLOB")]
        [StringLength(4000, ErrorMessage = "数据项JSON长度不能超出4000字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string ITEM_SETUP_JSON { get; set; }

        /// <summary>
        /// 发布号
        /// </summary>
        [Column("ISSUED_NO")]
        [StringLength(50, ErrorMessage = "发布号长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string ISSUED_NO { get; set; }

        /// <summary>
        /// 启用日期
        /// </summary>
        [Column("START_DATE")]
        [Unicode(false)]
        public DateTime? START_DATE { get; set; }

        /// <summary>
        /// 指定审核人员
        /// </summary>
        [Column("ASSIGN_APPROVAL")]
        [StringLength(50, ErrorMessage = "指定审核人员长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string ASSIGN_APPROVAL { get; set; }

        /// <summary>
        /// 批准日期
        /// </summary>
        [Column("APPROVAL_TIME")]
        [Unicode(false)]
        public DateTime? APPROVAL_TIME { get; set; }

        /// <summary>
        /// 记录单版本描述
        /// </summary>
        [Column("FORM_VER_DESC")]
        [StringLength(500, ErrorMessage = "记录单版本描述长度不能超出500字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string FORM_VER_DESC { get; set; }

        /// <summary>
        /// 发布日期
        /// </summary>
        [Column("ISSUED_TIME")]
        [Unicode(false)]
        public DateTime? ISSUED_TIME { get; set; }

        /// <summary>
        /// 记录单文件
        /// </summary>
        [Column("FORM_VER_FILE")]
        [StringLength(500, ErrorMessage = "记录单文件长度不能超出500字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string FORM_VER_FILE { get; set; }

        /// <summary>
        /// 版本号
        /// </summary>
        [Column("FORM_VER_NO")]
        [StringLength(50, ErrorMessage = "版本号长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string FORM_VER_NO { get; set; }

        /// <summary>
        /// 批准电脑
        /// </summary>
        [Column("APPROVAL_COMPUTER")]
        [StringLength(50, ErrorMessage = "批准电脑长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string APPROVAL_COMPUTER { get; set; }

        /// <summary>
        /// 记录单版本状态0未提交1已提交2已审核未通过3待审批4已审批未通过5待发布6已发布-0废止
        /// </summary>
        [Column("FORM_VER_STATE")]
        [StringLength(20, ErrorMessage = "记录单版本状态0未提交1已提交2已审核未通过3待审批4已审批未通过5待发布6已发布-0废止长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string FORM_VER_STATE { get; set; }

        /// <summary>
        /// 继承上一版本0否1是
        /// </summary>
        [Column("IF_INHERIT_OVER")]
        [StringLength(20, ErrorMessage = "继承上一版本0否1是长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string IF_INHERIT_OVER { get; set; }

        /// <summary>
        /// 发布者
        /// </summary>
        [Column("ISSUED_PERSON")]
        [StringLength(50, ErrorMessage = "发布者长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string ISSUED_PERSON { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Column("REMARK")]
        [StringLength(200, ErrorMessage = "备注长度不能超出200字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string REMARK { get; set; }

        /// <summary>
        /// 排序号
        /// </summary>
        [Column("FORM_VER_SORT")]
        [StringLength(20, ErrorMessage = "排序号长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string FORM_VER_SORT { get; set; }

        /// <summary>
        /// 提交日期
        /// </summary>
        [Column("SUBMIT_TIME")]
        [Unicode(false)]
        public DateTime? SUBMIT_TIME { get; set; }

        /// <summary>
        /// 提交电脑
        /// </summary>
        [Column("SUBMIT_COMPUTER")]
        [StringLength(50, ErrorMessage = "提交电脑长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string SUBMIT_COMPUTER { get; set; }

        /// <summary>
        /// 指定审核人员
        /// </summary>
        [Column("ASSIGN_CHECK")]
        [StringLength(50, ErrorMessage = "指定审核人员长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string ASSIGN_CHECK { get; set; }

        /// <summary>
        /// 审核日期
        /// </summary>
        [Column("CHECK_TIME")]
        [Unicode(false)]
        public DateTime? CHECK_TIME { get; set; }

        /// <summary>
        /// 首次登记时间
        /// </summary>
        [Column("FIRST_RTIME")]
        [Unicode(false)]
        public DateTime? FIRST_RTIME { get; set; }

        /// <summary>
        /// 最后修改人员
        /// </summary>
        [Column("LAST_MPERSON")]
        [StringLength(50, ErrorMessage = "最后修改人员长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string LAST_MPERSON { get; set; }

        /// <summary>
        /// 实施日期
        /// </summary>
        [Column("EXECUTE_DATE")]
        [Unicode(false)]
        public DateTime? EXECUTE_DATE { get; set; }

        /// <summary>
        /// 审核电脑
        /// </summary>
        [Column("CHECK_COMPUTER")]
        [StringLength(50, ErrorMessage = "审核电脑长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string CHECK_COMPUTER { get; set; }


    }
}
