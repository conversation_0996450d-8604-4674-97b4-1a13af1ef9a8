﻿using System.Reflection;
using System.Xml.Linq;

namespace XH.H82.Base.Helper;

public static class XmlHelper
{
    public static string SerializeObjectToXml<T>(T obj)
    {
        // 获取泛型对象的类型
        Type type = typeof(T);
        // 创建根节点
        XElement rootElement = new XElement("Root");

        // 遍历对象的所有属性
        foreach (PropertyInfo property in type.GetProperties())
        {
            // 获取属性值
            object value = property.GetValue(obj);
            // 如果值不为空，添加到XML中
            if (value != null)
            {
                XElement element = new XElement(property.Name, value.ToString());
                rootElement.Add(element);
            }
        }

        // 返回XML字符串
        return rootElement.ToString();
    }
}