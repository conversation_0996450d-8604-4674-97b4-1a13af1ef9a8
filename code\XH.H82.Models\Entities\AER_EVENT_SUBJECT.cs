﻿using H.Utility.SqlSugarInfra;
using Microsoft.EntityFrameworkCore;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.Entities
{
    [DBOwner("XH_OA")]
    [Table("AER_EVENT_SUBJECT")]
    public class AER_EVENT_SUBJECT
    {
        /// <summary>
        /// 事件主体ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Column("SUBJECT_ID")]
        [Unicode(false)]
        public string SUBJECT_ID { get; set; }

        /// <summary>
        /// 事件ID
        /// </summary>
        [Column("EVENT_ID")]
        [Unicode(false)]
        public string EVENT_ID { get; set; }

        /// <summary>
        /// 主体ID
        /// </summary>
        [Column("SUBJECT_CORR_ID")]
        [Unicode(false)]
        public string SUBJECT_CORR_ID { get; set; }

        /// <summary>
        /// 事件主体名称
        /// </summary>
        [Column("SUBJECT_NAME")]
        [Unicode(false)]
        public string SUBJECT_NAME { get; set; }

        /// <summary>
        /// 状态 0禁用 1在用 2删除
        /// </summary>
        [Column("SUBJECT_STATE")]
        [Unicode(false)]
        public string SUBJECT_STATE { get; set; }

        /// <summary>
        /// 首次登记人
        /// </summary>
        [Column("FIRST_RPERSON")]
        [Unicode(false)]
        public string? FIRST_RPERSON { get; set; }

        /// <summary>
        /// 首次登记时间
        /// </summary>
        [Column("FIRST_RTIME")]
        [Unicode(false)]
        public DateTime FIRST_RTIME { get; set; }

        /// <summary>
        /// 最后修改人员
        /// </summary>
        [Column("LAST_MPERSON")]
        [Unicode(false)]
        public string? LAST_MPERSON { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        [Column("LAST_MTIME")]
        [Unicode(false)]
        public DateTime LAST_MTIME { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Column("REMARK")]
        [Unicode(false)]
        public string? REMARK { get; set; }

        /// <summary>
        /// 主体分类
        /// </summary>
        [Column("SUBJECT_CLASS")]
        [Unicode(false)]
        public string? SUBJECT_CLASS { get; set; }
    }
}
