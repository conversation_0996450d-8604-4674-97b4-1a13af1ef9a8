using Newtonsoft.Json;

namespace XH.H82.Models.BusinessModuleClient.Dto.IoTDevices;

/// <summary>
/// 传感器类型下的监测类型
/// </summary>
public class SensorTypeAndMonitorTypeDto
{
    /// <summary>
    /// 医疗设备类型枚举
    /// </summary>
    [JsonProperty("key")]
    public int Key { get; set; }
    /// <summary>
    /// 医疗设备类型名称
    /// </summary>
    [JsonProperty("value")]
    public string Value { get; set; }
    
    [JsonProperty("bioAlarmTypes")]
    public List<MonitorType> MonitorTypes { get; set; }
 }