﻿namespace XH.H82.Models.EquipmengtClassNew;

/// <summary>
/// 档案记录Dto
/// </summary>
public class EqpArchivesDto
{
    /// <summary>
    /// 档案顺序
    /// </summary>
    public double? EqpArchivesSort{ get; set; }
    /// <summary>
    /// 档案记录ID
    /// </summary>
    public string EqpArchivesId{ get; set; }
    
    /// <summary>
    /// 档案记录状态;0 禁用  1在用  2删除
    /// </summary>
    public string EqpArchivesState{ get; set; }

    /// <summary>
    /// 档案记录状态;0 禁用  1在用  2删除
    /// </summary>
    public string EqpArchivesStateName => EqpArchivesState switch
    {
        "0" => "禁用",
        "1" => "在用",
        "2" => "删除",
        _ => "未知"
    };
    /// <summary>
    /// 档案名称
    /// </summary>
    public string EqpArchivesName{ get; set; }
    
    /// <summary>
    /// 档案记录父级id
    /// </summary>
    public string? EqpArchivesPid{ get; set; }
    /// <summary>
    /// 档案记录父级名称
    /// </summary>
    public string? EqpArchivesPidName{ get; set; }
    
    /// <summary>
    /// 档案记录类型;0:固定 1:扩展 默认0
    /// </summary>
    public string EqpArchivesType{ get; set; }

    /// <summary>
    /// 是否上传附件
    /// </summary>
    public bool IsUpload { get; set; } = true;

    /// <summary>
    /// 适用设备类型名称
    /// </summary>
    public string ApplyEquipmentClassNames { get; set; }
    
    /// <summary>
    /// 首次登记人
    /// </summary>
    public string? FIRST_RPERSON{ get; set; }
    
    /// <summary>
    /// 首次登记时间
    /// </summary>
    public DateTime? FIRST_RTIME{ get; set; }
    
    /// <summary>
    /// 最后修改人员
    /// </summary>
    public string? LAST_MPERSON{ get; set; }
    
    /// <summary>
    /// 最后修改时间
    /// </summary>
    public DateTime? LAST_MTIME{ get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark{ get; set; }
    
    
    /// <summary>
    /// 是否是细分类
    /// </summary>
    public bool IsSubdivision { get; set; }
}


public class EqpArchivesExt
{
    public bool IsUpload { get; set; }
}