﻿using System.ServiceModel;
using EasyCaching.Core;
using H.Utility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.Extensions.Caching.Memory;
using ReportService;
using RestSharp;
using XH.H82.Base.Setup;
using XH.H82.IServices;
using XH.H82.Models.Dtos;
using XH.H82.Models.Entities;
using XH.LAB.UTILS.Interface;
using XH.LAB.UTILS.Models;
using XH.LAB.UTILS.Models.Dto;
using IHostingEnvironment = Microsoft.AspNetCore.Hosting.IHostingEnvironment;
using UploadFileDto = XH.H82.Models.Dtos.UploadFileDto;

namespace XH.H82.API.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize]
    public class XhEquipmentDocController : ControllerBase
    {
        private readonly IXhEquipmentDocService _xhEquipmentDocService;
        private readonly IEquipmentDocService _equipmentDocService;
        private readonly IOperationRecordService _operationRecordService;
        private readonly IBaseService _baseService;
        private readonly IHostingEnvironment _hostingEnvironment;
        private readonly IModuleLabGroupService _IModuleLabGroupService;
        private readonly string file_preview_address;
        private readonly string file_upload_address;
        private readonly IConfiguration _configuration;
        private readonly IBaseDataServices _baseDataServices;
        private readonly IMemoryCache _easyCacheMemory;
        private string currentLabKey = "XH:LIS:H82:CURRENTLAB:UserSelectedLab:";
        private readonly string RedisModule;
        private readonly RestClient _clientS13;
        private readonly IUploadFileService _IUploadFileService;
        private XingHePrintServiceSoapClient _client;
        //接口服务地址
        private readonly string _serviceAddress;
        public XhEquipmentDocController(IXhEquipmentDocService equipmentDocService, IHostingEnvironment hostingEnvironment,
            IBaseService baseService, IModuleLabGroupService iModuleLabGroupService, IConfiguration configuration
            , IBaseDataServices baseDataServices, IMemoryCache easyCahceFactory, IUploadFileService uploadFileService, IEquipmentDocService iequipmentDocService, IOperationRecordService operationRecordService)
        {
            _IUploadFileService = uploadFileService;
            _xhEquipmentDocService = equipmentDocService;
            _hostingEnvironment = hostingEnvironment;
            _equipmentDocService = iequipmentDocService;
            _operationRecordService = operationRecordService;
            _baseService = baseService;
            _IModuleLabGroupService = iModuleLabGroupService;
            _configuration = configuration;
            file_preview_address = _configuration["S54"];
            file_upload_address = _configuration["S28"];
            _baseDataServices = baseDataServices;
            RedisModule = _configuration["RedisModule"];
            _easyCacheMemory = easyCahceFactory;

            var binding = new BasicHttpBinding();
            binding.MaxReceivedMessageSize = 241000000;
            _serviceAddress = configuration["ReportService"];
            _client = new XingHePrintServiceSoapClient(binding, new EndpointAddress(_serviceAddress));
        }
        [HttpPost]
        public IActionResult AutoDispatchUploadFile([FromForm] UploadZipDto zipFile)
        {
            var dataDict = new Dictionary<string, object>();
            var claims = User.ToClaimsDto();
            dataDict["HOSPITAL_ID"] = claims.HOSPITAL_ID; //"33A001";
            dataDict["USER_NAME"] = claims.HOSPITAL_ID;//"深远中";
            FileFunctionDispatcher dispatcher = RegisterDispatcher();
            var result = _IUploadFileService.AutoDispatchUploadFile(dispatcher, zipFile, dataDict);
            return Ok(result);
        }

        [HttpGet]
        public IActionResult AutoDispatchUploadFileTest()
        {
            string testPath = @"D:\设备.zip";
            using var stream = new MemoryStream(System.IO.File.ReadAllBytes(testPath).ToArray());
            var formFile = new FormFile(stream, 0, stream.Length, "streamFile", testPath.Split(@"\").Last());
            var zipFile = new UploadZipDto();
            zipFile.FILE = formFile;
            zipFile.FILE_NAME = testPath;

            var dataDict = new Dictionary<string, object>();
            var claims = User.ToClaimsDto();
            dataDict["HOSPITAL_ID"] = "33A001";//claims.HOSPITAL_ID;
            dataDict["USER_NAME"] = "深远中";//claims.HOSPITAL_ID;
            FileFunctionDispatcher dispatcher = RegisterDispatcher();
            var result = _IUploadFileService.AutoDispatchUploadFile(dispatcher, zipFile, dataDict);
            return Ok(result);
        }
        /// <summary>
        /// 隐藏/显示设备
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult HideEquipment([BindRequired] string equipmentId)
        {
            var claims = User.ToClaimsDto();
            var userName = claims.HIS_NAME;
            var res = _xhEquipmentDocService.HideEquipment(equipmentId, userName);
            return Ok(res);
        }

        /// <summary>
        ///   设备树结构（专业组）
        /// </summary>
        /// <param name="areaId">院区ID</param>
        /// <param name="equipmentNo">设备代号</param>
        /// <param name="ifHide">是否隐藏（0：显示；1：隐藏；空：全部）</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetEquipmentListByMgroup(string areaId, string labId, string equipmentNo, string? ifHide ,string? pGroupId)
        {
            var claims = User.ToClaimsDto();
            var result = _xhEquipmentDocService.GetEquipmentListByMgroup(claims.USER_NO, claims.HOSPITAL_ID, equipmentNo, labId, areaId,ifHide);
            if (result is null)
            {
                return Ok(result.ToResultDto());
            }
            foreach (var secondStageTree in result.SecondStageTree)
            {
                secondStageTree.ThirdStageTree.RemoveAll(x => x.FourthStageTree.Count() == 0);
                foreach (var pgNode in secondStageTree.ThirdStageTree)
                {
                    pgNode.FourthStageTree =  pgNode.FourthStageTree.OrderByDescending(x => x.FifthStageTree.Count).ToList();
                }
            }

            result.SecondStageTree.RemoveAll(x => x.ThirdStageTree.Count() == 0);

            if (pGroupId is not null)
            {
                result.SecondStageTree.RemoveAll(x => x.ThirdStageTree.Any(x=>x.PGROUP_ID != pGroupId));
            }
            return Ok(result.ToResultDto());
        }


        /// <summary>
        ///   设备类型树结构（显示设备）
        /// </summary>
        /// <param name="areaId">院区ID</param>
        /// <param name="equipmentNo">设备代号</param>
        /// <param name="ifHide">是否隐藏（0：显示；1：隐藏；空：全部）</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetEquipmentClassList(string areaId, string equipmentNo, string ifHide , string? pGroupId)
        {
            var claims = User.ToClaimsDto();
            var clientMac = GetLocalMac.GetMac();
            currentLabKey = currentLabKey + claims.USER_NO + "." + clientMac;
            var labId = _easyCacheMemory.Get<string>(currentLabKey);
            var result = _xhEquipmentDocService.GetEquipmentClassList(claims.USER_NO, claims.HOSPITAL_ID, equipmentNo, labId, areaId, ifHide);
            
            foreach (var secondStageTree in result.SecondStageTree)
            {
                foreach (var thirdStageTree in secondStageTree.ThirdStageTree)
                {
                    thirdStageTree.FourthStageTree.RemoveAll(x => x.FifthStageTree.Count() == 0);
                    foreach (var pgNode in thirdStageTree.FourthStageTree)
                    {
                        pgNode.FifthStageTree =  pgNode.FifthStageTree.OrderByDescending(x => x.SixthStageTree.Count).ToList();
                    }
                }
            }
            foreach (var secondStageTree in result.SecondStageTree)
            {
                secondStageTree.ThirdStageTree.RemoveAll(x => x.FourthStageTree.Count() == 0);
            }
            result.SecondStageTree.RemoveAll(x => x.ThirdStageTree.Count() == 0);
            
            if (pGroupId is not null)
            {
                foreach (var secondStageTree in result.SecondStageTree)
                {
                    secondStageTree.ThirdStageTree.RemoveAll(x => x.FourthStageTree.Any(x=>x.PGROUP_ID != pGroupId));
                }
            }

            return Ok(result.ToResultDto());
        }

        #region 解释压缩包并自动上传设备附件
        private FileFunctionDispatcher RegisterDispatcher()
        {
            //根节点（固定）
            var root = FileFunctionDispatcher.GetRoot();

            //指定执行方法的顺序
            root.Then(root.ToTry(MatchEquipmentFunc).Then(
                //root.ToTry(MatchEquipmentFileClassFunc).Then(root.ToTry(UpLoadEquipmentFileFunc)),
                root.ToTry(MatchWorkSequentialFunc).Then(root.ToTry(MatchWorkSequentialListClassFunc).Then(root.ToTry(UploadWorkSequentialListFileFunc))),
                root.ToTry(MatchInstallInfoFunc).Then(root.ToTry(MatchInstallInfoNextFunc).Then(root.ToTry(UploadInstallInfoFile))),
                root.ToTry(MatchBaseInfoFunc).Then(root.ToTry(MatchBaseInfoNextFunc).Then(root.ToTry(UploadFile))),
                root.ToTry(MatchEquipmentSecondFunc).Then(root.ToTry(UploadFile))));

            return root;
        }


        Dictionary<string, List<EMS_EQUIPMENT_INFO>> _equipmentInfoCache = new Dictionary<string, List<EMS_EQUIPMENT_INFO>>();
        /// <summary>
        /// 第一级目录匹配设备，并往上下文中写入设备对象
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        FileDispatcherContext MatchEquipmentFunc(FileDispatcherContext context)
        {
            context.Msg += "Level 1 MatchEquipmentFunc: ";
            //获取当前的路径名称
            string equipName = context.CurrentPath.Trim();
            List<EMS_EQUIPMENT_INFO> equips;
            if (!_equipmentInfoCache.ContainsKey(equipName))
            {
                _equipmentInfoCache[equipName] = _xhEquipmentDocService.GetEquipmentListByName(hospitalId: context.DataDict["HOSPITAL_ID"].ToString(), null, null, null, null, null, equipName, null, null);
            }
            equips = _equipmentInfoCache[equipName];
            if (equips.Count == 0)
            {
                context.Success = false;
                context.Msg += $"未找到名称为 {equipName} 的设备记录！\n ";
            }
            else if (equips.Count > 1)
            {
                context.Success = false;
                context.Msg += $"找到多条名称为 {equipName} 的设备记录，请手动添加附件！\n ";
            }
            else if (equips.Count == 1)
            {
                context.DataDict[$"EMS_EQUIPMENT_INFO"] = equips.FirstOrDefault();
                context.Success = true;
                context.Msg += "finished！\n ";
            }
            return context;
        }
        /// <summary>
        /// 调试专用 后续清理
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        FileDispatcherContext MatchFirstFunc(FileDispatcherContext context)
        {
            context.Msg += "Level 0 MatchEquipmentFunc: ";
            //获取当前的路径名称
            string equipName = context.CurrentPath.Trim();
            if (equipName.ToUpper() == "设备")
            {
                context.Success = true;
                context.Msg += "finished！\n ";
            }
            else
            {
                context.Success = false;
                context.Msg += "pass！\n ";
            }
            return context;
        }

        /// <summary>
        /// SOP档案、设备说明书、资质证书类型路径判断
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        FileDispatcherContext MatchEquipmentFileClassFunc(FileDispatcherContext context)
        {
            context.Msg += "Level 2 MatchEquipmentFileClassFunc: ";
            //获取当前的路径名称
            string className = context.CurrentPath.Trim();
            if (className.ToUpper() == "SOP档案" || className.ToUpper() == "设备说明书" || className.ToUpper() == "资质证书")
            {
                context.DataDict["DOC_CLASS"] = className.ToUpper();
                context.Success = true;
                context.Msg += "finished！\n ";
            }
            else
            {
                context.Success = false;
                context.Msg += "pass！\n ";
            }
            return context;
        }

        FileDispatcherContext UpLoadEnclosure(FileDispatcherContext context, string DOC_CLASS, string DOC_INFO_ID)
        {
            try
            {
                var uploadFiles = new UploadFileDto();
                using (var stream = new MemoryStream(System.IO.File.ReadAllBytes(context.PysicalPath).ToArray()))
                {
                    uploadFiles.FILE = new FormFile(stream, 0, stream.Length, "streamFile", context.PysicalPath.Split(new char[] { '/', '\\' }).Last());
                    uploadFiles.DOC_CLASS = DOC_CLASS;
                    uploadFiles.DOC_INFO_ID = DOC_INFO_ID;
                    uploadFiles.DOC_NAME = context.CurrentPath;
                    uploadFiles.DOC_SUFFIX = context.CurrentPath.IsNullOrEmpty() ? "" : "." + context.CurrentPath.Split('.').Last();
                    var result = _baseService.UploadEnclosureInfo(uploadFiles, context.DataDict["USER_NAME"].ToString(), context.DataDict["HOSPITAL_ID"].ToString());

                    if (result.success)
                    {
                        context.Success = true;
                        context.Msg += "finished！\n ";
                    }
                    else
                    {
                        context.Success = false;
                        context.Msg += $"上传{DOC_CLASS}返回失败：{result.msg}\n ";
                    }
                }
            }
            catch (Exception ex)
            {
                context.Success = false;
                context.Msg += $"上传{DOC_CLASS}失败：{ex.Message}\n ";
            }
            return context;
        }

        /// <summary>
        /// SOP档案、设备说明书、资质证书类型文件上传
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        FileDispatcherContext UpLoadEquipmentFileFunc(FileDispatcherContext context)
        {
            context.Msg += "Level 3 UpLoadEquipmentFileFunc: ";
            //获取当前的路径名称
            string className = context.CurrentPath.Trim();
            string docClass = context.DataDict["DOC_CLASS"].ToString();
            FileDispatcherContext retContext = UpLoadEnclosure(context, docClass, (context.DataDict["EMS_EQUIPMENT_INFO"] as EMS_EQUIPMENT_INFO)?.EQUIPMENT_ID);
            return retContext;
        }


        /// <summary>
        /// 运行记录路径判断
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        FileDispatcherContext MatchWorkSequentialFunc(FileDispatcherContext context)
        {
            context.Msg += "Level 2 MatchWorkSequentialFunc: ";
            //获取当前的路径名称
            string className = context.CurrentPath.Trim();
            if (className.ToUpper() == "运行记录")
            {
                context.Success = true;
                context.Msg += "finished！\n ";
            }
            else
            {
                context.Success = false;
                context.Msg += "pass！\n ";
            }
            return context;
        }

        /// <summary>
        /// 运行记录之下的类型判断
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        FileDispatcherContext MatchWorkSequentialListClassFunc(FileDispatcherContext context)
        {
            context.Msg += "Level 3 MatchWorkSequentialListClassFunc: ";
            //获取当前的路径名称
            string className = context.CurrentPath.Trim();
            if (className.ToUpper() == "保养" || className.ToUpper() == "维修" || className.ToUpper() == "校准检定" || className.ToUpper() == "比对" || className.ToUpper() == "性能验证" || className.ToUpper() == "变更")
            {
                context.DataDict["WorkSequentialListClass"] = className.ToUpper();
                context.Success = true;
                context.Msg += "finished！\n ";
            }
            else
            {
                context.Success = false;
                context.Msg += "pass！\n ";
            }
            return context;
        }

        Dictionary<string, string> _DOC_INFO_ID_CACHE = new Dictionary<string, string>();
        FileDispatcherContext UploadWorkSequentialListFileFunc(FileDispatcherContext context)
        {
            context.Msg += "Level 4 UploadWorkSequentialListFileFunc: ";

            string docClass = "";
            string docInfoId = "";
            var claims = User.ToClaimsDto();
            string nowPerson = claims.HIS_NAME;
            string equipmentId = (context.DataDict[$"EMS_EQUIPMENT_INFO"] as EMS_EQUIPMENT_INFO).EQUIPMENT_ID;
            string workSequentialListClass = context.DataDict["WorkSequentialListClass"].ToString();
            DateTime dt = DateTime.Now;
            if (!_DOC_INFO_ID_CACHE.ContainsKey($"{equipmentId}_{workSequentialListClass}"))
            {
                switch (workSequentialListClass)
                {
                    case "保养":
                        {
                            docClass = "保养记录";
                            var record = new EMS_MAINTAIN_INFO();
                            record.MAINTAIN_ID = IDGenHelper.CreateGuid().ToString();
                            record.EQUIPMENT_ID = equipmentId;
                            record.HOSPITAL_ID = claims.HOSPITAL_ID;
                            record.FIRST_RPERSON = nowPerson;
                            record.FIRST_RTIME = dt;
                            record.LAST_MPERSON = nowPerson;
                            record.LAST_MTIME = dt;
                            record.MAINTAIN_STATE = "1";
                            var res = _operationRecordService.SaveMaintainInfo(record);
                            docInfoId = res?.MAINTAIN_ID ?? "";
                        }
                        break;

                    case "维修":
                        {
                            docClass = "维修记录";
                            var record = new EMS_REPAIR_INFO();
                            record.REPAIR_ID = IDGenHelper.CreateGuid().ToString();
                            record.EQUIPMENT_ID = equipmentId;
                            record.HOSPITAL_ID = claims.HOSPITAL_ID;
                            record.FIRST_RPERSON = nowPerson;
                            record.FIRST_RTIME = dt;
                            record.LAST_MPERSON = nowPerson;
                            record.LAST_MTIME = dt;
                            record.REPAIR_STATE = "1";
                            var res = _operationRecordService.SaveRepairInfo(record);
                            docInfoId = res?.REPAIR_ID ?? "";
                        }
                        break;

                    case "校准检定":
                        {
                            docClass = "校准记录";
                            var record = new EMS_CORRECT_INFO();
                            record.CORRECT_ID = IDGenHelper.CreateGuid().ToString();
                            record.EQUIPMENT_ID = equipmentId;
                            record.HOSPITAL_ID = claims.HOSPITAL_ID;
                            record.FIRST_RPERSON = nowPerson;
                            record.FIRST_RTIME = dt;
                            record.LAST_MPERSON = nowPerson;
                            record.LAST_MTIME = dt;
                            record.CORRECT_STATE = "1";
                            record.STATE = "已执行";
                            var res = _operationRecordService.SaveCorrectInfo(record);
                            docInfoId = res?.CORRECT_ID ?? "";
                        }
                        break;

                    case "比对":
                        {
                            docClass = "比对记录";
                            var record = new EMS_COMPARISON_INFO();
                            record.COMPARISON_ID = IDGenHelper.CreateGuid().ToString();
                            record.EQUIPMENT_ID = equipmentId;
                            record.HOSPITAL_ID = claims.HOSPITAL_ID;
                            record.FIRST_RPERSON = nowPerson;
                            record.FIRST_RTIME = dt;
                            record.LAST_MPERSON = nowPerson;
                            record.LAST_MTIME = dt;
                            record.COMPARISON_STATE = "1";
                            record.STATE = "已执行";
                            var res = _operationRecordService.SaveComparisonInfo(record);
                            docInfoId = res?.COMPARISON_ID ?? "";
                        }
                        break;

                    case "性能验证":
                        {
                            docClass = "性能验证记录";
                            var record = new EMS_VERIFICATION_INFO();
                            record.VERIFICATION_ID = IDGenHelper.CreateGuid().ToString();
                            record.EQUIPMENT_ID = equipmentId;
                            record.HOSPITAL_ID = claims.HOSPITAL_ID;
                            record.FIRST_RPERSON = nowPerson;
                            record.FIRST_RTIME = dt;
                            record.LAST_MPERSON = nowPerson;
                            record.LAST_MTIME = dt;
                            record.VERIFICATION_STATE = "1";
                            record.STATE = "已执行";
                            var res = _operationRecordService.SaveVerificationInfo(record);
                            docInfoId = res?.VERIFICATION_ID ?? "";
                        }
                        break;

                    case "变更":
                        {
                            docClass = "变更记录";
                            var record = new EMS_CHANGE_INFO();
                            record.CHANGE_ID = IDGenHelper.CreateGuid().ToString();
                            record.EQUIPMENT_ID = equipmentId;
                            record.HOSPITAL_ID = claims.HOSPITAL_ID;
                            record.FIRST_RPERSON = nowPerson;
                            record.FIRST_RTIME = dt;
                            record.LAST_MPERSON = nowPerson;
                            record.LAST_MTIME = dt;
                            record.CHANGE_STATE = "1";
                            var res = _operationRecordService.SaveChangeInfo(record);
                            docInfoId = res?.CHANGE_ID ?? "";
                        }
                        break;
                }
                _DOC_INFO_ID_CACHE[$"{equipmentId}_{workSequentialListClass}"] = docInfoId;
            }
            else
            {
                //关联到相同的运行记录中
                docInfoId = _DOC_INFO_ID_CACHE[$"{equipmentId}_{workSequentialListClass}"];
                switch (workSequentialListClass)
                {
                    case "保养":
                        docClass = "保养记录";
                        break;

                    case "维修":
                        docClass = "维修记录";
                        break;

                    case "校准检定":
                        docClass = "校准记录";
                        break;

                    case "比对":
                        docClass = "比对记录";
                        break;

                    case "性能验证":
                        docClass = "性能验证记录";
                        break;

                    case "变更":
                        docClass = "变更记录";
                        break;
                }
            }
            if (docInfoId.IsNotNullOrEmpty())
            {
                FileDispatcherContext retContext = UpLoadEnclosure(context, docClass, docInfoId);
                return retContext;
            }
            else
            {
                context.Success = false;
                context.Msg += $"上传{docClass}失败：新增对应记录失败 \n ";
            }
            return context;
        }

        /// <summary>
        /// 安装信息处理（第二层）
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        FileDispatcherContext MatchInstallInfoFunc(FileDispatcherContext context)
        {
            context.Msg += "Level 2 MatchInstallInfoFunc: ";
            string className = context.CurrentPath.Trim();
            if (className == "安装信息")
            {
                context.Success = true;
                context.DataDict["DOC_CLASS"] = "安装信息";
                context.Msg += "finished！\n ";
            }
            else
            {
                context.Success = false;
                context.Msg += "pass！\n ";
            }

            return context;
        }

        /// <summary>
        /// 基本处理（第二层）
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        FileDispatcherContext MatchBaseInfoFunc(FileDispatcherContext context)
        {
            context.Msg += "Level 2 MatchInstallInfoFunc: ";
            string className = context.CurrentPath.Trim();
            if (className == "基本信息")
            {
                context.Success = true;
                context.DataDict["DOC_CLASS"] = "基本信息";
                context.Msg += "finished！\n ";
            }
            else
            {
                context.Success = false;
                context.Msg += "pass！\n ";
            }

            return context;
        }
        /// <summary>
        /// 基本信息下一层级（第三层）
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        FileDispatcherContext MatchBaseInfoNextFunc(FileDispatcherContext context)
        {
            context.Msg += "Level 3 MatchBaseInfoNextFunc: ";
            string className = context.CurrentPath.Trim();
            if (className == "外观信息")
            {
                context.Success = true;
                context.DataDict["DOC_CLASS"] = className;
                context.Msg += "finished！\n ";
            }
            else
            {
                context.Success = false;
                context.Msg += "pass！\n ";
            }

            return context;
        }
        /// <summary>
        /// 通用二级目录
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        FileDispatcherContext MatchEquipmentSecondFunc(FileDispatcherContext context)
        {
            context.Msg += "Level 2 MatchEquipmentSecondFunc: ";
            List<string> listSecond = new List<string>() { "基本信息", "商务信息", "启停信息", "报废信息", "资质证书", "设备说明书", "SOP档案" };
            string className = context.CurrentPath.Trim();
            if (listSecond.Contains(className))
            {
                context.Success = true;
                context.DataDict["DOC_CLASS"] = className;
                context.Msg += "finished！\n ";
            }
            else
            {
                context.Success = false;
                context.Msg += "pass！\n ";
            }

            return context;
        }

        /// <summary>
        /// 安装信息下一层级（第三层）
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        FileDispatcherContext MatchInstallInfoNextFunc(FileDispatcherContext context)
        {
            List<string> listNext = new List<string>() { "开箱记录", "安装记录", "调试和运行记录", "培训记录", "授权记录", "开机性能验证报告" };
            context.Msg += "Level 3 MatchInstallInfoNextFunc: ";
            string className = context.CurrentPath.Trim();
            if (listNext.Contains(className))
            {
                context.Success = true;
                context.DataDict["DOC_CLASS"] = className;
                context.Msg += "finished！\n ";
            }
            else
            {
                context.Success = false;
                context.Msg += "pass！\n ";
            }

            return context;
        }
        /// <summary>
        /// 通用上传附件(第3层目录)
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        FileDispatcherContext UploadFile(FileDispatcherContext context)
        {
            context.Msg += "Level 3 UploadFile: ";
            string EQUIPMENT_ID = (context.DataDict["EMS_EQUIPMENT_INFO"] as EMS_EQUIPMENT_INFO)?.EQUIPMENT_ID;
            //获取当前的路径名称
            string className = context.CurrentPath.Trim();
            string DOC_CLASS = context.DataDict["DOC_CLASS"].ToString();
            if (DOC_CLASS == "启停信息")
            {
                List<EMS_START_STOP> listStop = _equipmentDocService.GetStartStopList(EQUIPMENT_ID);
                if (listStop != null && listStop.Count > 0)
                    EQUIPMENT_ID = listStop[0].START_STOP_ID;
                else
                    //context.Success = false;
                    //context.Msg += "查询启停信息失败！\n ";
                    return context;
            }
            try
            {
                var uploadFiles = new UploadFileDto();
                using (var stream = new MemoryStream(System.IO.File.ReadAllBytes(context.PysicalPath).ToArray()))
                {
                    uploadFiles.FILE = new FormFile(stream, 0, stream.Length, "streamFile", context.PysicalPath.Split(new char[] { '/', '\\' }).Last());
                    uploadFiles.DOC_CLASS = DOC_CLASS;
                    uploadFiles.DOC_INFO_ID = EQUIPMENT_ID;
                    uploadFiles.DOC_NAME = context.CurrentPath;
                    uploadFiles.DOC_SUFFIX = context.CurrentPath.IsNullOrEmpty() ? "" : "." + context.CurrentPath.Split('.').Last();
                    var result = _baseService.UploadEnclosureInfo(uploadFiles, context.DataDict["USER_NAME"].ToString(), context.DataDict["HOSPITAL_ID"].ToString());
                    if (result.success)
                    {
                        context.Success = true;
                        context.Msg += "finished！\n ";
                    }
                    else
                    {
                        context.Success = false;
                        context.Msg += $"上传附件:{uploadFiles.DOC_CLASS}返回失败：{result.msg}\n ";
                    }
                }
            }
            catch (Exception ex)
            {
                context.Success = false;
                context.Msg += $"上传附件:{DOC_CLASS}：{ex.Message}\n ";
            }
            return context;
        }

        /// <summary>
        /// 上传安装信息记录附件（第4层）
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        FileDispatcherContext UploadInstallInfoFile(FileDispatcherContext context)
        {
            string EQUIPMENT_ID = (context.DataDict["EMS_EQUIPMENT_INFO"] as EMS_EQUIPMENT_INFO)?.EQUIPMENT_ID;
            string DOC_CLASS = context.DataDict["DOC_CLASS"].ToString();
            string DOC_INFO_ID = "";
            switch (DOC_CLASS)
            {
                case "开箱记录":
                    EMS_UNPACK_INFO unPack = _equipmentDocService.GetUnpackInfo(EQUIPMENT_ID);
                    //查询开箱记录 没有则自动插入一条
                    if (unPack == null)
                    {
                        unPack = new EMS_UNPACK_INFO();
                        unPack.UNPACK_ID = IDGenHelper.CreateGuid().ToString();
                        unPack.UNPACK_PERSON = context.DataDict["USER_NAME"].ToString();
                        unPack.UNPACK_DATE = DateTime.Now;
                        unPack.EQUIPMENT_ID = EQUIPMENT_ID;
                        unPack.HOSPITAL_ID = context.DataDict["HOSPITAL_ID"].ToString();
                        unPack.FIRST_RPERSON = context.DataDict["USER_NAME"].ToString();
                        unPack.FIRST_RTIME = DateTime.Now;
                        unPack.LAST_MPERSON = context.DataDict["USER_NAME"].ToString();
                        unPack.LAST_MTIME = DateTime.Now;
                        unPack = _equipmentDocService.SaveUnpackInfo(unPack);
                        if (unPack == null)
                        {
                            context.Success = false;
                            context.Msg += "查询开箱记录信息失败";
                            return context;
                        }
                    }
                    //查询配件信息 没有则自动插入一条 有则默认取第一条记录
                    List<EMS_PARTS_INFO> listParts = _equipmentDocService.GetPartsList(EQUIPMENT_ID);
                    EMS_PARTS_INFO record = null;
                    if (listParts == null || listParts.Count == 0)
                    {
                        record = new EMS_PARTS_INFO();
                        record.EQUIPMENT_ID = EQUIPMENT_ID;
                        record.PARTS_ID = IDGenHelper.CreateGuid().ToString();
                        record.PARTS_NAME = "上传附件";
                        record.PARTS_STATE = "1";
                        record.HOSPITAL_ID = context.DataDict["HOSPITAL_ID"].ToString();
                        record.FIRST_RPERSON = context.DataDict["USER_NAME"].ToString();
                        record.FIRST_RTIME = DateTime.Now;
                        record.LAST_MPERSON = context.DataDict["USER_NAME"].ToString();
                        record.FIRST_RTIME = DateTime.Now;
                        record = _equipmentDocService.SavePartsInfo(record);
                        if (record == null)
                        {
                            context.Success = false;
                            context.Msg += "查询配件信息失败";
                            return context;
                        }
                    }
                    else
                        record = listParts.FirstOrDefault();
                    DOC_INFO_ID = record.PARTS_ID;
                    break;
                case "培训记录":
                    List<EMS_TRAIN_INFO> listTrainInfo = _equipmentDocService.GetTrainList(EQUIPMENT_ID);
                    //查询培训记录 没有则自动插入一条
                    if (listTrainInfo == null || listTrainInfo.Count == 0)
                    {
                        EMS_TRAIN_INFO trainInfo = new EMS_TRAIN_INFO();
                        trainInfo.TRAIN_ID = IDGenHelper.CreateGuid().ToString();
                        trainInfo.TRAIN_NAME = context.DataDict["USER_NAME"].ToString();
                        trainInfo.TRAIN_STATE = "1";
                        trainInfo.TRAIN_TIME = DateTime.Now;
                        trainInfo.EQUIPMENT_ID = EQUIPMENT_ID;
                        trainInfo.HOSPITAL_ID = context.DataDict["HOSPITAL_ID"].ToString();
                        trainInfo.FIRST_RPERSON = context.DataDict["USER_NAME"].ToString();
                        trainInfo.FIRST_RTIME = DateTime.Now;
                        trainInfo.LAST_MPERSON = context.DataDict["USER_NAME"].ToString();
                        trainInfo.LAST_MTIME = DateTime.Now;
                        trainInfo = _equipmentDocService.SaveTrainInfo(trainInfo);
                        if (trainInfo == null)
                        {
                            context.Success = false;
                            context.Msg += "查询培训记录信息失败";
                            return context;
                        }
                        DOC_INFO_ID = trainInfo.TRAIN_ID;
                    }
                    else
                        DOC_INFO_ID = listTrainInfo.FirstOrDefault().TRAIN_ID;
                    break;
                case "授权记录":
                    List<EMS_AUTHORIZE_INFO> listAuthInfo = _equipmentDocService.GetAuthorizeList(EQUIPMENT_ID);
                    //查询培训记录 没有则自动插入一条
                    if (listAuthInfo == null || listAuthInfo.Count == 0)
                    {
                        EMS_AUTHORIZE_INFO authInfo = new EMS_AUTHORIZE_INFO();
                        authInfo.AUTHORIZE_ID = IDGenHelper.CreateGuid().ToString();
                        authInfo.AUTHORIZE_PERSON = context.DataDict["USER_NAME"].ToString();
                        authInfo.AUTHORIZE_STATE = "1";
                        authInfo.AUTHORIZE_DATE = DateTime.Now;
                        authInfo.EQUIPMENT_ID = EQUIPMENT_ID;
                        authInfo.HOSPITAL_ID = context.DataDict["HOSPITAL_ID"].ToString();
                        authInfo.FIRST_RPERSON = context.DataDict["USER_NAME"].ToString();
                        authInfo.FIRST_RTIME = DateTime.Now;
                        authInfo.LAST_MPERSON = context.DataDict["USER_NAME"].ToString();
                        authInfo.LAST_MTIME = DateTime.Now;
                        authInfo = _equipmentDocService.SaveAuthorizeInfo(authInfo);
                        if (authInfo == null)
                        {
                            context.Success = false;
                            context.Msg += "查询授权记录信息失败";
                            return context;
                        }
                        DOC_INFO_ID = authInfo.AUTHORIZE_ID;
                    }
                    else
                        DOC_INFO_ID = listAuthInfo.FirstOrDefault().AUTHORIZE_ID;
                    break;
                default:
                    DOC_INFO_ID = EQUIPMENT_ID;
                    break;
            }
            context.Msg += "Level 4 UploadUnPackFile:";
            //获取当前的路径名称
            string className = context.CurrentPath.Trim();
            try
            {
                var uploadFiles = new UploadFileDto();
                using (var stream = new MemoryStream(System.IO.File.ReadAllBytes(context.PysicalPath).ToArray()))
                {
                    uploadFiles.FILE = new FormFile(stream, 0, stream.Length, "streamFile", context.PysicalPath.Split(new char[] { '/', '\\' }).Last());
                    uploadFiles.DOC_CLASS = DOC_CLASS;
                    uploadFiles.DOC_INFO_ID = DOC_INFO_ID;
                    uploadFiles.DOC_NAME = context.CurrentPath;
                    uploadFiles.DOC_SUFFIX = context.CurrentPath.IsNullOrEmpty() ? "" : "." + context.CurrentPath.Split('.').Last();
                    var result = _baseService.UploadEnclosureInfo(uploadFiles, context.DataDict["USER_NAME"].ToString(), context.DataDict["HOSPITAL_ID"].ToString());
                    if (result.success)
                    {
                        context.Success = true;
                        context.Msg += "finished！\n ";
                    }
                    else
                    {
                        context.Success = false;
                        context.Msg += $"上传附件:{uploadFiles.DOC_CLASS}返回失败：{result.msg}\n ";
                    }
                }
            }
            catch (Exception ex)
            {
                context.Success = false;
                context.Msg += $"上传附件:{DOC_CLASS}：{ex.Message}\n ";
            }
            return context;
        }
        #endregion

        /// <summary>
        /// 生成预览文件
        /// </summary>
        /// <param name="equipmentId"></param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GenerateEquipmentInfo(string equipmentId)
        {
            var claims = User.ToClaimsDto();
            var res = _xhEquipmentDocService.GenerateEquipmentInfo(equipmentId, claims.USER_NAME, claims.HOSPITAL_ID);
            return Ok(res);
        }

        /// <summary>
        /// 标识卡打印
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult CardPrint([BindRequired] string? cardType, List<string> equipmentId)
        {
            var res = _equipmentDocService.GetCardElement(cardType, equipmentId);
            return Ok(res.ToResultDto());
        }


        /// <summary>
        /// 导入设备列表
        /// </summary>
        /// <param name="record"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult ExportEquipmentList([FromBody] List<ExportEquipmentDto> record)
        {
            var claims = User.ToClaimsDto();
            var clientMac = GetLocalMac.GetMac();
            currentLabKey = currentLabKey + claims.USER_NO + "." + clientMac;
            var labId = _easyCacheMemory.Get<string>(currentLabKey);
            var userName = claims.HIS_NAME;
            var res = _xhEquipmentDocService.ExportEquipmentList(record, labId, claims.HOSPITAL_ID, userName);
            return Ok(res);
        }

        /// <summary>
        /// 获取标识卡打印模板
        /// </summary>
        /// <param name="equipmentId"></param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetCardTypeList()
        {
            var claims = User.ToClaimsDto();
            var res = _xhEquipmentDocService.GetCardTypeList();
            return Ok(res.ToResultDto());
        }


        /// <summary>
        /// 设置门禁设备的门禁方向
        /// </summary>
        /// <param name="equipmentId"></param>
        /// <param name="isIn"></param>
        /// <returns></returns>
        [HttpPost("{equipmentId}")]
        public IActionResult SetEquipmentIsIn(string equipmentId, int isIn)
        {
            var equipment =  _equipmentDocService.GetEquipmentInfo(equipmentId);
            if (equipment is null)
            {
                throw new BizException("当前设备不存在");
            }

            if (equipment.SMBL_CLASS =="8")
            {
                var entranceEquipment = new EntranceEquipment()
                {
                    EQUIPMENT_JSON = equipment.EQUIPMENT_JSON,
                    IsIn = isIn
                } ;
                equipment.EQUIPMENT_JSON = entranceEquipment.EQUIPMENT_JSON;
                _equipmentDocService.SaveEquipmentInfo(equipment);
            }
            return Ok(true.ToResultDto());
        }
    }
}
