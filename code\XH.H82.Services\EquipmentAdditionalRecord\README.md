# 设备扩展记录服务 (EquipmentAdditionalRecordService)

## 概述

设备扩展记录服务用于处理设备档案记录的扩展字段数据。该服务可以：

1. 查询设备的档案记录数据（如校准记录、调试记录等）
2. 查询对应的扩展字段数据（存储在 EMS_EQP_ADDN_RECORD 表中）
3. 将两者结合返回完整的档案记录信息

## 数据表关系

- **EMS_EQP_ARCHIVES_DICT**: 档案记录字典表，定义了档案记录的类型和配置
- **EMS_EQP_ADDN_RECORD**: 设备扩展记录表，存储设备的扩展字段数据
- **EMS_EQUIPMENT_INFO**: 设备信息表
- **EMS_CORRECT_INFO**: 校准记录表
- **EMS_DEBUG_INFO**: 调试记录表
- **EMS_TRAIN_INFO**: 培训记录表

## 主要功能

### 1. 获取单个档案记录详细信息

```csharp
// 获取设备的某个档案记录详细信息（包含业务数据和扩展字段）
var detail = service.GetEquipmentArchiveDetail(equipmentId, eqpArchivesId);
```

返回的 `EquipmentArchiveDetailDto` 包含：
- 档案记录基本信息（来自 EMS_EQP_ARCHIVES_DICT）
- 业务数据（如校准记录、调试记录等）
- 扩展字段数据（来自 EMS_EQP_ADDN_RECORD）

### 2. 获取设备所有档案记录

```csharp
// 获取设备的所有档案记录详细信息列表
var details = service.GetEquipmentArchiveDetails(equipmentId);
```

### 3. 管理扩展记录

```csharp
// 保存或更新扩展记录
var record = new EMS_EQP_ADDN_RECORD
{
    EquipmentId = "设备ID",
    EqpArchivesId = "档案记录ID", 
    EquitmentJson = "扩展字段JSON数据",
    // ... 其他字段
};
var result = service.SaveEquipmentAdditionalRecord(record);

// 删除扩展记录（软删除）
var result = service.DeleteEquipmentAdditionalRecord(eqpRecordId);
```

## API 接口

### GET /api/EquipmentAdditionalRecord/GetArchiveDetail
获取设备的档案记录详细信息

参数：
- `equipmentId`: 设备ID
- `eqpArchivesId`: 档案记录ID

### GET /api/EquipmentAdditionalRecord/GetArchiveDetails  
获取设备的所有档案记录详细信息列表

参数：
- `equipmentId`: 设备ID

### POST /api/EquipmentAdditionalRecord/SaveAdditionalRecord
保存或更新设备扩展记录

请求体：EMS_EQP_ADDN_RECORD 对象

### DELETE /api/EquipmentAdditionalRecord/DeleteAdditionalRecord
删除设备扩展记录

参数：
- `eqpRecordId`: 记录ID

## 使用示例

### 前端调用示例

```javascript
// 获取设备档案记录详细信息
const getArchiveDetail = async (equipmentId, eqpArchivesId) => {
    const response = await fetch(`/api/EquipmentAdditionalRecord/GetArchiveDetail?equipmentId=${equipmentId}&eqpArchivesId=${eqpArchivesId}`);
    const result = await response.json();
    
    if (result.success) {
        const detail = result.data;
        console.log('档案记录名称:', detail.eqpArchivesName);
        console.log('业务数据:', detail.businessData);
        console.log('扩展字段:', detail.additionalRecord?.equitmentJson);
    }
};

// 保存扩展记录
const saveAdditionalRecord = async (record) => {
    const response = await fetch('/api/EquipmentAdditionalRecord/SaveAdditionalRecord', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(record)
    });
    const result = await response.json();
    return result;
};
```

## 扩展说明

### 业务数据映射

服务会根据档案记录ID自动映射到对应的业务表：

- 校准记录相关 → EMS_CORRECT_INFO
- 调试记录相关 → EMS_DEBUG_INFO  
- 培训记录相关 → EMS_TRAIN_INFO

可以在 `GetBusinessDataByArchiveId` 方法中添加更多的映射规则。

### 扩展字段存储

扩展字段数据以JSON格式存储在 `EMS_EQP_ADDN_RECORD.EQUITMENT_JSON` 字段中，可以存储任意结构的数据。

## 注意事项

1. 所有删除操作都是软删除，通过设置 `EqpRecordState = "2"` 实现
2. 服务会自动验证设备和档案记录的存在性
3. 支持并发操作，使用数据库事务保证数据一致性
4. 所有操作都会记录操作人和操作时间
