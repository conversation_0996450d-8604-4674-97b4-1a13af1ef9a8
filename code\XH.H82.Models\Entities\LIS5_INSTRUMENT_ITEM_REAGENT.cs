﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using H.Utility.SqlSugarInfra;
using SqlSugar;

namespace XH.H82.Models.Entities
{
    [DBOwner("XH_SYS")]
    public class LIS5_INSTRUMENT_ITEM_REAGENT
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string REAGENT_ID { get; set; }
        public string UNIT_ID { get; set; }
        public string INSTRUMENT_ID { get; set; }
        public string HOSPITAL_ID { get; set; }
        public string LAB_ID { get; set; }
        public string ITEM_ID { get; set; }
        public string MUNIT_ID { get; set; }
        public string MATERIAL_ID { get; set; }
        public string MATERIAL_NAME { get; set; }
        public string MATERIAL_SPEC { get; set; }
        public string INSTRUMENT_SNUM { get; set; }
        public string TEST_INSTRUMENT { get; set; }
        public string TEST_CHANNEL { get; set; }
        public double TEST_AMOUNT { get; set; }
        public string REAGENT_STATE { get; set; }
        public string FIRST_RPERSON { get; set; }
        public string FIRST_RTIME { get; set; }
        public string LAST_MPERSON { get; set; }
        public string LAST_MTIME { get; set; }
        public string REMARK { get; set; }

    }
}
