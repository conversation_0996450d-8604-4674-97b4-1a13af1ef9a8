﻿using H.BASE;
using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using Microsoft.AspNetCore.Http;
using XH.H82.IServices.oaBaseData;
using XH.H82.Models;
using XH.H82.Models.SugarDbContext;
using XH.LAB.UTILS.Models.Entites;

namespace XH.H82.Services;

public class OaBasedataService : IOaBasedataService
{
    private readonly ISqlSugarUow<SugarDbContext_Master> _dbContext;
    private readonly IHttpContextAccessor _httpContext;
    
    public OaBasedataService(ISqlSugarUow<SugarDbContext_Master> dbContext, IHttpContextAccessor httpContext)
    {
        _dbContext = dbContext;
        _httpContext = httpContext;

        _dbContext.SetCreateTimeAndCreatePersonData(_httpContext.HttpContext.User.ToClaimsDto());
    }
    public OA_BASE_DATA AddOaBaseData(string? fatherId, string classId, OaBaseDataContent content)
    {
        if (content.DataId.IsNotNullOrEmpty())
        {
            var hasData = GetOaBaseData( fatherId ,classId, content.DataId!);
            if (hasData != null)
            {
                return hasData;
            }
        }
        var data = new OA_BASE_DATA()
        {
            MODULE_ID = AppSettingsProvider.CurrModuleId,
            FATHER_ID = fatherId ?? "",
            CLASS_ID = classId,
            DATA_ID = content.DataId ?? IDGenHelper.CreateGuid(),
            DATA_SORT = content.Sort ?? "000",
            DATA_NAME = content.DataName ?? "",
            DATA_SNAME = content.DataSname ?? "",
            DATA_ENAME = content.DataEname ?? "",
            STANDART_ID = content.StandartId ?? "",
            CUSTOM_CODE = content.CustomCode ?? "",
            SPELL_CODE = content.SpellCode ?? "",
            STATE_FLAG = "1",
            REMARK = content.Remark ?? "",
        };
        _dbContext.Db.Insertable(data).ExecuteCommand();
        return data;
    }

    public OA_BASE_DATA UpdataOaBaseData(string? fatherId, string classId, OaBaseDataContent content)
    {
        if (content.DataId.IsNotNullOrEmpty())
        {
            var result = GetOaBaseData(fatherId, classId, content.DataId!);
            if (result is null)
            {
                throw new Exception("数据不存在");
            }
            result.DATA_NAME = content.DataName ?? result.DATA_NAME;
            result.DATA_SNAME = content.DataSname ?? result.DATA_SNAME;
            result.DATA_ENAME = content.DataEname ?? result.DATA_ENAME;
            result.DATA_SORT = content.Sort ?? result.DATA_SORT;
            result.REMARK = content.Remark ?? result.REMARK;
            result.CUSTOM_CODE = content.CustomCode ?? result.CUSTOM_CODE;
            result.SPELL_CODE = content.SpellCode ?? result.SPELL_CODE;
            result.STANDART_ID = content.StandartId ?? result.STANDART_ID;
            _dbContext.Db.Updateable(result).ExecuteCommand();
            return result;
        }
        throw  new BizException("数据ID不能为空");
    }

    public void DeleteOaBaseData(string? fatherId, string classId, string dataId, bool isDeleted = false)
    {
        var data = GetOaBaseData( fatherId ,classId, dataId);
        if (data is not null)
        {
            if (isDeleted)
            {
                _dbContext.Db.Deleteable(data).ExecuteCommand();
            }
            else
            {
                data.STATE_FLAG = "2";
                _dbContext.Db.Updateable(data).ExecuteCommand();
            }
        }
    }
    public OA_BASE_DATA? GetOaBaseData(string? fatherId, string classId, string dataId)
    {
        var user = _httpContext.HttpContext.User.ToClaimsDto();
        var  result =  _dbContext.Db.Queryable<OA_BASE_DATA>()
            .Where(x => x.HOSPITAL_ID == user.HOSPITAL_ID)
            .Where(x => x.MODULE_ID == AppSettingsProvider.CurrModuleId)
            .WhereIF(fatherId.IsNotNullOrEmpty(), x => x.FATHER_ID == fatherId)
            .WhereIF(classId.IsNotNullOrEmpty(), x => x.CLASS_ID == classId)
            .First();
        return result;
    }

    public List<OA_BASE_DATA> GetOaBaseDatas(string? fatherId, string? classId)
    {
        var user = _httpContext.HttpContext.User.ToClaimsDto();
        var result = Array.Empty<OA_BASE_DATA>().ToList();
        _dbContext.Db.Queryable<OA_BASE_DATA>()
            .Where(x=>x.HOSPITAL_ID == user.HOSPITAL_ID)
            .Where(x=>x.MODULE_ID == AppSettingsProvider.CurrModuleId)
            .WhereIF(fatherId.IsNotNullOrEmpty(), x => x.FATHER_ID == fatherId)
            .WhereIF(classId.IsNotNullOrEmpty(), x => x.CLASS_ID == classId)
            .ForEach(x => result.Add(x));
        return result;
    }

    public void DisableOrEnableOaBaseData(string? fatherId, string classId, string dataId)
    {
        var data = GetOaBaseData(fatherId, classId, dataId);
        if (data is not null)
        {
            data.STATE_FLAG = data.STATE_FLAG == "1" ? "0" : "1";
            _dbContext.Db.Updateable(data).ExecuteCommand();
        }
    }
}