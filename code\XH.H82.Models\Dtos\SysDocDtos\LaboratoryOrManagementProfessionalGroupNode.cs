﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.Dtos.SysDocDtos
{
    public class LaboratoryOrManagementProfessionalGroupNode
    {
        [JsonProperty("MGROUP_ID")]
        public string MgroupId { get; set; }

        [<PERSON><PERSON><PERSON>roper<PERSON>("MGROUP_NAME")]
        public string MgroupName { get; set; }

        [JsonProperty("TYPE_NAME")]
        public string TypeName { get; set; }
        [JsonProperty("DocComTypeDto")]
        public List<InspectionProfessionalGroupNode> ProfessionalGroupNodes { get; set; }
        
    }
}
