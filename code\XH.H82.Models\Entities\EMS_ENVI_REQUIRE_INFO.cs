﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using H.Utility.SqlSugarInfra;
using SqlSugar;

namespace XH.H82.Models.Entities
{
    [DBOwner("XH_OA")]
    public class EMS_ENVI_REQUIRE_INFO
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string EQUIPMENT_ID { get; set; }
        public string HOSPITAL_ID { get; set; }
        public string EQUIPMENT_SIZE { get; set; }
        public string EQUIPMENT_WEIGHT { get; set; }
        public string BEARING_REQUIRE { get; set; }
        public string SPACE_REQUIRE { get; set; }
        public string AIR_REQUIRE { get; set; }
        public string WATER_REQUIRE { get; set; }
        public string TEMPERATURE_REQUIRE { get; set; }
        public string HUMIDITY_REQUIRE { get; set; }
        public string AIR_PRESSURE_REQUIRE { get; set; }
        public string POWER_REQUIRE { get; set; }
        public string VOLTAGE_REQUIRE { get; set; }
        public string ELECTRICITY_REQUIRE { get; set; }
        public string OTHER_REQUIRE { get; set; }
        public string REQUIRE_STATE { get; set; }
        public string FIRST_RPERSON { get; set; }
        public DateTime? FIRST_RTIME { get; set; }
        public string LAST_MPERSON { get; set; }
        public DateTime? LAST_MTIME { get; set; }
        public string REMARK { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string LENGTH { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string WIDTH { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string HEIGHT { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string TEMP_MIN { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string TEMP_MAX { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string HUMI_MIN { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string HUMI_MAX { get; set; }

    }
}
