[{"ContainingType": "XH.H82.API.Controllers.AuthorizationRecord.AuthorizationRecordController", "Method": "AddAuthorizationDict", "RelativePath": "api/AuthorizationRecord/AddAuthorizationDict", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "XH.H82.Models.Dtos.AuthorizationRecord.AuthorizationRecordDictInput", "IsRequired": true}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[XH.H82.Models.Dtos.AuthorizationRecord.AuthorizationRecordDictDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.AuthorizationRecord.AuthorizationRecordController", "Method": "AddAuthorizeRecord", "RelativePath": "api/AuthorizationRecord/AddAuthorizeRecord/{equipmentId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}, {"Name": "input", "Type": "XH.H82.Models.Dtos.AuthorizationRecord.AuthorizationRecordDto", "IsRequired": true}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[XH.H82.Models.Entities.EMS_AUTHORIZE_INFO, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.AuthorizationRecord.AuthorizationRecordController", "Method": "DeleteContentDict", "RelativePath": "api/AuthorizationRecord/DeleteContentDict/{Id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.AuthorizationRecord.AuthorizationRecordController", "Method": "GetAuthorizeRecordDicts", "RelativePath": "api/AuthorizationRecord/GetAuthorizeRecordDicts", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "content", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Models.Dtos.AuthorizationRecord.AuthorizationRecordDictDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.AuthorizationRecord.AuthorizationRecordController", "Method": "GetTableUsers", "RelativePath": "api/AuthorizationRecord/GetTableUsers", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "names", "Type": "System.String", "IsRequired": false}, {"Name": "ids", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Services.AuthorizationRecord.TableUserInfoDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.AuthorizationRecord.AuthorizationRecordController", "Method": "UpdateAuthorizationDict", "RelativePath": "api/AuthorizationRecord/UpdateAuthorizationDict/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "input", "Type": "XH.H82.Models.Dtos.AuthorizationRecord.AuthorizationRecordDictInput", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.AuthorizationRecord.AuthorizationRecordController", "Method": "UpdateAuthorizeRecord", "RelativePath": "api/AuthorizationRecord/UpdateAuthorizeRecord/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "input", "Type": "XH.H82.Models.Dtos.AuthorizationRecord.AuthorizationRecordDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.BaseController", "Method": "BatchEditEnclosureInfo", "RelativePath": "api/Base/BatchEditEnclosureInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "System.Collections.Generic.List`1[[XH.H82.Models.Entities.EMS_DOC_INFO, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.BaseController", "Method": "BatchInsertEnclosureInfo", "RelativePath": "api/Base/BatchInsertEnclosureInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "System.Collections.Generic.List`1[[XH.H82.Models.Entities.EMS_DOC_INFO, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.BaseController", "Method": "DeleteEnclosure", "RelativePath": "api/Base/DeleteEnclosure", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "doc_id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.BaseController", "Method": "EditEnclosureInfo", "RelativePath": "api/Base/EditEnclosureInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "doc_id", "Type": "System.String", "IsRequired": true}, {"Name": "doc_name", "Type": "System.String", "IsRequired": false}, {"Name": "remark", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.BaseController", "Method": "GetAdverseEventInfo", "RelativePath": "api/Base/GetAdverseEventInfo", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipment_id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.BaseController", "Method": "GetAreaPullList", "RelativePath": "api/Base/GetAreaPullList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "labId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.BaseController", "Method": "GetCompanyClassList", "RelativePath": "api/Base/GetCompanyClassList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.BaseController", "Method": "GetEnclosureInfo", "RelativePath": "api/Base/GetEnclosureInfo", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "DOC_CLASS", "Type": "System.String", "IsRequired": true}, {"Name": "DOC_INFO_ID", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.BaseController", "Method": "GetFuncDictInfo", "RelativePath": "api/Base/GetFuncDictInfo", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "menuId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.BaseController", "Method": "GetGlobalLabId", "RelativePath": "api/Base/GetGlobalLabId", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "labId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.BaseController", "Method": "GetMenuInfo", "RelativePath": "api/Base/GetMenuInfo", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.BaseController", "Method": "GetMgroupList", "RelativePath": "api/Base/GetMgroupList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "areaId", "Type": "System.String", "IsRequired": false}, {"Name": "pGroupId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.BaseController", "Method": "GetStagingAreaFiles", "RelativePath": "api/Base/GetStagingAreaFiles", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "docClass", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Models.Entities.EMS_DOC_INFO, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.BaseController", "Method": "GetUserLabList", "RelativePath": "api/Base/GetUserLabList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.BaseController", "Method": "UnUserStagingAreaFile", "RelativePath": "api/Base/UnUserStagingAreaFile/{docId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "docId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.BaseController", "Method": "UploadEnclosure", "RelativePath": "api/Base/UploadEnclosure", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "DOC_CLASS", "Type": "System.String", "IsRequired": false}, {"Name": "DOC_INFO_ID", "Type": "System.String", "IsRequired": false}, {"Name": "DOC_NAME", "Type": "System.String", "IsRequired": false}, {"Name": "DOC_SUFFIX", "Type": "System.String", "IsRequired": false}, {"Name": "FILE", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.BaseController", "Method": "UploadStagingAreaFile", "RelativePath": "api/Base/UploadStagingAreaFile", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "DOC_CLASS", "Type": "System.String", "IsRequired": false}, {"Name": "DOC_NAME", "Type": "System.String", "IsRequired": false}, {"Name": "DOC_SUFFIX", "Type": "System.String", "IsRequired": false}, {"Name": "FILE", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.BaseController", "Method": "UseStagingAreaFile", "RelativePath": "api/Base/UseStagingAreaFile", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "XH.H82.Models.Dtos.Base.AreaFileDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers._Demos.BaseDataDemoController", "Method": "AccountInfo", "RelativePath": "api/BaseDataDemo/AccountInfo", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers._Demos.BaseDataDemoController", "Method": "ApiExportTest", "RelativePath": "api/BaseDataDemo/ApiExportTest", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers._Demos.BaseDataDemoController", "Method": "ApiExportTest2", "RelativePath": "api/BaseDataDemo/ApiExportTest2", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers._Demos.BaseDataDemoController", "Method": "GetBaseData", "RelativePath": "api/BaseDataDemo/GetBaseData", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers._Demos.BaseDataDemoController", "Method": "GetLis5SetupDicts", "RelativePath": "api/BaseDataDemo/GetLis5SetupDicts", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers._Demos.BaseDataDemoController", "Method": "GetMenuButtonApi", "RelativePath": "api/BaseDataDemo/GetMenuButtonApi", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers._Demos.BaseDataDemoController", "Method": "GetOrganizeData", "RelativePath": "api/BaseDataDemo/GetOrganizeData", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers._Demos.BaseDataDemoController", "Method": "GetTableMax", "RelativePath": "api/BaseDataDemo/GetTableMax", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers._Demos.CacheTestController", "Method": "CacheAOPTest", "RelativePath": "api/CacheTest/CacheAOPTest", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers._Demos.CacheTestController", "Method": "GetValueFromRedis", "RelativePath": "api/CacheTest/GetValueFromRedis", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "key", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers._Demos.CacheTestController", "Method": "SeriSerializerBenchMark", "RelativePath": "api/CacheTest/SeriSerializerBenchMark", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers._Demos.CacheTestController", "Method": "WriteToRedis", "RelativePath": "api/CacheTest/WriteToRedis", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "key", "Type": "System.String", "IsRequired": false}, {"Name": "value", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.Certificate.CertificateController", "Method": "AddCertificateAttachment", "RelativePath": "api/Certificate/AddCertificateAttachment/{cretificateId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "cretificateId", "Type": "System.String", "IsRequired": true}, {"Name": "DOC_CLASS", "Type": "System.String", "IsRequired": false}, {"Name": "DOC_INFO_ID", "Type": "System.String", "IsRequired": false}, {"Name": "DOC_NAME", "Type": "System.String", "IsRequired": false}, {"Name": "DOC_SUFFIX", "Type": "System.String", "IsRequired": false}, {"Name": "FILE", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[XH.H82.Models.Dtos.Certificate.EquipmentCertificateDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.Certificate.CertificateController", "Method": "CreateCertificateType", "RelativePath": "api/Certificate/CreateCertificateType", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "certificateTypeName", "Type": "System.String", "IsRequired": false}, {"Name": "remark", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[XH.H82.Models.Dtos.Certificate.CertificateTypeDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.Certificate.CertificateController", "Method": "CreateEquipmentCertificate", "RelativePath": "api/Certificate/CreateEquipmentCertificate/{equipmentId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}, {"Name": "input", "Type": "XH.H82.Models.Dtos.Certificate.EquipmentCertificateInput", "IsRequired": true}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[XH.H82.Models.Dtos.Certificate.EquipmentCertificateDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.Certificate.CertificateController", "Method": "DeleteCertificateType", "RelativePath": "api/Certificate/DeleteCertificateType/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.Certificate.CertificateController", "Method": "DeleteEquipmentCertificate", "RelativePath": "api/Certificate/DeleteEquipmentCertificate/{cretificateId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "cretificateId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.Certificate.CertificateController", "Method": "DeleteEquipmentCertificateAttachment", "RelativePath": "api/Certificate/DeleteEquipmentCertificateAttachment/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.Certificate.CertificateController", "Method": "DisableOrEnableCertificateType", "RelativePath": "api/Certificate/DisableOrEnableCertificateType/{id}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.Certificate.CertificateController", "Method": "EditCertificateType", "RelativePath": "api/Certificate/EditCertificateType/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "certificateTypeName", "Type": "System.String", "IsRequired": false}, {"Name": "remark", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[XH.H82.Models.Dtos.Certificate.CertificateTypeDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.Certificate.CertificateController", "Method": "EditEquipmentCertificate", "RelativePath": "api/Certificate/EditEquipmentCertificate/{cretificateId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "cretificateId", "Type": "System.String", "IsRequired": true}, {"Name": "input", "Type": "XH.H82.Models.Dtos.Certificate.EquipmentCertificateInput", "IsRequired": true}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[XH.H82.Models.Dtos.Certificate.EquipmentCertificateDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.Certificate.CertificateController", "Method": "GetCertificateTypes", "RelativePath": "api/Certificate/GetCertificateTypes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "typeName", "Type": "System.String", "IsRequired": false}, {"Name": "state", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Models.Dtos.Certificate.CertificateTypeDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.Certificate.CertificateController", "Method": "GetEquipmentCertificates", "RelativePath": "api/Certificate/GetEquipmentCertificates/{equipmentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Models.Dtos.Certificate.EquipmentCertificateDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.Certificate.CertificateController", "Method": "GetEquipmentCompanyCertificates", "RelativePath": "api/Certificate/GetEquipmentCompanyCertificates/{equipmentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Models.Dtos.Certificate.CompanyCertificatDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.CurrencyController", "Method": "AddPersonTypeInfo", "RelativePath": "api/Currency/AddPersonTypeInfo", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.CurrencyController", "Method": "AddPostInfo", "RelativePath": "api/Currency/AddPostInfo", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.CurrencyController", "Method": "DeletePersonTypeInfo", "RelativePath": "api/Currency/DeletePersonTypeInfo", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "BasciId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.CurrencyController", "Method": "DeletePostInfo", "RelativePath": "api/Currency/DeletePostInfo", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "BasciId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.CurrencyController", "Method": "GetLabList", "RelativePath": "api/<PERSON><PERSON><PERSON><PERSON>/GetLabList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "areaId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.CurrencyController", "Method": "GetPersonList", "RelativePath": "api/<PERSON><PERSON><PERSON><PERSON>/GetPersonList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "areaId", "Type": "System.String", "IsRequired": true}, {"Name": "mgroupId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.CurrencyController", "Method": "GetPersonTypeList", "RelativePath": "api/Currency/GetPersonTypeList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.CurrencyController", "Method": "GetPgroupPullList", "RelativePath": "api/C<PERSON>ren<PERSON>/GetPgroupPullList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "areaId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.CurrencyController", "Method": "GetPostList", "RelativePath": "api/<PERSON><PERSON><PERSON><PERSON>/GetPostList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.CurrencyController", "Method": "GetProfessionalClass", "RelativePath": "api/Currency/GetProfessionalClass", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.CurrencyController", "Method": "GetScrapStopPersonList", "RelativePath": "api/Currency/GetScrapStopPersonList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "areaId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers._Demos.DemoController", "Method": "AutoMapperTest", "RelativePath": "api/Demo/AutoMapperTest", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers._Demos.DemoController", "Method": "BulkDeleteDemo", "RelativePath": "api/Demo/BulkDeleteDemo", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers._Demos.DemoController", "Method": "BulkInsertDemo", "RelativePath": "api/Demo/BulkInsertDemo", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers._Demos.DemoController", "Method": "DeleteByPredicate", "RelativePath": "api/Demo/DeleteByPredicate", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers._Demos.DemoController", "Method": "EFDemoFromDb2", "RelativePath": "api/Demo/EFDemoFromDb2", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers._Demos.DemoController", "Method": "EFDemoFromDefaultDb", "RelativePath": "api/Demo/EFDemoFromDefaultDb", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers._Demos.DemoController", "Method": "ModelValidateTest", "RelativePath": "api/Demo/ModelValidateTest", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "XH.H82.Models.Dtos.StartTemplateDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers._Demos.DemoController", "Method": "QueryBySql", "RelativePath": "api/Demo/QueryBySql", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers._Demos.DemoController", "Method": "UpdateByPredicate", "RelativePath": "api/Demo/UpdateByPredicate", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers._Demos.DemoGetConfigController", "Method": "GetModuleAllConfig", "RelativePath": "api/DemoGetConfig/GetModuleAllConfig", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers._Demos.DemoGetConfigController", "Method": "GetSingleConfig", "RelativePath": "api/DemoGetConfig/GetSingleConfig", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "AddAuthorizeInfo", "RelativePath": "api/EquipmentDoc/AddAuthorizeInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "XH.H82.Models.Entities.EMS_AUTHORIZE_INFO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "AddContactInfo", "RelativePath": "api/EquipmentDoc/AddContactInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "XH.H82.Models.Entities.SYS6_COMPANY_CONTACT", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "AddDebugInfo", "RelativePath": "api/EquipmentDoc/AddDebugInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "XH.H82.Models.Entities.EMS_DEBUG_INFO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "AddEnviRequireInfo", "RelativePath": "api/EquipmentDoc/AddEnviRequireInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "XH.H82.Models.Entities.EMS_ENVI_REQUIRE_INFO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "AddEquipmentInfo", "RelativePath": "api/EquipmentDoc/AddEquipmentInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "XH.H82.Models.Entities.EMS_EQUIPMENT_INFO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "AddInstallInfo", "RelativePath": "api/EquipmentDoc/AddInstallInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "XH.H82.Models.Entities.EMS_INSTALL_INFO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "AddPartsInfo", "RelativePath": "api/EquipmentDoc/AddPartsInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "XH.H82.Models.Entities.EMS_PARTS_INFO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "AddPurchaseInfo", "RelativePath": "api/EquipmentDoc/AddPurchaseInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "XH.H82.Models.Entities.EMS_PURCHASE_INFO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "AddStartInfo", "RelativePath": "api/EquipmentDoc/AddStartInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "XH.H82.Models.Entities.EMS_START_STOP", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "AddStartupInfo", "RelativePath": "api/EquipmentDoc/AddStartupInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "XH.H82.Models.Entities.EMS_STARTUP_INFO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "AddTrainInfo", "RelativePath": "api/EquipmentDoc/AddTrainInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "XH.H82.Models.Entities.EMS_TRAIN_INFO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "AddUnpackInfo", "RelativePath": "api/EquipmentDoc/AddUnpackInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "XH.H82.Models.Entities.EMS_UNPACK_INFO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "CardPrint", "RelativePath": "api/EquipmentDoc/CardPrint", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "cardType", "Type": "System.String", "IsRequired": true}, {"Name": "equipmentId", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "CreatCardTemplate", "RelativePath": "api/EquipmentDoc/CreatCardTemplate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "cardType", "Type": "System.String", "IsRequired": true}, {"Name": "equipmentId", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "DeleteAuthorizeInfo", "RelativePath": "api/EquipmentDoc/DeleteAuthorizeInfo", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "authorizeId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "DeleteContactInfo", "RelativePath": "api/EquipmentDoc/DeleteContactInfo", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "contactId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "DeleteEquipmentInfo", "RelativePath": "api/EquipmentDoc/DeleteEquipmentInfo", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "DeletePartsInfo", "RelativePath": "api/EquipmentDoc/DeletePartsInfo", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "partsId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "DeleteTrainInfo", "RelativePath": "api/EquipmentDoc/DeleteTrainInfo", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "trainId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "DistributionEquipmentsToSmblLab", "RelativePath": "api/EquipmentDoc/DistributionEquipmentsToSmblLab/{smblLabId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "smblLabId", "Type": "System.String", "IsRequired": true}, {"Name": "equipmentIds", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "DownloadEquipmentCard", "RelativePath": "api/EquipmentDoc/DownloadEquipmentCard", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "imageUrls", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "ExportDeviceList", "RelativePath": "api/EquipmentDoc/ExportDeviceList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "System.Collections.Generic.List`1[[XH.H82.Models.Entities.EMS_EQUIPMENT_INFO, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}, {"Name": "exportType", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "ForbidContactPerson", "RelativePath": "api/EquipmentDoc/ForbidContactPerson", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "XH.H82.Models.Entities.SYS6_COMPANY_CONTACT", "IsRequired": true}, {"Name": "equipmentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "GetAuthorizeList", "RelativePath": "api/EquipmentDoc/GetAuthorizeList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "GetCardTemplate", "RelativePath": "api/EquipmentDoc/GetCardTemplate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "cardType", "Type": "System.String", "IsRequired": true}, {"Name": "equipmentId", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "GetCompanyClassList", "RelativePath": "api/EquipmentDoc/GetCompanyClassList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "GetCompanyContactList", "RelativePath": "api/EquipmentDoc/GetCompanyContactList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "companyId", "Type": "System.String", "IsRequired": true}, {"Name": "equipmentId", "Type": "System.String", "IsRequired": true}, {"Name": "contactType", "Type": "System.String", "IsRequired": true}, {"Name": "contactState", "Type": "System.String", "IsRequired": false}, {"Name": "keyword", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "GetCompanyList", "RelativePath": "api/EquipmentDoc/GetCompanyList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "type", "Type": "System.String", "IsRequired": false}, {"Name": "serviceArea", "Type": "System.String", "IsRequired": false}, {"Name": "keyword", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "GetContactInfoList", "RelativePath": "api/EquipmentDoc/GetContactInfoList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}, {"Name": "state", "Type": "System.String", "IsRequired": false}, {"Name": "supplier", "Type": "System.String", "IsRequired": false}, {"Name": "key<PERSON>ord", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "GetDebugInfo", "RelativePath": "api/EquipmentDoc/GetDebugInfo", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "GetEnviRequireInfo", "RelativePath": "api/EquipmentDoc/GetEnviRequireInfo", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "GetEquipmentInfo", "RelativePath": "api/EquipmentDoc/GetEquipmentInfo", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "GetEquipmentList", "RelativePath": "api/EquipmentDoc/GetEquipmentList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "areaId", "Type": "System.String", "IsRequired": false}, {"Name": "state", "Type": "System.String", "IsRequired": false}, {"Name": "type", "Type": "System.String", "IsRequired": false}, {"Name": "mgroupId", "Type": "System.String", "IsRequired": false}, {"Name": "key<PERSON>ord", "Type": "System.String", "IsRequired": false}, {"Name": "pgroupId", "Type": "System.String", "IsRequired": false}, {"Name": "is<PERSON><PERSON>d", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "GetEquipmentTemplate", "RelativePath": "api/EquipmentDoc/GetEquipmentTemplate", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "GetInstallInfo", "RelativePath": "api/EquipmentDoc/GetInstallInfo", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "GetPartsList", "RelativePath": "api/EquipmentDoc/GetPartsList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "GetPipelineList", "RelativePath": "api/EquipmentDoc/GetPipelineList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "pgroupId", "Type": "System.String", "IsRequired": false}, {"Name": "areaId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "GetPurchaseInfo", "RelativePath": "api/EquipmentDoc/GetPurchaseInfo", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "GetScrapInfo", "RelativePath": "api/EquipmentDoc/GetScrapInfo", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "GetStartStopList", "RelativePath": "api/EquipmentDoc/GetStartStopList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "GetStartupInfo", "RelativePath": "api/EquipmentDoc/GetStartupInfo", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "GetSubscribeInfo", "RelativePath": "api/EquipmentDoc/GetSubscribeInfo", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "GetTrainList", "RelativePath": "api/EquipmentDoc/GetTrainList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "GetUnpackInfo", "RelativePath": "api/EquipmentDoc/GetUnpackInfo", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "ParseExcel", "RelativePath": "api/EquipmentDoc/ParseExcel", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "PdfToImage", "RelativePath": "api/EquipmentDoc/PdfToImage", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "pdfBase64", "Type": "System.Object", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "SaveContactInfo", "RelativePath": "api/EquipmentDoc/SaveContactInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "XH.H82.Models.Entities.SYS6_COMPANY_CONTACT", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "SaveSubscribeInfo", "RelativePath": "api/EquipmentDoc/SaveSubscribeInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "XH.H82.Models.Entities.EMS_SUBSCRIBE_INFO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "UnDistributionEquipmentsToSmblLab", "RelativePath": "api/EquipmentDoc/UnDistributionEquipmentsToSmblLab", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentIds", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "UpdateAuthorizeInfo", "RelativePath": "api/EquipmentDoc/UpdateAuthorizeInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "XH.H82.Models.Entities.EMS_AUTHORIZE_INFO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "UpdateCompanyContact", "RelativePath": "api/EquipmentDoc/UpdateCompanyContact", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "System.Collections.Generic.List`1[[XH.H82.Models.Entities.SYS6_COMPANY_CONTACT, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}, {"Name": "equipmentId", "Type": "System.String", "IsRequired": true}, {"Name": "contactType", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "UpdateDebugInfo", "RelativePath": "api/EquipmentDoc/UpdateDebugInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "XH.H82.Models.Entities.EMS_DEBUG_INFO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "UpdateEnviRequireInfo", "RelativePath": "api/EquipmentDoc/UpdateEnviRequireInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "XH.H82.Models.Entities.EMS_ENVI_REQUIRE_INFO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "UpdateEquipmentInfo", "RelativePath": "api/EquipmentDoc/UpdateEquipmentInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "XH.H82.Models.Entities.EMS_EQUIPMENT_INFO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "UpdateInstallInfo", "RelativePath": "api/EquipmentDoc/UpdateInstallInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "XH.H82.Models.Entities.EMS_INSTALL_INFO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "UpdatePartsInfo", "RelativePath": "api/EquipmentDoc/UpdatePartsInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "XH.H82.Models.Entities.EMS_PARTS_INFO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "UpdatePurchaseInfo", "RelativePath": "api/EquipmentDoc/UpdatePurchaseInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "XH.H82.Models.Entities.EMS_PURCHASE_INFO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "UpdateScrapInfo", "RelativePath": "api/EquipmentDoc/UpdateScrapInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "XH.H82.Models.Entities.EMS_SCRAP_INFO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "UpdateStartInfo", "RelativePath": "api/EquipmentDoc/UpdateStartInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "XH.H82.Models.Entities.EMS_START_STOP", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "UpdateStartupInfo", "RelativePath": "api/EquipmentDoc/UpdateStartupInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "XH.H82.Models.Entities.EMS_STARTUP_INFO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "UpdateTrainInfo", "RelativePath": "api/EquipmentDoc/UpdateTrainInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "XH.H82.Models.Entities.EMS_TRAIN_INFO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentDocController", "Method": "UpdateUnpackInfo", "RelativePath": "api/EquipmentDoc/UpdateUnpackInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "XH.H82.Models.Entities.EMS_UNPACK_INFO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentWarning.EquipmentWarningController", "Method": "AddInkScreen", "RelativePath": "api/EquipmentWarning/AddInkScreen", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "XH.H82.Models.DeviceRelevantInformation.Dto.AddInkScreenInput", "IsRequired": true}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.EquipmentWarning.EquipmentWarningController", "Method": "BindEquipment", "RelativePath": "api/EquipmentWarning/BindEquipment/{inkScreenId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "inkScreenId", "Type": "System.String", "IsRequired": true}, {"Name": "input", "Type": "XH.H82.Models.DeviceRelevantInformation.Dto.BindInkScreen", "IsRequired": true}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.EquipmentWarning.EquipmentWarningController", "Method": "DeleteInkScreen", "RelativePath": "api/EquipmentWarning/DeleteInkScreen/{inkScreenId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "inkScreenId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.EquipmentWarning.EquipmentWarningController", "Method": "GetEquipmentWarnInfo", "RelativePath": "api/EquipmentWarning/GetEquipmentWarnInfo", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "areaId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[XH.H82.Models.DeviceRelevantInformation.Dto.EquipmentWarnInfoDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.EquipmentWarning.EquipmentWarningController", "Method": "GetGroups", "RelativePath": "api/EquipmentWarning/GetGroups/{labId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "labId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[XH.H82.Models.DeviceRelevantInformation.Dto.GroupDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.EquipmentWarning.EquipmentWarningController", "Method": "GetInkScreenBindEquipments", "RelativePath": "api/EquipmentWarning/GetInkScreenBindEquipments", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "hosptialId", "Type": "System.String", "IsRequired": false}, {"Name": "labId", "Type": "System.String", "IsRequired": false}, {"Name": "pGourpId", "Type": "System.String", "IsRequired": false}, {"Name": "codeOrModelOrnName", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Models.DeviceRelevantInformation.Dto.InkScreenBindEquipmentDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.EquipmentWarning.EquipmentWarningController", "Method": "GetInkScreenBinds", "RelativePath": "api/EquipmentWarning/GetInkScreenBinds", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "hosptialId", "Type": "System.String", "IsRequired": false}, {"Name": "isBand", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "pGourpId", "Type": "System.String", "IsRequired": false}, {"Name": "mac", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Models.DeviceRelevantInformation.Dto.InkScreenBindDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.EquipmentWarning.EquipmentWarningController", "Method": "GetInkScreenDevices", "RelativePath": "api/EquipmentWarning/GetInkScreenDevices", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Models.Card.InkScreenDevice, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.EquipmentWarning.EquipmentWarningController", "Method": "GetInkScreens", "RelativePath": "api/EquipmentWarning/GetInkScreens", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "hosptialId", "Type": "System.String", "IsRequired": false}, {"Name": "lightState", "Type": "System.Nullable`1[[XH.H82.Models.DeviceRelevantInformation.Enum.LightStateEnum, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}, {"Name": "lineState", "Type": "System.Nullable`1[[XH.H82.Models.DeviceRelevantInformation.Enum.LineStateEnum, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}, {"Name": "equipmentState", "Type": "System.Nullable`1[[XH.H82.Models.DeviceRelevantInformation.Enum.EquipmentStateEnum, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}, {"Name": "pGourpId", "Type": "System.String", "IsRequired": false}, {"Name": "macOrEquipmentCode", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Models.DeviceRelevantInformation.Dto.InkScreenDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.EquipmentWarning.EquipmentWarningController", "Method": "GetWquipmentWarnRecords", "RelativePath": "api/EquipmentWarning/GetWquipmentWarnRecords", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": false}, {"Name": "warnMsg", "Type": "System.String", "IsRequired": false}, {"Name": "startTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Models.DeviceRelevantInformation.Dto.WarnRecordDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.EquipmentWarning.EquipmentWarningController", "Method": "InkScreenlighted", "RelativePath": "api/EquipmentWarning/InkScreenlighted/{inkScreenId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "inkScreenId", "Type": "System.String", "IsRequired": true}, {"Name": "lightStateEnum", "Type": "XH.H82.Models.DeviceRelevantInformation.Enum.LightStateEnum", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.EquipmentWarning.EquipmentWarningController", "Method": "PowerCheck", "RelativePath": "api/EquipmentWarning/PowerCheck", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.EquipmentWarning.EquipmentWarningController", "Method": "UpdateInkScreen", "RelativePath": "api/EquipmentWarning/UpdateInkScreen/{inkScreenId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "inkScreenId", "Type": "System.String", "IsRequired": true}, {"Name": "input", "Type": "XH.H82.Models.DeviceRelevantInformation.Dto.UpdateInkScreenInput", "IsRequired": true}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.FileManageController", "Method": "FindDocumentNodesAndDocuments", "RelativePath": "api/FileManage/FindDocumentNodesAndDocuments", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "labId", "Type": "System.String", "IsRequired": false}, {"Name": "classId", "Type": "System.String", "IsRequired": false}, {"Name": "equipmentId", "Type": "System.String", "IsRequired": false}, {"Name": "firstmenukey", "Type": "System.String", "IsRequired": false}, {"Name": "docClass", "Type": "System.String", "IsRequired": false}, {"Name": "docType", "Type": "System.String", "IsRequired": false}, {"Name": "pgroupId", "Type": "System.String", "IsRequired": false}, {"Name": "doc<PERSON>ame", "Type": "System.String", "IsRequired": false}, {"Name": "docClassId", "Type": "System.Nullable`1[[XH.H82.Models.Dtos.SysDocDtos.SOPType, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.FileManageController", "Method": "GetDocFile", "RelativePath": "api/FileManage/GetDocFile", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "docId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.FileManageController", "Method": "GetDocInfoList", "RelativePath": "api/FileManage/GetDocInfoList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "classId", "Type": "System.String", "IsRequired": true}, {"Name": "equipmentId", "Type": "System.String", "IsRequired": false}, {"Name": "pgroupId", "Type": "System.String", "IsRequired": false}, {"Name": "docType", "Type": "System.String", "IsRequired": false}, {"Name": "keyword", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.FileManageController", "Method": "GetFileTree", "RelativePath": "api/FileManage/GetFileTree", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "labId", "Type": "System.String", "IsRequired": false}, {"Name": "isSmbl", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.FileManageController", "Method": "GetPgroupList", "RelativePath": "api/FileManage/GetPgroupList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "labId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.FileManageController", "Method": "UploadDocFile", "RelativePath": "api/FileManage/UploadDocFile", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "filePath", "Type": "System.Collections.Generic.List`1[[XH.H82.Models.Dtos.DocFileDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}, {"Name": "equipmentId", "Type": "System.String", "IsRequired": true}, {"Name": "docInfoId", "Type": "System.String", "IsRequired": false}, {"Name": "docClass", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.FileTemplate.FileTemplateController", "Method": "ExportEquipmentPreview", "RelativePath": "api/FileTemplate/ExportEquipmentPreview", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}, {"Name": "styleId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.FileTemplate.FileTemplateController", "Method": "ExportEquipmentsExcelData", "RelativePath": "api/FileTemplate/ExportEquipmentsExcelData", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "styleId", "Type": "System.String", "IsRequired": false}, {"Name": "areaId", "Type": "System.String", "IsRequired": false}, {"Name": "hospitalId", "Type": "System.String", "IsRequired": false}, {"Name": "state", "Type": "System.String", "IsRequired": false}, {"Name": "type", "Type": "System.String", "IsRequired": false}, {"Name": "mgroupId", "Type": "System.String", "IsRequired": false}, {"Name": "key<PERSON>ord", "Type": "System.String", "IsRequired": false}, {"Name": "labId", "Type": "System.String", "IsRequired": false}, {"Name": "pgroupId", "Type": "System.String", "IsRequired": false}, {"Name": "is<PERSON><PERSON>d", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.FileTemplate.FileTemplateController", "Method": "ExportEquipmentsPreview", "RelativePath": "api/FileTemplate/ExportEquipmentsPreview", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "styleId", "Type": "System.String", "IsRequired": false}, {"Name": "areaId", "Type": "System.String", "IsRequired": false}, {"Name": "hospitalId", "Type": "System.String", "IsRequired": false}, {"Name": "state", "Type": "System.String", "IsRequired": false}, {"Name": "type", "Type": "System.String", "IsRequired": false}, {"Name": "mgroupId", "Type": "System.String", "IsRequired": false}, {"Name": "key<PERSON>ord", "Type": "System.String", "IsRequired": false}, {"Name": "labId", "Type": "System.String", "IsRequired": false}, {"Name": "pgroupId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.FileTemplate.FileTemplateController", "Method": "ExportEquipmentsWord", "RelativePath": "api/FileTemplate/ExportEquipmentsWord", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "styleId", "Type": "System.String", "IsRequired": false}, {"Name": "areaId", "Type": "System.String", "IsRequired": false}, {"Name": "hospitalId", "Type": "System.String", "IsRequired": false}, {"Name": "state", "Type": "System.String", "IsRequired": false}, {"Name": "type", "Type": "System.String", "IsRequired": false}, {"Name": "mgroupId", "Type": "System.String", "IsRequired": false}, {"Name": "key<PERSON>ord", "Type": "System.String", "IsRequired": false}, {"Name": "labId", "Type": "System.String", "IsRequired": false}, {"Name": "pgroupId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.FileTemplate.FileTemplateController", "Method": "ExportEquipmentsWorkPlanExcelData", "RelativePath": "api/FileTemplate/ExportEquipmentsWorkPlanExcelData", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "styleId", "Type": "System.String", "IsRequired": false}, {"Name": "labId", "Type": "System.String", "IsRequired": false}, {"Name": "keyword", "Type": "System.String", "IsRequired": false}, {"Name": "mgroupId", "Type": "System.String", "IsRequired": false}, {"Name": "equipmentClass", "Type": "System.String", "IsRequired": false}, {"Name": "pgroupId", "Type": "System.String", "IsRequired": false}, {"Name": "areaId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.FileTemplate.FileTemplateController", "Method": "ExportEquipmentsWorkPlanPreview", "RelativePath": "api/FileTemplate/ExportEquipmentsWorkPlanPreview", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "styleId", "Type": "System.String", "IsRequired": false}, {"Name": "labId", "Type": "System.String", "IsRequired": false}, {"Name": "keyword", "Type": "System.String", "IsRequired": false}, {"Name": "mgroupId", "Type": "System.String", "IsRequired": false}, {"Name": "equipmentClass", "Type": "System.String", "IsRequired": false}, {"Name": "pgroupId", "Type": "System.String", "IsRequired": false}, {"Name": "areaId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.FileTemplate.FileTemplateController", "Method": "ExportEquipmentsWorkPlanWord", "RelativePath": "api/FileTemplate/ExportEquipmentsWorkPlanWord", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "styleId", "Type": "System.String", "IsRequired": false}, {"Name": "labId", "Type": "System.String", "IsRequired": false}, {"Name": "keyword", "Type": "System.String", "IsRequired": false}, {"Name": "mgroupId", "Type": "System.String", "IsRequired": false}, {"Name": "equipmentClass", "Type": "System.String", "IsRequired": false}, {"Name": "pgroupId", "Type": "System.String", "IsRequired": false}, {"Name": "areaId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.FileTemplate.FileTemplateController", "Method": "ExportEquipmentWord", "RelativePath": "api/FileTemplate/ExportEquipmentWord", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}, {"Name": "styleId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.FileTemplate.FileTemplateController", "Method": "GetTemplateSettingsUrl", "RelativePath": "api/FileTemplate/GetTemplateSettingsUrl", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "H.Utility.CommonService.InfraController", "Method": "GetInfo", "RelativePath": "api/info", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "H.Utility.CommonService.InfraController", "Method": "CheckVersion", "RelativePath": "api/Infra/CheckVersion", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "H.Utility.CommonService.InfraController", "Method": "GetAboutUrl", "RelativePath": "api/Infra/GetAboutUrl", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "H.Utility.CommonService.InfraController", "Method": "GetCurrVersion", "RelativePath": "api/Infra/GetCurrVersion", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "H.Utility.CommonService.InfraController", "Method": "GetDocFile", "RelativePath": "api/Infra/GetDocFile", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "H.Utility.CommonService.InfraController", "Method": "GetHelpDocCatlog", "RelativePath": "api/Infra/GetHelpDocCatlog", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "H.Utility.CommonService.InfraController", "Method": "GetHospitalInfo", "RelativePath": "api/Infra/GetHospitalInfo", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "hospitalId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "H.Utility.CommonService.InfraController", "Method": "GetModuleUrl", "RelativePath": "api/Infra/GetModuleUrl", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "moduleId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "H.Utility.CommonService.InfraController", "Method": "GetScreenLockOption", "RelativePath": "api/Infra/GetScreenLockOption", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "hospitalId", "Type": "System.String", "IsRequired": false}, {"Name": "areaId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "H.Utility.CommonService.InfraController", "Method": "GetUnitLoginUrl", "RelativePath": "api/Infra/GetUnitLoginUrl", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "H.Utility.CommonService.InfraController", "Method": "GetWarterMarkOption", "RelativePath": "api/Infra/GetWarterMarkOption", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "H.Utility.CommonService.InfraController", "Method": "HealthCheck", "RelativePath": "api/Infra/HealthCheck", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "H.Utility.CommonService.InfraController", "Method": "ReNewToken", "RelativePath": "api/Infra/ReNewToken", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "expriedToken", "Type": "System.String", "IsRequired": false}, {"Name": "refreshToken", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "H.Utility.CommonService.InfraController", "Method": "SelfCheck", "RelativePath": "api/Infra/SelfCheck", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "H.Utility.CommonService.InfraController", "Method": "TokenKeep", "RelativePath": "api/Infra/Token<PERSON>", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "H.Utility.CommonService.InfraController", "Method": "UpdateLog", "RelativePath": "api/Infra/UpdateLog", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "H.Utility.CommonService.InfraController", "Method": "UserVerify", "RelativePath": "api/Infra/UserVerify", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "logId", "Type": "System.String", "IsRequired": false}, {"Name": "password", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentWarning.InkScreenController", "Method": "AddTemplate", "RelativePath": "api/InkScreen/AddTemplate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "XH.H82.Models.InkScreenTemplate.Dto.AddTemplateInput", "IsRequired": true}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.EquipmentWarning.InkScreenController", "Method": "ApplicationGroups", "RelativePath": "api/InkScreen/ApplicationGroups/{templateId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "templateId", "Type": "System.String", "IsRequired": true}, {"Name": "groups", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.EquipmentWarning.InkScreenController", "Method": "CopyTemplate", "RelativePath": "api/InkScreen/CopyTemplate/{templateId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "templateId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.EquipmentWarning.InkScreenController", "Method": "DeleteTemplate", "RelativePath": "api/InkScreen/DeleteTemplate/{templateId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "templateId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.EquipmentWarning.InkScreenController", "Method": "GetAllTemplateAttributes", "RelativePath": "api/InkScreen/GetAllTemplateAttributes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Models.InkScreenTemplate.TemplateAttribute, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.EquipmentWarning.InkScreenController", "Method": "GetGroups", "RelativePath": "api/InkScreen/GetGroups", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "labId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Models.DeviceRelevantInformation.Dto.GroupDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.EquipmentWarning.InkScreenController", "Method": "GetGroupsTree", "RelativePath": "api/InkScreen/GetGroupsTree", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "labId", "Type": "System.String", "IsRequired": false}, {"Name": "groupId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Models.Dtos.Tree.OrganizationalTreeNode, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.EquipmentWarning.InkScreenController", "Method": "GetTemplatePreview", "RelativePath": "api/InkScreen/GetTemplatePreview/{templateId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "templateId", "Type": "System.String", "IsRequired": true}, {"Name": "parameters", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentWarning.InkScreenController", "Method": "GetTemplates", "RelativePath": "api/InkScreen/GetTemplates", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "labId", "Type": "System.String", "IsRequired": false}, {"Name": "groupId", "Type": "System.String", "IsRequired": false}, {"Name": "templateName", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Models.InkScreenTemplate.Dto.TemplatesDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.EquipmentWarning.InkScreenController", "Method": "GetTemplatesTest1", "RelativePath": "api/InkScreen/GetTemplatesTest1", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "labId", "Type": "System.String", "IsRequired": false}, {"Name": "groupId", "Type": "System.String", "IsRequired": false}, {"Name": "templateName", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Models.InkScreenTemplate.Dto.TemplatesDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.EquipmentWarning.InkScreenController", "Method": "SaveTemplate", "RelativePath": "api/InkScreen/SaveTemplate/{templateId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "templateId", "Type": "System.String", "IsRequired": true}, {"Name": "input", "Type": "XH.H82.Models.InkScreenTemplate.Dto.SaveTemplateInput", "IsRequired": true}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.EquipmentWarning.InkScreenController", "Method": "SetInkScreen", "RelativePath": "api/InkScreen/SetInkScreen/{mac}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "mac", "Type": "System.String", "IsRequired": true}, {"Name": "values", "Type": "System.Collections.Generic.List`1[[XH.H82.Models.InkScreenTemplate.Dto.TemplateDataAndValue, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}, {"Name": "nextMaintainDateStatus", "Type": "System.Boolean", "IsRequired": false}, {"Name": "nextCorrectDateStatus", "Type": "System.Boolean", "IsRequired": false}, {"Name": "circumstance", "Type": "System.String", "IsRequired": false}, {"Name": "meassage", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.EquipmentWarning.InkScreenController", "Method": "UpdateTemplate", "RelativePath": "api/InkScreen/UpdateTemplate/{templateId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "templateId", "Type": "System.String", "IsRequired": true}, {"Name": "input", "Type": "XH.H82.Models.InkScreenTemplate.Dto.UpdateTemplateInput", "IsRequired": true}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.Inspection.InspectionController", "Method": "GetEquipmentList", "RelativePath": "api/Inspection/GetEquipmentList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "labId", "Type": "System.String", "IsRequired": false}, {"Name": "areaId", "Type": "System.String", "IsRequired": false}, {"Name": "mgroupId", "Type": "System.String", "IsRequired": false}, {"Name": "pgroupId", "Type": "System.String", "IsRequired": false}, {"Name": "state", "Type": "System.String", "IsRequired": false}, {"Name": "type", "Type": "System.String", "IsRequired": false}, {"Name": "key<PERSON>ord", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Models.Dtos.EquipmentInfoDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.Inspection.InspectionController", "Method": "GetEquipmentListByMgroup", "RelativePath": "api/Inspection/GetEquipmentListByMgroup", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "labId", "Type": "System.String", "IsRequired": false}, {"Name": "areaId", "Type": "System.String", "IsRequired": false}, {"Name": "mgroupid", "Type": "System.String", "IsRequired": false}, {"Name": "pgroupId", "Type": "System.String", "IsRequired": false}, {"Name": "equipmentNo", "Type": "System.String", "IsRequired": false}, {"Name": "smblFlag", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Models.Dtos.NewOATreeDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.Inspection.InspectionController", "Method": "GetHospitalProfessionalGroups", "RelativePath": "api/Inspection/GetHospitalProfessionalGroups", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "labId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Models.Dtos.NewOATreeDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.IoTDevice.IoTDeviceController", "Method": "ChangeThsEquipmentDid", "RelativePath": "api/IoTDevice/ChangeThsEquipmentDid", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "thsEquipmentId", "Type": "System.String", "IsRequired": false}, {"Name": "emsEquipmentId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.IoTDevice.IoTDeviceController", "Method": "ChangeThsEquipmentSn", "RelativePath": "api/IoTDevice/ChangeThsEquipmentSn", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": false}, {"Name": "sn", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.IoTDevice.IoTDeviceController", "Method": "ConutHosptailEquipmentDistribution", "RelativePath": "api/IoTDevice/ConutHosptailEquipmentDistribution", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "hospitalId", "Type": "System.String", "IsRequired": false}, {"Name": "labId", "Type": "System.String", "IsRequired": false}, {"Name": "smblLabId", "Type": "System.String", "IsRequired": false}, {"Name": "smblClass", "Type": "System.String", "IsRequired": false}, {"Name": "equipmentName", "Type": "System.String", "IsRequired": false}, {"Name": "isSamePeriod", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.API.Controllers.IoTDevice.ConutHosptailEquipmentDistributionDto, XH.H82.API, Version=**********, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.IoTDevice.IoTDeviceController", "Method": "ConutOverviewMaintained", "RelativePath": "api/IoTDevice/ConutOverviewMaintained", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "smblLabId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.API.Controllers.IoTDevice.Dtos.OverviewMaintainedDto, XH.H82.API, Version=**********, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.IoTDevice.IoTDeviceController", "Method": "ConutOverviewMaintainedDetal", "RelativePath": "api/IoTDevice/ConutOverviewMaintainedDetal", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "smblLabId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.API.Controllers.IoTDevice.Dtos.OverviewMaintainedDetailDto, XH.H82.API, Version=**********, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.IoTDevice.IoTDeviceController", "Method": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/IoTDevice/ConutReminders", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "smblLabId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Models.Dtos.ConutReminderDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.IoTDevice.IoTDeviceController", "Method": "CountTheTotalUseEquipmentCategories", "RelativePath": "api/IoTDevice/CountTheTotalUseEquipmentCategories", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "hosiptalId", "Type": "System.String", "IsRequired": false}, {"Name": "labId", "Type": "System.String", "IsRequired": false}, {"Name": "smblLabId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.API.Controllers.IoTDevice.CountTheTotalUseEquipmentDto, XH.H82.API, Version=**********, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.IoTDevice.IoTDeviceController", "Method": "EquipmentInit", "RelativePath": "api/IoTDevice/EquipmentInit", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.IoTDevice.IoTDeviceController", "Method": "EquipmentInit1111", "RelativePath": "api/IoTDevice/EquipmentInit1111", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.IoTDevice.IoTDeviceController", "Method": "GetAccessControls", "RelativePath": "api/IoTDevice/GetAccessControls", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.IoTDevice.IoTDeviceController", "Method": "GetCalendarRecords", "RelativePath": "api/IoTDevice/GetCalendarRecords/{time}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "time", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}, {"Name": "smblLabId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.API.Controllers.IoTDevice.CalendarRecordsDto, XH.H82.API, Version=**********, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.IoTDevice.IoTDeviceController", "Method": "GetChuanqganqi", "RelativePath": "api/IoTDevice/GetChuanqganqi", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.IoTDevice.IoTDeviceController", "Method": "GetDeviceMonitorUseRecoreds", "RelativePath": "api/IoTDevice/GetDeviceMonitorUseRecoreds/{equipmentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}, {"Name": "startDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "endDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "smblClass", "Type": "System.String", "IsRequired": false}, {"Name": "itemId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.API.Controllers.IoTDevice.IotDeviceUsingRecordDto, XH.H82.API, Version=**********, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.IoTDevice.IoTDeviceController", "Method": "GetDviceMonitorsStatisticsByLineChartAndMrak", "RelativePath": "api/IoTDevice/GetDviceMonitorsStatisticsByLineChartAndMrak/{equipmentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}, {"Name": "date", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "timeSpan", "Type": "System.Double", "IsRequired": false}, {"Name": "smblClass", "Type": "System.String", "IsRequired": false}, {"Name": "ItemId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.API.Controllers.IoTDevice.LineChartDto, XH.H82.API, Version=**********, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.IoTDevice.IoTDeviceController", "Method": "GetEquipmentsDistribution", "RelativePath": "api/IoTDevice/GetEquipmentsDistribution", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "inlet", "Type": "XH.H82.Models.Smbl.SmblInletEnum", "IsRequired": false}, {"Name": "id", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[XH.H82.API.Controllers.IoTDevice.EquipmentDistributionDto, XH.H82.API, Version=**********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.IoTDevice.IoTDeviceController", "Method": "GetEquipmentYearCheck", "RelativePath": "api/IoTDevice/GetEquipmentYearCheck", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "year", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "inlet", "Type": "XH.H82.Models.Smbl.SmblInletEnum", "IsRequired": false}, {"Name": "id", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Models.Dtos.ConutEquipmentYearChectByMonth, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.IoTDevice.IoTDeviceController", "Method": "GetEquipmentYearCheckCount", "RelativePath": "api/IoTDevice/GetEquipmentYearCheckCount", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "inlet", "Type": "XH.H82.Models.Smbl.SmblInletEnum", "IsRequired": false}, {"Name": "id", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Models.Dtos.EquipmentYearCheckCountDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.IoTDevice.IoTDeviceController", "Method": "GetIoTDevieces", "RelativePath": "api/IoTDevice/GetIoTDevieces", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.LoginController", "Method": "UserLogin", "RelativePath": "api/Login/UserLogin", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "obj", "Type": "System.Object", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.LoginController", "Method": "UserLogin", "RelativePath": "api/Login/UserLogin", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "obj", "Type": "System.Object", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OfficeExcelController", "Method": "AddVisLocation", "RelativePath": "api/OfficeExcel/AddVisLocation", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "XH.H82.API.Controllers.OfficeExcelController+VisLocationInput", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OfficeExcelController", "Method": "GetLabPGroupList", "RelativePath": "api/OfficeExcel/GetLabPGroupList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "LAB_ID", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OfficeExcelController", "Method": "GetLastVisLocation", "RelativePath": "api/OfficeExcel/GetLastVisLocation", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "labId", "Type": "System.String", "IsRequired": false}, {"Name": "styleClassCode", "Type": "System.String", "IsRequired": false}, {"Name": "flag", "Type": "System.String", "IsRequired": false}, {"Name": "loadMode", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OfficeExcelController", "Method": "GetTemplateList", "RelativePath": "api/OfficeExcel/GetTemplateList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "HOSPITAL_ID", "Type": "System.String", "IsRequired": false}, {"Name": "LAB_ID", "Type": "System.String", "IsRequired": false}, {"Name": "AREA_ID", "Type": "System.String", "IsRequired": false}, {"Name": "PGROUP_ID", "Type": "System.String", "IsRequired": false}, {"Name": "LAB_PGROUP_TYPE", "Type": "System.String", "IsRequired": false}, {"Name": "STYLE_CLASS_CODE", "Type": "System.String", "IsRequired": false}, {"Name": "DATA_ID", "Type": "System.String", "IsRequired": false}, {"Name": "STYLE_NAME", "Type": "System.String", "IsRequired": false}, {"Name": "CATALOG_NAME", "Type": "System.String", "IsRequired": false}, {"Name": "STYLE_IDS", "Type": "System.String", "IsRequired": false}, {"Name": "OFFICE_FLAG", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "AddChangeInfo", "RelativePath": "api/OperationRecord/AddChangeInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "XH.H82.Models.Entities.EMS_CHANGE_INFO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "AddChangeInfo", "RelativePath": "api/OperationRecord/AddChangeInfo/{equipmentId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}, {"Name": "input", "Type": "XH.H82.Models.Dtos.Change.ChangeInput", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "AddComparison", "RelativePath": "api/OperationRecord/AddComparison/{equipmentId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}, {"Name": "input", "Type": "XH.H82.Models.Dtos.Comparison.ComparisonInput", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "AddComparisonInfo", "RelativePath": "api/OperationRecord/AddComparisonInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "XH.H82.Models.Entities.EMS_COMPARISON_INFO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "AddCorrect", "RelativePath": "api/OperationRecord/AddCorrect/{equipmentId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}, {"Name": "input", "Type": "XH.H82.Models.Dtos.Correct.CorrectInput", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "AddCorrectInfo", "RelativePath": "api/OperationRecord/AddCorrectInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "XH.H82.Models.Entities.EMS_CORRECT_INFO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "Add<PERSON><PERSON><PERSON>", "RelativePath": "api/OperationRecord/AddMaintain/{equipmentId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}, {"Name": "input", "Type": "XH.H82.Models.Dtos.Maintain.MaintainInput", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "AddMaintainInfo", "RelativePath": "api/OperationRecord/AddMaintainInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "XH.H82.Models.Entities.EMS_MAINTAIN_INFO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "AddRepairInfo", "RelativePath": "api/OperationRecord/AddRepairInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "XH.H82.Models.Entities.EMS_REPAIR_INFO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "AddRepairInfo", "RelativePath": "api/OperationRecord/AddRepairInfo/{equipmentId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}, {"Name": "input", "Type": "XH.H82.Models.Dtos.Repair.RepairInput", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "AddVerification", "RelativePath": "api/OperationRecord/AddVerification/{equipmentId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}, {"Name": "input", "Type": "XH.H82.Models.Dtos.Verification.VerificationInput", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "AddVerificationInfo", "RelativePath": "api/OperationRecord/AddVerificationInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "XH.H82.Models.Entities.EMS_VERIFICATION_INFO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "CreateImplement", "RelativePath": "api/OperationRecord/CreateImplement/{equipmentId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}, {"Name": "input", "Type": "XH.H82.Models.Dtos.Implement.ImplementInput", "IsRequired": true}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[XH.H82.Models.Entities.Transaction.EMS_IMPLEMENT_INFO, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "DeleteChange", "RelativePath": "api/OperationRecord/DeleteChange/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "DeleteChangeInfo", "RelativePath": "api/OperationRecord/DeleteChangeInfo", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "changeId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "DeleteComparison", "RelativePath": "api/OperationRecord/DeleteComparison/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "DeleteComparisonInfo", "RelativePath": "api/OperationRecord/DeleteComparisonInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "comparisonId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "DeleteCorrect", "RelativePath": "api/OperationRecord/DeleteCorrect/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "DeleteCorrectInfo", "RelativePath": "api/OperationRecord/DeleteCorrectInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "correctId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "DeleteImplement", "RelativePath": "api/OperationRecord/DeleteImplement/{ImplementId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "ImplementId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "DeleteMaintain", "RelativePath": "api/OperationRecord/DeleteMaintain/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "DeleteMaintainInfo", "RelativePath": "api/OperationRecord/DeleteMaintainInfo", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "maintainId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "DeleteRepair", "RelativePath": "api/OperationRecord/DeleteRepair/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "DeleteRepairInfo", "RelativePath": "api/OperationRecord/DeleteRepairInfo", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "repairId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "DeleteVerification", "RelativePath": "api/OperationRecord/DeleteVerification/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "DeleteVerificationInfo", "RelativePath": "api/OperationRecord/DeleteVerificationInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "verificationInfoId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "EditImplement", "RelativePath": "api/OperationRecord/EditImplement/{ImplementId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "ImplementId", "Type": "System.String", "IsRequired": true}, {"Name": "input", "Type": "XH.H82.Models.Dtos.Implement.ImplementInput", "IsRequired": true}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "GetChangeList", "RelativePath": "api/OperationRecord/GetChangeList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "GetChanges", "RelativePath": "api/OperationRecord/GetChanges/{equipmentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "GetComparisonList", "RelativePath": "api/OperationRecord/GetComparisonList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "GetComparisons", "RelativePath": "api/OperationRecord/GetComparisons/{equipmentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "GetCorrectFile", "RelativePath": "api/OperationRecord/GetCorrectFile", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}, {"Name": "year", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "GetCorrectList", "RelativePath": "api/OperationRecord/GetCorrectList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "GetCorrects", "RelativePath": "api/OperationRecord/GetCorrects/{equipmentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}, {"Name": "startTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "GetDecontaminationFile", "RelativePath": "api/OperationRecord/GetDecontaminationFile", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}, {"Name": "year", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "GetDocInfos", "RelativePath": "api/OperationRecord/GetDocInfos/{docInfoId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "docInfoId", "Type": "System.String", "IsRequired": true}, {"Name": "docClass", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Models.Entities.EMS_DOC_INFO, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "GetImplements", "RelativePath": "api/OperationRecord/GetImplements", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}, {"Name": "startTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "context", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Models.Dtos.Implement.ImplementDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "GetMaintainFile", "RelativePath": "api/OperationRecord/GetMaintainFile", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}, {"Name": "year", "Type": "System.Int32", "IsRequired": true}, {"Name": "month", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "GetMaintainList", "RelativePath": "api/OperationRecord/GetMaintainList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "GetMaintains", "RelativePath": "api/OperationRecord/GetMaintains/{equipmentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}, {"Name": "maintainCycle", "Type": "System.String", "IsRequired": false}, {"Name": "content", "Type": "System.String", "IsRequired": false}, {"Name": "startTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "GetOperFiles", "RelativePath": "api/OperationRecord/GetOperFiles", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "module", "Type": "System.String", "IsRequired": true}, {"Name": "operId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "GetRelationEventInfo", "RelativePath": "api/OperationRecord/GetRelationEventInfo", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "relationNo", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "GetRepairFile", "RelativePath": "api/OperationRecord/GetRepairFile", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}, {"Name": "year", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "GetRepairList", "RelativePath": "api/OperationRecord/GetRepairList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "GetRepairs", "RelativePath": "api/OperationRecord/GetRepairs/{equipmentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}, {"Name": "content", "Type": "System.String", "IsRequired": false}, {"Name": "startTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "GetTransactionFillingRecords", "RelativePath": "api/OperationRecord/GetTransactionFillingRecords/{equipmentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}, {"Name": "transactionClass", "Type": "System.String", "IsRequired": false}, {"Name": "startTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Models.Dtos.Tim.ExecRecord, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "GetTransactions", "RelativePath": "api/OperationRecord/GetTransactions/{equipmentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Models.Dtos.Tim.TransactionForm, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "GetUsingFile", "RelativePath": "api/OperationRecord/GetUsingFile", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}, {"Name": "year", "Type": "System.Int32", "IsRequired": true}, {"Name": "month", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "GetVerificationList", "RelativePath": "api/OperationRecord/GetVerificationList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "GetVerifications", "RelativePath": "api/OperationRecord/GetVerifications/{equipmentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "GetWorkSequentialList", "RelativePath": "api/OperationRecord/GetWorkSequentialList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}, {"Name": "year", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "UpdateChange", "RelativePath": "api/OperationRecord/UpdateChange/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "input", "Type": "XH.H82.Models.Dtos.Change.ChangeInput", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "UpdateChangeInfo", "RelativePath": "api/OperationRecord/UpdateChangeInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "XH.H82.Models.Entities.EMS_CHANGE_INFO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "UpdateComparison", "RelativePath": "api/OperationRecord/UpdateComparison/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "input", "Type": "XH.H82.Models.Dtos.Comparison.ComparisonInput", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "UpdateComparisonInfo", "RelativePath": "api/OperationRecord/UpdateComparisonInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "XH.H82.Models.Entities.EMS_COMPARISON_INFO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "UpdateCorrect", "RelativePath": "api/OperationRecord/UpdateCorrect/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "input", "Type": "XH.H82.Models.Dtos.Correct.CorrectInput", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "UpdateCorrectInfo", "RelativePath": "api/OperationRecord/UpdateCorrectInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "XH.H82.Models.Entities.EMS_CORRECT_INFO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "UpdateMaintainInfo", "RelativePath": "api/OperationRecord/UpdateMaintainInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "XH.H82.Models.Entities.EMS_MAINTAIN_INFO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "UpdateMaintainInfo", "RelativePath": "api/OperationRecord/UpdateMaintainInfo/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "input", "Type": "XH.H82.Models.Dtos.Maintain.MaintainInput", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "UpdateRepair", "RelativePath": "api/OperationRecord/UpdateRepair/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "input", "Type": "XH.H82.Models.Dtos.Repair.RepairInput", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "UpdateRepairInfo", "RelativePath": "api/OperationRecord/UpdateRepairInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "XH.H82.Models.Entities.EMS_REPAIR_INFO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "UpdateVerification", "RelativePath": "api/OperationRecord/UpdateVerification/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "input", "Type": "XH.H82.Models.Dtos.Verification.VerificationInput", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.OperationRecordController", "Method": "UpdateVerificationInfo", "RelativePath": "api/OperationRecord/UpdateVerificationInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "XH.H82.Models.Entities.EMS_VERIFICATION_INFO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.Organization.OrganizationPullController", "Method": "GenericPullAreaPGroups", "RelativePath": "api/OrganizationPull/GenericPullAreaPGroups", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "labId", "Type": "System.String", "IsRequired": false}, {"Name": "areaId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Base.Tree.AllTreeNode, XH.H82.Base, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.Organization.OrganizationPullController", "Method": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/OrganizationPull/GenericPullPersons", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "labId", "Type": "System.String", "IsRequired": false}, {"Name": "areaId", "Type": "System.String", "IsRequired": false}, {"Name": "mgroupId", "Type": "System.String", "IsRequired": false}, {"Name": "pgroupId", "Type": "System.String", "IsRequired": false}, {"Name": "modelId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Base.Tree.AllTreeNode, XH.H82.Base, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.Organization.OrganizationPullController", "Method": "SmblEquipmentClassPull", "RelativePath": "api/OrganizationPull/SmblEquipmentClassPull", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Models.Smbl.Dto.SmblEquipmentClassDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.Organization.OrganizationPullController", "Method": "SmblPullAreaPGroups", "RelativePath": "api/OrganizationPull/SmblPullAreaPGroups", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Base.Tree.AllTreeNode, XH.H82.Base, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.Organization.OrganizationTreeController", "Method": "EquipmentsTree", "RelativePath": "api/OrganizationTree/EquipmentsTree/{labId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "labId", "Type": "System.String", "IsRequired": true}, {"Name": "areaId", "Type": "System.String", "IsRequired": false}, {"Name": "pgroupId", "Type": "System.String", "IsRequired": false}, {"Name": "onlySmbl", "Type": "System.Boolean", "IsRequired": false}, {"Name": "showHide", "Type": "System.Boolean", "IsRequired": false}, {"Name": "hasClass", "Type": "System.Boolean", "IsRequired": false}, {"Name": "equipmentCode", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Base.Tree.AllTreeNode, XH.H82.Base, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.Organization.OrganizationTreeController", "Method": "InstrumentEquipmentTree", "RelativePath": "api/OrganizationTree/InstrumentEquipmentTree/{labId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "labId", "Type": "System.String", "IsRequired": true}, {"Name": "areaId", "Type": "System.String", "IsRequired": false}, {"Name": "pgroupId", "Type": "System.String", "IsRequired": false}, {"Name": "onlySmbl", "Type": "System.Boolean", "IsRequired": false}, {"Name": "showHide", "Type": "System.Boolean", "IsRequired": false}, {"Name": "hasClass", "Type": "System.Boolean", "IsRequired": false}, {"Name": "equipmentCode", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Base.Tree.AllTreeNode, XH.H82.Base, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.Organization.OrganizationTreeController", "Method": "ScrapsOrStopTree", "RelativePath": "api/OrganizationTree/ScrapsOrStopTree/{labId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "labId", "Type": "System.String", "IsRequired": true}, {"Name": "areaId", "Type": "System.String", "IsRequired": false}, {"Name": "pgroupId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Base.Tree.AllTreeNode, XH.H82.Base, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.Organization.OrganizationTreeController", "Method": "SubscriptionTree", "RelativePath": "api/OrganizationTree/SubscriptionTree/{labId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "labId", "Type": "System.String", "IsRequired": true}, {"Name": "areaId", "Type": "System.String", "IsRequired": false}, {"Name": "pgroupId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Base.Tree.AllTreeNode, XH.H82.Base, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.Organization.OrganizationTreeController", "Method": "WorkPlanTree", "RelativePath": "api/OrganizationTree/WorkPlanTree/{labId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "labId", "Type": "System.String", "IsRequired": true}, {"Name": "areaId", "Type": "System.String", "IsRequired": false}, {"Name": "pgroupId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Base.Tree.AllTreeNode, XH.H82.Base, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.Organization.OrgnizationTreeForSmblController", "Method": "EquipmentClassTree", "RelativePath": "api/OrgnizationTreeForSmbl/EquipmentClassTree/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "inlet", "Type": "XH.H82.Models.Smbl.SmblInletEnum", "IsRequired": false}, {"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "onlySmbl", "Type": "System.Boolean", "IsRequired": false}, {"Name": "showHide", "Type": "System.Boolean", "IsRequired": false}, {"Name": "equipmentCode", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Base.Tree.AllTreeNode, XH.H82.Base, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.Organization.OrgnizationTreeForSmblController", "Method": "EquipmentTree", "RelativePath": "api/OrgnizationTreeForSmbl/EquipmentTree/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "inlet", "Type": "XH.H82.Models.Smbl.SmblInletEnum", "IsRequired": false}, {"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "smblLabId", "Type": "System.String", "IsRequired": false}, {"Name": "onlySmbl", "Type": "System.Boolean", "IsRequired": false}, {"Name": "showHide", "Type": "System.Boolean", "IsRequired": false}, {"Name": "hasClass", "Type": "System.Boolean", "IsRequired": false}, {"Name": "equipmentCode", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Base.Tree.AllTreeNode, XH.H82.Base, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.Organization.OrgnizationTreeForSmblController", "Method": "GetDistributionEquipments", "RelativePath": "api/OrgnizationTreeForSmbl/GetDistributionEquipments/{smblLabId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "smblLabId", "Type": "System.String", "IsRequired": true}, {"Name": "smblEquipmentClass", "Type": "System.String", "IsRequired": false}, {"Name": "smblState", "Type": "System.String", "IsRequired": false}, {"Name": "nameOrCode", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Models.Smbl.Dto.SmblEquipmentDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.Organization.OrgnizationTreeForSmblController", "Method": "GetDistributionEquipmentTree", "RelativePath": "api/OrgnizationTreeForSmbl/GetDistributionEquipmentTree/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "inlet", "Type": "XH.H82.Models.Smbl.SmblInletEnum", "IsRequired": false}, {"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Base.Tree.AllTreeNode, XH.H82.Base, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.Organization.OrgnizationTreeForSmblController", "Method": "GetPerDistributionEquipments", "RelativePath": "api/OrgnizationTreeForSmbl/GetPerDistributionEquipments", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "smblLabId", "Type": "System.String", "IsRequired": false}, {"Name": "smblEquipmentClass", "Type": "System.String", "IsRequired": false}, {"Name": "smblState", "Type": "System.String", "IsRequired": false}, {"Name": "nameOrCode", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Models.Smbl.Dto.SmblEquipmentDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.Organization.OrgnizationTreeForSmblController", "Method": "InstrumentEquipmentTree", "RelativePath": "api/OrgnizationTreeForSmbl/InstrumentEquipmentTree/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "inlet", "Type": "XH.H82.Models.Smbl.SmblInletEnum", "IsRequired": false}, {"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "smblLabId", "Type": "System.String", "IsRequired": false}, {"Name": "onlySmbl", "Type": "System.Boolean", "IsRequired": false}, {"Name": "showHide", "Type": "System.Boolean", "IsRequired": false}, {"Name": "hasClass", "Type": "System.Boolean", "IsRequired": false}, {"Name": "equipmentCode", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Base.Tree.AllTreeNode, XH.H82.Base, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.Organization.OrgnizationTreeForSmblController", "Method": "InstrumentsClassTree", "RelativePath": "api/OrgnizationTreeForSmbl/InstrumentsClassTree/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "inlet", "Type": "XH.H82.Models.Smbl.SmblInletEnum", "IsRequired": false}, {"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "onlySmbl", "Type": "System.Boolean", "IsRequired": false}, {"Name": "showHide", "Type": "System.Boolean", "IsRequired": false}, {"Name": "equipmentCode", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Base.Tree.AllTreeNode, XH.H82.Base, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.Organization.OrgnizationTreeForSmblController", "Method": "ScrapsOrStopClassTree", "RelativePath": "api/OrgnizationTreeForSmbl/ScrapsOrStopClassTree/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "inlet", "Type": "XH.H82.Models.Smbl.SmblInletEnum", "IsRequired": false}, {"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "smblLabId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Base.Tree.AllTreeNode, XH.H82.Base, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.Organization.OrgnizationTreeForSmblController", "Method": "ScrapsOrStopTree", "RelativePath": "api/OrgnizationTreeForSmbl/ScrapsOrStopTree/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "inlet", "Type": "XH.H82.Models.Smbl.SmblInletEnum", "IsRequired": false}, {"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "smblLabId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Base.Tree.AllTreeNode, XH.H82.Base, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.Organization.OrgnizationTreeForSmblController", "Method": "SubscriptionTree", "RelativePath": "api/OrgnizationTreeForSmbl/SubscriptionTree/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "inlet", "Type": "XH.H82.Models.Smbl.SmblInletEnum", "IsRequired": false}, {"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "smblLabId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Base.Tree.AllTreeNode, XH.H82.Base, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.Organization.OrgnizationTreeForSmblController", "Method": "WorkPlanClassTree", "RelativePath": "api/OrgnizationTreeForSmbl/WorkPlanClassTree/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "inlet", "Type": "XH.H82.Models.Smbl.SmblInletEnum", "IsRequired": false}, {"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Base.Tree.AllTreeNode, XH.H82.Base, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.Organization.OrgnizationTreeForSmblController", "Method": "WorkPlanTree", "RelativePath": "api/OrgnizationTreeForSmbl/WorkPlanTree/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "inlet", "Type": "XH.H82.Models.Smbl.SmblInletEnum", "IsRequired": false}, {"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "smblLabId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Base.Tree.AllTreeNode, XH.H82.Base, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.IoTDevice.PushAlertsController", "Method": "ExportToBeMaintainedEquipments", "RelativePath": "api/PushAlerts/ExportToBeMaintainedEquipments", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.IoTDevice.PushAlertsController", "Method": "InitCamera", "RelativePath": "api/Push<PERSON>ts/InitCamera", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": false}, {"Name": "sn", "Type": "System.String", "IsRequired": false}, {"Name": "aiSettinbg", "Type": "System.String", "IsRequired": false}, {"Name": "isAi", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.IoTDevice.PushAlertsController", "Method": "InitCameraAlerts", "RelativePath": "api/Push<PERSON>lerts/InitCamera<PERSON>lerts", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.IoTDevice.PushAlertsController", "Method": "PushCamera<PERSON><PERSON><PERSON>", "RelativePath": "api/Push<PERSON>lerts/PushCamera<PERSON>ts", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "XH.H82.API.Controllers.IoTDevice.Dto.CameraAlertDto", "IsRequired": true}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.IoTDevice.PushAlertsController", "Method": "PushMedicalEquipmentAlerts", "RelativePath": "api/PushAlerts/PushMedicalEquipmentAlerts", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "XH.H82.API.Controllers.IoTDevice.Dto.MedicalEquipmentAlertDto", "IsRequired": true}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.ReagentInfoController", "Method": "GetCalibratorItemList", "RelativePath": "api/ReagentInfo/GetCalibratorItemList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "itemId", "Type": "System.String", "IsRequired": false}, {"Name": "equipmentId", "Type": "System.String", "IsRequired": false}, {"Name": "materialId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.ReagentInfoController", "Method": "GetCalibratorList", "RelativePath": "api/ReagentInfo/GetCalibratorList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.ReagentInfoController", "Method": "GetInstrumentItemList", "RelativePath": "api/ReagentInfo/GetInstrumentItemList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.ReagentInfoController", "Method": "GetInstrumentItemReagentHistoryeList", "RelativePath": "api/ReagentInfo/GetInstrumentItemReagentHistoryeList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "itemId", "Type": "System.String", "IsRequired": false}, {"Name": "channelId", "Type": "System.String", "IsRequired": false}, {"Name": "equipmentId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.ReagentInfoController", "Method": "GetInstrumentItemReagentList", "RelativePath": "api/ReagentInfo/GetInstrumentItemReagentList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "itemId", "Type": "System.String", "IsRequired": false}, {"Name": "channelId", "Type": "System.String", "IsRequired": false}, {"Name": "equipmentId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.ReagentInfoController", "Method": "GetInstrumentReagentCommonList", "RelativePath": "api/ReagentInfo/GetInstrumentReagentCommonList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.Transaction.RecordContentController", "Method": "AddContentDict", "RelativePath": "api/RecordContent/AddContentDict", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "XH.H82.Models.Dtos.Transaction.AddContentDictInput", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.Transaction.RecordContentController", "Method": "DeleteContentDict", "RelativePath": "api/RecordContent/DeleteContentDict/{Id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.Transaction.RecordContentController", "Method": "GetContentDicts", "RelativePath": "api/RecordContent/GetContentDicts", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "classId", "Type": "System.String", "IsRequired": false}, {"Name": "content", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Models.Dtos.Transaction.ContentDictDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.Transaction.RecordContentController", "Method": "UpdateContentDict", "RelativePath": "api/RecordContent/UpdateContentDict/{Id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.String", "IsRequired": true}, {"Name": "input", "Type": "XH.H82.Models.Dtos.Transaction.UpdateContentDictInput", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.ScrapStopController", "Method": "ApplyAdopt", "RelativePath": "api/ScrapStop/ApplyAdopt", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "System.Collections.Generic.List`1[[XH.H82.Models.Dtos.ScrapStopListDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}, {"Name": "password", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.ScrapStopController", "Method": "ApplyReject", "RelativePath": "api/ScrapStop/ApplyReject", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "System.Collections.Generic.List`1[[XH.H82.Models.Dtos.ScrapStopListDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}, {"Name": "password", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.ScrapStopController", "Method": "ApplyRevoke", "RelativePath": "api/ScrapStop/ApplyRevoke", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "System.Collections.Generic.List`1[[XH.H82.Models.Dtos.ScrapStopListDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}, {"Name": "password", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.ScrapStopController", "Method": "BatchSubmit", "RelativePath": "api/ScrapStop/BatchSubmit", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "System.Collections.Generic.List`1[[XH.H82.Models.Dtos.ScrapStopListDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}, {"Name": "password", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.ScrapStopController", "Method": "DeleteAwaitSubmit", "RelativePath": "api/ScrapStop/DeleteAwaitSubmit", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "System.Collections.Generic.List`1[[XH.H82.Models.Dtos.ScrapStopListDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.ScrapStopController", "Method": "GetEquipmentApplyList", "RelativePath": "api/ScrapStop/GetEquipmentApplyList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "mgroupId", "Type": "System.String", "IsRequired": false}, {"Name": "equipmentClass", "Type": "System.String", "IsRequired": false}, {"Name": "keyword", "Type": "System.String", "IsRequired": false}, {"Name": "areaId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.ScrapStopController", "Method": "GetScrapStopList", "RelativePath": "api/ScrapStop/GetScrapStopList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startTime", "Type": "System.DateTime", "IsRequired": true}, {"Name": "endTime", "Type": "System.DateTime", "IsRequired": true}, {"Name": "mgroupId", "Type": "System.String", "IsRequired": false}, {"Name": "equipmentClass", "Type": "System.String", "IsRequired": false}, {"Name": "state", "Type": "System.String", "IsRequired": false}, {"Name": "applyClass", "Type": "System.String", "IsRequired": false}, {"Name": "equipmentKey", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "person_select", "Type": "System.String", "IsRequired": false}, {"Name": "mgroup_select", "Type": "System.String", "IsRequired": false}, {"Name": "areaId", "Type": "System.String", "IsRequired": false}, {"Name": "pgroupId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.ScrapStopController", "Method": "SaveApply", "RelativePath": "api/ScrapStop/SaveApply", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "System.Collections.Generic.List`1[[XH.H82.Models.Dtos.ScrapStopListDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.ScrapStopController", "Method": "SaveScrapStopApply", "RelativePath": "api/ScrapStop/SaveScrapStopApply", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "System.Collections.Generic.List`1[[XH.H82.Models.Dtos.ScrapStopListDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.ScrapStopController", "Method": "ScrapStopProcess", "RelativePath": "api/ScrapStop/ScrapStopProcess", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "scrapId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.ScrapStopController", "Method": "SubmitApply", "RelativePath": "api/ScrapStop/SubmitApply", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "System.Collections.Generic.List`1[[XH.H82.Models.Dtos.ScrapStopListDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}, {"Name": "password", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.ScrapStopController", "Method": "SubmitScrapStopApply", "RelativePath": "api/ScrapStop/SubmitScrapStopApply", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "System.Collections.Generic.List`1[[XH.H82.Models.Dtos.ScrapStopListDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}, {"Name": "password", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.SetUpController", "Method": "ApplyAllSetUpInfo", "RelativePath": "api/SetUp/ApplyAllSetUpInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "choiceValue", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.SetUpController", "Method": "GetPositionInfo", "RelativePath": "api/SetUp/GetPositionInfo", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.SetUpController", "Method": "GetSysSetUpList", "RelativePath": "api/SetUp/GetSysSetUpList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "mgroupId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.SetUpController", "Method": "IsNeedCheckPasswordByEditEquipment", "RelativePath": "api/SetUp/IsNeedCheckPasswordByEditEquipment", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.SetUpController", "Method": "IsNeedReviewProcess", "RelativePath": "api/SetUp/IsNeedReviewProcess", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.SetUpController", "Method": "UpdateSysSetUpInfo", "RelativePath": "api/SetUp/UpdateSysSetUpInfo", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "XH.LAB.UTILS.Models.SYS6_SETUP_DICT", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.SmblEquipmentController", "Method": "GetEquipmentApplyList", "RelativePath": "api/SmblEquipment/GetEquipmentApplyList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentClass", "Type": "System.String", "IsRequired": false}, {"Name": "keyword", "Type": "System.String", "IsRequired": false}, {"Name": "pGourpId", "Type": "System.String", "IsRequired": false}, {"Name": "labId", "Type": "System.String", "IsRequired": false}, {"Name": "smblLabId", "Type": "System.String", "IsRequired": false}, {"Name": "smblFlag", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.SmblEquipmentController", "Method": "GetEquipmentList", "RelativePath": "api/SmblEquipment/GetEquipmentList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "state", "Type": "System.String", "IsRequired": false}, {"Name": "smblEquipmentClass", "Type": "System.String", "IsRequired": false}, {"Name": "type", "Type": "System.String", "IsRequired": false}, {"Name": "key<PERSON>ord", "Type": "System.String", "IsRequired": false}, {"Name": "is<PERSON><PERSON>d", "Type": "System.String", "IsRequired": false}, {"Name": "labId", "Type": "System.String", "IsRequired": false}, {"Name": "smblLabId", "Type": "System.String", "IsRequired": false}, {"Name": "smblFlag", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.SmblEquipmentController", "Method": "GetScrapStopList", "RelativePath": "api/SmblEquipment/GetScrapStopList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startTime", "Type": "System.DateTime", "IsRequired": true}, {"Name": "endTime", "Type": "System.DateTime", "IsRequired": true}, {"Name": "smblEquipmentClass", "Type": "System.String", "IsRequired": false}, {"Name": "equipmentClass", "Type": "System.String", "IsRequired": false}, {"Name": "szLastDispose", "Type": "System.String", "IsRequired": false}, {"Name": "szEventClass", "Type": "System.String", "IsRequired": false}, {"Name": "equipmentKey", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "person_select", "Type": "System.String", "IsRequired": false}, {"Name": "labId", "Type": "System.String", "IsRequired": false}, {"Name": "smblLabId", "Type": "System.String", "IsRequired": false}, {"Name": "smblFlag", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.SmblEquipmentController", "Method": "GetSubscribeInfoList", "RelativePath": "api/SmblEquipment/GetSubscribeInfoList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startTime", "Type": "System.DateTime", "IsRequired": true}, {"Name": "endTime", "Type": "System.DateTime", "IsRequired": true}, {"Name": "state", "Type": "System.String", "IsRequired": false}, {"Name": "search", "Type": "System.String", "IsRequired": false}, {"Name": "labId", "Type": "System.String", "IsRequired": false}, {"Name": "smblLabId", "Type": "System.String", "IsRequired": false}, {"Name": "smblFlag", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.SmblEquipmentController", "Method": "GetWorkPlans", "RelativePath": "api/SmblEquipment/GetWorkPlans", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "keyword", "Type": "System.String", "IsRequired": false}, {"Name": "smblEquipmentClass", "Type": "System.String", "IsRequired": false}, {"Name": "equipmentClass", "Type": "System.String", "IsRequired": false}, {"Name": "labId", "Type": "System.String", "IsRequired": false}, {"Name": "smblLabId", "Type": "System.String", "IsRequired": false}, {"Name": "smblFlag", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Models.Dtos.EmsWorkPlanDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.SubscribeController", "Method": "AddSubscribeInfo", "RelativePath": "api/Subscribe/AddSubscribeInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "XH.H82.Models.Entities.EMS_SUBSCRIBE_INFO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.SubscribeController", "Method": "DeleteSubscribeInfo", "RelativePath": "api/Subscribe/DeleteSubscribeInfo", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "subscribeId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.SubscribeController", "Method": "GetSubscribeInfoList", "RelativePath": "api/Subscribe/GetSubscribeInfoList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startTime", "Type": "System.DateTime", "IsRequired": true}, {"Name": "endTime", "Type": "System.DateTime", "IsRequired": true}, {"Name": "mgroupId", "Type": "System.String", "IsRequired": false}, {"Name": "state", "Type": "System.String", "IsRequired": false}, {"Name": "search", "Type": "System.String", "IsRequired": false}, {"Name": "pgroupId", "Type": "System.String", "IsRequired": false}, {"Name": "areaId", "Type": "System.String", "IsRequired": true}, {"Name": "smblFlag", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.SubscribeController", "Method": "GetSubscribeProcess", "RelativePath": "api/Subscribe/GetSubscribeProcess", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "subscribeId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.SubscribeController", "Method": "UpdateSubscribeInfo", "RelativePath": "api/Subscribe/UpdateSubscribeInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "XH.H82.Models.Entities.EMS_SUBSCRIBE_INFO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.SystemController", "Method": "AboutSystem", "RelativePath": "api/System/AboutSystem", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.SystemController", "Method": "ExportMenuApiList", "RelativePath": "api/System/ExportMenuApiList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "format", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.SystemController", "Method": "GetHospitalInfo", "RelativePath": "api/System/GetHospitalInfo", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "hospitalId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.SystemController", "Method": "GetLinkDocumentationSystemUrl", "RelativePath": "api/System/GetLinkDocumentationSystemUrl", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "labId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.SystemController", "Method": "GetLogonHospitalLablist", "RelativePath": "api/System/GetLogonHospitalLablist", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[XH.LAB.UTILS.Models.LogonHospitalLabList, XH.LAB.UTILS, Version=6.25.300.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.SystemController", "Method": "GetToken", "RelativePath": "api/System/GetToken", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "logId", "Type": "System.String", "IsRequired": false}, {"Name": "password", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.SystemController", "Method": "GetTokenSubstitution", "RelativePath": "api/System/GetTokenSubstitution", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "accessToken", "Type": "System.String", "IsRequired": false}, {"Name": "refreshToken", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.SystemController", "Method": "GetUnitLoginUrl", "RelativePath": "api/System/GetUnitLoginUrl", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.SystemController", "Method": "GetUserLabGroup", "RelativePath": "api/System/GetUserLabGroup", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.SystemController", "Method": "HasButtonPermissions", "RelativePath": "api/System/HasButtonPermissions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "buttonPermissionId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.SystemController", "Method": "HasEquipmentDeletedPromiss", "RelativePath": "api/System/HasEquipmentDeletedPromiss", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.SystemController", "Method": "HealthCheck", "RelativePath": "api/System/HealthCheck", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.SystemController", "Method": "ReNewToken", "RelativePath": "api/System/ReNewToken", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "expriedToken", "Type": "System.String", "IsRequired": false}, {"Name": "refreshToken", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.SystemController", "Method": "ServiceAreaList", "RelativePath": "api/System/ServiceAreaList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "type", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Models.Dtos.KeyValueDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.SystemController", "Method": "UpdateLog", "RelativePath": "api/System/UpdateLog", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.SystemController", "Method": "UserVerify", "RelativePath": "api/System/UserVerify", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "smPassword", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.TestController", "Method": "AutoTest", "RelativePath": "api/Test/AutoTest", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.TestController", "Method": "ConutEquipmentMonitoringItemsDay", "RelativePath": "api/Test/ConutEquipmentMonitoringItemsDay", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "date", "Type": "System.DateTime", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.TestController", "Method": "Equipment_shuiya", "RelativePath": "api/Test/Equipment_shuiya", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "sn", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.TestController", "Method": "Equipment_xiyanqi", "RelativePath": "api/Test/Equipment_xiyanqi", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "sn", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.TestController", "Method": "Equipment_zinengkaiguan", "RelativePath": "api/Test/Equipment_zinengkaiguan", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.TestController", "Method": "Equipment_ziwaideng", "RelativePath": "api/Test/Equipment_ziwaideng", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "sn", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.TestController", "Method": "EquipmentEvnInit", "RelativePath": "api/Test/EquipmentEvnInit", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.TestController", "Method": "EquipmentInit", "RelativePath": "api/Test/EquipmentInit", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.TestController", "Method": "EquipmentlabmInit", "RelativePath": "api/Test/EquipmentlabmInit", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.TestController", "Method": "EquipmentWaterInit", "RelativePath": "api/Test/EquipmentWaterInit", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.TestController", "Method": "GetEnvironmentDevicesDto", "RelativePath": "api/Test/GetEnvironmentDevicesDto", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "sn", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.TestController", "Method": "GetEquipmentMoitorHoursData", "RelativePath": "api/Test/GetEquipmentMoitorHoursData", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": false}, {"Name": "date", "Type": "System.DateTime", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.TestController", "Method": "GetEquipmentMoitorHoursDataByDay", "RelativePath": "api/Test/GetEquipmentMoitorHoursDataByDay", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": false}, {"Name": "date", "Type": "System.DateTime", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.TestController", "Method": "GetTest10009", "RelativePath": "api/Test/GetTest10009", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.TestController", "Method": "GetTestNewBoxUpload", "RelativePath": "api/Test/GetTestNewBoxUpload", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.TestController", "Method": "InitEquipmentMoitorHoursData", "RelativePath": "api/Test/InitEquipmentMoitorHoursData", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": false}, {"Name": "date", "Type": "System.DateTime", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.TestController", "Method": "testSopFiles", "RelativePath": "api/Test/testSopFiles", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers._Demos.TokenTestController", "Method": "AuthorizeApi", "RelativePath": "api/TokenTest/AuthorizeApi", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers._Demos.TokenTestController", "Method": "CreateToken", "RelativePath": "api/TokenTest/CreateToken", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers._Demos.TokenTestController", "Method": "NonRoleAuthorizeTest", "RelativePath": "api/TokenTest/NonRoleAuthorizeTest", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers._Demos.TokenTestController", "Method": "ReNewToken", "RelativePath": "api/TokenTest/ReNewToken", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "expriedToken", "Type": "System.String", "IsRequired": false}, {"Name": "refeshToken", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.WorkPlanController", "Method": "GetClassList", "RelativePath": "api/WorkPlan/GetClassList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.WorkPlanController", "Method": "SaveWorkPlan", "RelativePath": "api/WorkPlan/SaveWorkPlan", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "System.Collections.Generic.List`1[[XH.H82.Models.Dtos.WorkPlanDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.XhEquipmentDocController", "Method": "AutoDispatchUploadFile", "RelativePath": "api/XhEquipmentDoc/AutoDispatchUploadFile", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "FILE", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "FILE_NAME", "Type": "System.String", "IsRequired": false}, {"Name": "FILE_SUFFIX", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.XhEquipmentDocController", "Method": "AutoDispatchUploadFileTest", "RelativePath": "api/XhEquipmentDoc/AutoDispatchUploadFileTest", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.XhEquipmentDocController", "Method": "CardPrint", "RelativePath": "api/XhEquipmentDoc/CardPrint", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "cardType", "Type": "System.String", "IsRequired": true}, {"Name": "equipmentId", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.XhEquipmentDocController", "Method": "ExportEquipmentList", "RelativePath": "api/XhEquipmentDoc/ExportEquipmentList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "record", "Type": "System.Collections.Generic.List`1[[XH.H82.Models.Dtos.ExportEquipmentDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.XhEquipmentDocController", "Method": "GenerateEquipmentInfo", "RelativePath": "api/XhEquipmentDoc/GenerateEquipmentInfo", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.XhEquipmentDocController", "Method": "GetCardTypeList", "RelativePath": "api/XhEquipmentDoc/GetCardTypeList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.XhEquipmentDocController", "Method": "GetEquipmentClassList", "RelativePath": "api/XhEquipmentDoc/GetEquipmentClassList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "areaId", "Type": "System.String", "IsRequired": false}, {"Name": "equipmentNo", "Type": "System.String", "IsRequired": false}, {"Name": "ifHide", "Type": "System.String", "IsRequired": false}, {"Name": "pGroupId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.XhEquipmentDocController", "Method": "GetEquipmentListByMgroup", "RelativePath": "api/XhEquipmentDoc/GetEquipmentListByMgroup", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "areaId", "Type": "System.String", "IsRequired": false}, {"Name": "labId", "Type": "System.String", "IsRequired": false}, {"Name": "equipmentNo", "Type": "System.String", "IsRequired": false}, {"Name": "ifHide", "Type": "System.String", "IsRequired": false}, {"Name": "pGroupId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.XhEquipmentDocController", "Method": "HideEquipment", "RelativePath": "api/XhEquipmentDoc/HideEquipment", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.XhEquipmentDocController", "Method": "SetEquipmentIsIn", "RelativePath": "api/XhEquipmentDoc/SetEquipmentIsIn/{equipmentId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentId", "Type": "System.String", "IsRequired": true}, {"Name": "isIn", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.XH.H82.API.Controllers.XhWorkPlanController", "Method": "AuditWorkPlans", "RelativePath": "api/XhWorkPlan/AuditWorkPlans", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "XH.H82.Services.OperationLog.AuditWorkPlanInput", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.XH.H82.API.Controllers.XhWorkPlanController", "Method": "GetEmsWordPlanCirculationRecords", "RelativePath": "api/XhWorkPlan/GetEmsWordPlanCirculationRecords", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "workPlanId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Models.Dtos.EmsWorkPlanCirculationRecordDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.XH.H82.API.Controllers.XhWorkPlanController", "Method": "GetEquipmentClassList", "RelativePath": "api/XhWorkPlan/GetEquipmentClassList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "areaId", "Type": "System.String", "IsRequired": false}, {"Name": "pGroupId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.XH.H82.API.Controllers.XhWorkPlanController", "Method": "GetMgroupList", "RelativePath": "api/XhWorkPlan/GetMgroupList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "areaId", "Type": "System.String", "IsRequired": false}, {"Name": "pGroupId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[XH.H82.Models.Dtos.NewOATreeDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.XH.H82.API.Controllers.XhWorkPlanController", "Method": "GetWorkPlanList", "RelativePath": "api/XhWorkPlan/GetWorkPlanList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "keyword", "Type": "System.String", "IsRequired": false}, {"Name": "mgroupId", "Type": "System.String", "IsRequired": false}, {"Name": "equipmentClass", "Type": "System.String", "IsRequired": false}, {"Name": "pgroupId", "Type": "System.String", "IsRequired": false}, {"Name": "areaId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.XH.H82.API.Controllers.XhWorkPlanController", "Method": "GetWorkPlans", "RelativePath": "api/XhWorkPlan/GetWorkPlans", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "labId", "Type": "System.String", "IsRequired": false}, {"Name": "keyword", "Type": "System.String", "IsRequired": false}, {"Name": "mgroupId", "Type": "System.String", "IsRequired": false}, {"Name": "equipmentClass", "Type": "System.String", "IsRequired": false}, {"Name": "pgroupId", "Type": "System.String", "IsRequired": false}, {"Name": "areaId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "XH.H82.API.Extensions.ResultDto`1[[System.Collections.Generic.List`1[[XH.H82.Models.Dtos.EmsWorkPlanDto, XH.H82.Models, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "XH.H82.API.Controllers.XH.H82.API.Controllers.XhWorkPlanController", "Method": "OverruledWorkPlans", "RelativePath": "api/XhWorkPlan/OverruledWorkPlans", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "XH.H82.Services.OperationLog.OverruledWorkPlanInput", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers.XH.H82.API.Controllers.XhWorkPlanController", "Method": "SubmitWorkPlans", "RelativePath": "api/XhWorkPlan/SubmitWorkPlans", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "XH.H82.Services.OperationLog.SubmitWorkPlanInput", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers._Demos.XinghePlatformController", "Method": "CallXhPlatformInterfaceSource", "RelativePath": "api/XinghePlatform/CallXhPlatformInterfaceSource", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "headXml", "Type": "System.String", "IsRequired": false}, {"Name": "bodyXml", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "XH.H82.API.Controllers._Demos.XinghePlatformController", "Method": "CallXingheInterface", "RelativePath": "api/XinghePlatform/CallXingheInterface", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "headXml", "Type": "System.String", "IsRequired": false}, {"Name": "bodyXml", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}]