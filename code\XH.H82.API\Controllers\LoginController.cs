﻿using System.Net.NetworkInformation;
using System.Security.Claims;
using H.Utility;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using XH.H82.Base.Helper;
using XH.H82.IServices;
using XH.H82.Models.ViewDtos;

namespace XH.H82.API.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    //[ApiExplorerSettings(GroupName = "base_api")]
    public class LoginController : ControllerBase
    {
        private readonly IXHLISCommonService _iHLISCommonService;
        private readonly IModuleLabGroupService _IModuleLabGroupService;
        private readonly string VersionNum = "";
        public LoginController( IConfiguration configuration, IXHLISCommonService iHLISCommonService, IModuleLabGroupService moduleLabGroupService)
        {
            _IModuleLabGroupService = moduleLabGroupService;
            _iHLISCommonService = iHLISCommonService;
            VersionNum = configuration["ConnectionStrings:VersionNum"];
        }

        /// <summary>
        /// 用户登录
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        [AcceptVerbs("Get", "Post")]     //用AcceptVerbs标识即可
        public object UserLogin(dynamic obj)
        {
            ResultDto dto = new ResultDto();
            try
            {
                dynamic json = JsonConvert.DeserializeObject(Convert.ToString(obj));
                string userInfo = json["userInfo"].ToString();

                bool loginType = false;
                if (userInfo.Contains("<Type>LogIn</Type>"))
                {
                    loginType = true;
                }
                loginType.CheckNotNullOrEmpty("登录类型不能为空");
                userInfo.CheckNotNullOrEmpty("userInfo");
                var login = _iHLISCommonService.Login(userInfo);
                UserInfo user = new UserInfo();
                if (login.success)
                {
                    // var user = login.data as UserInfo;
                    user = login.data as UserInfo;
                    if (loginType)
                    {
                        //人员科室
                        var lablist = _IModuleLabGroupService.GetLisInspectionLabInfo(user.USER_NO, user.HOSPITAL_ID);
                        user.LIS_INSPECTION_LAB = lablist;

                        //人员科室分组
                        var mgrouplist = _IModuleLabGroupService.GetLisInspectionMgroupInfo(user.USER_NO, user.HOSPITAL_ID);
                        user.SYS6_INSPECTION_MGROUP = mgrouplist;

                        user.CLIENT_IP = "127.0.0.1";

                        IPGlobalProperties computerProperties = IPGlobalProperties.GetIPGlobalProperties();
                        NetworkInterface[] nics = NetworkInterface.GetAllNetworkInterfaces();
                        var MACIp = "";
                        foreach (NetworkInterface adapter in nics)
                        {
                            var adapterName = adapter.Name;
                            //  if (adapterName == "本地连接" || adapterName == "宽带连接")
                            if (adapterName == "以太网")
                            {
                                PhysicalAddress address = adapter.GetPhysicalAddress();
                                byte[] bytes = address.GetAddressBytes();
                                for (int i = 0; i < bytes.Length; i++)
                                {
                                    MACIp += bytes[i].ToString("X2");

                                    if (i != bytes.Length - 1)
                                    {
                                        MACIp += "-";
                                    }
                                }
                            }
                        }
                        user.MAC_IP = MACIp.ToString();
                        user.VERSION_NUM = VersionNum;
                        user.USER_NAME = user.USER_NAME;
                        user.OPERATE_PERSON = user.LOGIN_ID + "_" + user.USER_NAME;
                        string fileUploadAddress = "http://" + _IModuleLabGroupService.GetModuleInfoByModuleId(user.HOSPITAL_ID, "J12");//
                        string filePreviewAddress = "http://" + _IModuleLabGroupService.GetModuleInfoByModuleId(user.HOSPITAL_ID, "J12-03");//J12-03
                        user.FILE_UPLOAD_ADDRESS = fileUploadAddress;
                        user.FILE_PREVIEW_ADDRESS = filePreviewAddress;

                        var sysUser = _IModuleLabGroupService.GetUserInfo(user.LOGIN_ID);

                        var pgrouplist = _IModuleLabGroupService.GetLisInspectionPgroupInfo(user.USER_NO, user.HOSPITAL_ID);
                        user.SYS6_INSPECTION_PGROUP = pgrouplist;
                        if (pgrouplist.FirstOrDefault() != null)
                        {
                            user.LAB_ID = pgrouplist.FirstOrDefault().LAB_ID;
                        }

                        user.PASSWORD = sysUser.PWD_BS;

                        string strMgroupId = "";
                        pgrouplist.ToList().ForEach(item =>
                        {
                            strMgroupId += "," + item.PGROUP_ID;
                        });
                        strMgroupId = strMgroupId.TrimStart(',');
                        if (!string.IsNullOrEmpty(user.USER_ID))
                        {
                            var claims = new[]
                              {
                                    new Claim("USER_NO", user.USER_ID),
                                    new Claim("LOG_ID", user.LOGIN_ID),
                                    new Claim("USER_NAME",user.USER_NAME),
                                    new Claim("HOSPITAL_ID",user.HOSPITAL_ID),
                                    new Claim("HIS_ID",user.HIS_ID),
                                    new Claim("LAB_ID",user.LAB_ID),
                                    new Claim("PASSWORD",user.PASSWORD),
                                    new Claim("MGROUPID_AGGREGATE",strMgroupId),
                                    new Claim("FILE_UPLOAD_ADDRESS",fileUploadAddress),
                                    new Claim("FILE_PREVIEW",filePreviewAddress),
                               };
                            user.Token = JwtTokenHelper.CreateToken(claims, 12);
                        }
                    }
                    return Ok(new ResultDto { msg = login.msg, success = true, data = user });
                }
                else
                {
                    return Ok(new ResultDto { msg = login.msg, success = false });
                }
            }
            catch (Exception ex)
            {
                return Ok(new ResultDto { msg = ex.ToString(), success = false });
            }
        }

    }
}
