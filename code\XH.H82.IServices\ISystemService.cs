﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using H.Utility;
using H.Utility.Dtos;
using XH.H82.Models.Dtos.Base;
using XH.LAB.UTILS.Models;

namespace XH.H82.IServices
{

    public enum EnumSetupType{
        显示设置, 表单设置, 表格设置, 条件设置, tab页设置
    }

    public enum EnumBaseDataGetType
    {
        缓存数据=1,
        表数据=2
    }
    /// <summary>
    /// 基础服务,包括接口请求/设置请求/基础数据请求/Token续签
    /// 由陈增华负责统一维护,注意不要修改此接口
    /// </summary>
    public interface ISystemService
    {


       
        /// <summary>
        ///  调用接口返回原始结果
        /// </summary>
        /// <param name="headXml"></param>
        /// <param name="bodyXml"></param>
        /// <param name="dataformat">返回的数据格式</param>
        /// <returns></returns>
        public string CallXhPlatformInterfaceSource(string headXml, string bodyXml, out string dataformat);

        /// <summary>
        /// 调用接口返回映射后的实体
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="headXml"></param>
        /// <param name="bodyXml"></param>
        /// <returns>T</returns>
        public T CallXhPlatformInterface<T>(string headXml, string bodyXml) where T : class;


        /// <summary>
        /// 获取配置项传入pageId 返回页面所有设置,不传pageId 获取模块所有设置
        /// </summary>
        /// <param name="hospitalId">机构ID</param>
        /// <param name="moduleId">模块ID</param>
        /// <param name="pageId">界面ID</param>
        /// <param name="qunitID">单元ID</param>
        /// <returns>如需操作内容.自行根据接口返回内容映射对象</returns>
        public object GetAllConfig(string hospitalId, string moduleId, string pageId,string? qunitID=null);

        /// <summary>
        /// 获取单个设置
        /// </summary>
        /// <param name="stepType"><see cref="EnumSetupType"/></param>
        /// <param name="hospitalId">机构ID</param>
        /// <param name="moduleId">模块ID</param>
        /// <param name="setupName">设置项名称</param>
        /// <param name="qunitID">单元ID</param>
        /// <returns></returns>
        public object GetSingleConfig(EnumSetupType stepType,string hospitalId, string moduleId, string setupName, string? qunitID = null);

        /// <summary>
        /// 直接用从S01签发token
        /// 注意,此方法仅需提供用户信息就能从S01签发token
        /// 仅特定项目,如统一登录使用,眼睛暴露给前端
        /// </summary>
        /// <param name="userNo"></param>
        /// <param name="tokenGuid"></param>
        /// <param name="moduleId"></param>
        /// <param name="extraInfo">扩展信息(claims中的扩展字段内容)</param>
        /// <returns></returns>
        public ResultDto GetIssueTokenInfo(string userNo, string tokenGuid, string moduleId,string? extraInfo);

        /// <summary>
        /// token续签
        /// </summary>
        /// <param name="expriedToken">过期的token</param>
        /// <param name="refreshToken">有效的刷新token</param>
        /// <returns></returns>
        public ResultDto ReNewToken(string expriedToken,string refreshToken);

        /// <summary>
        /// 传入用户信息自定义生成TOKEN
        /// </summary>
        /// <param name="userNo"></param>
        /// <param name="tokenGuid"></param>
        /// <param name="moduleId"></param>
        /// <param name="obj">自定义用户信息</param>
        /// <returns></returns>
        public ResultDto CustomCreateToken(string userNo, dynamic obj, string tokenGuid, string moduleId);
        /// <summary>
        /// 调用6.0方法方法
        /// </summary>
        /// <param name="headXml"></param>
        /// <param name="bodyXml"></param>
        /// <returns></returns>
        public ResultDto CallS10InterfaceSource(string headXml, string bodyXml);


        /// <summary>
        /// 换token
        /// </summary>
        /// <param name="expriedToken"></param>
        /// <param name="refreshToken"></param>
        /// <returns></returns>
        ResultDto TokenSubstitution(string expriedToken, string refreshToken);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="jsonStr"></param>
        /// <returns></returns>
        ResultDto UserVerify(string jsonStr);

        /// <summary>
        /// 获取医疗结构信息
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        ResultDto GetHospitalInfo(string hospitalId);


        /// <summary>
        /// 查询当权用户是否有设备档案删除权限
        /// </summary>
        /// <returns></returns>
        public bool HasEquipmentDeletedPromiss();
        
        LogonHospitalLabList GetLogonHospitalLablist(ClaimsDto user);
        
         sys6UserDto Sm2PasswordCheck(string loginId, string smPassword = "");
            
         bool HasButtonPermissions(string promiss);
    }
}
