using System.Linq.Expressions;
using System.Reflection;
using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using NuGet.Packaging;
using Serilog;
using SqlSugar;
using XH.H82.IServices.EquipmentCodeCustom;
using XH.H82.Models.Entities;
using XH.H82.Models.EquipmengtClassNew;
using XH.H82.Models.EquipmentCodeCustom;
using XH.H82.Models.SugarDbContext;
using XH.H82.Services.DeviceDataRefresh;

namespace XH.H82.Services.EquipmentCodeCustom;

public class CustomCodeService : ICustomCodeService
{
    private readonly ISqlSugarUow<SugarDbContext_Master> _dbContext;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IConfiguration _configuration;
    private readonly EquipmentContext _equipmentContext;
    private readonly IMemoryCache _memoryCache;
    const string CACHE_KEY = "CustomCodeService_GetEquipmentCodeCustomDict";
    

    public CustomCodeService(ISqlSugarUow<SugarDbContext_Master> dbcontext, IHttpContextAccessor httpContextAccessor, IConfiguration configuration, IMemoryCache memoryCache)
    {
        _dbContext = dbcontext;
        _httpContextAccessor = httpContextAccessor;
        _configuration = configuration;
        _memoryCache = memoryCache;
        _equipmentContext = new EquipmentContext(_dbContext);
    }


    public List<EquipmentCodeCustomDict> GetEquipmentCodeCustomDictByCondition(Expression<Func<EMS_EQPNO_FORMAT_DICT , bool>> expression)
    {
        var  result =  Array.Empty<EquipmentCodeCustomDict>().ToList();
        var dict = _dbContext.Db.Queryable<EMS_EQPNO_FORMAT_DICT>()
            .Where(expression)
            .ToList();
       
        foreach (var item in dict)
        {
            var equipmentCodeCustomDict = new EquipmentCodeCustomDict
            {
                EqpNoId = item.EqpNoId,
                HospitalId = item.HospitalId,
                EqpNoName = item.EqpNoName,
                EqpNoLevel = item.EqpNoLevel,
                EqpNoClass = item.EqpNoClass,
                EqpNoApplys = item.EqpNoApplys,
                EqpNoState = item.EqpNoState,
                DisplayContent = JsonConvert.DeserializeObject<DisplayContent>(item.EqpDisplayJson)
            };
            result.Add(equipmentCodeCustomDict);
        }

        foreach (var item in result)
        {
            var classNames = "";
            var applys = "";
            item.EqpNoClassList.ForEach(x =>
            {
                if (x == "0")
                {
                    classNames += "全部设备" + "、";
                }
                else
                {
                    classNames += _equipmentContext.ExchangeEquipmentClass(x,null) + "、";
                }
            });
            classNames = classNames.TrimEnd('、');
            item.EqpNoClassName = classNames;
            if (item.EqpNoApplyList.Any())
            {
                item.EqpNoApplyList.ForEach(x =>
                {
                    applys += _equipmentContext.ExchangeProfessionalGroupName(x) + "、";
                });
            }
            
            applys = applys.TrimEnd('、');
            item.EqpNoApplysName = applys;
            item.DisplayContentName =
                GetEquipmentUCode(item.DisplayContent, GetEquipmentInfoByCache(item.EqpNoClassList.First()));
        }
        return result;
    }

    public List<EquipmentCodeCustomDict> GetEquipmentCodeCustomDict()
    {
        return GetEquipmentCodeCustomDictByCondition(x=>x.EqpNoState != "2");
    }



    public List<EquipmentCodeCustomDict> GetEquipmentCodeCustomDict(EMS_EQUIPMENT_INFO equipment)
    {
        var dict = Array.Empty<EquipmentCodeCustomDict>().ToList();

        // 获取设备类型信息，确定层级
        var equipmentClass = _dbContext.Db.Queryable<EMS_EQUIPMENT_CLASS_DICT>()
            .Where(x => x.ClassId == equipment.EQUIPMENT_CLASS)
            .First();

        if (equipmentClass == null)
        {
            // 如果找不到设备类型，使用原有逻辑
            _dbContext.Db.Queryable<EMS_EQPNO_FORMAT_DICT>()
               .Where(x => x.EqpNoState == "1")
               .Where(x => x.EqpNoApplys.Contains(equipment.UNIT_ID))
               .ForEach(x => dict.Add(x.GetEquipmentCodeCustomDict()));

            return dict.Where(x => x.EqpNoClassList.Contains(equipment.EQUIPMENT_CLASS) || x.EqpNoClass == "0").ToList();
        }

        // 根据设备类型层级确定查询范围
        var targetClassIds = new List<string>();

        if (equipmentClass.ClassLevel == "1")
        {
            // 1级设备类型：直接使用当前设备类型
            targetClassIds.Add(equipment.EQUIPMENT_CLASS);
        }
        else if (equipmentClass.ClassLevel == "2")
        {
            // 2级设备类型：需要找到父级的ClassId
            if (!string.IsNullOrEmpty(equipmentClass.ParentClassId) && equipmentClass.ParentClassId != "0")
            {
                targetClassIds.Add(equipmentClass.ParentClassId);
            }
            // 同时也包含自己的类型
            targetClassIds.Add(equipment.EQUIPMENT_CLASS);
        }
        else
        {
            // 其他层级：使用当前设备类型
            targetClassIds.Add(equipment.EQUIPMENT_CLASS);
        }

        // 查询匹配的自定义模板字典
        _dbContext.Db.Queryable<EMS_EQPNO_FORMAT_DICT>()
           .Where(x => x.EqpNoState == "1")
           .Where(x => x.EqpNoApplys.Contains(equipment.UNIT_ID))
           .ForEach(x => dict.Add(x.GetEquipmentCodeCustomDict()));

        // 根据设备类型层级过滤结果
        var result = dict.Where(x =>
            x.EqpNoClass == "0" || // 全部类型
            targetClassIds.Any(classId => x.EqpNoClassList.Contains(classId)) // 匹配目标类型
        ).ToList();

        return result;
    }
    
    /// <summary>
    /// 获取设备对应的最高优先级配置模板
    /// 优先级计算规则：
    /// 1. EqpNoLevel 数值越大，优先级越高
    /// 2. EqpNoApplys 应用范围越小（越精确），优先级越高
    /// 3. EqpNoClass 为具体类型比"0"（全部类型）优先级更高
    /// </summary>
    /// <param name="equipment">设备信息</param>
    /// <returns>最高优先级的配置模板，如果没有找到则返回null</returns>
    public EquipmentCodeCustomDict? GetEquipmentCodeCustomDictByEquipment(EMS_EQUIPMENT_INFO equipment)
    {
        var candidateConfigs = GetEquipmentCodeCustomDict(equipment);
        if (!candidateConfigs.Any())
        {
            return null;
        }

        // 计算每个配置的综合优先级分数
        var configsWithPriority = candidateConfigs.Select(config => new
        {
            Config = config,
            Priority = CalculateConfigPriority(config, equipment)
        }).ToList();

        // 按优先级分数降序排列，取最高分的配置
        var bestConfig = configsWithPriority
            .OrderByDescending(x => x.Priority.TotalScore)
            .ThenByDescending(x => x.Priority.LevelScore)
            .ThenByDescending(x => x.Priority.ScopeScore)
            .ThenByDescending(x => x.Priority.ClassScore)
            .First();

        return bestConfig.Config;
    }

    /// <summary>
    /// 计算配置的优先级分数
    /// </summary>
    /// <param name="config">配置模板</param>
    /// <param name="equipment">设备信息</param>
    /// <returns>优先级分数对象</returns>
    private ConfigPriorityScore CalculateConfigPriority(EquipmentCodeCustomDict config, EMS_EQUIPMENT_INFO equipment)
    {
        // 1. Level分数：直接使用EqpNoLevel数值
        var levelScore = int.TryParse(config.EqpNoLevel, out var level) ? level : 0;

        // 2. Scope分数：应用范围越小分数越高（范围越精确优先级越高）
        var applyList = config.EqpNoApplyList ?? new List<string>();
        var scopeScore = applyList.Count > 0 ? (1000 / applyList.Count) : 0; // 范围越小分数越高

        // 3. Class分数：具体类型比"0"（全部类型）优先级更高
        var classList = config.EqpNoClassList ?? new List<string>();
        var classScore = 0;
        if (classList.Contains("0"))
        {
            classScore = 100; // 全部类型基础分数
        }
        else if (classList.Contains(equipment.EQUIPMENT_CLASS))
        {
            classScore = 200; // 精确匹配设备类型，更高分数
        }

        // 4. 计算总分
        var totalScore = levelScore * 10000 + scopeScore * 100 + classScore;

        return new ConfigPriorityScore
        {
            LevelScore = levelScore,
            ScopeScore = scopeScore,
            ClassScore = classScore,
            TotalScore = totalScore
        };
    }

    /// <summary>
    /// 配置优先级分数结构
    /// </summary>
    private class ConfigPriorityScore
    {
        public int LevelScore { get; set; }    // 级别分数
        public int ScopeScore { get; set; }    // 范围分数
        public int ClassScore { get; set; }    // 类型分数
        public int TotalScore { get; set; }    // 总分数
    }

    public string ExchangeEquipmentUCode(DisplayContent dictContent ,EMS_EQUIPMENT_INFO equipment)
    {
        var code = GetEquipmentUCode(dictContent, equipment);
        Console.WriteLine("生成设备编号：{0}",code);
        return code;
    }
    
    private string GetEquipmentUCode(DisplayContent dictContent , EMS_EQUIPMENT_INFO  equipment)
    {
        FillEquipmentInfo(equipment);
        // 获取模板
        string result = dictContent.DisplayContentCode; // 假设 template 是类似 "{EQUIPMENT_NAME}_{EQUIPMENT_CODE}" 的字符串

        if (result.IsNullOrEmpty())
        {
            return "";
        }
        // 使用反射获取 equipment 的属性信息
        PropertyInfo[] properties = equipment.GetType().GetProperties();

        // 将模板中的占位符替换为实际属性值
        foreach (PropertyInfo property in properties)
        {
            string placeholder = "{" + property.Name + "}";
            if (result.Contains(placeholder))
            {
                if (dictContent.FixedFieldDisplays != null)
                {
                    var display = dictContent.FixedFieldDisplays.FirstOrDefault(x => x.Key == placeholder);
                    if (display != null)
                    {
                        if (display.Value.IsNullOrEmpty())
                        {
                            var objectValue = property.GetValue(equipment);
                            result = result.Replace(placeholder, objectValue?.ToString() ?? "");
                        }
                        else
                        {
                            result = result.Replace(placeholder, display.Value);
                        }
                    }
                    else
                    {
                        var value = property.GetValue(equipment);
                        result = result.Replace(placeholder, value?.ToString() ?? "");
                    }
                }
                else
                {
                    var value = property.GetValue(equipment);
                    result = result.Replace(placeholder, value?.ToString() ?? "");
                }
            }

        }
        return result ;
    }

    public EquipmentCodeCustomDict AddEquipmentCodeCustom(AddEquipmentCodeCustomDictDto input)
    {
        var user = _httpContextAccessor.HttpContext.User.ToClaimsDto();
        var initDisplays = DisplayContent.CreatFixedFieldDisplays();
        if (input.DisplayContent.FixedFieldDisplays.Any())
        {
            foreach (var keyValue in initDisplays)
            {
                var inputKeyValue = input.DisplayContent.FixedFieldDisplays.FirstOrDefault(x => x.Key == keyValue.Key);
                if (inputKeyValue is null)
                {
                    continue;
                }
                keyValue.Value = inputKeyValue.Value;
            }
        }
        input.DisplayContent.FixedFieldDisplays = initDisplays;
        var dict = new EMS_EQPNO_FORMAT_DICT();
        dict.EqpNoId = IDGenHelper.CreateGuid();
        dict.HospitalId = user.HOSPITAL_ID;
        dict.EqpNoName = input.EqpNoName;
        dict.EqpNoLevel = input.EqpNoLevel;
        dict.EqpNoClass = input.EqpNoClass;
        dict.EqpDisplayJson = input.EqpDisplayJson;
        dict.EqpNoApplys = input.EqpNoApplys;
        dict.EqpNoState = input.EqpNoState;
        dict.FirstRperson = user.HIS_NAME;
        dict.FirstRtime = DateTime.Now;
        dict.LastMperson = user.HIS_NAME;
        dict.LastMtime = DateTime.Now;
        dict.Remark = "";
        var newDict =  _dbContext.Db.GetSimpleClient<EMS_EQPNO_FORMAT_DICT>().InsertReturnEntity(dict);
        UpdateEquipmentUcode(dict.EqpNoId);
        return newDict.GetEquipmentCodeCustomDict();
    }

    public EquipmentCodeCustomDict UpdateEquipmentCodeCustomDict(UpdateEquipmentCodeCustomDictDto input)
    {
        
        var classesIds  = input.EqpNoClass.Split(";");
        var level= _dbContext.Db.Queryable<EMS_EQUIPMENT_CLASS_DICT>()
            .Where(x=>classesIds.Contains(x.ClassId))
            .OrderBy(x=>x.ClassLevel)
            .Select(x=>x.ClassLevel)
            .First();
        if (level.IsNullOrEmpty())
        {
            level = "0";
        }
        
        if (input.EqpNoId.IsNullOrEmpty())
        {
            var newCodeCustom = AddEquipmentCodeCustom(new AddEquipmentCodeCustomDictDto()
            {
                EqpNoName = input.EqpNoName,
                EqpNoLevel = level,
                EqpNoClass = input.EqpNoClass,
                EqpNoApplys = input.EqpNoApplys,
                DisplayContent = input.DisplayContent,
            });
            input.EqpNoId = newCodeCustom.EqpNoId;
        }
        var user = _httpContextAccessor.HttpContext.User.ToClaimsDto();
        var dict = _dbContext.Db.GetSimpleClient<EMS_EQPNO_FORMAT_DICT>().GetFirst(x => x.EqpNoId == input.EqpNoId);
        dict.EqpNoName = input.EqpNoName;
        dict.EqpNoLevel = level;
        dict.EqpNoClass = input.EqpNoClass;
        dict.EqpDisplayJson = input.GetEqpDisplayJson;
        dict.EqpNoApplys = input.EqpNoApplys;
        dict.LastMperson = user.HIS_NAME;
        dict.LastMtime = DateTime.Now;
        dict.Remark = "";
         _dbContext.Db.GetSimpleClient<EMS_EQPNO_FORMAT_DICT>().Update(dict);

         UpdateEquipmentUcode(input.EqpNoId);
        return dict.GetEquipmentCodeCustomDict();
    }


    /// <summary>
    /// 更新设备编码 - 根据配置模板批量更新设备的EQUIPMENT_UCODE
    /// </summary>
    /// <param name="eqpNoId">指定配置ID，为空时更新所有启用配置影响的设备</param>
    private void UpdateEquipmentUcode(string eqpNoId = null)
    {
        // 1. 获取启用状态的配置模板
        var configTemplates = _dbContext.Db.Queryable<EMS_EQPNO_FORMAT_DICT>()
            .WhereIF(eqpNoId.IsNotNullOrEmpty(), x => x.EqpNoId == eqpNoId)
            .Where(x => x.EqpNoState == "1")
            .ToList();

        if (!configTemplates.Any()) return;

        // 2. 收集所有受影响的专业组ID和设备类型ID
        var affectedUnitIds = new HashSet<string>();
        var templateClassIds = new HashSet<string>();

        foreach (var template in configTemplates)
        {
            var config = template.GetEquipmentCodeCustomDict();
            foreach (var unitId in config.EqpNoApplyList)
            {
                affectedUnitIds.Add(unitId);
            }

            // 收集模板中配置的设备类型
            if (config.EqpNoClass == "0")
            {
                templateClassIds.Add("0"); // 全部类型
            }
            else
            {
                templateClassIds.AddRange(config.EqpNoClassList);
            }
        }

        if (!affectedUnitIds.Any()) return;

        // 3. 获取所有受影响专业组的设备
        var equipments = _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>()
            .Where(x => affectedUnitIds.Contains(x.UNIT_ID))
            .ToList();

        // 4. 根据层级逻辑确定受影响的设备
        var affectedEquipments = GetAffectedEquipmentsByClassHierarchy(equipments, templateClassIds);

        // 5. 批量更新设备编码
        var updatedEquipments = new List<EMS_EQUIPMENT_INFO>();

        foreach (var equipment in affectedEquipments)
        {
            try
            {
                var newUcode = GenerateEquipmentUcode(equipment);

                // 只有编码发生变化时才更新
                if (equipment.EQUIPMENT_UCODE != newUcode)
                {
                    equipment.EQUIPMENT_UCODE = newUcode;
                    equipment.LAST_MTIME = DateTime.Now;
                    updatedEquipments.Add(equipment);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"更新设备 {equipment.EQUIPMENT_ID} 编码失败: {ex.Message}");
            }
        }

        // 6. 批量保存到数据库
        if (updatedEquipments.Any())
        {
            _dbContext.Db.Updateable(updatedEquipments)
                .UpdateColumns(x => new { x.EQUIPMENT_UCODE, x.LAST_MTIME })
                .ExecuteCommand();

            Console.WriteLine($"成功更新了 {updatedEquipments.Count} 个设备的编码");
        }
    }

    /// <summary>
    /// 根据设备类型层级逻辑确定受影响的设备
    /// </summary>
    /// <param name="equipments">候选设备列表</param>
    /// <param name="templateClassIds">模板中配置的设备类型ID集合</param>
    /// <returns>受影响的设备列表</returns>
    private List<EMS_EQUIPMENT_INFO> GetAffectedEquipmentsByClassHierarchy(List<EMS_EQUIPMENT_INFO> equipments, HashSet<string> templateClassIds)
    {
        var affectedEquipments = new List<EMS_EQUIPMENT_INFO>();

        // 如果模板包含"0"（全部类型），则所有设备都受影响
        if (templateClassIds.Contains("0"))
        {
            return equipments;
        }

        // 获取所有设备的类型信息，批量查询以提高性能
        var equipmentClassIds = equipments.Select(x => x.EQUIPMENT_CLASS).Distinct().ToList();
        var equipmentClasses = _dbContext.Db.Queryable<EMS_EQUIPMENT_CLASS_DICT>()
            .Where(x => equipmentClassIds.Contains(x.ClassId))
            .ToList();

        // 创建设备类型字典以便快速查找
        var classDict = equipmentClasses.ToDictionary(x => x.ClassId, x => x);

        foreach (var equipment in equipments)
        {
            if (IsEquipmentAffectedByTemplate(equipment, templateClassIds, classDict))
            {
                affectedEquipments.Add(equipment);
            }
        }

        return affectedEquipments;
    }

    /// <summary>
    /// 判断设备是否受模板影响
    /// </summary>
    /// <param name="equipment">设备信息</param>
    /// <param name="templateClassIds">模板中配置的设备类型ID集合</param>
    /// <param name="classDict">设备类型字典</param>
    /// <returns>是否受影响</returns>
    private bool IsEquipmentAffectedByTemplate(EMS_EQUIPMENT_INFO equipment, HashSet<string> templateClassIds, Dictionary<string, EMS_EQUIPMENT_CLASS_DICT> classDict)
    {
        // 1. 直接匹配设备类型
        if (templateClassIds.Contains(equipment.EQUIPMENT_CLASS))
        {
            return true;
        }

        // 2. 如果设备类型不在字典中，只能直接匹配
        if (!classDict.TryGetValue(equipment.EQUIPMENT_CLASS, out var equipmentClass))
        {
            return false;
        }

        // 3. 根据设备类型层级进行匹配
        if (equipmentClass.ClassLevel == "1")
        {
            // 1级设备类型：只匹配自身
            return templateClassIds.Contains(equipment.EQUIPMENT_CLASS);
        }
        if (equipmentClass.ClassLevel == "2")
        {
            // 2级设备类型：匹配自身或父级
            if (templateClassIds.Contains(equipment.EQUIPMENT_CLASS))
            {
                return true;
            }

            // 检查是否匹配父级类型
            if (!string.IsNullOrEmpty(equipmentClass.ParentClassId) &&
                equipmentClass.ParentClassId != "0" &&
                templateClassIds.Contains(equipmentClass.ParentClassId))
            {
                return true;
            }
        }
        else
        {
            // 其他层级：只匹配自身
            return templateClassIds.Contains(equipment.EQUIPMENT_CLASS);
        }

        return false;
    }

    /// <summary>
    /// 为单个设备生成编码 - 统一的编码生成逻辑
    /// </summary>
    /// <param name="equipment">设备信息</param>
    /// <returns>生成的设备编码</returns>
    private string GenerateEquipmentUcode(EMS_EQUIPMENT_INFO equipment)
    {
        // 获取该设备的最高优先级配置
        var config = GetEquipmentCodeCustomDictByEquipment(equipment);

        if (config != null)
        {
            // 使用配置模板生成编码
            var generatedCode = ExchangeEquipmentUCode(config.DisplayContent, equipment);
            if (!generatedCode.IsNullOrEmpty())
            {
                return generatedCode;
            }
        }

        // 没有配置或生成失败时，使用默认逻辑
        return equipment.EQUIPMENT_UCODE.IsNullOrEmpty()
            ? equipment.EQUIPMENT_CODE ?? ""
            : equipment.EQUIPMENT_UCODE;
    }

    public void DeleteEquipmentCodeCustomDict(string EqpNoId)
    {
        var user  = _httpContextAccessor.HttpContext.User.ToClaimsDto();
        _dbContext.Db.Updateable<EMS_EQPNO_FORMAT_DICT>().SetColumns(p => new EMS_EQPNO_FORMAT_DICT()
        {
            EqpNoState ="2",
            LastMtime = DateTime.Now,
            LastMperson = user.HIS_NAME
        }).Where(p => p.EqpNoId == EqpNoId).ExecuteCommand();
    }

    public void EnableOrDisableEquipmentCodeCustomDict(string EqpNoId)
    {
        var user  = _httpContextAccessor.HttpContext.User.ToClaimsDto();
        var  state =  _dbContext.Db.Queryable<EMS_EQPNO_FORMAT_DICT>()
            .Where(p => p.EqpNoId == EqpNoId)
            .Select(x => x.EqpNoState)
            .First();
        if (state.IsNullOrEmpty())
        {
            return;
        }
        _dbContext.Db.Updateable<EMS_EQPNO_FORMAT_DICT>().SetColumns(p => new EMS_EQPNO_FORMAT_DICT()
        {
            EqpNoState = state == "1" ? "0" : "1",
            LastMtime = DateTime.Now,
            LastMperson = user.HIS_NAME
        }).Where(p => p.EqpNoId == EqpNoId).ExecuteCommand();
    }

    private EMS_EQUIPMENT_INFO FillEquipmentInfo(EMS_EQUIPMENT_INFO equipment)
    {
        equipment!.HOSPITAL_NAME = _equipmentContext.ExchangeHosptailName(equipment.HOSPITAL_ID);
        equipment.LAB_NAME = _equipmentContext.ExchangeLabName(equipment.LAB_ID);
        equipment.UNIT_NAME = _equipmentContext.ExchangeProfessionalGroupName(equipment.UNIT_ID);
        equipment.MGROUP_NAME = _equipmentContext.ExChangeMGroupByPGroupId(equipment.UNIT_ID);
        equipment.AREA_NAME = _equipmentContext.ExchangeAreaNameByPGroupId(equipment.UNIT_ID);
        if (equipment.VEST_PIPELINE.IsNotNullOrEmpty())
        {
           var pipEquipment =  _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>().First(x => x.EQUIPMENT_ID == equipment.VEST_PIPELINE);
           equipment.PIPELINE_NAME = pipEquipment.EQUIPMENT_UCODE;
        }
        if (_configuration.GetSection("IsSmbl").Value == "1")
        {
            equipment.SMBL_LAB_NAME = _equipmentContext.ExchangeSmblLabName(equipment.SMBL_LAB_ID);
            equipment.SMBL_CLASS_NAME = _equipmentContext.ExchangeEquipmentSmblClass(equipment.SMBL_CLASS);
        }
        return equipment;
    }

    public EMS_EQUIPMENT_INFO GetEquipmentInfo(string eqpNoClass)
    {
        var equipment = _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>()
            .WhereIF(eqpNoClass != "0", x => x.EQUIPMENT_CLASS == eqpNoClass)
            .First();
        equipment!.HOSPITAL_NAME = _equipmentContext.ExchangeHosptailName(equipment.HOSPITAL_ID);
        equipment.LAB_NAME = _equipmentContext.ExchangeLabName(equipment.LAB_ID);
        equipment.UNIT_NAME = _equipmentContext.ExchangeProfessionalGroupName(equipment.UNIT_ID);
        equipment.MGROUP_NAME = _equipmentContext.ExChangeMGroupByPGroupId(equipment.UNIT_ID);
        equipment.AREA_NAME = _equipmentContext.ExchangeAreaNameByPGroupId(equipment.UNIT_ID);
        try
        {
            if (_configuration.GetSection("IsSmbl").Value == "1")
            {
                equipment.SMBL_LAB_NAME = _equipmentContext.ExchangeSmblLabName(equipment.SMBL_LAB_ID);
                equipment.SMBL_CLASS_NAME = _equipmentContext.ExchangeEquipmentSmblClass(equipment.SMBL_CLASS);
            }
        }
        catch (Exception e)
        {
            Log.Error($"当前机构不支持生安版本！{e}");
        }
        return equipment;
    }
    private EMS_EQUIPMENT_INFO GetEquipmentInfoByCache(string eqpNoClass)
    {
        if (_memoryCache.TryGetValue<EMS_EQUIPMENT_INFO>($"{CACHE_KEY}:{eqpNoClass}", out var equipment))
        {
            return equipment;
        }
        equipment = _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>()
            .WhereIF(eqpNoClass != "0", x => x.EQUIPMENT_CLASS == eqpNoClass)
            .First();
        if (equipment is null)
        {
            equipment = _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>()
                .First();
        }
        equipment!.HOSPITAL_NAME = _equipmentContext.ExchangeHosptailName(equipment.HOSPITAL_ID);
        equipment.LAB_NAME = _equipmentContext.ExchangeLabName(equipment.LAB_ID);
        equipment.UNIT_NAME = _equipmentContext.ExchangeProfessionalGroupName(equipment.UNIT_ID);
        equipment.MGROUP_NAME = _equipmentContext.ExChangeMGroupByPGroupId(equipment.UNIT_ID);
        equipment.AREA_NAME = _equipmentContext.ExchangeAreaNameByPGroupId(equipment.UNIT_ID);
        try
        {
            if (_configuration.GetSection("IsSmbl").Value == "1")
            {
                equipment.SMBL_LAB_NAME = _equipmentContext.ExchangeSmblLabName(equipment.SMBL_LAB_ID);
                equipment.SMBL_CLASS_NAME = _equipmentContext.ExchangeEquipmentSmblClass(equipment.SMBL_CLASS);
            }
        }
        catch (Exception e)
        {
            Log.Error($"当前机构不支持生安版本！{e}");
        }
        _memoryCache.Set($"{CACHE_KEY}:{eqpNoClass}", equipment , TimeSpan.FromHours(1));
        return equipment;
    }
}