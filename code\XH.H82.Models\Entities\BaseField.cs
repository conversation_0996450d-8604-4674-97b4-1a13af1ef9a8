﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.Entities
{
    /// <summary>
    /// 基础的几个字段
    /// </summary>
    public class BaseField
    {
        public virtual string FIRST_RPERSON { get; set; }

        public virtual DateTime? FIRST_RTIME { get; set; }

        public virtual string LAST_MPERSON { get; set; }

        public virtual DateTime? LAST_MTIME { get; set; }

        public virtual string REMARK { get; set; }
    }
}
