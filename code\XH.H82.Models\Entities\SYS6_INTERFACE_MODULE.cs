using System.ComponentModel.DataAnnotations;
using H.Utility.SqlSugarInfra;
using Npoi.Mapper.Attributes;
using SqlSugar;

namespace XH.H82.Models.Entities;

[DBOwner("XH_SYS")]

public class SYS6_INTERFACE_MODULE
{

        [SugarColumn(IsPrimaryKey = true)]
        [Key]
        [Column("INTERFACE_M_ID")]
        [Required(ErrorMessage = "INTERFACE_M_ID不允许为空")]

        public string INTERFACE_M_ID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("INTERFACE_ID")]
        [Required(ErrorMessage = "INTERFACE_ID不允许为空")]

        public string INTERFACE_ID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("MODULE_ID")]
        [Required(ErrorMessage = "MODULE_ID不允许为空")]

        public string MODULE_ID { get; set; }

        ///// <summary>
        ///// 
        ///// </summary>
        //[Column("APP_ID")]

        //public string? APP_ID { get; set; }

        ///// <summary>
        ///// 
        ///// </summary>
        //[Column("APP_KEY")]

        //public string? APP_KEY { get; set; }

        /// <summary>
        /// 应用APP_SECRETY
        /// </summary>
        [Column("APP_SECRET")]
        public string? APP_SECRET { get; set; }

        /// <summary>
        /// 应用APP_KEY
        /// </summary>
        [Column("APP_KEY")]
        public string? APP_KEY { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("MODULE_STATE")]

        public string? MODULE_STATE { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("FIRST_RPERSON")]

        public string? FIRST_RPERSON { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("FIRST_RTIME")]

        public DateTime? FIRST_RTIME { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("LAST_MPERSON")]

        public string? LAST_MPERSON { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("LAST_MTIME")]

        public DateTime? LAST_MTIME { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("REMARK")]

        public string? REMARK { get; set; }

        [Column("TIMESTAMP_LIMIT")]
        public int? TIMESTAMP_LIMIT { get; set; } = 900;
}