namespace XH.H82.Models.Dtos.AuthorizationRecord;

/// <summary>
/// 授权记录添加input
/// </summary>
public class AuthorizationRecordDto
{
    /// <summary>
    /// 授权人ID
    /// </summary>
    public string AUTHORIZE_PERSON_ID { get; set; }

    /// <summary>
    /// 被授权人
    /// </summary>
    public string AUTHORIZED_PERSON { get; set; }
    /// <summary>
    /// 被授权人ids ;分割
    /// </summary>
    public string AUTHORIZED_PERSON_IDS { get; set; }
    /// <summary>
    /// 授权权限
    /// </summary>
    public string AUTHORIZED_ROLE { get; set; }
    /// <summary>
    /// 授权时间
    /// </summary>
    public DateTime? AUTHORIZE_DATE { get; set; }
}


