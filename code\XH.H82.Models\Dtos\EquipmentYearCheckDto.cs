﻿namespace XH.H82.Models.Dtos;

public class EquipmentYearCheckDto
{
    public int No { get; set; }
    /// <summary>
    /// 设备 id
    /// </summary>
    public string Id { get; set; }

    /// <summary>
    /// 设备名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 实验室名称
    /// </summary>
    /// <returns></returns>
    public string SmblLabName { get; set; }
    /// <summary>
    /// 生物安全设备类型
    /// </summary>
    public string SmblClass { get; set; }
    /// <summary>
    /// 上次年检时间
    /// </summary>

    public DateTime LastYearCheckTime { get; set; }
    /// <summary>
    /// 下次年检时间
    /// </summary>
    public DateTime NextYearCheckTime { get; set; }
}



public class ConutEquipmentYearChectByMonth
{
    public string Month { get; set; }
    public int Conut { get; set; }
    public List<EquipmentYearCheckDto> List { get; set; } = new List<EquipmentYearCheckDto>();
}

public class EquipmentYearCheckCountDto
{
    /// <summary>
    /// 年份
    /// </summary>
    public int Year { get; set; }

    /// <summary>
    ///  数目
    /// </summary>
    public int Count { get; set; }
}