﻿using H.Utility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Microsoft.FSharp.Core;
using NPOI.Util;
using XH.H82.IServices;

namespace XH.H82.API.Controllers._Demos
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize]
    [ApiExplorerSettings(GroupName = "DEMO")]
    public class BaseDataDemoController : ControllerBase
    {
        private readonly IBaseDataServices _basedataService;

        public BaseDataDemoController(IBaseDataServices basedataService)
        {
            _basedataService = basedataService;

        }

        /// <summary>
        /// 固定基础数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetBaseData()
        {
            var res = _basedataService.GetLis5BaseData("图片分类,单据分类", "LIS", "", "", EnumBaseDataGetType.缓存数据);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        /// 组织机构
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetOrganizeData()
        {
            var res = _basedataService.GetOrganizeData("33A001", EnumBaseDataGetType.缓存数据);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        /// 菜单按钮
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetMenuButtonApi()
        {
            var res = _basedataService.GetMenuButtonApi("B32", EnumBaseDataGetType.缓存数据);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        /// 获取用户
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult AccountInfo()
        {
            var res = _basedataService.AccountInfo("33A001", EnumBaseDataGetType.缓存数据);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        /// 获取系统设置
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetLis5SetupDicts()
        {
            var res = _basedataService.GetLis5SetupDicts("B32", "", "33A001", "医疗机构", "", "", EnumBaseDataGetType.缓存数据);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        /// 获取最大值
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetTableMax()
        {
            var res = _basedataService.GetTableMax("LIS5_BANK_BOX_LIST", "ID", 1, 1);
            return Ok(res);
        }

        /// <summary>
        /// 单个菜单导出样式控制器 无功能
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ExportInfo(MenuId = "M0001", ApiName = "测试api")]
        public IActionResult ApiExportTest()
        {
            return Ok();
        }
        /// <summary>
        /// 多个菜单导出样式控制器 无功能
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ExportInfo(MenuId = "M0001,M0002,M0003", ApiName = "测试api2")]
        public IActionResult ApiExportTest2()
        {
            return Ok();
        }
    }
}
