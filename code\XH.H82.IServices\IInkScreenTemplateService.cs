﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XH.H82.Models.Card;
using XH.H82.Models.Entities.InkScreen;
using XH.H82.Models.InkScreenTemplate;
using XH.H82.Models.InkScreenTemplate.Dto;

namespace XH.H82.IServices
{
    public interface IInkScreenTemplateService
    {
        /// <summary>
        /// 复制模板
        /// </summary>
        /// <param name="templateId"></param>
        void CopyTemplate(string templateId);

        /// <summary>
        /// 添加模板
        /// </summary>
        void AddTemplate(string TemplateName, string reamrk);

        /// <summary>
        /// 修改模板名称、备注
        /// </summary>
        void UpdateTemplate(string templateId, string TemplateName, string? reamrk);

        /// <summary>
        /// 删除模板信息
        /// </summary>
        void DeleteTemplate(string templateId);

        /// <summary>
        /// 返回模板信息
        /// </summary>
        /// <returns></returns>
        List<EMS_INKSCREEN_TEMPLATE> GetTemplates(string labId, string? groupId, string? name);

        /// <summary>
        /// 获取模板基础属性
        /// </summary>
        /// <param name="TemplateName"></param>
        /// <returns></returns>
        List<TemplateAttribute> GetAllTemplateAttributes();

        /// <summary>
        /// 保存模板内容信息信息
        /// </summary>
        /// <param name="templateId"></param>
        /// <param name="tempContent"></param>
        /// <param name="tempTitle"></param>
        /// <param name="setAbnormal"></param>
        /// <param name="setQRCode"></param>
        /// <param name="SetWireframe"></param>
        void SaveTemplate(string templateId, string tempContent, string tempTitle, bool setAbnormal, bool setQRCode, bool SetWireframe);

        /// <summary>
        /// 墨水屏模板应用到专业组
        /// </summary>
        /// <param name="templateId"></param>
        /// <param name="groups"></param>
        void ApplicationGroups(string templateId, List<string> groups);
        /// <summary>
        /// 获取模板预览信息
        /// </summary>
        /// <param name="templateId"></param>
        /// <param name="parameters"></param>
        /// <returns></returns>
        IdentificationCard GetTemplatePreview(string templateId, string parameters);


        /// <summary>
        /// 直接设置墨水屏展示值
        /// </summary>
        /// <param name="templateDataAndValues">基础属性与值</param>
        /// <param name="nextMaintainDateStatus">下次保养时间是否添加红色边框</param>
        /// <param name="nextCorrectDateStatus">下次校准时间是否添加红色边框</param>
        /// <param name="circumstance">设备运行状态：启用，停用，报废，未启用</param>
        /// <param name="meassage">异常信息</param>
        /// <returns></returns>
        IdentificationCard SetTemplateValue(List<TemplateDataAndValue> templateDataAndValues,bool nextMaintainDateStatus = false, bool nextCorrectDateStatus = false, string circumstance  = "启用" ,string meassage = "");
    }
}
