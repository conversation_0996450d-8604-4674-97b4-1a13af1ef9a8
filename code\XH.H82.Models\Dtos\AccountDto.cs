﻿namespace XH.H82.Models.Dtos;

public class AccountDto
{
    public string AccessToken { get; set; }

    public string RefreshToken { get; set; }

    public string UserNo { get; set; }

    public string UserName { get; set; }

    public ToKenDto USER { get; set; }
}



public class ToKenDto
{
    public string DISPLAY_NAME { get; set; }
    public string EXTRA_INFO { get; set; }
    public string HOSPITAL_CNAME { get; set; }
    public string HOSPITAL_ID { get; set; }
    public string INSTANCE_ID { get; set; }
    public string LAB_ID { get; set; }
    public string LOGID { get; set; }
    public string MANAGE_CLASS { get; set; }
    public string MGROUP_ID { get; set; }
    public string MODULE_ID { get; set; }
    public string PHONE_NO { get; set; }
    public string POWER { get; set; }
    public string ROLE_LIST { get; set; }
    public string TECH_POST { get; set; }
    public string TOKENGUID { get; set; }
    public string USER_NAME { get; set; }
    public string USER_NO { get; set; }
    public string PERSON_ID { get; set; }
    public string OPERATE_PERSON { get; set; }
}