﻿namespace XH.H82.Models.Dtos
{
    /// <summary>
    /// token 解析后的对象
    /// </summary>
    public class ClaimsDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public string USER_NO { get; set; }
        /// <summary>
        /// 实例ID
        /// </summary>
        public string INSTANCE_ID { get; set; }
        /// <summary>
        /// 机构ID
        /// </summary>
        public string HOSPITAL_ID { get; set; }
        /// <summary>
        /// 机构名称
        /// </summary>
        public string HOSPITAL_CNAME { get; set; }
        /// <summary>
        /// 科室ID
        /// </summary>
        public string LAB_ID { get; set; }
        /// <summary>
        /// 专业组ID
        /// </summary>
        public string MGROUP_ID { get; set; }
        /// <summary>
        /// 用户名
        /// </summary>
        public string LOGID { get; set; }
        /// <summary>
        /// 用户名称
        /// </summary>
        public string USER_NAME { get; set; }
        /// <summary>
        /// 用户类型
        /// </summary>
        public string MANAGE_CLASS { get; set; }
        /// <summary>
        /// 职称
        /// </summary>
        public string TECH_POST { get; set; }
        /// <summary>
        /// 职务
        /// </summary>
        public string POWER { get; set; }
        /// <summary>
        /// 手机
        /// </summary>
        public string PHONE_NO { get; set; }
        /// <summary>
        /// 操作人
        /// </summary>
        public string DISPLAY_NAME { get; set; }
        /// <summary>
        /// 请求guid
        /// </summary>
        public string TOKENGUID { get; set; }
    }
}