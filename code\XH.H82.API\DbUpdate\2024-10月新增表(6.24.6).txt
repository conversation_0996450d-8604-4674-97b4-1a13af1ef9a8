CREATE TABLE XH_OA.EMS_IMPLEMENT_INFO(
    IMPLEMENT_ID VARCHAR2(50) NOT NULL,
    HOSPITAL_ID VARCHAR2(50) NOT NULL,
    EQUIPMENT_ID VARCHAR2(50) NOT NULL,
    IMPLEMENT_CYCLE VARCHAR2(20),
    IMPLEMENT_PERSON VARCHAR2(50),
    IMPLEMENT_DATA DATE,
    IMPLEMENT_CONTEXT VARCHAR2(1000),
    IMPLEMENT_STATE VARCHAR2(10)   DEFAULT '1' NOT NULL,
    FIRST_RPERSON VARCHAR2(50) ,
    FIRST_RTIME DATE,
    LAST_MPERSON VARCHAR2(50),
    LAST_MTIME DATE,
    REMARK VARCHAR2(200)
);

ALTER TABLE XH_OA.EMS_IMPLEMENT_INFO ADD CONSTRAINT PK_OA_EMS_IMPLEMENT_INFO PRIMARY KEY (IMPLEMENT_ID);

COMMENT ON TABLE XH_OA.EMS_IMPLEMENT_INFO IS '设备事务使用记录';
COMMENT ON COLUMN XH_OA.EMS_IMPLEMENT_INFO.IMPLEMENT_ID IS 'PK';
COMMENT ON COLUMN XH_OA.EMS_IMPLEMENT_INFO.HOSPITAL_ID IS '医疗机构ID';
COMMENT ON COLUMN XH_OA.EMS_IMPLEMENT_INFO.EQUIPMENT_ID IS '设备ID';
COMMENT ON COLUMN XH_OA.EMS_IMPLEMENT_INFO.IMPLEMENT_CYCLE IS '使用周期;0 日/次  1月';
COMMENT ON COLUMN XH_OA.EMS_IMPLEMENT_INFO.IMPLEMENT_PERSON IS '登记人员';
COMMENT ON COLUMN XH_OA.EMS_IMPLEMENT_INFO.IMPLEMENT_DATA IS '使用时间';
COMMENT ON COLUMN XH_OA.EMS_IMPLEMENT_INFO.IMPLEMENT_CONTEXT IS '登记内容';
COMMENT ON COLUMN XH_OA.EMS_IMPLEMENT_INFO.IMPLEMENT_STATE IS '状态;0禁用 1在用 2删除 默认值1 ';
COMMENT ON COLUMN XH_OA.EMS_IMPLEMENT_INFO.FIRST_RPERSON IS '首次操作人';
COMMENT ON COLUMN XH_OA.EMS_IMPLEMENT_INFO.FIRST_RTIME IS '首次创建时间';
COMMENT ON COLUMN XH_OA.EMS_IMPLEMENT_INFO.LAST_MPERSON IS '最后一次操作人';
COMMENT ON COLUMN XH_OA.EMS_IMPLEMENT_INFO.LAST_MTIME IS '最后一次操作时间';
COMMENT ON COLUMN XH_OA.EMS_IMPLEMENT_INFO.REMARK IS '备注';

GRANT SELECT, INSERT, UPDATE, DELETE ON XH_OA.EMS_IMPLEMENT_INFO TO XH_COM;