﻿<?xml version="1.0" encoding="utf-8" ?>
<root>
	<取预约号>
		<OperType>SP</OperType>
		<MSSQL>
			<SPName>USP_LIS5_NOPM_TAKE_NUMBER</SPName>
		</MSSQL>
	</取预约号>
	<更新叫号状态>
		<OperType>SelectWithCondation</OperType>
		<MSSQL>
			<SQL>
				UPDATE LIS5_QUEUE_LIST SET CALL_STATE='0' WHERE 1=1
			</SQL>
		</MSSQL>
	</更新叫号状态>
	<获取采血预约系统设置>
		<OperType>Select</OperType>
		<MSSQL>
			<SQL>
				SELECT *
				FROM dbo.UF_GET_SETUP_INFO(@HOSPITAL_ID,@UNIT_ID,'标本采集','A20')
				WHERE SETUP_NO IN('A20401')
			</SQL>
		</MSSQL>
		<Oracle>
			<SQL>
				SELECT *
				FROM TABLE(UF_GET_SETUP_INFO(:HOSPITAL_ID,:UNIT_ID,'标本采集','A20'))
				WHERE SETUP_NO IN('A20401')
			</SQL>
		</Oracle>
	</获取采血预约系统设置>
</root>