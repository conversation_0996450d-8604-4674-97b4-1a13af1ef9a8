[
	{
		"CardTypeName": "标准版",
		"CardTypeKey": "StandardCard_preview",
		"IsShow": "1"
	},
	{
		"CardTypeName": "二维码版",
		"CardTypeKey": "QRcodeCard_preview",
		"IsShow": "1"
	},
	{
		"CardTypeName": "自动状态标识卡", //显示在界面中的名称
		"CardTypeKey": "StausCard_preview_auto", //必须与模板名称同名
		"IsShow": "1" // 0-隐藏  1-显示
	},
	{
		"CardTypeName": "运行状态标识卡",
		"CardTypeKey": "StatusCard_preview_active",
		"IsShow": "1"
	},
	{
		"CardTypeName": "停用状态标识卡",
		"CardTypeKey": "StatusCard_preview_stop",
		"IsShow": "1"
	},
	{
		"CardTypeName": "禁用状态标识卡",
		"CardTypeKey": "StatusCard_preview_ban",
		"IsShow": "1"
	},
	{
		"CardTypeName": "报废状态标识卡",
		"CardTypeKey": "StatusCard_preview_scrap",
		"IsShow": "1"
	}

]