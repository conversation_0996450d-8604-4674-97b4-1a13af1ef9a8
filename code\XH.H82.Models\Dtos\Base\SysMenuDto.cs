﻿using System;
using System.Collections.Generic;
using System.Text;

namespace XH.H82.Models.Dtos.Base
{
    public class SysMenuDto
    {
        public string MENU_NO { get; set; }
        public string HOSPITAL_ID { get; set; }
        public string MENU_CLASS_ID { get; set; }
        public string MENU_SORT { get; set; }
        public string MENU_CODE { get; set; }
        public string SYS_MENU { get; set; }
        public string MENU_LEVEL { get; set; }
        public string PARENT_CODE { get; set; }
        public string MENU_CLASS { get; set; }
        public string MENU_NAME { get; set; }
        public string MENU_ENAME { get; set; }
        public string MENU_URL { get; set; }
        public string MENU_CNAME { get; set; }
        public string SHORTCUT_KEY { get; set; }
        public string MENU_ICON { get; set; }
        public string REMARK { get; set; }

        public string BACKGROUND_COLOR { get; set; }
        public string FONT_COLOR { get; set; }
        public string IF_REGISTER { get; set; }//是否注册
        public string EXTERNAL_LINK { get; set; }//外部连接
    }
}
