﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Security.Cryptography;
using System.Threading.Channels;
using XH.H82.Models.ViewDtos;
using SqlSugar;
using H.Utility.SqlSugarInfra;

namespace XH.H82.Models.Entities.Common
{
    [DBOwner("XH_SYS")]
    [SugarTable("LIS6_INSTRUMENT_INFO")]
    public class LIS6_INSTRUMENT_INFO
    {
        /// <summary>
        /// 仪器ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string INSTRUMENT_ID { get; set; }
        /// <summary>
        /// 医疗机构ID
        /// </summary>
        public string HOSPITAL_ID { get; set; }
        /// <summary>
        /// 科室ID
        /// </summary>
        public string LAB_ID { get; set; }
        /// <summary>
        /// 设备型号ID
        /// </summary>
        public string SINSTRUMENT_ID { get; set; }
        /// <summary>
        /// 设备名称
        /// </summary>
        public string INSTRUMENT_NAME { get; set; }
        /// <summary>
        /// 仪器序列号
        /// </summary>
        public string INSTRUMENT_SNUM { get; set; }
        /// <summary>
        /// 注册码
        /// </summary>
        public string INSTRUMENT_REGISTER { get; set; }
        /// <summary>
        /// 设备位置
        /// </summary>
        public string INSTRUMENT_POSITION { get; set; }
        /// <summary>
        /// 生产厂家
        /// </summary>
        public string INSTRUMENT_FACTORY { get; set; }
        /// <summary>
        /// 仪器状态（0：未启用；1：在用；2：停用；3：报废）
        /// </summary>
        public string INSTRUMENT_STATE { get; set; }
        /// <summary>
        /// 首次登记人
        /// </summary>
        public string FIRST_RPERSON { get; set; }
        /// <summary>
        /// 首次登记时间
        /// </summary>
        public DateTime? FIRST_RTIME { get; set; }
        /// <summary>
        /// 最后登记人
        /// </summary>
        public string LAST_MPERSON { get; set; }
        /// <summary>
        /// 最后登记时间
        /// </summary>
        public DateTime? LAST_MTIME { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string REMARK { get; set; }
        /// <summary>
        /// 所属检验单元
        /// </summary>
        public string GROUP_ID { get; set; }
        /// <summary>
        /// 设备类型（1：检测仪器；2：流水线；3：虚拟手工）
        /// </summary>
        public string INSTRUMENT_TYPE { get; set; }
        /// <summary>
        /// 通讯方式（1：单向；2：双向；3：批量发送）
        /// </summary>
        public string COMM_MODE { get; set; }
        /// <summary>
        /// 仪器图片比例
        /// </summary>
        public string GRAPH_TYPE { get; set; }
        /// <summary>
        /// 自定义名称
        /// </summary>
        public string INSTRUMENT_CNAME { get; set; }
        /// <summary>
        /// 关联标本接收单元
        /// </summary>
        public string IUNIT_ID { get; set; }
        /// <summary>
        /// 编号时机
        /// </summary>
        public string NUMBERING_MODE { get; set; }
        /// <summary>
        /// 计算机名称ip
        /// </summary>
        public string CLIENT_ID { get; set; }
        /// <summary>
        /// 信息发送时机
        /// </summary>
        public string ORDER_SEND_STAGE { get; set; }
        /// <summary>
        /// 设备ID
        /// </summary>
        public string EQUIPMENT_ID { get; set; }
        /// <summary>
        /// 实例ID
        /// </summary>
        public string INSTANCE_ID { get; set; }

    }
}
