using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using H.Utility.SqlSugarInfra;
using Microsoft.EntityFrameworkCore;
using SqlSugar;

namespace XH.H82.Models.Entities.Tim
{
    [DBOwner("XH_OA")]
    public class TIM_FORM_MAIN_INFO
	{
		/// <summary>
		/// 记录单主体ID
		/// </summary>
		[Key]
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        [Column("FORM_MAIN_ID")]
		[Required(ErrorMessage = "记录单主体ID不允许为空")]

		[StringLength(50, ErrorMessage = "记录单主体ID长度不能超出50字符")]
		[Unicode(false)]

		public string FORM_MAIN_ID { get; set; }

		/// <summary>
		/// 首次登记人
		/// </summary>
		[Column("FIRST_RPERSON")]
		[StringLength(50, ErrorMessage = "首次登记人长度不能超出50字符")]
		[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FIRST_RPERSON { get; set; }

		/// <summary>
		/// 最后修改时间
		/// </summary>
		[Column("LAST_MTIME")]
		[Unicode(false)]
		public DateTime? LAST_MTIME { get; set; }

		/// <summary>
		/// 事务主体ID
		/// </summary>
		[Column("WORK_MAINID")]
		[Required(ErrorMessage = "事务主体ID不允许为空")]

		[StringLength(50, ErrorMessage = "事务主体ID长度不能超出50字符")]
		[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string WORK_MAINID { get; set; }

		/// <summary>
		/// 排序号
		/// </summary>
		[Column("MAIN_SORT")]
		[StringLength(20, ErrorMessage = "排序号长度不能超出20字符")]
		[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? MAIN_SORT { get; set; }

		/// <summary>
		/// 状态
		/// </summary>
		[Column("MAIN_STATE")]
		[StringLength(20, ErrorMessage = "状态长度不能超出20字符")]
		[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? MAIN_STATE { get; set; }

		/// <summary>
		/// 记录单ID
		/// </summary>
		[Column("FORM_ID")]
		[Required(ErrorMessage = "记录单ID不允许为空")]

		[StringLength(20, ErrorMessage = "记录单ID长度不能超出20字符")]
		[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string FORM_ID { get; set; }

		/// <summary>
		/// 备注
		/// </summary>
		[Column("REMARK")]
		[StringLength(200, ErrorMessage = "备注长度不能超出200字符")]
		[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REMARK { get; set; }

		/// <summary>
		/// 首次登记时间
		/// </summary>
		[Column("FIRST_RTIME")]
		[Unicode(false)]
		public DateTime? FIRST_RTIME { get; set; }

		/// <summary>
		/// 最后修改人员
		/// </summary>
		[Column("LAST_MPERSON")]
		[StringLength(50, ErrorMessage = "最后修改人员长度不能超出50字符")]
		[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? LAST_MPERSON { get; set; }

		/// <summary>
		/// 医疗机构ID
		/// </summary>
		[Column("HOSPITAL_ID")]
		[Required(ErrorMessage = "医疗机构ID不允许为空")]

		[StringLength(50, ErrorMessage = "医疗机构ID长度不能超出50字符")]
		[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string HOSPITAL_ID { get; set; }


	}
}
