﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XH.LAB.UTILS.Helpers;

namespace XH.H82.Models.Dtos.Tree
{
    public class OrganizationalTreeNode
    {
        /// <summary>
        /// 数据主键
        /// </summary>
        public string NodeId { get; set; }
        /// <summary>
        /// 节点名称
        /// </summary>
        public string NodeName { get; set; }
        /// <summary>
        /// 节点状态
        /// </summary>
        public string NodeState { get; set; }
        /// <summary>
        /// 节点状态名称
        /// </summary>
        public string NodeStateName { get; set; }
        /// <summary>
        /// 节点类型
        /// </summary>
        public OrganizationalTreeNodeEnum NodeType { get; set; }

        /// <summary>
        /// 节点类型中文
        /// </summary>
        public string NodeTypeName { get; set; }

        public List<OrganizationalTreeNode> ChildrenNode { get; set; } = new List<OrganizationalTreeNode>();

        public bool isRoot { get; set; }

        public bool isLeaves
        {

            get
            {
                if (ChildrenNode.Count > 0)
                {
                    return false;
                }
                else
                {
                    return true;
                }

            }
        }


        public void NodeStateTracking() {

            foreach (var node in ChildrenNode)
            {
                if (!node.isLeaves)
                {
                    node.NodeStateTracking();
                }
            }
            if (ChildrenNode.Any(x => x.NodeState == "1"))
            {
                NodeState = "1";
                NodeStateName = "已应用";
            }
        }


        public static OrganizationalTreeNode GetRootNode(string NodeId, string NodeName)
        {

            return new OrganizationalTreeNode()
            {
                NodeId = NodeId,
                NodeName = NodeName,
                NodeType = OrganizationalTreeNodeEnum.Root,
                NodeState = "",
                NodeStateName = "根节点无状态",
                NodeTypeName = OrganizationalTreeNodeEnum.Root.ToDesc(),
                isRoot = true,
            };
        }
    }





    public enum OrganizationalTreeNodeEnum
    {
        [Description("根节点")]
        Root,
        [Description("院区")]
        Hospital,
        [Description("科室")]
        Lab,
        [Description("管理专业组")]
        Mgroup,
        [Description("检验专业组")]
        Pgroup,
    }

}
