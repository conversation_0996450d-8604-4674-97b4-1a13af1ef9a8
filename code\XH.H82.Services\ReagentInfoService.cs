﻿using H.BASE.SqlSugarInfra.Uow;
using H.IRepository;
using H.Utility;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Channels;
using System.Threading.Tasks;
using XH.H82.IServices;
using XH.H82.Models;
using XH.H82.Models.Entities;
using XH.H82.Models.Entities.Common;
using XH.H82.Models.SugarDbContext;

namespace XH.H82.Services
{
    public class ReagentInfoService :IReagentInfoService
    {
        private readonly ILogger<ReagentInfoService> _logger;
        private readonly ISqlSugarUow<SugarDbContext_Master> _sqlSugarUow;
        public ReagentInfoService(ILogger<ReagentInfoService> logger,ISqlSugarUow<SugarDbContext_Master> sqlSugarUow)
        {
            _logger = logger;
            _sqlSugarUow = sqlSugarUow;
            //ExecutingChangeSqlHelper.ExecutingChangeSql(_sqlSugarUow);
        }
        public List<LIS6_INSTRUMENT_ITEM> GetInstrumentItemList(string equipmentId)
        {
            try
            {
                var list = _sqlSugarUow.Db.Queryable<LIS6_INSTRUMENT_INFO>()
                    .InnerJoin<LIS6_INSTRUMENT_ITEM>((a, b) => a.INSTRUMENT_ID == b.INSTRUMENT_ID)
                    .Where((a, b) => a.EQUIPMENT_ID == equipmentId && b.CHANNEL_STATE == "1")
                    .Select((a, b) => b)
                    .ToList();
                return list.DistinctBy(a => a.ITEM_ID).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError("项目信息查询失败:\n" + ex.Message);
                throw new KeyNotFoundException($"项目信息查询失败");
            }
        }
        /// <summary>
        /// 获取试剂信息
        /// </summary>
        /// <param name="itemId">项目ID</param>
        /// <param name="channelId">通道ID</param>
        /// <param name="reagentState">1：在用；0：禁用</param>
        /// <returns></returns>
        /// <exception cref="KeyNotFoundException"></exception>
        public ResultDto GetInstrumentItemReagentList(string itemId, string channelId, string reagentState,string equipmentId)
        {
            ResultDto result = new ResultDto();
            try
            {
                var instrumentId = _sqlSugarUow.Db.Queryable<EMS_EQUIPMENT_INFO>()
                    .Where(p => p.EQUIPMENT_ID == equipmentId)
                    .Select(i => i.INSTRUMENT_ID)
                    .First();
                //if(instrumentId.IsNullOrEmpty())
                //{
                //    return new ResultDto { success = false, msg = "未找到仪器信息" };
                //}
                var list = _sqlSugarUow.Db.Queryable<LIS6_INSTRUMENT_ITEM_REAGENT>()
                    .LeftJoin<SYS6_MATERIAL_INFO>((a,b) => a.MATERIAL_ID == b.MATERIAL_ID)
                    .LeftJoin<SYS6_COMPANY_INFO>((a,b,c) => b.FACTURER_ID == c.COMPANY_ID)
                    .Where((a,b,c) => a.ITEM_ID == itemId && a.INSTRUMENT_ID == instrumentId && a.MATERIAL_TYPE == "2" && a.REAGENT_STATE == reagentState)
                    .Select((a,b,c) =>new
                    {
                        a.MATERIAL_ID,
                        a.REAGENT_ID,
                        a.ITEM_ID,
                        a.CHANNEL_ID,
                        a.INSTRUMENT_ID,
                        a.TEST_AMOUNT,
                        b.MATERIAL_CODE,
                        b.MATERIAL_CLASS,
                        b.MATERIAL_CNAME,
                        b.MATERIAL_SCNAME,
                        b.MATERIAL_ENAME,
                        b.MATERIAL_SENAME,
                        b.MATERIAL_SPEC,
                        b.MATERIAL_TYPE,
                        b.FACTURER_ID,
                        FACTURER_NAME = c.COMPANY_NAME,
                        b.MATERIAL_YZTYPE,
                        b.MATERIAL_PACK,
                        b.MATERIAL_BAR,
                        b.CONVET_RATIO,
                        a.FIRST_RTIME
                    }).ToList();

                result.data = list;
                result.success = true;
            }
            catch (Exception ex)
            {
                result.success = false;
                result.msg = "项目试剂信息查询失败";
                _logger.LogError("项目试剂信息查询失败:\n" + ex.Message);
                throw new KeyNotFoundException($"项目试剂信息查询失败");
            }
            return result;
        }

        public ResultDto  GetInstrumentReagentCommonList(string equipmentId)
        {
            ResultDto result = new ResultDto();
            try
            {
                var instrumentId = _sqlSugarUow.Db.Queryable<EMS_EQUIPMENT_INFO>()
                    .Where(p => p.EQUIPMENT_ID == equipmentId)
                    .Select(i => i.INSTRUMENT_ID)
                    .First();
                //if(instrumentId.IsNullOrEmpty())
                //{
                //    return new ResultDto { success = false, msg = "未找到仪器信息" };
                //}
                var list = _sqlSugarUow.Db.Queryable<LIS6_INSTRUMENT_ITEM_REAGENT>()
                    .LeftJoin<SYS6_MATERIAL_INFO>((a, b) => a.MATERIAL_ID == b.MATERIAL_ID)
                    .LeftJoin<SYS6_COMPANY_INFO>((a, b, c) => b.FACTURER_ID == c.COMPANY_ID)
                    .Where((a, b, c) => a.MATERIAL_TYPE == "3" && a.REAGENT_STATE == "1" && a.INSTRUMENT_ID == instrumentId)
                    .Select((a, b, c) => new
                    {
                        a.MATERIAL_ID,
                        a.REAGENT_ID,
                        a.ITEM_ID,
                        a.CHANNEL_ID,
                        a.INSTRUMENT_ID,
                        a.TEST_AMOUNT,
                        b.MATERIAL_CODE,
                        b.MATERIAL_CLASS,
                        b.MATERIAL_CNAME,
                        b.MATERIAL_SCNAME,
                        b.MATERIAL_ENAME,
                        b.MATERIAL_SENAME,
                        b.MATERIAL_SPEC,
                        MATERIAL_TYPE = "公用试剂",
                        b.FACTURER_ID,
                        FACTURER_NAME = c.COMPANY_NAME,
                        b.MATERIAL_YZTYPE,
                        b.MATERIAL_PACK,
                        b.MATERIAL_BAR,
                        b.CONVET_RATIO,
                        a.FIRST_RTIME
                    }).ToList();
                result.data = list;
                result.success = true;
            }
            catch (Exception ex)
            {
                result.success = false;
                result.msg = "仪器公共试剂查询失败";
                _logger.LogError("仪器公共试剂查询失败:\n" + ex.Message);
                throw new KeyNotFoundException($"仪器公共试剂查询失败");
            }
            return result;
        }

        public ResultDto  GetCalibratorList(string equipmentId)
        {
            ResultDto result = new ResultDto();
            try
            {
                var list = _sqlSugarUow.Db.Queryable<LIS6_INSTRUMENT_INFO>()
                    .LeftJoin<LIS6_INSTRUMENT_ITEM_REAGENT>((a, b) => a.INSTRUMENT_ID == b.INSTRUMENT_ID)
                    .LeftJoin<SYS6_MATERIAL_INFO>((a, b, c) => b.MATERIAL_ID == c.MATERIAL_ID)
                    .LeftJoin<SYS6_COMPANY_INFO>((a, b, c, d) => c.FACTURER_ID == d.COMPANY_ID)
                    .Where((a, b, c, d) => a.EQUIPMENT_ID == equipmentId && b.MATERIAL_TYPE == "4" && b.REAGENT_STATE == "1")
                    .Select((a, b, c, d) => new
                    {
                        b.MATERIAL_ID,
                        b.REAGENT_ID,
                        b.ITEM_ID,
                        b.CHANNEL_ID,
                        b.INSTRUMENT_ID,
                        b.TEST_AMOUNT,
                        c.MATERIAL_CODE,
                        c.MATERIAL_CLASS,
                        c.MATERIAL_CNAME,
                        c.MATERIAL_SCNAME,
                        c.MATERIAL_ENAME,
                        c.MATERIAL_SENAME,
                        c.MATERIAL_SPEC,
                        c.MATERIAL_TYPE,
                        c.FACTURER_ID,
                        FACTURER_NAME = d.COMPANY_NAME,
                        c.MATERIAL_YZTYPE,
                        c.MATERIAL_PACK,
                        c.MATERIAL_BAR,
                        c.CONVET_RATIO,
                        b.FIRST_RTIME
                    }).ToList();
                result.data = list.DistinctBy(p => p.MATERIAL_ID).ToList();
                result.success = true;
            }
            catch (Exception ex)
            {
                result.success = false;
                result.msg = "仪器校准品查询失败";
                _logger.LogError("仪器校准品查询失败:\n" + ex.Message);
                throw new KeyNotFoundException($"仪器校准品查询失败");
            }
            return result;
        }

        public List<LIS6_INSTRUMENT_ITEM> GetCalibratorItemList(string itemId,string equipmentId,string materialId)
        {
            try
            {
                var instrumentId = _sqlSugarUow.Db.Queryable<EMS_EQUIPMENT_INFO>()
                    .Where(p => p.EQUIPMENT_ID == equipmentId)
                    .Select(i => i.INSTRUMENT_ID)
                    .First();
                var list = _sqlSugarUow.Db.Queryable<LIS6_INSTRUMENT_ITEM_REAGENT>()
                    .InnerJoin<LIS6_INSTRUMENT_ITEM>((itemReagent,instrumentItem) => itemReagent.ITEM_ID == instrumentItem.ITEM_ID)
                    .Where((itemReagent, instrumentItem) => itemReagent.REAGENT_STATE == "1" && itemReagent.MATERIAL_ID == materialId && instrumentItem.INSTRUMENT_ID == instrumentId && instrumentItem.CHANNEL_STATE == "1")
                    .Select((itemReagent, instrumentItem) => instrumentItem)
                    .ToList();
                return list.DistinctBy(p => p.ITEM_ID).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError("校准品项目查询失败:\n" + ex.Message);
                throw new KeyNotFoundException($"校准品项目查询失败");
            }
        }
    }
}
