﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using H.Utility.SqlSugarInfra;
using SqlSugar;

namespace XH.H82.Models.Entities.Common
{
    [DBOwner("XH_SYS")]
    public class LIS6_INSPECTION_GROUP
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string GROUP_ID { get; set; }
        public string GROUP_NAME { get; set; }
        public string LAB_ID { get;set; }
        public string HOSPITAL_ID { get; set; }
        public string PGROUP_ID { get;set; }
        public string STATE_FLAG { get; set; }
    }
}
