﻿using H.Utility.SqlSugarInfra;
using SqlSugar;

namespace XH.H82.Models.EquipmengtClassNew;

/// <summary>
/// 设备类型档案记录关联表
/// </summary>
[DBOwner("XH_OA")]
[SugarTable("EMS_EQP_CLASS_ARCHIVES", TableDescription = "设备类型档案记录关联表")]
public class EMS_EQP_CLASS_ARCHIVES
{
    /// <summary>
    /// 关联id主键
    /// </summary>
    [SugarColumn(IsPrimaryKey = true,ColumnName = "CLASS_ARCHIVES_ID")]
    public string ClassArchivesId{ get; set; }
        
    /// <summary>
    /// 医疗机构ID
    /// </summary>
    [SugarColumn(ColumnName = "HOSPITAL_ID")]
    public string HospitalId{ get; set; }
        
    /// <summary>
    /// 设备类型id
    /// </summary>
    [SugarColumn(ColumnName = "EQP_CLASS_ID")]
    public string EqpClassId{ get; set; }
        
    /// <summary>
    /// 档案记录字典id
    /// </summary>
    [SugarColumn(ColumnName = "EQP_ARCHIVES_ID")]
    public string EqpArchivesId{ get; set; }
        
    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "REMARK")]
    public string? Remark{ get; set; }
        
    /// <summary>
    /// 最后修改时间
    /// </summary>
    [SugarColumn(ColumnName = "LAST_MTIME")]
    public DateTime? LAST_MTIME{ get; set; }
        
    /// <summary>
    /// 最后修改人员
    /// </summary>
    [SugarColumn(ColumnName = "LAST_MPERSON")]
    public string? LAST_MPERSON{ get; set; }
        
    /// <summary>
    /// 首次登记时间
    /// </summary>
    [SugarColumn(ColumnName = "FIRST_RTIME")]
    public DateTime? FIRST_RTIME{ get; set; }
        
    /// <summary>
    /// 首次登记人
    /// </summary>
    [SugarColumn(ColumnName = "FIRST_RPERSON")]
    public string? FIRST_RPERSON{ get; set; }
    
        
}