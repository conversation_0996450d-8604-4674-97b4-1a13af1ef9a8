using H.BASE.SqlSugarInfra.Uow;
using XH.H82.Models.SugarDbContext;
using XH.H82.Models.Entities;
using XH.H82.Models.EquipmengtClassNew;
using XH.H82.Models.EquipmentCodeCustom;
using XH.H82.Services.EquipmentCodeCustom;

namespace XH.H82.API.Examples;

/// <summary>
/// GetEquipmentCodeCustomDict 方法测试示例
/// </summary>
public class GetEquipmentCodeCustomDictTest
{
    private readonly ISqlSugarUow<SugarDbContext_Master> _dbContext;
    private readonly CustomCodeService _customCodeService;

    public GetEquipmentCodeCustomDictTest(ISqlSugarUow<SugarDbContext_Master> dbContext, CustomCodeService customCodeService)
    {
        _dbContext = dbContext;
        _customCodeService = customCodeService;
    }

    /// <summary>
    /// 测试1级设备类型的查询
    /// </summary>
    public void TestLevel1EquipmentClass()
    {
        Console.WriteLine("=== 测试1级设备类型 ===");
        
        // 创建测试设备（1级设备类型）
        var equipment = new EMS_EQUIPMENT_INFO
        {
            EQUIPMENT_CLASS = "H82CLASSJZL", // 假设这是1级设备类型
            UNIT_ID = "UNIT001",
            EQUIPMENT_ID = "TEST001",
            EQUIPMENT_NAME = "测试设备1"
        };

        // 执行查询
        var result = _customCodeService.GetEquipmentCodeCustomDict(equipment);
        
        Console.WriteLine($"设备类型: {equipment.EQUIPMENT_CLASS}");
        Console.WriteLine($"专业组: {equipment.UNIT_ID}");
        Console.WriteLine($"找到模板数量: {result.Count}");
        
        foreach (var template in result)
        {
            Console.WriteLine($"- 模板: {template.EqpNoName}");
            Console.WriteLine($"  适用类型: {template.EqpNoClass}");
            Console.WriteLine($"  适用范围: {template.EqpNoApplys}");
            Console.WriteLine($"  优先级: {template.EqpNoLevel}");
        }
    }

    /// <summary>
    /// 测试2级设备类型的查询
    /// </summary>
    public void TestLevel2EquipmentClass()
    {
        Console.WriteLine("\n=== 测试2级设备类型 ===");
        
        // 首先查找一个2级设备类型
        var level2Class = _dbContext.Db.Queryable<EMS_EQUIPMENT_CLASS_DICT>()
            .Where(x => x.ClassLevel == "2" && x.ParentClassId != "0")
            .First();
            
        if (level2Class == null)
        {
            Console.WriteLine("未找到2级设备类型，跳过测试");
            return;
        }

        // 创建测试设备（2级设备类型）
        var equipment = new EMS_EQUIPMENT_INFO
        {
            EQUIPMENT_CLASS = level2Class.ClassId,
            UNIT_ID = "UNIT001",
            EQUIPMENT_ID = "TEST002",
            EQUIPMENT_NAME = "测试设备2"
        };

        // 执行查询
        var result = _customCodeService.GetEquipmentCodeCustomDict(equipment);
        
        Console.WriteLine($"设备类型: {equipment.EQUIPMENT_CLASS} (2级)");
        Console.WriteLine($"父级类型: {level2Class.ParentClassId}");
        Console.WriteLine($"专业组: {equipment.UNIT_ID}");
        Console.WriteLine($"找到模板数量: {result.Count}");
        
        foreach (var template in result)
        {
            Console.WriteLine($"- 模板: {template.EqpNoName}");
            Console.WriteLine($"  适用类型: {template.EqpNoClass}");
            Console.WriteLine($"  适用范围: {template.EqpNoApplys}");
            Console.WriteLine($"  优先级: {template.EqpNoLevel}");
            
            // 检查是否匹配父级类型
            if (template.EqpNoClass != "0" && template.EqpNoClassList.Contains(level2Class.ParentClassId))
            {
                Console.WriteLine($"  ✓ 匹配父级类型: {level2Class.ParentClassId}");
            }
            if (template.EqpNoClass != "0" && template.EqpNoClassList.Contains(equipment.EQUIPMENT_CLASS))
            {
                Console.WriteLine($"  ✓ 匹配自身类型: {equipment.EQUIPMENT_CLASS}");
            }
        }
    }

    /// <summary>
    /// 测试不存在的设备类型
    /// </summary>
    public void TestNonExistentEquipmentClass()
    {
        Console.WriteLine("\n=== 测试不存在的设备类型 ===");
        
        // 创建测试设备（不存在的设备类型）
        var equipment = new EMS_EQUIPMENT_INFO
        {
            EQUIPMENT_CLASS = "NONEXISTENT_CLASS",
            UNIT_ID = "UNIT001",
            EQUIPMENT_ID = "TEST003",
            EQUIPMENT_NAME = "测试设备3"
        };

        // 执行查询
        var result = _customCodeService.GetEquipmentCodeCustomDict(equipment);
        
        Console.WriteLine($"设备类型: {equipment.EQUIPMENT_CLASS} (不存在)");
        Console.WriteLine($"专业组: {equipment.UNIT_ID}");
        Console.WriteLine($"找到模板数量: {result.Count}");
        Console.WriteLine("应该使用原始逻辑进行查询");
        
        foreach (var template in result)
        {
            Console.WriteLine($"- 模板: {template.EqpNoName}");
            Console.WriteLine($"  适用类型: {template.EqpNoClass}");
            Console.WriteLine($"  适用范围: {template.EqpNoApplys}");
        }
    }

    /// <summary>
    /// 运行所有测试
    /// </summary>
    public void RunAllTests()
    {
        try
        {
            TestLevel1EquipmentClass();
            TestLevel2EquipmentClass();
            TestNonExistentEquipmentClass();
            
            Console.WriteLine("\n=== 所有测试完成 ===");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"测试过程中发生异常: {ex.Message}");
            Console.WriteLine($"异常详情: {ex}");
        }
    }
}
