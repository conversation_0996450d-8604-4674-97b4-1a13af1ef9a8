﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace XH.H82.Models.Entities
{
    [DBOwner("XH_OA")]
    public class EMS_CHANGE_INFO : IRecord
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string CHANGE_ID { get; set; }
        public string HOSPITAL_ID { get; set; }
        [Required(ErrorMessage ="设备id不能为空")]
        public string EQUIPMENT_ID { get; set; }
        public string CHANGE_NO { get; set; }
        public DateTime? CHANGE_DATE { get; set; }
        public string CHANGE_PERSON { get; set; }
        public string CHANGE_CONTENT { get; set; }
        public string CHANGE_STATE { get; set; }
        public string FIRST_RPERSON { get; set; }
        public DateTime? FIRST_RTIME { get; set; }
        public string LAST_MPERSON { get; set; }
        public DateTime? LAST_MTIME { get; set; }
        public string REMARK { get; set; }

        public string GetId()
        {
            return CHANGE_ID;
        }

        public string GetEquipmentId()
        {
            return EQUIPMENT_ID;
        }

        public (string Type, string TypeName) GetType()
        {
            return ("6", "变更");
        }

        public DateTime? GetRecordDate()
        {
            return CHANGE_DATE;
        }
    }
}
