﻿using XH.H82.Models.BusinessModuleClient.H04;

namespace XH.H82.Models.TemplateDesign
{
    public class Form
    {
        /// <summary>
        /// 排序号
        /// </summary>
        public string formName { get; set; }
        /// <summary>
        /// 排序号
        /// </summary>
        public string formCname { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string formCode { get; set; }

        /// <summary>
        /// 是否新增
        /// </summary>
        public bool? ifNew { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool? ifShow { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool? titleShow { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string titleColor { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int? titleSize { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string titleStyle { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string titleBackground { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string titleAlign { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int? contentLine { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int? contentMaxLine { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int contentHeightClass { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string contentHeightRatio { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string contentAlign { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string contentColor { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int? contentFontSize { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string contentStyle { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string contentBackground { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string titleAndContentType { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool? contentEnlarge { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool? ifRequired { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool? replaceField { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string onlyRead { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string @default { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string resetContent { get; set; }

        /// <summary>
        /// 输入方式
        /// </summary>
        public string dataType { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string dataClass { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool? allowMaintainDropDownData { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string formDesc { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string suffix { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int? sort { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool? editeState { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool? unitFlag { get; set; }

        public StyleJson? styleJson { get; set; }

    }

    /// <summary>
    /// 工具箱表单信息
    /// </summary>
    public class PageSettingForm
    {
        /// <summary>
        /// 
        /// </summary>
        public List<Form> form { get; set; }

    }

}
