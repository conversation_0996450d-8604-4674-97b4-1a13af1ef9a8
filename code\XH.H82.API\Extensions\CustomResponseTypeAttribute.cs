﻿using H.Utility;
using Microsoft.AspNetCore.Mvc;
using Polly.Caching;

namespace XH.H82.API.Extensions
{
    public class CustomResponseTypeAttribute : ProducesResponseTypeAttribute
    {

        public CustomResponseTypeAttribute(int statusCode) : base(statusCode)
        {
        }
        public CustomResponseTypeAttribute(Type type, int statusCode = 200) : base(type, statusCode)
        {
            base.Type = typeof(ResultDto<>).MakeGenericType(type);
            base.StatusCode = statusCode;
        }

        public CustomResponseTypeAttribute(Type type, int statusCode, string contentType, params string[] additionalContentTypes) : base(type, statusCode, contentType, additionalContentTypes)
        {
            base.Type = typeof(ResultDto<>).MakeGenericType(type);
            base.StatusCode = statusCode;
        }

    }

}
