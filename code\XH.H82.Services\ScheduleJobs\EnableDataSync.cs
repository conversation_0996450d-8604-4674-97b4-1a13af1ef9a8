﻿using H.BASE.SqlSugarInfra.Uow;
using H.IRepository;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Serilog;
using XH.H82.Models.Entities;
using XH.H82.Models.SugarDbContext;
using static iTextSharp.text.pdf.AcroFields;

namespace XH.H82.Services.ScheduleJobs
{
    public class EnableDataSync : IHostedService, IDisposable
    {
        private readonly IConfiguration _configuration;

        private readonly ISqlSugarUow<SugarDbContext_Master> _sqlSugarUow;
        private Timer _timer;
        public EnableDataSync(IConfiguration configuration, ISqlSugarUow<SugarDbContext_Master> sqlSugarUow)
        {
            _configuration = configuration;
            _sqlSugarUow = sqlSugarUow;
        }
        public void Dispose()
        {
            _timer?.Dispose();
        }
        public Task StartAsync(CancellationToken cancellationToken)
        {
            try
            {
                Log.Information("设备启用任务");
                //获取所有state为1的启用信息
                var res = _sqlSugarUow.Db.Queryable<EMS_START_STOP>()
                .Where(p => p.START_STOP_STATE == "1" && p.START_STOP_TYPE == "1")
                .ToList();
                res.ForEach(item =>
                {
                    //若启用时间为今天
                    if (item.START_DATE == DateTime.Today)
                    {
                        //修改state为0
                        _sqlSugarUow.Db.Updateable<EMS_START_STOP>().SetColumns(p => new EMS_START_STOP()
                        {
                            START_STOP_STATE = "0",
                            LAST_MTIME = DateTime.Now
                        }).Where(i => i.START_STOP_ID == item.START_STOP_ID).ExecuteCommand();
                        //修改设备状态为1
                        _sqlSugarUow.Db.Updateable<EMS_EQUIPMENT_INFO>().SetColumns(p => new EMS_EQUIPMENT_INFO
                        {
                            EQUIPMENT_STATE = "1",
                            LAST_MPERSON = item.LAST_MPERSON != null ? item.LAST_MPERSON : item.FIRST_RPERSON,
                            LAST_MTIME = DateTime.Now,
                        }).Where(i => i.EQUIPMENT_ID == item.EQUIPMENT_ID).ExecuteCommand();
                    }
                });
                _timer = new Timer(DoWork, "", TimeSpan.Zero, TimeSpan.FromMinutes(1));
            }
            catch (Exception ex)
            {
                Log.Error(ex.ToString());
            }
            return Task.CompletedTask;
        }
        public Task StopAsync(CancellationToken cancellationToken)
        {
            _timer?.Change(Timeout.Infinite, 0);
            return Task.CompletedTask;
        }
        public async void DoWork(object state)
        {
            //若当前时间为0点
            if (DateTime.Now.Hour == 0 && DateTime.Now.Minute == 0)
            {
                //获取所有state为1的启用信息
                var res = _sqlSugarUow.Db.Queryable<EMS_START_STOP>()
                .Where(p => p.START_STOP_STATE == "1" && p.START_STOP_TYPE == "1")
                .ToList();
                res.ForEach(item =>
                {
                    //若启用时间为今天
                    if (item.START_DATE == DateTime.Today)
                    {
                        //修改state为0
                        _sqlSugarUow.Db.Updateable<EMS_START_STOP>().SetColumns(p => new EMS_START_STOP()
                        {
                            START_STOP_STATE = "0",
                            LAST_MTIME = DateTime.Now
                        }).Where(i => i.START_STOP_ID == item.START_STOP_ID).ExecuteCommand();
                        //修改设备状态为1
                        _sqlSugarUow.Db.Updateable<EMS_EQUIPMENT_INFO>().SetColumns(p => new EMS_EQUIPMENT_INFO
                        {
                            EQUIPMENT_STATE = "1",
                            LAST_MPERSON = item.LAST_MPERSON != null ? item.LAST_MPERSON : item.FIRST_RPERSON,
                            LAST_MTIME = DateTime.Now,
                        }).Where(i => i.EQUIPMENT_ID == item.EQUIPMENT_ID).ExecuteCommand();
                    }
                });
            }
        }


        public async void ChangeEquipmentState()
        {

            Log.Information("设备启用任务");
            var awaitingExcutes = new Dictionary<string, List<EMS_START_STOP>>();

            var datas = await _sqlSugarUow.Db.Queryable<EMS_START_STOP>()
               .OrderBy(x => x.OPER_TIME)
               .Where(x => x.START_DATE != null)
               .Where(x => x.OPER_TIME != null)
               .Where(x => x.START_STOP_STATE == "1")
               .ToListAsync();
            foreach (var item in datas)
            {
                if (!awaitingExcutes.TryGetValue(item.EQUIPMENT_ID, out var list))
                {
                    if (list is null)
                    {
                        list = new();
                        awaitingExcutes.Add(item.EQUIPMENT_ID, list);
                    }
                    list.Add(item);
                }
                else
                {
                    list!.Add(item);
                }
            }

            foreach (var awaitingExcute in awaitingExcutes)
            {
                if (awaitingExcute.Value is not null)
                {

                    foreach (var last in awaitingExcute.Value)
                    {
                        if (last!.START_DATE <= DateTime.Now.Date)
                        {
                            //修改state为0
                            _sqlSugarUow.Db.Updateable<EMS_START_STOP>().SetColumns(p => new EMS_START_STOP()
                            {
                                START_STOP_STATE = "0",
                                LAST_MTIME = DateTime.Now
                            }).Where(i => i.START_STOP_ID == last.START_STOP_ID).ExecuteCommand();
                            //修改设备状态为1
                            _sqlSugarUow.Db.Updateable<EMS_EQUIPMENT_INFO>().SetColumns(p => new EMS_EQUIPMENT_INFO
                            {
                                EQUIPMENT_STATE = last.START_STOP_TYPE == "1" ? "1" : "2",
                                LAST_MPERSON = last.LAST_MPERSON != null ? last.LAST_MPERSON : last.FIRST_RPERSON,
                                LAST_MTIME = DateTime.Now,
                            }).Where(i => i.EQUIPMENT_ID == last.EQUIPMENT_ID).ExecuteCommand();
                        }
                    }
                }
            }
        }

    }
}
