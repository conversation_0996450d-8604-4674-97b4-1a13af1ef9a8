﻿using H.BASE;
using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using XH.H82.Base.Tree;
using XH.H82.IServices;
using XH.H82.Models.Dtos;
using XH.H82.Models.Entities.Common;
using XH.H82.Models.SugarDbContext;
using XH.LAB.UTILS.Interface;
using XH.LAB.UTILS.Models;

namespace XH.H82.Services
{
    public class CurrencyService : ICurrencyService
    {
        private readonly ILogger<CurrencyService> _logger;
        private readonly ISqlSugarUow<SugarDbContext_Master> _dbContext;
        private readonly IAuthorityService2 _authorityService;
        private readonly IHttpContextAccessor _httpContext;
        private readonly AuthorityContext _authorityContext;
        public CurrencyService(ISqlSugarUow<SugarDbContext_Master> dbContext, IHttpContextAccessor httpContext, IAuthorityService2 authorityService)
        {
            _dbContext = dbContext;
            _httpContext = httpContext;
            _authorityService = authorityService;
            //ExecutingChangeSqlHelper.ExecutingChangeSql(_dbContext);
            _authorityContext = new AuthorityContext(_dbContext,_authorityService);
        }

        public PersonTreeDto GetPersonList(string mgroupId, string userNo, string hospitalId, string labId, string areaId)
        {
            _authorityContext.SetUser(_httpContext.HttpContext.User.ToClaimsDto(),labId, areaId);
            var groupList =
                _authorityService.GetUserPermissionPgroup(_dbContext,new OrgParams()
                {
                    hospital_id = hospitalId,
                    lab_id =  labId,
                    area_id = areaId
                },AppSettingsProvider.CurrModuleId);
            var userList = _dbContext.Db.Queryable<SYS6_USER>().Where(p => groupList.Select(i => i.PGROUP_ID).Contains(p.DEPT_CODE) && p.STATE_FLAG == "1").ToList();
            var personTreeDto = new PersonTreeDto();
            var mgroupList = new List<MGROUP_LIST>();
            groupList.ForEach(item =>
            {
                mgroupList.Add(new MGROUP_LIST()
                {
                    MGROUP_ID = item.PGROUP_ID,
                    MGROUP_NAME = item.PGROUP_NAME
                });
            });
            for (int i = 0; i < mgroupList.Count; i++)
            {
                var personInfoList = new List<PERSON_LIST>();
                userList.ForEach(item =>
                {
                    if (item.DEPT_CODE == mgroupList[i].MGROUP_ID)
                    {
                        personInfoList.Add(new PERSON_LIST()
                        {
                            PERSON_ID = item.USER_NO,
                            PERSON_NAME = $"{item.HIS_ID ?? item.LOGID}_{item.USERNAME}"
                        });
                    }
                    mgroupList[i].PERSON_LIST = personInfoList;
                });
            }
            personTreeDto.MGROUP_LIST = mgroupList;
            return personTreeDto;
        }
        public List<BasicDataDto> GetLabList(string hospitalId, string areaId)
        {
            var res = _dbContext.Db.Queryable<SYS6_INSPECTION_LAB>()
                .InnerJoin<SYS6_LABAREA_DICT>((a, b) => a.LAB_ID == b.LAB_ID)
                .Where((a, b) => b.HOSPITAL_ID == hospitalId && b.AREA_ID == areaId)
                .ToList();
            var labList = new List<BasicDataDto>();
            res.ForEach(item =>
            {
                labList.Add(new BasicDataDto()
                {
                    BasciId = item.LAB_ID,
                    BasicName = item.LAB_NAME
                });
            });
            return labList;
        }
        public List<BasicDataDto> GetPersonTypeList()
        {
            var res = _dbContext.Db.Queryable<SYS6_BASE_DATA>()
                .Where(p => p.HOSPITAL_ID == "H0000" && p.LAB_ID == "L000" && p.CLASS_ID == "人员类型" && p.DATA_STATE == "1")
                .ToList();
            var personTypeList = new List<BasicDataDto>();
            res.ForEach(item =>
            {
                personTypeList.Add(new BasicDataDto()
                {
                    BasciId = item.DATA_ID,
                    BasicName = item.DATA_CNAME,
                    OperPerson = item.LAST_MPERSON,
                    OperTime = item.LAST_MTIME
                });
            });
            return personTypeList;
        }
        public List<BasicDataDto> GetPostList()
        {
            var res = _dbContext.Db.Queryable<SYS6_BASE_DATA>()
                .Where(p => p.HOSPITAL_ID == "H0000" && p.LAB_ID == "L000" && p.CLASS_ID == "职务/岗位" && p.DATA_STATE == "1")
                .ToList();
            var postList = new List<BasicDataDto>();
            res.ForEach(item =>
            {
                postList.Add(new BasicDataDto()
                {
                    BasciId = item.DATA_ID,
                    BasicName = item.DATA_CNAME,
                    OperPerson = item.LAST_MPERSON,
                    OperTime = item.LAST_MTIME
                });
            });
            return postList;
        }
        public List<BasicDataDto> GetProfessionalClass()
        {
            var res = _dbContext.Db.Queryable<SYS6_BASE_DATA>()
                .Where(p => p.CLASS_ID == "专业分类" && p.DATA_STATE == "1")
                .ToList();
            var professionalClassList = new List<BasicDataDto>();
            res.ForEach(item =>
            {
                professionalClassList.Add(new BasicDataDto
                {
                    BasciId = item.DATA_ID,
                    BasicName = item.DATA_CNAME
                });
            });
            return professionalClassList;
        }

        public ResultDto AddPostInfo(string postName, string labId, string hospitalId, string userName)
        {
            ResultDto result = new ResultDto();
            try
            {
                var res = _dbContext.Db.Queryable<SYS6_BASE_DATA>()
                                .Where(p => p.CLASS_ID == "职务/岗位" && p.DATA_STATE == "1")
                                .OrderByDescending(i => Convert.ToInt32(i.DATA_ID))
                                .First();
                var postInfo = new SYS6_BASE_DATA();
                if (res == null)
                {
                    postInfo.DATA_ID = "1";
                }
                else
                {
                    postInfo.DATA_ID = (Convert.ToInt32(res.DATA_ID) + 1).ToString();
                }
                postInfo.HOSPITAL_ID = "H0000";
                postInfo.LAB_ID = "L000";
                postInfo.CLASS_ID = "职务/岗位";
                postInfo.DATA_CNAME = postName;
                postInfo.DATA_STATE = "1";
                postInfo.FIRST_RPERSON = userName;
                postInfo.FIRST_RTIME = DateTime.Now;
                postInfo.LAST_MPERSON = userName;
                postInfo.LAST_MTIME = DateTime.Now;
                _dbContext.Db.Insertable(postInfo).ExecuteCommand();
                result.success = true;
            }
            catch (Exception ex)
            {
                result.success = false;
                result.msg = "保存职务/岗位失败";
                _logger.LogError("保存职务/岗位失败:\n" + ex.Message);
            }
            return result;
        }
        public ResultDto DeletePostInfo(string BasciId)
        {
            ResultDto result = new ResultDto();
            try
            {
                _dbContext.Db.Deleteable<SYS6_BASE_DATA>().Where(p => p.DATA_ID == BasciId && p.CLASS_ID == "职务/岗位").ExecuteCommand();
                result.success = true;
            }
            catch (Exception ex)
            {
                result.success = false;
                result.msg = "删除职务/岗位失败";
                _logger.LogError("删除职务/岗位失败:\n" + ex.Message);
            }
            return result;

        }
        public ResultDto AddPersonTypeInfo(string personTypeName, string labId, string hospitalId, string userName)
        {
            ResultDto result = new ResultDto();
            try
            {
                var res = _dbContext.Db.Queryable<SYS6_BASE_DATA>()
                .Where(p => p.CLASS_ID == "人员类型" && p.DATA_STATE == "1")
                .OrderByDescending(i => Convert.ToInt32(i.DATA_ID))
                .First();
                var personTypeInfo = new SYS6_BASE_DATA();
                if (res == null)
                {
                    personTypeInfo.DATA_ID = "1";
                }
                else
                {
                    personTypeInfo.DATA_ID = (Convert.ToInt32(res.DATA_ID) + 1).ToString();
                }
                personTypeInfo.HOSPITAL_ID = "H0000";
                personTypeInfo.LAB_ID = "L000";
                personTypeInfo.CLASS_ID = "人员类型";
                personTypeInfo.DATA_CNAME = personTypeName;
                personTypeInfo.DATA_STATE = "1";
                personTypeInfo.FIRST_RPERSON = userName;
                personTypeInfo.FIRST_RTIME = DateTime.Now;
                personTypeInfo.LAST_MPERSON = userName;
                personTypeInfo.LAST_MTIME = DateTime.Now;
                _dbContext.Db.Insertable(personTypeInfo).ExecuteCommand();
                result.success = true;
            }
            catch (Exception ex)
            {
                result.success = false;
                result.msg = "保存人员类型失败";
                _logger.LogError("保存人员类型失败:\n" + ex.Message);
            }
            return result;
        }
        public ResultDto DeletePersonTypeInfo(string BasciId)
        {
            ResultDto result = new ResultDto();
            try
            {
                _dbContext.Db.Deleteable<SYS6_BASE_DATA>().Where(p => p.DATA_ID == BasciId && p.CLASS_ID == "人员类型").ExecuteCommand();
                result.success = true;
            }
            catch (Exception ex)
            {
                result.success = false;
                result.msg = "删除人员类型失败";
                _logger.LogError("删除人员类型失败:\n" + ex.Message);
            }
            return result;
        }
        public List<BasicDataDto> GetPgroupPullList(string areaId, string userNo, string labId)
        {
            var user = _httpContext.HttpContext.User.ToClaimsDto();
            _authorityContext.SetUser(user,labId, areaId);
            var groupList = _authorityContext.GetAccessibleProfessionalGroups(labId, areaId).Select(pgroup => new BasicDataDto
            {
                BasciId = pgroup.PGROUP_ID,
                BasicName = pgroup.PGROUP_NAME,
                BasicSort = pgroup.PGROUP_SORT,
            }).Distinct().OrderBy(area => area.BasicSort).ToList();



            return groupList;
        }

        /// <summary>
        /// 获取院区-专业组下拉列表
        /// </summary>
        /// <param name="areaId"></param>
        /// <param name="labId"></param>
        /// <returns></returns>
        public List<ITreeNode> GetPullAreaPgroups(string? areaId, string labId)
        {
            _authorityContext.SetUser(_httpContext.HttpContext.User.ToClaimsDto(), labId, areaId);
            var result = _authorityContext.GetAreaTree(labId, areaId, null, null);
            return result.CHILDREN;
        }

        public List<ITreeNode> GetPullPersons(string? labId, string? areaId, string? mgroupId, string? pgroupId, string? modelId)
        {   
            _authorityContext.SetUser(_httpContext.HttpContext.User.ToClaimsDto(), labId, areaId);
            var result = _authorityContext.GetUserTree(labId, areaId, mgroupId, pgroupId, modelId);
            return result.CHILDREN;
        }
        
        public List<ITreeNode> GetEquipments(string? labId, string? areaId, string? mgroupId, string? pgroupId, string? modelId)
        {
            var result = _authorityContext.GetEquipmentTree(labId, areaId, mgroupId, pgroupId, modelId);
            return result.CHILDREN;
        }


        public (RootNode rootNode, List<ITreeNode> nodes) GetISOBaseOrganizationTree(string labId)
        {
            var user = _httpContext.HttpContext.User.ToClaimsDto();
            _authorityContext.SetUser(user,labId);
            var nodes = new List<ITreeNode>();
            var root = new RootNode();
            var num = 1;
            var areas =  _authorityContext.GetAccessibleAreas(labId);
            foreach (var area in areas)
            {
              var areaNode = _authorityContext.AddAreaNode(root, area, false, ref num);
              nodes.Add(areaNode);
              var mgroups = _authorityContext.GetManagerGroups(labId, area.AREA_ID, null);
              foreach (var mgroup in mgroups)
              {
                  var mgNode = _authorityContext.AddMGproug(areaNode, mgroup, false, ref num);
                  nodes.Add(mgNode);
                  var pgroups = _authorityContext.GetAccessibleProfessionalGroups(labId, area.AREA_ID)
                      .Where(x=>x.MGROUP_ID == mgroup.MGROUP_ID);
                  foreach (var pgroup in pgroups)
                  {
                     var pgNode  = _authorityContext.AddPGproug(mgNode, pgroup, false, ref num);
                     nodes.Add(pgNode);
                  }
              }
            }
            
            return (root, nodes);
        }
        
        
        public (RootNode rootNode, List<ITreeNode> nodes) GetISOLabBaseOrganizationTree(string labId)
        {
            var user = _httpContext.HttpContext.User.ToClaimsDto();
            _authorityContext.SetUser(user,labId);
            var nodes = new List<ITreeNode>();
            var root = new RootNode();
            var num = 1;
            var lab = _authorityContext.GetUserLabList().Where(x=>x.LAB_ID == labId).First();
            if (lab is null)
            {
                return (root, nodes);;
            }

            var labNode = _authorityContext.AddLabNode(root, lab, false, ref num);
            nodes.Add(labNode); 
            var mgroups = _authorityContext.GetManagerGroups(labId, null, null);
            foreach (var mgroup in mgroups)
            {
                var mgNode = _authorityContext.AddMGproug(labNode, mgroup, false, ref num);
                nodes.Add(mgNode);
                var pgroups = _authorityContext.GetAccessibleProfessionalGroups(labId, null)
                    .Where(x=>x.MGROUP_ID == mgroup.MGROUP_ID);
                foreach (var pgroup in pgroups)
                {
                    var pgNode  = _authorityContext.AddPGproug(mgNode, pgroup, false, ref num);
                    nodes.Add(pgNode);
                }
            }
            return (root, nodes);
        }
    }
}
