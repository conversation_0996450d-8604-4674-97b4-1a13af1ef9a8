﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using H.Utility.SqlSugarInfra;
using SqlSugar;

namespace XH.H82.Models.Entities.THS
{
    /// <summary>
    /// 设备信息表
    /// </summary>
    [DBOwner("XH_SYS")]
    [Table("THS_EQUIPMENT_INFO")]
    [SugarTable("THS_EQUIPMENT_INFO")]
    public class THS_EQUIPMENT_INFO
    {
        /// <summary>
        /// 温湿度设备ID
        /// </summary>
        [Key]
        [SugarColumn(IsPrimaryKey = true)]
        public string? EQUIPMENT_ID { get; set; }
        /// <summary>
        /// 设备ID
        /// </summary>
        public string? EQUIPMENT_DID { get; set; }
        /// <summary>
        /// 设备名称
        /// </summary>
        public string? EQUIPMENT_NAME { get; set; }
        /// <summary>
        /// 设备代号EQUIPMENT_CODE[EMS_EQUIPMENT_INFO_NEW]
        /// </summary>
        public string? EQUIPMENT_CODE { get; set; }
        /// <summary>
        /// 设备位置INSTRUMENT_POSITION[LIS5_INSTRUMENT_INFO]
        /// </summary>
        public string? EQUIPMENT_POSITION { get; set; }
        /// <summary>
        /// 管理单元ID
        /// </summary>
        public string? UNIT_ID { get; set; }
        /// <summary>
        /// 设备层架数量
        /// </summary>
        public int? SHELF_NUM { get; set; }
        /// <summary>
        /// 坐标参数
        /// </summary>
        public string? COORDINATE_PARAM { get; set; }
        /// <summary>
        /// 排序号
        /// </summary>
        public string? EQUIPMENT_SORT { get; set; }
        /// <summary>
        /// 是否重点设备0否1是
        /// </summary>
        public string? IF_MAJOR_EQUIPMENT { get; set; }
        /// <summary>
        /// 记录时间点
        /// </summary>
        public string? RECORD_TIMING { get; set; }
        /// <summary>
        /// 报警联系方式
        /// </summary>
        public string? ALARM_TYPE { get; set; }
        /// <summary>
        /// 报警联系人
        /// </summary>
        public string? ALARM_LINKMAN { get; set; }
        /// <summary>
        /// 报警联系电话
        /// </summary>
        public string? ALARM_PHONE { get; set; }
        /// <summary>
        /// 首次登记人
        /// </summary>
        public string? FIRST_RPERSON { get; set; }
        /// <summary>
        /// 首次登记时间
        /// </summary>
        public DateTime? FIRST_RTIME { get; set; }
        /// <summary>
        /// 最后修改人员
        /// </summary>
        public string? LAST_MPERSON { get; set; }
        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime? LAST_MTIME { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string? REMARK { get; set; }
        /// <summary>
        /// 设备状态
        /// </summary>
        public string? EQUIPMENT_STATE { get; set; }
        /// <summary>
        /// 医疗机构ID
        /// </summary>
        public string? HOSPITAL_ID { get; set; }
        /// <summary>
        /// 设备类型1设备2房间
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string? EQUIPMENT_TYPE { get; set; }

        /// <summary>
        /// 设备对应SN
        /// </summary>
        public string? EQUIPMENT_SN { get; set; }
    }
}
