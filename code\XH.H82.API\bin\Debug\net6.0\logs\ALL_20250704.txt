2025-07-04 11:03:04.476 +08:00 [INF] ==>App Start..2025-07-04 11:03:04
2025-07-04 11:03:04.684 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-04 11:03:04.688 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-04 11:03:07.067 +08:00 [INF] ==>基础连接请求完成.
2025-07-04 11:03:07.546 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-04 11:03:08.046 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-04 11:03:08.376 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-04 11:03:08.378 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-04 11:03:08.718 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-04 11:03:10.864 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-04 11:03:11.241 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-04 11:03:11.792 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-04 11:03:11.793 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-04 11:03:12.985 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-04 11:03:15.430 +08:00 [INF] ==>初始化完成..
2025-07-04 11:03:15.497 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-04 11:03:15.498 +08:00 [INF] 设备启用任务
2025-07-04 11:03:15.498 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-04 11:03:17.386 +08:00 [INF] 【SQL执行耗时:1860.3833ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-04 11:03:17.712 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-04 11:03:17.724 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-04 11:03:17.726 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-04 11:03:17.726 +08:00 [INF] Hosting environment: Development
2025-07-04 11:03:17.726 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-04 11:05:50.280 +08:00 [INF] ==>App Start..2025-07-04 11:05:50
2025-07-04 11:05:50.462 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-04 11:05:50.465 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-04 11:05:51.962 +08:00 [INF] ==>基础连接请求完成.
2025-07-04 11:05:52.352 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-04 11:05:52.752 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-04 11:05:53.087 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-04 11:05:53.089 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-04 11:05:53.219 +08:00 [ERR] 调用H07检测版本依赖时发生错误:Index and length must refer to a location within the string. (Parameter 'length')
System.ArgumentOutOfRangeException: Index and length must refer to a location within the string. (Parameter 'length')
   at System.String.Substring(Int32 startIndex, Int32 length)
   at Spectre.Console.Rendering.Segment.SplitOverflow(Segment segment, Nullable`1 overflow, Int32 maxWidth) in /_/src/Spectre.Console/Rendering/Segment.cs:line 351
   at Spectre.Console.Paragraph.SplitLines(Int32 maxWidth) in /_/src/Spectre.Console/Widgets/Paragraph.cs:line 242
   at Spectre.Console.Paragraph.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Widgets/Paragraph.cs:line 144
   at Spectre.Console.Rendering.Renderable.Spectre.Console.Rendering.IRenderable.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Rendering/Renderable.cs:line 19
   at Spectre.Console.Markup.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Widgets/Markup.cs:line 54
   at Spectre.Console.Rendering.Renderable.Spectre.Console.Rendering.IRenderable.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Rendering/Renderable.cs:line 19
   at Spectre.Console.TableRenderer.Render(TableRendererContext context, List`1 columnWidths) in /_/src/Spectre.Console/Widgets/Table/TableRenderer.cs:line 31
   at Spectre.Console.Table.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Widgets/Table/Table.cs:line 141
   at Spectre.Console.Rendering.Renderable.Spectre.Console.Rendering.IRenderable.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Rendering/Renderable.cs:line 19
   at Spectre.Console.RenderableExtensions.GetSegments(IAnsiConsole console, RenderContext options, IEnumerable`1 renderables) in /_/src/Spectre.Console/Extensions/RenderableExtensions.cs:line 37
   at Spectre.Console.RenderableExtensions.GetSegments(IRenderable renderable, IAnsiConsole console) in /_/src/Spectre.Console/Extensions/RenderableExtensions.cs:line 29
   at Spectre.Console.AnsiBuilder.Build(IAnsiConsole console, IRenderable renderable) in /_/src/Spectre.Console/Internal/Backends/Ansi/AnsiBuilder.cs:line 17
   at Spectre.Console.AnsiConsoleBackend.Write(IRenderable renderable) in /_/src/Spectre.Console/Internal/Backends/Ansi/AnsiConsoleBackend.cs:line 30
   at Spectre.Console.AnsiConsoleFacade.Write(IRenderable renderable) in /_/src/Spectre.Console/Internal/Backends/AnsiConsoleFacade.cs:line 40
   at Spectre.Console.AnsiConsole.Write(IRenderable renderable) in /_/src/Spectre.Console/AnsiConsole.Rendering.cs:line 29
   at H.BASE.AppInit.<RegisterInfraServer>b__22_0()
2025-07-04 11:05:53.519 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-04 11:05:54.029 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-04 11:05:54.144 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-04 11:05:54.595 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-04 11:05:54.596 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-04 11:05:55.529 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-04 11:05:57.702 +08:00 [INF] ==>初始化完成..
2025-07-04 11:05:57.725 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-04 11:05:57.728 +08:00 [INF] 设备启用任务
2025-07-04 11:05:57.729 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-04 11:05:58.288 +08:00 [INF] 【SQL执行耗时:534.333ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-04 11:05:58.446 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-04 11:05:58.463 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-04 11:05:58.464 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-04 11:05:58.465 +08:00 [INF] Hosting environment: Development
2025-07-04 11:05:58.465 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-04 11:06:04.531 +08:00 [INF] HTTP GET /favicon.ico responded 404 in 253.3020 ms
2025-07-04 11:06:19.323 +08:00 [INF] 第三方url地址为：http://************
2025-07-04 11:06:20.277 +08:00 [INF] HTTP GET /api/Test/ClassDictTest responded 200 in 1432.5054 ms
2025-07-04 11:06:20.278 +08:00 [INF] 【接口超时阀值预警】 [bbc7119bc7cc4fea5322522ed9c1b1ba]接口/api/Test/ClassDictTest,耗时:[1433]毫秒
2025-07-04 11:07:52.452 +08:00 [INF] 第三方url地址为：http://************
2025-07-04 11:07:52.461 +08:00 [INF] HTTP GET /api/Test/ClassDictTest responded 200 in 19.9166 ms
2025-07-04 11:09:04.462 +08:00 [INF] 第三方url地址为：http://************
2025-07-04 11:09:04.467 +08:00 [INF] HTTP GET /api/Test/ClassDictTest responded 200 in 30.9678 ms
2025-07-04 11:09:21.847 +08:00 [INF] ==>App Start..2025-07-04 11:09:21
2025-07-04 11:09:22.014 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-04 11:09:22.017 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-04 11:09:23.524 +08:00 [INF] ==>基础连接请求完成.
2025-07-04 11:09:23.900 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-04 11:09:24.229 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-04 11:09:24.556 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-04 11:09:24.562 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-04 11:09:24.605 +08:00 [ERR] 调用H07检测版本依赖时发生错误:Index and length must refer to a location within the string. (Parameter 'length')
System.ArgumentOutOfRangeException: Index and length must refer to a location within the string. (Parameter 'length')
   at System.String.Substring(Int32 startIndex, Int32 length)
   at Spectre.Console.Rendering.Segment.SplitOverflow(Segment segment, Nullable`1 overflow, Int32 maxWidth) in /_/src/Spectre.Console/Rendering/Segment.cs:line 351
   at Spectre.Console.Paragraph.SplitLines(Int32 maxWidth) in /_/src/Spectre.Console/Widgets/Paragraph.cs:line 242
   at Spectre.Console.Paragraph.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Widgets/Paragraph.cs:line 144
   at Spectre.Console.Rendering.Renderable.Spectre.Console.Rendering.IRenderable.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Rendering/Renderable.cs:line 19
   at Spectre.Console.Markup.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Widgets/Markup.cs:line 54
   at Spectre.Console.Rendering.Renderable.Spectre.Console.Rendering.IRenderable.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Rendering/Renderable.cs:line 19
   at Spectre.Console.TableRenderer.Render(TableRendererContext context, List`1 columnWidths) in /_/src/Spectre.Console/Widgets/Table/TableRenderer.cs:line 31
   at Spectre.Console.Table.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Widgets/Table/Table.cs:line 141
   at Spectre.Console.Rendering.Renderable.Spectre.Console.Rendering.IRenderable.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Rendering/Renderable.cs:line 19
   at Spectre.Console.RenderableExtensions.GetSegments(IAnsiConsole console, RenderContext options, IEnumerable`1 renderables) in /_/src/Spectre.Console/Extensions/RenderableExtensions.cs:line 37
   at Spectre.Console.RenderableExtensions.GetSegments(IRenderable renderable, IAnsiConsole console) in /_/src/Spectre.Console/Extensions/RenderableExtensions.cs:line 29
   at Spectre.Console.AnsiBuilder.Build(IAnsiConsole console, IRenderable renderable) in /_/src/Spectre.Console/Internal/Backends/Ansi/AnsiBuilder.cs:line 17
   at Spectre.Console.AnsiConsoleBackend.Write(IRenderable renderable) in /_/src/Spectre.Console/Internal/Backends/Ansi/AnsiConsoleBackend.cs:line 30
   at Spectre.Console.AnsiConsoleFacade.Write(IRenderable renderable) in /_/src/Spectre.Console/Internal/Backends/AnsiConsoleFacade.cs:line 40
   at Spectre.Console.AnsiConsole.Write(IRenderable renderable) in /_/src/Spectre.Console/AnsiConsole.Rendering.cs:line 29
   at H.BASE.AppInit.<RegisterInfraServer>b__22_0()
2025-07-04 11:09:24.900 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-04 11:09:25.355 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-04 11:09:25.449 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-04 11:09:25.859 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-04 11:09:25.860 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-04 11:09:26.820 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-04 11:09:29.001 +08:00 [INF] ==>初始化完成..
2025-07-04 11:09:29.021 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-04 11:09:29.023 +08:00 [INF] 设备启用任务
2025-07-04 11:09:29.024 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-04 11:09:29.417 +08:00 [INF] 【SQL执行耗时:373.1799ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-04 11:09:29.560 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-04 11:09:29.574 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-04 11:09:29.576 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-04 11:09:29.576 +08:00 [INF] Hosting environment: Development
2025-07-04 11:09:29.577 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-04 11:09:42.145 +08:00 [INF] 第三方url地址为：http://************
2025-07-04 11:09:42.647 +08:00 [INF] HTTP GET /api/Test/ClassDictTest responded 200 in 1172.7808 ms
2025-07-04 11:09:42.650 +08:00 [INF] 【接口超时阀值预警】 [84beb72524392c4d3eb29ca374d4886f]接口/api/Test/ClassDictTest,耗时:[1179]毫秒
2025-07-04 11:10:16.340 +08:00 [INF] 第三方url地址为：http://************
2025-07-04 11:10:40.304 +08:00 [INF] HTTP GET /api/Test/ClassDictTest responded 200 in 23977.1726 ms
2025-07-04 11:10:40.305 +08:00 [INF] 【接口超时阀值预警】 [c107655338f4f85c1d1b66d823083eea]接口/api/Test/ClassDictTest,耗时:[23977]毫秒
2025-07-04 11:12:15.721 +08:00 [INF] ==>App Start..2025-07-04 11:12:15
2025-07-04 11:12:15.887 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-04 11:12:15.890 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-04 11:12:17.298 +08:00 [INF] ==>基础连接请求完成.
2025-07-04 11:12:17.672 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-04 11:12:17.999 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-04 11:12:18.380 +08:00 [ERR] 调用H07检测版本依赖时发生错误:Index and length must refer to a location within the string. (Parameter 'length')
System.ArgumentOutOfRangeException: Index and length must refer to a location within the string. (Parameter 'length')
   at System.String.Substring(Int32 startIndex, Int32 length)
   at Spectre.Console.Rendering.Segment.SplitOverflow(Segment segment, Nullable`1 overflow, Int32 maxWidth) in /_/src/Spectre.Console/Rendering/Segment.cs:line 351
   at Spectre.Console.Paragraph.SplitLines(Int32 maxWidth) in /_/src/Spectre.Console/Widgets/Paragraph.cs:line 242
   at Spectre.Console.Paragraph.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Widgets/Paragraph.cs:line 144
   at Spectre.Console.Rendering.Renderable.Spectre.Console.Rendering.IRenderable.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Rendering/Renderable.cs:line 19
   at Spectre.Console.Markup.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Widgets/Markup.cs:line 54
   at Spectre.Console.Rendering.Renderable.Spectre.Console.Rendering.IRenderable.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Rendering/Renderable.cs:line 19
   at Spectre.Console.TableRenderer.Render(TableRendererContext context, List`1 columnWidths) in /_/src/Spectre.Console/Widgets/Table/TableRenderer.cs:line 31
   at Spectre.Console.Table.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Widgets/Table/Table.cs:line 141
   at Spectre.Console.Rendering.Renderable.Spectre.Console.Rendering.IRenderable.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Rendering/Renderable.cs:line 19
   at Spectre.Console.RenderableExtensions.GetSegments(IAnsiConsole console, RenderContext options, IEnumerable`1 renderables) in /_/src/Spectre.Console/Extensions/RenderableExtensions.cs:line 37
   at Spectre.Console.RenderableExtensions.GetSegments(IRenderable renderable, IAnsiConsole console) in /_/src/Spectre.Console/Extensions/RenderableExtensions.cs:line 29
   at Spectre.Console.AnsiBuilder.Build(IAnsiConsole console, IRenderable renderable) in /_/src/Spectre.Console/Internal/Backends/Ansi/AnsiBuilder.cs:line 17
   at Spectre.Console.AnsiConsoleBackend.Write(IRenderable renderable) in /_/src/Spectre.Console/Internal/Backends/Ansi/AnsiConsoleBackend.cs:line 30
   at Spectre.Console.AnsiConsoleFacade.Write(IRenderable renderable) in /_/src/Spectre.Console/Internal/Backends/AnsiConsoleFacade.cs:line 40
   at Spectre.Console.AnsiConsole.Write(IRenderable renderable) in /_/src/Spectre.Console/AnsiConsole.Rendering.cs:line 29
   at H.BASE.AppInit.<RegisterInfraServer>b__22_0()
2025-07-04 11:12:18.381 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-04 11:12:18.441 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-04 11:12:18.780 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-04 11:12:19.139 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-04 11:12:19.207 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-04 11:12:19.640 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-04 11:12:19.640 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-04 11:12:20.487 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-04 11:12:22.621 +08:00 [INF] ==>初始化完成..
2025-07-04 11:12:22.641 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-04 11:12:22.644 +08:00 [INF] 设备启用任务
2025-07-04 11:12:22.644 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-04 11:12:23.046 +08:00 [INF] 【SQL执行耗时:380.8519ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-04 11:12:23.181 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-04 11:12:23.196 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-04 11:12:23.197 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-04 11:12:23.197 +08:00 [INF] Hosting environment: Development
2025-07-04 11:12:23.198 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-04 11:14:19.411 +08:00 [INF] 第三方url地址为：http://************
2025-07-04 11:14:19.780 +08:00 [INF] HTTP GET /api/Test/ClassDictTest responded 200 in 882.4075 ms
2025-07-04 11:14:19.783 +08:00 [INF] 【接口超时阀值预警】 [83a08874a674edd645994972fa4a6e41]接口/api/Test/ClassDictTest,耗时:[889]毫秒
2025-07-04 11:15:23.146 +08:00 [INF] 第三方url地址为：http://************
2025-07-04 11:15:23.155 +08:00 [INF] HTTP GET /api/Test/ClassDictTest responded 200 in 24.9696 ms
2025-07-04 11:17:46.643 +08:00 [INF] 第三方url地址为：http://************
2025-07-04 11:17:46.650 +08:00 [INF] HTTP GET /api/Test/ClassDictTest responded 200 in 9.2691 ms
2025-07-04 11:18:11.746 +08:00 [INF] 第三方url地址为：http://************
2025-07-04 11:18:11.750 +08:00 [INF] HTTP GET /api/Test/ClassDictTest responded 200 in 41.7216 ms
2025-07-04 11:18:28.401 +08:00 [INF] 第三方url地址为：http://************
2025-07-04 11:18:28.405 +08:00 [INF] HTTP GET /api/Test/ClassDictTest responded 200 in 6.6848 ms
2025-07-04 11:18:29.415 +08:00 [INF] 第三方url地址为：http://************
2025-07-04 11:18:29.418 +08:00 [INF] HTTP GET /api/Test/ClassDictTest responded 200 in 7.0578 ms
2025-07-04 11:18:35.443 +08:00 [INF] 第三方url地址为：http://************
2025-07-04 11:18:35.446 +08:00 [INF] HTTP GET /api/Test/ClassDictTest responded 200 in 5.8549 ms
2025-07-04 11:18:50.976 +08:00 [INF] ==>App Start..2025-07-04 11:18:50
2025-07-04 11:18:51.145 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-04 11:18:51.148 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-04 11:18:52.566 +08:00 [INF] ==>基础连接请求完成.
2025-07-04 11:18:52.952 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-04 11:18:53.289 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-04 11:18:53.747 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-04 11:18:53.749 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-04 11:18:53.804 +08:00 [ERR] 调用H07检测版本依赖时发生错误:Index and length must refer to a location within the string. (Parameter 'length')
System.ArgumentOutOfRangeException: Index and length must refer to a location within the string. (Parameter 'length')
   at System.String.Substring(Int32 startIndex, Int32 length)
   at Spectre.Console.Rendering.Segment.SplitOverflow(Segment segment, Nullable`1 overflow, Int32 maxWidth) in /_/src/Spectre.Console/Rendering/Segment.cs:line 351
   at Spectre.Console.Paragraph.SplitLines(Int32 maxWidth) in /_/src/Spectre.Console/Widgets/Paragraph.cs:line 242
   at Spectre.Console.Paragraph.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Widgets/Paragraph.cs:line 144
   at Spectre.Console.Rendering.Renderable.Spectre.Console.Rendering.IRenderable.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Rendering/Renderable.cs:line 19
   at Spectre.Console.Markup.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Widgets/Markup.cs:line 54
   at Spectre.Console.Rendering.Renderable.Spectre.Console.Rendering.IRenderable.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Rendering/Renderable.cs:line 19
   at Spectre.Console.TableRenderer.Render(TableRendererContext context, List`1 columnWidths) in /_/src/Spectre.Console/Widgets/Table/TableRenderer.cs:line 31
   at Spectre.Console.Table.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Widgets/Table/Table.cs:line 141
   at Spectre.Console.Rendering.Renderable.Spectre.Console.Rendering.IRenderable.Render(RenderContext context, Int32 maxWidth) in /_/src/Spectre.Console/Rendering/Renderable.cs:line 19
   at Spectre.Console.RenderableExtensions.GetSegments(IAnsiConsole console, RenderContext options, IEnumerable`1 renderables) in /_/src/Spectre.Console/Extensions/RenderableExtensions.cs:line 37
   at Spectre.Console.RenderableExtensions.GetSegments(IRenderable renderable, IAnsiConsole console) in /_/src/Spectre.Console/Extensions/RenderableExtensions.cs:line 29
   at Spectre.Console.AnsiBuilder.Build(IAnsiConsole console, IRenderable renderable) in /_/src/Spectre.Console/Internal/Backends/Ansi/AnsiBuilder.cs:line 17
   at Spectre.Console.AnsiConsoleBackend.Write(IRenderable renderable) in /_/src/Spectre.Console/Internal/Backends/Ansi/AnsiConsoleBackend.cs:line 30
   at Spectre.Console.AnsiConsoleFacade.Write(IRenderable renderable) in /_/src/Spectre.Console/Internal/Backends/AnsiConsoleFacade.cs:line 40
   at Spectre.Console.AnsiConsole.Write(IRenderable renderable) in /_/src/Spectre.Console/AnsiConsole.Rendering.cs:line 29
   at H.BASE.AppInit.<RegisterInfraServer>b__22_0()
2025-07-04 11:18:54.108 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-04 11:18:54.446 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-04 11:18:54.595 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-04 11:18:55.056 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-04 11:18:55.056 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-04 11:18:56.142 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-04 11:18:58.453 +08:00 [INF] ==>初始化完成..
2025-07-04 11:18:58.475 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-04 11:18:58.477 +08:00 [INF] 设备启用任务
2025-07-04 11:18:58.478 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-04 11:18:58.863 +08:00 [INF] 【SQL执行耗时:365.0191ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-04 11:18:59.001 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-04 11:18:59.015 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-04 11:18:59.016 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-04 11:18:59.016 +08:00 [INF] Hosting environment: Development
2025-07-04 11:18:59.017 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-04 11:18:59.953 +08:00 [INF] 第三方url地址为：http://************
2025-07-04 11:19:14.511 +08:00 [INF] HTTP GET /api/Test/ClassDictTest responded 200 in 15234.2947 ms
2025-07-04 11:19:14.513 +08:00 [INF] 【接口超时阀值预警】 [adec741f2978d09819b8157fbff23d56]接口/api/Test/ClassDictTest,耗时:[15242]毫秒
2025-07-04 11:19:16.173 +08:00 [INF] 第三方url地址为：http://************
2025-07-04 11:19:30.876 +08:00 [INF] HTTP GET /api/Test/ClassDictTest responded 200 in 14711.6692 ms
2025-07-04 11:19:30.876 +08:00 [INF] 【接口超时阀值预警】 [b6d845db7385e1b3ef0e43330eb6b8b8]接口/api/Test/ClassDictTest,耗时:[14712]毫秒
2025-07-04 11:20:08.129 +08:00 [INF] 第三方url地址为：http://************
2025-07-04 11:20:08.136 +08:00 [INF] HTTP GET /api/Test/ClassDictTest responded 200 in 9.7468 ms
2025-07-04 11:21:05.526 +08:00 [INF] 第三方url地址为：http://************
2025-07-04 11:21:05.530 +08:00 [INF] HTTP GET /api/Test/ClassDictTest responded 200 in 11.1128 ms
2025-07-04 11:21:42.141 +08:00 [INF] 第三方url地址为：http://************
2025-07-04 11:21:55.746 +08:00 [INF] HTTP GET /api/Test/ClassDictTest responded 200 in 13661.9631 ms
2025-07-04 11:21:55.747 +08:00 [INF] 【接口超时阀值预警】 [7b7d12ac0aa4e5de97816ca625804a30]接口/api/Test/ClassDictTest,耗时:[13662]毫秒
2025-07-04 11:22:19.392 +08:00 [INF] 第三方url地址为：http://************
2025-07-04 11:22:19.397 +08:00 [INF] HTTP GET /api/Test/ClassDictTest responded 200 in 71.1484 ms
2025-07-04 11:22:21.373 +08:00 [INF] 第三方url地址为：http://************
2025-07-04 11:22:21.378 +08:00 [INF] HTTP GET /api/Test/ClassDictTest responded 200 in 10.9893 ms
2025-07-04 14:07:58.847 +08:00 [INF] ==>App Start..2025-07-04 14:07:58
2025-07-04 14:07:59.036 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-04 14:07:59.039 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-04 14:08:00.939 +08:00 [INF] ==>基础连接请求完成.
2025-07-04 14:08:59.175 +08:00 [INF] ==>App Start..2025-07-04 14:08:59
2025-07-04 14:08:59.345 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-04 14:08:59.349 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-04 14:09:00.819 +08:00 [INF] ==>基础连接请求完成.
2025-07-04 14:09:01.199 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-04 14:09:01.637 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-04 14:09:01.939 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-04 14:09:01.941 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-04 14:09:02.273 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-04 14:09:04.033 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-04 14:09:04.212 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-04 14:09:04.700 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-04 14:09:04.700 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-04 14:09:05.625 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-04 14:09:07.885 +08:00 [INF] ==>初始化完成..
2025-07-04 14:09:07.925 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-04 14:09:07.928 +08:00 [INF] 设备启用任务
2025-07-04 14:09:07.929 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-04 14:09:08.326 +08:00 [INF] 【SQL执行耗时:375.2949ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-04 14:09:08.481 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-04 14:09:08.496 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-04 14:09:08.497 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-04 14:09:08.498 +08:00 [INF] Hosting environment: Development
2025-07-04 14:09:08.498 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-04 14:09:28.383 +08:00 [INF] 第三方url地址为：http://************
2025-07-04 14:09:29.087 +08:00 [INF] HTTP GET /api/Test/ClassDictTest responded 200 in 1268.6856 ms
2025-07-04 14:09:29.089 +08:00 [INF] 【接口超时阀值预警】 [2f0ef84b909e31ab72fe8453d0c6c6f4]接口/api/Test/ClassDictTest,耗时:[1274]毫秒
2025-07-04 14:44:19.944 +08:00 [INF] ==>App Start..2025-07-04 14:44:19
2025-07-04 14:44:20.120 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-04 14:44:20.123 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-04 14:44:21.611 +08:00 [INF] ==>基础连接请求完成.
2025-07-04 14:44:21.991 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-04 14:44:22.452 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-04 14:44:22.766 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-04 14:44:22.768 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-04 14:44:23.098 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-04 14:44:23.670 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-04 14:44:23.775 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-04 14:44:24.219 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-04 14:44:24.220 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-04 14:44:25.378 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-04 14:45:05.413 +08:00 [INF] ==>App Start..2025-07-04 14:45:05
2025-07-04 14:45:05.589 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-04 14:45:05.592 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-04 14:45:06.983 +08:00 [INF] ==>基础连接请求完成.
2025-07-04 14:45:07.362 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-04 14:45:07.692 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-04 14:45:08.026 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-04 14:45:08.034 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-04 14:45:08.361 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-04 14:45:08.799 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-04 14:45:08.891 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-04 14:45:09.324 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-04 14:45:09.325 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-04 14:45:10.177 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-04 14:45:30.203 +08:00 [INF] ==>初始化完成..
2025-07-04 14:45:30.223 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-04 14:45:30.224 +08:00 [INF] 设备启用任务
2025-07-04 14:45:30.225 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-04 14:45:30.595 +08:00 [INF] 【SQL执行耗时:353.6407ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-04 14:45:30.727 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-04 14:45:30.739 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-04 14:45:30.740 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-04 14:45:30.740 +08:00 [INF] Hosting environment: Development
2025-07-04 14:45:30.741 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-04 14:50:16.950 +08:00 [INF] ==>App Start..2025-07-04 14:50:16
2025-07-04 14:50:17.117 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-04 14:50:17.121 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-04 14:50:18.553 +08:00 [INF] ==>基础连接请求完成.
2025-07-04 14:50:18.931 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-04 14:50:19.294 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-04 14:50:19.610 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-04 14:50:19.612 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-04 14:50:19.940 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-04 14:50:20.381 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-04 14:50:20.473 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-04 14:50:20.890 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-04 14:50:20.890 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-04 14:50:21.896 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-04 14:50:24.506 +08:00 [INF] ==>初始化完成..
2025-07-04 14:50:24.527 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-04 14:50:24.529 +08:00 [INF] 设备启用任务
2025-07-04 14:50:24.530 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-04 14:50:24.907 +08:00 [INF] 【SQL执行耗时:360.4007ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-04 14:50:25.043 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-04 14:50:25.055 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-04 14:50:25.057 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-04 14:50:25.057 +08:00 [INF] Hosting environment: Development
2025-07-04 14:50:25.057 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-04 15:14:04.311 +08:00 [INF] ==>App Start..2025-07-04 15:14:04
2025-07-04 15:14:04.490 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-04 15:14:04.493 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-04 15:14:06.010 +08:00 [INF] ==>基础连接请求完成.
2025-07-04 15:14:06.414 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-04 15:14:06.884 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-04 15:14:07.295 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-04 15:14:07.298 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-04 15:14:07.643 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-04 15:14:08.101 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-04 15:14:08.207 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-04 15:14:08.667 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-04 15:14:08.667 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-04 15:14:09.578 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-04 15:14:12.359 +08:00 [INF] ==>初始化完成..
2025-07-04 15:14:12.380 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-04 15:14:12.383 +08:00 [INF] 设备启用任务
2025-07-04 15:14:12.384 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-04 15:14:12.751 +08:00 [INF] 【SQL执行耗时:348.5079ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-04 15:14:12.898 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-04 15:14:12.913 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-04 15:14:12.915 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-04 15:14:12.915 +08:00 [INF] Hosting environment: Development
2025-07-04 15:14:12.916 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-04 15:14:55.112 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetEquipmentClassDictTree responded 200 in 3367.3075 ms
2025-07-04 15:14:55.130 +08:00 [INF] 【接口超时阀值预警】 [dd86696f55ad42234591f72cbfcbf05d]接口/api/EquipmentClassNew/GetEquipmentClassDictTree,耗时:[3389]毫秒
2025-07-04 15:55:19.975 +08:00 [INF] ==>App Start..2025-07-04 15:55:19
2025-07-04 15:55:20.158 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-04 15:55:20.161 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-04 15:55:21.806 +08:00 [INF] ==>基础连接请求完成.
2025-07-04 15:55:22.196 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-04 15:55:22.660 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-04 15:55:22.986 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-04 15:55:22.995 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-04 15:55:23.320 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-04 15:55:23.838 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-04 15:55:23.944 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-04 15:55:24.374 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-04 15:55:24.374 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-04 15:55:25.508 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-04 15:55:28.198 +08:00 [INF] ==>初始化完成..
2025-07-04 15:55:28.219 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-04 15:55:28.222 +08:00 [INF] 设备启用任务
2025-07-04 15:55:28.223 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-04 15:55:28.605 +08:00 [INF] 【SQL执行耗时:362.7742ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-04 15:55:28.765 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-04 15:55:28.779 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-04 15:55:28.781 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-04 15:55:28.781 +08:00 [INF] Hosting environment: Development
2025-07-04 15:55:28.782 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-04 15:55:39.640 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetEquipmentClassDictTree responded 401 in 285.9889 ms
2025-07-04 15:55:40.428 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetEquipmentClassDictTree responded 401 in 6.4667 ms
2025-07-04 15:55:40.952 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetEquipmentClassDictTree responded 401 in 1.3169 ms
2025-07-04 15:55:41.115 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetEquipmentClassDictTree responded 401 in 0.8375 ms
2025-07-04 15:55:41.259 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetEquipmentClassDictTree responded 401 in 0.7166 ms
2025-07-04 15:55:41.420 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetEquipmentClassDictTree responded 401 in 0.9021 ms
2025-07-04 15:55:41.551 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetEquipmentClassDictTree responded 401 in 0.7599 ms
2025-07-04 15:55:41.693 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetEquipmentClassDictTree responded 401 in 0.7030 ms
2025-07-04 15:55:41.850 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetEquipmentClassDictTree responded 401 in 0.8711 ms
2025-07-04 15:55:41.994 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetEquipmentClassDictTree responded 401 in 0.7518 ms
2025-07-04 15:55:42.147 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetEquipmentClassDictTree responded 401 in 0.6926 ms
2025-07-04 15:55:42.289 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetEquipmentClassDictTree responded 401 in 0.7754 ms
2025-07-04 15:55:42.434 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetEquipmentClassDictTree responded 401 in 0.7198 ms
2025-07-04 15:55:42.590 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetEquipmentClassDictTree responded 401 in 0.7092 ms
2025-07-04 15:55:42.753 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetEquipmentClassDictTree responded 401 in 0.7353 ms
2025-07-04 15:56:04.448 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetEquipmentClassDictTree responded 200 in 2674.0900 ms
2025-07-04 15:56:04.450 +08:00 [INF] 【接口超时阀值预警】 [7083502fba092ebb965a05ba51ff6ace]接口/api/EquipmentClassNew/GetEquipmentClassDictTree,耗时:[2674]毫秒
2025-07-04 15:56:52.213 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetEquipmentClassDictTree responded 200 in 46.8274 ms
2025-07-04 15:56:59.776 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetEquipmentClassDictTree responded 200 in 38.8032 ms
2025-07-04 15:57:23.638 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetEquipmentClassDictTree responded 200 in 58.4336 ms
