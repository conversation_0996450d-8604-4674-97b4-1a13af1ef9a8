﻿using Newtonsoft.Json;

namespace XH.H82.Models.Flow;

public class Property
{
    [JsonProperty("approveAction")]
    public object ApproveAction { get; set; }

    [JsonProperty("approverProperty")]
    public ApproverProperty ApproverProperty { get; set; }

    [JsonProperty("approveType")]
    public string ApproveType { get; set; }

    [JsonProperty("condition")]
    public object Condition { get; set; }

    [JsonProperty("copyApproverPropertys")]
    public string[] CopyApproverPropertys { get; set; }

    [JsonProperty("elementPerms")]
    public object ElementPerms { get; set; }

    [JsonProperty("ifSign")]
    public string IfSign { get; set; }
}