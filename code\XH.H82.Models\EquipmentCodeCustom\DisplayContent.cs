﻿using XH.H82.Models.Dtos;

namespace XH.H82.Models.EquipmentCodeCustom;

public class DisplayContent
{
    /// <summary>
    /// 显示内容中文({设备编码}_{设备名称})
    /// </summary>
    public string? DisplayContentString { get; set; }

    /// <summary>
    /// 展示内容对应的字段  （;拼接 如{EQUIPMENT_CODE}_{EQUIPMENT_NAME}）
    /// </summary>
    public string? DisplayContentCode { get; set;}

    /// <summary>
    /// 固定字段值修改显示（机构、科室、专业组、管理专业组、备案实验室、院区）
    /// </summary>
    public List<KeyValueDto>  FixedFieldDisplays  { get; set; } = new List<KeyValueDto>();

    public static List<KeyValueDto> CreatFixedFieldDisplays()
    {
        return new List<KeyValueDto>()
        {
            new ()
            {
                Key = "{HOSPITAL_NAME}",
                Value = ""
            },
            new ()
            {
                Key = "{AREA_NAME}",
                Value = ""
            }
            ,
            new ()
            {
                Key = "{LAB_NAME}",
                Value = ""
            }
            ,
            new ()
            {
                Key = "{MGROUP_NAME}",
                Value = ""
            }
            ,
            new ()
            {
                Key = "{UNIT_NAME}",
                Value = ""
            }
            ,
            new ()
            {
                Key = "{SMBL_LAB_NAME}",
                Value = ""
            }
        };
    }
}