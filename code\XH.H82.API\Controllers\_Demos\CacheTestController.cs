﻿using EasyCaching.Core;
using H.Utility;
using Microsoft.AspNetCore.Mvc;
using System.Diagnostics;
using XH.H82.IServices;
using XH.H82.Models.Entities;

namespace XH.H82.API.Controllers._Demos
{
    /// <summary>
    /// 缓存读写DEMO
    /// </summary>
    [Route("api/[controller]/[action]")]
    [ApiExplorerSettings(GroupName = "DEMO")]
    [NonController]
    public class CacheTestController : ControllerBase
    {

        private readonly IEasyCachingProvider _easyCachingDefaut;
        private readonly ILogger<CacheTestController> _logger;
        private readonly IDemoServices _demoserivce;
        private readonly IEasyCachingProvider _cacheRedis1;
        private readonly IEasyCachingProvider _cacheRedis2;
        private readonly IRedisCachingProvider _cacheRedisNative;

        /////////////////////////////
        //注意:IEasyCachingProvider默认使用最后一个注入的缓存提供器,在多个redis下容易混淆
        //多个redis场景下应该注入IEasyCachingProviderFactory并根据名称区分(名称来自S01配置)
        //如下例
        /////////////////////////////
        public CacheTestController(IEasyCachingProviderFactory easyCahcingFactory, IEasyCachingProvider easyCachingDefaut,
            ILogger<CacheTestController> logger, IDemoServices demoserivce)
        {
            //默认缓存提供器(最后一次注入)
            _easyCachingDefaut = easyCachingDefaut;
            _logger = logger;
            _demoserivce = demoserivce;
            //显示指定redis provider
            //redis1
            _cacheRedis1 = easyCahcingFactory.GetCachingProvider("S02");
            //reidis2
            _cacheRedis2 = easyCahcingFactory.GetCachingProvider("S03");
            //如果需要原生redis操作
            _cacheRedisNative = easyCahcingFactory.GetRedisProvider("S02");
        }
        [HttpPut]
        public IActionResult WriteToRedis(string key, string value)
        {
            _easyCachingDefaut.Set("[默认Provider写入]" + key, value, TimeSpan.FromMinutes(5));
            _cacheRedis1.Set("[写入Redis1]" + key, value, TimeSpan.FromMinutes(1));
            _cacheRedis2.Set("[写入Redis2]" + key, value, TimeSpan.FromMinutes(1));

            //原生Redis操作
            var res = _cacheRedisNative.SearchKeys("test");
            return Ok();
        }
        [HttpGet]
        public IActionResult GetValueFromRedis(string key)
        {
            var data = _easyCachingDefaut.Get<string>(key);

            if (data.HasValue)
            {
                return Ok(data.Value.ToResultDto());
            }
            else
            {
                return Ok("NO Value".ToResultDto());
            }
        }

        [HttpGet]
        public IActionResult SeriSerializerBenchMark()
        {
            List<TEST_START_TEMPLATE> largeList = new List<TEST_START_TEMPLATE>();
            for (int i = 0; i < 10000; i++)
            {
                largeList.Add(new TEST_START_TEMPLATE()
                {
                    NAME = "xxxsdfsdfsd手动阀手动阀胜多负少士大夫士大夫时尚大方士大夫撒旦发射点士大夫",
                    VALUE = i,
                    ID = "ID" + i.ToString()
                });
            }
            Stopwatch sw = new Stopwatch();
            sw.Start();
            _cacheRedis1.Set("TestLargeList", largeList, TimeSpan.FromMinutes(5));
            sw.Stop();
            _logger.LogInformation("写入耗时:" + sw.ElapsedMilliseconds.ToString() + "ms");
            //读取
            Stopwatch sw1 = new Stopwatch();
            sw1.Start();

            var data = _cacheRedis1.Get<List<TEST_START_TEMPLATE>>("TestLargeList");
            if (data.HasValue)
            {
                var res = data.Value;
                _logger.LogInformation("读取耗时:" + sw1.ElapsedMilliseconds.ToString() + "ms");
            }
            sw1.Stop();
            return Ok();
        }

        /// <summary>
        /// 缓存AOP写法
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult CacheAOPTest(string id)
        {
            var res = _demoserivce.GetDemoDateWithCache(id);
            return Ok(res.ToResultDto());
        }
    }
}
