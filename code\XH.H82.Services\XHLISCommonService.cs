﻿// ---------------------------------------------
//  @Author: chen<PERSON><PERSON><PERSON>
//  @Date:  2020-04-27 17:28
//  @Last Modified by: chenzen<PERSON>ua
//  @Last Modified time: 2020-04-27 17:28
//  @Description:调用5.0接口服务
// ---------------------------------------------

using System.ServiceModel;
using System.Xml.Linq;
using H.Utility;
using Microsoft.Extensions.Configuration;
using XH.H82.IServices;
using XH.H82.Models.ViewDtos;

namespace XH.H82.Services
{
    public class XHLISCommonService : IXHLISCommonService
    {

        //接口服务地址
        private readonly string _serviceAddress;
        public XHLISCommonService(IConfiguration configuration)
        {
            try
            {
                var binding = new BasicHttpBinding();
                binding.MaxReceivedMessageSize = 241000000;
                _serviceAddress = configuration["ConnectionStrings:XHLISService"];
               // _client = new XHLISServiceSoapClient(binding, new EndpointAddress(_serviceAddress));
            }
            catch (Exception e)
            {
                throw new BizException("统一用户登录服务初始化出错:" + e.Message);
            }
        }
        public ResultDto Login(string userInfoXml)
        {
            string param = userInfoXml;

            //string strXml = _client.ServiceClientAsync("4", param, "").Result;
            var ele = XElement.Parse(String.Empty);
            string ReturnCode = ele.Element("RETURN_CODE").Value;
            if (!param.Contains("<Type>EditPwd</Type>"))
            {
                if (new List<string> { "1", "3", "4", "5" }.Contains(ReturnCode))
                {
                    var userInfo = new UserInfo();
                    userInfo.LOGIN_ID = ele.Element("LOGIN_ID").Value;
                    userInfo.USER_NO = ele.Element("USER_ID").Value;
                    userInfo.USER_ID = ele.Element("USER_ID").Value;
                    userInfo.USER_NAME = ele.Element("USER_NAME").Value;
                    userInfo.HIS_ID = ele.Element("HIS_ID").Value;
                    userInfo.POWER = ele.Element("POWER").Value;
                    if (ele.Element("MODULE_LIST") != null)
                    {
                        userInfo.moduleList = ele.Element("MODULE_LIST").Value;
                    }
                    else
                    {
                        userInfo.moduleList = "";
                    }
                    if (ele.Element("HOSPITAL_ID") != null)
                    {
                        userInfo.HOSPITAL_ID = ele.Element("HOSPITAL_ID").Value;
                        if (ele.Element("HOSPITAL_CNAME") != null)
                        {
                            userInfo.HOSPITAL_NAME = ele.Element("HOSPITAL_CNAME").Value;
                        }

                    }
                    return new ResultDto() { data = userInfo };
                }
                else
                {
                    return new ResultDto() { success = false, msg = ele.Element("RETURN_MSG").Value };
                }
            }
            else
            {
                if (ReturnCode == "1")
                {
                    return new ResultDto() { success = true, msg = ele.Element("RETURN_MSG").Value };
                }
                else
                {
                    return new ResultDto() { success = false, msg = ele.Element("RETURN_MSG").Value };
                }

            }
        }
        public ResultDto Login(string LoginId, string LoginPwd )
        {
            string param = string.Format("<UserInfo><Type>CheckPwd</Type><LoginId>{0}</LoginId><LoginPwd>{1}</LoginPwd></UserInfo>",
                LoginId, LoginPwd);

           // string strXml = _client.ServiceClientAsync("4", param, "").Result;
            var ele = XElement.Parse(String.Empty);
            string ReturnCode = ele.Element("RETURN_CODE").Value;
            if (new List<string> { "1", "3", "4", "5" }.Contains(ReturnCode))
            {
                var userInfo = new UserInfo();
                userInfo.LOGIN_ID = LoginId;
                userInfo.USER_NO = ele.Element("USER_ID").Value;
                userInfo.USER_NAME = ele.Element("USER_NAME").Value;
                userInfo.HIS_ID = ele.Element("HIS_ID").Value;
                userInfo.POWER = ele.Element("POWER").Value;
                if (ele.Element("MODULE_LIST") != null)
                {
                    userInfo.moduleList = ele.Element("MODULE_LIST").Value;
                }
                else
                {
                    userInfo.moduleList = "";
                }
                if (ele.Element("HOSPITAL_ID") != null)
                {
                    userInfo.HOSPITAL_ID = ele.Element("HOSPITAL_ID").Value;
                }
                return new ResultDto() { data = userInfo };
            }
            else
            {
                return new ResultDto() { success = false, msg = ele.Element("RETURN_MSG").Value };
            }
        }
    }
}