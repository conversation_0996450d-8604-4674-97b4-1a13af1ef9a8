namespace XH.H82.Models.Dtos.Tim;


/// <summary>
/// 记事务录单
/// </summary>
public class TransactionForm
{
    /// <summary>
    /// 记录单ID
    /// </summary>
    public string FORM_ID { get; set; }

    /// <summary>
    /// 记录单名称
    /// </summary>
    public string FORM_NAME { get; set; }

    /// <summary>
    /// 记录单分类Id  
    /// </summary>
    public string CLASS_ID { get; set; }

    /// <summary>
    /// 记录单版本id
    /// </summary>
    public string  FORM_VER_ID { get; set; }
    
    /// <summary>
    /// 记录单分名称
    /// </summary>
    public string CLASS_NAME { get; set; }
    
    /// <summary>
    /// 事务项主体ID/设备id
    /// </summary>
    public string WORK_MAINID { get; set; }

    public List<TransactionItem> TransactionItems { get; set; } = new List<TransactionItem>();

}

/// <summary>
/// 事务项
/// </summary>
public class TransactionItem
{
    /// <summary>
    /// pk
    /// </summary>
    public string WORK_ID { get; set; }
    /// <summary>
    /// 事务名称
    /// </summary>
    public string WORK_NAME { get; set; }   
    /// <summary>
    /// 计划类型 1周保养2月保养3季保养4年保养
    /// </summary>
    public string WORK_PLAN_TYPE { get; set; }

    /// <summary>
    /// 事务计划类型
    /// </summary>
    public string TRANSACTION_ITEM_CLASS { get; set; }

    /// <summary>
    /// 记录单版本id
    /// </summary>
    public string FORM_VER_ID { get; set; }
}