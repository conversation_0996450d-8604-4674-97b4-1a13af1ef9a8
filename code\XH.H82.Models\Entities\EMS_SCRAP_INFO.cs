﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using H.Utility.SqlSugarInfra;
using SqlSugar;

namespace XH.H82.Models.Entities
{
    [DBOwner("XH_OA")]
    public class EMS_SCRAP_INFO
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string SCRAP_ID { get; set; }
        public string HOSPITAL_ID { get; set; }
        public string EQUIPMENT_ID { get; set; }
        public DateTime? SCRAP_DATE { get; set; }
        public string SCRAP_CAUSE { get; set; }
        public string OPER_PERSON { get; set; }
        public string OPER_PERSON_ID { get; set; }
        public DateTime? OPER_TIME { get; set; }
        public string SCRAP_STATE { get; set; }
        public string FIRST_RPERSON { get; set; }
        public DateTime? FIRST_RTIME { get; set; }
        public string LAST_MPERSON { get; set; }
        public DateTime? LAST_MTIME { get; set; }
        public string REMARK { get; set; }
        public string EXAMINE_PERSON { get; set; }
        public string EXAMINE_PERSON_ID { get; set; }
        public DateTime? EXAMINE_DATE { get; set; }
        public string EXAMINE_OPINION { get; set; }
        public string APPROVE_PERSON { get; set; }
        public string APPROVE_PERSON_ID { get; set; }
        public DateTime? APPROVE_DATE { get; set; }
        public string APPROVE_OPINION { get; set; }
        public string APPLY_STATE { get; set; }
        public string APPLY_TYPE { get; set; }
        public string IF_RE_APPLY { get; set; }

        public string FILE_PATH { get; set; }
    }
}
