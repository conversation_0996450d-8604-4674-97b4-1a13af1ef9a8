﻿using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using XH.H82.IServices;
using XH.H82.Models.Dtos;
using XH.H82.Models.SugarDbContext;
using XH.LAB.UTILS.Interface;
using XH.LAB.UTILS.Models;

namespace XH.H82.Services
{
    public class XhCurrencyService : IXhCurrencyService
    {
        private readonly ILogger<CurrencyService> _logger;
        private readonly ISqlSugarUow<SugarDbContext_Master> _sqlSugarUow;
        private readonly IAuthorityService2 _authorityService;
        private readonly IHttpContextAccessor _httpContext;
        public XhCurrencyService(ISqlSugarUow<SugarDbContext_Master> sqlSugarUow, ILogger<CurrencyService> logger, IAuthorityService2 authorityService, IHttpContextAccessor httpContext)
        {
            _sqlSugarUow = sqlSugarUow;
            _logger = logger;
            _authorityService = authorityService;
            _httpContext = httpContext;
            //ExecutingChangeSqlHelper.ExecutingChangeSql(_sqlSugarUow);
        }

        public PersonTreeDto GetScrapStopPersonList(string areaId, string hospitalId)
        {
            var authorityContext = new AuthorityContext(_sqlSugarUow, _authorityService);
            authorityContext.SetUser(_httpContext.HttpContext.User.ToClaimsDto(),null,areaId);
            //停用、报废设备的审核权限
            string scrapStopPermissionID = "H8256004";
            var users = authorityContext.GetPermissionUsers(scrapStopPermissionID);

            List<string> userGroupList = users.Select(u => u.DEPT_CODE).ToList();

            var groupList = _sqlSugarUow.Db.Queryable<SYS6_INSPECTION_PGROUP>()
                .Where(pgroup => pgroup.PGROUP_STATE == "1" && userGroupList.Contains(pgroup.PGROUP_ID))
                .ToList();

            var personTreeDto = new PersonTreeDto();
            var mgroupList = new List<MGROUP_LIST>();
            //原来代码似乎把pgroup和mgroup混淆了，dto模型已定，这里只能将错就错
            groupList.ForEach(item =>
            {
                mgroupList.Add(new MGROUP_LIST()
                {
                    MGROUP_ID = item.PGROUP_ID,
                    MGROUP_NAME = item.PGROUP_NAME
                });
            });
            for (int i = 0; i < mgroupList.Count; i++)
            {
                var personInfoList = new List<PERSON_LIST>();
                users.ForEach(item =>
                {
                    if (item.DEPT_CODE == mgroupList[i].MGROUP_ID)
                    {
                        personInfoList.Add(new PERSON_LIST()
                        {
                            PERSON_ID = item.USER_NO,
                            PERSON_NAME = item.HIS_NAME
                        });
                    }
                    mgroupList[i].PERSON_LIST = personInfoList;
                });
            }
            personTreeDto.MGROUP_LIST = mgroupList;
            return personTreeDto;
        }
    }
}
