﻿using MathNet.Numerics;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XH.H82.Models.Card;
using XH.H82.Models.DeviceRelevantInformation.Dto;
using XH.H82.Models.DeviceRelevantInformation.Enum;
using XH.H82.Models.Entities.InkScreen;
using XH.LAB.UTILS.Models;

namespace XH.H82.IServices.DeviceDataRefresh
{
    public interface IInkScreenService
    {

        List<GroupDto> GetPgroups(string? labId);
        void InkScreenDeviceLampControl(string inkScreenId, LightStateEnum lightStateEnum);


        List<InkScreenDevice> GetAllInkScreen();

        /// <summary>
        /// 墨水屏绑定信息列表
        /// </summary>
        /// <param name="isBand">是否绑定 </param>
        /// <param name="pGourpId">检验专业组id</param>
        /// <param name="mac">mac地址</param>
        /// <param name="hosptialId">医疗机构id</param>
        /// <returns></returns>
        /// <returns></returns>
        List<InkScreenBindDto> GetInkScreenBindEquipments(string hosptialId, bool? isBand, string? pGourpId, string? mac);

        /// <summary>
        /// 添加一个墨水屏设备西信息
        /// </summary>
        /// <param name="Mac">mac地址</param>
        /// <param name="InkScreenName">墨水屏名称</param>
        /// <param name="hosptialId">医疗机构id</param>
        /// <param name="Remark">备注</param>
        void AddNewInkScreen(string mac, string inkScreenName, string hosptialId, string? remark);

        /// <summary>
        /// 更新墨水屏设备信息
        /// </summary>
        /// <param name="inkScreenId"></param>
        /// <param name="remark"></param>
        void UpdateInkScreenInfo(string inkScreenId, string? remark);

        /// <summary>
        /// 删除墨水屏设备信息
        /// </summary>
        /// <param name="InkScreenId"></param>
        void DeleteInkScreen(string InkScreenId);

        /// <summary>
        /// 绑定设备
        /// </summary>
        /// <param name="inkScreenId"></param>
        /// <param name="equipmentId"></param>
        void BandEquipment(string inkScreenId, string equipmentId);

        /// <summary>
        /// 检查电量
        /// </summary>
        void CheckPower();

        /// <summary>
        /// 获取设备墨水屏列表
        /// </summary>
        /// <param name="lightState">亮灯状态</param>
        /// <param name="lineState">在线状态</param>
        /// <param name="pGourpId">检验专业组id</param>
        /// <param name="equipmentState">设备状态</param>
        /// <param name="macOrEquipmentCode">mac或者设备代号</param>
        /// <param name="hosptialId">医疗机构id</param>
        /// <returns></returns>
        List<InkScreenDto> GetInkScreens(string? hosptialId, LightStateEnum? lightState, LineStateEnum? lineState, EquipmentStateEnum? equipmentState, string? pGourpId, string? macOrEquipmentCode);

        /// <summary>
        /// 查询设备的报警信记录
        /// </summary>
        /// <param name="equipmentId"></param>
        /// <param name="warnMsg"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        List<WarnRecordDto> GetEquipmentWarnRecords(string? equipmentId, string? warnMsg, DateTime? startTime, DateTime? endTime);



    }
}
