namespace XH.H82.Models.Dtos.Certificate;

/// <summary>
/// 创建设备正式信息输入
/// </summary>
public class EquipmentCertificateInput
{
    /// <summary>
    /// 证书类型ID（DATA_ID ）
    /// </summary>
    public string CretificateType { get; set; }

    /// <summary>
    /// 过期时间
    /// </summary>
    public DateTime? ExpiryDate { get; set; }
    /// <summary>
    /// 发证时间
    /// </summary>
    public DateTime CerDate { get; set; }
    /// <summary>
    /// 提醒时间
    /// </summary>
    public DateTime? CerWanrDate { get; set; }
}