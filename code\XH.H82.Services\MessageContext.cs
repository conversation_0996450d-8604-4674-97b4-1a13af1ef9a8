﻿using H.BASE;
using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using Newtonsoft.Json.Linq;
using XH.H82.IServices;
using XH.H82.Models.Entities;
using XH.H82.Models.Entities.InkScreen;
using XH.H82.Models.Entities.LIS6;
using XH.H82.Models.Message;
using XH.LAB.UTILS.H05Message;
using XH.LAB.UTILS.Interface;
using XH.LAB.UTILS.Models;

namespace XH.H82.Services;

public class MessageContext
{
    private readonly  IMessageService _messageService;
    private readonly  ISqlSugarUow _dbContext;
    private readonly  IBaseDataServices _baseDataServices;
    
    
    public MessageContext(IMessageService messageService ,ISqlSugarUow dbContext, IBaseDataServices baseDataServices)
    {
        _messageService = messageService;
        _dbContext = dbContext;
        _baseDataServices = baseDataServices;
    }

    public void SendInkScreenMsg(string mac)
    {
        var  ink =  _dbContext.Db.Queryable<EMS_INKSCREEN_INFO>().First(x => x.MAC == mac);
        if (ink is null)
        {
            return;
        }
        var tokenInfo = _baseDataServices.GetCustomTokenInfo(AppSettingsProvider.CurrModuleId);
        var tokenRsp = _baseDataServices.GetCustomToken(tokenInfo);
        if (tokenRsp.success)
        {
            var token = JObject.Parse(tokenRsp.data.ToString())["AccessToken"].ToString();
           _messageService.SetToken(token);
        }
        else
        {
            throw new BizException($"{tokenRsp.msg}");
        }
        if (GetMeassageByDataId(ink.INKSCREEN_ID))
        {
            return;
        }
        var equipment = _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>().First(x => x.EQUIPMENT_ID == ink.EQUIPMENT_ID);
        if (equipment is null)
        {
            return;
        }
        if (equipment.KEEP_PERSON.IsNullOrEmpty())
        {
            return;
        }
        var receiver = GetUserByHisName("李影");
        if (receiver is null)
        {
            return;
        }
        if (ink.LAST_MPERSON.IsNullOrEmpty())
        {
            return;
        }
        var sender = GetUserByHisName(ink.LAST_MPERSON);
        if (sender is null)
        {
            return;
        }
        var msg = new NewMessage(ink.INKSCREEN_ID, "墨水屏电量过低", "M130107");//M130142
        msg.SetSender("H82",$"{sender.LOGID}_{sender.USERNAME}", $"设备：{equipment.EQUIPMENT_NAME}所绑定的墨水屏：{ink.INKSCREEN_NAME}的电量过低，请及时处理！");
        msg.SetReceiver(receiver.HOSPITAL_ID, receiver.LAB_ID, "A000", AppSettingsProvider.CurrModuleId, receiver.USER_NO, $"{receiver.LOGID}_{receiver.USERNAME}");
        var rsp =  _messageService.SendReminderMessage(new List<BaseMessage>() { msg });
        if (!rsp.success)
        {
            throw new BizException(rsp.msg);
        }
    }

    /// <summary>
    /// 查询指定的消息是否已经发送
    /// </summary>
    /// <param name="dataId"></param>
    /// <returns></returns>
    public bool GetMeassageByDataId(string dataId)
    {
        var result =  _dbContext.Db.Queryable<LIS6_MSG_INFO>().Where(x => x.MsgCorrid == dataId)
            .Where(x => x.MsgState == "0")
            .Where(x=>x.MsgClass == "M13")
            .Where(x=>x.MsgType == "M130107")
            .Count() > 0 ;
        return result;
    }

    private SYS6_USER? GetUserByHisName(string hisName)
    {
        var userNameAndLogId = hisName.Split('_');
        var logId = userNameAndLogId[0];
        if (userNameAndLogId.Length<2)
        {
            var person = _dbContext.Db.Queryable<SYS6_USER>()
                .Where(x=>x.USERNAME == logId)
                .First();
            if (person is null)
            {
                return null;
            }
            return person;
        }
        else
        {
            var userName = userNameAndLogId[1];
            var person = _dbContext.Db.Queryable<SYS6_USER>()
                .Where(x => x.LOGID == logId)
                .Where(x=>x.USERNAME == userName)
                .First();
            if (person is null)
            {
                return null;
            }
            return person;
        }
    }
}