﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;
using H.Utility.SqlSugarInfra;
using SqlSugar;

namespace XH.H82.Models.Entities.Common
{
    [DBOwner("XH_SYS")]
    public class LIS5_SAMPLE_INFO_DICT
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string SETUP_ID { get; set; }
        public string? HOSPITAL_ID { get; set; }
        public string? FIELD_CODE { get; set; }
        public string? FIELD_NAME { get; set; }
        public string? FIELD_CNAME { get; set; }
        public string? FIELD_SORT { get; set; }
        public string? FIELD_FORMAT { get; set; }
        public string? DEFAULT_VALUE { get; set; }
        public string? FIELD_HEIGHT { get; set; }
        public string? FIELD_FONT_SIZE { get; set; }
        public string? FIELD_FONT_STYLE { get; set; }
        public string? FIELD_COLOR { get; set; }
        public string? TITLE_COLOR { get; set; }
        public string? IF_READ_ONLY { get; set; }
        public string? IF_MEMORY { get; set; }
        public string? DATA_CLASS_ID { get; set; }
        public string? DROP_DOWN { get; set; }
        public string? SUFFIX_EVENT { get; set; }
        public string? FIELD_STATE { get; set; }
        public string? FIRST_RPERSON { get; set; }
        public string? FIRST_RTIME { get; set; }
        public string? LAST_MPERSON { get; set; }
        public string? LAST_MTIME { get; set; }
        public string? REMARK { get; set; }
        public string? FIELD_TYPE { get; set; }
        public string? MODULE_ID { get; set; }
        public string? SETUP_CLASS { get; set; }
        public string? IF_NEED_INPUT { get; set; }
        public string? IF_REPLACE_FIELD { get; set; }
        public string? LOCAL_EDIT_FLAG { get; set; }

    }
}
