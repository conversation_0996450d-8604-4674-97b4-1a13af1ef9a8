using System;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using SqlSugar;
using H.Utility.SqlSugarInfra;

namespace XH.H82.Models.Entities.Common
{
    [DBOwner("XH_SYS")]
    [SugarTable("SYS6_SERVER_INFO")]
    public class SYS6_SERVER_INFO
    {
        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Column("SERVER_ID")]
        [Required(ErrorMessage = "不允许为空")]
        [StringLength(20, ErrorMessage = "SERVER_ID长度不能超出20字符")]
        [Unicode(false)]
        public string SERVER_ID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("SERVER_CNAME")]
        [StringLength(50, ErrorMessage = "SERVER_CNAME长度不能超出50字符")]
        [Unicode(false)]
        public string? SERVER_CNAME { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("SERVER_PERSON")]
        [StringLength(100, ErrorMessage = "SERVER_PERSON长度不能超出100字符")]
        [Unicode(false)]
        public string? SERVER_PERSON { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("SERVER_ADDRESS")]
        [StringLength(100, ErrorMessage = "SERVER_ADDRESS长度不能超出100字符")]
        [Unicode(false)]
        public string? SERVER_ADDRESS { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("SERVER_IP")]
        [StringLength(50, ErrorMessage = "SERVER_IP长度不能超出50字符")]
        [Unicode(false)]
        public string? SERVER_IP { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("SERVER_STATE")]
        [StringLength(50, ErrorMessage = "SERVER_STATE长度不能超出50字符")]
        [Unicode(false)]
        public string? SERVER_STATE { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("UPDATE_FLAG")]
        [StringLength(10, ErrorMessage = "UPDATE_FLAG长度不能超出10字符")]
        [Unicode(false)]
        public string? UPDATE_FLAG { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("FIRST_RPERSON")]
        [StringLength(50, ErrorMessage = "FIRST_RPERSON长度不能超出50字符")]
        [Unicode(false)]
        public string? FIRST_RPERSON { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("FIRST_RTIME")]
        [Unicode(false)]
        public DateTime? FIRST_RTIME { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("LAST_MPERSON")]
        [StringLength(50, ErrorMessage = "LAST_MPERSON长度不能超出50字符")]
        [Unicode(false)]
        public string? LAST_MPERSON { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("LAST_MTIME")]
        [Unicode(false)]
        public DateTime? LAST_MTIME { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("REMARK")]
        [StringLength(200, ErrorMessage = "REMARK长度不能超出200字符")]
        [Unicode(false)]
        public string? REMARK { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("HOSPITAL_ID")]
        [StringLength(20, ErrorMessage = "HOSPITAL_ID长度不能超出20字符")]
        [Unicode(false)]
        public string? HOSPITAL_ID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("SERVER_CLASS")]
        [StringLength(50, ErrorMessage = "SERVER_CLASS长度不能超出50字符")]
        [Unicode(false)]
        public string? SERVER_CLASS { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("OS_VER")]
        [StringLength(200, ErrorMessage = "OS_VER长度不能超出200字符")]
        [Unicode(false)]
        public string? OS_VER { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("OS_BITS")]
        [StringLength(20, ErrorMessage = "OS_BITS长度不能超出20字符")]
        [Unicode(false)]
        public string? OS_BITS { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("OS_BOOT_TIME")]
        [StringLength(20, ErrorMessage = "OS_BOOT_TIME长度不能超出20字符")]
        [Unicode(false)]
        public string? OS_BOOT_TIME { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("DISK_INFO")]
        [StringLength(200, ErrorMessage = "DISK_INFO长度不能超出200字符")]
        [Unicode(false)]
        public string? DISK_INFO { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("MEMORY_INFO")]
        [StringLength(100, ErrorMessage = "MEMORY_INFO长度不能超出100字符")]
        [Unicode(false)]
        public string? MEMORY_INFO { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("CPU_INFO")]
        [StringLength(200, ErrorMessage = "CPU_INFO长度不能超出200字符")]
        [Unicode(false)]
        public string? CPU_INFO { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("OS_CLASS")]
        [StringLength(20, ErrorMessage = "OS_CLASS长度不能超出20字符")]
        [Unicode(false)]
        public string? OS_CLASS { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("SERVER_SORT")]
        [StringLength(20, ErrorMessage = "SERVER_SORT长度不能超出20字符")]
        [Unicode(false)]
        public string? SERVER_SORT { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("INSTANCE_ID")]
        [StringLength(20, ErrorMessage = "INSTANCE_ID长度不能超出20字符")]
        [Unicode(false)]
        public string? INSTANCE_ID { get; set; }


    }
}
