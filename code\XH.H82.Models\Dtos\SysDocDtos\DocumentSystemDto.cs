﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.Dtos.SysDocDtos
{
    /// <summary>
    /// 链接文件系统弹窗信息模型
    /// </summary>
    public class DocumentSystemDto
    {
        /// <summary>
        /// 科室/专业组结构节点
        /// </summary>
        public List<LaboratoryOrManagementProfessionalGroupNode> Nodes { get; set; }

        /// <summary>
        /// 列表文件数据
        /// </summary>
        public List<DocInfoDto> Files { get; set; }

        public DocumentSystemDto(List<LaboratoryOrManagementProfessionalGroupNode>? nodes, List<DocInfoDto>? files)
        {
            Nodes = nodes ?? new List<LaboratoryOrManagementProfessionalGroupNode>();
            Files = files ?? new List<DocInfoDto>();
        }

    }
}
