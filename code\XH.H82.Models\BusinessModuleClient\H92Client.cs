﻿using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using RestSharp;
using Serilog;
using System.ComponentModel;
using System.Diagnostics;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;
using XH.H82.Models.BusinessModuleClient.Dto;
using XH.H82.Models.DeviceRelevantInformation;
using XH.H82.Models.DeviceRelevantInformation.Enum;
using XH.H82.Models.SugarDbContext;

namespace XH.H82.Models.BusinessModuleClient
{
    public class H92Client
    {
        private string addressH92 { get; set; } = "";
        private IHttpContextAccessor _httpContext;
        public H92Client(string ip, IHttpContextAccessor httpContext)
        {
            if (ip.IsNullOrEmpty())
            {
                throw new ArgumentNullException("addressH92 为空");
            }
            addressH92 = ip;
            _httpContext = httpContext;
        }
        public ResultDto H92ClientGet<T>(string url, T requestBody = default(T), bool isNeedToken = true)
        {
            if (addressH92.IsNullOrEmpty())
            {
                throw new ArgumentNullException("addressH92 为空");
            }

            using RestClient client = new RestClient(
                new RestClientOptions
                {
                    RemoteCertificateValidationCallback = (object sender, X509Certificate? certificate, X509Chain? chain, SslPolicyErrors sslPolicyErrors) => true,
                    BaseUrl = new Uri(addressH92),
                    ThrowOnAnyError = true,

                });

            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            string value = _httpContext.HttpContext.Request.Headers.Authorization.ToString();
            RestRequest request = new RestRequest(url);
            if (requestBody != null)
            {
                request.AddBody(requestBody);
            }
            if (isNeedToken)
            {
                request.AddHeader("Authorization", value);
            }
            try
            {
                RestResponse<ResultDto> restResponse = client.ExecuteGet<ResultDto>(request);
                stopwatch.Stop();
                Log.ForContext("elapsed", stopwatch.ElapsedMilliseconds).Information($"调用H92模块[{url}],耗时:{stopwatch.ElapsedMilliseconds}ms");
                if (restResponse.IsSuccessful && restResponse.Data.success)
                {
                    return restResponse.Data;
                }

                Log.Error($"调用H92模块[{url}]发生错误:{restResponse.ErrorException}");
                throw new BizException($"调用H92模块[{url}]发生错误:{restResponse.ErrorException}", restResponse.ErrorException);
            }
            catch (Exception ex)
            {
                Log.Error($"调用H92模块[{url}]发生错误:{ex}");
                throw new BizException(ex.Message);
            }
        }

        public H92Client()
        {
            
        }

        public List<CertificatTypeDto> GetCertificatTypes(ISqlSugarUow<SugarDbContext_Master> _dbContext)
        {
            var resuult = new List<CertificatTypeDto>();
            var certypes = _dbContext.Db.GetSimpleClient<OA_BASE_DATA>().AsQueryable()
                .Where(x => x.CLASS_ID == "资质证书类型")
                .Where(x => x.STATE_FLAG == "1")
                .ToList();

            foreach (var item in certypes)
            {
                resuult.Add(new CertificatTypeDto()
                {
                    Key = item.DATA_ID,
                    Value = item.DATA_NAME
                });
            }
            return resuult;
        }


        public List<CertificatDto> GetCompanyCerlist(string id, ISqlSugarUow<SugarDbContext_Master> _dbContext)
        {
            try
            {
                var creListQuery = _dbContext.Db.GetSimpleClient<SMS_COMPANY_FILE>()
                    .AsQueryable()
                    .Where(x=>x.COMPANY_ID == id)
                    .OrderByDescending(x => x.FILE_EXPIRY_DATE)
                    .Where(x => x.FILE_STATE == "1");
                
                var creList = creListQuery.ToList();

                var creTypes = GetCertificatTypes(_dbContext);

                var result = new List<CertificatDto>();

                foreach (var item in creList)
                {
                    var creType = creTypes.FirstOrDefault(x => x.Key == item.FILE_TYPE);
                    var dto = new CertificatDto();
                    dto.Id = item.FILE_ID;
                    dto.CompanyId = item.COMPANY_ID;
                    dto.FileTypeName = creType is null ? "" : creType.Value;
                    dto.FileName = item.FILE_NAME;
                    dto.FilePath = item.FILE_PATH;
                    dto.FileExpiryDate = item.FILE_EXPIRY_DATE;
                    dto.CerWarnDate = item.CER_WARN_DATE;
                    dto.StateCer = GetCerStatae(item).ToString();
                    dto.OperPerson = item.LAST_MPERSON;
                    dto.FileRecord = item.FILE_RECORD;
                    dto.UploadFileId = item.UPLOAD_FILE_ID;
                    dto.FilePreviewPath = item.FILE_PREVIEW_PATH;
                    dto.FileSuffix = item.FILE_SUFFIX;
                    dto.DocType = item.DOC_TYPE;
                    dto.cerData = item.CER_DATA;
                    result.Add(dto);
                }
                return result;
            }
            catch (Exception ex)
            {
                Log.Error($"{ex.Message}");
                return new List<CertificatDto>();
            }
        }
        
        /// <summary>
        /// 获取供应商的证书列表
        /// </summary>
        /// <returns></returns>
        public List<CertificatDto> GetCompanyCerlist(ISqlSugarUow<SugarDbContext_Master> _dbContext)
        {
            try
            {
                var creListQuery = _dbContext.Db.GetSimpleClient<SMS_COMPANY_FILE>()
                                        .AsQueryable()
                                        .OrderByDescending(x => x.FILE_EXPIRY_DATE)
                                        .Where(x => x.FILE_STATE == "1");
                
                var creList = creListQuery.ToList();

                var creTypes = GetCertificatTypes(_dbContext);

                var result = new List<CertificatDto>();

                foreach (var item in creList)
                {
                    var creType = creTypes.FirstOrDefault(x => x.Key == item.FILE_TYPE);
                    var dto = new CertificatDto();
                    dto.CompanyId = item.COMPANY_ID;
                    dto.FileTypeName = creType is null ? "" : creType.Value;
                    dto.FileName = item.FILE_NAME;
                    dto.FileExpiryDate = item.FILE_EXPIRY_DATE;
                    dto.CerWarnDate = item.CER_WARN_DATE;
                    dto.StateCer = GetCerStatae(item).ToString();
                    dto.OperPerson = item.LAST_MPERSON;
                    result.Add(dto);
                }
                return result;
            }
            catch (Exception ex)
            {
                Log.Error($"{ex.Message}");
                return new List<CertificatDto>();
            }
        }
        private STATE_CER? GetCerStatae(SMS_COMPANY_FILE data)
        {
            if (data is null)
            {
                return null;
            }
            if (data.FILE_STATE == "0")
            {
                return STATE_CER.INVALID;
            }
            if (!data.FILE_EXPIRY_DATE.HasValue)
            {
                return STATE_CER.ACTIVE;
            }

            var result = data.FILE_EXPIRY_DATE < DateTime.Now.Date ? STATE_CER.EXPIRE : data.CER_WARN_DATE >= DateTime.Now.Date ? STATE_CER.ACTIVE : STATE_CER.PRE_EXPIRE;
            return result;
        }
    }
    public class CertificatDto
    {
        [JsonProperty("FILE_ID")]
        public string Id { get; set; }
        
        [JsonProperty("COMPANY_ID")]
        public string CompanyId { get; set; }

        [JsonProperty("FILE_TYPE_name")]
        public string FileTypeName { get; set; }

        [JsonProperty("FILE_NAME")]
        public string FileName { get; set; }
        [JsonProperty("FILE_PATH")]
        public string FilePath { get;set; }
        [JsonProperty("CER_DATA")]
        public DateTime? cerData { get; set; }

        [JsonProperty("FILE_EXPIRY_DATE")]
        public DateTime? FileExpiryDate { get; set; }

        [JsonProperty("CER_WARN_DATE")]
        public DateTime? CerWarnDate { get; set; }

        [JsonProperty("STATE_CER")]
        public string StateCer { get; set; }
        
        [JsonProperty("FILE_PREVIEW_PATH")]
        public string FilePreviewPath { get; set; }

        [JsonProperty("FILE_SUFFIX")]
        public string FileSuffix { get; set; }

        [JsonProperty("DOC_TYPE")]
        public string DocType { get; set; }
        

        [JsonProperty("LAST_MPERSON")]
        public string OperPerson { get; set; }
        
        [JsonProperty("UPLOAD_FILE_ID")]
        public string? UploadFileId { get; set; }
        
        [JsonProperty("FILE_RECORD")]
        public string?  FileRecord { get; set; }

        /// <summary>
        /// 获取供应商证书附件
        /// </summary>
        /// <returns></returns>
        public List<FileRecordInfo> GetFileRecords()
        {
            var result = new List<FileRecordInfo>();
            if (FileRecord.IsNullOrEmpty())
            {
                if (UploadFileId.IsNotNullOrEmpty())
                {
                    var filePath = FilePath.IsNotNullOrEmpty() ? this.FilePath.Replace("/S54", "") :"";
                    var fileSuffix = FileSuffix ?? Path.GetExtension(filePath);
                    result.Add(new ()
                    {
                        UploadFileId = this.UploadFileId,
                        FileName = this.FileName,
                        Path = filePath,
                        PreviewPath = this.FilePreviewPath.IsNotNullOrEmpty() ?  this.FilePreviewPath.Replace("/S54","") : "" ,
                        FileSuffix = fileSuffix,
                        DocType = this.DocType ?? fileSuffix switch {
                            ".png"=>"IMG",
                            ".jpn"=>"IMG",
                            ".pdf"=>"PDF",
                            _=>"IMG"
                        } 
                    });
                }
                
                return result;
            }
            else
            {
                var fileRecords = JsonConvert.DeserializeObject<List<FileRecordInfo>>(FileRecord!);
                fileRecords.ForEach(x =>
                {
                    var filePath = x.Path.IsNotNullOrEmpty() ? x.Path.Replace("/S54", "") :"";
                    var fileSuffix = FileSuffix ?? Path.GetExtension(filePath);
                    x.Path = filePath;
                    x.PreviewPath = x.PreviewPath.IsNotNullOrEmpty() ? x.PreviewPath.Replace("/S54", "") : "";
                    x.FileSuffix = fileSuffix;
                    x.DocType = x.DocType ?? fileSuffix switch
                    {
                        ".png" => "IMG",
                        ".jpn" => "IMG",
                        ".pdf" => "PDF",
                        _ => "IMG"
                    };
                });
                result.AddRange(fileRecords);
                return result;
            }
        }

        public CertificateWarn GetCertificatStateInfo(string companyName = "")
        {
            switch (StateCer)
            {
                case "ACTIVE":
                    return new CertificateWarn()
                    {
                        CompanyId = CompanyId,
                        CompanyName = companyName,
                        CertificateName = FileName,
                        CertificateType = FileTypeName,
                        CertificateStatus = CertificatStatusEnum.Active,
                        CertificateDate = 0,
                        CertificateState = TaskStatusEnum.NoReminder,
                        UpdatePerson = OperPerson,
                    };
                case "PRE_EXPIRE":

                    var preExpireDays = (int)((FileExpiryDate - DateTime.Now).Value.TotalDays);
                    return new CertificateWarn()
                    {
                        CompanyId = CompanyId,
                        CompanyName = companyName,
                        CertificateName = FileName,
                        CertificateType = FileTypeName,
                        CertificateStatus = CertificatStatusEnum.PreExpire,
                        CertificateDate = preExpireDays,
                        CertificateState = TaskStatusEnum.Warning,
                        UpdatePerson = OperPerson,

                    };
                case "EXPIRE":
                    var expireDays = (int)((DateTime.Now - FileExpiryDate).Value.TotalDays);
                    if (expireDays == 0)
                    {
                        expireDays = 1;
                    }
                    return new CertificateWarn()
                    {
                        CompanyId = CompanyId,
                        CompanyName = companyName,
                        CertificateName = FileName,
                        CertificateType = FileTypeName,
                        CertificateStatus = CertificatStatusEnum.Expire,
                        CertificateDate = expireDays,
                        CertificateState = TaskStatusEnum.Alarm,
                        UpdatePerson = OperPerson,

                    };
                case "INVALID":
                    return new CertificateWarn()
                    {
                        CompanyId = CompanyId,
                        CompanyName = companyName,
                        CertificateName = FileName,
                        CertificateType = FileTypeName,
                        CertificateStatus = CertificatStatusEnum.Invalid,
                        CertificateDate = 0,
                        CertificateState = TaskStatusEnum.NoReminder,
                        UpdatePerson = OperPerson,
                    };
                default:
                    return new CertificateWarn()
                    {
                        CompanyId = CompanyId,
                        CompanyName = companyName,
                        CertificateName = FileName,
                        CertificateType = FileTypeName,
                        CertificateStatus = CertificatStatusEnum.Active,
                        CertificateDate = 0,
                        CertificateState = TaskStatusEnum.NoReminder,
                        UpdatePerson = OperPerson,

                    };
            }
        }
    }
    
    /// <summary>
    /// 供应商多附件记录
    /// </summary>
    public  class FileRecordInfo
    {
        [JsonProperty("UPLOAD_FILE_ID")]
        public string UploadFileId { get; set; }

        [JsonProperty("FILE_NAME")]
        public object FileName { get; set; }

        [JsonProperty("FILE_PATH")]
        public string Path { get; set; }

        [JsonProperty("FILE_PREVIEW_PATH")]
        public string PreviewPath { get; set; }

        [JsonProperty("FILE_SUFFIX")]
        public object FileSuffix { get; set; }

        [JsonProperty("DOC_TYPE")]
        public string DocType { get; set; }
    }
    
    /// <summary>
    /// 证书类型
    /// </summary>
    public enum CertificatStatusEnum
    {
        /// <summary>
        /// 正常
        /// </summary>
        [Description("正常")]
        Active = 0,

        /// <summary>
        /// 临近过期
        /// </summary>
        [Description("临近过期")]
        PreExpire = 1,

        /// <summary>
        /// 已过期
        /// </summary>
        [Description("已过期")]

        Expire = 2,
        /// <summary>
        /// 作废
        /// </summary>
        [Description("作废")]

        Invalid = 3,
    }
    public enum STATE_CER
    {
        /// <summary>
        ///正常
        /// </summary>
        [Description("正常")]
        ACTIVE = 0,

        /// <summary>
        ///临近过期
        /// </summary>
        [Description("临近过期")]
        PRE_EXPIRE = 1,

        /// <summary>
        ///过期
        /// </summary>
        [Description("过期")]
        EXPIRE = 2,

        /// <summary>
        ///作废
        /// </summary>
        [Description("作废")]
        INVALID = 3,

    }
    
    
}
