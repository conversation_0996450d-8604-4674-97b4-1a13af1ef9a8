﻿using H.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XH.H82.Models.Entities.Common;

namespace XH.H82.IServices
{
    public interface IReagentInfoService
    {
        List<LIS6_INSTRUMENT_ITEM> GetInstrumentItemList(string equipmentId);
        ResultDto GetInstrumentItemReagentList(string itemId, string channelId, string reagentState,string equipmentId);

        ResultDto GetInstrumentReagentCommonList(string equipmentId);

        ResultDto GetCalibratorList(string equipmentId);

        List<LIS6_INSTRUMENT_ITEM> GetCalibratorItemList(string itemId, string equipmentId, string materialId);
    }
}
