2025-07-01 09:52:25.575 +08:00 [INF] ==>App Start..2025-07-01 09:52:25
2025-07-01 09:52:25.752 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-01 09:52:25.756 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-01 09:52:27.757 +08:00 [INF] ==>基础连接请求完成.
2025-07-01 09:52:28.150 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-01 09:52:28.541 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-01 09:52:28.836 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-01 09:52:28.838 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-01 09:52:29.176 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-01 09:52:29.250 +08:00 [INF] ==>写入ELK:关闭
2025-07-01 09:52:30.703 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-01 09:52:31.038 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-01 09:52:31.537 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-01 09:52:31.537 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-01 09:52:32.585 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-01 09:52:34.760 +08:00 [INF] ==>初始化完成..
2025-07-01 09:52:34.813 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-01 09:52:34.815 +08:00 [INF] 设备启用任务
2025-07-01 09:52:34.816 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-01 09:52:35.239 +08:00 [INF] 【SQL执行耗时:402.1237ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-01 09:52:35.392 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-01 09:52:35.406 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-01 09:52:35.407 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-01 09:52:35.407 +08:00 [INF] Hosting environment: Development
2025-07-01 09:52:35.407 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-01 09:58:48.502 +08:00 [INF] 【SQL执行耗时:363.94ms】

[Sql]:SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" <> :EqpNoState0 ) 
[Pars]:
[Name]::EqpNoState0 [Value]:2 [Type]:String    

2025-07-01 09:58:48.599 +08:00 [INF] HTTP GET /api/CodeCustom/GetEquipmentCodeCustomDict responded 200 in 3795.5931 ms
2025-07-01 09:58:48.601 +08:00 [INF] 【接口超时阀值预警】 [e563d22e3c4ceabb441a55316fcbfb9f]接口/api/CodeCustom/GetEquipmentCodeCustomDict,耗时:[3801]毫秒
2025-07-01 09:58:52.825 +08:00 [INF] Redis connection error restored
2025-07-01 09:58:52.828 +08:00 [INF] Redis connection error restored
2025-07-01 09:58:52.967 +08:00 [INF] Redis connection error restored
2025-07-01 09:58:52.967 +08:00 [INF] Redis connection error restored
2025-07-01 09:59:56.643 +08:00 [INF] HTTP POST /api/CodeCustom/AddEquipmentCodeCustomDict responded 200 in 97.9228 ms
2025-07-01 10:00:04.050 +08:00 [INF] 【SQL执行耗时:359.0531ms】

[Sql]:INSERT INTO "XH_OA"."EMS_EQPNO_FORMAT_DICT"  
           ("EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK")
     VALUES
           (:EQP_NO_ID,:HOSPITAL_ID,:EQP_NO_NAME,:EQP_NO_LEVEL,:EQP_NO_CLASS,:EQP_DISPLAY_JSON,:EQP_NO_APPLYS,:EQP_NO_STATE,:FIRST_RPERSON,:FIRST_RTIME,:LAST_MPERSON,:LAST_MTIME,:REMARK)  
[Pars]:
[Name]::EQP_NO_ID [Value]:******************************** [Type]:String    
[Name]::HOSPITAL_ID [Value]:33A001 [Type]:String    
[Name]::EQP_NO_NAME [Value]:测试1 [Type]:String    
[Name]::EQP_NO_LEVEL [Value]:0 [Type]:String    
[Name]::EQP_NO_CLASS [Value]:0 [Type]:String    
[Name]::EQP_DISPLAY_JSON [Value]:{"DisplayContentString":"{设备编号}_{设备名称}","DisplayContentCode":"{EQUIPMENT_CODE}_{EQUIPMENT_NAME}"} [Type]:String    
[Name]::EQP_NO_APPLYS [Value]:PG001 [Type]:String    
[Name]::EQP_NO_STATE [Value]:1 [Type]:String    
[Name]::FIRST_RPERSON [Value]:gz1_广测重 [Type]:String    
[Name]::FIRST_RTIME [Value]:2025/7/1 10:00:03 [Type]:DateTime    
[Name]::LAST_MPERSON [Value]:gz1_广测重 [Type]:String    
[Name]::LAST_MTIME [Value]:2025/7/1 10:00:03 [Type]:DateTime    
[Name]::REMARK [Value]: [Type]:String    

2025-07-01 10:00:04.099 +08:00 [INF] HTTP POST /api/CodeCustom/AddEquipmentCodeCustomDict responded 200 in 484.7162 ms
2025-07-01 10:00:11.058 +08:00 [INF] 【SQL执行耗时:343.7768ms】

[Sql]:SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" <> :EqpNoState0 ) 
[Pars]:
[Name]::EqpNoState0 [Value]:2 [Type]:String    

2025-07-01 10:00:11.164 +08:00 [INF] HTTP GET /api/CodeCustom/GetEquipmentCodeCustomDict responded 200 in 490.0418 ms
2025-07-01 10:52:00.460 +08:00 [INF] ==>App Start..2025-07-01 10:52:00
2025-07-01 10:52:00.636 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-01 10:52:00.639 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-01 10:52:02.200 +08:00 [INF] ==>基础连接请求完成.
2025-07-01 10:52:02.602 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-01 10:52:03.096 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-01 10:52:03.435 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-01 10:52:03.438 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-01 10:52:03.773 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-01 10:52:04.585 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-01 10:52:04.682 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-01 10:52:05.168 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-01 10:52:05.308 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-01 10:52:05.941 +08:00 [ERR] 查询出错:中文提示 :  连接数据库过程中发生错误，检查服务器是否正常连接字符串是否正确，错误信息：ORA-12545: Network Transport: Unable to resolve connect hostnameDbType="Oracle";ConfigId="".
English Message : Connection open error . ORA-12545: Network Transport: Unable to resolve connect hostnameDbType="Oracle";ConfigId="" :
SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON" FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "LAST_MTIME" IS NULL )
2025-07-01 10:59:51.519 +08:00 [INF] ==>App Start..2025-07-01 10:59:51
2025-07-01 10:59:51.693 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-01 10:59:51.696 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-01 10:59:53.126 +08:00 [INF] ==>基础连接请求完成.
2025-07-01 10:59:53.494 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-01 10:59:53.851 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-01 10:59:54.167 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-01 10:59:54.169 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-01 10:59:54.503 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-01 10:59:54.945 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-01 10:59:55.049 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-01 10:59:55.500 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-01 10:59:55.500 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-01 10:59:56.527 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-01 10:59:59.229 +08:00 [INF] ==>初始化完成..
2025-07-01 10:59:59.251 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-01 10:59:59.253 +08:00 [INF] 设备启用任务
2025-07-01 10:59:59.254 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-01 10:59:59.655 +08:00 [INF] 【SQL执行耗时:378.3941ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-01 10:59:59.798 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-01 10:59:59.813 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-01 10:59:59.814 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-01 10:59:59.814 +08:00 [INF] Hosting environment: Development
2025-07-01 10:59:59.814 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-01 11:01:17.488 +08:00 [INF] 【SQL执行耗时:502.2969ms】

[Sql]:SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" <> :EqpNoState0 ) 
[Pars]:
[Name]::EqpNoState0 [Value]:2 [Type]:String    

2025-07-01 11:01:17.645 +08:00 [INF] HTTP GET /api/CodeCustom/GetEquipmentCodeCustomDict responded 200 in 4822.0472 ms
2025-07-01 11:01:17.648 +08:00 [INF] 【接口超时阀值预警】 [9bf90e133bbda1984da10ee15d038438]接口/api/CodeCustom/GetEquipmentCodeCustomDict,耗时:[4829]毫秒
2025-07-01 11:01:25.958 +08:00 [INF] 【SQL执行耗时:611.4676ms】

[Sql]: SELECT "EQP_NO_STATE"  FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_ID" = :EqpNoId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EqpNoId0 [Value]:******************************** [Type]:String    

2025-07-01 11:01:26.456 +08:00 [INF] 【SQL执行耗时:377.3338ms】

[Sql]:UPDATE "XH_OA"."EMS_EQPNO_FORMAT_DICT"  SET
            "EQP_NO_STATE" = ( CASE  WHEN ( :Const0 = :state1 ) THEN :MethodConst2  ELSE :MethodConst3 END ) , "LAST_MPERSON" = :Const5 , "LAST_MTIME" = :constant4   WHERE ( "EQP_NO_ID" = :EqpNoId6 ) 
[Pars]:
[Name]::Const0 [Value]:1 [Type]:String    
[Name]::state1 [Value]:1 [Type]:String    
[Name]::MethodConst2 [Value]:0 [Type]:String    
[Name]::MethodConst3 [Value]:1 [Type]:String    
[Name]::constant4 [Value]:2025/7/1 11:01:26 [Type]:DateTime    
[Name]::Const5 [Value]:gz1_广测重 [Type]:String    
[Name]::EqpNoId6 [Value]:******************************** [Type]:String    
[Name]::EQP_NO_ID [Value]: [Type]:String    
[Name]::EQP_NO_STATE [Value]: [Type]:String    
[Name]::LAST_MPERSON [Value]: [Type]:String    
[Name]::LAST_MTIME [Value]: [Type]:DateTime    

2025-07-01 11:01:26.498 +08:00 [INF] HTTP POST /api/CodeCustom/EnableOrdisableEquipmentCodeCustomDict/******************************** responded 200 in 1235.4327 ms
2025-07-01 11:01:26.499 +08:00 [INF] 【接口超时阀值预警】 [31e19af7106f5e3047cebc12ca479b66]接口/api/CodeCustom/EnableOrdisableEquipmentCodeCustomDict/********************************,耗时:[1235]毫秒
2025-07-01 11:01:30.561 +08:00 [INF] 【SQL执行耗时:356.6602ms】

[Sql]:SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" <> :EqpNoState0 ) 
[Pars]:
[Name]::EqpNoState0 [Value]:2 [Type]:String    

2025-07-01 11:01:30.667 +08:00 [INF] HTTP GET /api/CodeCustom/GetEquipmentCodeCustomDict responded 200 in 556.6423 ms
2025-07-01 11:01:30.668 +08:00 [INF] 【接口超时阀值预警】 [a253b30c47aa6544e96fc477b1fc7165]接口/api/CodeCustom/GetEquipmentCodeCustomDict,耗时:[557]毫秒
2025-07-01 11:01:38.849 +08:00 [INF] 【SQL执行耗时:336.0867ms】

[Sql]: SELECT "EQP_NO_STATE"  FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_ID" = :EqpNoId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EqpNoId0 [Value]:******************************** [Type]:String    

2025-07-01 11:01:39.246 +08:00 [INF] 【SQL执行耗时:360.3702ms】

[Sql]:UPDATE "XH_OA"."EMS_EQPNO_FORMAT_DICT"  SET
            "EQP_NO_STATE" = ( CASE  WHEN ( :Const0 = :state1 ) THEN :MethodConst2  ELSE :MethodConst3 END ) , "LAST_MPERSON" = :Const5 , "LAST_MTIME" = :constant4   WHERE ( "EQP_NO_ID" = :EqpNoId6 ) 
[Pars]:
[Name]::Const0 [Value]:0 [Type]:String    
[Name]::state1 [Value]:1 [Type]:String    
[Name]::MethodConst2 [Value]:0 [Type]:String    
[Name]::MethodConst3 [Value]:1 [Type]:String    
[Name]::constant4 [Value]:2025/7/1 11:01:38 [Type]:DateTime    
[Name]::Const5 [Value]:gz1_广测重 [Type]:String    
[Name]::EqpNoId6 [Value]:******************************** [Type]:String    
[Name]::EQP_NO_ID [Value]: [Type]:String    
[Name]::EQP_NO_STATE [Value]: [Type]:String    
[Name]::LAST_MPERSON [Value]: [Type]:String    
[Name]::LAST_MTIME [Value]: [Type]:DateTime    

2025-07-01 11:01:39.308 +08:00 [INF] HTTP POST /api/CodeCustom/EnableOrdisableEquipmentCodeCustomDict/******************************** responded 200 in 834.2631 ms
2025-07-01 11:01:39.308 +08:00 [INF] 【接口超时阀值预警】 [97363bddad9c5a17f8018f64850906f1]接口/api/CodeCustom/EnableOrdisableEquipmentCodeCustomDict/********************************,耗时:[834]毫秒
2025-07-01 11:01:41.476 +08:00 [INF] 【SQL执行耗时:355.7099ms】

[Sql]:SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" <> :EqpNoState0 ) 
[Pars]:
[Name]::EqpNoState0 [Value]:2 [Type]:String    

2025-07-01 11:01:41.589 +08:00 [INF] HTTP GET /api/CodeCustom/GetEquipmentCodeCustomDict responded 200 in 505.4355 ms
2025-07-01 11:01:41.590 +08:00 [INF] 【接口超时阀值预警】 [237f7f26b79bf6c7b7ff4c5027d23157]接口/api/CodeCustom/GetEquipmentCodeCustomDict,耗时:[505]毫秒
2025-07-01 11:35:39.629 +08:00 [INF] ==>App Start..2025-07-01 11:35:39
2025-07-01 11:35:39.804 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-01 11:35:39.808 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-01 11:36:17.816 +08:00 [INF] ==>App Start..2025-07-01 11:36:17
2025-07-01 11:36:17.985 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-01 11:36:17.988 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-01 11:36:19.483 +08:00 [INF] ==>基础连接请求完成.
2025-07-01 11:36:19.861 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-01 11:36:20.227 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-01 11:36:20.554 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-01 11:36:20.556 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-01 11:36:20.888 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-01 11:36:21.276 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-01 11:36:21.361 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-01 11:36:21.796 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-01 11:36:21.796 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-01 11:36:25.847 +08:00 [ERR] 查询出错:ORA-00904: "AREA_NAME": 标识符无效:
SELECT "AREA_NAME","EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON" FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "LAST_MTIME" IS NULL )
2025-07-01 13:33:23.887 +08:00 [INF] ==>App Start..2025-07-01 13:33:23
2025-07-01 13:33:24.058 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-01 13:33:24.061 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-01 13:33:26.042 +08:00 [INF] ==>基础连接请求完成.
2025-07-01 13:33:26.421 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-01 13:33:26.836 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-01 13:33:27.142 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-01 13:33:27.145 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-01 13:33:27.475 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-01 13:33:29.185 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-01 13:33:29.622 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-01 13:33:30.090 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-01 13:33:30.091 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-01 13:33:31.054 +08:00 [ERR] 查询出错:ORA-00904: "AREA_NAME": 标识符无效:
SELECT "AREA_NAME","EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON" FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "LAST_MTIME" IS NULL )
2025-07-01 13:34:34.476 +08:00 [INF] ==>App Start..2025-07-01 13:34:34
2025-07-01 13:34:34.666 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-01 13:34:34.670 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-01 13:34:36.242 +08:00 [INF] ==>基础连接请求完成.
2025-07-01 13:34:36.620 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-01 13:34:37.109 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-01 13:34:37.455 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-01 13:34:37.464 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-01 13:34:37.793 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-01 13:34:38.328 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-01 13:34:38.431 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-01 13:34:38.870 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-01 13:34:38.870 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-01 13:34:39.754 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-01 13:34:41.979 +08:00 [INF] ==>初始化完成..
2025-07-01 13:34:42.021 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-01 13:34:42.025 +08:00 [INF] 设备启用任务
2025-07-01 13:34:42.026 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-01 13:34:42.414 +08:00 [INF] 【SQL执行耗时:366.4194ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-01 13:34:42.567 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-01 13:34:42.580 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-01 13:34:42.582 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-01 13:34:42.582 +08:00 [INF] Hosting environment: Development
2025-07-01 13:34:42.583 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-01 13:35:24.563 +08:00 [INF] 【SQL执行耗时:354.5376ms】

[Sql]:SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" <> :EqpNoState0 ) 
[Pars]:
[Name]::EqpNoState0 [Value]:2 [Type]:String    

2025-07-01 13:35:24.717 +08:00 [INF] HTTP GET /api/CodeCustom/GetEquipmentCodeCustomDict responded 200 in 6840.2757 ms
2025-07-01 13:35:24.719 +08:00 [INF] 【接口超时阀值预警】 [fdb528ab723f3b30e831b5e8d9b3cbbe]接口/api/CodeCustom/GetEquipmentCodeCustomDict,耗时:[6848]毫秒
2025-07-01 13:35:44.291 +08:00 [INF] 【SQL执行耗时:345.2621ms】

[Sql]:SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_ID" = :EqpNoId0 ) 
[Pars]:
[Name]::EqpNoId0 [Value]:******************************** [Type]:String    

2025-07-01 13:35:44.776 +08:00 [INF] 【SQL执行耗时:374.8785ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_ID" = :EQUIPMENT_ID0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_ID0 [Value]:1721768701646213120 [Type]:String    

2025-07-01 13:35:45.232 +08:00 [INF] 【SQL执行耗时:374.3702ms】

[Sql]:SELECT "HOSPITAL_ID","HOSPITAL_LOGIN","HOSPITAL_CNAME","HOSPITAL_SNAME","HOSPITAL_TYPE","HOSPITAL_LEVEL","LEVEL1_CODE","LEVEL2_CODE","LEVEL3_CODE","HOSPITAL_BARCODE","HOSPITAL_PROPERTY","HOSPITAL_PERSON","HOSPITAL_ADDRESS","HOSPITAL_TEL","HOSPITAL_STATE","FIRST_PERSON","FIRST_RTIME","LAST_MPRESON","LAST_MTIME","REMARK","HOSPITAL_BCODE","HOSPITAL_RANGE","BARCODE_TYPE","BARCODE_RANGE","CHARGE_PROVIDE_TYPE","DATADB_IP","WECHAT_NAME","LAST_MPERSON","FIRST_RPERSON","UPDATE_FLAG","INSERFACE_SERVICE","SAMELEVEL_HOSPITAL","HOSPITAL_CLASS","HOSPITAL_URL","REGISTER_CODE","PACK_BAR_PREFIX","IF_ITEM_MANAGE","IF_TEMPLATE_HOS","MEDICAL_CONGENER","MEDICAL_CONGENER_ROLE","VEST_IN_DC","INSTANCE_ID","HOSPITAL_ATTRIBUTE","DATASYNC_TYPE","HGROUP_ID","CITEM_MANAGE" FROM "XH_SYS"."SYS6_HOSPITAL_INFO" 

2025-07-01 13:35:45.695 +08:00 [INF] 【SQL执行耗时:367.5051ms】

[Sql]:SELECT "LAB_ID","FIRST_RPERSON","LAB_NAME","LAB_ADDRESS","LAB_CODE","LAST_MTIME","LAB_SORT","LAB_RNAME","LAB_CLASS","LAB_PERSON","LINKMAN_TEL","REGISTER_CODE","LAB_LINKMAN","STATE_FLAG","REMARK","LAST_MPERSON","FIRST_RTIME","SPELL_CODE","LAB_TEL","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_LAB" 

2025-07-01 13:35:46.101 +08:00 [INF] 【SQL执行耗时:323.0144ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-01 13:35:46.529 +08:00 [INF] 【SQL执行耗时:355.5874ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP" 

2025-07-01 13:35:46.967 +08:00 [INF] 【SQL执行耗时:360.3722ms】

[Sql]:SELECT "AREA_ID","AREA_CODE","AREA_SORT","AREA_NAME","AREA_CLASS","AREA_PERSON","AREA_ADDRESS","AREA_TEL","SPELL_CODE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","STATE_FLAG","REMARK","HOSPITAL_ID","TAG_SIGN","AREA_ANAME","PACK_BAR_PREFIX" FROM "XH_SYS"."SYS6_INSPECTION_AREA" 

2025-07-01 13:35:47.051 +08:00 [INF] HTTP GET /api/CodeCustom/GetEquipmentUCodePreview/******************************** responded 200 in 3184.4736 ms
2025-07-01 13:35:47.052 +08:00 [INF] 【接口超时阀值预警】 [5274e3ff395c72e99d1700dd0994e605]接口/api/CodeCustom/GetEquipmentUCodePreview/********************************,耗时:[3185]毫秒
2025-07-01 13:36:39.208 +08:00 [INF] ==>App Start..2025-07-01 13:36:39
2025-07-01 13:36:39.383 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-01 13:36:39.386 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-01 13:36:40.818 +08:00 [INF] ==>基础连接请求完成.
2025-07-01 13:36:41.194 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-01 13:36:41.565 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-01 13:36:41.967 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-01 13:36:41.969 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-01 13:36:42.306 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-01 13:36:42.702 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-01 13:36:42.794 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-01 13:36:43.221 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-01 13:36:43.221 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-01 13:36:44.076 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-01 13:36:46.360 +08:00 [INF] ==>初始化完成..
2025-07-01 13:36:46.380 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-01 13:36:46.382 +08:00 [INF] 设备启用任务
2025-07-01 13:36:46.383 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-01 13:36:46.768 +08:00 [INF] 【SQL执行耗时:364.2385ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-01 13:36:46.914 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-01 13:36:46.927 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-01 13:36:46.929 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-01 13:36:46.929 +08:00 [INF] Hosting environment: Development
2025-07-01 13:36:46.929 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-01 13:36:50.557 +08:00 [INF] 【SQL执行耗时:346.9582ms】

[Sql]:SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_ID" = :EqpNoId0 ) 
[Pars]:
[Name]::EqpNoId0 [Value]:******************************** [Type]:String    

2025-07-01 13:36:51.033 +08:00 [INF] 【SQL执行耗时:345.2489ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_ID" = :EQUIPMENT_ID0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_ID0 [Value]:1721768701646213120 [Type]:String    

2025-07-01 13:36:51.449 +08:00 [INF] 【SQL执行耗时:338.4457ms】

[Sql]:SELECT "HOSPITAL_ID","HOSPITAL_LOGIN","HOSPITAL_CNAME","HOSPITAL_SNAME","HOSPITAL_TYPE","HOSPITAL_LEVEL","LEVEL1_CODE","LEVEL2_CODE","LEVEL3_CODE","HOSPITAL_BARCODE","HOSPITAL_PROPERTY","HOSPITAL_PERSON","HOSPITAL_ADDRESS","HOSPITAL_TEL","HOSPITAL_STATE","FIRST_PERSON","FIRST_RTIME","LAST_MPRESON","LAST_MTIME","REMARK","HOSPITAL_BCODE","HOSPITAL_RANGE","BARCODE_TYPE","BARCODE_RANGE","CHARGE_PROVIDE_TYPE","DATADB_IP","WECHAT_NAME","LAST_MPERSON","FIRST_RPERSON","UPDATE_FLAG","INSERFACE_SERVICE","SAMELEVEL_HOSPITAL","HOSPITAL_CLASS","HOSPITAL_URL","REGISTER_CODE","PACK_BAR_PREFIX","IF_ITEM_MANAGE","IF_TEMPLATE_HOS","MEDICAL_CONGENER","MEDICAL_CONGENER_ROLE","VEST_IN_DC","INSTANCE_ID","HOSPITAL_ATTRIBUTE","DATASYNC_TYPE","HGROUP_ID","CITEM_MANAGE" FROM "XH_SYS"."SYS6_HOSPITAL_INFO" 

2025-07-01 13:36:51.873 +08:00 [INF] 【SQL执行耗时:343.2252ms】

[Sql]:SELECT "LAB_ID","FIRST_RPERSON","LAB_NAME","LAB_ADDRESS","LAB_CODE","LAST_MTIME","LAB_SORT","LAB_RNAME","LAB_CLASS","LAB_PERSON","LINKMAN_TEL","REGISTER_CODE","LAB_LINKMAN","STATE_FLAG","REMARK","LAST_MPERSON","FIRST_RTIME","SPELL_CODE","LAB_TEL","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_LAB" 

2025-07-01 13:36:52.301 +08:00 [INF] 【SQL执行耗时:350.3833ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-01 13:36:52.757 +08:00 [INF] 【SQL执行耗时:377.814ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP" 

2025-07-01 13:36:53.198 +08:00 [INF] 【SQL执行耗时:359.9232ms】

[Sql]:SELECT "AREA_ID","AREA_CODE","AREA_SORT","AREA_NAME","AREA_CLASS","AREA_PERSON","AREA_ADDRESS","AREA_TEL","SPELL_CODE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","STATE_FLAG","REMARK","HOSPITAL_ID","TAG_SIGN","AREA_ANAME","PACK_BAR_PREFIX" FROM "XH_SYS"."SYS6_INSPECTION_AREA" 

2025-07-01 13:36:53.296 +08:00 [INF] HTTP GET /api/CodeCustom/GetEquipmentUCodePreview/******************************** responded 200 in 5855.4121 ms
2025-07-01 13:36:53.298 +08:00 [INF] 【接口超时阀值预警】 [641ae3bbbafe2756e7f859d6bd169e04]接口/api/CodeCustom/GetEquipmentUCodePreview/********************************,耗时:[5862]毫秒
2025-07-01 13:36:55.474 +08:00 [INF] 【SQL执行耗时:347.8256ms】

[Sql]:SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_ID" = :EqpNoId0 ) 
[Pars]:
[Name]::EqpNoId0 [Value]:******************************** [Type]:String    

2025-07-01 13:36:55.981 +08:00 [INF] 【SQL执行耗时:395.7326ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_ID" = :EQUIPMENT_ID0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_ID0 [Value]:1721768701646213120 [Type]:String    

2025-07-01 13:36:56.398 +08:00 [INF] 【SQL执行耗时:336.8289ms】

[Sql]:SELECT "HOSPITAL_ID","HOSPITAL_LOGIN","HOSPITAL_CNAME","HOSPITAL_SNAME","HOSPITAL_TYPE","HOSPITAL_LEVEL","LEVEL1_CODE","LEVEL2_CODE","LEVEL3_CODE","HOSPITAL_BARCODE","HOSPITAL_PROPERTY","HOSPITAL_PERSON","HOSPITAL_ADDRESS","HOSPITAL_TEL","HOSPITAL_STATE","FIRST_PERSON","FIRST_RTIME","LAST_MPRESON","LAST_MTIME","REMARK","HOSPITAL_BCODE","HOSPITAL_RANGE","BARCODE_TYPE","BARCODE_RANGE","CHARGE_PROVIDE_TYPE","DATADB_IP","WECHAT_NAME","LAST_MPERSON","FIRST_RPERSON","UPDATE_FLAG","INSERFACE_SERVICE","SAMELEVEL_HOSPITAL","HOSPITAL_CLASS","HOSPITAL_URL","REGISTER_CODE","PACK_BAR_PREFIX","IF_ITEM_MANAGE","IF_TEMPLATE_HOS","MEDICAL_CONGENER","MEDICAL_CONGENER_ROLE","VEST_IN_DC","INSTANCE_ID","HOSPITAL_ATTRIBUTE","DATASYNC_TYPE","HGROUP_ID","CITEM_MANAGE" FROM "XH_SYS"."SYS6_HOSPITAL_INFO" 

2025-07-01 13:36:56.811 +08:00 [INF] 【SQL执行耗时:343.5976ms】

[Sql]:SELECT "LAB_ID","FIRST_RPERSON","LAB_NAME","LAB_ADDRESS","LAB_CODE","LAST_MTIME","LAB_SORT","LAB_RNAME","LAB_CLASS","LAB_PERSON","LINKMAN_TEL","REGISTER_CODE","LAB_LINKMAN","STATE_FLAG","REMARK","LAST_MPERSON","FIRST_RTIME","SPELL_CODE","LAB_TEL","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_LAB" 

2025-07-01 13:36:57.229 +08:00 [INF] 【SQL执行耗时:344.7547ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-01 13:36:57.638 +08:00 [INF] 【SQL执行耗时:337.2612ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP" 

2025-07-01 13:36:58.059 +08:00 [INF] 【SQL执行耗时:353.0831ms】

[Sql]:SELECT "AREA_ID","AREA_CODE","AREA_SORT","AREA_NAME","AREA_CLASS","AREA_PERSON","AREA_ADDRESS","AREA_TEL","SPELL_CODE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","STATE_FLAG","REMARK","HOSPITAL_ID","TAG_SIGN","AREA_ANAME","PACK_BAR_PREFIX" FROM "XH_SYS"."SYS6_INSPECTION_AREA" 

2025-07-01 13:36:58.132 +08:00 [INF] HTTP GET /api/CodeCustom/GetEquipmentUCodePreview/******************************** responded 200 in 3051.1116 ms
2025-07-01 13:36:58.132 +08:00 [INF] 【接口超时阀值预警】 [752bf145647449038e1e9d217435f7e2]接口/api/CodeCustom/GetEquipmentUCodePreview/********************************,耗时:[3051]毫秒
2025-07-01 13:37:04.839 +08:00 [INF] 【SQL执行耗时:347.7907ms】

[Sql]:SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_ID" = :EqpNoId0 ) 
[Pars]:
[Name]::EqpNoId0 [Value]:******************************** [Type]:String    

2025-07-01 13:37:16.307 +08:00 [INF] 【SQL执行耗时:364.5118ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_ID" = :EQUIPMENT_ID0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_ID0 [Value]:1721768701646213120 [Type]:String    

2025-07-01 13:37:16.737 +08:00 [INF] 【SQL执行耗时:349.2715ms】

[Sql]:SELECT "HOSPITAL_ID","HOSPITAL_LOGIN","HOSPITAL_CNAME","HOSPITAL_SNAME","HOSPITAL_TYPE","HOSPITAL_LEVEL","LEVEL1_CODE","LEVEL2_CODE","LEVEL3_CODE","HOSPITAL_BARCODE","HOSPITAL_PROPERTY","HOSPITAL_PERSON","HOSPITAL_ADDRESS","HOSPITAL_TEL","HOSPITAL_STATE","FIRST_PERSON","FIRST_RTIME","LAST_MPRESON","LAST_MTIME","REMARK","HOSPITAL_BCODE","HOSPITAL_RANGE","BARCODE_TYPE","BARCODE_RANGE","CHARGE_PROVIDE_TYPE","DATADB_IP","WECHAT_NAME","LAST_MPERSON","FIRST_RPERSON","UPDATE_FLAG","INSERFACE_SERVICE","SAMELEVEL_HOSPITAL","HOSPITAL_CLASS","HOSPITAL_URL","REGISTER_CODE","PACK_BAR_PREFIX","IF_ITEM_MANAGE","IF_TEMPLATE_HOS","MEDICAL_CONGENER","MEDICAL_CONGENER_ROLE","VEST_IN_DC","INSTANCE_ID","HOSPITAL_ATTRIBUTE","DATASYNC_TYPE","HGROUP_ID","CITEM_MANAGE" FROM "XH_SYS"."SYS6_HOSPITAL_INFO" 

2025-07-01 13:37:17.166 +08:00 [INF] 【SQL执行耗时:354.0379ms】

[Sql]:SELECT "LAB_ID","FIRST_RPERSON","LAB_NAME","LAB_ADDRESS","LAB_CODE","LAST_MTIME","LAB_SORT","LAB_RNAME","LAB_CLASS","LAB_PERSON","LINKMAN_TEL","REGISTER_CODE","LAB_LINKMAN","STATE_FLAG","REMARK","LAST_MPERSON","FIRST_RTIME","SPELL_CODE","LAB_TEL","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_LAB" 

2025-07-01 13:37:17.587 +08:00 [INF] 【SQL执行耗时:348.4791ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-01 13:37:18.028 +08:00 [INF] 【SQL执行耗时:369.076ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP" 

2025-07-01 13:37:18.443 +08:00 [INF] 【SQL执行耗时:339.7563ms】

[Sql]:SELECT "AREA_ID","AREA_CODE","AREA_SORT","AREA_NAME","AREA_CLASS","AREA_PERSON","AREA_ADDRESS","AREA_TEL","SPELL_CODE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","STATE_FLAG","REMARK","HOSPITAL_ID","TAG_SIGN","AREA_ANAME","PACK_BAR_PREFIX" FROM "XH_SYS"."SYS6_INSPECTION_AREA" 

2025-07-01 13:37:55.698 +08:00 [INF] HTTP GET /api/CodeCustom/GetEquipmentUCodePreview/******************************** responded 200 in 52831.1396 ms
2025-07-01 13:37:55.698 +08:00 [INF] 【接口超时阀值预警】 [6aced786303d4bba2e7ac5fa4983f09a]接口/api/CodeCustom/GetEquipmentUCodePreview/********************************,耗时:[52831]毫秒
2025-07-01 13:38:02.593 +08:00 [INF] 【SQL执行耗时:367.6085ms】

[Sql]:SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_ID" = :EqpNoId0 ) 
[Pars]:
[Name]::EqpNoId0 [Value]:******************************** [Type]:String    

2025-07-01 13:38:03.053 +08:00 [INF] 【SQL执行耗时:347.3411ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_ID" = :EQUIPMENT_ID0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_ID0 [Value]:1721768701646213120 [Type]:String    

2025-07-01 13:38:03.472 +08:00 [INF] 【SQL执行耗时:346.368ms】

[Sql]:SELECT "HOSPITAL_ID","HOSPITAL_LOGIN","HOSPITAL_CNAME","HOSPITAL_SNAME","HOSPITAL_TYPE","HOSPITAL_LEVEL","LEVEL1_CODE","LEVEL2_CODE","LEVEL3_CODE","HOSPITAL_BARCODE","HOSPITAL_PROPERTY","HOSPITAL_PERSON","HOSPITAL_ADDRESS","HOSPITAL_TEL","HOSPITAL_STATE","FIRST_PERSON","FIRST_RTIME","LAST_MPRESON","LAST_MTIME","REMARK","HOSPITAL_BCODE","HOSPITAL_RANGE","BARCODE_TYPE","BARCODE_RANGE","CHARGE_PROVIDE_TYPE","DATADB_IP","WECHAT_NAME","LAST_MPERSON","FIRST_RPERSON","UPDATE_FLAG","INSERFACE_SERVICE","SAMELEVEL_HOSPITAL","HOSPITAL_CLASS","HOSPITAL_URL","REGISTER_CODE","PACK_BAR_PREFIX","IF_ITEM_MANAGE","IF_TEMPLATE_HOS","MEDICAL_CONGENER","MEDICAL_CONGENER_ROLE","VEST_IN_DC","INSTANCE_ID","HOSPITAL_ATTRIBUTE","DATASYNC_TYPE","HGROUP_ID","CITEM_MANAGE" FROM "XH_SYS"."SYS6_HOSPITAL_INFO" 

2025-07-01 13:38:03.901 +08:00 [INF] 【SQL执行耗时:355.5211ms】

[Sql]:SELECT "LAB_ID","FIRST_RPERSON","LAB_NAME","LAB_ADDRESS","LAB_CODE","LAST_MTIME","LAB_SORT","LAB_RNAME","LAB_CLASS","LAB_PERSON","LINKMAN_TEL","REGISTER_CODE","LAB_LINKMAN","STATE_FLAG","REMARK","LAST_MPERSON","FIRST_RTIME","SPELL_CODE","LAB_TEL","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_LAB" 

2025-07-01 13:38:04.337 +08:00 [INF] 【SQL执行耗时:358.2765ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-01 13:38:04.763 +08:00 [INF] 【SQL执行耗时:352.5683ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP" 

2025-07-01 13:38:05.207 +08:00 [INF] 【SQL执行耗时:373.4629ms】

[Sql]:SELECT "AREA_ID","AREA_CODE","AREA_SORT","AREA_NAME","AREA_CLASS","AREA_PERSON","AREA_ADDRESS","AREA_TEL","SPELL_CODE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","STATE_FLAG","REMARK","HOSPITAL_ID","TAG_SIGN","AREA_ANAME","PACK_BAR_PREFIX" FROM "XH_SYS"."SYS6_INSPECTION_AREA" 

2025-07-01 13:38:07.419 +08:00 [INF] HTTP GET /api/CodeCustom/GetEquipmentUCodePreview/******************************** responded 200 in 7126.2260 ms
2025-07-01 13:38:07.420 +08:00 [INF] 【接口超时阀值预警】 [0ebd7affb2219c6c7c203ae27db066f4]接口/api/CodeCustom/GetEquipmentUCodePreview/********************************,耗时:[7126]毫秒
2025-07-01 13:38:09.768 +08:00 [INF] 【SQL执行耗时:362.7632ms】

[Sql]:SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_ID" = :EqpNoId0 ) 
[Pars]:
[Name]::EqpNoId0 [Value]:******************************** [Type]:String    

2025-07-01 13:38:10.224 +08:00 [INF] 【SQL执行耗时:345.2368ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_ID" = :EQUIPMENT_ID0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_ID0 [Value]:1721768701646213120 [Type]:String    

2025-07-01 13:38:10.651 +08:00 [INF] 【SQL执行耗时:356.2191ms】

[Sql]:SELECT "HOSPITAL_ID","HOSPITAL_LOGIN","HOSPITAL_CNAME","HOSPITAL_SNAME","HOSPITAL_TYPE","HOSPITAL_LEVEL","LEVEL1_CODE","LEVEL2_CODE","LEVEL3_CODE","HOSPITAL_BARCODE","HOSPITAL_PROPERTY","HOSPITAL_PERSON","HOSPITAL_ADDRESS","HOSPITAL_TEL","HOSPITAL_STATE","FIRST_PERSON","FIRST_RTIME","LAST_MPRESON","LAST_MTIME","REMARK","HOSPITAL_BCODE","HOSPITAL_RANGE","BARCODE_TYPE","BARCODE_RANGE","CHARGE_PROVIDE_TYPE","DATADB_IP","WECHAT_NAME","LAST_MPERSON","FIRST_RPERSON","UPDATE_FLAG","INSERFACE_SERVICE","SAMELEVEL_HOSPITAL","HOSPITAL_CLASS","HOSPITAL_URL","REGISTER_CODE","PACK_BAR_PREFIX","IF_ITEM_MANAGE","IF_TEMPLATE_HOS","MEDICAL_CONGENER","MEDICAL_CONGENER_ROLE","VEST_IN_DC","INSTANCE_ID","HOSPITAL_ATTRIBUTE","DATASYNC_TYPE","HGROUP_ID","CITEM_MANAGE" FROM "XH_SYS"."SYS6_HOSPITAL_INFO" 

2025-07-01 13:38:11.106 +08:00 [INF] 【SQL执行耗时:381.1632ms】

[Sql]:SELECT "LAB_ID","FIRST_RPERSON","LAB_NAME","LAB_ADDRESS","LAB_CODE","LAST_MTIME","LAB_SORT","LAB_RNAME","LAB_CLASS","LAB_PERSON","LINKMAN_TEL","REGISTER_CODE","LAB_LINKMAN","STATE_FLAG","REMARK","LAST_MPERSON","FIRST_RTIME","SPELL_CODE","LAB_TEL","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_LAB" 

2025-07-01 13:38:11.520 +08:00 [INF] 【SQL执行耗时:344.4823ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-01 13:38:11.932 +08:00 [INF] 【SQL执行耗时:342.7541ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP" 

2025-07-01 13:38:12.334 +08:00 [INF] 【SQL执行耗时:330.6537ms】

[Sql]:SELECT "AREA_ID","AREA_CODE","AREA_SORT","AREA_NAME","AREA_CLASS","AREA_PERSON","AREA_ADDRESS","AREA_TEL","SPELL_CODE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","STATE_FLAG","REMARK","HOSPITAL_ID","TAG_SIGN","AREA_ANAME","PACK_BAR_PREFIX" FROM "XH_SYS"."SYS6_INSPECTION_AREA" 

2025-07-01 13:38:12.401 +08:00 [INF] HTTP GET /api/CodeCustom/GetEquipmentUCodePreview/******************************** responded 200 in 3035.3586 ms
2025-07-01 13:38:12.401 +08:00 [INF] 【接口超时阀值预警】 [b5f29f66ae8e741546910e84176bb57c]接口/api/CodeCustom/GetEquipmentUCodePreview/********************************,耗时:[3035]毫秒
2025-07-01 13:38:15.755 +08:00 [INF] 【SQL执行耗时:358.6122ms】

[Sql]:SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_ID" = :EqpNoId0 ) 
[Pars]:
[Name]::EqpNoId0 [Value]:******************************** [Type]:String    

2025-07-01 13:38:16.211 +08:00 [INF] 【SQL执行耗时:346.8501ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_ID" = :EQUIPMENT_ID0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_ID0 [Value]:1721768701646213120 [Type]:String    

2025-07-01 13:38:16.644 +08:00 [INF] 【SQL执行耗时:361.8552ms】

[Sql]:SELECT "HOSPITAL_ID","HOSPITAL_LOGIN","HOSPITAL_CNAME","HOSPITAL_SNAME","HOSPITAL_TYPE","HOSPITAL_LEVEL","LEVEL1_CODE","LEVEL2_CODE","LEVEL3_CODE","HOSPITAL_BARCODE","HOSPITAL_PROPERTY","HOSPITAL_PERSON","HOSPITAL_ADDRESS","HOSPITAL_TEL","HOSPITAL_STATE","FIRST_PERSON","FIRST_RTIME","LAST_MPRESON","LAST_MTIME","REMARK","HOSPITAL_BCODE","HOSPITAL_RANGE","BARCODE_TYPE","BARCODE_RANGE","CHARGE_PROVIDE_TYPE","DATADB_IP","WECHAT_NAME","LAST_MPERSON","FIRST_RPERSON","UPDATE_FLAG","INSERFACE_SERVICE","SAMELEVEL_HOSPITAL","HOSPITAL_CLASS","HOSPITAL_URL","REGISTER_CODE","PACK_BAR_PREFIX","IF_ITEM_MANAGE","IF_TEMPLATE_HOS","MEDICAL_CONGENER","MEDICAL_CONGENER_ROLE","VEST_IN_DC","INSTANCE_ID","HOSPITAL_ATTRIBUTE","DATASYNC_TYPE","HGROUP_ID","CITEM_MANAGE" FROM "XH_SYS"."SYS6_HOSPITAL_INFO" 

2025-07-01 13:38:17.067 +08:00 [INF] 【SQL执行耗时:337.8505ms】

[Sql]:SELECT "LAB_ID","FIRST_RPERSON","LAB_NAME","LAB_ADDRESS","LAB_CODE","LAST_MTIME","LAB_SORT","LAB_RNAME","LAB_CLASS","LAB_PERSON","LINKMAN_TEL","REGISTER_CODE","LAB_LINKMAN","STATE_FLAG","REMARK","LAST_MPERSON","FIRST_RTIME","SPELL_CODE","LAB_TEL","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_LAB" 

2025-07-01 13:38:17.488 +08:00 [INF] 【SQL执行耗时:351.2559ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-01 13:38:17.906 +08:00 [INF] 【SQL执行耗时:344.5948ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP" 

2025-07-01 13:38:18.322 +08:00 [INF] 【SQL执行耗时:344.5605ms】

[Sql]:SELECT "AREA_ID","AREA_CODE","AREA_SORT","AREA_NAME","AREA_CLASS","AREA_PERSON","AREA_ADDRESS","AREA_TEL","SPELL_CODE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","STATE_FLAG","REMARK","HOSPITAL_ID","TAG_SIGN","AREA_ANAME","PACK_BAR_PREFIX" FROM "XH_SYS"."SYS6_INSPECTION_AREA" 

2025-07-01 13:38:18.394 +08:00 [INF] HTTP GET /api/CodeCustom/GetEquipmentUCodePreview/******************************** responded 200 in 3034.0967 ms
2025-07-01 13:38:18.394 +08:00 [INF] 【接口超时阀值预警】 [49507c59ee46cd961f902cb93908559d]接口/api/CodeCustom/GetEquipmentUCodePreview/********************************,耗时:[3034]毫秒
2025-07-01 13:38:47.796 +08:00 [INF] 【SQL执行耗时:499.6982ms】

[Sql]:SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_ID" = :EqpNoId0 ) 
[Pars]:
[Name]::EqpNoId0 [Value]:******************************** [Type]:String    

2025-07-01 13:38:48.250 +08:00 [INF] 【SQL执行耗时:342.9636ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_ID" = :EQUIPMENT_ID0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_ID0 [Value]:1721768701646213120 [Type]:String    

2025-07-01 13:38:48.664 +08:00 [INF] 【SQL执行耗时:343.3358ms】

[Sql]:SELECT "HOSPITAL_ID","HOSPITAL_LOGIN","HOSPITAL_CNAME","HOSPITAL_SNAME","HOSPITAL_TYPE","HOSPITAL_LEVEL","LEVEL1_CODE","LEVEL2_CODE","LEVEL3_CODE","HOSPITAL_BARCODE","HOSPITAL_PROPERTY","HOSPITAL_PERSON","HOSPITAL_ADDRESS","HOSPITAL_TEL","HOSPITAL_STATE","FIRST_PERSON","FIRST_RTIME","LAST_MPRESON","LAST_MTIME","REMARK","HOSPITAL_BCODE","HOSPITAL_RANGE","BARCODE_TYPE","BARCODE_RANGE","CHARGE_PROVIDE_TYPE","DATADB_IP","WECHAT_NAME","LAST_MPERSON","FIRST_RPERSON","UPDATE_FLAG","INSERFACE_SERVICE","SAMELEVEL_HOSPITAL","HOSPITAL_CLASS","HOSPITAL_URL","REGISTER_CODE","PACK_BAR_PREFIX","IF_ITEM_MANAGE","IF_TEMPLATE_HOS","MEDICAL_CONGENER","MEDICAL_CONGENER_ROLE","VEST_IN_DC","INSTANCE_ID","HOSPITAL_ATTRIBUTE","DATASYNC_TYPE","HGROUP_ID","CITEM_MANAGE" FROM "XH_SYS"."SYS6_HOSPITAL_INFO" 

2025-07-01 13:38:49.103 +08:00 [INF] 【SQL执行耗时:368.2263ms】

[Sql]:SELECT "LAB_ID","FIRST_RPERSON","LAB_NAME","LAB_ADDRESS","LAB_CODE","LAST_MTIME","LAB_SORT","LAB_RNAME","LAB_CLASS","LAB_PERSON","LINKMAN_TEL","REGISTER_CODE","LAB_LINKMAN","STATE_FLAG","REMARK","LAST_MPERSON","FIRST_RTIME","SPELL_CODE","LAB_TEL","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_LAB" 

2025-07-01 13:38:49.551 +08:00 [INF] 【SQL执行耗时:377.2673ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-01 13:38:50.256 +08:00 [INF] 【SQL执行耗时:625.6859ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP" 

2025-07-01 13:38:50.667 +08:00 [INF] 【SQL执行耗时:335.2512ms】

[Sql]:SELECT "AREA_ID","AREA_CODE","AREA_SORT","AREA_NAME","AREA_CLASS","AREA_PERSON","AREA_ADDRESS","AREA_TEL","SPELL_CODE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","STATE_FLAG","REMARK","HOSPITAL_ID","TAG_SIGN","AREA_ANAME","PACK_BAR_PREFIX" FROM "XH_SYS"."SYS6_INSPECTION_AREA" 

2025-07-01 13:39:12.315 +08:00 [INF] ==>App Start..2025-07-01 13:39:12
2025-07-01 13:39:12.491 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-01 13:39:12.494 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-01 13:39:13.901 +08:00 [INF] ==>基础连接请求完成.
2025-07-01 13:39:14.273 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-01 13:39:14.619 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-01 13:39:14.942 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-01 13:39:14.949 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-01 13:39:15.277 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-01 13:39:15.709 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-01 13:39:15.814 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-01 13:39:16.253 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-01 13:39:16.253 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-01 13:39:17.156 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-01 13:39:19.321 +08:00 [INF] ==>初始化完成..
2025-07-01 13:39:19.341 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-01 13:39:19.343 +08:00 [INF] 设备启用任务
2025-07-01 13:39:19.343 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-01 13:39:19.743 +08:00 [INF] 【SQL执行耗时:379.6022ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-01 13:39:19.878 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-01 13:39:19.890 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-01 13:39:19.891 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-01 13:39:19.891 +08:00 [INF] Hosting environment: Development
2025-07-01 13:39:19.892 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-01 13:39:25.067 +08:00 [INF] 【SQL执行耗时:354.2573ms】

[Sql]:SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_ID" = :EqpNoId0 ) 
[Pars]:
[Name]::EqpNoId0 [Value]:******************************** [Type]:String    

2025-07-01 13:39:25.600 +08:00 [INF] 【SQL执行耗时:398.0555ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_ID" = :EQUIPMENT_ID0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_ID0 [Value]:1721768701646213120 [Type]:String    

2025-07-01 13:39:26.049 +08:00 [INF] 【SQL执行耗时:365.6769ms】

[Sql]:SELECT "HOSPITAL_ID","HOSPITAL_LOGIN","HOSPITAL_CNAME","HOSPITAL_SNAME","HOSPITAL_TYPE","HOSPITAL_LEVEL","LEVEL1_CODE","LEVEL2_CODE","LEVEL3_CODE","HOSPITAL_BARCODE","HOSPITAL_PROPERTY","HOSPITAL_PERSON","HOSPITAL_ADDRESS","HOSPITAL_TEL","HOSPITAL_STATE","FIRST_PERSON","FIRST_RTIME","LAST_MPRESON","LAST_MTIME","REMARK","HOSPITAL_BCODE","HOSPITAL_RANGE","BARCODE_TYPE","BARCODE_RANGE","CHARGE_PROVIDE_TYPE","DATADB_IP","WECHAT_NAME","LAST_MPERSON","FIRST_RPERSON","UPDATE_FLAG","INSERFACE_SERVICE","SAMELEVEL_HOSPITAL","HOSPITAL_CLASS","HOSPITAL_URL","REGISTER_CODE","PACK_BAR_PREFIX","IF_ITEM_MANAGE","IF_TEMPLATE_HOS","MEDICAL_CONGENER","MEDICAL_CONGENER_ROLE","VEST_IN_DC","INSTANCE_ID","HOSPITAL_ATTRIBUTE","DATASYNC_TYPE","HGROUP_ID","CITEM_MANAGE" FROM "XH_SYS"."SYS6_HOSPITAL_INFO" 

2025-07-01 13:39:26.493 +08:00 [INF] 【SQL执行耗时:341.1237ms】

[Sql]:SELECT "LAB_ID","FIRST_RPERSON","LAB_NAME","LAB_ADDRESS","LAB_CODE","LAST_MTIME","LAB_SORT","LAB_RNAME","LAB_CLASS","LAB_PERSON","LINKMAN_TEL","REGISTER_CODE","LAB_LINKMAN","STATE_FLAG","REMARK","LAST_MPERSON","FIRST_RTIME","SPELL_CODE","LAB_TEL","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_LAB" 

2025-07-01 13:39:26.930 +08:00 [INF] 【SQL执行耗时:357.0822ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-01 13:39:27.380 +08:00 [INF] 【SQL执行耗时:369.9069ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP" 

2025-07-01 13:39:27.805 +08:00 [INF] 【SQL执行耗时:346.5745ms】

[Sql]:SELECT "AREA_ID","AREA_CODE","AREA_SORT","AREA_NAME","AREA_CLASS","AREA_PERSON","AREA_ADDRESS","AREA_TEL","SPELL_CODE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","STATE_FLAG","REMARK","HOSPITAL_ID","TAG_SIGN","AREA_ANAME","PACK_BAR_PREFIX" FROM "XH_SYS"."SYS6_INSPECTION_AREA" 

2025-07-01 13:39:27.902 +08:00 [INF] HTTP GET /api/CodeCustom/GetEquipmentUCodePreview/******************************** responded 200 in 5593.3041 ms
2025-07-01 13:39:27.904 +08:00 [INF] 【接口超时阀值预警】 [aab11bc694d9b1cc71b9af7ad058db76]接口/api/CodeCustom/GetEquipmentUCodePreview/********************************,耗时:[5600]毫秒
2025-07-01 13:43:06.853 +08:00 [INF] ==>App Start..2025-07-01 13:43:06
2025-07-01 13:43:07.025 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-01 13:43:07.028 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-01 13:43:08.562 +08:00 [INF] ==>基础连接请求完成.
2025-07-01 13:43:08.925 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-01 13:43:09.280 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-01 13:43:09.609 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-01 13:43:09.612 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-01 13:43:10.002 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-01 13:43:10.350 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-01 13:43:10.442 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-01 13:43:10.856 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-01 13:43:10.856 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-01 13:43:11.746 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-01 13:43:13.986 +08:00 [INF] ==>初始化完成..
2025-07-01 13:43:14.006 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-01 13:43:14.009 +08:00 [INF] 设备启用任务
2025-07-01 13:43:14.009 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-01 13:43:14.387 +08:00 [INF] 【SQL执行耗时:357.4417ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-01 13:43:14.533 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-01 13:43:14.547 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-01 13:43:14.549 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-01 13:43:14.549 +08:00 [INF] Hosting environment: Development
2025-07-01 13:43:14.550 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-01 13:43:27.225 +08:00 [INF] 【SQL执行耗时:369.7725ms】

[Sql]:SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" <> :EqpNoState0 ) 
[Pars]:
[Name]::EqpNoState0 [Value]:2 [Type]:String    

2025-07-01 13:43:27.386 +08:00 [INF] HTTP GET /api/CodeCustom/GetEquipmentCodeCustomDict responded 200 in 3345.8806 ms
2025-07-01 13:43:27.388 +08:00 [INF] 【接口超时阀值预警】 [a580d5f679820f3545556fa05feed2b2]接口/api/CodeCustom/GetEquipmentCodeCustomDict,耗时:[3354]毫秒
2025-07-01 13:43:40.888 +08:00 [INF] 【SQL执行耗时:341.3015ms】

[Sql]:SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" <> :EqpNoState0 ) 
[Pars]:
[Name]::EqpNoState0 [Value]:2 [Type]:String    

2025-07-01 13:43:40.991 +08:00 [INF] HTTP GET /api/CodeCustom/GetEquipmentCodeCustomDict responded 200 in 490.4480 ms
2025-07-01 13:50:51.959 +08:00 [INF] ==>App Start..2025-07-01 13:50:51
2025-07-01 13:50:52.129 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-01 13:50:52.132 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-01 13:51:03.304 +08:00 [INF] ==>基础连接请求完成.
2025-07-01 13:51:03.696 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-01 13:51:04.111 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-01 13:51:04.445 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-01 13:51:04.452 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-01 13:51:04.804 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-01 13:51:05.426 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-01 13:51:05.545 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-01 13:51:06.005 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-01 13:51:06.006 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-01 13:51:06.999 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-01 13:51:09.247 +08:00 [INF] ==>初始化完成..
2025-07-01 13:51:09.267 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-01 13:51:09.268 +08:00 [INF] 设备启用任务
2025-07-01 13:51:09.269 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-01 13:51:09.655 +08:00 [INF] 【SQL执行耗时:364.6697ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-01 13:51:09.807 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-01 13:51:09.821 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-01 13:51:09.822 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-01 13:51:09.823 +08:00 [INF] Hosting environment: Development
2025-07-01 13:51:09.823 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-01 15:07:06.152 +08:00 [INF] ==>App Start..2025-07-01 15:07:06
2025-07-01 15:07:06.370 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-01 15:07:06.375 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-01 15:07:07.920 +08:00 [INF] ==>基础连接请求完成.
2025-07-01 15:07:08.481 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-01 15:07:08.891 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-01 15:07:09.226 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-01 15:07:09.229 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-01 15:07:09.562 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-01 15:07:10.007 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-01 15:07:10.089 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-01 15:07:10.556 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-01 15:07:10.556 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-01 15:07:14.613 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-01 15:07:16.866 +08:00 [INF] ==>初始化完成..
2025-07-01 15:07:16.887 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-01 15:07:16.889 +08:00 [INF] 设备启用任务
2025-07-01 15:07:16.890 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-01 15:07:17.301 +08:00 [INF] 【SQL执行耗时:390.8973ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-01 15:07:17.439 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-01 15:07:17.452 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-01 15:07:17.452 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-01 15:07:17.453 +08:00 [INF] Hosting environment: Development
2025-07-01 15:07:17.453 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-01 15:07:24.320 +08:00 [INF] HTTP GET /api/CodeCustom/GetCustomDictCode responded 200 in 4394.1622 ms
2025-07-01 15:07:24.381 +08:00 [INF] 【接口超时阀值预警】 [0d360744904e3d1a060668f1bf5fd7e0]接口/api/CodeCustom/GetCustomDictCode,耗时:[4459]毫秒
2025-07-01 15:07:58.154 +08:00 [INF] HTTP GET /api/CodeCustom/GetCustomDictCode responded 200 in 21320.9110 ms
2025-07-01 15:07:58.155 +08:00 [INF] 【接口超时阀值预警】 [d8edc90fc27de9ea777b372393f9f6c5]接口/api/CodeCustom/GetCustomDictCode,耗时:[21321]毫秒
2025-07-01 15:09:14.741 +08:00 [INF] HTTP GET /api/CodeCustom/GetCustomDictCode responded 200 in 1712.3707 ms
2025-07-01 15:09:14.742 +08:00 [INF] 【接口超时阀值预警】 [1b3ac343d57de0e2a05e60e8037d8fc3]接口/api/CodeCustom/GetCustomDictCode,耗时:[1712]毫秒
2025-07-01 15:13:25.098 +08:00 [INF] ==>App Start..2025-07-01 15:13:25
2025-07-01 15:13:25.269 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-01 15:13:25.272 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-01 15:13:26.683 +08:00 [INF] ==>基础连接请求完成.
2025-07-01 15:13:27.648 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-01 15:13:28.016 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-01 15:13:28.338 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-01 15:13:28.340 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-01 15:13:29.033 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-01 15:13:29.118 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-01 15:13:29.181 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-01 15:13:29.595 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-01 15:13:29.595 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-01 15:13:33.235 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-01 15:13:35.431 +08:00 [INF] ==>初始化完成..
2025-07-01 15:13:35.451 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-01 15:13:35.452 +08:00 [INF] 设备启用任务
2025-07-01 15:13:35.453 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-01 15:13:35.863 +08:00 [INF] 【SQL执行耗时:389.6215ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-01 15:13:36.006 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-01 15:13:36.018 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-01 15:13:36.019 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-01 15:13:36.020 +08:00 [INF] Hosting environment: Development
2025-07-01 15:13:36.020 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-01 15:13:48.091 +08:00 [INF] HTTP PUT /api/CodeCustom/UpdateEquipmentCodeCustomDict/ responded 404 in 4198.6294 ms
2025-07-01 15:13:48.119 +08:00 [INF] 【接口超时阀值预警】 [2be7719e731efef6b7d9d5d8c1ff7770]接口/api/CodeCustom/UpdateEquipmentCodeCustomDict/,耗时:[4231]毫秒
2025-07-01 15:13:51.819 +08:00 [INF] HTTP PUT /api/CodeCustom/UpdateEquipmentCodeCustomDict/ responded 404 in 164.3577 ms
2025-07-01 15:14:01.818 +08:00 [INF] HTTP GET /api/CodeCustom/GetCustomDictCode responded 200 in 440.4113 ms
