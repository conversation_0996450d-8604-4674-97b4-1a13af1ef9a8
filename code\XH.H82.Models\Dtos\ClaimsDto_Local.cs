﻿using System.ComponentModel.DataAnnotations.Schema;

namespace XH.H82.Models.Dtos
{
    /// <summary>
    /// token 解析后的对象
    /// </summary>
    public class ClaimsDto_Local
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public string USER_NO { get; set; }
        public string LOGID { get; set; }
        public string USER_NAME { get; set; }
        public string HOSPITAL_ID { get; set; }
        public string LAB_ID { get; set; }
        public string PGROUP_ID { get; set; }
        public string FILE_UPLOAD_ADDRESS { get; set; }
        public string FILE_PREVIEW_ADDRESS { get; set; }
        public string MGROUPID_AGGREGATE { get; set; }

    }
}