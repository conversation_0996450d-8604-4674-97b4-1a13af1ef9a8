﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using H.Utility.SqlSugarInfra;
using SqlSugar;

namespace XH.H82.Models.Entities
{
    [DBOwner("XH_SYS")]
    public class LIS_REQUISITION_INFO
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string requisition_id { get; set; }
        public string patient_name { get; set; }

    }
}
