using H.Utility.SqlSugarInfra;
using Microsoft.EntityFrameworkCore;
using SqlSugar;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace XH.H82.Models.Entities.Tim
{
    [DBOwner("XH_OA")]
    public class TIM_WORK_FORM
    {
        /// <summary>
        /// 记录单ID
        /// </summary>
        [Key]
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        [Column("FORM_ID")]
        [Required(ErrorMessage = "记录单ID不允许为空")]

        [StringLength(50, ErrorMessage = "记录单ID长度不能超出50字符")]
        [Unicode(false)]
        public string FORM_ID { get; set; }

        /// <summary>
        /// 填写页面布局1左右结构2上下结构
        /// </summary>
        [Column("FILL_LAYOUT")]
        [StringLength(10, ErrorMessage = "填写页面布局1左右结构2上下结构长度不能超出10字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string FILL_LAYOUT { get; set; }

        /// <summary>
        /// 首次登记人
        /// </summary>
        [Column("FIRST_RPERSON")]
        [StringLength(50, ErrorMessage = "首次登记人长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string FIRST_RPERSON { get; set; }

        /// <summary>
        /// 科室ID
        /// </summary>
        [Column("LAB_ID")]
        [Required(ErrorMessage = "科室ID不允许为空")]

        [StringLength(20, ErrorMessage = "科室ID长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string LAB_ID { get; set; }

        /// <summary>
        /// 自动归档0否1是
        /// </summary>
        [Column("IF_AUTO_ISSUED")]
        [StringLength(10, ErrorMessage = "自动归档0否1是长度不能超出10字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string IF_AUTO_ISSUED { get; set; }

        /// <summary>
        /// 是否有主体0无1有
        /// </summary>
        [Column("IF_MAIN")]
        [StringLength(10, ErrorMessage = "是否有主体0无1有长度不能超出10字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string IF_MAIN { get; set; }

        /// <summary>
        /// 纵向多列数
        /// </summary>
        [Column("COL_NUM")]
        [Unicode(false)]
        public decimal? COL_NUM { get; set; }


        public int? HEADER_NUM { get; set; }

        /// <summary>
        /// 记录单状态0废止1在用
        /// </summary>
        [Column("FORM_STATE")]
        [StringLength(20, ErrorMessage = "记录单状态0废止1在用长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string FORM_STATE { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        [Column("LAST_MTIME")]
        [Unicode(false)]
        public DateTime? LAST_MTIME { get; set; }

        /// <summary>
        /// 主体类型
        /// </summary>
        [Column("MAIN_TYPE")]
        [StringLength(10, ErrorMessage = "主体类型长度不能超出10字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string MAIN_TYPE { get; set; }

        /// <summary>
        /// 归档时机
        /// </summary>
        [Column("ISSUED_OPPORTUNITY")]
        [StringLength(10, ErrorMessage = "归档时机长度不能超出10字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string ISSUED_OPPORTUNITY { get; set; }

        /// <summary>
        /// 记录单名称
        /// </summary>
        [Column("FORM_NAME")]
        [StringLength(200, ErrorMessage = "记录单名称长度不能超出200字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string FORM_NAME { get; set; }

        /// <summary>
        /// 记录单描述
        /// </summary>
        [Column("FORM_DESC")]
        [StringLength(500, ErrorMessage = "记录单描述长度不能超出500字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string FORM_DESC { get; set; }

        /// <summary>
        /// 归档计划1日2周3月4季5年0每次
        /// </summary>
        [Column("ISSUED_PLAN")]
        [StringLength(10, ErrorMessage = "归档计划1日2周3月4季5年0每次长度不能超出10字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string ISSUED_PLAN { get; set; }

        /// <summary>
        /// 序号格式
        /// </summary>
        [Column("NO_FORMAT")]
        [StringLength(10, ErrorMessage = "序号格式长度不能超出10字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string NO_FORMAT { get; set; }

        /// <summary>
        /// 排序号
        /// </summary>
        [Column("DOC_SORT")]
        [StringLength(20, ErrorMessage = "排序号长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string DOC_SORT { get; set; }

        /// <summary>
        /// 页眉/页脚设置
        /// </summary>
        [Column("HEADER_FOOTER")]
        [StringLength(10, ErrorMessage = "页眉/页脚设置长度不能超出10字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string HEADER_FOOTER { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Column("REMARK")]
        [StringLength(200, ErrorMessage = "备注长度不能超出200字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string REMARK { get; set; }

        /// <summary>
        /// 记录单分类ID
        /// </summary>
        [Column("CLASS_ID")]
        [Required(ErrorMessage = "记录单分类ID不允许为空")]

        [StringLength(20, ErrorMessage = "记录单分类ID长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string CLASS_ID { get; set; }

        /// <summary>
        /// 记录单简称
        /// </summary>
        [Column("FORM_SNAME")]
        [StringLength(100, ErrorMessage = "记录单简称长度不能超出100字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string FORM_SNAME { get; set; }

        /// <summary>
        /// 首次登记时间
        /// </summary>
        [Column("FIRST_RTIME")]
        [Unicode(false)]
        public DateTime? FIRST_RTIME { get; set; }

        /// <summary>
        /// 最后修改人员
        /// </summary>
        [Column("LAST_MPERSON")]
        [StringLength(50, ErrorMessage = "最后修改人员长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string LAST_MPERSON { get; set; }

        /// <summary>
        /// 归属分类
        /// </summary>
        [Column("FORM_CLASS")]
        [StringLength(50, ErrorMessage = "归属分类长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string FORM_CLASS { get; set; }

        /// <summary>
        /// 归档时间
        /// </summary>
        [Column("ISSUED_HOUR")]
        [StringLength(10, ErrorMessage = "归档时间长度不能超出10字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string ISSUED_HOUR { get; set; }

        /// <summary>
        /// 检验专业组ID
        /// </summary>
        [Column("PGROUP_ID")]
        [Required(ErrorMessage = "检验专业组ID不允许为空")]

        [StringLength(20, ErrorMessage = "检验专业组ID长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string PGROUP_ID { get; set; }

        /// <summary>
        /// 记录单编号
        /// </summary>
        [Column("FORM_NO")]
        [StringLength(50, ErrorMessage = "记录单编号长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string FORM_NO { get; set; }

        /// <summary>
        /// 归档类型1单次单表2多次汇总
        /// </summary>
        [Column("FILL_TYPE")]
        [StringLength(10, ErrorMessage = "归档类型1单次单表2多次汇总长度不能超出10字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string FILL_TYPE { get; set; }

        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string LAYOUT_RATIO { get; set; }

        /// <summary>
        /// 医疗机构ID
        /// </summary>
        [Column("HOSPITAL_ID")]
        [Required(ErrorMessage = "医疗机构ID不允许为空")]

        [StringLength(20, ErrorMessage = "医疗机构ID长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string HOSPITAL_ID { get; set; }

        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string ISSUED_CHECK { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string FORM_SOURCE { get; set; }
        //[SugarColumn(SqlParameterDbType = System.Rows.DbType.AnsiString)]
        public string ARCHIVE_RENAME { get; set; }

        /// <summary>
        /// 填写方式(1事务项 2上传 3线上填写,默认1)
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string FILL_METHOD { get; set; }

    }
}
