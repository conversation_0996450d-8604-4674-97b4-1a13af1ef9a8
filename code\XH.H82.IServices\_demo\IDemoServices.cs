﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XH.H82.Models.Dtos;
using XH.H82.Models.Entities;

namespace XH.H82.IServices
{
    public interface IDemoServices
    {
        public List<TEST_START_TEMPLATE> GetDemoData();
        public List<LIS_REQUISITION_INFO> TestDB2EF();
        public int BulkInsert(List<TEST_START_TEMPLATE> bulkModels);
        public void BulkDelete(List<TEST_START_TEMPLATE> bulkModels);
        public int DeleteByPredicate();
        public void UpdateByPredicate();
        public List<TEST_START_TEMPLATE> QueryBySQL();

        /// <summary>
        /// 演示缓存AOP用法
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public List<TEST_START_TEMPLATE> GetDemoDateWithCache(string id);

        /// <summary>
        /// 演示automapper 用法
        /// </summary>
        /// <returns></returns>
        public List<StartTemplateDto> AutoMapTest();
    }
}
