﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Security.Cryptography;
using System.Threading.Channels;
using XH.H82.Models.ViewDtos;
using SqlSugar;
using H.Utility.SqlSugarInfra;

namespace XH.H82.Models.Entities.Common
{
    [DBOwner("XH_SYS")]
    [SugarTable("LIS6_INSTRUMENT_ITEM")]
    public class LIS6_INSTRUMENT_ITEM
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string? CHANNEL_ID { get; set; }
        //public string? UNIT_ID { get; set; }
        public string? INSTRUMENT_ID { get; set; }
        public string? ITEM_ID { get; set; }
        public string? INSTRUMENT_SNUM { get; set; }
        public string? ITEM_NAME { get; set; }
        public string? ITEM_CODE { get; set; }

        public string? ITEM_SORT { get; set; }
        public string? CHANNEL { get; set; }
        public string? SAMPLE_CLASS { get; set; }
        public string? RATIO { get; set; }
        public string? CHANNEL_TYPE { get; set; }
        public string? REMARK { get; set; }
        public string? TEST_DURATION { get; set; }
        public string? TEST_METHOD { get; set; }
        public string? TEST_ITEM_UNIT { get; set; }
        public string? VALUE_TYPE { get; set; }
        public string? CHANNEL_STATE { get; set; }
    }
}
