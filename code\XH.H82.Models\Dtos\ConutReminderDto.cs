﻿namespace XH.H82.Models.Dtos;

public class ConutReminderDto
{
    /// <summary>
    /// No 序号  也是前端渲染的主键
    /// </summary>
    public int No { get; set; }
    /// <summary>
    /// 类型
    /// </summary>
    public string ReminderType { get; set; }

    /// <summary>
    /// 日期
    /// </summary>
    public DateTime? Date { get; set; }
    /// <summary>
    /// 设备名称
    /// </summary>
    public string EquipmentName { get; set; }
        
    /// <summary>
    /// 生安设备类型
    /// </summary>
    public string SmblClass {get; set;}
    /// <summary>
    /// 设备型号
    /// </summary>
    public string EquipmentModel{ get; set; }
    /// <summary>
    /// 内容   特殊事件类型的才有值  优先显示特殊事件
    /// </summary>
    /// <returns></returns>
    public string Content { get; set; }

    /// <summary>
    /// 状态没有超期  只有特殊事件   待执行 
    /// </summary>
    public string Status {get; set; }

}