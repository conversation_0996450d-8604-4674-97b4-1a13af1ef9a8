﻿using H.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XH.H82.Models.Dtos;

namespace XH.H82.IServices
{
    public interface IXhScrapStopService
    {
        ResultDto ScrapStopApply(List<ScrapStopListDto> record, string hospitalId, string userName, string userNo, string state);

        ResultDto SaveApply(List<ScrapStopListDto> record, string hospitalId, string userName, string userNo, string state);

        ResultDto BatchSubmit(List<ScrapStopListDto> record, string userName, string userNo, string hospitalId);

        ResultDto ApplyReject(List<ScrapStopListDto> record, string hospitalId, string userName, string userNo);

        ResultDto ApplyRevoke(List<ScrapStopListDto> record, string userName, string userNo);

        ResultDto ApplyAdopt(List<ScrapStopListDto> record, string hospitalId, string userName, string userNo, string file_upload_address);
    }
}
