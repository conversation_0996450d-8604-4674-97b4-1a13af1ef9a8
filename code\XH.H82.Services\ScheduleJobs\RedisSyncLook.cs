﻿using H.Utility.Helper;
using Serilog;
using StackExchange.Redis;

namespace XH.H82.Services.ScheduleJobs;

public static class RedisSyncLook
{
    public static async Task<bool> GetTheLock(string key , string minutes, string serviceName)
    {
        var time = TimeSpan.FromMinutes(int.Parse(minutes));
        var redis = XhRedisHelper.UseS02Client();
        var result = await redis.AddAsync(key, $"{serviceName}", time, When.NotExists);
        return result;
    }
    
    public static async Task GetTheLockFalse(string key)
    {
        var redis = XhRedisHelper.UseS02Client();
        var redisS02 = XhRedisHelper.UseS02();
        var serviceName = await redis.GetAsync<string>(key);
        var ttl = await redisS02.KeyTimeToLiveAsync(key);
        if (ttl.HasValue)
        {
            if (ttl.Value.Seconds / 3600 >= 1)
            {
                Log.Warning($"已有服务正在执行，服务名：{serviceName},剩余时长未{ttl / 3600}小时");
            }
            if (ttl.Value.Seconds / 3600 < 1)
            {
                Log.Warning($"已有服务正在执行，服务名：{serviceName},剩余时长未{ttl}秒");
            }
        }
        else
        {
            Log.Warning($"不存在服务正在执行，服务名：{serviceName},剩余时长未{ttl}秒");
        }
    }

    public static async Task EndTheLock(string key)
    {
        var redis =XhRedisHelper.UseS02Client();
        var redisS02 = XhRedisHelper.UseS02();
        var isDeleted = await redis.RemoveAsync(key);
        if (isDeleted)
        {
            Log.Information($"已有停止正在执行的定时任务，服务名：IoTDeviceSync");
        }
        else
        {
            var serviceName = await redis.GetAsync<string>(key);
            var ttl = await redisS02.KeyTimeToLiveAsync(key);
            Log.Error($"已有停止正在执行的定时任务，服务名{serviceName}：IoTDeviceSync,缓存键删除失败，剩余时长为:{ttl}秒");
        }
        
    }
}