﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using XH.H82.Models.Entities;
using XH.H82.Models.Entities.Certificate;
using XH.H82.Models.Entities.InkScreen;
using XH.H82.Models.Entities.WarnRecord;

namespace XH.H82.Models.TemplateDesign;

[DBOwner("XH_OA")]
[SugarTable("EMS_EQUIPMENT_INFO")]
public class Equipment
{
    
    /// <summary>
    /// 所属专业组
    /// </summary>
    [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "所属专业组")]
    public string UNIT_ID { get; set; }

    /// <summary>
    /// 机构id
    /// </summary>
    [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "所属医疗机构")]
    public string HOSPITAL_ID { get; set; }

    /// <summary>
    /// 实验室id
    /// </summary>
    [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "所属科室")]
    public string LAB_ID { get; set; }

    /// <summary>
    /// 专业类型 
    /// </summary>
    [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "专业分类")]
    public string PROFESSIONAL_CLASS { get; set; }
    

    /// <summary>
    /// 设备序号
    /// </summary>
    [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "设备序号")]
    public string EQUIPMENT_NUM { get; set; }

    /// <summary>
    /// 设备名称
    /// </summary>
    [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "设备名称")]
    public string EQUIPMENT_NAME { get; set; }

    /// <summary>
    ///  科室设备编号
    /// </summary>
    [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "科室设备编号")]
    public string DEPT_SECTION_NO { get; set; }

    /// <summary>
    /// 设备英文名称
    /// </summary>
    [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "设备英文名称")]
    public string EQUIPMENT_ENAME { get; set; }

    /// <summary>
    /// 设备型号
    /// </summary>
    [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "设备型号")]
    public string EQUIPMENT_MODEL { get; set; }

    /// <summary>
    /// 流水线所属（对应设备id）
    /// </summary>
    [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "所属流水线")]
    public string VEST_PIPELINE { get; set; }

    /// <summary>
    /// 设备分类
    /// </summary>
    [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "设备类型")]
    public string EQUIPMENT_CLASS { get; set; }
    
    /// <summary>
    /// 医院设备编号
    /// </summary>
    [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "医院设备编号")]
    public string SECTION_NO { get; set; }
    

    /// <summary>
    /// 设备价格(元）
    /// </summary>
    [SugarColumn(SqlParameterDbType = System.Data.DbType.Double, ColumnDescription = "设备价格")]
    public double? SELL_PRICE { get; set; }

    /// <summary>
    /// 设备责任人
    /// </summary>
    [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "设备责任人")]
    public string KEEP_PERSON { get; set; }

    /// <summary>
    /// 安装日期
    /// </summary>
    [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "安装日期")]
    public string INSTALL_DATE { get; set; }

    /// <summary>
    /// 安装位置
    /// </summary>
    [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "安装位置")]
    public string INSTALL_AREA { get; set; }

    /// <summary>
    /// 折旧时间
    /// </summary>
    [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "折旧年限")]
    public string DEPRECIATION_TIME { get; set; }

    /// <summary>
    /// 联系电话
    /// </summary>
    [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "联系方式")]
    public string CONTACT_PHONE { get; set; }
    
    /// <summary>
    /// 注册证号（中文）
    /// </summary>
    [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "注册证号")]
    public string REGISTRATION_NUM { get; set; }

    /// <summary>
    /// 注册证号（英文）
    /// </summary>
    [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "英文注册证号")]
    public string REGISTRATION_ENUM { get; set; }

    /// <summary>
    /// 设备状态：0-未启用；1-启用；2-停用 3-报废 
    /// </summary>
    [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "设备状态")]
    public string EQUIPMENT_STATE { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "备注")]
    public string REMARK { get; set; }
    

    /// <summary>
    /// 到货日期
    /// </summary>
    [SugarColumn(SqlParameterDbType = System.Data.DbType.DateTime, ColumnDescription = "到货日期")]
    public DateTime? EQ_IN_TIME { get; set; }

    /// <summary>
    /// 出厂日期
    /// </summary>
    [SugarColumn(SqlParameterDbType = System.Data.DbType.DateTime, ColumnDescription = "出厂日期")]
    public DateTime? EQ_OUT_TIME { get; set; }


    /// <summary>
    /// 序列号
    /// </summary>
    [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "设备序列号")]
    public string SERIAL_NUMBER { get; set; }

    /// <summary>
    /// 设备代号
    /// </summary>
    [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "设备代号")]
    public string EQUIPMENT_CODE { get; set; }
    
    /// <summary>
    /// 首次启用日期
    /// </summary>
    [SugarColumn(SqlParameterDbType = System.Data.DbType.DateTime, ColumnDescription = "首次启用日期")]
    public DateTime? ENABLE_TIME { get; set; }
    

    /// <summary>
    /// 使用年限
    /// </summary>
    [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "使用年限")]
    public string? EQ_SERVICE_LIFE { get; set; }

    /// <summary>
    /// 生物安全
    /// </summary>
    [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "生物安全")]
    public string? SMBL_FLAG { get; set; } = "0";

    /// <summary>
    /// 生安监测实验室ID
    /// </summary>
    [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "备案实验室")]
    public string? SMBL_LAB_ID { get; set; }
    
    /// <summary>
    /// 设备自身的自定义代号
    /// </summary>
    [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "设备自定义代号")]
    public string? EQUIPMENT_UCODE { get; set; }

    /// <summary>
    /// 生安设备类型
    /// </summary>
    [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "生安设备类型")]
    public string? SMBL_CLASS { get; set; }
    
    /// <summary>
    /// 设备产出地 （国产，进口）
    /// </summary>
    [SugarColumn(SqlParameterDbType = System.Data.DbType.Int32, ColumnDescription = "设备产出地")]
    public CountryOriginEnum COUNTRY_ORIGIN { get; set; }
    
    
}

