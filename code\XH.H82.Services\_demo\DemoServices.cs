﻿using H.IRepository;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using Dapper;
using H.Utility;
using Oracle.ManagedDataAccess.Client;
using XH.H82.IServices;
using XH.H82.Models;
using XH.H82.Models.Dtos;
using XH.H82.Models.Entities;
using AutoMapper.QueryableExtensions;

namespace XH.H82.Services
{
    public class DemoServices : IDemoServices
    {
        //注意:为了书写方便 IUnitOfWork默认指向DBContext_Master,你也可以显示指定IUnitOfWork<DBContext_Master>
        private readonly IUnitOfWork _uowOacle;
        private readonly IUnitOfWork<MainDBContext> _uowSqlserver;
        private readonly IMapper _mapper;

        public DemoServices(IUnitOfWork uow_oacle,/*IUnitOfWork<DBContext_Master2> uow_sqlserver,*/IMapper mapper)
        {
            _uowOacle = uow_oacle;
            //第二个数据库
           //_uowSqlserver = uow_sqlserver;
           _mapper = mapper;
        }

        public int BulkInsert(List<TEST_START_TEMPLATE> bulkModels)
        {
            _uowOacle.GetRepository<TEST_START_TEMPLATE>().BulkInsert(bulkModels);
            return 1;
        }

        public void BulkDelete(List<TEST_START_TEMPLATE> bulkModels)
        {
            _uowOacle.GetRepository<TEST_START_TEMPLATE>().BulkDelete(bulkModels);
        }

        public List<TEST_START_TEMPLATE> GetDemoData()
        {
            return _uowOacle.GetRepository<TEST_START_TEMPLATE>().DbSet().ToList();
        }

        public List<LIS_REQUISITION_INFO> TestDB2EF()
        {
            return _uowSqlserver.GetRepository<LIS_REQUISITION_INFO>().DbSet().ToList();
        }

        public int DeleteByPredicate()
        {
            return _uowOacle.GetRepository<TEST_START_TEMPLATE>().Delete(p => p.CREATE_TIME < DateTime.Now);
        }

        public void UpdateByPredicate()
        {
            _uowOacle.GetRepository<TEST_START_TEMPLATE>().Update(p =>p.VALUE<50,p=>new TEST_START_TEMPLATE()
            {
                VALUE = 5000
            });
        }

        public List<TEST_START_TEMPLATE> QueryBySQL()
        {
            //传参
            var p = new DynamicParameters();
            p.Add(":value", 50);
            //dapper查询返回dataset
            var resds =  _uowOacle.DapperQueryDataset(" select * from TEST_START_TEMPLATE where value<@value",
                p);
            //返回对象
            var res2 = _uowOacle.DapperQuery<TEST_START_TEMPLATE>(" select * from TEST_START_TEMPLATE where value<@value",p).ToList();


            //存储过程
            OracleParameter[] p1 = new OracleParameter[]
            {
                new OracleParameter()
                {
                    ParameterName = "str",
                    Value = "职务",
                },
                new OracleParameter()
                {
                    Direction = ParameterDirection.Output,
                    OracleDbType = OracleDbType.RefCursor
                }
            };

           var res= _uowOacle.QueryWithSql("GET_START_TEMPLATE_TEST_SP", CommandType.StoredProcedure,p1);

           //XML节点

            return res2;
        }
        [Cache(AbsoluteExpiration =5,CacheKeyPrefix = "C31:CACHE")]
        public List<TEST_START_TEMPLATE> GetDemoDateWithCache(string id)
        {
            var q=_uowOacle.GetRepository<TEST_START_TEMPLATE>().Find(p => p.ID == id);
            return q.ToList();
        }

        public List<StartTemplateDto> AutoMapTest()
        {
            //查询时映射(推荐)
            var res= _uowOacle.GetRepository<TEST_START_TEMPLATE>().DbSet().Take(100)
               .ProjectTo<StartTemplateDto>(_mapper.ConfigurationProvider)
               .ToList();

            //或者查询后映射
            var q1 = _uowOacle.GetRepository<TEST_START_TEMPLATE>().DbSet().Take(100)
                .ToList();
             var res1=_mapper.Map<List<StartTemplateDto>>(q1);
            //////////////////
            return res;  
        }
    }
}
