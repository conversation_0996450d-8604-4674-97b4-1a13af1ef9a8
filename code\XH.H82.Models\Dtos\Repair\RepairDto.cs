﻿using H.Utility;
using XH.H82.Models.Dtos.Maintain;
using XH.H82.Models.Entities;

namespace XH.H82.Models.Dtos.Repair;

public class RepairDto
{
    public static EMS_REPAIR_INFO CreateModule(string equipmentId, string? hospitalId  , RepairInput input)
    {
        var record = new EMS_REPAIR_INFO();
        record.EQUIPMENT_ID = equipmentId;
        record.REPAIR_ID = IDGenHelper.CreateGuid();
        record.HOSPITAL_ID = hospitalId ?? "H0000";
        record.REPAIR_DATE = input.REPAIR_DATE;
        record.REPAIR_PERSON = input.REPAIR_PERSON;
        record.REPAIR_RESULT = input.REPAIR_RESULT;
        if (record.REPAIR_PERSON.IsNotNullOrEmpty() && record.REPAIR_PERSON.Contains("_"))
        {
            record.REPAIR_PERSON = record.REPAIR_PERSON.Split('_')[1];
        }
        record.REPAIR_CONTENT = input.REPAIR_CONTENT;
        record.REMARK = input.REMARK;
        record.OCCUR_EVENT = input.OCCUR_EVENT;
        record.REPAIR_STATE = "1";
        return record;
    }
}

/// <summary>
/// 维修记录入参模型
/// </summary>
/// <param name="REPAIR_DATE">维修事件</param>
/// <param name="REPAIR_PERSON">维修人员</param>
/// <param name="REPAIR_CONTENT">维修内容</param>
/// <param name="REPAIR_RESULT">维修结果</param>
/// <param name="OCCUR_EVENT">产生事件</param>
/// <param name="REMARK">备注</param>
public record RepairInput(
    DateTime? REPAIR_DATE,
    string? REPAIR_PERSON,
    string? REPAIR_CONTENT,
    string? REPAIR_RESULT,
    string OCCUR_EVENT,
    string? REMARK);