﻿using EasyCaching.Core;
using H.Utility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.Extensions.Caching.Memory;
using XH.H82.Base.Setup;
using XH.H82.IServices;
using XH.H82.Models.Dtos;

namespace XH.H82.API.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize]
    public class ScrapStopController : ControllerBase
    {
        private readonly IScrapStopService _scrapStopService;
        private readonly IXhScrapStopService _xhScrapStopService;
        private readonly IModuleLabGroupService _IModuleLabGroupService;
        private readonly string file_preview_address;
        private readonly string file_upload_address;
        private readonly IConfiguration _configuration;
        private readonly IMemoryCache _easyCacheMemory;
        private string currentLabKey = "XH:LIS:H82:CURRENTLAB:UserSelectedLab:";
        private readonly string RedisModule;
        public ScrapStopController(IScrapStopService scrapStopService, IXhScrapStopService xhScrapStopService, IModuleLabGroupService iModuleLabGroupService
            , IConfiguration configuration, IMemoryCache easyCahceFactory)
        {
            _scrapStopService = scrapStopService;
            _xhScrapStopService = xhScrapStopService;
            _IModuleLabGroupService = iModuleLabGroupService;
            _configuration = configuration;
            file_preview_address = _configuration["S54"];
            file_upload_address = _configuration["S28"];
            RedisModule = _configuration["RedisModule"];
            _easyCacheMemory = easyCahceFactory;
        }

        /// <summary>
        ///   获取报废停用列表
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="mgroupId">管理专业组id</param>
        /// <param name="equipmentClass">设备类型</param>
        /// <param name="state">状态</param>
        /// <param name="applyClass">申请类型</param>
        /// <param name="equipmentKey">设备检索</param>
        /// <param name="personKey">人物检索</param>
        /// <param name="areaId">院区ID</param>
        /// <param name="pgroupId">检验专业组ID</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetScrapStopList([BindRequired] DateTime startTime, [BindRequired] DateTime endTime, string mgroupId, string equipmentClass, string state,
            string applyClass, string equipmentKey, string personKey,
             string person_select, string mgroup_select, string areaId, string pgroupId)
        {
            var claims = User.ToClaimsDto();
            var clientMac = GetLocalMac.GetMac();
            currentLabKey = currentLabKey + claims.USER_NO + "." + clientMac;
            var labId = _easyCacheMemory.Get<string>(currentLabKey);
            if (mgroupId != null)
            {
                if (mgroupId.Contains("MG") == false)
                {
                    pgroupId = mgroupId;
                    mgroupId = null;
                }
            }
            var res = _scrapStopService.GetScrapStopList(
                startTime, endTime, claims.HOSPITAL_ID, mgroupId, equipmentClass, state, applyClass,
                equipmentKey, personKey, file_preview_address,
                claims.USER_NO, person_select, mgroup_select, claims.MGROUP_ID, labId, areaId, pgroupId);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   获取设备列表
        /// </summary>
        /// <param name="mgroupId">专业组id</param>
        /// <param name="equipmentClass">设备类型</param>
        /// <param name="keyword">检索</param>
        /// <param name="areaId">院区ID</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetEquipmentApplyList(string mgroupId, string equipmentClass, string keyword, string areaId)
        {
            var claims = User.ToClaimsDto();
            var clientMac = GetLocalMac.GetMac();
            currentLabKey = currentLabKey + claims.USER_NO + "." + clientMac;
            var labId = _easyCacheMemory.Get<string>(currentLabKey);
            var res = _scrapStopService.GetEquipmentApplyList(claims.USER_NO, claims.HOSPITAL_ID, mgroupId, equipmentClass, keyword, labId, areaId);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   报废停用申请保存
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult SaveScrapStopApply([FromBody] List<ScrapStopListDto> record)
        {
            var claims = User.ToClaimsDto();
            var userName = claims.HIS_NAME; ;
            var res = _xhScrapStopService.ScrapStopApply(record, claims.HOSPITAL_ID, userName, claims.USER_NO, "0");
            return Ok(res);
        }


        /// <summary>
        ///   报废停用申请提交
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult SubmitScrapStopApply([FromBody] List<ScrapStopListDto> record, [BindRequired] string password)
        {
            var claims = User.ToClaimsDto();
            var user = _IModuleLabGroupService.GetUserInfo(claims.LOGID, password);
            // 用户检查
            if (user == null)
                return Ok(new ResultDto { success = false, msg = "密码错误" });
            var userName = claims.HIS_NAME;
            var res = _xhScrapStopService.ScrapStopApply(record, claims.HOSPITAL_ID, userName, claims.USER_NO, "1");
            return Ok(res);
        }

        /// <summary>
        ///   报废停用修改(保存)
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult SaveApply([FromBody] List<ScrapStopListDto> record)
        {
            var claims = User.ToClaimsDto();
            var userName = claims.HIS_NAME;
            var res = _xhScrapStopService.SaveApply(record, claims.HOSPITAL_ID, userName, claims.USER_NO, "0");
            return Ok(res);
        }

        /// <summary>
        ///   批量提交
        /// </summary>
        /// <param name="password">密码</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult BatchSubmit([FromBody] List<ScrapStopListDto> record, [BindRequired] string password)
        {
            var claims = User.ToClaimsDto();
            var user = _IModuleLabGroupService.GetUserInfo(claims.LOGID, password);
            // 用户检查
            if (user == null)
                return Ok(new ResultDto { success = false, msg = "密码错误" });
            var userName = claims.HIS_NAME;
            var res = _xhScrapStopService.BatchSubmit(record, userName, claims.USER_NO, claims.HOSPITAL_ID);
            return Ok(res);
        }

        /// <summary>
        ///   报废停用修改(提交)
        /// </summary>
        /// <param name="password">密码</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult SubmitApply([FromBody] List<ScrapStopListDto> record, [BindRequired] string password)
        {
            var claims = User.ToClaimsDto();
            var user = _IModuleLabGroupService.GetUserInfo(claims.LOGID, password);
            // 用户检查
            if (user == null)
                return Ok(new ResultDto { success = false, msg = "密码错误" });
            var userName = claims.HIS_NAME;
            var res = _xhScrapStopService.SaveApply(record, claims.HOSPITAL_ID, userName, claims.USER_NO, "1");
            return Ok(res);
        }


        /// <summary>
        ///   删除申请信息
        /// </summary>
        /// <returns></returns>
        [HttpDelete]
        public IActionResult DeleteAwaitSubmit([FromBody] List<ScrapStopListDto> record)
        {
            var res = _scrapStopService.DeleteAwaitSubmit(record);
            return Ok(res);
        }

        /// <summary>
        ///   撤销
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult ApplyRevoke([FromBody] List<ScrapStopListDto> record, [BindRequired] string password)
        {
            var claims = User.ToClaimsDto();
            var user = _IModuleLabGroupService.GetUserInfo(claims.LOGID, password);
            // 用户检查
            if (user == null)
                return Ok(new ResultDto { success = false, msg = "密码错误" });
            var userName = claims.HIS_NAME;
            var res = _xhScrapStopService.ApplyRevoke(record, userName, claims.USER_NO);
            return Ok(res);
        }

        /// <summary>
        ///   申请通过
        /// </summary>
        /// <param name="password">密码</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult ApplyAdopt([FromBody] List<ScrapStopListDto> record, [BindRequired] string password)
        {
            var claims = User.ToClaimsDto();
            var user = _IModuleLabGroupService.GetUserInfo(claims.LOGID, password);
            // 用户检查
            if (user == null)
                return Ok(new ResultDto { success = false, msg = "密码错误" });
            var userName = claims.HIS_NAME;

            var res = _xhScrapStopService.ApplyAdopt(record, claims.HOSPITAL_ID, userName, claims.USER_NO, file_upload_address);
            return Ok(res);
        }

        /// <summary>
        ///   申请驳回
        /// </summary>
        /// <param name="password">密码</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult ApplyReject([FromBody] List<ScrapStopListDto> record, [BindRequired] string password)
        {
            var claims = User.ToClaimsDto();
            var user = _IModuleLabGroupService.GetUserInfo(claims.LOGID, password);
            // 用户检查
            if (user == null)
                return Ok(new ResultDto { success = false, msg = "密码错误" });
            var userName = claims.HIS_NAME;
            var res = _xhScrapStopService.ApplyReject(record, claims.HOSPITAL_ID, userName, claims.USER_NO);
            return Ok(res);
        }

        /// <summary>
        ///   报废停用流程图
        /// </summary>
        /// <param name="scrapId">报废停用ID</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult ScrapStopProcess(string scrapId)
        {
            var res = _scrapStopService.ScrapStopProcess(scrapId);
            return Ok(res.ToResultDto());
        }
    }
}