﻿using H.Utility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Newtonsoft.Json;
using Serilog;
using SqlSugar;
using XH.H82.API.Extensions;
using XH.H82.IServices;
using XH.H82.IServices.EquipmentOnlyOffice;

namespace XH.H82.API.Controllers.FileTemplate
{

    /// <summary>
    /// onlyoffic相关接口
    /// </summary>
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize]
    public class FileTemplateController : ControllerBase
    {
        private readonly IEquipmentDocService _equipmentDocService;
        private readonly IConfiguration _configuration;
        private readonly IXhWorkPlanService _xhWorkPlanService;
        private IEquipmentOnlyOfficeService _equipmentOnlyOfficeService;


        public FileTemplateController(IEquipmentDocService equipmentDocService, IConfiguration configuration, IXhWorkPlanService xhWorkPlanService, IEquipmentOnlyOfficeService equipmentOnlyOfficeService)
        {
            _equipmentDocService = equipmentDocService;
            _configuration = configuration;
            _xhWorkPlanService = xhWorkPlanService;
            _equipmentOnlyOfficeService = equipmentOnlyOfficeService;
        }

        /// <summary>
        /// 根据模板预览设备基本信息（浏览功能及预览pdf）
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult ExportEquipmentPreview([BindRequired] string equipmentId, string styleId)
        {

            try
            {
                if (string.IsNullOrEmpty(equipmentId))
                {
                    throw new ArgumentNullException("请输入设备ID");
                }
                if (string.IsNullOrEmpty(styleId))
                {
                    throw new ArgumentNullException("请选择模板ID");
                }

                var ids = new List<string>() { $"{equipmentId}" };
                var info = _equipmentOnlyOfficeService.FillEquipmentInfoDatas(styleId, ids);
                //var dto = _equipmentOnlyOfficeService.FillEquipmentInfo(styleId, info[0]);
                var dto = _equipmentOnlyOfficeService.FillEquipmentTextAndRecords(styleId, info[0]);
                //dto.DATAS.RemoveAll(x => (!x.ARRAYS.Any() && !x.FIELDS.Any()));
                var fileStream = _equipmentOnlyOfficeService.PreviewFile(dto);
                Console.WriteLine(JsonConvert.SerializeObject(dto,Formatting.Indented));
                return File(fileStream, "application/pdf", $"{info[0].EQUIPMENT_NAME}-{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}.pdf");
            }
            catch (Exception e)
            {
                var result = new ResultDto<bool>();
                result.success = false;
                result.msg = e.Message;
                return Ok(result);
            }

        }

        /// <summary>
        /// 根据模板预览设备档案清单（设备列表）
        /// </summary>
        /// <param name="styleId"></param>
        /// <param name="areaId"></param>
        /// <param name="hospitalId"></param>
        /// <param name="state"></param>
        /// <param name="type"></param>
        /// <param name="mgroupId"></param>
        /// <param name="keyWord"></param>
        /// <param name="labId"></param>
        /// <param name="pgroupId"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        [HttpGet]
        public IActionResult ExportEquipmentsPreview(string styleId, string areaId, string hospitalId, string state, string type, string mgroupId, string keyWord, string labId, string pgroupId)
        {
            try
            {
                if (string.IsNullOrEmpty(styleId))
                {
                    throw new ArgumentNullException("请选择模板ID");
                }

                var user = User.ToClaimsDto();
                if (mgroupId != null)
                {
                    if (mgroupId.Contains("MG") == false)
                    {
                        pgroupId = mgroupId;
                        mgroupId = null;
                    }
                }
                var equipmentInfos = _equipmentDocService.GetEquipmentList(areaId, user.USER_NO, hospitalId, state, type, mgroupId, keyWord, labId, pgroupId);

                var ids = equipmentInfos.Select(x => x.EQUIPMENT_ID).ToList();
                var info = _equipmentOnlyOfficeService.FillEquipmentInfoDatas(styleId, ids);
                var dto = _equipmentOnlyOfficeService.FillEquipmentInfos(styleId, info);
                dto.DATAS.RemoveAll(x => (!x.ARRAYS.Any() && !x.FIELDS.Any()));

                var fileStream = _equipmentOnlyOfficeService.PreviewFile(dto);

                return File(fileStream, "application/pdf", $"设备清单-{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}.pdf");

            }
            catch (Exception e)
            {
                var result = new ResultDto<bool>();
                result.success = false;
                result.msg = e.Message;
                return Ok(result);
            }
        }

        /// <summary>
        /// 根据模板预览工作计划列表
        /// </summary>
        /// <param name="styleId"></param>
        /// <param name="labId"></param>
        /// <param name="keyword"></param>
        /// <param name="mgroupId"></param>
        /// <param name="equipmentClass"></param>
        /// <param name="pgroupId"></param>
        /// <param name="areaId"></param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult ExportEquipmentsWorkPlanPreview(string styleId, string labId, string keyword, string mgroupId, string equipmentClass, string pgroupId, string areaId)
        {
            try
            {
                if (string.IsNullOrEmpty(styleId))
                {
                    throw new ArgumentNullException("请选择模板ID");
                }

                var user = User.ToClaimsDto();
                if (mgroupId != null)
                {
                    if (mgroupId.Contains("MG") == false)
                    {
                        pgroupId = mgroupId;
                        mgroupId = null;
                    }
                }
                var workplans = _xhWorkPlanService.GetWorkPlans(user, keyword
                    , mgroupId, equipmentClass, labId, pgroupId, areaId);

                var ids = workplans.Select(x => x.EQUIPMENT_ID).ToList();
                var info = _equipmentOnlyOfficeService.FillEquipmentInfoDatas(styleId, ids);
                var dto = _equipmentOnlyOfficeService.FillEquipmentInfos(styleId, info);
                var fileStream = _equipmentOnlyOfficeService.PreviewFile(dto);

                return File(fileStream, "application/pdf", $"设备工作计划-{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}.pdf");
            }
            catch (Exception e)
            {
                var result = new ResultDto<bool>();
                result.success = false;
                result.msg = e.Message;
                return Ok(result);
            }

        }

        /// <summary>
        /// 根据模板导出设备基础信息word文件
        /// </summary>
        /// <param name="equipmentId"></param>
        /// <param name="styleId"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        [HttpGet]
        public IActionResult ExportEquipmentWord([BindRequired] string equipmentId, string styleId)
        {

            // styleId  D08C03E9FC154577B3B299918B23D0C1
            try
            {
                if (string.IsNullOrEmpty(equipmentId))
                {
                    throw new ArgumentNullException("请输入设备ID");
                }
                if (string.IsNullOrEmpty(styleId))
                {
                    throw new ArgumentNullException("请选择模板ID");
                }
                var ids = new List<string>() { $"{equipmentId}" };
                var info = _equipmentOnlyOfficeService.FillEquipmentInfoDatas(styleId, ids);
                var dto = _equipmentOnlyOfficeService.FillEquipmentInfo(styleId, info[0]);
                var fileStream = _equipmentOnlyOfficeService.ExportStyleFile(dto);
                return File(fileStream, "application/vnd.openxmlformats-officedocument.wordprocessingml.document", $"{info[0].EQUIPMENT_NAME}-{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}.docx");
            }
            catch (Exception e)
            {
                var result = new ResultDto<bool>();
                result.success = false;
                result.msg = e.Message;
                return Ok(result);
            }

        }


        /// <summary>
        /// 根据模板导出工作计划列表word文件
        /// </summary>
        /// <param name="styleId"></param>
        /// <param name="labId"></param>
        /// <param name="keyword"></param>
        /// <param name="mgroupId"></param>
        /// <param name="equipmentClass"></param>
        /// <param name="pgroupId"></param>
        /// <param name="areaId"></param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult ExportEquipmentsWorkPlanWord(string styleId, string labId, string keyword, string mgroupId, string equipmentClass, string pgroupId, string areaId)
        {
            try
            {
                if (string.IsNullOrEmpty(styleId))
                {
                    throw new ArgumentNullException("请选择模板ID");
                }

                var user = User.ToClaimsDto();
                if (mgroupId != null)
                {
                    if (mgroupId.Contains("MG") == false)
                    {
                        pgroupId = mgroupId;
                        mgroupId = null;
                    }
                }
                var workplans = _xhWorkPlanService.GetWorkPlans(user, keyword
                    , mgroupId, equipmentClass, labId, pgroupId, areaId);

                var ids = workplans.Select(x => x.EQUIPMENT_ID).ToList();
                var info = _equipmentOnlyOfficeService.FillEquipmentInfoDatas(styleId, ids);
                var dto = _equipmentOnlyOfficeService.FillEquipmentInfos(styleId, info);
                var fileStream = _equipmentOnlyOfficeService.ExportStyleFile(dto);
                return File(fileStream, "application/vnd.openxmlformats-officedocument.wordprocessingml.document", $"设备工作计划-{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}.docx");
            }
            catch (Exception e)
            {
                var result = new ResultDto<bool>();
                result.success = false;
                result.msg = e.Message;
                return Ok(result);
            }
        }
        /// <summary>
        /// 根据模板导出设备档案清单（设备列表）word文件
        /// </summary>
        /// <param name="styleId"></param>
        /// <param name="areaId"></param>
        /// <param name="hospitalId"></param>
        /// <param name="state"></param>
        /// <param name="type"></param>
        /// <param name="mgroupId"></param>
        /// <param name="keyWord"></param>
        /// <param name="labId"></param>
        /// <param name="pgroupId"></param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult ExportEquipmentsWord(string styleId, string areaId, string hospitalId, string state, string type, string mgroupId, string keyWord, string labId, string pgroupId)
        {
            try
            {
                if (string.IsNullOrEmpty(styleId))
                {
                    throw new ArgumentNullException("请选择模板ID");
                }

                var user = User.ToClaimsDto();
                if (mgroupId != null)
                {
                    if (mgroupId.Contains("MG") == false)
                    {
                        pgroupId = mgroupId;
                        mgroupId = null;
                    }
                }
                var equipmentInfos = _equipmentDocService.GetEquipmentList(areaId, user.USER_NO, hospitalId, state, type, mgroupId, keyWord, labId, pgroupId);
                var ids = equipmentInfos.Select(x => x.EQUIPMENT_ID).ToList();
                var info = _equipmentOnlyOfficeService.FillEquipmentInfoDatas(styleId, ids);
                var dto = _equipmentOnlyOfficeService.FillEquipmentInfos(styleId, info);
                var fileStream = _equipmentOnlyOfficeService.ExportStyleFile(dto);
                return File(fileStream, "application/vnd.openxmlformats-officedocument.wordprocessingml.document", $"设备清单-{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}.docx");

            }
            catch (Exception e)
            {
                var result = new ResultDto<bool>();
                result.success = false;
                result.msg = e.Message;
                return Ok(result);
            }
        }

        /// <summary>
        /// 获取编辑文档的iframe路径
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetTemplateSettingsUrl()
        {
            return Ok(_configuration["H115"].ToResultDto());
        }


        /// <summary>
        /// 根据模板导出设备列表excel数据
        /// </summary>
        /// <param name="styleId"></param>
        /// <param name="areaId"></param>
        /// <param name="hospitalId"></param>
        /// <param name="state"></param>
        /// <param name="type"></param>
        /// <param name="mgroupId"></param>
        /// <param name="keyWord"></param>
        /// <param name="labId"></param>
        /// <param name="pgroupId"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        [HttpGet]
        public IActionResult ExportEquipmentsExcelData(string styleId, string areaId, string hospitalId, string state, string type, string mgroupId, string keyWord, string labId, string pgroupId, string? isHidd)
        {

            try
            {
                if (string.IsNullOrEmpty(styleId))
                {
                    throw new ArgumentNullException("请选择模板ID");
                }
                var user = User.ToClaimsDto();
                if (mgroupId != null)
                {
                    if (mgroupId.Contains("MG") == false)
                    {
                        pgroupId = mgroupId;
                        mgroupId = null;
                    }
                }
                var equipmentInfos = _equipmentDocService.GetEquipmentList(areaId, user.USER_NO, hospitalId, state, type, mgroupId, keyWord, labId, pgroupId).WhereIF(isHidd.IsNotNullOrEmpty(), x => x.IS_HIDE == isHidd);

                var ids = equipmentInfos.Select(x => x.EQUIPMENT_ID).ToList();
                var infos = _equipmentOnlyOfficeService.FillEquipmentInfoDatas(styleId, ids,true);
                var dto = _equipmentOnlyOfficeService.FillExcelModelEquipments(styleId, infos);
                var result = _equipmentOnlyOfficeService.LoadExcelData(dto);
                return Ok(result);

            }
            catch (Exception e)
            {
                var result = new ResultDto<bool>();
                result.success = false;
                result.msg = e.Message;
                return Ok(result);
            }
        }
        /// <summary>
        /// 根据模板导出工作计划列表excel数据
        /// </summary>
        /// <param name="styleId"></param>
        /// <param name="labId"></param>
        /// <param name="keyword"></param>
        /// <param name="mgroupId"></param>
        /// <param name="equipmentClass"></param>
        /// <param name="pgroupId"></param>
        /// <param name="areaId"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        [HttpGet]
        public IActionResult ExportEquipmentsWorkPlanExcelData(string styleId, string labId, string keyword, string mgroupId, string equipmentClass, string pgroupId, string areaId)
        {
            try
            {
                if (string.IsNullOrEmpty(styleId))
                {
                    throw new ArgumentNullException("请选择模板ID");
                }

                var user = User.ToClaimsDto();
                if (mgroupId != null)
                {
                    if (mgroupId.Contains("MG") == false)
                    {
                        pgroupId = mgroupId;
                        mgroupId = null;
                    }
                }
                var workplans = _xhWorkPlanService.GetWorkPlans(user, keyword
                    , mgroupId, equipmentClass, labId, pgroupId, areaId);
                var ids = workplans.Select(x => x.EQUIPMENT_ID).ToList();
                var infos = _equipmentOnlyOfficeService.FillEquipmentInfoDatas(styleId, ids,true);
                var dto = _equipmentOnlyOfficeService.FillExcelModelEquipments(styleId, infos);
                var result = _equipmentOnlyOfficeService.LoadExcelData(dto);
                return Ok(result);

            }
            catch (Exception e)
            {
                var result = new ResultDto<bool>();
                result.success = false;
                result.msg = e.Message;
                return Ok(result);
            }
        }



    }
}
