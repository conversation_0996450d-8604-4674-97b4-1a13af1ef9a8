﻿using Newtonsoft.Json;

namespace XH.H82.Models.Flow;

public class FlowInstanceNodeDto
{
    [JsonProperty("instanceNodeId")]
    public string InstanceNodeId { get; set; }

    [JsonProperty("flowInstanceId")]
    public string FlowInstanceId { get; set; }

    [JsonProperty("flowNodeId")]
    public string FlowNodeId { get; set; }

    [JsonProperty("flowNodeType")]
    public string FlowNodeType { get; set; }

    [JsonProperty("flowNodeName")]
    public string FlowNodeName { get; set; }

    [JsonProperty("nodePrincipal")]
    public string NodePrincipal { get; set; }

    [JsonProperty("nodePrincipalName")]
    public string NodePrincipalName { get; set; }

    [JsonProperty("instanceState")]
    public string InstanceState { get; set; }

    [JsonProperty("rejectInfo")]
    public string RejectInfo { get; set; }

    [Json<PERSON>roperty("nodeRemark")]
    public string NodeRemark { get; set; }

    [JsonProperty("nodeStartTime")]
    public string NodeStartTime { get; set; }

    [Json<PERSON>roper<PERSON>("nodeProcessTime")]
    public string NodeProcessTime { get; set; }

    [JsonProperty("nodeRealPerson")]
    public string NodeRealPerson { get; set; }

    [JsonProperty("nodeRealPersonName")]
    public string NodeRealPersonName { get; set; }

    [JsonProperty("firstRpersion")]
    public string FirstRpersion { get; set; }

    [JsonProperty("firstRtime")]
    public string FirstRtime { get; set; }

    [JsonProperty("lastMperson")]
    public string LastMperson { get; set; }

    [JsonProperty("lastMtime")]
    public string LastMtime { get; set; }

    [JsonProperty("remark")]
    public string Remark { get; set; }
}