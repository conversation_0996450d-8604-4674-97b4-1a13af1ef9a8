﻿"restore":{"projectUniqueName":"C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.IServices\\XH.H82.IServices.csproj","projectName":"XH.H82.IServices","projectPath":"C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.IServices\\XH.H82.IServices.csproj","outputPath":"C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.IServices\\obj\\","projectStyle":"PackageReference","fallbackFolders":["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"],"originalTargetFrameworks":["net6.0"],"sources":{"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\":{},"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net6.0":{"targetAlias":"net6.0","projectReferences":{"C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Base\\XH.H82.Base.csproj":{"projectPath":"C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Base\\XH.H82.Base.csproj"},"C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Models\\XH.H82.Models.csproj":{"projectPath":"C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Models\\XH.H82.Models.csproj"}}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"},"SdkAnalysisLevel":"9.0.300"}"frameworks":{"net6.0":{"targetAlias":"net6.0","dependencies":{"EPPlus":{"target":"Package","version":"[6.2.6, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"frameworkReferences":{"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}