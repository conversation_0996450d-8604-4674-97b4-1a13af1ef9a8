﻿using H.Utility.SqlSugarInfra;
using Microsoft.EntityFrameworkCore;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.Entities
{
    [DBOwner("XH_OA")]
    [Table("AER_ADVERSE_EVENT")]
    public class AER_ADVERSE_EVENT
    {
        /// <summary>
        /// 事件ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Column("EVENT_ID")]
        [Unicode(false)]
        public string EVENT_ID { get; set; }

        /// <summary>
        /// 医疗机构ID
        /// </summary>
        [Column("HOSPITAL_ID")]
        [Unicode(false)]
        public string HOSPITAL_ID { get; set; }

        /// <summary>
        /// 事件名称
        /// </summary>
        [Column("EVENT_NAME")]
        [Unicode(false)]
        public string EVENT_NAME { get; set; }

        /// <summary>
        /// 事件分类
        /// </summary>
        [Column("EVENT_CLASS")]
        [Unicode(false)]
        public string? EVENT_CLASS { get; set; }

        /// <summary>
        /// 关联主体
        /// </summary>
        [Column("EVENT_SUBJECT")]
        [Unicode(false)]
        public string? EVENT_SUBJECT { get; set; }

        /// <summary>
        /// 发生原因分类
        /// </summary>
        [Column("EVENT_CAUSE_CLASS")]
        [Unicode(false)]
        public string? EVENT_CAUSE_CLASS { get; set; }

        /// <summary>
        /// 责任人联系方式
        /// </summary>
        [Column("MOBILE_NO")]
        [Unicode(false)]
        public string? MOBILE_NO { get; set; }

        /// <summary>
        /// 责任实验室
        /// </summary>
        [Column("PGROUP_ID")]
        [Unicode(false)]
        public string? PGROUP_ID { get; set; }

        /// <summary>
        /// 责任科室
        /// </summary>
        [Column("LAB_ID")]
        [Unicode(false)]
        public string? LAB_ID { get; set; }

        /// <summary>
        /// 责任人
        /// </summary>
        [Column("RESPONSIBLE")]
        [Unicode(false)]
        public string? RESPONSIBLE { get; set; }

        /// <summary>
        /// 发生时间
        /// </summary>
        [Column("EVENT_TIME")]
        [Unicode(false)]
        public DateTime? EVENT_TIME { get; set; }

        /// <summary>
        /// 事件等级
        /// </summary>
        [Column("EVENT_LEVEL")]
        [Unicode(false)]
        public string? EVENT_LEVEL { get; set; }

        /// <summary>
        /// 发生地点
        /// </summary>
        [Column("EVENT_ADDR")]
        [Unicode(false)]
        public string? EVENT_ADDR { get; set; }

        /// <summary>
        /// 发生原因
        /// </summary>
        [Column("EVENT_CAUSE")]
        [Unicode(false)]
        public string? EVENT_CAUSE { get; set; }

        /// <summary>
        /// 事件描述
        /// </summary>
        [Column("EVENT_DESC")]
        [Unicode(false)]
        public string? EVENT_DESC { get; set; }

        /// <summary>
        /// 原因分析
        /// </summary>
        [Column("CAUSE_ANALYZE")]
        [Unicode(false)]
        public string? CAUSE_ANALYZE { get; set; }

        /// <summary>
        /// 处置情况
        /// </summary>
        [Column("DISPOSE_CONDITION")]
        [Unicode(false)]
        public string? DISPOSE_CONDITION { get; set; }

        /// <summary>
        /// 处置人
        /// </summary>
        [Column("DISPOSE_PERSON")]
        [Unicode(false)]
        public string? DISPOSE_PERSON { get; set; }

        /// <summary>
        /// 处置时间
        /// </summary>
        [Column("DISPOSE_TIME")]
        [Unicode(false)]
        public DateTime DISPOSE_TIME { get; set; }

        /// <summary>
        /// 改进方案
        /// </summary>
        [Column("IMPROVE_PROJECT")]
        [Unicode(false)]
        public string? IMPROVE_PROJECT { get; set; }

        /// <summary>
        /// 最后处置
        /// </summary>
        [Column("LAST_DISPOSE")]
        [Unicode(false)]
        public string? LAST_DISPOSE { get; set; }

        /// <summary>
        /// 下一处置人
        /// </summary>
        [Column("NEXT_DISPOSE_PERSON")]
        [Unicode(false)]
        public string? NEXT_DISPOSE_PERSON { get; set; }

        /// <summary>
        /// 归档文件
        /// </summary>
        [Column("ISSUED_FILE")]
        [Unicode(false)]
        public string? ISSUED_FILE { get; set; }

        /// <summary>
        /// 状态 0已登记 1已关闭 2保存
        /// </summary>
        [Column("EVENT_STATE")]
        [Unicode(false)]
        public string? EVENT_STATE { get; set; }

        /// <summary>
        /// 首次登记人
        /// </summary>
        [Column("FIRST_RPERSON")]
        [Unicode(false)]
        public string? FIRST_RPERSON { get; set; }

        /// <summary>
        /// 首次登记时间
        /// </summary>
        [Column("FIRST_RTIME")]
        [Unicode(false)]
        public DateTime FIRST_RTIME { get; set; }

        /// <summary>
        /// 最后修改人员
        /// </summary>
        [Column("LAST_MPERSON")]
        [Unicode(false)]
        public string? LAST_MPERSON { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        [Column("LAST_MTIME")]
        [Unicode(false)]
        public DateTime LAST_MTIME { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Column("REMARK")]
        [Unicode(false)]
        public string? REMARK { get; set; }

        /// <summary>
        /// 关联主体
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string? CLASS_MAP_SUBJECT { get; set; }

        /// <summary>
        /// 主体列表
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<AER_EVENT_SUBJECT>? AER_EVENT_SUBJECTs { get; set; }
    }
}
