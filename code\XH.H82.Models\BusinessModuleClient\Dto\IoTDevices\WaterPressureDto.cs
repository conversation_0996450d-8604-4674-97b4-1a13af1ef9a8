﻿using Newtonsoft.Json;

namespace XH.H82.Models.BusinessModuleClient.Dto.IoTDevices;

/// <summary>
/// 水压传感器数据模型
/// </summary>
public class WaterPressureDto
{
    /// <summary>
    /// Id
    /// </summary>
    [JsonProperty("id")]
    public long Id { get; set; }
    [JsonProperty("sn")]
    public string Sn { get; set; }
    [JsonProperty("labId")]
    public long? LabId { get; set; }
    [JsonProperty("roomId")]
    public long? RoomId { get; set; }
    [JsonProperty("checkpointId")]
    public long? CheckpointId { get; set; }
    [JsonProperty("value")]
    public string Value { get; set; }
    [JsonProperty("alarmRule")]
    public string AlarmRule { get; set; }
    [JsonProperty("alarm")]
    public long Alarm { get; set; }
    [JsonProperty("createTime")]
    public DateTime? CreateTime { get; set; }
    /// <summary>
    /// 在线状态
    /// </summary>
    public int SwitchStatus { get; set; } = 2;
}