﻿namespace XH.H82.Models.EquipmengtClassNew;

/// <summary>
/// 设备类型档案记录展示Dto
/// </summary>
public class ClassLinkArchivesDto
{
    /// <summary>
    /// 档案记录id
    /// </summary>
    public string EqpArchivesId { get; set; }

    /// <summary>
    /// 档案记录细分类名称
    /// </summary>
    public string  EqpArchivesName { get; set; }
    
    /// <summary>
    /// 档案记录父级id
    /// </summary>
    public string EqpArchivesPid{ get; set; }
    /// <summary>
    /// 档案记录父级名称
    /// </summary>
    public string EqpArchivesPName{ get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public string?  Remark { get; set; }

    /// <summary>
    /// 档案记录类型;0:固定 1:扩展 默认0
    /// </summary>
    public string EqpArchivesType{ get; set; }
    /// <summary>
    /// 是否是细分类
    /// </summary>
    public bool IsSubdivision { get; set; }
    
    
    
    public ClassLinkArchivesDto(EMS_EQP_ARCHIVES_DICT archivesDict)
    {
        EqpArchivesId = archivesDict.EqpArchivesId;
        EqpArchivesPid = archivesDict.EqpArchivesPid;
        Remark = archivesDict.Remark;
        EqpArchivesType = archivesDict.EqpArchivesType;

        //档案记录大类
        if (archivesDict.EqpArchivesPid == "0")
        {
            EqpArchivesName = "";
            EqpArchivesPName = archivesDict.EqpArchivesName;
            IsSubdivision = false;
        }
        else
        {
            //档案记录细分类
            EqpArchivesName = archivesDict.EqpArchivesName;
            EqpArchivesPName = archivesDict.EqpArchivesPName;
            IsSubdivision = true;
        }
        
    }
}