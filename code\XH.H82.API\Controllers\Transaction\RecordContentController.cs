﻿using System.ComponentModel.DataAnnotations;
using H.Utility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using XH.H82.API.Extensions;
using XH.H82.IServices.Transaction;
using XH.H82.Models.Dtos.Transaction;

namespace XH.H82.API.Controllers.Transaction
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize]
    public class RecordContentController : ControllerBase
    {

        IContentDictService _contentDictService;

        public RecordContentController(IContentDictService contentDictService) => _contentDictService = contentDictService;

        /// <summary>
        /// 查询字段内容
        /// </summary>
        /// <param name="classId"> XX记录 </param>
        /// <param name="content">字典内容</param>
        /// <returns></returns>
        [HttpGet]
        [CustomResponseType(typeof(List<ContentDictDto>))]
        public IActionResult GetContentDicts(string? classId, string? content)
        {
            var result = _contentDictService.GetContentDicts(classId, content);
            return Ok(result.ToResultDto());
        }

        /// <summary>
        /// 新增字典
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult AddContentDict(AddContentDictInput input)
        {
            _contentDictService.AddContentDict(input.ClassId, input.Content, input.Remark);
            return Ok(true.ToResultDto());
        }

        /// <summary>
        /// 修改字典内容
        /// </summary>
        /// <param name="Id"></param>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPut("{Id}")]
        public IActionResult UpdateContentDict([Required] string Id, [FromBody] UpdateContentDictInput input)
        {
            _contentDictService.UpdateContentDict(Id, input.Content, input.Remark);
            return Ok(true.ToResultDto());
        }

        /// <summary>
        /// 删除字典
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpDelete("{Id}")]
        public IActionResult DeleteContentDict([Required] string Id)
        {
            _contentDictService.DeleteContentDict(Id);
            return Ok(true.ToResultDto());
        }

    }
}
