/* 2025/6/26 新增设备自定义名称模板表(zzk) */
CREATE TABLE XH_OA.EMS_EQPNO_FORMAT_DICT(
    EQP_NO_ID VARCHAR2(50) NOT NULL,
    HOSPITAL_ID VARCHAR2(20) NOT NULL,
    EQP_NO_NAME VARCHAR2(200) NOT NULL,
    EQP_NO_LEVEL VARCHAR2(20) DEFAULT  0 NOT NULL,
    EQP_NO_CLASS VARCHAR2(500) DEFAULT  0 NOT NULL,
    EQP_DISPLAY_JSON CLOB,
    EQP_NO_APPLYS VARCHAR2(2000),
    EQP_NO_STATE VARCHAR2(10) DEFAULT  1 NOT NULL,
    FIRST_RPERSON VARCHAR2(50),
    FIRST_RTIME DATE,
    LAST_MPERSON VARCHAR2(50),
    LAST_MTIME DATE,
    REMARK VARCHAR2(200)
);
ALTER TABLE XH_OA.EMS_EQPNO_FORMAT_DICT ADD CONSTRAINT PK_OA_EMS_EQPNO_FORMAT_DICT PRIMARY KEY (EQP_NO_ID);

COMMENT ON TABLE XH_OA.EMS_EQPNO_FORMAT_DICT IS '设备自定义名称模板';
COMMENT ON COLUMN XH_OA.EMS_EQPNO_FORMAT_DICT.EQP_NO_ID IS '自定义名称模板id';
COMMENT ON COLUMN XH_OA.EMS_EQPNO_FORMAT_DICT.HOSPITAL_ID IS '医疗机构ID';
COMMENT ON COLUMN XH_OA.EMS_EQPNO_FORMAT_DICT.EQP_NO_NAME IS '设备自定义名称模板名;模板名称';
COMMENT ON COLUMN XH_OA.EMS_EQPNO_FORMAT_DICT.EQP_NO_LEVEL IS '模板优先级;模板优先级、数值越大、优先级越高';
COMMENT ON COLUMN XH_OA.EMS_EQPNO_FORMAT_DICT.EQP_NO_CLASS IS '应用设备类型;0 全部 设备类型';
COMMENT ON COLUMN XH_OA.EMS_EQPNO_FORMAT_DICT.EQP_DISPLAY_JSON IS '设备代号配置';
COMMENT ON COLUMN XH_OA.EMS_EQPNO_FORMAT_DICT.EQP_NO_APPLYS IS '应用范围';
COMMENT ON COLUMN XH_OA.EMS_EQPNO_FORMAT_DICT.EQP_NO_STATE IS '数据状态;0 禁用   1在用   2删除';
COMMENT ON COLUMN XH_OA.EMS_EQPNO_FORMAT_DICT.FIRST_RPERSON IS '首次登记人';
COMMENT ON COLUMN XH_OA.EMS_EQPNO_FORMAT_DICT.FIRST_RTIME IS '首次登记时间';
COMMENT ON COLUMN XH_OA.EMS_EQPNO_FORMAT_DICT.LAST_MPERSON IS '最后修改人员';
COMMENT ON COLUMN XH_OA.EMS_EQPNO_FORMAT_DICT.LAST_MTIME IS '最后修改时间';
COMMENT ON COLUMN XH_OA.EMS_EQPNO_FORMAT_DICT.REMARK IS '备注';
GRANT SELECT, INSERT, UPDATE, DELETE ON XH_OA.EMS_EQPNO_FORMAT_DICT TO XH_COM;

/* 2025/6/26 新增设备类型表(zzk) */
CREATE TABLE XH_OA.EMS_EQUIPMENT_CLASS_DICT(
    CLASS_ID VARCHAR2(50) NOT NULL,
    HOSPITAL_ID VARCHAR2(20) NOT NULL,
    PARENT_CLASS_ID VARCHAR2(50),
    CLASS_NAME VARCHAR2(200) NOT NULL,
    CLASS_SNAME VARCHAR2(50),
    CLASS_TAG VARCHAR2(50) DEFAULT  0,
    CLASS_STYLE VARCHAR2(2000),
    CLASS_LEVEL VARCHAR2(50) DEFAULT  1 NOT NULL,
    CLASS_STATE VARCHAR2(50) DEFAULT  1 NOT NULL,
    REMARK VARCHAR2(200),
    LAST_MTIME DATE,
    LAST_MPERSON VARCHAR2(50),
    FIRST_RTIME DATE,
    FIRST_RPERSON VARCHAR2(50)
);
ALTER TABLE XH_OA.EMS_EQUIPMENT_CLASS_DICT ADD CONSTRAINT PK_OA_EMS_EQUIPMENT_CLASS_DICT PRIMARY KEY (CLASS_ID);

COMMENT ON TABLE XH_OA.EMS_EQUIPMENT_CLASS_DICT IS '设备分类表';
COMMENT ON COLUMN XH_OA.EMS_EQUIPMENT_CLASS_DICT.CLASS_ID IS '设备分类id';
COMMENT ON COLUMN XH_OA.EMS_EQUIPMENT_CLASS_DICT.HOSPITAL_ID IS '医疗机构ID';
COMMENT ON COLUMN XH_OA.EMS_EQUIPMENT_CLASS_DICT.PARENT_CLASS_ID IS '父级设备分类id;父级节点为固定大类的固定id';
COMMENT ON COLUMN XH_OA.EMS_EQUIPMENT_CLASS_DICT.CLASS_NAME IS '设备分类名称';
COMMENT ON COLUMN XH_OA.EMS_EQUIPMENT_CLASS_DICT.CLASS_SNAME IS '设备分类简称';
COMMENT ON COLUMN XH_OA.EMS_EQUIPMENT_CLASS_DICT.CLASS_TAG IS '设备分类标签;0 iso 1生物安全  2POCT 3高等级';
COMMENT ON COLUMN XH_OA.EMS_EQUIPMENT_CLASS_DICT.CLASS_STYLE IS '设备分样式;用于存储图案颜色';
COMMENT ON COLUMN XH_OA.EMS_EQUIPMENT_CLASS_DICT.CLASS_LEVEL IS '设备分优先级;根节点为0 逐级递增1  结合自定义代号作模板选择权重';
COMMENT ON COLUMN XH_OA.EMS_EQUIPMENT_CLASS_DICT.CLASS_STATE IS '设备分类状态;0 禁用   1在用  2删除';
COMMENT ON COLUMN XH_OA.EMS_EQUIPMENT_CLASS_DICT.REMARK IS '备注';
COMMENT ON COLUMN XH_OA.EMS_EQUIPMENT_CLASS_DICT.LAST_MTIME IS '最后修改时间';
COMMENT ON COLUMN XH_OA.EMS_EQUIPMENT_CLASS_DICT.LAST_MPERSON IS '最后修改人员';
COMMENT ON COLUMN XH_OA.EMS_EQUIPMENT_CLASS_DICT.FIRST_RTIME IS '首次登记时间';
COMMENT ON COLUMN XH_OA.EMS_EQUIPMENT_CLASS_DICT.FIRST_RPERSON IS '首次登记人';
GRANT SELECT, INSERT, UPDATE, DELETE ON XH_OA.EMS_EQUIPMENT_CLASS_DICT TO XH_COM;



/* 2025/6/26 新增设备档案记录字典表(zzk) */
CREATE TABLE XH_OA.EMS_EQP_ARCHIVES_DICT(
    EQP_ARCHIVES_ID VARCHAR2(50) NOT NULL,
    HOSPITAL_ID VARCHAR2(20) NOT NULL,
    EQP_ARCHIVES_PID VARCHAR2(50),
    EQP_ARCHIVES_TYPE VARCHAR2(10) DEFAULT  0 NOT NULL,
    EQP_ARCHIVES_NAME VARCHAR2(200) NOT NULL,
    EQP_ARCHIVES_SORT NUMBER,
    EQP_ARCHIVES_STATE VARCHAR2(10) DEFAULT  1 NOT NULL,
    FORM_SETUP_ID VARCHAR2(50),
    TABLE_SETUP_ID VARCHAR2(50),
    EQP_ARCHIVES_JSON CLOB,
    FIRST_RPERSON VARCHAR2(50),
    FIRST_RTIME DATE,
    LAST_MPERSON VARCHAR2(50),
    LAST_MTIME DATE,
    REMARK VARCHAR2(200)
);
ALTER TABLE XH_OA.EMS_EQP_ARCHIVES_DICT ADD CONSTRAINT PK_OA_EMS_EQP_ARCHIVES_DICT PRIMARY KEY (EQP_ARCHIVES_ID);

COMMENT ON TABLE XH_OA.EMS_EQP_ARCHIVES_DICT IS '设备档案记录字典表';
COMMENT ON COLUMN XH_OA.EMS_EQP_ARCHIVES_DICT.EQP_ARCHIVES_ID IS '档案记录ID';
COMMENT ON COLUMN XH_OA.EMS_EQP_ARCHIVES_DICT.HOSPITAL_ID IS '医疗机构ID';
COMMENT ON COLUMN XH_OA.EMS_EQP_ARCHIVES_DICT.EQP_ARCHIVES_PID IS '档案记录父级ID;细分类的父级id 父级id可为空';
COMMENT ON COLUMN XH_OA.EMS_EQP_ARCHIVES_DICT.EQP_ARCHIVES_TYPE IS '档案记录类型;0:固定 1:扩展 默认0';
COMMENT ON COLUMN XH_OA.EMS_EQP_ARCHIVES_DICT.EQP_ARCHIVES_NAME IS '档案名称';
COMMENT ON COLUMN XH_OA.EMS_EQP_ARCHIVES_DICT.EQP_ARCHIVES_SORT IS '档案顺序';
COMMENT ON COLUMN XH_OA.EMS_EQP_ARCHIVES_DICT.EQP_ARCHIVES_STATE IS '档案记录状态;0 禁用  1在用  2删除';
COMMENT ON COLUMN XH_OA.EMS_EQP_ARCHIVES_DICT.FORM_SETUP_ID IS '表单配置记录id';
COMMENT ON COLUMN XH_OA.EMS_EQP_ARCHIVES_DICT.TABLE_SETUP_ID IS '表格配置记录id';
COMMENT ON COLUMN XH_OA.EMS_EQP_ARCHIVES_DICT.EQP_ARCHIVES_JSON IS '用于存储不同档案记录的拓展功能：如是否上传附件';
COMMENT ON COLUMN XH_OA.EMS_EQP_ARCHIVES_DICT.FIRST_RPERSON IS '首次登记人';
COMMENT ON COLUMN XH_OA.EMS_EQP_ARCHIVES_DICT.FIRST_RTIME IS '首次登记时间';
COMMENT ON COLUMN XH_OA.EMS_EQP_ARCHIVES_DICT.LAST_MPERSON IS '最后修改人员';
COMMENT ON COLUMN XH_OA.EMS_EQP_ARCHIVES_DICT.LAST_MTIME IS '最后修改时间';
COMMENT ON COLUMN XH_OA.EMS_EQP_ARCHIVES_DICT.REMARK IS '备注';
GRANT SELECT, INSERT, UPDATE, DELETE ON XH_OA.EMS_EQP_ARCHIVES_DICT TO XH_COM;

/* 2025/6/26 新增设备类型档案记录关联表（zzk） */
CREATE TABLE XH_OA.EMS_EQP_CLASS_ARCHIVES(
    CLASS_ARCHIVES_ID VARCHAR2(50) NOT NULL,
    HOSPITAL_ID VARCHAR2(20) NOT NULL,
    EQP_CLASS_ID VARCHAR2(50) NOT NULL,
    EQP_ARCHIVES_ID VARCHAR2(50) NOT NULL,
    REMARK VARCHAR2(200),
    LAST_MTIME DATE,
    LAST_MPERSON VARCHAR2(50),
    FIRST_RTIME DATE,
    FIRST_RPERSON VARCHAR2(50)
);
ALTER TABLE XH_OA.EMS_EQP_CLASS_ARCHIVES ADD CONSTRAINT PK_OA_EMS_EQP_CLASS_ARCHIVES PRIMARY KEY (CLASS_ARCHIVES_ID);

COMMENT ON TABLE XH_OA.EMS_EQP_CLASS_ARCHIVES IS '设备类型档案记录关联表';
COMMENT ON COLUMN XH_OA.EMS_EQP_CLASS_ARCHIVES.CLASS_ARCHIVES_ID IS '关联id主键';
COMMENT ON COLUMN XH_OA.EMS_EQP_CLASS_ARCHIVES.HOSPITAL_ID IS '医疗机构ID';
COMMENT ON COLUMN XH_OA.EMS_EQP_CLASS_ARCHIVES.EQP_CLASS_ID IS '设备类型id';
COMMENT ON COLUMN XH_OA.EMS_EQP_CLASS_ARCHIVES.EQP_ARCHIVES_ID IS '档案记录字典id';
COMMENT ON COLUMN XH_OA.EMS_EQP_CLASS_ARCHIVES.REMARK IS '备注';
COMMENT ON COLUMN XH_OA.EMS_EQP_CLASS_ARCHIVES.LAST_MTIME IS '最后修改时间';
COMMENT ON COLUMN XH_OA.EMS_EQP_CLASS_ARCHIVES.LAST_MPERSON IS '最后修改人员';
COMMENT ON COLUMN XH_OA.EMS_EQP_CLASS_ARCHIVES.FIRST_RTIME IS '首次登记时间';
COMMENT ON COLUMN XH_OA.EMS_EQP_CLASS_ARCHIVES.FIRST_RPERSON IS '首次登记人';
GRANT SELECT, INSERT, UPDATE, DELETE ON XH_OA.EMS_EQP_CLASS_ARCHIVES TO XH_COM;

/* 2025/6/26 新增设备设置字典表(zzk) */
CREATE TABLE XH_OA.EMS_EQP_SETUP_DICT(
    EQP_SETUP_ID VARCHAR2(50) NOT NULL,
    HOSPITAL_ID VARCHAR2(20) NOT NULL,
    EQP_SETUP_CNAME VARCHAR2(50),
    EQP_SETUP_TYPE VARCHAR2(10),
    EQP_SETUP_JSON CLOB,
    EQP_SETUP_NSORT NUMBER,
    EQP_SETUP_NSTATE VARCHAR2(20) DEFAULT  1,
    FIRST_RPERSON VARCHAR2(50),
    FIRST_RTIME DATE,
    LAST_MPERSON VARCHAR2(50),
    LAST_MTIME DATE,
    REMARK VARCHAR2(200)
);
ALTER TABLE XH_OA.EMS_EQP_SETUP_DICT ADD CONSTRAINT PK_OA_EMS_EQP_SETUP_DICT PRIMARY KEY (EQP_SETUP_ID);

COMMENT ON TABLE XH_OA.EMS_EQP_SETUP_DICT IS '设备设置字典表';
COMMENT ON COLUMN XH_OA.EMS_EQP_SETUP_DICT.EQP_SETUP_ID IS '设置ID';
COMMENT ON COLUMN XH_OA.EMS_EQP_SETUP_DICT.HOSPITAL_ID IS '医疗机构ID';
COMMENT ON COLUMN XH_OA.EMS_EQP_SETUP_DICT.EQP_SETUP_CNAME IS '自定义名称';
COMMENT ON COLUMN XH_OA.EMS_EQP_SETUP_DICT.EQP_SETUP_TYPE IS '设置类型';
COMMENT ON COLUMN XH_OA.EMS_EQP_SETUP_DICT.EQP_SETUP_JSON IS '设置属性;扩展属性';
COMMENT ON COLUMN XH_OA.EMS_EQP_SETUP_DICT.EQP_SETUP_NSORT IS '排序号';
COMMENT ON COLUMN XH_OA.EMS_EQP_SETUP_DICT.EQP_SETUP_NSTATE IS '状态;0禁用  1在用  2删除';
COMMENT ON COLUMN XH_OA.EMS_EQP_SETUP_DICT.FIRST_RPERSON IS '首次登记人';
COMMENT ON COLUMN XH_OA.EMS_EQP_SETUP_DICT.FIRST_RTIME IS '首次登记时间';
COMMENT ON COLUMN XH_OA.EMS_EQP_SETUP_DICT.LAST_MPERSON IS '最后修改人员';
COMMENT ON COLUMN XH_OA.EMS_EQP_SETUP_DICT.LAST_MTIME IS '最后修改时间';
COMMENT ON COLUMN XH_OA.EMS_EQP_SETUP_DICT.REMARK IS '备注';
GRANT SELECT, INSERT, UPDATE, DELETE ON XH_OA.EMS_EQP_SETUP_DICT TO XH_COM;

/* 2025/6/26 新增设备附加记录表(zzk) */
CREATE TABLE XH_OA.EMS_EQP_ADDN_RECORD(
    EQP_RECORD_ID VARCHAR2(50) NOT NULL,
    HOSPITAL_ID VARCHAR2(20) NOT NULL,
    EQUIPMENT_ID VARCHAR2(50) NOT NULL,
    EQP_ARCHIVES_ID VARCHAR2(50),
    EQUITMENT_JSON CLOB,
    EQP_RECORD_AFFIX VARCHAR2(500),
    EQP_RECORD_SORT VARCHAR2(10),
    EQP_RECORD_STATE VARCHAR2(10) DEFAULT  1,
    FIRST_RPERSON VARCHAR2(50),
    FIRST_RTIME DATE,
    LAST_MPERSON VARCHAR2(50),
    LAST_MTIME DATE,
    REMARK VARCHAR2(200)
);
ALTER TABLE XH_OA.EMS_EQP_ADDN_RECORD ADD CONSTRAINT PK_OA_EMS_EQP_ADDN_RECORD PRIMARY KEY (EQP_RECORD_ID);

COMMENT ON TABLE XH_OA.EMS_EQP_ADDN_RECORD IS '设备附加记录表';
COMMENT ON COLUMN XH_OA.EMS_EQP_ADDN_RECORD.EQP_RECORD_ID IS '记录ID';
COMMENT ON COLUMN XH_OA.EMS_EQP_ADDN_RECORD.HOSPITAL_ID IS '医疗机构ID';
COMMENT ON COLUMN XH_OA.EMS_EQP_ADDN_RECORD.EQUIPMENT_ID IS '设备ID';
COMMENT ON COLUMN XH_OA.EMS_EQP_ADDN_RECORD.EQP_ARCHIVES_ID IS '档案记录字典ID';
COMMENT ON COLUMN XH_OA.EMS_EQP_ADDN_RECORD.EQUITMENT_JSON IS '附加信息';
COMMENT ON COLUMN XH_OA.EMS_EQP_ADDN_RECORD.EQP_RECORD_AFFIX IS '附件';
COMMENT ON COLUMN XH_OA.EMS_EQP_ADDN_RECORD.EQP_RECORD_SORT IS '排序号';
COMMENT ON COLUMN XH_OA.EMS_EQP_ADDN_RECORD.EQP_RECORD_STATE IS '状态;0禁用 1在用 2删除';
COMMENT ON COLUMN XH_OA.EMS_EQP_ADDN_RECORD.FIRST_RPERSON IS '首次登记人';
COMMENT ON COLUMN XH_OA.EMS_EQP_ADDN_RECORD.FIRST_RTIME IS '首次登记时间';
COMMENT ON COLUMN XH_OA.EMS_EQP_ADDN_RECORD.LAST_MPERSON IS '最后修改人员';
COMMENT ON COLUMN XH_OA.EMS_EQP_ADDN_RECORD.LAST_MTIME IS '最后修改时间';
COMMENT ON COLUMN XH_OA.EMS_EQP_ADDN_RECORD.REMARK IS '备注';
GRANT SELECT, INSERT, UPDATE, DELETE ON XH_OA.EMS_EQP_ADDN_RECORD TO XH_COM;