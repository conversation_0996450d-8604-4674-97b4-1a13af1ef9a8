server {
      #端口号
      listen 18082 ssl;
      # gzip config
      gzip on;
      gzip_min_length 1k;
      gzip_comp_level 9;
      gzip_types text/plain text/css text/javascript application/json application/javascript application/x-javascript application/xml;
      gzip_vary on;
      gzip_disable "MSIE [1-6]\.";
      client_max_body_size 1024m;


      location / {
            #网站文件地址
            root  /xh22sc/H82/web;
            index  index.html index.htm;
            try_files  $uri $uri/ /index.html;
      }
        location /api {
        #后台API地址
        proxy_pass https://localhost:18482;
        }
	       
        location /H82pdf/ {
			   proxy_pass https://localhost:18482; 
         }
  }
