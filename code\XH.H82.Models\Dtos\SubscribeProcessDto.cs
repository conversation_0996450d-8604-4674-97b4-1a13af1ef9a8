﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.Dtos
{
    public class SubscribeProcessDto
    {
        public string SUBSCRIBE_PERSON { get; set; }
        public string SUBSCRIBE_DATE { get; set; }
        public string IF_APPROVAL { get; set; }
        public string APPROVAL_PERSON { get; set; }
        public string APPROVAL_DATE { get; set; }
        public List<CONTACT_SIGNING> CONTACT_SIGNING { get; set; }
        public List<INSTRUMENT_INSTALL> INSTRUMENT_INSTALL { get; set; }
    }
    
    public class CONTACT_SIGNING
    {
        public string CONTACT_NAME { get; set; }
        public string CONTACT_NO { get; set; }
        public string CONTACT_DATE { get; set; }
    }
    public class INSTRUMENT_INSTALL
    {
        public string INSTRUMENT_NAME { get; set; }
        public string INSTALL_DATE { get; set; }
    }
}
