﻿namespace XH.H82.API.Controllers.IoTDevice;

/// <summary>
/// 设备运行情况单个设备分类模型
/// </summary>
public class CountTheTotalUseEquipmentCategoriesDto
{

    /// <summary>
    /// 备案实验室Id
    /// </summary>
    public string SmblLabId { get; set; }
    /// <summary>
    /// 备案实验室名称
    /// </summary>
    public string SmblLabName { get; set; }
    /// <summary>
    /// 生安设备类型
    /// </summary>
    public string SmblClass { get; set; }
    /// <summary>
    /// 设备名称
    /// </summary>
    public string EquipmentName { get; set; }
    
    /// <summary>
    /// 当日使用率
    /// </summary>
    public double DayUsageRate { get; set; }
    /// <summary>
    /// 当日使用小时
    /// </summary>
    public double CurrentUseDayHours { get; set; }

    /// <summary>
    /// 当月平均每天使用小时
    /// </summary>
    public double CurrentAvgUseMoonHours { get; set; }
    /// <summary>
    /// 当月使用天数
    /// </summary>
    public  int CurrentAvgUseMoonUseDay { get; set; }
    /// <summary>
    /// 是否接入
    /// </summary>
    public bool IsAccess { get; set; } = false;
}