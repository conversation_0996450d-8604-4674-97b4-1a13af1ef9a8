﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Text;

namespace H.Utility.Helper
{
    public static class Base64Helper
    {
        public static Bitmap Base64StrToImg(string base64Str)
        {
            base64Str = base64Str.Replace("data:image/png;base64,", "").Replace("data:image/jgp;base64,", "")
                .Replace("data:image/jpg;base64,", "").Replace("data:image/jpeg;base64,", ""); //将base64头部信息替换


            byte[] arr2 = Convert.FromBase64String(base64Str);
            using (MemoryStream ms = new MemoryStream(arr2))
            {
                System.Drawing.Bitmap bmp = new System.Drawing.Bitmap(ms);
                //复制一份防止出现GDI+一般性错误
                Bitmap bmpNew = new Bitmap(bmp);
                bmp.Dispose();
                return bmpNew;
            }
        }

        /// <summary>
        /// 文件转base64
        /// </summary>
        /// <returns>base64字符串</returns>
        public static string FileToBase64String(string FilePath)
        {
            FileStream fsForRead = new FileStream(FilePath, FileMode.Open);
            string base64Str = "";
            try
            {
                //读写指针移到距开头10个字节处
                fsForRead.Seek(0, SeekOrigin.Begin);
                byte[] bs = new byte[fsForRead.Length];
                int log = Convert.ToInt32(fsForRead.Length);
                //从文件中读取10个字节放到数组bs中
                fsForRead.Read(bs, 0, log);
                base64Str = Convert.ToBase64String(bs);
                return base64Str;
            }
            catch (Exception ex)
            {
                Console.Write(ex.Message);
                Console.ReadLine();
                return base64Str;
            }
            finally
            {
                fsForRead.Close();
            }
        }

        public static Stream Base64ToBytes(string base64String)
        {
            try
            {
                // 将 Base64 字符串转换为字节数组
                byte[] byteArray = Convert.FromBase64String(base64String);
                // 创建内存流并返回
                return new MemoryStream(byteArray);
            }
            catch (FormatException ex)
            {
                throw new ArgumentException("Base64 字符串格式错误："+ex.Message ); // 重新抛出异常，让调用者处理
            }
        }
    }
}
