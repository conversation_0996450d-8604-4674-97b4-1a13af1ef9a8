﻿using Newtonsoft.Json;

namespace XH.H82.Models.Flow;

public class FlowDto
{
    [JsonProperty("flowClassId")]
    public string FlowClassId { get; set; }
    [JsonProperty("flowDesc")]
    public string FlowDesc { get; set; }
    [JsonProperty("iconOption")]
    public string IconOption{ get; set; }
    [JsonProperty("flowId")]
    public string FlowId{ get; set; }
    [JsonProperty("flowCode")]
    public string FlowCode{ get; set; }
    [JsonProperty("flowName")]
    public string FlowName{ get; set; }
    [JsonProperty("flowVer")]
    public string FlowVer{ get; set; }
    [JsonProperty("flowState")]
    public string FlowState{ get; set; }
    [JsonProperty("flowJson")]
    public FlowJsonDto FlowJson{ get; set; }
    [JsonProperty("needChoseApprover")]
    public bool NeedChoseApprover{ get; set; }
    [JsonProperty("formJson")]
    public string FormJson{ get; set; }
    [JsonProperty("onUsed")]
    public bool? OnUsed{ get; set; }
    [JsonProperty("hospitalId")]
    public string HospitalId{ get; set; }
    [JsonProperty("ifPublish")]
    public bool? IfPublish{ get; set; }
    [JsonProperty("firstRpersion")]
    public string FirstRpersion{ get; set; }
    [JsonProperty("firstRtime")]
    public string FirstRtime{ get; set; }
    [JsonProperty("lastMperson")]
    public string LastMperson{ get; set; }
    [JsonProperty("lastMtime")]
    public string LastMtime{ get; set; }
    [JsonProperty("remark")]
    public string Remark{ get; set; }
}