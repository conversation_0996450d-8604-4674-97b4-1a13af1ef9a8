﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using H.Utility.SqlSugarInfra;
using SqlSugar;

namespace XH.H82.Models.Entities
{
    [DBOwner("XH_SYS")]
    public class LIS5_INSTRUMENT_ITEM
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string CHANNEL_ID { get; set; }
        public string UNIT_ID { get; set; }
        public string INSTRUMENT_ID { get; set; }
        public string SINSTRU_SERIALID { get; set; }
        public string ITEM_ID { get; set; }
        public string INSTRUMENT_SNUM { get; set; }
        public string ITEM_NAME { get; set; }
        public string ITEM_CODE { get; set; }
        public string ITEM_SORT { get; set; }
        public string CHANNEL { get; set; }
        public string SAMPLE_CLASS { get; set; }
        public string RATIO { get; set; }
        public string ITEM_PRECISION { get; set; }
        public string DILUTION_RATE { get; set; }
        public string TAG_SUFFIX { get; set; }
        public string CHANNEL_TYPE { get; set; }
        public string FIRST_RPERSON { get; set; }
        public string FIRST_RTIME { get; set; }
        public string LAST_MPERSON { get; set; }
        public string LAST_MTIME { get; set; }
        public string REMARK { get; set; }
        public string SAMPLE_CLASS_NAME { get; set; }
        public string ITEM_ONLINE { get; set; }
        public string SINSTRUMENT_ID { get; set; }
        public string SERIAL_NO { get; set; }
        public string TEST_DURATION { get; set; }
        public double TEST_TIMELIMIT { get; set; }
        public string TEST_METHOD { get; set; }

    }
}
