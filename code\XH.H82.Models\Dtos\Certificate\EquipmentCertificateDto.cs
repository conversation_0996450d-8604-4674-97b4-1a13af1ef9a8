using AutoMapper;
using AutoMapper.Configuration.Annotations;
using H.Utility;
using Microsoft.AspNetCore.Mvc;
using NPOI.SS.Formula.Functions;
using XH.H82.Models.BusinessModuleClient;
using XH.H82.Models.DeviceRelevantInformation;
using XH.H82.Models.DeviceRelevantInformation.Enum;
using XH.H82.Models.Entities.Certificate;

namespace XH.H82.Models.Dtos.Certificate;

/// <summary>
/// 证书Dto
/// </summary>
public class EquipmentCertificateDto
{
    /// <summary>
    /// 证书Id
    /// </summary>
    public string Id { get; set; }
    
    /// <summary>
    /// 证书类型Id
    /// </summary>
    public string CertificateType { get; set; }
    
    /// <summary>
    /// 证书类型名称
    /// </summary>
    public string  CertificateTypeName { get; set; }
    /// <summary>
    /// 证书现在的状态（长期、废用、临近过期、停用）
    /// </summary>
    public string CertificateStatus { get; set; }
    /// <summary>
    /// 过期时间
    /// </summary>
    public DateTime? ExpiryDate { get; set; }
    /// <summary>
    /// 发证日期
    /// </summary>
    public DateTime? CerDate { get; set; }
    /// <summary>
    /// 提醒时间
    /// </summary>
    public DateTime? CerWarnDate { get; set; }
    /// <summary>
    /// 对象状态的到截至日期的天数，如果是长期的证书则为 0 
    /// </summary>
    public int Days { get; set; } 
    /// <summary>
    /// 附件列表
    /// </summary>
    public List<AttachmentDto> Attachments { get; set; }
    
    /// <summary>
    /// 静态工厂创建方法
    /// </summary>
    /// <returns></returns>
    public static EquipmentCertificateDto Create(
        string id, 
        string certificateType,
        string certificateTypeName,
        DateTime? expiryDate,
        DateTime? cerDate,
        DateTime? cerWarnDate,
        bool isVoided
        )
    {
        var now = DateTime.Now;
        var equipmentCertificateDto = new EquipmentCertificateDto()
        {
            Id = id,
            CertificateType = certificateType,
            CertificateTypeName = certificateTypeName,
            ExpiryDate = expiryDate,
            CerDate = cerDate,
            CerWarnDate = cerWarnDate,
            Attachments = new ()
        };
        
        if (!expiryDate.HasValue)
        {
            equipmentCertificateDto.CertificateStatus = "正常";
            equipmentCertificateDto.Days = 0;
        }
        else
        {
            if (now.Date>expiryDate.Value.Date)
            {
                equipmentCertificateDto.CertificateStatus = "超期";
                equipmentCertificateDto.Days = Math.Abs((now - expiryDate.Value).Days);
            }
            else
            {
                if (cerWarnDate.HasValue)
                {
                    if (now>cerWarnDate)
                    {
                        equipmentCertificateDto.CertificateStatus = "临近过期";
                        equipmentCertificateDto.Days = Math.Abs((now - expiryDate.Value).Days);
                    }
                    else
                    {
                        equipmentCertificateDto.CertificateStatus = "正常";
                        equipmentCertificateDto.Days = Math.Abs((now - expiryDate.Value).Days);
                    }
                }
                else
                {
                    equipmentCertificateDto.CertificateStatus = "正常";
                    equipmentCertificateDto.Days = Math.Abs((now - expiryDate.Value).Days);
                }
            }
        }

        if (isVoided)
        {
            equipmentCertificateDto.CertificateStatus = "作废";
            equipmentCertificateDto.Days = 0;
        }
        return equipmentCertificateDto;
    }


}