﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.Entities
{
    /// <summary>
    /// 记录单归档信息表
    /// </summary>
    [DBOwner("XH_OA")]
    public class TIM_WORK_FORM_ISSUE
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string ISSUE_ID { get; set; }
        public string HOSPITAL_ID { get; set; }
        public string LAB_ID { get; set; }
        public string PGROUP_ID { get; set; }
        public string UNIT_ID { get; set; }
        public string MODULE_ID { get; set; }
        public string FORM_ID { get; set; }
        public string FORM_VER_MAIN_ID { get; set; }
        public string ISSUE_NAME { get; set; }
        public string ISSUE_DATE { get; set; }
        public string ISSUE_NUM { get; set; }
        public string RECORD_NUM { get; set; }
        public string RECORDSYS_NUM { get; set; }
        public DateTime? ISSUED_TIME { get; set; }
        public string ISSUED_PERSON { get; set; }
        public string ISSUED_COMPUTER { get; set; }
        public string ISSUE_FILE { get; set; }
        public string ISSUE_STATE { get; set; }
        public string FIRST_RPERSON { get; set; }
        public DateTime? FIRST_RTIME { get; set; }
        public string LAST_MPERSON { get; set; }
        public DateTime? LAST_MTIME { get; set; }
        public string REMARK { get; set; }
        public int? ISSUE_MONTH { get; set; }
        public int? ISSUE_YEAR { get; set; }
        public int? RECORD_NUMS { get; set; }
    }
}
