﻿using System.Collections.Generic;

namespace XH.H82.Models.Dtos.FileTemplate
{
    /// <summary>
    /// 模板字段数据DTO
    /// </summary>
    public class StyleTemplateClassDataDto
    {
        /// <summary>
        /// 类型ID
        /// </summary>
        public string CLASSE_CODE { get; set; }
        /// <summary>
        /// 单字段数据
        /// </summary>
        public Dictionary<string, string> FIELDS { get; set; } = new Dictionary<string, string>();


        /// <summary>
        /// 列表数据ARRAYS不为空时必须传,作为判断表格填充第一个字段的条件,值为FIELD_CODE
        /// </summary>
        //  public string? START_FIELD { get; set; }

        /// <summary>
        /// 列表数据
        /// </summary>
        public List<Dictionary<string, string>> ARRAYS { get; set; } = new List<Dictionary<string, string>>();
    }

}
