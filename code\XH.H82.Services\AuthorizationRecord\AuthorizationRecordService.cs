using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using Microsoft.AspNetCore.Http;
using XH.H82.IServices.AuthorizationRecord;
using XH.H82.Models.BusinessModuleClient;
using XH.H82.Models.DeviceRelevantInformation;
using XH.H82.Models.Dtos.AuthorizationRecord;
using XH.H82.Models.Entities;
using XH.H82.Models.Entities.Common;
using XH.H82.Models.Entities.PMS;
using XH.H82.Models.Entities.Transaction;
using XH.H82.Models.SugarDbContext;
using XH.H82.Services.DeviceDataRefresh;
using XH.LAB.UTILS.Models;

namespace XH.H82.Services.AuthorizationRecord;

public class AuthorizationRecordService : IAuthorizationRecordService
{
    private readonly ISqlSugarUow<SugarDbContext_Master> _dbContext;
    private readonly IHttpContextAccessor _httpContext;
    private readonly ClaimsDto _user;
    private const string AuthorizationClassId = "授权权限";


    public AuthorizationRecordService(ISqlSugarUow<SugarDbContext_Master> dbContext, IHttpContextAccessor httpContext)
    {
        _dbContext = dbContext;
        _httpContext = httpContext;
        _user = _httpContext.HttpContext.User.ToClaimsDto();
        _dbContext.SetCreateTimeAndCreatePersonData(_user);
    }
    
    public OA_BASE_DATA AddAuthorizationRecordDict(string content , string? remark)
    {
        var exitsDict = _dbContext.Db.Queryable<OA_BASE_DATA>()
            .Where(x => x.FATHER_ID == RecordContentDict.RECORD_AUTHORIZATION_DICT)
            .Where(x => x.STATE_FLAG == "1")
            .Where(x => x.CLASS_ID == AuthorizationClassId)
            .Where(x => x.DATA_NAME == content)
            .First();
        if (exitsDict is not null)
        {
            return exitsDict;
        }
        
        var dict = RecordContentDict.CreateAuthorizationDict(_user.HOSPITAL_ID, _user.USER_NAME, AuthorizationClassId,
            content, remark);
        return  _dbContext.Db.Insertable(dict).ExecuteReturnEntity();
    }
    public void UpdateAuthorizationRecordDict(string id, string content, string? remark)
    {
        var dict = _dbContext.Db.Queryable<OA_BASE_DATA>()
            .Where(x=>x.CLASS_ID == AuthorizationClassId)
            .Where(x => x.DATA_ID == id && x.FATHER_ID == RecordContentDict.RECORD_AUTHORIZATION_DICT && x.STATE_FLAG == "1")
            .First();

        if (dict is not null)
        {
            dict.DATA_NAME = content;
            dict.REMARK = remark;
            _dbContext.Db.Updateable(dict).ExecuteCommand();
        }
    }
    public void DeleteAuthorizationRecordDict(string id)
    {
        var dict = _dbContext.Db.Queryable<OA_BASE_DATA>()
            .Where(x=>x.CLASS_ID == AuthorizationClassId)
            .Where(x => x.DATA_ID == id && x.FATHER_ID == RecordContentDict.RECORD_AUTHORIZATION_DICT && x.STATE_FLAG == "1")
            .First();

        if (dict is not null)
        {
            dict.STATE_FLAG = "2";
            _dbContext.Db.Updateable(dict).IgnoreColumns(ignoreAllNullColumns:true).ExecuteCommand();
        }
    }
    public List<OA_BASE_DATA> GetAuthorizationRecordDicts(string? content)
    {
        var dicts = _dbContext.Db.Queryable<OA_BASE_DATA>()
            .OrderByDescending(x => x.FIRST_RTIME)
            .Where(x => x.FATHER_ID == RecordContentDict.RECORD_AUTHORIZATION_DICT)
            .Where(x => x.STATE_FLAG == "1")
            .Where( x => x.CLASS_ID == AuthorizationClassId)
            .WhereIF(content.IsNotNullOrEmpty(), x => x.DATA_NAME.Contains(content))
            .ToList();
        if (dicts is null)
        {
            return new List<OA_BASE_DATA>();
        }
        else
        {
            return dicts;
        }
    }

    /// <summary>
    /// 根据用户hisName 返回用户信息
    /// </summary>
    /// <param name="names"></param>
    /// <returns></returns>
    public List<TableUserInfoDto> GetTableUsersByHisName(List<string> names)
    {
        var result = new List<TableUserInfoDto>();
        var equipmentContext = new EquipmentContext(_dbContext);
        foreach (var name in names)
        {
            var info = GetUserByHisName(name);
            result.Add(new TableUserInfoDto()
            {
                UserNo = info.UserNO,
                UserName = info.UserName,
                PGroupId = info.PGid,
                PGroupName = equipmentContext.ExchangeProfessionalGroupName(info.PGid)
            });
        }
        return result;
    }
    
    /// <summary>
    /// 根据用户userNo 返回用户信息
    /// </summary>
    /// <param name="ids"></param>
    /// <returns></returns>
    public List<TableUserInfoDto> GetTableUsersByIds(List<string> ids)
    {
        var result = new List<TableUserInfoDto>();
        var equipmentContext = new EquipmentContext(_dbContext);
        foreach (var userNo in ids)
        {
            var info = GetPersonJob(userNo);
            result.Add(new TableUserInfoDto()
            {
                UserNo = userNo,
                UserName = info.userName,
                PGroupId = info.PGid,
                PGroupName = equipmentContext.ExchangeProfessionalGroupName(info.PGid)
            });
        }
        
        return result;
    }
    
    /// <summary>
    /// 添加授权记录
    /// </summary>
    /// <param name="equipmentId">设备id</param>
    /// <param name="input"></param>
    /// <returns></returns>
    public EMS_AUTHORIZE_INFO AddAuthorizationRecord(string equipmentId, AuthorizationRecordDto input)
    {

        var personJob = GetPersonJob(input.AUTHORIZE_PERSON_ID);
        if (input.AUTHORIZED_PERSON_IDS.IsNullOrEmpty())
        {
            input.AUTHORIZED_PERSON_IDS = "";
        }
        var prsonsIds = input.AUTHORIZED_PERSON_IDS.Split(";").ToArray();
        input.AUTHORIZED_PERSON = "";
        foreach (var personId in prsonsIds)
        {
            var personInfo = GetPersonJob(personId);
            input.AUTHORIZED_PERSON += personInfo.userName+";";
        }
        input.AUTHORIZED_PERSON = input.AUTHORIZED_PERSON.TrimEnd(';');
        var authorizationRecord = new EMS_AUTHORIZE_INFO()
        {
            EQUIPMENT_ID = equipmentId,
            AUTHORIZE_ID = IDGenHelper.CreateGuid(),
            AUTHORIZE_STATE = "1",
            HOSPITAL_ID = _user.HOSPITAL_ID,
            AUTHORIZE_PERSON = personJob.userName,
            AUTHORIZED_PERSON = input.AUTHORIZED_PERSON,
            AUTHORIZED_ROLE = input.AUTHORIZED_ROLE,
            AUTHORIZE_DATE = input.AUTHORIZE_DATE,
            AUTHORIZED_PERSON_POST = personJob.job,
        };
        var result = _dbContext.Db.Insertable(authorizationRecord).ExecuteReturnEntity();
        result.AUTHORIZE_PERSON_ID = input.AUTHORIZE_PERSON_ID;
        result.AUTHORIZED_PERSON_IDS =input.AUTHORIZED_PERSON_IDS;
        return result;
    }
    
    /// <summary>
    /// 更新授权记录
    /// </summary>
    /// <param name="授权记录id  AUTHORIZE_ID ">设备id</param>
    /// <param name="input"></param>
    /// <returns></returns>
    public void UpdateAuthorizationRecord(string id, AuthorizationRecordDto input)
    {
        var authorizationRecord =  _dbContext.Db.Queryable<EMS_AUTHORIZE_INFO>()
            .Where(x => x.AUTHORIZE_ID == id)
            .First();
        if (authorizationRecord is  not null)
        {
            var personJob = GetPersonJob(input.AUTHORIZE_PERSON_ID);
            var prsonsIds = input.AUTHORIZED_PERSON_IDS.Split(";").ToArray();
            input.AUTHORIZED_PERSON = "";
            foreach (var personId in prsonsIds)
            {
                var personInfo = GetPersonJob(personId);
                input.AUTHORIZED_PERSON += personInfo.userName+";";
            }
            input.AUTHORIZED_PERSON  =  input.AUTHORIZED_PERSON.TrimEnd(';');
            authorizationRecord.AUTHORIZE_PERSON = personJob.userName;
            authorizationRecord.AUTHORIZED_PERSON = input.AUTHORIZED_PERSON;
            authorizationRecord.AUTHORIZED_ROLE = input.AUTHORIZED_ROLE;
            authorizationRecord.AUTHORIZE_DATE = input.AUTHORIZE_DATE;
            authorizationRecord.AUTHORIZED_PERSON_POST = personJob.job;
            _dbContext.Db.Updateable(authorizationRecord).ExecuteCommand();
        }
    }
    
    /// <summary>
    /// 根据用户id（personId） 获取用户职务
    /// </summary>
    /// <param name="personId"> 用户id PMS_PERSON_INFO.PERSON_ID </param>
    /// <returns></returns>
    private (string userName, string job, string PGid) GetPersonJob(string personId)
    {
        var personJob = "";
        if (personId.IsNullOrEmpty())
        {
            return ("",personJob,"");
        }
        var personJobs = _dbContext.Db.Queryable<SYS6_BASE_DATA>()
            .Where(x => x.CLASS_ID == "职务")
            .ToList();
        var person = _dbContext.Db.Queryable<SYS6_USER>()
            .Where(x => x.USER_NO == personId)
            .Select(x => new { x.LOGID, x.USERNAME, x.POWER ,x.DEPT_CODE})
            .First();
        if (person is not null)
        {
            var job = personJobs.FirstOrDefault(x => x.DATA_ID == person.POWER);
            if (job is not null)
            {
                personJob = job.DATA_CNAME ?? "";
            }
        }

        if (person is null )
        {
            return (userName:$"{person.LOGID}_{person.USERNAME}",personJob , "" );
        }

        return (userName:$"{person.LOGID}_{person.USERNAME}",personJob , person.DEPT_CODE );
        
    }
    
    
    private (string? PGid, string UserNO, string UserName) GetUserByHisName(string hisName)
    {
        var userNameAndLogId = hisName.Split('_');
        var userName = userNameAndLogId[1];
        var logId = userNameAndLogId[0];
        
        var person = _dbContext.Db.Queryable<SYS6_USER>()
            .Where(x => x.LOGID == logId)
            .Where(x=>x.USERNAME == userName)
            .Select(x => new { x.DEPT_CODE, x.USERNAME, x.USER_NO })
            .First();

        if (person is null)
        {
            return ("","",userName);
        }
        return (PGid: person.DEPT_CODE, UserNO: person.USER_NO, UserName: person.USERNAME);
    }
}