﻿using H.Utility.SqlSugarInfra;
using SqlSugar;

namespace XH.H82.Models.Entities.Common
{

    /// <summary>
    /// OnlyOffice模版样式Excel导出样式配置
    /// </summary>
    [DBOwner("XH_OA")]
    public class OA_EXCEL_STYLE_TEMPLATE
    {
        [SugarColumn(IsPrimaryKey = true)]
        /// <summary>
        /// excel导出模板ID
        /// </summary>
        public string EXCEL_TPL_ID { get; set; }

        /// <summary>
        /// 样式模板ID
        /// </summary>
        public string STYLE_ID { get; set; }

        /// <summary>
        /// 表头配置
        /// </summary>
        public string? COLUMN_JSON { get; set; }

        /// <summary>
        /// 表格配置
        /// </summary>
        public string? TABLE_JSON { get; set; }

        /// <summary>
        /// 其他配置
        /// </summary>
        public string? ADDN_JSON { get; set; }


        /// <summary>
        /// 状态;0禁用1在用2删除
        /// </summary>
        public string EXCEL_TPL_STATE { get; set; }

        /// <summary>
        /// 首次登记人
        /// </summary>
        public string FIRST_RPERSON { get; set; }

        /// <summary>
        /// 首次登记时间
        /// </summary>
        public DateTime FIRST_RTIME { get; set; }

        /// <summary>
        /// 最后修改人员
        /// </summary>
        public string LAST_MPERSON { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime LAST_MTIME { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string REMARK { get; set; }

        /// <summary>
        /// 配置的分类及字段(OA_FIELD_DICT),格式CLASS_CODE:FIELD_CODE1,FIELD_CODE2  数据项则固定CLASS_CODE=数据项三个字
        /// </summary>
        public string ITEM_SETUP_JSON { get; set; }




    }
}
