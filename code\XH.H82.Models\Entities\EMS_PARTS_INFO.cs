﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using H.Utility.SqlSugarInfra;
using SqlSugar;

namespace XH.H82.Models.Entities
{
    [DBOwner("XH_OA")]
    public class EMS_PARTS_INFO
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string PARTS_ID { get; set; }
        public string HOSPITAL_ID { get; set; }
        public string EQUIPMENT_ID { get; set; }
        public string PARTS_NAME { get; set; }
        public string PARTS_MODEL { get; set; }
        public string PARTS_SPEC { get; set; }
        public double PARTS_AMOUNT { get; set; }
        public string PARTS_SNUM { get; set; }
        public string PARTS_ORIGIN { get; set; }
        public string PARTS_BRAND { get; set; }
        public string PARTS_POSITION { get; set; }
        public string PARTS_STATE { get; set; }
        public string FIRST_RPERSON { get; set; }
        public DateTime? FIRST_RTIME { get; set; }
        public string LAST_MPERSON { get; set; }
        public DateTime? LAST_MTIME { get; set; }
        public string REMARK { get; set; }
    }
}
