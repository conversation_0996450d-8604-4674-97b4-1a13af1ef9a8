﻿using H.Utility;
using System.Diagnostics;
using System.Net;
using System.Net.NetworkInformation;
using System.Net.Sockets;

namespace XH.H82.API.Middleware
{
    /// <summary>
    /// 当前服务ip地址中间件  辅助诊断负载到哪个服务器（临时方案）
    /// </summary>
    public class AddrResposeHeadMiddleware
    {
        private readonly RequestDelegate _next;

        public AddrResposeHeadMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task Invoke(HttpContext context)
        {
            Activity activity = Activity.Current;
            context.Request.Headers.TryGetValue("Xh-Lab-Addr", out var labAddrStr);
            string addrStr = (!labAddrStr.ToString().IsNullOrEmpty()) ? labAddrStr.ToString() : GetServerAddr();
            context.Response.Headers.TryAdd("Xh-Lab-Addr", addrStr);
            activity.SetTag("Xh-Lab-Addr", addrStr);
            await _next(context);
        }

        private string GetServerAddr()
        {
            try
            {
                //获取服务器上所有网卡的IP地址
                NetworkInterface[] networks = NetworkInterface.GetAllNetworkInterfaces();
                string serverIpAddresses = string.Empty;

                foreach (var network in networks)
                {
                    if (!network.Name.Contains("docker") && !network.Name.Contains("vm"))
                    {
                        var ipAddress = network.GetIPProperties().UnicastAddresses.Where(p => p.Address.AddressFamily == AddressFamily.InterNetwork
                    && !IPAddress.IsLoopback(p.Address)).FirstOrDefault()?.Address.ToString();
                        if (ipAddress.IsNotNullOrEmpty())
                        {
                            serverIpAddresses += network.Name + ":" + ipAddress + "|";
                        }
                    }

                }
                return serverIpAddresses;
            }
            catch (Exception ex)
            {

                return ex.Message;
            }

        }
    }
}
