﻿<?xml version="1.0" encoding="utf-8" ?>
<root>
	<更新item表数据>
		<MSSQL>
			<SQL>
				UPDATE LIS_REQUISITION_ITEM SET REQUISITION_ID = @REQUISITION_ID_NEW WHERE REQUISITION_ID = @REQUISITION_ID
			</SQL>
		</MSSQL>
	</更新item表数据>

	<更新collectinfo表数据>
		<MSSQL>
			<SQL>
				UPDATE LIS5_REQ_COLLECT_INFO SET REQUISITION_ID = @REQUISITION_ID_NEW WHERE REQUISITION_ID = @REQUISITION_ID
			</SQL>
		</MSSQL>
	</更新collectinfo表数据>

	<更新info表数据>
		<MSSQL>
			<SQL>
				UPDATE LIS_REQUISITION_INFO
				SET REQUISITION_ID = @REQUISITION_ID_NEW,
					EXECUTE_PERSON = @LOGINID_NAME,
					EXECUTE_TIME   = SYSDATE,
					EXECUTE_DEPT   = @PATIENT_DEPT,
					PRINT_PERSON   = @LOGINID_NAME,
					PRINT_TIME     = SYSDATE,
					PRINT_DEPT     = @PATIENT_DEPT,
					REQ_ID_OLD     = @REQUISITION_ID,
				    REQUISITION_STATE= 'printed'
				WHERE REQUISITION_ID = @REQUISITION_ID
			</SQL>
		</MSSQL>
	</更新info表数据>

	<增加操作记录>
		<MSSQL>
			<SQL>
				INSERT INTO LIS5_REQ_SAMPLE_STATE(SAMPLE_ID, REQUISITION_ID, HOSPITAL_ID, EXECUTE_MODULE, EXECUTE_ACTION, EXECUTE_TIME, EXECUTE_PERSON, EXECUTE_DEPT, EXEC_FRONT_STATE, SAMPLE_STATE, EXECUTE_IP, EXECUTE_COMPUTER, FIRST_PERSON, FIRST_TIME, LAST_MPERSON, LAST_MTIME, REMARK)
				VALUES(@GUID,@REQUISITION_ID, @HOSPITAL_ID, @EXECUTE_MODULE, @EXECUTE_ACTION, @CURR_TIME, @EXECUTE_PERSON, @EXECUTE_DEPT, @EXEC_FRONT_STATE, @ETAG_STATE, @EXECUTE_IP, @EXECUTE_COMPUTER, @EXECUTE_PERSON, @CURR_TIME, NULL, NULL, @REMARK)
			</SQL>
		</MSSQL>
	</增加操作记录>
</root>