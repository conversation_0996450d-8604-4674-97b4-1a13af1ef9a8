using System.ComponentModel.DataAnnotations;
using AutoMapper;
using H.Utility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using XH.H82.API.Extensions;
using XH.H82.IServices.Certificate;
using XH.H82.Models.Dtos;
using XH.H82.Models.Dtos.Certificate;

namespace XH.H82.API.Controllers.Certificate;



[Route("api/[controller]/[action]")]
[ApiController]
[Authorize]
public class CertificateController :ControllerBase
{
    private readonly IHttpContextAccessor _httpContext;
    private readonly ICertificateService _certificateService;
    private readonly IMapper _mapper;

    public CertificateController(IHttpContextAccessor httpContext, IMapper mapper, ICertificateService certificateService)
    {
        _httpContext = httpContext;
        _mapper = mapper;
        _certificateService = certificateService;
    }


    /// <summary>
    /// 查询设备相关证书
    /// </summary>
    /// <returns></returns>
    [HttpGet("{equipmentId}")]
    [CustomResponseType(typeof(List<EquipmentCertificateDto>))]

    public IActionResult GetEquipmentCertificates([Required]string equipmentId)
    {
        var equipmentCertificates =  _certificateService.GetEquipmentCertificates(equipmentId);
        var result = new List<EquipmentCertificateDto>();
        foreach (var equipmentCertificate in equipmentCertificates)
        {
            var certificateTypeName = _certificateService.GetCertificateTypeName(equipmentCertificate.CRETIFICATE_TYPE);
            var dto = EquipmentCertificateDto.Create(
                equipmentCertificate.CERTIFICATE_ID,
                equipmentCertificate.CRETIFICATE_TYPE,
                certificateTypeName,
                equipmentCertificate.EXPIRY_DATE,
                equipmentCertificate.CER_DATE,
                equipmentCertificate.CER_WANR_DATE,
                false
            );
            var attachmentDtos = _mapper.Map<List<AttachmentDto>>(equipmentCertificate.ATTACHMENTS);
            dto.Attachments.AddRange(attachmentDtos);
            result.Add(dto);
        }
        return Ok(result.ToResultDto());
    }

    /// <summary>
    /// 添加设备的证书信息
    /// </summary>
    /// <param name="equipmentId"></param>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("{equipmentId}")]
    [CustomResponseType(typeof(EquipmentCertificateDto))]

    public IActionResult CreateEquipmentCertificate([Required]string equipmentId , [FromBody]EquipmentCertificateInput input)
    {
       var equipmentCertificate =   _certificateService.AddEquipmentCertificate(equipmentId,input.CretificateType,input.ExpiryDate,input.CerDate,input.CerWanrDate);
       var certificateTypeName = _certificateService.GetCertificateTypeName(equipmentCertificate.CRETIFICATE_TYPE);
       var result = EquipmentCertificateDto.Create(
           equipmentCertificate.CERTIFICATE_ID,
           equipmentCertificate.CRETIFICATE_TYPE,
           certificateTypeName,
           equipmentCertificate.EXPIRY_DATE,
           equipmentCertificate.CER_DATE,
           equipmentCertificate.CER_WANR_DATE,
           false
       );
       return Ok(result.ToResultDto());
    }
    /// <summary>
    /// 证书信息添加附件
    /// </summary>
    /// <param name="cretificateId">正式信息ID</param>
    /// <param name="file">模型内的DOC_INFO_ID 不需要传参</param>
    /// <returns></returns>
    [HttpPost("{cretificateId}")]
    [CustomResponseType(typeof(EquipmentCertificateDto))]

    public IActionResult AddCertificateAttachment([Required]string cretificateId , [FromForm] UploadFileDto file)
    {
       var equipmentCertificate = _certificateService.AddEquipmentCertificateAttachments(cretificateId, file);
       var certificateTypeName = _certificateService.GetCertificateTypeName(equipmentCertificate.CRETIFICATE_TYPE);
       var result = EquipmentCertificateDto.Create(
           equipmentCertificate.CERTIFICATE_ID,
           equipmentCertificate.CRETIFICATE_TYPE,
           certificateTypeName, 
           equipmentCertificate.EXPIRY_DATE,
           equipmentCertificate.CER_DATE,
           equipmentCertificate.CER_WANR_DATE,
           false
       );
       result.Attachments = _mapper.Map<List<AttachmentDto>>(equipmentCertificate.ATTACHMENTS);
       return Ok(result.ToResultDto());
    }

    /// <summary>
    /// 编辑证书信息
    /// </summary>
    /// <param name="cretificateId"></param>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPut("{cretificateId}")]
    [CustomResponseType(typeof(EquipmentCertificateDto))]

    public IActionResult EditEquipmentCertificate([Required]string cretificateId , [FromBody] EquipmentCertificateInput input)
    {
        var equipmentCertificate = _certificateService.EditEquipmentCertificate(cretificateId, input.CretificateType,
            input.ExpiryDate, input.CerDate, input.CerWanrDate);
        var certificateTypeName = _certificateService.GetCertificateTypeName(equipmentCertificate.CRETIFICATE_TYPE);
        var result = EquipmentCertificateDto.Create(
            equipmentCertificate.CERTIFICATE_ID,
            equipmentCertificate.CRETIFICATE_TYPE,
            certificateTypeName,
            equipmentCertificate.EXPIRY_DATE,
            equipmentCertificate.CER_DATE,
            equipmentCertificate.CER_WANR_DATE,
            false
        );
        result.Attachments = _mapper.Map<List<AttachmentDto>>(equipmentCertificate.ATTACHMENTS);
        return Ok(result.ToResultDto());
        
    }

    /// <summary>
    /// 删除证书信息
    /// </summary>
    /// <returns></returns>
    [HttpDelete("{cretificateId}")]
    public IActionResult DeleteEquipmentCertificate([Required] string cretificateId )
    {
        _certificateService.DeleteEquipmentCertificate(cretificateId);
        return Ok(true.ToResultDto());
    }
    
    /// <summary>
    /// 删除附件
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpDelete("{id}")]
    public IActionResult DeleteEquipmentCertificateAttachment([Required] string id )
    {
         _certificateService.DeleteEquipmentCertificateAttachment(id);
         return Ok(true.ToResultDto());
    }


    /// <summary>
    /// 获取设备关联供应商列表
    /// </summary>
    /// <param name="equipmentId"></param>
    /// <returns></returns>
    [HttpGet("{equipmentId}")]
    [CustomResponseType(typeof(List<CompanyCertificatDto>))]

    public IActionResult GetEquipmentCompanyCertificates([Required]string equipmentId)
    {
      var result =   _certificateService.GetEquipmentCompanyCertificates(equipmentId);
      return Ok(result.ToResultDto());
    }


    #region 证书类型操作
    /// <summary>
    /// 获取证书类型列表
    /// </summary>
    /// <param name="typeName">类型名称模糊查询</param>
    /// <param name="state">状态：0禁用、1启用 不填则都 返回</param>
    /// <returns></returns>
    [HttpGet]
    [CustomResponseType(typeof(List<CertificateTypeDto>))]
    public IActionResult GetCertificateTypes(string? typeName ,string? state )
    { 
       var datas=  _certificateService.GetEquipmentCertificateTypes(typeName,state);
       var result = _mapper.Map<List<CertificateTypeDto>>(datas);
       
       return Ok(result.ToResultDto());
    }
    /// <summary>
    /// 编辑证书类型数据
    /// </summary>
    /// <param name="id"></param>
    /// <param name="certificateTypeName"></param>
    /// <param name="remark"></param>
    /// <returns></returns>
    [HttpPut("{id}")]
    [CustomResponseType(typeof(CertificateTypeDto))]

    public IActionResult EditCertificateType([Required] string id, [Required]string certificateTypeName , string? remark)
    {
        var data =  _certificateService.EditCertificateType(id,certificateTypeName,remark);
        var result = _mapper.Map<CertificateTypeDto>(data);
        return Ok(result.ToResultDto());
    }
    /// <summary>
    /// 创建证书类型
    /// </summary>
    /// <param name="certificateTypeName"></param>
    /// <param name="remark"></param>
    /// <returns></returns>
    [HttpPost]
    [CustomResponseType(typeof(CertificateTypeDto))]
    public IActionResult CreateCertificateType([Required]string certificateTypeName , string? remark)
    {
        var data =  _certificateService.AddCertificateType(certificateTypeName,remark);
        var result = _mapper.Map<CertificateTypeDto>(data);
        return Ok(result.ToResultDto());
        
    }

    /// <summary>
    /// 删除证书类型
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpDelete ("{id}")]
    public IActionResult DeleteCertificateType([Required] string id)
    {
        _certificateService.DeleteCertificateType(id);
        return Ok(true.ToResultDto());
    }
    
    /// <summary>
    /// 禁用/启用证书类型
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpPost ("{id}")]
    public IActionResult DisableOrEnableCertificateType([Required] string id)
    {
        _certificateService.DisableOrEnableCertificateType(id);
        return Ok(true.ToResultDto());
    }
    #endregion

}