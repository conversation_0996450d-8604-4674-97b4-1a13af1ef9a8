2025-07-02 10:53:20.579 +08:00 [INF] ==>App Start..2025-07-02 10:53:20
2025-07-02 10:53:20.789 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-02 10:53:20.793 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-02 10:53:23.035 +08:00 [INF] ==>基础连接请求完成.
2025-07-02 10:53:24.074 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-02 10:53:24.994 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-02 10:53:25.306 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-02 10:53:25.309 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-02 10:53:25.642 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-02 10:53:27.893 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-02 10:53:28.422 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-02 10:53:29.019 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-02 10:53:29.019 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-02 10:53:29.548 +08:00 [ERR] 查询出错:中文提示 :  连接数据库过程中发生错误，检查服务器是否正常连接字符串是否正确，错误信息：ORA-12545: Network Transport: Unable to resolve connect hostnameDbType="Oracle";ConfigId="".
English Message : Connection open error . ORA-12545: Network Transport: Unable to resolve connect hostnameDbType="Oracle";ConfigId="" :
SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON" FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "LAST_MTIME" IS NULL )
2025-07-02 10:53:50.654 +08:00 [INF] ==>App Start..2025-07-02 10:53:50
2025-07-02 10:53:50.838 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-02 10:53:50.842 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-02 10:53:52.443 +08:00 [INF] ==>基础连接请求完成.
2025-07-02 10:53:52.860 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-02 10:53:53.338 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-02 10:53:53.683 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-02 10:53:53.686 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-02 10:53:54.035 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-02 10:53:54.660 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-02 10:53:54.775 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-02 10:53:55.229 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-02 10:53:55.229 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-02 10:53:55.726 +08:00 [ERR] 查询出错:中文提示 :  连接数据库过程中发生错误，检查服务器是否正常连接字符串是否正确，错误信息：ORA-12545: Network Transport: Unable to resolve connect hostnameDbType="Oracle";ConfigId="".
English Message : Connection open error . ORA-12545: Network Transport: Unable to resolve connect hostnameDbType="Oracle";ConfigId="" :
SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON" FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "LAST_MTIME" IS NULL )
2025-07-02 10:54:10.705 +08:00 [INF] ==>App Start..2025-07-02 10:54:10
2025-07-02 10:54:10.921 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-02 10:54:10.924 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-02 10:54:12.385 +08:00 [INF] ==>基础连接请求完成.
2025-07-02 10:54:12.763 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-02 10:54:13.137 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-02 10:54:13.476 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-02 10:54:13.478 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-02 10:54:13.816 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-02 10:54:14.247 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-02 10:54:14.360 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-02 10:54:14.805 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-02 10:54:14.805 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-02 10:54:15.860 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-02 10:54:18.099 +08:00 [INF] ==>初始化完成..
2025-07-02 10:54:18.161 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-02 10:54:18.164 +08:00 [INF] 设备启用任务
2025-07-02 10:54:18.165 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-02 10:54:18.577 +08:00 [INF] 【SQL执行耗时:390.7155ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-02 10:54:18.735 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-02 10:54:18.749 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-02 10:54:18.751 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-02 10:54:18.751 +08:00 [INF] Hosting environment: Development
2025-07-02 10:54:18.751 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-02 10:55:06.411 +08:00 [INF] 第三方url地址为：http://************
2025-07-02 10:56:20.005 +08:00 [INF] ==>App Start..2025-07-02 10:56:19
2025-07-02 10:56:20.183 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-02 10:56:20.186 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-02 10:56:21.844 +08:00 [INF] ==>基础连接请求完成.
2025-07-02 10:56:22.233 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-02 10:56:22.641 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-02 10:56:22.980 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-02 10:56:22.987 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-02 10:56:23.341 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-02 10:56:23.810 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-02 10:56:23.905 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-02 10:56:24.331 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-02 10:56:24.332 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-02 10:56:25.804 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-02 10:56:28.039 +08:00 [INF] ==>初始化完成..
2025-07-02 10:56:28.063 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-02 10:56:28.065 +08:00 [INF] 设备启用任务
2025-07-02 10:56:28.066 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-02 10:56:28.467 +08:00 [INF] 【SQL执行耗时:379.7531ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-02 10:56:28.611 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-02 10:56:28.625 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-02 10:56:28.627 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-02 10:56:28.627 +08:00 [INF] Hosting environment: Development
2025-07-02 10:56:28.628 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-02 10:56:36.442 +08:00 [INF] 第三方url地址为：http://************
2025-07-02 10:58:50.988 +08:00 [INF] ==>App Start..2025-07-02 10:58:50
2025-07-02 10:58:51.163 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-02 10:58:51.166 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-02 10:58:52.753 +08:00 [INF] ==>基础连接请求完成.
2025-07-02 10:58:53.135 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-02 10:58:53.582 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-02 10:58:53.942 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-02 10:58:53.945 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-02 10:58:54.759 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-02 10:58:54.857 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-02 10:58:55.281 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-02 10:58:55.281 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-02 10:58:55.366 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-02 10:58:56.183 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-02 10:58:58.979 +08:00 [INF] ==>初始化完成..
2025-07-02 10:58:59.000 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-02 10:58:59.002 +08:00 [INF] 设备启用任务
2025-07-02 10:58:59.003 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-02 10:58:59.434 +08:00 [INF] 【SQL执行耗时:409.2812ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-02 10:58:59.565 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-02 10:58:59.577 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-02 10:58:59.579 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-02 10:58:59.579 +08:00 [INF] Hosting environment: Development
2025-07-02 10:58:59.579 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-02 10:59:23.095 +08:00 [INF] 第三方url地址为：http://************
2025-07-02 10:59:50.393 +08:00 [INF] HTTP GET /api/XhEquipmentDoc/GetEquipmentListByMgroup responded 200 in 32170.3721 ms
2025-07-02 10:59:50.396 +08:00 [INF] 【接口超时阀值预警】 [519f072bc0bc8cbe6b8ea321fe6f6e4b]接口/api/XhEquipmentDoc/GetEquipmentListByMgroup,耗时:[32178]毫秒
2025-07-02 10:59:59.152 +08:00 [INF] 第三方url地址为：http://************
2025-07-02 11:01:06.877 +08:00 [INF] HTTP GET /api/XhEquipmentDoc/GetEquipmentListByMgroup responded 200 in 67768.4676 ms
2025-07-02 11:01:06.877 +08:00 [INF] 【接口超时阀值预警】 [3471c1c88cc33d81861108a16f890686]接口/api/XhEquipmentDoc/GetEquipmentListByMgroup,耗时:[67768]毫秒
2025-07-02 11:01:17.275 +08:00 [INF] 第三方url地址为：http://************
2025-07-02 11:01:47.822 +08:00 [INF] HTTP GET /api/EquipmentDoc/GetEquipmentList responded 200 in 30589.3132 ms
2025-07-02 11:01:47.822 +08:00 [INF] 【接口超时阀值预警】 [117239270dbc87d07bcd4b1bc7dae824]接口/api/EquipmentDoc/GetEquipmentList,耗时:[30589]毫秒
2025-07-02 11:01:50.112 +08:00 [INF] 第三方url地址为：http://************
2025-07-02 11:02:48.912 +08:00 [INF] 第三方url地址为：http://************
2025-07-02 11:02:58.084 +08:00 [INF] HTTP GET /api/EquipmentDoc/GetEquipmentList responded 200 in 68013.9468 ms
2025-07-02 11:02:58.084 +08:00 [INF] 【接口超时阀值预警】 [9a35ae28d3c586a7bc3aa1f516e1831a]接口/api/EquipmentDoc/GetEquipmentList,耗时:[68014]毫秒
2025-07-02 11:06:48.805 +08:00 [INF] 第三方url地址为：http://************
2025-07-02 11:06:49.736 +08:00 [INF] HTTP GET /api/XhEquipmentDoc/GetEquipmentListByMgroup responded 200 in 240862.6916 ms
2025-07-02 11:06:49.736 +08:00 [INF] 【接口超时阀值预警】 [220d5c027ea6709925754c3ceba6fd0c]接口/api/XhEquipmentDoc/GetEquipmentListByMgroup,耗时:[240863]毫秒
2025-07-02 11:07:58.797 +08:00 [INF] 第三方url地址为：http://************
2025-07-02 11:09:34.613 +08:00 [ERR] 查询出错:中文提示 :  连接数据库过程中发生错误，检查服务器是否正常连接字符串是否正确，错误信息：Connection request timed outDbType="Oracle";ConfigId="".
English Message : Connection open error . Connection request timed outDbType="Oracle";ConfigId="" :
 SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_ID" = '1721768701478440960' ) AND   ROWNUM = 1 
2025-07-02 11:09:38.736 +08:00 [ERR] 未处理的异常::SqlSugar.SqlSugarException: 中文提示 :  连接数据库过程中发生错误，检查服务器是否正常连接字符串是否正确，错误信息：Connection request timed outDbType="Oracle";ConfigId="".
English Message : Connection open error . Connection request timed outDbType="Oracle";ConfigId="" 
   at SqlSugar.Check.Exception(Boolean isException, String message, String[] args)
   at SqlSugar.AdoProvider.CheckConnection()
   at SqlSugar.OracleProvider.GetCommand(String sql, SugarParameter[] parameters)
   at SqlSugar.AdoProvider.GetDataReader(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetData[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToList[TResult]()
   at SqlSugar.QueryableProvider`1.ToList()
   at SqlSugar.QueryableProvider`1.First()
   at SqlSugar.QueryableProvider`1.First(Expression`1 expression)
   at XH.H82.Services.XhEquipmentDocService.GetEquipmentListByInspection(String userNo, String equipmentNo, String labId, String areaId, String mgroupid, String pgroupid, String isHide, String smblFlag) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\XhEquipmentDocService.cs:line 254
   at XH.H82.Services.XhEquipmentDocService.GetEquipmentListByMgroup(String userNo, String hospitalId, String equipmentNo, String labId, String areaId, String ifHide) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\XhEquipmentDocService.cs:line 489
   at Castle.Proxies.Invocations.IXhEquipmentDocService_GetEquipmentListByMgroup.InvokeMethodOnTarget()
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.UseCacheWhenFailedAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.CacheAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at Castle.Proxies.IXhEquipmentDocServiceProxy.GetEquipmentListByMgroup(String userNo, String hospitalId, String equipmentNo, String labId, String areaId, String ifHide)
   at XH.H82.API.Controllers.XhEquipmentDocController.GetEquipmentListByMgroup(String areaId, String labId, String equipmentNo, String ifHide, String pGroupId) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\Controllers\XhEquipmentDocController.cs:line 123
   at lambda_method908(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-07-02 11:09:38.739 +08:00 [ERR] HTTP GET /api/XhEquipmentDoc/GetEquipmentListByMgroup responded 500 in 169971.4111 ms
2025-07-02 11:09:38.740 +08:00 [INF] 【接口超时阀值预警】 [6aa2a1112897c7a71fc8e44e86dcfbb3]接口/api/XhEquipmentDoc/GetEquipmentListByMgroup,耗时:[169971]毫秒
2025-07-02 11:09:38.778 +08:00 [INF] HTTP GET /api/Base/GetGlobalLabId responded 200 in 4165.3994 ms
2025-07-02 11:09:38.779 +08:00 [INF] 【接口超时阀值预警】 [709ac8f4310b296d91f37342b20391ce]接口/api/Base/GetGlobalLabId,耗时:[4166]毫秒
2025-07-02 11:09:42.638 +08:00 [INF] HTTP GET /api/Base/GetGlobalLabId responded 200 in 70.0532 ms
2025-07-02 11:09:47.970 +08:00 [INF] HTTP GET /api/EquipmentDoc/GetEquipmentList responded 200 in 109252.3322 ms
2025-07-02 11:09:47.971 +08:00 [INF] 【接口超时阀值预警】 [78f095d0988fe707456dd9768b66455a]接口/api/EquipmentDoc/GetEquipmentList,耗时:[109252]毫秒
2025-07-02 11:09:53.536 +08:00 [INF] 第三方url地址为：http://************
2025-07-02 11:15:39.778 +08:00 [INF] ==>App Start..2025-07-02 11:15:39
2025-07-02 11:15:40.031 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-02 11:15:40.034 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-02 11:15:41.535 +08:00 [INF] ==>基础连接请求完成.
2025-07-02 11:15:41.930 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-02 11:15:42.310 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-02 11:15:42.632 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-02 11:15:42.634 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-02 11:15:42.963 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-02 11:15:43.426 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-02 11:15:43.516 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-02 11:15:43.934 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-02 11:15:43.934 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-02 11:15:45.062 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-02 11:15:47.362 +08:00 [INF] ==>初始化完成..
2025-07-02 11:15:47.382 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-02 11:15:47.384 +08:00 [INF] 设备启用任务
2025-07-02 11:15:47.385 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-02 11:15:47.812 +08:00 [INF] 【SQL执行耗时:405.4121ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-02 11:15:47.942 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-02 11:15:47.954 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-02 11:15:47.955 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-02 11:15:47.955 +08:00 [INF] Hosting environment: Development
2025-07-02 11:15:47.955 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-02 11:15:54.393 +08:00 [INF] 第三方url地址为：http://************
2025-07-02 11:17:04.500 +08:00 [INF] HTTP GET /api/EquipmentDoc/GetEquipmentList responded 200 in 73274.1913 ms
2025-07-02 11:17:04.504 +08:00 [INF] 【接口超时阀值预警】 [24e0c5ae3bc75560377aa9acf2fd057f]接口/api/EquipmentDoc/GetEquipmentList,耗时:[73281]毫秒
2025-07-02 11:17:41.438 +08:00 [INF] 第三方url地址为：http://************
2025-07-02 11:17:54.282 +08:00 [INF] HTTP GET /api/EquipmentDoc/GetEquipmentList responded 200 in 12902.1056 ms
2025-07-02 11:17:54.282 +08:00 [INF] 【接口超时阀值预警】 [8810f6591bf16e2fc75340e2c9e974e2]接口/api/EquipmentDoc/GetEquipmentList,耗时:[12902]毫秒
2025-07-02 11:17:55.655 +08:00 [INF] 第三方url地址为：http://************
2025-07-02 11:19:45.358 +08:00 [INF] HTTP GET /api/EquipmentDoc/GetEquipmentList responded 200 in 109738.3865 ms
2025-07-02 11:19:45.358 +08:00 [INF] 【接口超时阀值预警】 [f59fa8061e3b7b4f0bdabbf51daf1ce7]接口/api/EquipmentDoc/GetEquipmentList,耗时:[109738]毫秒
2025-07-02 11:32:27.326 +08:00 [INF] ==>App Start..2025-07-02 11:32:27
2025-07-02 11:32:27.527 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-02 11:32:27.530 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-02 11:32:29.134 +08:00 [INF] ==>基础连接请求完成.
2025-07-02 11:32:29.543 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-02 11:32:29.948 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-02 11:32:30.276 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-02 11:32:30.284 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-02 11:32:30.663 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-02 11:32:31.190 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-02 11:32:31.318 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-02 11:32:31.779 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-02 11:32:31.779 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-02 11:32:32.732 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-02 11:32:35.040 +08:00 [INF] ==>初始化完成..
2025-07-02 11:32:35.060 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-02 11:32:35.064 +08:00 [INF] 设备启用任务
2025-07-02 11:32:35.064 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-02 11:32:36.001 +08:00 [INF] 【SQL执行耗时:913.9526ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-02 11:32:36.219 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-02 11:32:36.232 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-02 11:32:36.233 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-02 11:32:36.233 +08:00 [INF] Hosting environment: Development
2025-07-02 11:32:36.233 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-02 11:32:49.441 +08:00 [INF] HTTP GET /api/XhEquipmentDoc/GetEquipmentClassList responded 401 in 287.0533 ms
2025-07-02 11:32:56.801 +08:00 [INF] 第三方url地址为：http://************
2025-07-02 11:33:11.878 +08:00 [INF] HTTP GET /api/XhEquipmentDoc/GetEquipmentClassList responded 200 in 20274.5297 ms
2025-07-02 11:33:11.880 +08:00 [INF] 【接口超时阀值预警】 [a4f8306a4d0caa0db269f379238c33af]接口/api/XhEquipmentDoc/GetEquipmentClassList,耗时:[20274]毫秒
2025-07-02 11:35:02.574 +08:00 [INF] ==>App Start..2025-07-02 11:35:02
2025-07-02 11:35:02.753 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-02 11:35:02.757 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-02 11:35:04.294 +08:00 [INF] ==>基础连接请求完成.
2025-07-02 11:35:04.855 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-02 11:35:05.265 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-02 11:35:05.571 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-02 11:35:05.579 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-02 11:35:05.920 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-02 11:35:06.384 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-02 11:35:06.483 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-02 11:35:06.923 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-02 11:35:06.924 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-02 11:35:07.800 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-02 11:35:10.101 +08:00 [INF] ==>初始化完成..
2025-07-02 11:35:10.123 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-02 11:35:10.126 +08:00 [INF] 设备启用任务
2025-07-02 11:35:10.127 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-02 11:35:10.518 +08:00 [INF] 【SQL执行耗时:370.3543ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-02 11:35:10.661 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-02 11:35:10.674 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-02 11:35:10.676 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-02 11:35:10.676 +08:00 [INF] Hosting environment: Development
2025-07-02 11:35:10.676 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-02 11:35:26.130 +08:00 [INF] 第三方url地址为：http://************
2025-07-02 11:35:43.564 +08:00 [INF] HTTP GET /api/XhEquipmentDoc/GetEquipmentClassList responded 200 in 21036.2028 ms
2025-07-02 11:35:43.567 +08:00 [INF] 【接口超时阀值预警】 [9f000bdeadba784dbc026e56801b31fc]接口/api/XhEquipmentDoc/GetEquipmentClassList,耗时:[21042]毫秒
2025-07-02 11:47:39.028 +08:00 [INF] ==>App Start..2025-07-02 11:47:39
2025-07-02 11:47:39.242 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-02 11:47:39.247 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-02 11:47:40.823 +08:00 [INF] ==>基础连接请求完成.
2025-07-02 11:47:41.220 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-02 11:47:41.675 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-02 11:47:42.012 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-02 11:47:42.020 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-02 11:47:42.357 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-02 11:47:42.922 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-02 11:47:43.037 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-02 11:47:43.487 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-02 11:47:43.487 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-02 11:47:44.627 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-02 11:47:46.931 +08:00 [INF] ==>初始化完成..
2025-07-02 11:47:46.952 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-02 11:47:46.956 +08:00 [INF] 设备启用任务
2025-07-02 11:47:46.957 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-02 11:47:47.374 +08:00 [INF] 【SQL执行耗时:395.1583ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-02 11:47:47.531 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-02 11:47:47.544 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-02 11:47:47.545 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-02 11:47:47.546 +08:00 [INF] Hosting environment: Development
2025-07-02 11:47:47.546 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-02 13:46:31.804 +08:00 [INF] HTTP GET /api/XhEquipmentDoc/GetEquipmentClassList responded 401 in 388.0284 ms
2025-07-02 13:46:46.664 +08:00 [INF] 第三方url地址为：http://************
2025-07-02 13:46:48.703 +08:00 [INF] 调用H07模块[/api/External/GetUserUnitInfo?moduleId=H82],耗时:549ms
2025-07-02 13:47:40.099 +08:00 [INF] HTTP GET /api/XhEquipmentDoc/GetEquipmentClassList responded 200 in 57774.3122 ms
2025-07-02 13:47:40.101 +08:00 [INF] 【接口超时阀值预警】 [eadf9dbd6ebff2f06e71e6f23ada9c8e]接口/api/XhEquipmentDoc/GetEquipmentClassList,耗时:[57774]毫秒
2025-07-02 13:47:43.401 +08:00 [INF] 第三方url地址为：http://************
2025-07-02 13:50:04.977 +08:00 [INF] HTTP GET /api/XhEquipmentDoc/GetEquipmentClassList responded 200 in 141619.6238 ms
2025-07-02 13:50:04.978 +08:00 [INF] 【接口超时阀值预警】 [40a2409bd64075323b5a7e5551fe39f1]接口/api/XhEquipmentDoc/GetEquipmentClassList,耗时:[141620]毫秒
2025-07-02 13:51:07.788 +08:00 [INF] 第三方url地址为：http://************
2025-07-02 14:01:34.602 +08:00 [INF] ==>App Start..2025-07-02 14:01:34
2025-07-02 14:01:34.799 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-02 14:01:34.803 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-02 14:01:36.500 +08:00 [INF] ==>基础连接请求完成.
2025-07-02 14:01:36.913 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-02 14:01:37.357 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-02 14:01:37.686 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-02 14:01:37.696 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-02 14:01:38.043 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-02 14:01:38.610 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-02 14:01:38.719 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-02 14:01:39.181 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-02 14:01:39.181 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-02 14:01:40.161 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-02 14:01:42.339 +08:00 [INF] ==>初始化完成..
2025-07-02 14:01:42.378 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-02 14:01:42.381 +08:00 [INF] 设备启用任务
2025-07-02 14:01:42.382 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-02 14:01:42.776 +08:00 [INF] 【SQL执行耗时:373.0063ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-02 14:01:42.944 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-02 14:01:42.957 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-02 14:01:42.959 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-02 14:01:42.959 +08:00 [INF] Hosting environment: Development
2025-07-02 14:01:42.959 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-02 14:01:47.321 +08:00 [INF] 第三方url地址为：http://************
2025-07-02 14:02:07.279 +08:00 [INF] HTTP GET /api/XhEquipmentDoc/GetEquipmentListByMgroup responded 200 in 24189.1374 ms
2025-07-02 14:02:07.282 +08:00 [INF] 【接口超时阀值预警】 [12313bbd867a2fc8c6c007b5bc38e1ef]接口/api/XhEquipmentDoc/GetEquipmentListByMgroup,耗时:[24196]毫秒
2025-07-02 14:03:34.836 +08:00 [INF] 第三方url地址为：http://************
2025-07-02 14:04:02.190 +08:00 [INF] HTTP GET /api/XhEquipmentDoc/GetEquipmentListByMgroup responded 200 in 27399.6490 ms
2025-07-02 14:04:02.190 +08:00 [INF] 【接口超时阀值预警】 [d67750ae8137dc7ba92e24a02d64c201]接口/api/XhEquipmentDoc/GetEquipmentListByMgroup,耗时:[27400]毫秒
2025-07-02 14:04:08.409 +08:00 [INF] 第三方url地址为：http://************
2025-07-02 14:04:35.190 +08:00 [INF] HTTP GET /api/XhEquipmentDoc/GetEquipmentListByMgroup responded 200 in 26859.8697 ms
2025-07-02 14:04:35.191 +08:00 [INF] 【接口超时阀值预警】 [78ca95c2aded4016317fceb4a8731dbe]接口/api/XhEquipmentDoc/GetEquipmentListByMgroup,耗时:[26860]毫秒
2025-07-02 14:05:30.111 +08:00 [INF] 第三方url地址为：http://************
2025-07-02 14:07:45.536 +08:00 [INF] HTTP GET /api/XhEquipmentDoc/GetEquipmentListByMgroup responded 200 in 135476.1208 ms
2025-07-02 14:07:45.536 +08:00 [INF] 【接口超时阀值预警】 [19ad2d1259b0c323b40686cc54a74548]接口/api/XhEquipmentDoc/GetEquipmentListByMgroup,耗时:[135476]毫秒
2025-07-02 14:09:19.903 +08:00 [INF] 第三方url地址为：http://************
2025-07-02 14:09:55.249 +08:00 [INF] HTTP GET /api/XhEquipmentDoc/GetEquipmentListByMgroup responded 200 in 35394.6696 ms
2025-07-02 14:09:55.250 +08:00 [INF] 【接口超时阀值预警】 [857fff461db71a8c7fc193509a6d2407]接口/api/XhEquipmentDoc/GetEquipmentListByMgroup,耗时:[35395]毫秒
2025-07-02 14:10:38.951 +08:00 [INF] 第三方url地址为：http://************
2025-07-02 14:11:03.273 +08:00 [INF] HTTP GET /api/EquipmentDoc/GetEquipmentList responded 200 in 24376.0715 ms
2025-07-02 14:11:03.274 +08:00 [INF] 【接口超时阀值预警】 [91ba203aad79aeb2a45b22a5a31b851a]接口/api/EquipmentDoc/GetEquipmentList,耗时:[24376]毫秒
2025-07-02 14:12:48.398 +08:00 [INF] 第三方url地址为：http://************
2025-07-02 14:13:19.257 +08:00 [INF] HTTP GET /api/XhEquipmentDoc/GetEquipmentListByMgroup responded 200 in 30906.9208 ms
2025-07-02 14:13:19.257 +08:00 [INF] 【接口超时阀值预警】 [46e76e053264b83145cd16592ba0704a]接口/api/XhEquipmentDoc/GetEquipmentListByMgroup,耗时:[30907]毫秒
2025-07-02 14:13:26.330 +08:00 [INF] 第三方url地址为：http://************
2025-07-02 14:13:56.392 +08:00 [INF] HTTP GET /api/XhEquipmentDoc/GetEquipmentListByMgroup responded 200 in 30101.2059 ms
2025-07-02 14:13:56.393 +08:00 [INF] 【接口超时阀值预警】 [d44aab52b4241bd2bb92f3e68fdd6138]接口/api/XhEquipmentDoc/GetEquipmentListByMgroup,耗时:[30101]毫秒
2025-07-02 14:14:22.637 +08:00 [INF] 第三方url地址为：http://************
2025-07-02 14:20:27.214 +08:00 [INF] ==>App Start..2025-07-02 14:20:27
2025-07-02 14:20:27.427 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-02 14:20:27.430 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-02 14:20:38.674 +08:00 [INF] ==>基础连接请求完成.
2025-07-02 14:20:39.341 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-02 14:20:39.742 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-02 14:20:40.313 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-02 14:20:40.316 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-02 14:20:40.653 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-02 14:20:40.828 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-02 14:20:40.907 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-02 14:20:41.439 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-02 14:20:41.439 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-02 14:20:44.090 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-02 14:20:48.206 +08:00 [INF] ==>初始化完成..
2025-07-02 14:20:48.226 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-02 14:20:48.227 +08:00 [INF] 设备启用任务
2025-07-02 14:20:48.228 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-02 14:20:49.889 +08:00 [INF] 【SQL执行耗时:1642.1097ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-02 14:20:50.043 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-02 14:20:57.102 +08:00 [INF] 第三方url地址为：http://************
2025-07-02 14:22:16.838 +08:00 [INF] HTTP GET /api/XhEquipmentDoc/GetEquipmentListByMgroup responded 200 in 84921.2487 ms
2025-07-02 14:22:16.842 +08:00 [INF] 【接口超时阀值预警】 [ed55cd61f14c673abe69d2d7868dc0c6]接口/api/XhEquipmentDoc/GetEquipmentListByMgroup,耗时:[84929]毫秒
2025-07-02 14:23:04.600 +08:00 [INF] HTTP GET /api/XhEquipmentDoc/GetEquipmentListByMgroup responded 401 in 12.7187 ms
2025-07-02 14:23:14.471 +08:00 [INF] 第三方url地址为：http://************
2025-07-02 14:38:18.121 +08:00 [INF] ==>App Start..2025-07-02 14:38:18
2025-07-02 14:38:18.314 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-02 14:38:18.318 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-02 14:38:19.880 +08:00 [INF] ==>基础连接请求完成.
2025-07-02 14:38:20.258 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-02 14:38:20.684 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-02 14:38:20.995 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-02 14:38:20.997 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-02 14:38:21.324 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-02 14:38:21.866 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-02 14:38:21.972 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-02 14:38:22.437 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-02 14:38:22.437 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-02 14:38:23.653 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-02 14:38:32.407 +08:00 [INF] ==>初始化完成..
2025-07-02 14:38:32.428 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-02 14:38:32.429 +08:00 [INF] 设备启用任务
2025-07-02 14:38:32.430 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-02 14:38:32.834 +08:00 [INF] 【SQL执行耗时:380.6991ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-02 14:38:32.981 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-02 14:38:32.998 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-02 14:38:33.000 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-02 14:38:33.000 +08:00 [INF] Hosting environment: Development
2025-07-02 14:38:33.001 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-02 14:39:32.321 +08:00 [INF] 第三方url地址为：http://************
2025-07-02 14:39:53.561 +08:00 [INF] HTTP GET /api/XhEquipmentDoc/GetEquipmentListByMgroup responded 200 in 26461.3598 ms
2025-07-02 14:39:53.565 +08:00 [INF] 【接口超时阀值预警】 [ad4d479dfd6be52d86d264c2c2783db4]接口/api/XhEquipmentDoc/GetEquipmentListByMgroup,耗时:[26468]毫秒
2025-07-02 14:40:28.351 +08:00 [INF] 第三方url地址为：http://************
2025-07-02 14:45:56.077 +08:00 [INF] ==>App Start..2025-07-02 14:45:56
2025-07-02 14:45:56.257 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-02 14:45:56.260 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-02 14:45:59.241 +08:00 [INF] ==>基础连接请求完成.
2025-07-02 14:45:59.859 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-02 14:46:00.219 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-02 14:46:00.571 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-02 14:46:00.579 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-02 14:46:00.919 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-02 14:46:01.261 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-02 14:46:01.318 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-02 14:46:01.735 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-02 14:46:01.735 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-02 14:46:03.028 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-02 14:46:08.210 +08:00 [INF] ==>初始化完成..
2025-07-02 14:46:08.231 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-02 14:46:08.233 +08:00 [INF] 设备启用任务
2025-07-02 14:46:08.233 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-02 14:46:09.873 +08:00 [INF] 【SQL执行耗时:1618.4271ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-02 14:46:10.184 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-02 14:46:15.265 +08:00 [INF] HTTP GET /favicon.ico responded 404 in 249.5216 ms
2025-07-02 14:46:38.595 +08:00 [INF] 第三方url地址为：http://************
2025-07-02 14:46:50.372 +08:00 [INF] 调用H07模块[/api/External/GetUserUnitInfo?moduleId=H82],耗时:1407ms
2025-07-02 14:47:19.415 +08:00 [INF] HTTP GET /api/XhEquipmentDoc/GetEquipmentListByMgroup responded 200 in 46569.7430 ms
2025-07-02 14:47:19.418 +08:00 [INF] 【接口超时阀值预警】 [85d6ae882e309f1caf3dd85695957b57]接口/api/XhEquipmentDoc/GetEquipmentListByMgroup,耗时:[46570]毫秒
2025-07-02 14:57:34.819 +08:00 [INF] ==>App Start..2025-07-02 14:57:34
2025-07-02 14:57:35.029 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-02 14:57:35.032 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-02 14:57:36.718 +08:00 [INF] ==>基础连接请求完成.
2025-07-02 14:57:37.102 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-02 14:57:37.597 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-02 14:57:37.926 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-02 14:57:37.932 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-02 14:57:38.278 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-02 14:57:38.856 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-02 14:57:38.981 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-02 14:57:39.474 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-02 14:57:39.474 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-02 14:57:40.563 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-02 14:57:42.946 +08:00 [INF] ==>初始化完成..
2025-07-02 14:57:42.970 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-02 14:57:42.973 +08:00 [INF] 设备启用任务
2025-07-02 14:57:42.974 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-02 14:57:43.385 +08:00 [INF] 【SQL执行耗时:380.1427ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-02 14:57:43.564 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-02 14:57:43.584 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-02 14:57:43.586 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-02 14:57:43.587 +08:00 [INF] Hosting environment: Development
2025-07-02 14:57:43.587 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-02 14:57:48.261 +08:00 [INF] HTTP GET /favicon.ico responded 404 in 189.1552 ms
2025-07-02 14:58:47.874 +08:00 [INF] 调用H07模块[/api/External/GetUserUnitInfo?moduleId=H82],耗时:570ms
2025-07-02 14:58:48.386 +08:00 [INF] 【SQL执行耗时:352.1032ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP"  WHERE (( "PGROUP_STATE" = :PGROUP_STATE0 ) AND  ("PGROUP_ID" IN ('PG520','PG022','PG006','PG005','PG095','PG013','PG055','PG054','PG001')) )  AND ( "HOSPITAL_ID" = :HOSPITAL_ID2 )  AND ( "LAB_ID" = :LAB_ID3 ) 
[Pars]:
[Name]::PGROUP_STATE0 [Value]:1 [Type]:String    
[Name]::HOSPITAL_ID2 [Value]:33A001 [Type]:String    
[Name]::LAB_ID3 [Value]:L001 [Type]:String    

2025-07-02 14:58:49.025 +08:00 [INF] 【SQL执行耗时:588.2648ms】

[Sql]:SELECT "AREA_ID","AREA_CODE","AREA_SORT","AREA_NAME","AREA_CLASS","AREA_PERSON","AREA_ADDRESS","AREA_TEL","SPELL_CODE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","STATE_FLAG","REMARK","HOSPITAL_ID","TAG_SIGN","AREA_ANAME","PACK_BAR_PREFIX" FROM "XH_SYS"."SYS6_INSPECTION_AREA"  WHERE  ("AREA_ID" IN ('A001','A016')) ORDER BY "AREA_ID" ASC

2025-07-02 14:58:49.439 +08:00 [INF] 【SQL执行耗时:341.9985ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP"  WHERE (( "MGROUP_STATE" = :MGROUP_STATE0 ) AND  ("MGROUP_ID" IN ('MG014','MG002','MG005','MG034','MG006')) )ORDER BY "MGROUP_SORT" ASC 
[Pars]:
[Name]::MGROUP_STATE0 [Value]:1 [Type]:String    

2025-07-02 14:58:50.102 +08:00 [INF] 【SQL执行耗时:622.3006ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP"  WHERE (( "MGROUP_STATE" = :MGROUP_STATE0 ) AND  ("MGROUP_ID" IN ('MG014')) )ORDER BY "MGROUP_SORT" ASC 
[Pars]:
[Name]::MGROUP_STATE0 [Value]:1 [Type]:String    

2025-07-02 14:58:50.188 +08:00 [INF] HTTP GET /api/CodeCustom/GetCustomDictLabTree responded 200 in 6250.0613 ms
2025-07-02 14:58:50.190 +08:00 [INF] 【接口超时阀值预警】 [4082355d43e7536063c81523709d761f]接口/api/CodeCustom/GetCustomDictLabTree,耗时:[6251]毫秒
2025-07-02 15:05:02.746 +08:00 [INF] ==>App Start..2025-07-02 15:05:02
2025-07-02 15:05:02.935 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-02 15:05:02.938 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-02 15:05:04.479 +08:00 [INF] ==>基础连接请求完成.
2025-07-02 15:05:04.849 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-02 15:05:05.234 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-02 15:05:05.537 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-02 15:05:05.543 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-02 15:05:05.879 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-02 15:05:06.318 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-02 15:05:06.411 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-02 15:05:06.837 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-02 15:05:06.837 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-02 15:05:07.904 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-02 15:05:10.214 +08:00 [INF] ==>初始化完成..
2025-07-02 15:05:10.235 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-02 15:05:10.237 +08:00 [INF] 设备启用任务
2025-07-02 15:05:10.238 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-02 15:05:10.636 +08:00 [INF] 【SQL执行耗时:375.7382ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-02 15:05:10.776 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-02 15:05:10.790 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-02 15:05:10.791 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-02 15:05:10.792 +08:00 [INF] Hosting environment: Development
2025-07-02 15:05:10.792 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-02 15:06:35.267 +08:00 [INF] 【SQL执行耗时:447.0312ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP"  WHERE (( "PGROUP_STATE" = :PGROUP_STATE0 ) AND  ("PGROUP_ID" IN ('PG520','PG022','PG006','PG005','PG095','PG013','PG055','PG054','PG001')) )  AND ( "HOSPITAL_ID" = :HOSPITAL_ID2 )  AND ( "LAB_ID" = :LAB_ID3 ) 
[Pars]:
[Name]::PGROUP_STATE0 [Value]:1 [Type]:String    
[Name]::HOSPITAL_ID2 [Value]:33A001 [Type]:String    
[Name]::LAB_ID3 [Value]:L001 [Type]:String    

2025-07-02 15:06:35.996 +08:00 [INF] 【SQL执行耗时:568.9478ms】

[Sql]:SELECT "LAB_ID","FIRST_RPERSON","LAB_NAME","LAB_ADDRESS","LAB_CODE","LAST_MTIME","LAB_SORT","LAB_RNAME","LAB_CLASS","LAB_PERSON","LINKMAN_TEL","REGISTER_CODE","LAB_LINKMAN","STATE_FLAG","REMARK","LAST_MPERSON","FIRST_RTIME","SPELL_CODE","LAB_TEL","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_LAB"  WHERE (( "STATE_FLAG" = :STATE_FLAG0 ) AND  ("LAB_ID" IN ('L001','LAB001')) )  AND ( "HOSPITAL_ID" = :HOSPITAL_ID2 ) 
[Pars]:
[Name]::STATE_FLAG0 [Value]:1 [Type]:String    
[Name]::HOSPITAL_ID2 [Value]:33A001 [Type]:String    

2025-07-02 15:06:36.414 +08:00 [INF] 【SQL执行耗时:355.1494ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP"  WHERE (( "MGROUP_STATE" = :MGROUP_STATE0 ) AND  ("MGROUP_ID" IN ('MG014','MG002','MG005','MG034','MG006')) )ORDER BY "MGROUP_SORT" ASC 
[Pars]:
[Name]::MGROUP_STATE0 [Value]:1 [Type]:String    

2025-07-02 15:06:36.483 +08:00 [INF] HTTP GET /api/CodeCustom/GetCustomDictLabTree responded 200 in 5698.9587 ms
2025-07-02 15:06:36.486 +08:00 [INF] 【接口超时阀值预警】 [620e2b75944ecf421f4f7f934a9cb6fe]接口/api/CodeCustom/GetCustomDictLabTree,耗时:[5707]毫秒
2025-07-02 15:07:59.452 +08:00 [INF] 【SQL执行耗时:640.0874ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP"  WHERE (( "PGROUP_STATE" = :PGROUP_STATE0 ) AND  ("PGROUP_ID" IN ('PG520','PG022','PG006','PG005','PG095','PG013','PG055','PG054','PG001')) )  AND ( "HOSPITAL_ID" = :HOSPITAL_ID2 )  AND ( "LAB_ID" = :LAB_ID3 ) 
[Pars]:
[Name]::PGROUP_STATE0 [Value]:1 [Type]:String    
[Name]::HOSPITAL_ID2 [Value]:33A001 [Type]:String    
[Name]::LAB_ID3 [Value]:L001 [Type]:String    

2025-07-02 15:07:59.981 +08:00 [INF] 【SQL执行耗时:381.204ms】

[Sql]:SELECT "LAB_ID","FIRST_RPERSON","LAB_NAME","LAB_ADDRESS","LAB_CODE","LAST_MTIME","LAB_SORT","LAB_RNAME","LAB_CLASS","LAB_PERSON","LINKMAN_TEL","REGISTER_CODE","LAB_LINKMAN","STATE_FLAG","REMARK","LAST_MPERSON","FIRST_RTIME","SPELL_CODE","LAB_TEL","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_LAB"  WHERE (( "STATE_FLAG" = :STATE_FLAG0 ) AND  ("LAB_ID" IN ('L001','LAB001')) )  AND ( "HOSPITAL_ID" = :HOSPITAL_ID2 ) 
[Pars]:
[Name]::STATE_FLAG0 [Value]:1 [Type]:String    
[Name]::HOSPITAL_ID2 [Value]:33A001 [Type]:String    

2025-07-02 15:08:00.625 +08:00 [INF] 【SQL执行耗时:602.7279ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP"  WHERE (( "MGROUP_STATE" = :MGROUP_STATE0 ) AND  ("MGROUP_ID" IN ('MG014','MG002','MG005','MG034','MG006')) )ORDER BY "MGROUP_SORT" ASC 
[Pars]:
[Name]::MGROUP_STATE0 [Value]:1 [Type]:String    

2025-07-02 15:08:00.666 +08:00 [INF] HTTP GET /api/CodeCustom/GetCustomDictLabTree responded 200 in 1976.1089 ms
2025-07-02 15:08:00.666 +08:00 [INF] 【接口超时阀值预警】 [4f41ab990dd9c7f5ca2d239fccef03f9]接口/api/CodeCustom/GetCustomDictLabTree,耗时:[1976]毫秒
2025-07-02 15:09:22.142 +08:00 [INF] ==>App Start..2025-07-02 15:09:22
2025-07-02 15:09:22.304 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-02 15:09:22.308 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-02 15:09:23.734 +08:00 [INF] ==>基础连接请求完成.
2025-07-02 15:09:24.100 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-02 15:09:24.503 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-02 15:09:24.807 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-02 15:09:24.809 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-02 15:09:25.148 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-02 15:09:25.604 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-02 15:09:25.690 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-02 15:09:26.108 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-02 15:09:26.108 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-02 15:09:27.004 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-02 15:09:29.192 +08:00 [INF] ==>初始化完成..
2025-07-02 15:09:29.212 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-02 15:09:29.215 +08:00 [INF] 设备启用任务
2025-07-02 15:09:29.216 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-02 15:09:29.626 +08:00 [INF] 【SQL执行耗时:388.4797ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-02 15:09:29.779 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-02 15:09:29.794 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-02 15:09:29.796 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-02 15:09:29.796 +08:00 [INF] Hosting environment: Development
2025-07-02 15:09:29.796 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
