﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using XH.H82.Models.Entities;

namespace XH.H82.Models.Entities.Transaction
{
    /// <summary>
    /// 设备事务使用记录
    /// </summary>

    [DBOwner("XH_OA")]
    [SugarTable("EMS_IMPLEMENT_INFO")]
    public class EMS_IMPLEMENT_INFO : BaseField
    {
        /// <summary>
        /// PK
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string IMPLEMENT_ID { get; set; }

        /// <summary>
        /// 医疗机构ID
        /// </summary>
        public string HOSPITAL_ID { get; set; }

        /// <summary>
        /// 设备ID
        /// </summary>
        public string EQUIPMENT_ID { get; set; }

        /// <summary>
        /// 使用周期;0 日/次  1月
        /// </summary>
        public string? IMPLEMENT_CYCLE { get; set; }

        /// <summary>
        /// 登记人员
        /// </summary>
        public string? IMPLEMENT_PERSON { get; set; }

        /// <summary>
        /// 使用时间
        /// </summary>
        public DateTime? IMPLEMENT_DATA { get; set; }

        /// <summary>
        /// 登记内容
        /// </summary>
        public string? IMPLEMENT_CONTEXT { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public string IMPLEMENT_STATE { get; set; } = "1";


        /// <summary>
        /// 数据来源   0 自己   1 监测   2 事务项
        /// </summary>
        [SugarColumn(IsIgnore = true)] 
        public string IMPLEMENT_SOURCE { get; set; } = "0";

    }

}
