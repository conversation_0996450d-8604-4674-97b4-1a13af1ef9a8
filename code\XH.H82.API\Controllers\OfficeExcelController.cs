﻿using EasyCaching.Core;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using XH.LAB.UTILS.Interface;

namespace XH.H82.API.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize]
    public class OfficeExcelController : ControllerBase
    {
        private readonly IH115OnlyOfficeService _h115OnlyOfficeService;

        public OfficeExcelController(IH115OnlyOfficeService h115OnlyOfficeService)
        {
            _h115OnlyOfficeService = h115OnlyOfficeService;
        }


        /// <summary>
        /// H115 GetLabPGroupList  接口
        /// </summary>
        /// <param name="LAB_ID">科室ID</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetLabPGroupList(string LAB_ID)
        {
            return Ok(_h115OnlyOfficeService.GetLabPGroupList(LAB_ID));
        }


        /// <summary>
        /// H115 GetTemplateList 接口
        /// </summary>
        /// <param name="HOSPITAL_ID">机构ID</param>
        /// <param name="LAB_ID">科室ID</param>
        /// <param name="AREA_ID">院区ID</param>
        /// <param name="PGROUP_ID">检验专业组ID</param>
        /// <param name="LAB_PGROUP_TYPE">专业组科室分类，0科室，1专业组 ，默认1</param>
        /// <param name="STYLE_CLASS_CODE">模板分类code</param>
        /// <param name="DATA_ID">数据id</param>
        /// <param name="STYLE_NAME">模板名称</param>
        /// <param name="CATALOG_NAME">目录名称</param>
        /// <param name="STYLE_IDS">模板styleId集合 “，”分割</param>
        /// <remarks>STYLE_IDS = "123,1233,46555"</remarks>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetTemplateList(string HOSPITAL_ID, string LAB_ID, string? AREA_ID, string? PGROUP_ID, string? LAB_PGROUP_TYPE, string? STYLE_CLASS_CODE, string? DATA_ID, string? STYLE_NAME, string? CATALOG_NAME, string? STYLE_IDS, string? OFFICE_FLAG)
        {
            return Ok(_h115OnlyOfficeService.GetTemplateList(HOSPITAL_ID, LAB_ID, AREA_ID, PGROUP_ID, LAB_PGROUP_TYPE, STYLE_CLASS_CODE, DATA_ID, STYLE_NAME, CATALOG_NAME, STYLE_IDS, OFFICE_FLAG));
        }

        /// <summary>
        /// 获取上次页面访问记录
        /// </summary>
        /// <param name="labId">科室id</param>
        /// <param name="styleClassCode">分类code</param>
        /// <param name="flag">前端一个页面多个清单模板</param>
        /// <param name="loadMode">web传1</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetLastVisLocation(string labId, string styleClassCode, string flag, string loadMode = "1")
        {
            return Ok(_h115OnlyOfficeService.GetLastVisLocation(labId, styleClassCode, flag, loadMode));
        }

        /// <summary>
        /// 新增清单功能上次记录页面
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult AddVisLocation(VisLocationInput input)
        {
            return Ok(_h115OnlyOfficeService.AddVisLocation(new(input.labId, input.styleClassCode, input.loadMode, input.flag, input.locationInfo, input.extInfo)));
        }

        /// <summary>
        /// 添加访问记录模型
        /// </summary>
        /// <param name="labId">科室id</param>
        /// <param name="styleClassCode">分类code</param>
        /// <param name="loadMode">web 传1</param>
        /// <param name="flag">前端有需要可传</param>
        /// <param name="locationInfo">页面信息</param>
        /// <param name="extInfo">拓展信息</param>
        public record VisLocationInput(string labId, string styleClassCode, string loadMode, string? flag, string? locationInfo, string? extInfo);

    }

}
