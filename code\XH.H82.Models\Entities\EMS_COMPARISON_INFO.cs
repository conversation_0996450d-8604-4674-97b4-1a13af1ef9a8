﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using Elastic.Clients.Elasticsearch;

namespace XH.H82.Models.Entities
{
    [DBOwner("XH_OA")]
    public class EMS_COMPARISON_INFO : IBeRecordEvent
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string COMPARISON_ID { get; set; }
        public string HOSPITAL_ID { get; set; }
        public string COMPARISON_NO { get; set; }
        public string EQUIPMENT_ID { get; set; }
        public DateTime? COMPARISON_DATE { get; set; }
        public string COMPARISON_PERSON { get; set; }
        public string COMPARISON_OBJECT { get; set; }
        public string COMPARISON_RESULT { get; set; }
        public string COMPARISON_STATE { get; set; }
        public string RELATION_EVENT { get; set; }
        public string FIRST_RPERSON { get; set; }
        public DateTime? FIRST_RTIME { get; set; }
        public string LAST_MPERSON { get; set; }
        public DateTime? LAST_MTIME { get; set; }
        public string REMARK { get; set; }
        public string STATE { get; set; }
        public string GetId()
        {
            return COMPARISON_ID;
        }

        public (string Type, string TypeName) GetType()
        {
            return ("4", "比对");
        }
        public string GetEquipmentId()
        {
            return EQUIPMENT_ID;
        }
        public DateTime? GetRecordDate()
        {
            return COMPARISON_DATE;
        }
    }
}
