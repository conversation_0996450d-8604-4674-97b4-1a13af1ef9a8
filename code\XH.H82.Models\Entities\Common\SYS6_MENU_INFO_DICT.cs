﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;
using H.Utility.SqlSugarInfra;
using SqlSugar;

namespace XH.H82.Models.Entities.Common
{
    [DBOwner("XH_SYS")]
    [SugarTable("SYS6_MENU_INFO_DICT")]
    public class SYS6_MENU_INFO_DICT
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string MENU_NO { get; set; }
        public string? HOSPITAL_ID { get; set; }
        public string? MENU_CLASS_ID { get; set; }
        public string? MENU_SORT { get; set; }
        public string? MENU_CODE { get; set; }
        public string? SYS_MENU { get; set; }
        public string? MENU_LEVEL { get; set; }
        public string? PARENT_CODE { get; set; }
        public string? MENU_CLASS { get; set; }
        public string? MENU_NAME { get; set; }
        public string? MENU_ENAME { get; set; }
        public string? MENU_URL { get; set; }
        public string? MENU_CNAME { get; set; }
        public string? SHORTCUT_KEY { get; set; }
        public string? MENU_ICON { get; set; }
        public string? IF_RIGHT_MENU { get; set; }
        public string? RIGHT_MENU { get; set; }
        public string? RIGHT_MENU_SORT { get; set; }
        public string? INSPECT_ISSUE_FUNC { get; set; }
        public string? REPORT_PRINT_FUNC { get; set; }
        public string? AUXILIARY_FUNC { get; set; }
        public string? IF_TOOLBAR { get; set; }
        public string? TOOLBAR_SNAME { get; set; }
        public string? TOOLBAR_SORT { get; set; }
        public string? MENU_DESC { get; set; }
        public string? MENU_STATE { get; set; }
        public string? FIRST_RPERSON { get; set; }
        public string? FIRST_RTIME { get; set; }
        public string? LAST_MPERSON { get; set; }
        public string? LAST_MTIME { get; set; }
        public string? REMARK { get; set; }
        public string? BACKGROUND_COLOR { get; set; }
        public string? FONT_COLOR { get; set; }

    }
}
