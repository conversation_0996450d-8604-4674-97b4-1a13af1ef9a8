#region (c) 2022 杏和软件. All rights reserved.
// @Author: ch<PERSON><PERSON><PERSON><PERSON>
// @CreateRecordDict: 2022-11-03 11:18
// @LastModified: 2022-11-03 15:50
// @Des:程序入口配置文件

#endregion
//***********起步框架使用文档:https://kdocs.cn/l/cbQKvmV5YAnX***********
using System.Net;
using System.Reflection;
using AspNetCoreRateLimit;
using H.BASE;
using H.BASE.Infrastructure;
using H.BASE.SqlSugarInfra;
using H.Utility;
using H.Utility.CommonService;
using H.Utility.Middleware;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Serilog;
using Swashbuckle.AspNetCore.SwaggerUI;
using XH.H82.API.Extensions;
using XH.H82.API.Extensions.YARP;
using XH.H82.API.ScheduleJobs;
using XH.H82.Models;
using XH.H82.Models.SugarDbContext;
using XH.H82.Services.ScheduleJobs;
using XH.LAB.UTILS.Helpers;
using XH.LAB.UTILS.Middleware;

//启用全程序集扫描
AppSettingsProvider.ContextScanAll = true;
var baseConfig = new BaseConfigDto();
var _configuration = new ConfigurationBuilder()
    .SetBasePath(AppContext.BaseDirectory)
    .AddJsonFile("configs/appsettings.json", false, true)
#if DEBUG
    .AddJsonFile("configs/appsettings.Development.json", true, true)
#endif
    .Build();
var appBuiler = new AppInit(_configuration, (data) =>
    {
        Console.WriteLine(JsonConvert.SerializeObject(data,  Formatting.Indented));
    })
.Init(null, args)
.RegisterELK()
.RegisterAuthentication()
.RegisterCacheProvider()
.RegisterIpRateLimit()
.RegisterSqlSugarDBContext<SugarDbContext_Master, SugarDbContext_Slave, SugarDbContext_Master2, SugarDbContext_Slave2>(EnumSqlSugarModel.ScopeModel)
.ConfigWebHost()
.ConfigControllers()
.ConfigureContainer(new AutofacModuleRegister())
.ConfigureAutoMapper(typeof(MappingProfile))
.builder;


//swagger个性东西比较多,放出来自己控制
appBuiler.Services.AddSwaggerSetup();
appBuiler.Services.AddAutoMapper(c => c.AddMaps("XH.H82.Models"));
// #region 加密设置 暂未启用
//
// AppSettingsProvider.IsEncryptSensitiveField = true;
//
// #endregion






//资源路径前缀
//把routers的参数代理到S54
var routers = new List<string>() { "H82pdf", "S54", "s54" };

var proxyConfig = new ProxyConfig(_configuration, routers);

appBuiler.Services.AddCustomReverseProxy(proxyConfig);
#region 自定义定时任务

appBuiler.Services.Configure<FormOptions>(x =>
{
    x.MultipartBodyLengthLimit = 1024 * 1024 * 1024; // 设置为1GB
});
/////////////////////////Redis消息队列定时任务
var computerName = Dns.GetHostName();
var consumerName = AppSettingsProvider.CurrModuleId + "_" + computerName;
//本系统需要监听的缓存在本系统中的存放方式 [DISTRIBUTED|LOCAL] 按照业务系统自行选择
var cacheType = "DISTRIBUTED";
//消费者名称,每个服务实例指定一个ID,用于记录消息被哪个服务实例消费,无其他用途 
AppSettingsProvider.consumerName = consumerName;//模块ID+机器名称
//如果本应用缓存使用分布式缓存,则所有的服务实例可以设置为同一个,通常为模块ID
//如果本应用缓存存在实例本地,如memeory chche,本地文件,静态变量等 中,则需要为每个服务实例指定不同的消费组.以便各自消费各自注册的消息的队列
//分布式=使用模块id作为消费组ID  单点缓存=直接使用消费者ID作为消费者ID
AppSettingsProvider.consumerGroupName = cacheType == "DISTRIBUTED" ? AppSettingsProvider.CurrModuleId : consumerName;
//本系统需要监听消息队列key数组,如果没有,请务必留空以免占用不必要的资源,key值由公司统一约定,此处必须保证填写正确
//!!!todo:注意,此处注册的两个队列仅作演示,生产过程中请务必按照实际需求注册和清理 消息队列名称由公司统一约定,你参阅文档
//AppSettingsProvider.listenStreamKeys = new string[] { "XH:BASE:STREAM:LIS_CHARGE_ITEM","XH:BASE:STREAM:LIS_TEST_ITEM" };
AppSettingsProvider.listenStreamKeys = new string[] { };
//appBuiler.Services.AddHostedService<RedisStreamListener>();
///////////////////////////
//定时清理本地日志为典型的定时任务场景,放出来方便大家参考
appBuiler.Services.AddHostedService<AutoClearLogFile>(); 
//appBuiler.Services.AddMemoryCache();
//更多定时任务在此处添加
#region 定时任务
//如果是协同库操作，涉及定时执行sql语句的服务锁的任务需要屏蔽
if (AppSettingsProvider.SynergySign == "1")
{
    appBuiler.Services.AddHostedService<EnableDataSync>();
    if (_configuration.GetSection("IsUseSmblSync").Value == "1")
    {
        appBuiler.Services.AddHostedService<IoTDeviceSync>();
        appBuiler.Services.AddHostedService<EvnDeviceSync>();
        appBuiler.Services.AddHostedService<LampDeviceSync>();
        appBuiler.Services.AddHostedService<WaterDeviceSync>();
        appBuiler.Services.AddHostedService<MonitorHoursDataByDaySync>();
    }
    if(_configuration.GetSection("IsAutoRefresh").Value == "1"){
        appBuiler.Services.AddHostedService<WarnInfoBackGroudService>();
    }
}

#endregion
#endregion

var app = appBuiler.Build();

#region 管道和中间件配置
var OpenSwagger = _configuration["OpenSwagger"] ?? "0";
if (app.Environment.IsDevelopment() || OpenSwagger == "1")
{
    app.UseSwagger();
    app.UseSwaggerUI(options =>
    {
        options.SwaggerEndpoint("/swagger/XH.H82.API/swagger.json", "XH.H82.API");
        options.SwaggerEndpoint("/swagger/DEMO/swagger.json", "DEMO");
        options.RoutePrefix = string.Empty;
        options.DocExpansion(DocExpansion.None); //->修改界面打开时自动折叠
        options.IndexStream = (Func<Stream>)(() => Assembly.GetExecutingAssembly()
            .GetManifestResourceStream("XH.H82.API.swagger.html"));
    });
}
//返回头标记xh_trace_id 以便在elk中查询整个访问链路
app.UseMiddleware<TraceResposeHeadMiddleware>();
//app.UseMiddleware<AddrResposeHeadMiddleware>();
app.UseMiddleware<LabTimeWatchMiddleware>();
app.UseSerilogRequestLogging(options =>
{
    options.EnrichDiagnosticContext = SerilogHelper.EnrichFromRequest;
});

//工具箱配置自动加载
// if (!File.Exists(OfficeHelper.PathCombine(AppContext.BaseDirectory,"工具箱已经配置.txt")))
// {
//    var  infraService  = app.Services.GetRequiredService<InfraServices>();
//    infraService.InitTFboxSettings();
//    File.Create(OfficeHelper.PathCombine(AppContext.BaseDirectory, "工具箱已经配置.txt"));
// }

//数据模型检查
// var checkOrm = new OrmCheckProvider<SugarDbContext_Master>(app.Services.GetRequiredService<ISqlSugarUow<SugarDbContext_Master>>());
//
// var checkOrmResult = await checkOrm.CheckDllOrms("XH.H82.Models","Entities");
//
// Console.WriteLine(JsonConvert.SerializeObject(checkOrmResult,Formatting.Indented));
//资源路径前缀
Console.WriteLine(app.Services.GetRequiredService<IConfiguration>().GetSection("UrlModuleS01").Value);
//Task.Factory.StartNew(() =>
//{
//    var _infraService = app.Services.GetRequiredService<InfraServices>();

//    var InitTFBoxResult = _infraService.InitTFboxSettings();
//    //var InitModuleRuleFormResult = _infraService.InitModuleRuleFormExcel();
//    Console.WriteLine(InitTFBoxResult == "1"? "工具箱导入成功" : "工具箱导入失败");
//    //Console.WriteLine(InitModuleRuleFormResult == "1" ? "系统设置导入成功" : "系统设置导入失败");
//});
//把routers的参数代理到S54
//app.UseMiddleware<XH.LAB.UTILS.Middleware.CustomMatchFileResourceMiddleware>("S54", routers);

var disableRateLimit = _configuration["DisableRateLimit"] ?? "1";
if (disableRateLimit != "1")
{
    app.UseIpRateLimiting();
}

//全局异常中间件
//app.UseMiddleware<GlobalExceptionMiddleware>();
app.UseMiddleware<LabExceptionMiddleware>();
#endregion
//关键字过滤中间件
//app.UseMiddleware<SQLInjectMiddleware>();
//http自动引导到https
//app.UseHttpsRedirection();
//以下两项顺序必须正确
app.UseRouting();


app.UseAuthentication(); //认证
app.UseAuthorization(); //授权中间件

app.UseEndpoints(endpoints =>
{
    endpoints.MapReverseProxy();
});
app.UseDateCheck();

app.MapControllers();


Log.Information("==>初始化完成..");
app.Run();

