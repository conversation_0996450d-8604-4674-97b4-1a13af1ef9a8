﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XH.H82.Models.Dtos;
using XH.H82.Models.Entities;
using XH.H82.Models.Entities.Common;
using H.Utility;

namespace XH.H82.IServices
{
    public interface IEquipmentDocService
    {
        //NewEquipClassTreeDto GetEquipmentClassList(string userNo, string hospitalId, string equipmentNo, string labId,string areaId,string ifHide);

        //NewOATreeDto GetEquipmentListByMgroup(string userNo, string hospitalId, string equipmentNo,string labId,string areaId,string ifHide);
        //ResultDto HideEquipment(string equipmentId, string userName);
        List<EquipmentInfoDto> GetEquipmentList(string areaId, string userNo, string hospitalId, string state, string type, string mgroupId, string keyWord, string labId, string pgroupId);
        EMS_EQUIPMENT_INFO GetEquipmentInfo(string equipmentId);
        ResultDto SaveEquipmentInfo(EMS_EQUIPMENT_INFO record);

        ResultDto DeleteEquipmentInfo(string equipmentId, string userName);
        List<SYS6_COMPANY_INFO> GetCompanyList(string type, string serviceArea, string keyword);
        List<SYS6_COMPANY_CONTACT> GetCompanyContactList(string companyId, string equipmentId, string contactType, string contactState, string keyword);
        ResultDto UpdateCompanyContact(List<SYS6_COMPANY_CONTACT> record, string equipmentId, string contactType, string userName);
        EMS_ENVI_REQUIRE_INFO GetEnviRequireInfo(string equipmentId);
        EMS_ENVI_REQUIRE_INFO SaveEnviRequireInfo(EMS_ENVI_REQUIRE_INFO record);
        EMS_SUBSCRIBE_INFO GetSubscribeInfo(string equipmentId);
        EMS_SUBSCRIBE_INFO SaveSubscribeInfo(EMS_SUBSCRIBE_INFO record);
        EMS_PURCHASE_INFO GetPurchaseInfo(string equipmentId);
        EMS_PURCHASE_INFO SavePurchaseInfo(EMS_PURCHASE_INFO record);
        EMS_INSTALL_INFO GetInstallInfo(string equipmentId);
        EMS_INSTALL_INFO SaveInstallInfo(EMS_INSTALL_INFO record);
        EMS_UNPACK_INFO GetUnpackInfo(string equipmentId);
        EMS_UNPACK_INFO SaveUnpackInfo(EMS_UNPACK_INFO record);
        List<EMS_PARTS_INFO> GetPartsList(string equipmentId);
        EMS_PARTS_INFO SavePartsInfo(EMS_PARTS_INFO record);
        ResultDto DeletePartsInfo(string partsId, string userName);
        List<EMS_TRAIN_INFO> GetTrainList(string equipmentId);
        EMS_TRAIN_INFO SaveTrainInfo(EMS_TRAIN_INFO record);
        ResultDto DeleteTrainInfo(string trainId, string userName);
        EMS_DEBUG_INFO GetDebugInfo(string equipmentId);
        EMS_DEBUG_INFO SaveDebugInfo(EMS_DEBUG_INFO record);
        List<EMS_AUTHORIZE_INFO> GetAuthorizeList(string equipmentId);
        EMS_AUTHORIZE_INFO SaveAuthorizeInfo(EMS_AUTHORIZE_INFO record);
        ResultDto DeleteAuthorizeInfo(string authorizeId, string userName);
        EMS_STARTUP_INFO GetStartupInfo(string equipmentId);
        EMS_STARTUP_INFO SaveStartupInfo(EMS_STARTUP_INFO record);
        List<EMS_START_STOP> GetStartStopList(string equipmentId);
        ResultDto SaveStartInfo(EMS_START_STOP record);
        EMS_SCRAP_INFO GetScrapInfo(string equipmentId);
        ResultDto SaveScrapInfo(EMS_SCRAP_INFO record);
        List<SYS6_COMPANY_CONTACT> GetContactInfoList(string equipmentId, string state, string supplier, string keyWord);
        SYS6_COMPANY_CONTACT SaveContactInfo(SYS6_COMPANY_CONTACT record);
        ResultDto DeleteContactInfo(string contactId, string userName);
        List<CardElementDto> GetCardElement(string cardType, List<string> equipmentId);
        ResultDto ForbidContactPerson(string equipment, string userName, SYS6_COMPANY_CONTACT record);
        List<BasicDataDto> GetCompanyClass();
        string CardPrintBase64(string dataXml);
        List<PgroupPipelineDto> GetPipelineList(string pgroupId, string labId, string areaId, string userNo);
        ResultDto ExportDeviceList(List<EMS_EQUIPMENT_INFO> record, string exportType);
        List<EMS_EQUIPMENT_INFO> FindBaseEquipmentInfo(List<string> equipmentIds);


        List<EquipmentInfoDto> GetEquipments(string userNo, string labId, string? areaId, string? mgroupId, string? pgroupId, string? state, string? type, string? keyWord);


        //int CreatCardTemplate(string equipmentId);
        //List<LIS5_BILLS_DICT> GetCardTemplate(string labId, string hospitalId);
        //List<SYS_QUERY_FIELD> GetCardInformation(string equipmentId);
        //List<SYS_QUERY_FIELD> GetSelectInformation(string equipmentId);
        //int AddSelectInformation(SYS_QUERY_FIELD record, string type, string equipmentId);
        List<EMS_EQUIPMENT_INFO> GetEquipmentsBycondition(
            bool isInclude,
            string labId, 
            string areaId, 
            string? mgroupId,
            string pgroupId, 
            string state, 
            string type, 
            string keyWord,
            string smblFlag
        );
    }
}
