﻿using H.Utility;
using XH.H82.Models.Entities;

namespace XH.H82.Models.Dtos.Verification;

public class VerificationDto
{
    public static EMS_VERIFICATION_INFO CreateAddModule(string equipmentId, string? hospitalId  , VerificationInput input)
    {
        var record = new EMS_VERIFICATION_INFO();
        record.EQUIPMENT_ID = equipmentId;
        record.VERIFICATION_ID = IDGenHelper.CreateGuid();
        record.HOSPITAL_ID = hospitalId ?? "H0000";
        record.VERIFICATION_DATE = input.VERIFICATION_DATE;
        record.VERIFICATION_PERSON = input.VERIFICATION_PERSON;
        if (record.VERIFICATION_PERSON.IsNotNullOrEmpty() && record.VERIFICATION_PERSON.Contains("_"))
        {
            record.VERIFICATION_PERSON = record.VERIFICATION_PERSON.Split('_')[1];
        }
        record.VERIFICATION_RESULT = input.VERIFICATION_RESULT;
        record.REMARK = input.REMARK;
        record.VERIFICATION_STATE = "1";
        record.STATE = "已执行";
        return record;
    }
}


/// <summary>
/// 性能验证表单入参模型
/// </summary>
/// <param name="VERIFICATION_DATE">验证日期</param>
/// <param name="VERIFICATION_PERSON">验证人员</param>
/// <param name="VERIFICATION_RESULT">验证结果</param>
/// <param name="REMARK">备注</param>
public record VerificationInput(
    DateTime? VERIFICATION_DATE,
    string? VERIFICATION_PERSON,
    string? VERIFICATION_RESULT,
    string? REMARK);