﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.Entities
{
    public class SCRAP_STOP_FORM
    {
        public string EQUIPMENT_NAME { get; set; }
        public string EQUIPMENT_CODE { get; set; }
        public string EQUIPMENT_MODEL { get; set; }
        public string EQUIPMENT_CLASS { get; set; }
        public string PGROUP_NAME { get; set; }
        public string MANUFACTURER { get; set; }
        public string DEALER { get; set; }
        public string EQ_OUT_TIME { get; set; }
        public string EQ_IN_TIME { get; set; }
        public string ENABLE_TIME { get; set; }
        public string APPLY_TYPE { get; set; }
        public string OPER_PERSON { get; set; }
        public string OPER_TIME { get; set; }
        public string SCRAP_CAUSE { get; set; }
        public string EXAMINE_PERSON { get; set; }
        public string EXAMINE_DATE { get; set; }
        public string EXAMINE_OPINION { get; set; }
        public string APPROVE_OPINION { get; set; }
        public string APPROVE_PERSON { get; set; }
        public string APPROVE_DATE { get; set; }
    }
}
