using H.Utility.SqlSugarInfra;
using Microsoft.EntityFrameworkCore;
using SqlSugar;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace XH.H82.Models.Entities.Tim
{
    [DBOwner("XH_OA")]
    public class TIM_WORK_EXEC_LIST
    {
        /// <summary>
        /// 
        /// </summary>
        [Key]
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        [Column("WEXEC_ID")]
        [Required(ErrorMessage = "不允许为空")]

        [StringLength(50, ErrorMessage = "WEXEC_ID长度不能超出50字符")]
        [Unicode(false)]
        public string WEXEC_ID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("LAB_ID")]
        [Required(ErrorMessage = "不允许为空")]

        [StringLength(20, ErrorMessage = "LAB_ID长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string LAB_ID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("ISSUE_STATE")]
        [StringLength(20, ErrorMessage = "ISSUE_STATE长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string ISSUE_STATE { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("FIRST_RPERSON")]
        [StringLength(50, ErrorMessage = "FIRST_RPERSON长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string FIRST_RPERSON { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("LAST_MTIME")]
        [Unicode(false)]
        public DateTime? LAST_MTIME { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("UNIT_ID")]
        [Required(ErrorMessage = "不允许为空")]

        [StringLength(20, ErrorMessage = "UNIT_ID长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string UNIT_ID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("WORK_MAINID")]
        [Required(ErrorMessage = "不允许为空")]

        [StringLength(50, ErrorMessage = "WORK_MAINID长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string WORK_MAINID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("FORM_VER_MAIN_ID")]
        [Required(ErrorMessage = "不允许为空")]

        [StringLength(50, ErrorMessage = "FORM_VER_MAIN_ID长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string FORM_VER_MAIN_ID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("WORK_ID")]
        [Required(ErrorMessage = "不允许为空")]

        [StringLength(20, ErrorMessage = "WORK_ID长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string WORK_ID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("RECORD_TIME")]
        [Unicode(false)]
        public DateTime? RECORD_TIME { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("FORM_ID")]
        [Required(ErrorMessage = "不允许为空")]

        [StringLength(20, ErrorMessage = "FORM_ID长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string FORM_ID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("WEXEC_PERSON")]
        [StringLength(50, ErrorMessage = "WEXEC_PERSON长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string WEXEC_PERSON { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("WEXEC_RESULT")]
        [StringLength(500, ErrorMessage = "WEXEC_RESULT长度不能超出500字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string WEXEC_RESULT { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("ISSUE_TIME")]
        [Unicode(false)]
        public DateTime? ISSUE_TIME { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("IF_MUST")]
        [StringLength(10, ErrorMessage = "IF_MUST长度不能超出10字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string IF_MUST { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("MODULE_ID")]
        [Required(ErrorMessage = "不允许为空")]

        [StringLength(20, ErrorMessage = "MODULE_ID长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string MODULE_ID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("WEXEC_TIME")]
        [Unicode(false)]
        public DateTime? WEXEC_TIME { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("RECORD_PERSON")]
        [StringLength(50, ErrorMessage = "RECORD_PERSON长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string RECORD_PERSON { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("ISSUE_ID")]
        [StringLength(50, ErrorMessage = "ISSUE_ID长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string ISSUE_ID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("REMARK")]
        [StringLength(200, ErrorMessage = "REMARK长度不能超出200字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string REMARK { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("WEXEC_DATE")]
        [Unicode(false)]
        public DateTime? WEXEC_DATE { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("WEXEC_COMPUTER")]
        [StringLength(50, ErrorMessage = "WEXEC_COMPUTER长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string WEXEC_COMPUTER { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("ISSUE_PERSON")]
        [StringLength(50, ErrorMessage = "ISSUE_PERSON长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string ISSUE_PERSON { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("WEXEC_STATE")]
        [StringLength(20, ErrorMessage = "WEXEC_STATE长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string WEXEC_STATE { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("FIRST_RTIME")]
        [Unicode(false)]
        public DateTime? FIRST_RTIME { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("LAST_MPERSON")]
        [StringLength(50, ErrorMessage = "LAST_MPERSON长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string LAST_MPERSON { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("WORK_MAINNAME")]
        [StringLength(50, ErrorMessage = "WORK_MAINNAME长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string WORK_MAINNAME { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("PGROUP_ID")]
        [Required(ErrorMessage = "不允许为空")]

        [StringLength(20, ErrorMessage = "PGROUP_ID长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string PGROUP_ID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("HOSPITAL_ID")]
        [Required(ErrorMessage = "不允许为空")]

        [StringLength(20, ErrorMessage = "HOSPITAL_ID长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string HOSPITAL_ID { get; set; }

        /// <summary>
        /// 指定执行人员
        /// </summary>
		[Column("DESIGN_WEXEC_PERSON")]
        [StringLength(50, ErrorMessage = "DESIGN_WEXEC_PERSON长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string DESIGN_WEXEC_PERSON { get; set; }
    }
}
