﻿using H.IRepository;
using H.Utility.Helper;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using NPOI.HSSF.Record;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using XH.H82.IServices;
using XH.H82.Models;
using XH.H82.Models.Dtos;
using XH.H82.Models.Entities;
using XH.H82.Models.Entities.Common;
using H.Utility;
using H.BASE.SqlSugarInfra.Uow;
using XH.H82.Models.SugarDbContext;
using Microsoft.Extensions.Logging;
using XH.LAB.UTILS.Models;
using XH.H82.Models.Dtos.Base;
using AutoMapper;
using SYS6_HOSPITAL_INFO = XH.LAB.UTILS.Models.SYS6_HOSPITAL_INFO;

namespace XH.H82.Services
{
    public class ModuleLabGroupService : IModuleLabGroupService
    {
        private readonly IMapper _mapper;
        private readonly IHostingEnvironment _hostingEnvironment;
        private readonly ISqlSugarUow<SugarDbContext_Master> _sqlSugarUow;
        private readonly ISystemService _systemService;
        public ModuleLabGroupService(IHostingEnvironment hostingEnvironment, ISqlSugarUow<SugarDbContext_Master> sqlSugarUow, IMapper mapper, ISystemService systemService)
        {
            _hostingEnvironment = hostingEnvironment;
            _sqlSugarUow = sqlSugarUow;
            _mapper = mapper;
            _systemService = systemService;
            //ExecutingChangeSqlHelper.ExecutingChangeSql(_sqlSugarUow);
        }

        public string GetModuleInfoByModuleId(string strHospitalId, string strModuleId)
        {
            string strUrl = string.Empty;
            SYS6_SOFT_MODULE_INFO SYS6_SOFT_MODULE_INFO = _sqlSugarUow.Db.Queryable<SYS6_SOFT_MODULE_INFO>()
                .Where(p => p.MODULE_STATE == "1" && p.MODULE_CODE == strModuleId)
                .First();
            // List<LIS5_SERVER_INFO> lis5_server_info = _oa.GetRepository<LIS5_SERVER_INFO>().Find(p => p.SERVER_TYPE == "应用类").ToList();
            if (SYS6_SOFT_MODULE_INFO != null)
            {
                SYS6_SERVER_INFO lis5serverinfo = _sqlSugarUow.Db.Queryable<SYS6_SERVER_INFO>()
                    .Where(P => P.HOSPITAL_ID == strHospitalId && P.SERVER_ID == SYS6_SOFT_MODULE_INFO.IIS_SERVER)
                    .First();
                if (lis5serverinfo != null)
                {
                    strUrl = lis5serverinfo.SERVER_IP + ":" + SYS6_SOFT_MODULE_INFO.PROGRAM_URL;
                }
            }
            return strUrl;
        }

        /// <summary>
        /// 获取人员对应科室信息
        /// </summary>
        /// <param name="strUserNo"></param>
        /// <returns></returns>
        public List<SYS6_INSPECTION_LAB> GetLisInspectionLabInfo(string strUserNo, string strHospitalId)
        {
            List<SYS6_INSPECTION_LAB> lis5inspectionpgroup = new List<SYS6_INSPECTION_LAB>();
            try
            {
                lis5inspectionpgroup = _sqlSugarUow.Db.Queryable<SYS6_USER_PGROUP>()
                    .LeftJoin<SYS6_INSPECTION_LAB>((a, b) => a.LAB_ID == b.LAB_ID)
                    .Where((a, b) => a.USER_NO == strUserNo && b.HOSPITAL_ID == strHospitalId)
                    .Select((a, b) => b)
                    .ToList();
            }
            catch (Exception ex)
            {
                throw new BizException("获取人员对应科室信息失败:" + ex.Message);
            }
            return lis5inspectionpgroup;
        }

        /// <summary>
        /// 获取人员对应分组
        /// </summary>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     
        /// <param name="strUserNo"></param>
        /// <returns></returns>
        public List<SYS6_INSPECTION_PGROUP> GetLisInspectionPgroupInfo(string strUserNo, string strHospitalId)
        {

            List<SYS6_INSPECTION_PGROUP> sys5inspectionpgroup = new List<SYS6_INSPECTION_PGROUP>();
            try
            {
                sys5inspectionpgroup = _sqlSugarUow.Db.Queryable<SYS6_USER>()
                    .LeftJoin<SYS6_INSPECTION_PGROUP>((a, b) => a.DEPT_CODE == b.PGROUP_ID)
                    .Where((a, b) => a.USER_NO == strUserNo && b.HOSPITAL_ID == strHospitalId)
                    .Select((a, b) => b)
                    .ToList();
            }
            catch (Exception ex)
            {
                throw new BizException("获取人员对应科室信息失败:" + ex.Message);
            }
            return sys5inspectionpgroup;
        }
        public List<SYS6_INSPECTION_MGROUP> GetLisInspectionMgroupInfo(string strUserNo, string strHospitalId)
        {
            var list = _sqlSugarUow.Db.Queryable<SYS6_USER>()
                .LeftJoin<SYS6_INSPECTION_PGROUP>((a, b) => a.DEPT_CODE == b.PGROUP_ID)
                .LeftJoin<SYS6_INSPECTION_MGROUP>((a, b, c) => b.MGROUP_ID == c.MGROUP_ID)
                .Where((a, b, c) => a.USER_NO == strUserNo && b.HOSPITAL_ID == strHospitalId && c.HOSPITAL_ID == strHospitalId)
                .Select((a, b, c) => c)
                .ToList();
            return list;
        }

        public sys6UserDto GetUserInfo(string logId, string password = "")
        {
            //var userInfo = Sm3PasswordCheck(logId, password);
            var userInfo = _systemService.Sm2PasswordCheck(logId, password);
            return userInfo;
        }
        private sys6UserDto Sm3PasswordCheck(string logId , string password = "")
        {
            sys6UserDto userInfo = null;

            var user = _sqlSugarUow.Db.Queryable<SYS6_USER>()
                .Where(p => p.LOGID == logId)
                .WhereIF(password.IsNotNullOrEmpty(), p => p.PWD_BS == password)
                .First();
            if (user != null)
            {
                userInfo = _mapper.Map<sys6UserDto>(user);
                var labId = _sqlSugarUow.Db.Queryable<SYS6_USER>()
                    .Where(p => p.USER_NO == userInfo.USER_NO)
                    .First().LAB_ID;
                var labInfo = _sqlSugarUow.Db.Queryable<SYS6_INSPECTION_LAB>()
                    .Where(p => p.LAB_ID == labId)
                    .First();
                if (labInfo != null)
                {
                    userInfo.LAB_ID = labId;
                    userInfo.LAB_NAME = labInfo.LAB_NAME;
                    userInfo.HOSPITAL_NAME = _sqlSugarUow.Db.Queryable<SYS6_HOSPITAL_INFO>()
                        .Where(p => p.HOSPITAL_ID == userInfo.HOSPITAL_ID).First().HOSPITAL_CNAME;
                }
            }
            return userInfo;
        }
        
    }
}
