﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using H.Utility.SqlSugarInfra;
using SqlSugar;

namespace XH.H82.Models.Entities.THS
{
    /// <summary>
    /// 单元信息表
    /// </summary>
    [DBOwner("XH_SYS")]
    [Table("THS_UNIT_INFO")]
    [SugarTable("THS_UNIT_INFO")]
    public class THS_UNIT_INFO
    {
        /// <summary>
        /// 管理单元ID
        /// </summary>
        [Key]
        [SugarColumn(IsPrimaryKey = true)]
        public string? UNIT_ID { get; set; }
        /// <summary>
        /// 医疗机构ID
        /// </summary>
        public string? HOSPITAL_ID { get; set; }
        /// <summary>
        /// 排序号
        /// </summary>
        public string? UNIT_SORT { get; set; }
        [SugarColumn(IsIgnore = true)]
        /// <summary>
        /// 负责人
        /// </summary>
        public string? UNIT_PERSON { get; set; }
        [SugarColumn(IsIgnore = true)]
        /// <summary>
        /// 联系电话
        /// </summary>
        public string? UNIT_TEL { get; set; }
        /// <summary>
        /// 首次登记人
        /// </summary>
        public string? FIRST_RPERSON { get; set; }
        /// <summary>
        /// 首次登记时间
        /// </summary>
        public DateTime? FIRST_RTIME { get; set; }
        /// <summary>
        /// 最后修改人员
        /// </summary>
        public string? LAST_MPERSON { get; set; }
        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime? LAST_MTIME { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string? REMARK { get; set; }
    }
}
