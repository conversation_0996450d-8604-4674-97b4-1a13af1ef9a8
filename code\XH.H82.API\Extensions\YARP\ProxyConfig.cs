﻿using Yarp.ReverseProxy.Configuration;

namespace XH.H82.API.Extensions.YARP
{
    public class ProxyConfig
    {

        IConfiguration _configuration { get; set; }

        private string address { get; set; }

        public List<string> Routes { get; set; } = new List<string>();

        public ProxyConfig(IConfiguration configuration, List<string> routes)
        {
            _configuration = configuration;
            address = _configuration["S54"];
            if (address is null)
            {
                address = "";
            }
            Routes = routes;
        }

        public List<RouteConfig> GetRoutes()
        {
            if (Routes.Count() == 0)
            {
                return new List<RouteConfig>();
            }
            else
            {
                var routes = new List<RouteConfig>();
                for (var i = 0; i < Routes.Count(); i++)
                {
                    routes.Add(new RouteConfig()
                    {
                        RouteId = $"{Routes[i]}-{i}",
                        Match = new RouteMatch
                        {
                            Path = $"/{Routes[i]}/" + "{**catch-all}",
                        },
                        ClusterId = $"{Routes[i]}-{i}"
                    });
                }
                return routes;

            }
        }


        public List<ClusterConfig> GetClusters()
        {

            if (Routes.Count() == 0)
            {
                return new List<ClusterConfig>();
            }
            else
            {

                var clusters = new List<ClusterConfig>();
                for (var i = 0; i < Routes.Count(); i++)
                {
                    clusters.Add(new()
                    {
                        ClusterId = $"{Routes[i]}-{i}",
                        Destinations = new Dictionary<string, DestinationConfig>
                        {
                            ["destination1"] = new DestinationConfig
                            {
                                Address = address
                            }
                        }
                    });
                }
                return clusters;
            }
        }
    }

}
