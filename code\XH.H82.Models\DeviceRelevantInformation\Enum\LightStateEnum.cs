﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.DeviceRelevantInformation.Enum
{

    /// <summary>
    /// 亮灯状态
    /// </summary>
    public enum LightStateEnum
    {
        /// <summary>
        /// 熄灯
        /// </summary>
        [Description("熄灯")]
        lightsOut = 0,
        /// <summary>
        /// 亮灯
        /// </summary>
        [Description("亮灯")]
        Lighting = 1,

    }

    /// <summary>
    /// 墨水屏幕线上状态
    /// </summary>
    public enum LineStateEnum
    {
        /// <summary>
        /// 离线
        /// </summary>
        [Description("离线")]
        OutLine = 0,
        /// <summary>
        /// 在线
        /// </summary>
        [Description("在线")]
        OnLine = 1,

    }

    /// <summary>
    /// 墨水屏与设备的绑定状态
    /// </summary>
    public enum BindState
    {
        /// <summary>
        /// 未绑定
        /// </summary>
        UnBind = 0,
        /// <summary>
        /// 已绑定
        /// </summary>
        Bind = 1,

    }
}
