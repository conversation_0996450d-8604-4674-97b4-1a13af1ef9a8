﻿using Spectre.Console;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XH.H82.Models.DeviceRelevantInformation.Enum;

namespace XH.H82.Models.DeviceRelevantInformation.Dto
{
    /// <summary>
    /// 预警信息模型
    /// </summary>
    public class EquipmentWarnInfoDto
    {
        /// <summary>
        /// 设备故障列表
        /// </summary>
        public List<EquipmentStatusSummary> FaultRecords { get; set; } = new List<EquipmentStatusSummary>();
        /// <summary>
        /// 需要提醒的保养设备列表
        /// </summary>
        public List<EquipmentStatusSummary> MaintenanceRecords { get; set; } = new List<EquipmentStatusSummary>();
        /// <summary>
        ///  需要提醒的校准设备列表
        /// </summary>
        public List<EquipmentStatusSummary> CorrectRecords { get; set; } = new List<EquipmentStatusSummary>();
        /// <summary>
        /// 需要提醒的比对设备列表
        /// </summary>
        public List<EquipmentStatusSummary> ComparisonRecords { get; set; } = new List<EquipmentStatusSummary>();
    }

    /// <summary>
    /// 提醒信息摘要
    /// </summary>
    public class EquipmentStatusSummary
    {
        /// <summary>
        /// 设备当前预警状态(0故障、1需要保养、2需要校准、需要比对)
        /// </summary>
        public EquipmentSummaryEnum Summary { get; set; }
        /// <summary>
        /// 设备ID
        /// </summary>
        public string EquipmentId { get; set; }
        /// <summary>
        /// 设备名称
        /// </summary>
        public string EquipmentName { get; set; }
        /// <summary>
        /// 设备代号
        /// </summary>
        public string EquipmentCode { get; set; }

        /// <summary>
        /// 设备型号
        /// </summary>
        public string EquipmentModel { get; set; }

        /// <summary>
        /// 专业组Id
        /// </summary>
        public string? GroupId { get; set; }

    }
}
