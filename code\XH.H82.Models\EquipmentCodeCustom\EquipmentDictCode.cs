﻿using System.ComponentModel;

namespace XH.H82.Models.EquipmentCodeCustom;

/// <summary>
/// 设备自定义模板选项模型
/// </summary>
public class EquipmentDictCode
{
    [Description("机构")]
    /// <summary>
    /// 机构名称
    /// </summary>
    /// <returns></returns>
    public string  HOSPITAL_NAME { get; set; }
    [Description("院区")]
    /// <summary>
    /// 院区名称
    /// </summary>
    public string? AREA_NAME { get; set; }
    [Description("科室")]
    /// <summary>
    /// 科室名称
    /// </summary>
    public string? LAB_NAME { get; set; }
    [Description("管理专业组")]
    /// <summary>
    /// 管理专业组名称
    /// </summary>
    public string MGROUP_NAME { get; set; }
    
    /// <summary>
    /// 检验专业组名称
    /// </summary>
    ///
    [Description("检验专业组")]
    public string UNIT_NAME  { get; set; }
    
    
    /// <summary>
    /// 备案实验室名称
    /// </summary>
    [Description("备案实验室")]
    public string SMBL_LAB_NAME { get; set; }
    [Description("设备名称（中文）")]
    /// <summary>
    /// 设备名称
    /// </summary>
    public string EQUIPMENT_NAME { get; set; }
    [Description("设备名称（英文）")]
    /// <summary>
    /// 设备英文名称
    /// </summary>
    public string EQUIPMENT_ENAME { get; set; }
    [Description("设备序号")]
    /// <summary>
    /// 设备序号
    /// </summary>
    public string EQUIPMENT_NUM { get; set; }
    [Description("设备型号")]
    /// <summary>
    /// 设备型号
    /// </summary>
    public string EQUIPMENT_MODEL { get; set; }

    [Description("科室设备编号")]
    /// <summary>
    /// 科室设备编号
    /// </summary>
    public string DEPT_SECTION_NO { get; set; }
    
    [Description("设备代号")]
    /// <summary>
    /// 设备代号
    /// </summary>
    public string EQUIPMENT_CODE { get; set; }
    [Description("医院设备编号")]
    /// <summary>
    /// 医院设备编号
    /// </summary>
    public string SECTION_NO { get; set; }
    [Description("所属流水线")]
    /// <summary>
    /// 流水线所属
    /// </summary>
    /// <returns></returns>
    public string PIPELINE_NAME { get; set; }

}