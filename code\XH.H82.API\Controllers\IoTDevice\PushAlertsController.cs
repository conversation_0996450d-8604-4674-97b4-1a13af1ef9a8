﻿using System.Data;
using System.Text;
using ExcelDataReader;
using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Serilog;
using XH.H82.API.Controllers.IoTDevice.Dto;
using XH.H82.API.Extensions;
using XH.H82.Base.Helper;
using XH.H82.IServices.IoTDevice;
using XH.H82.Models.DeviceRelevantInformation;
using XH.H82.Models.SugarDbContext;
using XH.H82.Services.DeviceDataRefresh;

namespace XH.H82.API.Controllers.IoTDevice;

/// <summary>
/// 外部入参接口
/// </summary>
[Route("api/[controller]/[action]")]
[ApiController]
[Authorize]
public class PushAlertsController: ControllerBase
{
    
    private readonly IHttpContextAccessor _httpContext;
    private readonly IIoTDeviceService _deviceService;
    private readonly ISqlSugarUow<SugarDbContext_Master> _dbContext;
    private readonly IConfiguration _configuration;
    public PushAlertsController(IHttpContextAccessor httpContext, IIoTDeviceService deviceService, ISqlSugarUow<SugarDbContext_Master> dbContext, IConfiguration configuration)
    {
        _httpContext = httpContext;
        _deviceService = deviceService;
        _dbContext = dbContext;
        _configuration = configuration;
    }

    /// <summary>
    /// 医疗设备告警推送接口
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [AllowAnonymous]
    [CustomResponseType(typeof(object))]
    public IActionResult PushMedicalEquipmentAlerts([FromBody] MedicalEquipmentAlertDto input)
    {
        _deviceService.PushEquipmentCameraAlert(input);
        return Ok(true.ToResultDto(true, "成功"));
    }
    
    /// <summary>
    /// AI、IPC摄像头推送接口
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [AllowAnonymous]
    [CustomResponseType(typeof(object))]
    
    public IActionResult PushCameraAlerts([FromBody] CameraAlertDto input)
    {
        Log.Logger.Information("AI摄像头告警信息推送信息处理");
        if (JsonConvert.SerializeObject(input).Length>2000)
        {
            Log.Logger.Information($"AI摄像头告警信息推送信息处理:{JsonConvert.SerializeObject(input).Substring(0,1999)}");
        }
        else
        {
            Log.Logger.Information($"AI摄像头告警信息推送信息处理:{JsonConvert.SerializeObject(input)}");
        }
        
        _deviceService.PushAiCameraAlert(input);
        return Ok(true.ToResultDto(true, "成功"));
    }
    
    /// <summary>
    /// AI告警类型初始化
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [AllowAnonymous]
    public IActionResult InitCameraAlerts()
    {
        _deviceService.InitCameraAlerts();
        return Ok(_deviceService.GetCameraAlerts().ToResultDto());
    }
    
    /// <summary>
    /// 摄像头数据初始化
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [AllowAnonymous]
    public IActionResult InitCamera(string equipmentId , string sn , string aiSettinbg ,bool isAi)
    {
        _deviceService.AiCameraInit(equipmentId , sn , aiSettinbg,isAi);
        return Ok(true.ToResultDto());
    }


    /// <summary>
    /// 导出设备待维护信息
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [AllowAnonymous]
    // [TypeFilter(typeof(IpRestrictionFilter))]
    public IActionResult ExportToBeMaintainedEquipments()
    {
        //获取程序所在目录  
        string sourceName = "EquipmentInfoToBeMaintained.xlsx";
        string sourcePath = Path.Combine(Environment.CurrentDirectory, "ExampleFile");
        string localPath = Path.Combine(sourcePath, sourceName);
        Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
        var dataTable = new DataTable();
        
        using (var stream = new FileStream(localPath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
        {
            stream.Position = 0;
            using (var reader = ExcelReaderFactory.CreateReader(stream))
            {
                var result = reader.AsDataSet();
                dataTable = result.Tables[0];
            }
        }
        var equipmentContext = new EquipmentContext(_dbContext);
        var equipments = _deviceService.GetToBeMaintainedEquipments();
        for (int i = 0; i < equipments.Count; i++)
        {
            dataTable.Rows.InsertAt(dataTable.NewRow(), i+1);
            dataTable.Rows[i+1][0] = equipments[i].EQUIPMENT_ID;
            dataTable.Rows[i+1][1] = equipmentContext.ExchangeSmblLabName(equipments[i].SMBL_LAB_ID);
            dataTable.Rows[i+1][2] = equipments[i].SMBL_FLAG == "1" ? "是" : "否";
            dataTable.Rows[i+1][3] = equipments[i].SMBL_CLASS_NAME ?? "";
            dataTable.Rows[i+1][4] = equipmentContext.ExchangeLabName(equipments[i].LAB_ID);
            dataTable.Rows[i+1][5] = equipmentContext.ExchangeProfessionalGroupName(equipments[i].UNIT_ID);
            dataTable.Rows[i+1][6] = equipmentContext.ExchangeEquipmentClass(equipments[i].EQUIPMENT_CLASS, equipments[i].EQUIPMENT_TYPE);
            dataTable.Rows[i + 1][7] = equipments[i].EQUIPMENT_NAME;
            dataTable.Rows[i + 1][8] = equipments[i].EQUIPMENT_UCODE?? equipments[i].EQUIPMENT_CODE;
            dataTable.Rows[i + 1][9] = equipments[i].EQUIPMENT_NUM;
            dataTable.Rows[i + 1][10] = equipments[i].EQUIPMENT_MODEL;
            dataTable.Rows[i + 1][11] = equipments[i].Documents.Any(x=>x.DOC_CLASS == "验收报告") ? "已维护" : "待维护";
            dataTable.Rows[i+1][12] = equipments[i].Documents.Any(x=>x.DOC_CLASS == "验收报告") ? equipments[i].Documents.OrderBy(x=>x.LAST_MTIME).LastOrDefault(x=>x.DOC_CLASS == "验收报告").LAST_MPERSON  : "";
            dataTable.Rows[i+1][13] = equipments[i].Documents.Any(x=>x.DOC_CLASS == "验收报告") ? equipments[i].Documents.OrderBy(x=>x.LAST_MTIME).LastOrDefault(x=>x.DOC_CLASS == "验收报告").LAST_MTIME.Value.ToString("yyyy-MM-dd")  : "";
            dataTable.Rows[i + 1][14] = equipments[i].CertificateInfos.Any() ? "已维护" : "待维护";
            dataTable.Rows[i+1][15] = equipments[i].CertificateInfos.Any() ? equipments[i].CertificateInfos.OrderBy(x=>x.LAST_MTIME).LastOrDefault().LAST_MPERSON  : "";
            dataTable.Rows[i+1][16] = equipments[i].CertificateInfos.Any() ? equipments[i].CertificateInfos.OrderBy(x=>x.LAST_MTIME).LastOrDefault().LAST_MTIME.Value.ToString("yyyy-MM-dd")  : "";
            dataTable.Rows[i + 1][17] = equipments[i].Documents.Any(x=>x.DOC_CLASS == "SOP档案") ? "已维护" : "待维护";
            dataTable.Rows[i+1][18] = equipments[i].Documents.Any(x=>x.DOC_CLASS == "SOP档案") ? equipments[i].Documents.OrderBy(x=>x.LAST_MTIME).LastOrDefault(x=>x.DOC_CLASS == "SOP档案").LAST_MPERSON  : "";
            dataTable.Rows[i+1][19] = equipments[i].Documents.Any(x=>x.DOC_CLASS == "SOP档案") ? equipments[i].Documents.OrderBy(x=>x.LAST_MTIME).LastOrDefault(x=>x.DOC_CLASS == "SOP档案").LAST_MTIME.Value.ToString("yyyy-MM-dd")  : "";
            dataTable.Rows[i + 1][20] = equipments[i].eMS_ENVI_REQUIRE_INFO is not  null ? "已维护" : "待维护";
            dataTable.Rows[i+1][21] = equipments[i].eMS_ENVI_REQUIRE_INFO is not null ? equipments[i].eMS_ENVI_REQUIRE_INFO.LAST_MPERSON  : "";
            dataTable.Rows[i+1][22] = equipments[i].eMS_ENVI_REQUIRE_INFO is not null ? equipments[i].eMS_ENVI_REQUIRE_INFO.LAST_MTIME.Value.ToString("yyyy-MM-dd")  : "";
            dataTable.Rows[i + 1][23] = equipments[i].DEALER.IsNotNullOrEmpty() ? "已维护" : "待维护";
            dataTable.Rows[i+1][24] = equipments[i].DEALER is null ? equipments[i].LAST_MPERSON  : "";
            dataTable.Rows[i+1][25] = equipments[i].DEALER is null ? equipments[i].LAST_MTIME.Value.ToString("yyyy-MM-dd")  : "";
            dataTable.Rows[i + 1][26] = equipments[i].eMS_CORRECT_INFO.Any() ? "已维护" : "待维护";
            dataTable.Rows[i+1][27] = equipments[i].eMS_CORRECT_INFO.Any() ? equipments[i].eMS_CORRECT_INFO.OrderBy(x=>x.LAST_MTIME).LastOrDefault().LAST_MPERSON  : "";
            dataTable.Rows[i+1][28] = equipments[i].eMS_CORRECT_INFO.Any() ? equipments[i].eMS_CORRECT_INFO.OrderBy(x=>x.LAST_MTIME).LastOrDefault().LAST_MTIME.Value.ToString("yyyy-MM-dd")  : "";
            dataTable.Rows[i + 1][29] = equipments[i].eMS_MAINTAIN_INFO.Any() ? "已维护" : "待维护";
            dataTable.Rows[i+1][30] = equipments[i].eMS_MAINTAIN_INFO.Any() ? equipments[i].eMS_MAINTAIN_INFO.OrderBy(x=>x.LAST_MTIME).LastOrDefault().LAST_MPERSON  : "";
            dataTable.Rows[i+1][31] = equipments[i].eMS_MAINTAIN_INFO.Any() ? equipments[i].eMS_MAINTAIN_INFO.OrderBy(x=>x.LAST_MTIME).LastOrDefault().LAST_MTIME.Value.ToString("yyyy-MM-dd")  : "";
            dataTable.Rows[i + 1][32] = equipments[i].Documents.Any(x=>x.DOC_CLASS == "设备说明书") ? "已维护" : "待维护";
            dataTable.Rows[i+1][33] = equipments[i].Documents.Any(x=>x.DOC_CLASS == "设备说明书") ? equipments[i].Documents.OrderBy(x=>x.LAST_MTIME).LastOrDefault(x=>x.DOC_CLASS == "设备说明书").LAST_MPERSON  : "";
            dataTable.Rows[i+1][34] = equipments[i].Documents.Any(x=>x.DOC_CLASS == "设备说明书") ? equipments[i].Documents.OrderBy(x=>x.LAST_MTIME).LastOrDefault(x=>x.DOC_CLASS == "设备说明书").LAST_MTIME.Value.ToString("yyyy-MM-dd")  : "";
            dataTable.Rows[i + 1][35] = equipments[i].eMS_COMPARISON_INFO.Any() ? "已维护" : "待维护";
            dataTable.Rows[i+1][36] = equipments[i].eMS_COMPARISON_INFO.Any() ? equipments[i].eMS_COMPARISON_INFO.OrderBy(x=>x.LAST_MTIME).LastOrDefault().LAST_MPERSON  : "";
            dataTable.Rows[i+1][37] = equipments[i].eMS_COMPARISON_INFO.Any() ? equipments[i].eMS_COMPARISON_INFO.OrderBy(x=>x.LAST_MTIME).LastOrDefault().LAST_MTIME.Value.ToString("yyyy-MM-dd")  : "";
            dataTable.Rows[i + 1][38] = equipments[i].eMS_VERIFICATION_INFO.Any() ? "已维护" : "待维护";
            dataTable.Rows[i+1][39] = equipments[i].eMS_VERIFICATION_INFO.Any() ? equipments[i].eMS_VERIFICATION_INFO.OrderBy(x=>x.LAST_MTIME).LastOrDefault().LAST_MPERSON  : "";
            dataTable.Rows[i+1][40] = equipments[i].eMS_VERIFICATION_INFO.Any() ? equipments[i].eMS_VERIFICATION_INFO.OrderBy(x=>x.LAST_MTIME).LastOrDefault().LAST_MTIME.Value.ToString("yyyy-MM-dd")  : "";
            dataTable.Rows[i + 1][41] = equipments[i].eMS_AUTHORIZE_INFO.Any() ? "已维护" : "待维护";
            dataTable.Rows[i+1][42] = equipments[i].eMS_AUTHORIZE_INFO.Any() ? equipments[i].eMS_AUTHORIZE_INFO.OrderBy(x=>x.LAST_MTIME).LastOrDefault().LAST_MPERSON  : "";
            dataTable.Rows[i+1][43] = equipments[i].eMS_AUTHORIZE_INFO.Any() ? equipments[i].eMS_AUTHORIZE_INFO.OrderBy(x=>x.LAST_MTIME).LastOrDefault().LAST_MTIME.Value.ToString("yyyy-MM-dd")  : "";
            dataTable.Rows[i + 1][44] = equipments[i].eMS_INSTALL_INFO is not  null ? "已维护" : "待维护";
            dataTable.Rows[i+1][45] = equipments[i].eMS_INSTALL_INFO is not null ? equipments[i].eMS_INSTALL_INFO.LAST_MPERSON  : "";
            dataTable.Rows[i+1][46] = equipments[i].eMS_INSTALL_INFO is not null ? equipments[i].eMS_INSTALL_INFO.LAST_MTIME.Value.ToString("yyyy-MM-dd")  : "";
            dataTable.Rows[i + 1][47] = equipments[i].eMS_TRAIN_INFO.Any() ? "已维护" : "待维护";
            dataTable.Rows[i+1][48] = equipments[i].eMS_TRAIN_INFO.Any() ? equipments[i].eMS_TRAIN_INFO.OrderBy(x=>x.LAST_MTIME).LastOrDefault().LAST_MPERSON  : "";
            dataTable.Rows[i+1][49] = equipments[i].eMS_TRAIN_INFO.Any() ? equipments[i].eMS_TRAIN_INFO.OrderBy(x=>x.LAST_MTIME).LastOrDefault().LAST_MTIME.Value.ToString("yyyy-MM-dd")  : "";

        }

        using var excelStream = ExcelHelper.WriteDataSetToExcelTemplate(dataTable.DataSet!, localPath);
        return File(excelStream.ToArray(), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "设备信息维护统计表.xlsx");
    }
}