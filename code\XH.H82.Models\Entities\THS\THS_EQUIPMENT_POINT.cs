﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using H.Utility.SqlSugarInfra;
using SqlSugar;

namespace XH.H82.Models.Entities.THS
{
    /// <summary>
    /// 设备监测点信息表
    /// </summary>
    [DBOwner("XH_SYS")]
    [Table("THS_EQUIPMENT_POINT")]
    [SugarTable("THS_EQUIPMENT_POINT")]
    public class THS_EQUIPMENT_POINT
    {
        /// <summary>
        /// 监测点ID
        /// </summary>
        [Key]
        [SugarColumn(IsPrimaryKey = true)]
        public string? POINT_ID { get; set; }
        /// <summary>
        /// 管理单元ID
        /// </summary>
        public string? UNIT_ID { get; set; }
        /// <summary>
        /// 医疗机构ID
        /// </summary>
        public string? HOSPITAL_ID { get; set; }
        /// <summary>
        /// 设备ID
        /// </summary>
        public string? EQUIPMENT_ID { get; set; }
        /// <summary>
        /// 监测点名称
        /// </summary>
        public string? POINT_NAME { get; set; }
        /// <summary>
        /// 监测点简称
        /// </summary>
        public string? POINT_SNAME { get; set; }
        /// <summary>
        /// 排序号
        /// </summary>
        public string? POINT_SORT { get; set; }
        /// <summary>
        /// 对应编号
        /// </summary>
        public string? POINT_NUM { get; set; }
        /// <summary>
        /// 数据获取方式
        /// </summary>
        public string? OBTAIN_MODE { get; set; }
        /// <summary>
        /// 在线状态1在线2离线
        /// </summary>
        public string? ONLINE_STATE { get; set; }
        /// <summary>
        /// <summary>
        /// 异常状态1正常2异常
        /// </summary>
        public string? ABNORMAL_STATE { get; set; }
        /// <summary>
        /// 状态0禁用1在用
        /// </summary>
        public string? POINT_STATE { get; set; }
        /// <summary>
        /// 首次登记人
        /// </summary>
        public string? FIRST_RPERSON { get; set; }
        /// <summary>
        /// 首次登记时间
        /// </summary>
        public DateTime? FIRST_RTIME { get; set; }
        /// <summary>
        /// 最后修改人员
        /// </summary>
        public string? LAST_MPERSON { get; set; }
        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime? LAST_MTIME { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string? REMARK { get; set; }
        /// <summary>
        /// 设备离线判断时限 例子：30分钟
        /// </summary>
        public int? ONLINE_TIME_LIMIT { get; set; }
        /// <summary>
        /// 数据获取频率(分钟)
        /// </summary>
        public int? OBTAIN_FREQUENCY { get; set; }
        /// <summary>
        /// 温度计编号
        /// </summary>
        public string? EQUIPMENT_MID { get; set; }
        /// <summary>
        /// 最后监测数据时间
        /// </summary>
        public string? LAST_TIME_POINT { get; set; }
    }
}
