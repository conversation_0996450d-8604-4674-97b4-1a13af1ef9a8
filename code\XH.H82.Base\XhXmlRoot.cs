﻿using System.Text;
using System.Xml.Serialization;
using H.Utility.Helper;
using Newtonsoft.Json;
using XH.H82.Base.Helper;

namespace XH.H82.Base;
[XmlRoot("Root")]
public class XhXmlRoot
{
    public string MethodCode { get; set; } = "IotSwitch";
    public string ProductCode { get; set; }= "SMBL";
    public string INTERFACE_ID{ get; set; } = "SMBL";
    public string DataFormat { get; set; }= "JSON";
    public string Sign { get; set; } = "";

    public static XhXmlRoot CreatIotDevice()
    {
        return new XhXmlRoot()
        {
            MethodCode  = "IotSwitch",
            ProductCode = "SMBL",
            INTERFACE_ID = "SMBL",
            DataFormat = "JSON",
            Sign = ""
        };
    }
}

[XmlRoot("Root")]
public class XhBody
{
}


public class  XhPlatformRequest{
    public string HeadXml { get; set; }
    public string BodyXml { get; set; }
    public static XhPlatformRequest Create<THead, TBody>(THead head,TBody body , string titck) 
        where THead : XhXmlRoot, new() 
        where TBody : XhBody,new()
    {
        var jsonBody = JsonConvert.SerializeObject(body);
        var sign = SmxUtilsHelper.SM3Utils($"{titck}{jsonBody}");
        head.Sign = sign;
        var xmlHead = XmlHelper.SerializeObjectToXml(head);
        return new XhPlatformRequest
        {
            HeadXml = Convert.ToBase64String(Encoding.UTF8.GetBytes(xmlHead)),
            BodyXml = Convert.ToBase64String(Encoding.UTF8.GetBytes(jsonBody)),
        };
    }
}

public class XhPlatformResponse<T>
{
     public string ResultCode { get; set; }
     public string ResultMsg { get; set; }
     public List<T> Items { get; set; }
}