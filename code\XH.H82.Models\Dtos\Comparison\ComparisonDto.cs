﻿using H.Utility;
using XH.H82.Models.Entities;

namespace XH.H82.Models.Dtos.Comparison;

public class ComparisonDto
{
    public static EMS_COMPARISON_INFO CreateAddModule(string equipmentId, string? hospitalId  , ComparisonInput input)
    {
        var record = new EMS_COMPARISON_INFO();
        record.EQUIPMENT_ID = equipmentId;
        record.COMPARISON_ID = IDGenHelper.CreateGuid();
        record.HOSPITAL_ID = hospitalId ?? "H0000";
        record.COMPARISON_RESULT = input.COMPARISON_RESULT;
        record.COMPARISON_DATE = input.COMPARISON_DATE;
        record.COMPARISON_PERSON = input.COMPARISON_PERSON;
        if (record.COMPARISON_PERSON.IsNotNullOrEmpty() && record.COMPARISON_PERSON.Contains("_"))
        {
            record.COMPARISON_PERSON = record.COMPARISON_PERSON.Split('_')[1];
        }
        record.COMPARISON_OBJECT = input.COMPARISON_OBJECT;
        record.REMARK = input.REMARK;
        record.COMPARISON_STATE = "1";
        record.STATE = "已执行";
        return record;
    }
}

/// <summary>
/// 比对记录入参模型
/// </summary>
/// <param name="COMPARISON_DATE">比对日期</param>
/// <param name="COMPARISON_OBJECT">比对对象</param>
/// <param name="COMPARISON_RESULT">比对结果</param>
/// <param name="COMPARISON_PERSON">操作人</param>
/// <param name="REMARK">备注</param>
public record ComparisonInput(
    DateTime? COMPARISON_DATE,
    string? COMPARISON_RESULT,
    string? COMPARISON_PERSON,
    string? COMPARISON_OBJECT,
    string? REMARK);