﻿using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Memory;
using Newtonsoft.Json;
using XH.H82.IServices;
using XH.H82.Models.Card;
using XH.H82.Models.DeviceRelevantInformation;
using XH.H82.Models.Entities.InkScreen;
using XH.H82.Models.InkScreenTemplate;
using XH.H82.Models.InkScreenTemplate.Dto;
using XH.H82.Models.SugarDbContext;
using XH.H82.Services.DeviceDataRefresh;
using XH.LAB.UTILS.Interface;
using Template = XH.H82.Models.InkScreenTemplate.Template;

namespace XH.H82.Services
{
    public class InkScreenTemplateService : IInkScreenTemplateService
    {
        private readonly ISqlSugarUow<SugarDbContext_Master> _dbContext;
        private ClaimsDto _user { get; set; }

        private AuthorityContext _authorityContext { get; set; }
        private EquipmentContext _equipmentCopntext { get; set; }
        private readonly IAuthorityService2 _authorityService;

        private IMemoryCache _memoryCache;
        public InkScreenTemplateService(ISqlSugarUow<SugarDbContext_Master> dbContext, IHttpContextAccessor httpContext, IMemoryCache memoryCache, IAuthorityService2 authorityService)
        {
            _dbContext = dbContext;
            _user = httpContext.HttpContext.User.ToClaimsDto();
            _dbContext.SetCreateTimeAndCreatePersonData(_user);
            _dbContext.IsDeleted<EMS_INKSCREEN_TEMPLATE>(x => x.TEMPLATE_STATE != "2");
            _authorityContext = new(_dbContext,authorityService);
            _equipmentCopntext = new(_dbContext);
            _memoryCache = memoryCache;
            _authorityService = authorityService;
        }

        public void AddTemplate(string TemplateName, string? reamrk)
        {
            var contentInit = TemplateAContentInit.Default();
            var template = new EMS_INKSCREEN_TEMPLATE()
            {
                TEMPLATE_ID = IDGenHelper.CreateGuid(),
                TEMPLATE_NAME = TemplateName,
                REMARK = reamrk,
                TEMPLATE_CONTENT = JsonConvert.SerializeObject(contentInit),
                TEMPLATE_TITLE = "检验中心设备运行状态及保养/校准标识",
                PGROUP_SID = "",
                TEMPLATE_STATE = "1",
            };
            _dbContext.Db.Insertable(template).ExecuteCommand();
        }
        public void CopyTemplate(string templateId)
        {
            var template = _dbContext.Db.Queryable<EMS_INKSCREEN_TEMPLATE>().First(x => x.TEMPLATE_ID == templateId);

            if (template is null)
            {
                throw new BizException("模板不存在！");
            }
            var newTemplate = template;
            newTemplate.TEMPLATE_NAME = $"{template.TEMPLATE_NAME}-copy";
            newTemplate.TEMPLATE_ID = IDGenHelper.CreateGuid();
            newTemplate.PGROUP_SID = "";
            _dbContext.Db.Insertable(newTemplate).ExecuteCommand();
        }
        public void DeleteTemplate(string templateId)
        {
            var template = _dbContext.Db.Queryable<EMS_INKSCREEN_TEMPLATE>().First(x => x.TEMPLATE_ID == templateId);
            if (template is null)
            {
                throw new BizException("模板不存在！");
            }
            template.TEMPLATE_STATE = "2";
            _dbContext.Db.Updateable(template).ExecuteCommand();

        }

        /// <summary>
        /// 修改模板名称及备注
        /// </summary>
        /// <param name="templateId"></param>
        /// <param name="TemplateName"></param>
        /// <param name="reamrk"></param>
        /// <exception cref="BizException"></exception>
        public void UpdateTemplate(string templateId, string TemplateName, string? reamrk)
        {
            var template = _dbContext.Db.Queryable<EMS_INKSCREEN_TEMPLATE>().First(x => x.TEMPLATE_ID == templateId);
            if (template is null)
            {
                throw new BizException("模板不存在！");
            }
            template.TEMPLATE_NAME = TemplateName;
            template.REMARK = reamrk;
            _dbContext.Db.Updateable(template).IgnoreColumns(ignoreAllNullColumns:true).ExecuteCommand();

        }

        public List<EMS_INKSCREEN_TEMPLATE> GetTemplates(string labId, string? groupId, string? name)
        {
            _authorityContext.SetUser(_user,  labId);
            var groups = _authorityContext.GetAccessibleProfessionalGroups(labId, null).Select(x => x.PGROUP_ID);
            var templates = _dbContext.Db.Queryable<EMS_INKSCREEN_TEMPLATE>()
                .WhereIF(groupId.IsNotNullOrEmpty(), x => x.PGROUP_SID.Contains(groupId!))
                .WhereIF(name.IsNotNullOrEmpty(), x => x.TEMPLATE_NAME.Contains(name!))
                .ToList()
                .Where(x => x.GetGroups().Count == 0 || x.GetGroups().Any(id => groups.Contains(id)))
                .ToList();
            if (templates is null)
            {
                return new();
            }
            return templates;
        }

        public List<TemplateAttribute> GetAllTemplateAttributes()
        {
            var result = new List<TemplateAttribute>();

            var data = new IdentificationCard();

            var attributes = data.ToAttributes();

            foreach (var attr in attributes)
            {
                if (attr.Value.IsNotNullOrEmpty())
                {
                    result.Add(new(attr.Value, attr.Key));
                }
            }
            return result;
        }

        public void SaveTemplate(string templateId, string tempContent, string tempTitle, bool setAbnormal, bool setQRCode, bool SetWireframe)
        {

            var template = _dbContext.Db.Queryable<EMS_INKSCREEN_TEMPLATE>().First(x => x.TEMPLATE_ID == templateId);
            if (template is null)
            {
                throw new BizException("模板不存在！");
            }
            template.TEMPLATE_TITLE = tempTitle;
            template.TEMPLATE_CONTENT = tempContent;
            template.SET_ABNORMAL = setAbnormal;
            template.SET_QR_CODE = setQRCode;
            template.SET_WIREFRAME = SetWireframe;
            _dbContext.Db.Updateable(template).ExecuteCommand();

        }

        /// <summary>
        /// 把模板应用到专业组
        /// </summary>
        /// <param name="templateId"></param>
        /// <param name="groups">专业组ids</param>
        /// <exception cref="BizException"></exception>
        public void ApplicationGroups(string templateId, List<string> groups)
        {
            var template = _dbContext.Db.Queryable<EMS_INKSCREEN_TEMPLATE>().First(x => x.TEMPLATE_ID == templateId);
            if (template is null)
            {
                throw new BizException("模板不存在！");
            }
            template.PGROUP_SID = "";
            foreach (var group in groups)
            {
                var count = _dbContext.Db.Queryable<EMS_INKSCREEN_TEMPLATE>().Where(x => x.TEMPLATE_ID != templateId).Count(x => x.PGROUP_SID.Contains(group));
                if (count > 0)
                {
                    var temp = _dbContext.Db.Queryable<EMS_INKSCREEN_TEMPLATE>().Where(x => x.TEMPLATE_ID != templateId).Where(x => x.PGROUP_SID.Contains(group))
                          .Select(x => new EMS_INKSCREEN_TEMPLATE()
                          {
                              TEMPLATE_ID = x.TEMPLATE_ID,
                              PGROUP_SID = x.PGROUP_SID
                          }).First();
                    temp.PGROUP_SID = temp.PGROUP_SID.Replace(group, "").Replace(";;", ";");

                    if (temp.PGROUP_SID.IsNotNullOrEmpty())
                    {
                        if (temp.PGROUP_SID.Substring(0, 1) == ";")
                        {
                            temp.PGROUP_SID = temp.PGROUP_SID.Substring(1, temp.PGROUP_SID.Length - 1);
                        }
                    }

                    _dbContext.Db.Updateable<EMS_INKSCREEN_TEMPLATE>()
                        .SetColumns(x => new EMS_INKSCREEN_TEMPLATE
                        {
                            PGROUP_SID = temp.PGROUP_SID
                        })
                        .Where(x => x.TEMPLATE_ID == temp.TEMPLATE_ID)
                        .ExecuteCommand();
                }

                if (template.PGROUP_SID.IsNotNullOrEmpty())
                {
                    if (template.PGROUP_SID.Substring(template.PGROUP_SID.Length - 1, 1) != ";")
                    {
                        template.PGROUP_SID += $";{group};";
                    }
                    else
                    {
                        template.PGROUP_SID += $"{group};";
                    }
                }
                else
                {
                    template.PGROUP_SID += $"{group};";
                }
            }

            if (template.PGROUP_SID.Length > 0)
            {
                if (template.PGROUP_SID.Substring(template.PGROUP_SID.Length - 1, 1) == ";")
                {
                    template.PGROUP_SID = template.PGROUP_SID.Remove(template.PGROUP_SID.Length - 1);
                }
            }

            _dbContext.Db.Updateable(template).ExecuteCommand();
        }

        public IdentificationCard GetTemplatePreview(string templateId, string? parameters)
        {
            if (parameters is null)
            {
                parameters = "";
            }
            var template = _dbContext.Db.Queryable<EMS_INKSCREEN_TEMPLATE>().First(x => x.TEMPLATE_ID == templateId);
            if (template is null)
            {
                throw new BizException("模板不存在！");
            }
            var content = JsonConvert.DeserializeObject<List<Template>>(template.TEMPLATE_CONTENT);

            var idCardInfo = new IdentificationCard()
            {
                templateTitle = template.TEMPLATE_TITLE,
                equipmentName = parameters.Contains("equipmentName") ? GetEquimentMaxLongValue("equipmentName") : "",
                dealer = parameters.Contains("dealer") ? GetEquimentMaxLongValue("dealer") : "",
                equipmentCode = parameters.Contains("equipmentCode") ? GetEquimentMaxLongValue("equipmentCode") : "",
                deptSectionNo = parameters.Contains("deptSectionNo") ? GetEquimentMaxLongValue("deptSectionNo") : "",
                enableTime = parameters.Contains("enableTime") ? GetEquimentMaxLongValue("enableTime") : "",
                installDate = parameters.Contains("installDate") ? GetEquimentMaxLongValue("installDate") : "",
                maintainType = parameters.Contains("maintainType") ? GetEquimentMaxLongValue("maintainType") : "",
                nextMaintainDate = parameters.Contains("nextMaintainDate") ? GetEquimentMaxLongValue("nextMaintainDate") : "",
                nextMaintainDateStatus = "false",
                nextCorrectDate = parameters.Contains("nextCorrectDate") ? GetEquimentMaxLongValue("nextCorrectDate") : "",
                nextCorrectDateStatus = "false",
                correctDept = parameters.Contains("correctDept") ? GetEquimentMaxLongValue("correctDept") : "",
                eqInPerson = parameters.Contains("eqInPerson") ? GetEquimentMaxLongValue("eqInPerson") : "",
                installArea = parameters.Contains("installArea") ? GetEquimentMaxLongValue("installArea") : "",
                circumstance = "启用",
                meassage = "",
                registrationNum = parameters.Contains("registrationNum") ? GetEquimentMaxLongValue("registrationNum") : "",
                eqOutTime = parameters.Contains("eqOutTime") ? GetEquimentMaxLongValue("eqOutTime") : "",
                professionalClass = parameters.Contains("professionalClass") ? GetEquimentMaxLongValue("professionalClass") : "",
                manufacturer = parameters.Contains("manufacturer") ? GetEquimentMaxLongValue("manufacturer") : "",
                serialNumber = parameters.Contains("serialNumber") ? GetEquimentMaxLongValue("serialNumber") : "",
                dealerContact = parameters.Contains("dealerContact") ? GetEquimentMaxLongValue("dealerContact") : "",
                lastComparisonDate = parameters.Contains("lastComparisonDate") ? GetEquimentMaxLongValue("lastComparisonDate") : "",
                depreciationTime = parameters.Contains("depreciationTime") ? GetEquimentMaxLongValue("depreciationTime") : "",
                correctIntervals = parameters.Contains("correctIntervals") ? GetEquimentMaxLongValue("correctIntervals") : "",
                manufacturerContact = parameters.Contains("manufacturerContact") ? GetEquimentMaxLongValue("manufacturerContact") : "",
                hospitalName = parameters.Contains("hospitalName") ? GetEquimentMaxLongValue("hospitalName") : "",
                labName = parameters.Contains("labName") ? GetEquimentMaxLongValue("labName") : "",
                sellPrice = parameters.Contains("sellPrice") ? GetEquimentMaxLongValue("sellPrice") : "",
                equipmentSize = parameters.Contains("equipmentSize") ? GetEquimentMaxLongValue("equipmentSize") : "",
                correctDate = parameters.Contains("correctDate") ?GetEquimentMaxLongValue("correctDate") : "",
                model = parameters.Contains("model") ?GetEquimentMaxLongValue("model") : "",
                maintainDate = parameters.Contains("maintainDate") ?GetEquimentMaxLongValue("maintainDate") : "",
            };

            idCardInfo.SetTemplates(content);
            return idCardInfo;
        }

        /// <summary>
        /// 直接设置墨水屏展示值
        /// </summary>
        /// <param name="templateDataAndValues"></param>
        /// <returns></returns>
        public IdentificationCard SetTemplateValue(List<TemplateDataAndValue> templateDataAndValues ,bool nextMaintainDateStatus = false, bool nextCorrectDateStatus = false, string circumstance  = "启用" ,string meassage = "")
        {
            var idCardInfo = new IdentificationCard();
            var templates = new List<Template>();
            foreach (var item in templateDataAndValues)
            { 
                var temps = new List<TemplateAttribute>();
                item.cols.RemoveAll(x => x.colKey.IsNullOrEmpty());
                for (int i = 0; i < item.cols.Count; i++)
                {
                   
                    if (i%2 == 0)
                    {
                        temps.Add(item.cols[i]);
                    }
                }
                templates.Add(new Template(item.rowIndex,temps));
                foreach (var property in idCardInfo.GetType().GetProperties())
                {
                    for (int i = 0; i < item.cols.Count; i++)
                    {
                        if (i%2 != 0)
                        {
                            if (property.Name == item.cols[i].colKey)
                            {
                                property.SetValue(idCardInfo,item.cols[i].colTitle);
                            }
                        }
                    }
                }
            }
            idCardInfo.nextMaintainDateStatus =nextMaintainDateStatus ? "true" : "false" ;
            idCardInfo.nextCorrectDateStatus = nextCorrectDateStatus ? "true" : "false";
            idCardInfo.circumstance = circumstance;
            idCardInfo.meassage = meassage;
            idCardInfo.SetTemplates(templates);
            return idCardInfo;
        }

        private string GetEquimentMaxLongValue(string attribute)
        {
            var value = _memoryCache.Get<string>($"LongValue:{attribute}");
            if (value is null)
            {
                value = _equipmentCopntext.GetEquimentMaxLongValue(attribute);
                _memoryCache.Set($"LongValue:{attribute}", value, TimeSpan.FromDays(1));
            }
            if (value is null)
            {
                value = "";
            }
            return value;
        }
    }
}
