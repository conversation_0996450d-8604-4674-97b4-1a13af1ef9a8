﻿using H.Utility.SqlSugarInfra;
using SqlSugar;

namespace XH.H82.Models.Entities;

/// <summary>
/// 实验室管理基础数据表
/// </summary>
[SugarTable("SYS6_BASE_DATA_CLASS")]
[DBOwner("XH_SYS")]
public class SYS6_BASE_DATA_CLASS
{
    /// <summary>
    /// 基础数据分类ID
    /// </summary>
    [SugarColumn(IsPrimaryKey = true)]
    public string DATA_CLASS_ID { get; set; }
    public string HOSPITAL_ID { get; set; }
    public string CLASS_ID { get; set; }
    public string CLASS_NAME { get; set; }
    public string CLASS_SORT { get; set; }
    public string DATA_TABLE { get; set; }
    public string DATA_FIELD { get; set; }
    public string CLASS_STATE { get; set; }
    public string FIRST_RPERSON { get; set; }
    public DateTime FIRST_RTIME { get; set; }
    public string LAST_MPERSON { get; set; }
    public DateTime LAST_MTIME { get; set; }
    public string REMARK { get; set; }
    public string ONE_CLASS { get; set; }
}