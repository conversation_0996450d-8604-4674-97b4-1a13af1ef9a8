﻿using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using Microsoft.Extensions.Options;
using Serilog;
using Serilog.AspNetCore;
using SqlSugar;
using System.Diagnostics.Metrics;
using XH.H82.Models.Entities;
using XH.H82.Models.SugarDbContext;

namespace XH.H82.API.Extensions
{
    public class DateCheckContext
    {
        private readonly ISqlSugarUow<SugarDbContext_Master> _dbContext;
        public DateCheckContext(ISqlSugarUow<SugarDbContext_Master> dbContext) => _dbContext = dbContext;

        /// <summary>
        /// 检查设备信息数据
        /// </summary>
        public void CheckEquipments()
        {
            _dbContext.NoLog();
            Log.Information("正在检查旧数据是否完整...");
            Log.Information("开始检查EMS_EQUIPMENT_INFO旧数据是否完整...");
            var equipments = _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>().Where(x => x.LAST_MTIME == null).ToList();
            if (equipments.Count > 0)
            {
                var ids = equipments.Select(x => x.EQUIPMENT_ID).ToList();
                _dbContext.Db.Updateable<EMS_EQUIPMENT_INFO>()
                    .SetColumns(x => new EMS_EQUIPMENT_INFO() { LAST_MTIME = DateTime.Now })
                    .Where(x => ids.Contains(x.EQUIPMENT_ID))
                    .ExecuteCommand();
            }
            Log.Information($"检查EMS_EQUIPMENT_INFO旧数据结束，已更新{equipments.Count}条...");
            
            var equipmentWordPlans = _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>()
                .Includes(x => x.eMS_WORK_PLAN).ToList()
                .Where(x => x.eMS_WORK_PLAN == null);

            var workPlans = new List<EMS_WORK_PLAN>();

            foreach (var equipmentWordPlan in equipmentWordPlans)
            {
                workPlans.Add(new EMS_WORK_PLAN()
                {
                    WORK_PLAN_ID = IDGenHelper.CreateGuid().ToString(),
                    WORK_PLAN_STATE = "1",
                    HOSPITAL_ID = equipmentWordPlan.HOSPITAL_ID,
                    EQUIPMENT_ID = equipmentWordPlan.EQUIPMENT_ID,
                    FIRST_RPERSON = equipmentWordPlan.FIRST_RPERSON,
                    FIRST_RTIME = DateTime.Now,
                    LAST_MPERSON = equipmentWordPlan.LAST_MPERSON,
                    LAST_MTIME = DateTime.Now,
                    REMARK = "历史设备数据的补充"
                });
            }
            if (workPlans.Count > 0)
            {
                _dbContext.Db.Insertable(workPlans).ExecuteCommand();
                Log.Information($"检查EMS_WORK_PLAN旧数据结束，已更新{workPlans.Count}条...");
            }
        }
        
    }
}
