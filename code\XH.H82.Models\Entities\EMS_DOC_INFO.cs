﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;
using H.Utility.SqlSugarInfra;
using SqlSugar;

namespace XH.H82.Models.Entities
{
    [DBOwner("XH_OA")]
    /// <summary>
    ///文件信息
    /// </summary>
    public class EMS_DOC_INFO
    {
        [SugarColumn(IsPrimaryKey = true)]
        [StringLength(50)]
        public string DOC_ID { get; set; }
        [StringLength(50)]
        public string HOSPITAL_ID { get; set; }
        [StringLength(50)]
        public string DOC_CLASS { get; set; }
        [StringLength(50)]
        public string DOC_INFO_ID { get; set; }
        [StringLength(50)]
        public string EQUIPMENT_ID { get; set; }
        [StringLength(200)]
        public string DOC_NAME { get; set; }
        [StringLength(200)]
        public string DOC_FILE { get; set; }
        [StringLength(20)]
        public string DOC_SUFFIX { get; set; }
        [StringLength(20)]
        public string DOC_TYPE { get; set; }
        [StringLength(200)]
        public string DOC_PATH { get; set; }
        [StringLength(50)]
        public string UPLOAD_PERSON { get; set; }
        public DateTime? UPLOAD_TIME { get; set; }
        [StringLength(50)]
        public string FIRST_RPERSON { get; set; }
        public DateTime? FIRST_RTIME { get; set; }
        [StringLength(50)]
        public string LAST_MPERSON { get; set; }
        public DateTime? LAST_MTIME { get; set; }
        [StringLength(20)]
        public string DOC_STATE { get; set; }
        [StringLength(200)]
        public string REMARK { get; set; }
        [StringLength(200)]
        public string PDF_PREVIEW_PATH { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string? FILEBASE64 { get; set; }
        public string? EMS_INFO_ID { get; set; }
    }
}
