﻿namespace XH.H82.Models.BusinessModuleClient
{
    public class ThirdVerifyInfoDto
    {
        public string key { get; set; }
        public string appKey { get; set; }

    }



    //public class KeyHandle
    //{
    //    public ISqlSugarUow DbContext { get; }

    //    public KeyHandle(ISqlSugarUow _dbContext)
    //    {
    //        DbContext = _dbContext;
    //    }
    //    public ThirdVerifyInfoDto GetKey()
    //    {
    //        var sys6_interface_module = DbContext.Db.Queryable<SYS6_INTERFACE_MODULE>().First(p => p.MODULE_ID == "H82");
    //        if (sys6_interface_module != null)
    //        {
    //            string app_key = sys6_interface_module.APP_KEY;
    //            string app_secret = sys6_interface_module.APP_SECRET;
    //            var contentKey = new
    //            {
    //                callModuleId = "H82",
    //                moduleId = "S28",
    //                requestTime = DateTime.Now,
    //                tokenGuid = IDGenHelper.CreateGuid(),
    //                userNo = "H82",
    //                isInteriorUser = false,
    //                obj = ""
    //            };
    //            string strKey = JsonHelper.ToJson(contentKey);
    //            string secretKey = SmxUtilsHelper.SM4UtilsEncrypt(app_secret, strKey);
    //            var result = new ThirdVerifyInfoDto()
    //            {
    //                key = secretKey,
    //                appKey = app_key
    //            };
    //            return result;
    //        }
    //    }



    //}
}