﻿using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Spire.Doc;
using Spire.Doc.Documents;
using SqlSugar;
using System.Reflection;
using XH.H82.IServices;
using XH.H82.Models.DeviceRelevantInformation;
using XH.H82.Models.Dtos;
using XH.H82.Models.Entities;
using XH.H82.Models.SugarDbContext;
using XH.H82.Services.DeviceDataRefresh;
using XH.LAB.UTILS.Interface;
using XH.LAB.UTILS.Models;

namespace XH.H82.Services
{
    public class XhScrapStopService : IXhScrapStopService
    {
        const string APPLY_PERMISSION_ID = "H8256001"; //申请报废停用权限
        const string REAPPLY_PERMISSION_ID = "H8256002"; //重新申请报废停用权限
        const string SUMMIT_PERMISSION_ID = "H8256003"; //提交报废停用权限
        const string AUDIT_PERMISSION_ID = "H8256004"; //通过报废停用权限
        const string REJECT_PERMISSION_ID = "H8256005"; //驳回报废停用权限
        const string REVOKE_PERMISSION_ID = "H8256006"; //撤销报废停用权限

        private readonly IHostingEnvironment _hostingEnvironment;
        private readonly IBaseDataServices _baseDataServices;
        private readonly ISqlSugarUow<SugarDbContext_Master> _sqlSugarUow;
        private readonly ILogger<OperationRecordService> _logger;
        private readonly IAuthorityService _authorityService;
        public XhScrapStopService(IHostingEnvironment hostingEnvironment, IBaseDataServices baseDataServices, ISqlSugarUow<SugarDbContext_Master> sqlSugarUow, ILogger<OperationRecordService> logger, IAuthorityService authorityService)
        {
            _hostingEnvironment = hostingEnvironment;
            _baseDataServices = baseDataServices;
            _sqlSugarUow = sqlSugarUow;
            _logger = logger;
            _authorityService = authorityService;
            //ExecutingChangeSqlHelper.ExecutingChangeSql(_sqlSugarUow);
        }

        public ResultDto ScrapStopApply(List<ScrapStopListDto> record, string hospitalId, string userName, string userNo, string state)
        {
            ////权限判断
            //_authorityService.CheckUserMenuPermission(_sqlSugarUow, userNo, APPLY_PERMISSION_ID, hospitalId, isThrowException : true);
            ResultDto result = new ResultDto();
            try
            {
                var record_scrap = new List<EMS_SCRAP_INFO>();
                var process_scrap = new List<EMS_SCRAP_LOG>();
                record.ForEach(item =>
                {
                    item.ScrapStop_ID = IDGenHelper.CreateGuid().ToString();
                    record_scrap.Add(new EMS_SCRAP_INFO()
                    {
                        SCRAP_ID = item.ScrapStop_ID,
                        APPLY_TYPE = item.APPLY_CLASS,
                        HOSPITAL_ID = hospitalId,
                        EQUIPMENT_ID = item.EQUIPMENT_ID,
                        SCRAP_CAUSE = item.APPLY_REASON,
                        SCRAP_STATE = "1",
                        OPER_PERSON = userName,
                        OPER_PERSON_ID = userNo,
                        OPER_TIME = DateTime.Now,
                        FIRST_RPERSON = userName,
                        FIRST_RTIME = DateTime.Now,
                        LAST_MPERSON = userName,
                        LAST_MTIME = DateTime.Now,
                        EXAMINE_PERSON = item.EXAMINE_PERSON,
                        EXAMINE_PERSON_ID = item.EXAMINE_PERSON_ID,
                        APPLY_STATE = state,
                        IF_RE_APPLY = "0",
                        SCRAP_DATE = item.SCRAP_DATE
                    });
                    if (state == "1")
                    {
                        process_scrap.Add(new EMS_SCRAP_LOG()
                        {
                            SCRAP_PROCESS_ID = IDGenHelper.CreateGuid().ToString(),
                            HOSPITAL_ID = hospitalId,
                            SCRAP_ID = item.ScrapStop_ID,
                            PROCESS_STATE = "1",
                            OPER_PERSON = userName,
                            OPER_TIME = DateTime.Now,
                            PROCESS_TYPE = item.APPLY_CLASS + "申请",
                            PROCESS_CONTENT = item.APPLY_REASON,
                            FIRST_RPERSON = userName,
                            FIRST_RTIME = DateTime.Now,
                            LAST_MPERSON = userName,
                            LAST_MTIME = DateTime.Now,
                        });
                    }
                });
                _sqlSugarUow.Db.Insertable(record_scrap).ExecuteCommand();
                _sqlSugarUow.Db.Insertable(process_scrap).ExecuteCommand();
                result.success = true;
            }
            catch (Exception ex)
            {
                result.success = false;
                result.msg = "报废申请失败";
                _logger.LogError("报废申请失败:\n" + ex.Message);
            }
            return result;
        }

        public ResultDto SaveApply(List<ScrapStopListDto> record, string hospitalId, string userName, string userNo, string state)
        {
            ////权限判断
            //if (record.Any() && record[0].IF_RE_APPLY == "1")
            //    _authorityService.CheckUserMenuPermission(_sqlSugarUow, userNo, REAPPLY_PERMISSION_ID, hospitalId, isThrowException: true);
            //else
            //    _authorityService.CheckUserMenuPermission(_sqlSugarUow, userNo, REAPPLY_PERMISSION_ID, hospitalId, isThrowException: true);
            ResultDto result = new ResultDto();
            try
            {
                var process_scrap = new List<EMS_SCRAP_LOG>();
                var logList = _sqlSugarUow.Db.Queryable<EMS_SCRAP_LOG>().ToList();
                record.ForEach(item =>
                {
                    //若当前状态为待提交
                    if (item.STATE == "0")
                    {
                        //若修改后提交
                        if (state == "1")
                        {
                            process_scrap.Add(new EMS_SCRAP_LOG()
                            {
                                SCRAP_PROCESS_ID = IDGenHelper.CreateGuid().ToString(),
                                HOSPITAL_ID = hospitalId,
                                SCRAP_ID = item.ScrapStop_ID,
                                PROCESS_STATE = "1",
                                OPER_PERSON = userName,
                                OPER_TIME = DateTime.Now,
                                PROCESS_TYPE = item.IF_RE_APPLY == "0" ? item.APPLY_CLASS + "申请" : item.APPLY_CLASS + "重新申请",
                                PROCESS_CONTENT = item.APPLY_REASON,
                                FIRST_RPERSON = userName,
                                FIRST_RTIME = DateTime.Now,
                                LAST_MPERSON = userName,
                                LAST_MTIME = DateTime.Now,
                            });
                        }
                    }
                    //若当前状态为待审核
                    if (item.STATE == "1")
                    {
                        //删除最新一条日志
                        var log_delete = logList.Where(p => p.SCRAP_ID == item.ScrapStop_ID).OrderByDescending(i => i.OPER_TIME).FirstOrDefault();
                        _sqlSugarUow.Db.Deleteable(log_delete);
                        //若修改后提交
                        if (state == "1")
                        {
                            process_scrap.Add(new EMS_SCRAP_LOG()
                            {
                                SCRAP_PROCESS_ID = IDGenHelper.CreateGuid().ToString(),
                                HOSPITAL_ID = hospitalId,
                                SCRAP_ID = item.ScrapStop_ID,
                                PROCESS_STATE = "1",
                                OPER_PERSON = userName,
                                OPER_TIME = DateTime.Now,
                                PROCESS_TYPE = item.IF_RE_APPLY == "0" ? item.APPLY_CLASS + "申请" : item.APPLY_CLASS + "重新申请",
                                PROCESS_CONTENT = item.APPLY_REASON,
                                FIRST_RPERSON = userName,
                                FIRST_RTIME = DateTime.Now,
                                LAST_MPERSON = userName,
                                LAST_MTIME = DateTime.Now,
                            });
                        }
                    }
                    if (item.STATE == "4" || item.STATE == "5")
                    {
                        if (state == "1")
                        {
                            process_scrap.Add(new EMS_SCRAP_LOG()
                            {
                                SCRAP_PROCESS_ID = IDGenHelper.CreateGuid().ToString(),
                                HOSPITAL_ID = hospitalId,
                                SCRAP_ID = item.ScrapStop_ID,
                                PROCESS_STATE = "1",
                                OPER_PERSON = userName,
                                OPER_TIME = DateTime.Now,
                                PROCESS_TYPE = item.APPLY_CLASS + "重新申请",
                                PROCESS_CONTENT = item.APPLY_REASON,
                                FIRST_RPERSON = userName,
                                FIRST_RTIME = DateTime.Now,
                                LAST_MPERSON = userName,
                                LAST_MTIME = DateTime.Now,
                            });
                        }
                    }
                    _sqlSugarUow.Db.Updateable<EMS_SCRAP_INFO>().SetColumns(p => new EMS_SCRAP_INFO
                    {
                        OPER_PERSON = item.IF_RE_APPLY == "0" ? item.APPLY_PERSON : userName,
                        OPER_TIME = item.IF_RE_APPLY == "0" ? item.APPLY_DATE : DateTime.Now,
                        APPLY_STATE = state,
                        APPLY_TYPE = item.APPLY_CLASS,
                        SCRAP_CAUSE = item.APPLY_REASON,
                        SCRAP_DATE = item.SCRAP_DATE,
                        EXAMINE_OPINION = null,
                        EXAMINE_DATE = null,
                        EXAMINE_PERSON = item.EXAMINE_PERSON,
                        EXAMINE_PERSON_ID = item.EXAMINE_PERSON_ID,
                        APPROVE_OPINION = null,
                        APPROVE_DATE = null,
                        APPROVE_PERSON = null,
                        APPROVE_PERSON_ID = null,
                        LAST_MPERSON = userName,
                        LAST_MTIME = DateTime.Now,
                        IF_RE_APPLY = (item.STATE != "4" && item.STATE != "5") ? item.IF_RE_APPLY :
                            (Convert.ToInt32(item.IF_RE_APPLY) + 1).ToString(),
                    }).Where(p => p.SCRAP_ID == item.ScrapStop_ID).ExecuteCommand();

                });
                _sqlSugarUow.Db.Insertable(process_scrap).ExecuteCommand();
                result.success = true;
            }
            catch (Exception ex)
            {
                result.success = false;
                result.msg = "提交失败";
                _logger.LogError("提交失败:\n" + ex.Message);
            }
            return result;
        }

        public ResultDto BatchSubmit(List<ScrapStopListDto> record, string userName, string userNo, string hospitalId)
        {
            ////权限判断
            //_authorityService.CheckUserMenuPermission(_sqlSugarUow, userNo, SUMMIT_PERMISSION_ID, hospitalId, isThrowException: true);
            ResultDto result = new ResultDto();
            try
            {
                var process_scrap = new List<EMS_SCRAP_LOG>();
                record.ForEach(item =>
                {
                    if (item.STATE == "0")
                    {
                        process_scrap.Add(new EMS_SCRAP_LOG
                        {
                            SCRAP_PROCESS_ID = IDGenHelper.CreateGuid().ToString(),
                            HOSPITAL_ID = hospitalId,
                            SCRAP_ID = item.ScrapStop_ID,
                            PROCESS_STATE = "1",
                            PROCESS_TYPE = item.IF_RE_APPLY == "0" ? item.APPLY_CLASS + "申请" : item.APPLY_CLASS + "重新申请",
                            PROCESS_CONTENT = item.APPLY_REASON,
                            OPER_PERSON = userName,
                            OPER_TIME = DateTime.Now,
                            FIRST_RPERSON = userName,
                            FIRST_RTIME = DateTime.Now,
                            LAST_MPERSON = userName,
                            LAST_MTIME = DateTime.Now,
                        });
                    }
                    _sqlSugarUow.Db.Updateable<EMS_SCRAP_INFO>().SetColumns(p => new EMS_SCRAP_INFO
                    {
                        OPER_PERSON = item.IF_RE_APPLY == "0" ? item.APPLY_PERSON : userName,
                        OPER_TIME = item.IF_RE_APPLY == "0" ? item.APPLY_DATE : DateTime.Now,
                        APPLY_STATE = "1",
                        LAST_MPERSON = userName,
                        LAST_MTIME = DateTime.Now,
                        IF_RE_APPLY = item.IF_RE_APPLY
                    }).Where(p => p.SCRAP_ID == item.ScrapStop_ID).ExecuteCommand();
                });
                _sqlSugarUow.Db.Insertable(process_scrap).ExecuteCommand();
            }
            catch (Exception ex)
            {
                result.success = false;
                result.msg = "批量提交失败";
                _logger.LogError("批量提交失败:\n" + ex.Message);
            }
            return result;
        }


        public ResultDto ApplyReject(List<ScrapStopListDto> record, string hospitalId, string userName, string userNo)
        {
            ////权限判断
            //_authorityService.CheckUserMenuPermission(_sqlSugarUow, userNo, REJECT_PERMISSION_ID, hospitalId, isThrowException: true);
            var result = new ResultDto();
            try
            {
                var process_scrap = new List<EMS_SCRAP_LOG>();
                record.ForEach(item =>
                {
                    if (item.STATE == "1")
                    {
                        _sqlSugarUow.Db.Updateable<EMS_SCRAP_INFO>().SetColumns(p => new EMS_SCRAP_INFO
                        {
                            APPLY_STATE = "4",
                            EXAMINE_DATE = DateTime.Now,
                            EXAMINE_OPINION = item.EXAMINE_OPINION,
                            LAST_MPERSON = userName,
                            LAST_MTIME = DateTime.Now
                        }).Where(p => p.SCRAP_ID == item.ScrapStop_ID).ExecuteCommand();
                        process_scrap.Add(new EMS_SCRAP_LOG
                        {
                            SCRAP_PROCESS_ID = IDGenHelper.CreateGuid().ToString(),
                            HOSPITAL_ID = hospitalId,
                            SCRAP_ID = item.ScrapStop_ID,
                            PROCESS_STATE = "1",
                            PROCESS_TYPE = "审核驳回",
                            PROCESS_CONTENT = item.EXAMINE_OPINION,
                            OPER_PERSON = userName,
                            OPER_TIME = DateTime.Now,
                            FIRST_RPERSON = userName,
                            FIRST_RTIME = DateTime.Now,
                            LAST_MPERSON = userName,
                            LAST_MTIME = DateTime.Now,
                        });
                    }
                    if (item.STATE == "2")
                    {
                        _sqlSugarUow.Db.Updateable<EMS_SCRAP_INFO>().SetColumns(p => new EMS_SCRAP_INFO
                        {
                            APPLY_STATE = "5",
                            APPROVE_DATE = DateTime.Now,
                            APPROVE_OPINION = item.APPROVE_OPINION,
                            LAST_MPERSON = userName,
                            LAST_MTIME = DateTime.Now
                        }).Where(p => p.SCRAP_ID == item.ScrapStop_ID).ExecuteCommand();
                        process_scrap.Add(new EMS_SCRAP_LOG
                        {
                            SCRAP_PROCESS_ID = IDGenHelper.CreateGuid().ToString(),
                            HOSPITAL_ID = hospitalId,
                            SCRAP_ID = item.ScrapStop_ID,
                            PROCESS_STATE = "1",
                            PROCESS_TYPE = "审批驳回",
                            PROCESS_CONTENT = item.APPROVE_OPINION,
                            OPER_PERSON = userName,
                            OPER_TIME = DateTime.Now,
                            FIRST_RPERSON = userName,
                            FIRST_RTIME = DateTime.Now,
                            LAST_MPERSON = userName,
                            LAST_MTIME = DateTime.Now,
                        });
                    }
                });
                _sqlSugarUow.Db.Insertable(process_scrap).ExecuteCommand();
            }
            catch (Exception ex)
            {
                result.success = false;
                result.msg = "操作失败";
                _logger.LogError("操作失败:\n" + ex.Message);
            }
            return result;
        }


        public ResultDto ApplyRevoke(List<ScrapStopListDto> record, string userName, string userNo)
        {
            ////权限判断
            //_authorityService.CheckUserMenuPermission(_sqlSugarUow, userNo, REVOKE_PERMISSION_ID, isThrowException: true);
            ResultDto result = new ResultDto();
            try
            {
                var oldList = _sqlSugarUow.Db.Queryable<EMS_SCRAP_LOG>()
                .Where(p => p.PROCESS_STATE == "1")
                .ToList();
                record.ForEach(item =>
                {
                    var res = oldList.Where(p => p.SCRAP_ID == item.ScrapStop_ID)
                    .OrderByDescending(i => i.OPER_TIME)
                    .ToList();
                    if (res.Count() == 1)
                    {
                        _sqlSugarUow.Db.Updateable<EMS_SCRAP_INFO>().SetColumns(p => new EMS_SCRAP_INFO
                        {
                            APPLY_STATE = "0",
                            LAST_MPERSON = userName,
                            LAST_MTIME = DateTime.Now,
                        }).Where(p => p.SCRAP_ID == item.ScrapStop_ID).ExecuteCommand();

                        _sqlSugarUow.Db.Deleteable(res[0]).ExecuteCommand();
                    }
                    if (res.Count() > 1)
                    {
                        if (item.STATE == "2" || item.STATE == "4")
                        {
                            _sqlSugarUow.Db.Updateable<EMS_SCRAP_INFO>().SetColumns(p => new EMS_SCRAP_INFO
                            {
                                APPLY_STATE = "1",
                                APPROVE_PERSON = null,
                                APPROVE_PERSON_ID = null,
                                EXAMINE_OPINION = null,
                                EXAMINE_DATE = null,
                                LAST_MPERSON = userName,
                                LAST_MTIME = DateTime.Now
                            }).Where(p => p.SCRAP_ID == item.ScrapStop_ID).ExecuteCommand();

                            _sqlSugarUow.Db.Deleteable(res[0]).ExecuteCommand();
                        }
                        if (item.STATE == "3" || item.STATE == "5")
                        {
                            _sqlSugarUow.Db.Updateable<EMS_SCRAP_INFO>().SetColumns(p => new EMS_SCRAP_INFO
                            {
                                APPLY_STATE = "2",
                                APPROVE_DATE = null,
                                APPROVE_OPINION = null,
                                LAST_MPERSON = userName,
                                LAST_MTIME = DateTime.Now
                            }).Where(p => p.SCRAP_ID == item.ScrapStop_ID).ExecuteCommand();

                            _sqlSugarUow.Db.Updateable<EMS_EQUIPMENT_INFO>().SetColumns(p => new EMS_EQUIPMENT_INFO
                            {
                                EQUIPMENT_STATE = "1",
                                LAST_MPERSON = userName,
                                LAST_MTIME = DateTime.Now
                            }).Where(p => p.EQUIPMENT_ID == item.EQUIPMENT_ID).ExecuteCommand();

                            _sqlSugarUow.Db.Deleteable(res[0]).ExecuteCommand();
                        }
                        if (item.STATE == "1")
                        {
                            _sqlSugarUow.Db.Updateable<EMS_SCRAP_INFO>().SetColumns(p => new EMS_SCRAP_INFO
                            {
                                APPLY_STATE = "0",
                                LAST_MPERSON = userName,
                                LAST_MTIME = DateTime.Now
                            }).Where(p => p.SCRAP_ID == item.ScrapStop_ID).ExecuteCommand();
                            _sqlSugarUow.Db.Deleteable(res[0]).ExecuteCommand();

                        }
                        if (item.STATE == "0")
                        {
                            if (res[0].PROCESS_TYPE == "审核驳回")
                            {
                                _sqlSugarUow.Db.Updateable<EMS_SCRAP_INFO>().SetColumns(p => new EMS_SCRAP_INFO
                                {
                                    APPLY_STATE = "4",
                                    SCRAP_CAUSE = res[1].PROCESS_CONTENT,
                                    EXAMINE_DATE = res[0].OPER_TIME,
                                    EXAMINE_PERSON = res[0].OPER_PERSON,
                                    EXAMINE_OPINION = res[0].PROCESS_CONTENT,
                                    LAST_MPERSON = userName,
                                    LAST_MTIME = DateTime.Now,
                                    IF_RE_APPLY = (Convert.ToInt32(p.IF_RE_APPLY) - 1).ToString()
                                }).Where(p => p.SCRAP_ID == item.ScrapStop_ID).ExecuteCommand();

                            }
                            if (res[0].PROCESS_TYPE == "审批驳回")
                            {
                                _sqlSugarUow.Db.Updateable<EMS_SCRAP_INFO>().SetColumns(p => new EMS_SCRAP_INFO
                                {
                                    APPLY_STATE = "5",
                                    SCRAP_CAUSE = res[2].PROCESS_CONTENT,
                                    EXAMINE_DATE = res[1].OPER_TIME,
                                    EXAMINE_PERSON = res[1].OPER_PERSON,
                                    EXAMINE_OPINION = res[1].PROCESS_CONTENT,
                                    APPROVE_DATE = res[0].OPER_TIME,
                                    APPROVE_PERSON = res[0].OPER_PERSON,
                                    APPROVE_OPINION = res[0].PROCESS_CONTENT,
                                    LAST_MPERSON = userName,
                                    LAST_MTIME = DateTime.Now,
                                    IF_RE_APPLY = (Convert.ToInt32(p.IF_RE_APPLY) - 1).ToString()
                                }).Where(p => p.SCRAP_ID == item.ScrapStop_ID).ExecuteCommand();

                            }
                        }
                    }
                });
                result.success = true;
            }
            catch (Exception ex)
            {
                result.success = false;
                result.msg = "操作失败";
                _logger.LogError("操作失败:\n" + ex.Message);
            }
            return result; ;
        }


        public ResultDto ApplyAdopt(List<ScrapStopListDto> record, string hospitalId, string userName, string userNo, string file_upload_address)
        {
            ////权限判断
            //_authorityService.CheckUserMenuPermission(_sqlSugarUow, userNo, AUDIT_PERMISSION_ID, isThrowException: true);
            ResultDto result = new ResultDto();
            try
            {
                var process_scrap = new List<EMS_SCRAP_LOG>();
                record.ForEach(item =>
                {
                    if (item.STATE == "1")
                    {
                        _sqlSugarUow.Db.Updateable<EMS_SCRAP_INFO>().SetColumns(p => new EMS_SCRAP_INFO
                        {
                            APPLY_STATE = "2",
                            EXAMINE_DATE = DateTime.Now,
                            EXAMINE_OPINION = $"同意{item.EQUIPMENT_CODE}" + p.APPLY_TYPE,
                            APPROVE_PERSON = item.APPROVE_PERSON,
                            APPROVE_PERSON_ID = item.APPROVE_PERSON_ID,
                            LAST_MPERSON = userName,
                            LAST_MTIME = DateTime.Now
                        }).Where(p => p.SCRAP_ID == item.ScrapStop_ID).ExecuteCommand();

                        process_scrap.Add(new EMS_SCRAP_LOG
                        {
                            SCRAP_PROCESS_ID = IDGenHelper.CreateGuid().ToString(),
                            HOSPITAL_ID = hospitalId,
                            SCRAP_ID = item.ScrapStop_ID,
                            PROCESS_STATE = "1",
                            PROCESS_TYPE = "审核通过",
                            PROCESS_CONTENT = $"同意{item.EQUIPMENT_CODE}{item.APPLY_CLASS}。",
                            OPER_PERSON = userName,
                            OPER_TIME = DateTime.Now,
                            FIRST_RPERSON = userName,
                            FIRST_RTIME = DateTime.Now,
                            LAST_MPERSON = userName,
                            LAST_MTIME = DateTime.Now,
                        });
                    }
                    if (item.STATE == "2")
                    {
                        _sqlSugarUow.Db.Updateable<EMS_SCRAP_INFO>().SetColumns(p => new EMS_SCRAP_INFO
                        {
                            APPLY_STATE = "3",
                            APPROVE_DATE = DateTime.Now,
                            APPROVE_OPINION = $"同意{item.EQUIPMENT_CODE}" + p.APPLY_TYPE,
                            LAST_MPERSON = userName,
                            LAST_MTIME = DateTime.Now
                        }).Where(p => p.SCRAP_ID == item.ScrapStop_ID).ExecuteCommand();

                        process_scrap.Add(new EMS_SCRAP_LOG
                        {
                            SCRAP_PROCESS_ID = IDGenHelper.CreateGuid().ToString(),
                            HOSPITAL_ID = hospitalId,
                            SCRAP_ID = item.ScrapStop_ID,
                            PROCESS_STATE = "1",
                            PROCESS_TYPE = "审批通过",
                            PROCESS_CONTENT = $"同意{item.EQUIPMENT_CODE}" + item.APPLY_CLASS,
                            OPER_PERSON = userName,
                            OPER_TIME = DateTime.Now,
                            FIRST_RPERSON = userName,
                            FIRST_RTIME = DateTime.Now,
                            LAST_MPERSON = userName,
                            LAST_MTIME = DateTime.Now,
                        });
                        if (item.APPLY_CLASS == "停用")
                        {
                            var record_stop = new EMS_START_STOP();
                            record_stop.START_STOP_ID = IDGenHelper.CreateGuid().ToString();
                            record_stop.HOSPITAL_ID = hospitalId;
                            record_stop.EQUIPMENT_ID = item.EQUIPMENT_ID;
                            record_stop.START_CAUSE = item.APPLY_REASON;
                            record_stop.OPER_PERSON = item.APPLY_PERSON;
                            record_stop.OPER_TIME = item.APPLY_DATE;
                            record_stop.START_STOP_STATE = "1";
                            record_stop.START_STOP_TYPE = "0";
                            record_stop.START_DATE = item.SCRAP_DATE;
                            record_stop.FIRST_RPERSON = userName;
                            record_stop.FIRST_RTIME = DateTime.Now;
                            record_stop.LAST_MPERSON = userName;
                            record_stop.LAST_MTIME = DateTime.Now;
                            _sqlSugarUow.Db.Insertable(record_stop).ExecuteCommand();
                            _sqlSugarUow.Db.Updateable<EMS_EQUIPMENT_INFO>().SetColumns(p => new EMS_EQUIPMENT_INFO
                            {
                                EQUIPMENT_STATE = "2",
                                LAST_MPERSON = userName,
                                LAST_MTIME = DateTime.Now
                            }).Where(p => p.EQUIPMENT_ID == item.EQUIPMENT_ID).ExecuteCommand();
                        }
                        if (item.APPLY_CLASS == "报废")
                        {
                            _sqlSugarUow.Db.Updateable<EMS_EQUIPMENT_INFO>().SetColumns(p => new EMS_EQUIPMENT_INFO
                            {
                                EQUIPMENT_STATE = "3",
                                EQ_SCRAP_PERSON = userName,
                                EQ_SCRAP_TIME = item.SCRAP_DATE,
                                LAST_MPERSON = userName,
                                LAST_MTIME = DateTime.Now
                            }).Where(p => p.EQUIPMENT_ID == item.EQUIPMENT_ID).ExecuteCommand();
                        }
                        //报废停用文档生成
                        ResultDto result1 = CreateScrapFile(item.EQUIPMENT_ID, item.ScrapStop_ID, file_upload_address);
                        if (result1.success == true)
                        {
                            string file_path = result1.data.ToString().Split('.')[0].ToString();
                            _sqlSugarUow.Db.Updateable<EMS_SCRAP_INFO>().SetColumns(p => new EMS_SCRAP_INFO
                            {
                                FILE_PATH = file_path
                            }).Where(p => p.SCRAP_ID == item.ScrapStop_ID).ExecuteCommand();
                        }
                    }
                });
                _sqlSugarUow.Db.Insertable(process_scrap).ExecuteCommand();
                result.success = true;

            }
            catch (Exception ex)
            {
                result.success = false;
                result.msg = "操作失败";
                _logger.LogError("操作失败:\n" + ex.Message);
            }
            return result;
        }


        public ResultDto CreateScrapFile(string EQUIPMENT_ID, string ScrapStop_ID, string file_upload_address)
        {
            ResultDto result = new ResultDto();
            Spire.Doc.License.LicenseProvider.SetLicenseKey("Exgti80+VQEAWvzoJ2DBP9zbFeaWRiqJ/S7/AglNpZZMbHab9dehnYHo44VeuHrtD6stIccrs26WJqPdD782lV4VYGOWZaF1H+i1WGT1X9BdQ9h5p7QNXQeCHbCLH5oBErmfA7Y4eCsh5M3J1lmDVDFfNKQ9VS6jJjKFA7EgQOa2P4G26eoExTZewyaF3AIgftu4BuX/S3aXH5rVG6RtmwG0pLuTDVyn2mcVw1nYlzc1jPIvyzpAZNSehpG33QXRXMRo7Wp9POYi/egG8NhPpl2f85235tt0d3LXjnIDNZblQcXGzTdIIjX3QR+dsirkqVg7A4mGX3JA9W+M1iRdAfRz3P42Nh/AOFQ5sz1ZUAhfHpWwebFZ/u9AtqJe1CPTwgW1pyKZVyKBXTNITYLkK7priwixTOxnL2s5i6uT/gUFfYrTPJfP/Ynyqlb6DMxxiMGAjB0GzLe51Q57vGaCCGtivTacHWKML65k90FW3AAOhXdhwKqLyWy+amkcJg7lpW82ZsxHYERTZGkDwRf4EODIWbqhVc3x+rZUKabBZULB3LJK8TjRfp5A1LDLnqqom5L9ZFtR+cdG78wFuVCV9dXFY66AZhspaJp8ALj/jQyoRNfih2zBqkbGzvsVS1XkYVFG0AM4CAKUuY2sM0cHoPdqyI/N1s4Y16Ec66hJzYzIEJUmkQ25WGMQWYoZUQzjFyWIO6/tr6uiwu+q3dx/eXFOouQ5pqDDjqxEa4S7cL+FgWZmPIh5lu12lYGyk9ahgOi/1SBrRun3U9LJxPx2lg92XXHG+/yOoRBa2twX6Tx6fRMflERyRi+8SECJu8IoMjcTwW/zqEclKhe3Au9PpDuRZ9PBQt1LBCBbSaIuE2poeoQ3DWHUlHar9wjFX7RQVWrVEi5roaIrZMr1f63acNFil2U9f0VJSeYgIM1kM9gM5f4fxISh6A0pkdSO1SykBHqIQFkpxzbzPIU8KGEuTE/MnYOBkxXIdQZ1aMepYX+p5sv7Brf2vkU+y2JajxXusOdoVaGeiMIWrbMOF6D0CGpuFvKRiktIuxLX1FaKi4SvpNZAcvZgUReZO+HHEDS45jIPkCXiVjcA7R5OiwfAzvMFxI2IZW9oNY+se9OTf/7/UZ1rSETlCY0NbYmSVX9X4yeHSGL/IpqsdJN+JvbXjBb88UZgZN/M1Yu1f+dsMqf8n0Iz3IH9PFEw6KK1NpNj5tRq1FDV+Av8NJy07ovpYBuGitvZZCSixgcUfo92v1IFJ6sI4FYaodPo8OC58NGuB8jR4FZH3CDbW0CbAwRf0gmMdUw0UMJV1fKetp48b/K1/UtK4KyutIlYqMQQBZHlMe1iu0InccHEMOo/XMulrpM/phc8nvdKYWSnJcYQRtdyNIvmLWKnTUGmWDEUG/PEzJaDl3d6v4Q3dwlUb/aFqDPsrOsofNwG9CTs0B0z7Y71bW/nyeCE4Lk/iHUZmgK9Le1t1+vclc4yrSo2oOziyJ/fyqg00FSr13qazezxyVs=");


            //获取程序所在目录  
            string sourceName = "ScrapRecord.docx";
            string sourcePath = Path.Combine(System.Environment.CurrentDirectory, "ExampleFile");
            string localPath = Path.Combine(sourcePath, sourceName);
            //创建 Document 类的对象
            Document document = new Document();
            //从磁盘加载 Word 文档
            document.LoadFromFile(localPath);

            //更新书签
            SCRAP_STOP_FORM detail = _sqlSugarUow.Db.Queryable<EMS_SCRAP_INFO>()
                .InnerJoin<EMS_EQUIPMENT_INFO>((scr, eqt) => scr.EQUIPMENT_ID == eqt.EQUIPMENT_ID)
                .LeftJoin<SYS6_INSPECTION_PGROUP>((scr, eqt, pgroup) => eqt.UNIT_ID == pgroup.PGROUP_ID)
                .Where((scr, eqt, pgroup) => scr.SCRAP_ID == ScrapStop_ID)
                .Select((scr, eqt, pgroup) => new SCRAP_STOP_FORM
                {
                    EQUIPMENT_NAME = eqt.EQUIPMENT_NAME,
                    EQUIPMENT_CODE = eqt.EQUIPMENT_CODE,
                    EQUIPMENT_MODEL = eqt.EQUIPMENT_MODEL,
                    EQUIPMENT_CLASS = eqt.EQUIPMENT_CLASS,
                    PGROUP_NAME = pgroup.PGROUP_NAME,
                    MANUFACTURER = eqt.MANUFACTURER,
                    DEALER = eqt.DEALER,
                    EQ_OUT_TIME = eqt.EQ_OUT_TIME.Value.ToString("yyyy-MM-dd"),
                    EQ_IN_TIME = eqt.EQ_IN_TIME.Value.ToString("yyyy-MM-dd"),
                    ENABLE_TIME = eqt.ENABLE_TIME.Value.ToString("yyyy-MM-dd"),
                    APPLY_TYPE = scr.APPLY_TYPE,
                    OPER_PERSON = scr.OPER_PERSON,
                    OPER_TIME = scr.OPER_TIME.Value.ToString("yyyy-MM-dd"),
                    SCRAP_CAUSE = scr.SCRAP_CAUSE,
                    EXAMINE_PERSON = scr.EXAMINE_PERSON,
                    EXAMINE_DATE = scr.EXAMINE_DATE.Value.ToString("yyyy-MM-dd"),
                    EXAMINE_OPINION = scr.EXAMINE_OPINION,
                    APPROVE_OPINION = scr.APPROVE_OPINION,
                    APPROVE_PERSON = scr.APPROVE_PERSON,
                    APPROVE_DATE = scr.APPROVE_DATE.Value.ToString("yyyy-MM-dd"),
                }).First();
            var equipmentContext = new EquipmentContext(_sqlSugarUow);
            detail.EQUIPMENT_CLASS = equipmentContext.ExchangeEquipmentClass(detail.EQUIPMENT_CLASS,"");
            if (detail != null)
            {
                BookmarksNavigator bookmarksNavigator = new BookmarksNavigator(document);
                Type bookMarkType = typeof(SCRAP_STOP_FORM);
                PropertyInfo[] bookMarksProps = bookMarkType.GetProperties(BindingFlags.Public | BindingFlags.Instance);
                foreach (var prop in bookMarksProps)
                {
                    string bookValue = prop.GetValue(detail)?.ToString() ?? null;
                    //获取书签
                    if (document.Bookmarks.FindByName(prop.Name) != null)
                    {
                        bookmarksNavigator.MoveToBookmark(prop.Name);
                        bookmarksNavigator.InsertText(bookValue);
                    }
                }

                string fileName = ScrapStop_ID + "ScrapRecord" + "_" + Guid.NewGuid().ToString("N");
                string newFilePath = sourcePath + fileName + ".docx";
                //保存文档
                document.SaveToFile(newFilePath, Spire.Doc.FileFormat.PDF);
                FileStream pdfFs = new FileStream(newFilePath, FileMode.Open);
                byte[] bt = new byte[pdfFs.Length];
                pdfFs.Read(bt, 0, bt.Length);
                pdfFs.Close();
                string strBase = Convert.ToBase64String(bt);
                File.Delete(newFilePath);
                if (strBase != null)
                {
                    var obj = new
                    {
                        fileName = fileName + ".pdf",//文件名称
                        src = strBase,//baes64字符串
                        folderName = @"EMS/datafile",//文件夹路径
                        ifCover = true,
                    };
                    var jstr = JsonConvert.SerializeObject(obj);
                    result = _baseDataServices.UploadPathFile(jstr, "");
                }
                else
                {
                    result.success = false;
                }
            }
            else
            {
                result.success = false;
            }
            return result;
        }
    }
}
