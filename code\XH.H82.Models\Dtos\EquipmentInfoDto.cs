﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.Dtos
{
    public class EquipmentInfoDto
    {
        public string EQUIPMENT_STATE { get; set; }
        public string EQUIPMENT_STATE_VALUE { get; set; }
        public string EQUIPMENT_ID { get; set; }
        public string EQUIPMENT_NAME { get; set; }
        public string EQUIPMENT_ENAME { get; set; }
        public string HOSPITAL_NAME { get; set; }
        public string LAB_ID  { get; set; }
        public string LAB_NAME { get; set; }
        public string UNIT_ID { get; set; }
        public string EQUIPMENT_MODEL { get; set; }
        public string DEPT_SECTION_NO { get; set; }
        public string REGISTRATION_NUM { get; set; }
        public string REGISTRATION_ENUM { get; set; }
        public string EQUIPMENT_CODE { get; set; }
        public string KEEP_PERSON { get; set; }
        public string SECTION_NO { get; set; }
        public string INSTALL_AREA { get; set; }
        public string SERIAL_NUMBER { get; set; }
        public string VEST_PIPELINE { get; set; }
        public string EQUIPMENT_NUM { get; set; }
        public string EQUIPMENT_CLASS { get; set; }
        public string CONTACT_PHONE { get; set; }
        public string MGROUP_NAME { get; set; }
        public string MANUFACTURER { get; set; }
        public string DEALER { get; set; }
        public string ENABLE_TIME { get; set; }
        public string INSTALL_DATE { get; set; }
        public DateTime? NEXT_MAINTAIN_DATE { get; set; }
        public DateTime? NEXT_CORRECT_DATE { get; set; }
        public DateTime? NEXT_COMPARISON_DATE { get; set; }
        public DateTime? NEXT_VERIFICATION_DATE { get; set; }
        public DateTime? MAINTAIN_DATE { get; set; }
        public DateTime? CORRECT_DATE { get; set; }
        public DateTime? COMPARISON_DATE { get; set; }
        public DateTime? VERIFICATION_DATE { get; set; }
        public string? MAINTAIN_INTERVALS { get; set; }
        public string? COMPARISON_INTERVALS { get; set; }
        public string? VERIFICATION_INTERVALS { get; set; }
        public string? CORRECT_INTERVALS { get; set; }
        public string REMARK { get; set; }

        /// <summary>
        /// 经销商联系人
        /// </summary>
        public string CONTACT_NAME { get; set; }

        /// <summary>
        /// 经销商联系电话
        /// </summary>
        public string PHONE_NO { get; set; }

        /// <summary>
        /// 外观信息图片地址
        /// </summary>
        public List<string> ListPath { get; set; }

        /// <summary>
        /// 出厂日期
        /// </summary>
        public string? EQ_OUT_TIME { get; set; }
        /// <summary>
        /// 折旧日期
        /// </summary>

        public string DEPRECIATION_TIME { get; set; }

        /// <summary>
        /// 设备分类名称
        /// </summary>
        public string EQUIPMENT_CLASS_NAME { get; set; }
        /// <summary>
        /// 是否隐藏
        /// </summary>
        public string? IS_HIDE { get; set; }
        /// <summary>
        /// 使用年限
        /// </summary>
        public string? EQ_SERVICE_LIFE { get; set; }
        /// <summary>
        /// 生安标识
        /// </summary>
        public string? SMBL_FLAG { get; set; } = "0";
        /// <summary>
        /// 备案实验室ID
        /// </summary>
        public string? SMBL_LAB_ID { get; set; }
        
        /// <summary>
        /// 备案实验室
        /// </summary>
        public string? SMBL_LAB_NAME { get; set; }
        /// <summary>
        /// 生安设备类型id
        /// </summary>
        public string? SMBL_CLASS { get; set; }
        /// <summary>
        /// 生安状态
        /// </summary>
        public string? SMBL_STATE { get; set; }

        /// <summary>
        /// 生安标识+设备状态
        /// </summary>
        public string EQUIPMENT_STAT_SBML { get; set; }
    }
}
