﻿using System.ComponentModel.DataAnnotations;
using H.Utility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using XH.H82.API.Extensions;
using XH.H82.Base.Tree;
using XH.H82.IServices;
using XH.H82.Services.DeviceDataRefresh;

namespace XH.H82.API.Controllers.Organization
{

    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize]
    public class OrganizationTreeController: ControllerBase
    {
        private readonly ICurrencyService _currencyService;
        private readonly IHttpContextAccessor _httpContext;
        private EquipmentTreeContext _equipmentTreeContext;
        public OrganizationTreeController(ICurrencyService currencyService, IHttpContextAccessor httpContext, EquipmentTreeContext equipmentTreeContext)
        {
            _currencyService = currencyService;
            _httpContext = httpContext;
            _equipmentTreeContext = equipmentTreeContext;
        }
        
        
        /// <summary>
        /// ISO入口-申购记录页面左侧树
        /// </summary>
        /// <param name="labId"></param>
        /// <param name="areaId"> 院区Id </param>
        /// <param name="pgroupId"> 检验专业组Id</param>
        /// <returns></returns>
        [HttpGet("{labId}")]
        [CustomResponseType(typeof(List<AllTreeNode>))]
        public IActionResult SubscriptionTree( [Required]string labId , string? areaId ,string? pgroupId)
        {
            var tree =  _currencyService.GetISOBaseOrganizationTree(labId);

            if (areaId is not null)
            {
                tree.rootNode.CHILDREN.RemoveAll(areaNode=>areaNode.SOURCE_ID != areaId);
            }
            
            if (pgroupId is not null)
            {
                tree.rootNode.CHILDREN
                    .RemoveAll(areaNode =>
                    { 
                       var num = areaNode.CHILDREN.Count;
                       var deletedNodeNum = areaNode.CHILDREN.RemoveAll(mgNode =>
                       {
                           bool result = mgNode.CHILDREN.Any(x=>x.SOURCE_ID!= pgroupId);
                           mgNode.CHILDREN.RemoveAll(pgNode => pgNode.SOURCE_ID != pgroupId);
                           return result;
                       });
                       if (deletedNodeNum == num)
                       {
                           return true;
                       }
                       return false;
                    });
            }
            
            var result = _equipmentTreeContext.IsoSubscriptionTree(tree);
            tree.rootNode.ConutChildrensNoAppendRootPath();
            return Ok(tree.rootNode.ToResultDto());
        }


        /// <summary>
        /// ISO入口-设备档案页面左侧树
        /// </summary>
        /// <param name="labId">科室id</param>
        /// <param name="areaId"> 院区Id </param>
        /// <param name="pgroupId"> 检验专业组Id</param>
        /// <param name="onlySmbl">是否只显示生安设备</param>
        /// <param name="showHide">是否显示隐藏设备</param>
        /// <param name="hasClass">是否显示设备类型层级</param>
        /// <param name="equipmentCode">设备代号（自定义名称）</param>
        /// <returns></returns>
        [HttpGet("{labId}")]
        [CustomResponseType(typeof(List<AllTreeNode>))]
        public IActionResult EquipmentsTree( [Required]string labId ,  string? areaId ,string? pgroupId,bool onlySmbl = false, bool showHide = false, bool hasClass = false,string equipmentCode = null)
        {
            var tree =  _currencyService.GetISOBaseOrganizationTree(labId);
            if (areaId is not null)
            {
                tree.rootNode.CHILDREN.RemoveAll(areaNode=>areaNode.SOURCE_ID != areaId);
            }
            if (pgroupId is not null)
            {
                tree.rootNode.CHILDREN
                    .RemoveAll(areaNode =>
                    { 
                        var num = areaNode.CHILDREN.Count;
                        var deletedNodeNum = areaNode.CHILDREN.RemoveAll(mgNode =>
                        {
                            bool result = mgNode.CHILDREN.All(x=>x.SOURCE_ID!= pgroupId);
                            mgNode.CHILDREN.RemoveAll(pgNode => pgNode.SOURCE_ID != pgroupId);
                            return result;
                        });
                        if (deletedNodeNum == num)
                        {
                            return true;
                        }
                        return false;
                    });
            }
            var result = _equipmentTreeContext.IsoEquipmentsTree(tree,equipmentCode,onlySmbl,showHide, hasClass);
            result.RemoveAll(x => x.NUM == 0);
            result.ForEach(x=>x.CHILDREN.RemoveAll(y=>y.NUM==0));
            result.ForEach(x=>x.CHILDREN.ForEach(y=>y.CHILDREN.RemoveAll(z=>z.NUM == 0)));
            tree.rootNode.ConutChildrensNoAppendRootPath();
            return Ok(tree.rootNode.ToResultDto());
        }


        /// <summary>
        /// ISO入口-试剂信息页面左侧树
        /// </summary>
        /// <param name="labId"> 科室id</param>
        /// <param name="areaId"> 院区Id </param>
        /// <param name="pgroupId"> 检验专业组Id</param>
        /// <param name="onlySmbl">是否只显示生安设备</param>
        /// <param name="showHide">是否显示隐藏设备</param>
        /// <param name="hasClass">是否显示设备类型层级</param>
        /// <returns></returns>
        [HttpGet("{labId}")]
        [CustomResponseType(typeof(List<AllTreeNode>))]
        public IActionResult InstrumentEquipmentTree([Required]string labId ,  string? areaId ,string? pgroupId,bool onlySmbl = false, bool showHide = false, bool hasClass = false ,string equipmentCode = null)
        {
            var tree =  _currencyService.GetISOBaseOrganizationTree(labId);

            if (areaId is not null)
            {
                tree.rootNode.CHILDREN.RemoveAll(areaNode=>areaNode.SOURCE_ID != areaId);
            }
            
            if (pgroupId is not null)
            {
                tree.rootNode.CHILDREN
                    .RemoveAll(areaNode =>
                    { 
                        var num = areaNode.CHILDREN.Count;
                        var deletedNodeNum = areaNode.CHILDREN.RemoveAll(mgNode =>
                        {
                            bool result = mgNode.CHILDREN.All(x=>x.SOURCE_ID!= pgroupId);
                            mgNode.CHILDREN.RemoveAll(pgNode => pgNode.SOURCE_ID != pgroupId);
                            return result;
                        });
                        if (deletedNodeNum == num)
                        {
                            return true;
                        }
                        return false;
                    });
            }
            
            var result = _equipmentTreeContext.IsoInstrumentEquipmentsTree(tree,equipmentCode,onlySmbl,showHide,hasClass);
            result.RemoveAll(x => x.NUM == 0);
            result.ForEach(x=>x.CHILDREN.RemoveAll(y=>y.NUM==0));
            result.ForEach(x=>x.CHILDREN.ForEach(y=>y.CHILDREN.RemoveAll(z=>z.NUM == 0)));
            tree.rootNode.ConutChildrensNoAppendRootPath();
            return Ok(tree.rootNode.ToResultDto());
        }
        
        
        /// <summary>
        /// ISO入口-工作计划页面左侧树
        /// </summary>
        /// <param name="labId"></param>
        /// <param name="areaId"> 院区Id </param>
        /// <param name="pgroupId"> 检验专业组Id</param>
        /// <returns></returns>
        [HttpGet("{labId}")]
        [CustomResponseType(typeof(List<AllTreeNode>))]
        public IActionResult WorkPlanTree([Required]string labId , string? areaId ,string? pgroupId)
        {
            var tree =  _currencyService.GetISOBaseOrganizationTree(labId);
            if (areaId is not null)
            {
                tree.rootNode.CHILDREN.RemoveAll(areaNode=>areaNode.SOURCE_ID != areaId);
            }
            if (pgroupId is not null)
            {
                tree.rootNode.CHILDREN
                    .RemoveAll(areaNode =>
                    { 
                        var num = areaNode.CHILDREN.Count;
                        var deletedNodeNum = areaNode.CHILDREN.RemoveAll(mgNode =>
                        {
                            bool result = mgNode.CHILDREN.Any(x=>x.SOURCE_ID!= pgroupId);
                            mgNode.CHILDREN.RemoveAll(pgNode => pgNode.SOURCE_ID != pgroupId);
                            return result;
                        });
                        if (deletedNodeNum == num)
                        {
                            return true;
                        }
                        return false;
                    });
            }
            var result = _equipmentTreeContext.IsoWorkPlanTree(tree);
            tree.rootNode.ConutChildrensNoAppendRootPath();
            return Ok(tree.rootNode.ToResultDto());
        }

        /// <summary>
        /// ISO入口-报废停用页面左侧树
        /// </summary>
        /// <param name="labId"> 科室id</param>
        /// <param name="areaId"> 院区Id </param>
        /// <param name="pgroupId"> 检验专业组Id</param>
        /// <returns></returns>
        [HttpGet("{labId}")]
        [CustomResponseType(typeof(List<AllTreeNode>))]
        public IActionResult ScrapsOrStopTree([Required]string labId , string? areaId ,string? pgroupId)
        {
            var tree =  _currencyService.GetISOBaseOrganizationTree(labId);
            if (areaId is not null)
            {
                tree.rootNode.CHILDREN.RemoveAll(areaNode=>areaNode.SOURCE_ID != areaId);
            }
            if (pgroupId is not null)
            {
                tree.rootNode.CHILDREN
                    .RemoveAll(areaNode =>
                    { 
                        var num = areaNode.CHILDREN.Count;
                        var deletedNodeNum = areaNode.CHILDREN.RemoveAll(mgNode =>
                        {
                            bool result = mgNode.CHILDREN.Any(x=>x.SOURCE_ID!= pgroupId);
                            mgNode.CHILDREN.RemoveAll(pgNode => pgNode.SOURCE_ID != pgroupId);
                            return result;
                        });
                        if (deletedNodeNum == num)
                        {
                            return true;
                        }
                        return false;
                    });
            }
            var result = _equipmentTreeContext.IsoScrapsOrStopTree(tree);

            tree.rootNode.ConutChildrensNoAppendRootPath();

            return Ok(result.ToResultDto());
        }

        
        /// <summary>
        /// 查询科室-管理专业组-检验专业组树
        /// </summary>
        /// <param name="labId">科室id</param>
        /// <returns></returns>
        [HttpGet("{labId}")]
        [CustomResponseType(typeof( List<ITreeNode>))]
        public IActionResult GetISOLabBaseOrganizationTree(string labId)
        { 
            var result = _currencyService.GetISOLabBaseOrganizationTree(labId);
            result.rootNode.ConutChildrensNoAppendRootPath();
            return Ok(result.rootNode.CHILDREN.Success());
        }
        
    }
}
