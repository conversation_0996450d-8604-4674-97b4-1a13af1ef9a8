﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.Entities.Common
{
    public class EquipmentMgroupTreeDto
    {
        public int TOTAL_EQUIPMENT_AMOUNT { get; set; }
        public List<SECONDARY_NODE> SECONDARY_NODE { get; set; }
    }
    public class SECONDARY_NODE
    {
        public string MGROUP_ID { get; set; }
        public string MGROUP_NAME { get; set; }
        public int EQUIPMENT_AMOUNT { get; set; }
        public List<TERTIARY_NODE> TERTIARY_NODE { get; set; }
    }
    public class TERTIARY_NODE
    {
        public string EQUIPMENT_ID { get; set; }
        public string EQUIPMENT_NAME { get; set; }
        public string EQUIPMENT_CLASS { get; set; }
        public string EQUIPMENT_STATE { get; set; }
        
    }
    
}
