﻿using System.ComponentModel.DataAnnotations;
using H.Utility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Serilog;
using XH.H82.API.Extensions;
using XH.H82.IServices.DeviceDataRefresh;
using XH.H82.Models.Card;
using XH.H82.Models.DeviceRelevantInformation.Dto;
using XH.H82.Models.DeviceRelevantInformation.Enum;

namespace XH.H82.API.Controllers.EquipmentWarning
{
    /// <summary>
    /// 设备预警相关信息
    /// </summary>
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize]
    public class EquipmentWarningController : ControllerBase
    {
        private readonly IDeviceRefreshService _deviceRefreshService;
        private IInkScreenService _inkScreenService;

        public EquipmentWarningController(IDeviceRefreshService deviceRefreshService, IInkScreenService inkScreenService)
        {
            _deviceRefreshService = deviceRefreshService;
            _inkScreenService = inkScreenService;
        }


        /// <summary>
        /// 获取医疗机构下的专业组下拉
        /// </summary>
        /// <returns></returns>
        [HttpGet("{labId}")]
        [CustomResponseType(typeof(GroupDto))]
        public IActionResult GetGroups(string? labId)
        {
            var result = _inkScreenService.GetPgroups(labId);
            return Ok(result.ToResultDto());
        }


        /// <summary>
        /// 在线接口-获取当前用户所能查看到的设备预警
        /// </summary>
        /// <param name="areaId">院区id</param>
        /// <returns></returns>
        [HttpGet]
        [CustomResponseType(typeof(EquipmentWarnInfoDto))]
        [AllowAnonymous]
        public IActionResult GetEquipmentWarnInfo(string? areaId)
        {
            var hosptalId = User.ToClaimsDto().HOSPITAL_ID;
            var labId = "";
            var result = _deviceRefreshService.GetEquipmentWarnInfo(hosptalId, areaId, labId);

            return Ok(result.ToResultDto());
        }


        /// <summary>
        /// 获取水墨屏下拉列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [CustomResponseType(typeof(List<InkScreenDevice>))]
        public IActionResult GetInkScreenDevices()
        {
            var result = _inkScreenService.GetAllInkScreen();
            return Ok(result.ToResultDto());
        }


        /// <summary>
        /// 获取设备墨水屏列表
        /// </summary>
        /// <param name="lightState">亮灯状态</param>
        /// <param name="lineState">在线状态</param>
        /// <param name="equipmentState">设备状态</param>
        /// <param name="pGourpId"></param>
        /// <param name="macOrEquipmentCode">mac或者设备代号</param>
        /// <param name="hosptialId">医疗机构id</param>
        /// <returns></returns>
        [HttpGet]
        [CustomResponseType(typeof(List<InkScreenDto>))]
        public IActionResult GetInkScreens([Required] string hosptialId, LightStateEnum? lightState, LineStateEnum? lineState, EquipmentStateEnum? equipmentState, string? pGourpId, string? macOrEquipmentCode)
        {




            try
            {
                var result = _inkScreenService.GetInkScreens(User.ToClaimsDto().HOSPITAL_ID, lightState, lineState, equipmentState, pGourpId, macOrEquipmentCode);
                return Ok(result.ToResultDto());
            }
            catch (Exception e)
            {
                Log.Error($"GetInkScreens：{e.Message}");
                return Ok(new ResultDto()
                {
                    success = false,
                    msg = e.Message,
                });
            }


        }


        /// <summary>
        /// 获取设备历史报警信息
        /// </summary>
        /// <param name="equipmentId">设备id</param>
        /// <param name="warnMsg">报警信息</param>
        /// <param name="startTime">报警记录开始时间</param>
        /// <param name="endTime">报警记录结束时间</param>
        /// <returns></returns>
        [HttpGet]
        [CustomResponseType(typeof(List<WarnRecordDto>))]
        public IActionResult GetWquipmentWarnRecords(string? equipmentId, string? warnMsg, DateTime? startTime, DateTime? endTime)
        {
            try
            {
                var result = _inkScreenService.GetEquipmentWarnRecords(equipmentId, warnMsg, startTime, endTime);
                return Ok(result.ToResultDto());
            }
            catch (Exception e)
            {
                Log.Error($"GetWquipmentWarnRecords：{e.Message}");
                return Ok(new ResultDto()
                {
                    success = false,
                    msg = e.Message,
                });
            }
        }


        /// <summary>
        /// 墨水屏绑定信息列表
        /// </summary>
        /// <param name="isBand">是否绑定 </param>
        /// <param name="pGourpId">检验专业组id</param>
        /// <param name="mac">mac地址</param>
        /// <param name="hosptialId">医疗机构id</param>
        /// <returns></returns>
        [HttpGet]
        [CustomResponseType(typeof(List<InkScreenBindDto>))]
        public IActionResult GetInkScreenBinds([Required] string hosptialId, bool? isBand, string? pGourpId, string? mac)
        {
            try
            {
                var result = _inkScreenService.GetInkScreenBindEquipments(hosptialId, isBand, pGourpId, mac);
                return Ok(result.ToResultDto());
            }
            catch (Exception e)
            {
                Log.Error($"GetInkScreenBinds：{e.Message}");
                return Ok(new ResultDto()
                {
                    success = false,
                    msg = e.Message,
                });
            }

        }


        /// <summary>
        /// 查询可绑定墨水屏的设备列表
        /// </summary>
        /// <param name="pGourpId">专业组Id</param>
        /// <param name="codeOrModelOrnName">设备代号/型号/名称检索</param>
        /// <param name="hosptialId">医疗机构id</param>
        /// <param name="labId">科室id</param>
        /// <returns></returns>
        [HttpGet]
        [CustomResponseType(typeof(List<InkScreenBindEquipmentDto>))]
        public IActionResult GetInkScreenBindEquipments([Required] string hosptialId, string? labId, string? pGourpId, string? codeOrModelOrnName)
        {
            var user = User.ToClaimsDto();
            try
            {
                var result = _deviceRefreshService.GetInkScreenBindEquipments(user, hosptialId, labId, pGourpId, codeOrModelOrnName);
                return Ok(result.ToResultDto());
            }
            catch (Exception e)
            {
                Log.Error($"GetInkScreenBindEquipments异常：{e.Message}");
                return Ok(new ResultDto()
                {
                    success = false,
                    msg = e.Message,
                });
            }
        }


        /// <summary>
        /// 新增墨水屏信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [CustomResponseType(typeof(bool))]
        public IActionResult AddInkScreen([FromBody] AddInkScreenInput input)
        {
            try
            {
                _inkScreenService.AddNewInkScreen(input.Mac, input.InkScreenName, input.hosptialId, input.Remark);
                return Ok(new ResultDto()
                {
                    success = true,
                    msg = "新增成功"
                });
            }
            catch (BizException e)
            {
                Log.Error($"AddInkScreen：{e.Message}");
                return Ok(new ResultDto()
                {
                    success = false,
                    msg = $"新增操作失败：{e.Message}"
                });
            }

        }


        /// <summary>
        /// 删除墨水屏信息
        /// </summary>
        /// <returns></returns>
        [HttpGet("{inkScreenId}")]
        [CustomResponseType(typeof(bool))]
        public IActionResult DeleteInkScreen([Required] string inkScreenId)
        {
            try
            {
                _inkScreenService.DeleteInkScreen(inkScreenId);
                return Ok(new ResultDto()
                {
                    success = true,
                    msg = "删除成功"
                });
            }
            catch (BizException e)
            {
                Log.Error($"DeleteInkScreen：{e.Message}");
                return Ok(new ResultDto()
                {
                    success = false,
                    msg = $"删除操作失败：{e.Message}"
                });
            }

        }



        /// <summary>
        /// 保存墨水屏信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("{inkScreenId}")]
        [CustomResponseType(typeof(bool))]
        public IActionResult UpdateInkScreen([Required] string inkScreenId, [FromBody] UpdateInkScreenInput input)
        {
            try
            {
                _inkScreenService.UpdateInkScreenInfo(inkScreenId, input.Remark);
                return Ok(new ResultDto()
                {
                    success = true,
                    msg = "保存成功"
                });
            }
            catch (BizException e)
            {
                Log.Error($"UpdateInkScreen：{e.Message}");
                return Ok(new ResultDto()
                {
                    success = false,
                    msg = $"保存操作失败：{e.Message}"
                });
            }
        }

        /// <summary>
        /// 绑定设备设备信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("{inkScreenId}")]
        [CustomResponseType(typeof(bool))]
        public IActionResult BindEquipment([Required] string inkScreenId, [FromBody] BindInkScreen input)
        {
            try
            {
                _inkScreenService.BandEquipment(inkScreenId, input.EquipmentId);
                return Ok(new ResultDto()
                {
                    success = true,
                    msg = "绑定成功"
                });
            }
            catch (BizException e)
            {
                Log.Error($"BindEquipment：{e.Message}");
                return Ok(new ResultDto()
                {
                    success = false,
                    msg = $"绑定操作失败：{e.Message}"
                });
            }
        }


        /// <summary>
        /// 电量检测
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [CustomResponseType(typeof(bool))]
        public IActionResult PowerCheck()
        {
            try
            {
                _inkScreenService.CheckPower();
                return Ok(new ResultDto()
                {
                    success = true,
                    msg = "检测成功"
                });
            }
            catch (BizException e)
            {
                Log.Error($"PowerCheck：{e.Message}");
                return Ok(new ResultDto()
                {
                    success = false,
                    msg = $"{e.Message}"
                });
            }
        }


        /// <summary>
        /// 控灯操作
        /// </summary>
        /// <param name="inkScreenId">水墨屏id</param>
        /// <param name="lightStateEnum">0熄灯，1亮灯</param>
        /// <returns></returns>
        [HttpGet("{inkScreenId}")]
        [CustomResponseType(typeof(bool))]

        public IActionResult InkScreenlighted([Required] string inkScreenId, [Required] LightStateEnum lightStateEnum)
        {

            try
            {
                _inkScreenService.InkScreenDeviceLampControl(inkScreenId, lightStateEnum);
                return Ok(new ResultDto()
                {
                    success = true,
                    msg = "检测成功"
                });
            }
            catch (BizException e)
            {
                Log.Error($"InkScreenlighted：{e.Message}");
                return Ok(new ResultDto()
                {
                    success = false,
                    msg = $"{e.Message}"
                });
            }


        }
    }
}
