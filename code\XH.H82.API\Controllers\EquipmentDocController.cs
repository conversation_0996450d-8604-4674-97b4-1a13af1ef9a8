﻿using System.ComponentModel.DataAnnotations;
using System.Data;
using System.IO.Compression;
using System.Net;
using System.ServiceModel;
using System.Text;
using System.Xml.Serialization;
using EasyCaching.Core;
using ExcelDataReader;
using H.Utility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.Extensions.Caching.Memory;
using Newtonsoft.Json;
using ReportService;
using RestSharp;
using Spire.Pdf;
using Spire.Pdf.License;
using SqlSugar;
using XH.H82.API.Extensions;
using XH.H82.Base.Setup;
using XH.H82.Base.Tree;
using XH.H82.IServices;
using XH.H82.IServices.Sbml;
using XH.H82.Models.Dtos;
using XH.H82.Models.Entities;
using XH.LAB.UTILS.Models;
using IHostingEnvironment = Microsoft.AspNetCore.Hosting.IHostingEnvironment;

namespace XH.H82.API.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize]

    public class EquipmentDocController : ControllerBase
    {
        private readonly IEquipmentDocService _equipmentDocService;
        private readonly IXhEquipmentDocService _xhEquipmentDocService;
        private readonly IBaseService _baseService;
        private readonly IHostingEnvironment _hostingEnvironment;
        private readonly IModuleLabGroupService _IModuleLabGroupService;
        private readonly string file_preview_address;
        private readonly string file_upload_address;
        private readonly IConfiguration _configuration;
        private readonly IBaseDataServices _baseDataServices;
        private readonly IMemoryCache _easyCacheMemory;
        private string currentLabKey = "XH:LIS:H82:CURRENTLAB:UserSelectedLab:";
        private readonly string RedisModule;
        private readonly RestClient _clientS13;
        private readonly ISmblServicve _smblServicve;


        private XingHePrintServiceSoapClient _client;
        //接口服务地址
        private readonly string _serviceAddress;
        public EquipmentDocController(IEquipmentDocService equipmentDocService, IHostingEnvironment hostingEnvironment,
            IBaseService baseService, IModuleLabGroupService iModuleLabGroupService, IConfiguration configuration
            , IBaseDataServices baseDataServices, IMemoryCache easyCahceFactory, IXhEquipmentDocService xhEquipmentDocService,
            ISmblServicve smblServicve)
        {
            _equipmentDocService = equipmentDocService;
            _hostingEnvironment = hostingEnvironment;
            _baseService = baseService;
            _IModuleLabGroupService = iModuleLabGroupService;
            _configuration = configuration;
            file_preview_address = _configuration["S54"];
            file_upload_address = _configuration["S28"];
            _baseDataServices = baseDataServices;
            RedisModule = _configuration["RedisModule"];
            _easyCacheMemory = easyCahceFactory;

            var binding = new BasicHttpBinding();
            binding.MaxReceivedMessageSize = 241000000;
            _serviceAddress = configuration["ReportService"];
            _client = new XingHePrintServiceSoapClient(binding, new EndpointAddress(_serviceAddress));
            _xhEquipmentDocService = xhEquipmentDocService;
            _smblServicve = smblServicve;
        }


        /// <summary>
        ///   获取设备信息列表
        /// </summary>
        /// <param name="areaId">院区ID</param>
        /// <param name="state">设备状态</param>
        /// <param name="type">设备类型</param>
        /// <param name="mgroupId">管理专业组id</param>
        /// <param name="keyWord">检索关键字</param>
        /// <param name="pgroupId">检验专业组ID</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetEquipmentList(string areaId, string state, string type, string mgroupId, string keyWord, string pgroupId, string? isHidd)
        {
            var claims = User.ToClaimsDto();
            var clientMac = GetLocalMac.GetMac();
            currentLabKey = currentLabKey + claims.USER_NO + "." + clientMac;
            var labId = _easyCacheMemory.Get<string>(currentLabKey);
            if (mgroupId != null)
            {
                if (mgroupId.Contains("MG") == false)
                {
                    pgroupId = mgroupId;
                    mgroupId = null;
                }
            }
            var res = _equipmentDocService.GetEquipmentList(areaId, claims.USER_NO, claims.HOSPITAL_ID, state, type, mgroupId, keyWord, labId, pgroupId);
            var result = res.WhereIF(isHidd.IsNotNullOrEmpty(), x => x.IS_HIDE == isHidd)
                .ToList();
            return Ok(result.ToResultDto());
        }

        /// <summary>
        ///   获取设备信息
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetEquipmentInfo([BindRequired] string equipmentId)
        {
            var res = _equipmentDocService.GetEquipmentInfo(equipmentId);

            return Ok(res.ToResultDto());
        }


        /// <summary>
        ///   添加设备信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult AddEquipmentInfo([FromBody] EMS_EQUIPMENT_INFO record)
        {
            var claims = User.ToClaimsDto();
            record.EQUIPMENT_ID = IDGenHelper.CreateGuid();
            record.HOSPITAL_ID = claims.HOSPITAL_ID;
            record.FIRST_RPERSON = claims.HIS_NAME;
            var clientMac = GetLocalMac.GetMac();
            currentLabKey = currentLabKey + claims.USER_NO + "." + clientMac;
            var labId = _easyCacheMemory.Get<string>(currentLabKey);
            record.FIRST_RTIME = DateTime.Now;
            record.LAST_MPERSON = claims.HIS_NAME;
            record.LAST_MTIME = DateTime.Now;
            record.LAB_ID = labId;
            var res = _equipmentDocService.SaveEquipmentInfo(record);
            
            return Ok(res);
        }

        /// <summary>
        ///   修改设备信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UpdateEquipmentInfo([FromBody] EMS_EQUIPMENT_INFO record)
        {
            var claims = User.ToClaimsDto();
            record.LAST_MPERSON = claims.HIS_NAME; ;
            record.LAST_MTIME = DateTime.Now;
            if (record.SMBL_FLAG == "0")
            {
                record.SMBL_CLASS = null;
                record.SMBL_LAB_ID = null;
            }
            //生安 场景下 需要通知备案实验室 数据更新
            if (record.SMBL_LAB_ID.IsNotNullOrEmpty())
            {
                _smblServicve.UpdateSmblLabTime(record.SMBL_LAB_ID);
            }
            var res = _equipmentDocService.SaveEquipmentInfo(record);
            return Ok(res);
        }
        /// <summary>
        ///   删除设备信息
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <returns></returns>
        [HttpDelete]
        public IActionResult DeleteEquipmentInfo([BindRequired] string equipmentId)
        {
            var claims = User.ToClaimsDto();
            var userName = claims.HIS_NAME;
            var res = _equipmentDocService.DeleteEquipmentInfo(equipmentId, userName);
            return Ok(new ResultDto { success = res.success, msg = res.msg });
        }

        /// <summary>
        ///   获取供应商列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetCompanyList(string type, string serviceArea, string keyword)
        {
            var res = _equipmentDocService.GetCompanyList(type, serviceArea, keyword);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   获取联系人列表
        /// </summary>
        /// <param name="companyId">公司ID</param>
        /// <param name="equipmentId">设备ID</param>
        /// <param name="contactType">制造商类型</param>
        /// <param name="keyword">检索</param>
        /// <param name="contactState">联系人状态</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetCompanyContactList([BindRequired] string companyId, [BindRequired] string equipmentId, [BindRequired] string contactType, string contactState, string keyword)
        {
            var res = _equipmentDocService.GetCompanyContactList(companyId, equipmentId, contactType, contactState, keyword);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   修改联系人信息
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UpdateCompanyContact(List<SYS6_COMPANY_CONTACT> record, [BindRequired] string equipmentId, [BindRequired] string contactType)
        {
            var claims = User.ToClaimsDto();
            var userName = claims.HIS_NAME;
            var res = _equipmentDocService.UpdateCompanyContact(record, equipmentId, contactType, userName);
            return Ok(res);
        }

        /// <summary>
        ///   获取设备环境要求
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetEnviRequireInfo([BindRequired] string equipmentId)
        {
            var res = _equipmentDocService.GetEnviRequireInfo(equipmentId);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   添加设备环境要求
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult AddEnviRequireInfo([FromBody] EMS_ENVI_REQUIRE_INFO record)
        {
            var claims = User.ToClaimsDto();
            record.HOSPITAL_ID = claims.HOSPITAL_ID;
            record.FIRST_RPERSON = claims.HIS_NAME; ;
            record.FIRST_RTIME = DateTime.Now;
            record.LAST_MPERSON = claims.HIS_NAME; ;
            record.LAST_MTIME = DateTime.Now;
            record.EQUIPMENT_SIZE = record.LENGTH + "," + record.WIDTH + "," + record.HEIGHT;
            record.TEMPERATURE_REQUIRE = record.TEMP_MIN + "," + record.TEMP_MAX;
            record.HUMIDITY_REQUIRE = record.HUMI_MIN + "," + record.HUMI_MAX;
            var res = _equipmentDocService.SaveEnviRequireInfo(record);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   修改设备环境要求
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UpdateEnviRequireInfo([FromBody] EMS_ENVI_REQUIRE_INFO record)
        {
            var claims = User.ToClaimsDto();
            record.EQUIPMENT_SIZE = record.LENGTH + "," + record.WIDTH + "," + record.HEIGHT;
            record.TEMPERATURE_REQUIRE = record.TEMP_MIN + "," + record.TEMP_MAX;
            record.HUMIDITY_REQUIRE = record.HUMI_MIN + "," + record.HUMI_MAX;
            record.LAST_MPERSON = claims.HIS_NAME;
            record.LAST_MTIME = DateTime.Now;
            var res = _equipmentDocService.SaveEnviRequireInfo(record);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   获取设备申购信息
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetSubscribeInfo([BindRequired] string equipmentId)
        {
            var res = _equipmentDocService.GetSubscribeInfo(equipmentId);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   商务信息页面保存申购信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult SaveSubscribeInfo([FromBody] EMS_SUBSCRIBE_INFO record)
        {
            var claims = User.ToClaimsDto();
            record.FIRST_RPERSON = claims.HIS_NAME;
            record.FIRST_RTIME = DateTime.Now;
            record.LAST_MPERSON = claims.HIS_NAME;
            record.LAST_MTIME = DateTime.Now;
            var res = _equipmentDocService.SaveSubscribeInfo(record);
            
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   获取设备采购信息
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetPurchaseInfo([BindRequired] string equipmentId)
        {
            var res = _equipmentDocService.GetPurchaseInfo(equipmentId);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   添加设备采购信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult AddPurchaseInfo([FromBody] EMS_PURCHASE_INFO record)
        {
            var claims = User.ToClaimsDto();
            record.PURCHASE_ID = IDGenHelper.CreateGuid();
            record.HOSPITAL_ID = claims.HOSPITAL_ID;
            var clientMac = GetLocalMac.GetMac();
            currentLabKey = currentLabKey + claims.USER_NO + "." + clientMac;
            var labId = _easyCacheMemory.Get<string>(currentLabKey);
            record.FIRST_RPERSON = claims.HIS_NAME;
            record.FIRST_RTIME = DateTime.Now;
            record.LAST_MPERSON = claims.HIS_NAME;
            record.LAST_MTIME = DateTime.Now;
            var res = _equipmentDocService.SavePurchaseInfo(record);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   修改设备采购信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UpdatePurchaseInfo([FromBody] EMS_PURCHASE_INFO record)
        {
            var claims = User.ToClaimsDto();
            record.LAST_MPERSON = claims.HIS_NAME;
            record.LAST_MTIME = DateTime.Now;
            var res = _equipmentDocService.SavePurchaseInfo(record);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   获取设备安装信息
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetInstallInfo([BindRequired] string equipmentId)
        {
            var res = _equipmentDocService.GetInstallInfo(equipmentId);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   添加设备安装信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult AddInstallInfo([FromBody] EMS_INSTALL_INFO record)
        {
            var claims = User.ToClaimsDto();
            record.INSTALL_ID = IDGenHelper.CreateGuid().ToString(); ;
            record.HOSPITAL_ID = claims.HOSPITAL_ID;
            record.FIRST_RPERSON = claims.HIS_NAME;
            record.FIRST_RTIME = DateTime.Now;
            record.LAST_MPERSON = claims.HIS_NAME;
            record.LAST_MTIME = DateTime.Now;
            var res = _equipmentDocService.SaveInstallInfo(record);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   修改设备安装信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UpdateInstallInfo([FromBody] EMS_INSTALL_INFO record)
        {
            var claims = User.ToClaimsDto();
            record.LAST_MPERSON = claims.HIS_NAME;
            record.LAST_MTIME = DateTime.Now;
            var res = _equipmentDocService.SaveInstallInfo(record);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   获取设备开箱记录
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetUnpackInfo([BindRequired] string equipmentId)
        {
            var res = _equipmentDocService.GetUnpackInfo(equipmentId);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   添加设备开箱信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult AddUnpackInfo([FromBody] EMS_UNPACK_INFO record)
        {
            var claims = User.ToClaimsDto();
            record.UNPACK_ID = IDGenHelper.CreateGuid().ToString(); ;
            record.HOSPITAL_ID = claims.HOSPITAL_ID;
            record.FIRST_RPERSON = claims.HIS_NAME;
            record.FIRST_RTIME = DateTime.Now;
            record.LAST_MPERSON = claims.HIS_NAME;
            record.LAST_MTIME = DateTime.Now;
            var res = _equipmentDocService.SaveUnpackInfo(record);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   修改设备开箱信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UpdateUnpackInfo([FromBody] EMS_UNPACK_INFO record)
        {
            var claims = User.ToClaimsDto();
            record.LAST_MPERSON = claims.HIS_NAME;
            record.LAST_MTIME = DateTime.Now;
            var res = _equipmentDocService.SaveUnpackInfo(record);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   获取设备配件列表
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetPartsList([BindRequired] string equipmentId)
        {
            var res = _equipmentDocService.GetPartsList(equipmentId);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   添加设备配件信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult AddPartsInfo([FromBody] EMS_PARTS_INFO record)
        {
            var claims = User.ToClaimsDto();
            record.PARTS_ID = IDGenHelper.CreateGuid().ToString();
            record.PARTS_STATE = "1";
            record.HOSPITAL_ID = claims.HOSPITAL_ID;
            record.FIRST_RPERSON = claims.HIS_NAME;
            record.FIRST_RTIME = DateTime.Now;
            record.LAST_MPERSON = claims.HIS_NAME;
            record.FIRST_RTIME = DateTime.Now;
            var res = _equipmentDocService.SavePartsInfo(record);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   修改设备配件信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UpdatePartsInfo([FromBody] EMS_PARTS_INFO record)
        {
            var claims = User.ToClaimsDto();
            record.LAST_MPERSON = claims.HIS_NAME;
            record.LAST_MTIME = DateTime.Now;
            var res = _equipmentDocService.SavePartsInfo(record);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   删除设备配件信息
        /// </summary>
        /// <param name="partsId">设备配件ID</param>
        /// <returns></returns>
        [HttpDelete]
        public IActionResult DeletePartsInfo([BindRequired] string partsId)
        {
            var claims = User.ToClaimsDto();
            var userName = claims.HIS_NAME;
            var res = _equipmentDocService.DeletePartsInfo(partsId, userName);
            return Ok(res);
        }

        /// <summary>
        ///   获取设备培训记录列表
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetTrainList([BindRequired] string equipmentId)
        {
            var res = _equipmentDocService.GetTrainList(equipmentId);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   添加设备培训记录信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult AddTrainInfo([FromBody] EMS_TRAIN_INFO record)
        {
            var claims = User.ToClaimsDto();
            record.TRAIN_ID = IDGenHelper.CreateGuid().ToString(); ;
            record.HOSPITAL_ID = claims.HOSPITAL_ID;
            record.TRAIN_STATE = "1";
            record.FIRST_RPERSON = claims.HIS_NAME;
            record.FIRST_RTIME = DateTime.Now;
            record.LAST_MPERSON = claims.HIS_NAME;
            record.LAST_MTIME = DateTime.Now;
            var res = _equipmentDocService.SaveTrainInfo(record);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   修改设备培训记录信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UpdateTrainInfo([FromBody] EMS_TRAIN_INFO record)
        {
            var claims = User.ToClaimsDto();
            record.LAST_MPERSON = claims.HIS_NAME;
            record.LAST_MTIME = DateTime.Now;
            var res = _equipmentDocService.SaveTrainInfo(record);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   删除设备培训记录信息
        /// </summary>
        /// <param name="trainId">设备培训记录ID</param>
        /// <returns></returns>
        [HttpDelete]
        public IActionResult DeleteTrainInfo([BindRequired] string trainId)
        {
            var claims = User.ToClaimsDto();
            var userName = claims.HIS_NAME;
            var res = _equipmentDocService.DeleteTrainInfo(trainId, userName);
            return Ok(res);
        }

        /// <summary>
        ///   获取设备调试和运行记录
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetDebugInfo([BindRequired] string equipmentId)
        {
            var res = _equipmentDocService.GetDebugInfo(equipmentId);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   添加设备调试和运行记录
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult AddDebugInfo([FromBody] EMS_DEBUG_INFO record)
        {
            var claims = User.ToClaimsDto();
            record.DEBUG_ID = IDGenHelper.CreateGuid().ToString(); ;
            record.HOSPITAL_ID = claims.HOSPITAL_ID;
            record.FIRST_RPERSON = claims.HIS_NAME;
            record.FIRST_RTIME = DateTime.Now;
            record.LAST_MPERSON = claims.HIS_NAME;
            record.LAST_MTIME = DateTime.Now;
            var res = _equipmentDocService.SaveDebugInfo(record);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   修改设备调试和运行记录
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UpdateDebugInfo([FromBody] EMS_DEBUG_INFO record)
        {
            var claims = User.ToClaimsDto();
            record.LAST_MPERSON = claims.HIS_NAME;
            record.LAST_MTIME = DateTime.Now;
            var res = _equipmentDocService.SaveDebugInfo(record);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   获取授权记录列表
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetAuthorizeList([BindRequired] string equipmentId)
        {
            var res = _equipmentDocService.GetAuthorizeList(equipmentId);
            
            
            
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   添加授权记录
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult AddAuthorizeInfo([FromBody] EMS_AUTHORIZE_INFO record)
        {
            var claims = User.ToClaimsDto();
            record.AUTHORIZE_ID = IDGenHelper.CreateGuid();
            record.AUTHORIZE_STATE = "1";
            record.HOSPITAL_ID = claims.HOSPITAL_ID;
            record.FIRST_RPERSON = claims.HIS_NAME;
            record.FIRST_RTIME = DateTime.Now;
            record.LAST_MPERSON = claims.HIS_NAME;
            record.LAST_MTIME = DateTime.Now;
            var res = _equipmentDocService.SaveAuthorizeInfo(record);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   修改授权记录
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UpdateAuthorizeInfo([FromBody] EMS_AUTHORIZE_INFO record)
        {
            var claims = User.ToClaimsDto();
            record.LAST_MPERSON = claims.HIS_NAME;
            record.LAST_MTIME = DateTime.Now;
            var res = _equipmentDocService.SaveAuthorizeInfo(record);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   删除授权记录
        /// </summary>
        /// <param name="authorizeId">设备授权ID</param>
        /// <returns></returns>
        [HttpDelete]
        public IActionResult DeleteAuthorizeInfo([BindRequired] string authorizeId)
        {
            var claims = User.ToClaimsDto();
            var userName = claims.HIS_NAME;
            var res = _equipmentDocService.DeleteAuthorizeInfo(authorizeId, userName);
            return Ok(res);
        }

        /// <summary>
        ///   获取开机性能验证信息
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetStartupInfo([BindRequired] string equipmentId)
        {
            var res = _equipmentDocService.GetStartupInfo(equipmentId);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   添加开机性能验证信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult AddStartupInfo([FromBody] EMS_STARTUP_INFO record)
        {
            var claims = User.ToClaimsDto();
            record.STRATUP_ID = IDGenHelper.CreateGuid().ToString(); ;
            record.HOSPITAL_ID = claims.HOSPITAL_ID;
            record.FIRST_RPERSON = claims.HIS_NAME;
            record.FIRST_RTIME = DateTime.Now;
            record.LAST_MPERSON = claims.HIS_NAME;
            record.LAST_MTIME = DateTime.Now;
            var res = _equipmentDocService.SaveStartupInfo(record);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   修改开机性能验证信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UpdateStartupInfo([FromBody] EMS_STARTUP_INFO record)
        {
            var claims = User.ToClaimsDto();
            record.LAST_MPERSON = claims.HIS_NAME;
            record.LAST_MTIME = DateTime.Now;
            var res = _equipmentDocService.SaveStartupInfo(record);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   获取设备启停列表
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetStartStopList([BindRequired] string equipmentId)
        {
            var res = _equipmentDocService.GetStartStopList(equipmentId);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   添加设备启用信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult AddStartInfo([FromBody] EMS_START_STOP record)
        {
            var claims = User.ToClaimsDto();
            record.START_STOP_ID = IDGenHelper.CreateGuid().ToString();
            record.HOSPITAL_ID = claims.HOSPITAL_ID;
            record.OPER_PERSON = claims.HIS_NAME;
            record.OPER_TIME = DateTime.Now;
            //若启用时间为今天和以前，则state为0；若为将来，state为1
            record.START_STOP_STATE = record.START_DATE <= DateTime.Today ?
                "0" : "1";
            record.START_STOP_TYPE = "1";
            record.FIRST_RPERSON = claims.HIS_NAME;
            record.FIRST_RTIME = DateTime.Now;
            record.LAST_MPERSON = claims.HIS_NAME;
            record.LAST_MTIME = DateTime.Now;
            var res = _equipmentDocService.SaveStartInfo(record);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   修改设备启用信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UpdateStartInfo([FromBody] EMS_START_STOP record)
        {
            var claims = User.ToClaimsDto();
            record.LAST_MPERSON = claims.HIS_NAME;
            record.LAST_MTIME = DateTime.Now;
            var res = _equipmentDocService.SaveStartInfo(record);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   获取设备报废信息
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetScrapInfo([BindRequired] string equipmentId)
        {
            var res = _equipmentDocService.GetScrapInfo(equipmentId);
            return Ok(res.ToResultDto());
        }

        [HttpPost]
        public IActionResult UpdateScrapInfo([FromBody] EMS_SCRAP_INFO record)
        {
            var claims = User.ToClaimsDto();
            record.LAST_MPERSON = claims.HIS_NAME;
            record.LAST_MTIME = DateTime.Now;
            var res = _equipmentDocService.SaveScrapInfo(record);
            return Ok(new ResultDto { success = res.success, msg = res.msg });
        }

        /// <summary>
        ///   获取供应商信息列表
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <param name="state">状态</param>
        /// <param name="supplier">供应商类型</param>
        /// <param name="keyWord">检索</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetContactInfoList([BindRequired] string equipmentId, string state, string supplier, string keyWord)
        {
            var res = _equipmentDocService.GetContactInfoList(equipmentId, state, supplier, keyWord);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   添加联系人信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult AddContactInfo([FromBody] SYS6_COMPANY_CONTACT record)
        {
            var claims = User.ToClaimsDto();
            record.CONTACT_ID = IDGenHelper.CreateGuid().Substring(0,18);
            record.HOSPITAL_ID = claims.HOSPITAL_ID;
            record.CONTACT_STATE = "1";
            record.FIRST_RPERSON = claims.HIS_NAME;
            record.FIRST_RTIME = DateTime.Now;
            record.LAST_MPERSON = claims.HIS_NAME;
            record.LAST_MTIME = DateTime.Now;
            var res = _equipmentDocService.SaveContactInfo(record);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   修改联系人信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult SaveContactInfo([FromBody] SYS6_COMPANY_CONTACT record)
        {
            var claims = User.ToClaimsDto();
            record.LAST_MPERSON = claims.HIS_NAME;
            record.LAST_MTIME = DateTime.Now;
            var res = _equipmentDocService.SaveContactInfo(record);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   删除供应商联系信息
        /// </summary>
        /// <param name="contactId">供应商联系id</param>
        /// <returns></returns>
        [HttpDelete]
        public IActionResult DeleteContactInfo(string contactId)
        {
            var claims = User.ToClaimsDto();
            var userName = claims.HIS_NAME;
            var res = _equipmentDocService.DeleteContactInfo(contactId, userName);
            return Ok(res);
        }
        [HttpPost]
        public IActionResult ForbidContactPerson([FromBody] SYS6_COMPANY_CONTACT record, [BindRequired] string equipmentId)
        {
            var claims = User.ToClaimsDto();
            var userName = claims.HIS_NAME;
            var res = _equipmentDocService.ForbidContactPerson(equipmentId, userName, record);
            return Ok(res);
        }

        /// <summary>
        ///   获取设备标识卡
        /// </summary>
        /// <param name="cardType">标识卡类型（0：标准版；1：二维码版）</param>
        /// <param name="equipmentId">设备id列表</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult GetCardTemplate([BindRequired] string cardType, [BindRequired] List<string> equipmentId)
        {
            var claims = User.ToClaimsDto();
            //标识卡名
            //string cardName = "_" + (cardType == "0" ? "StandardCard" : "QRcodeCard") + ".png";
            string cardName = "_" + cardType + ".png";
            var result = new List<string>();
            var res = _equipmentDocService.GetCardElement(cardType, equipmentId);
            equipmentId.ForEach(item =>
            {
                var equiment = res.Where(p => p.EQUIPMENT_ID == item).FirstOrDefault();
                //标识卡地址
                var equipmentCode = equiment?.EQUIPMENT_CODE;
                string cardPath = Path.Combine(file_preview_address, "EMS/datafile", equipmentCode + cardName);
                //string cardPath = this.HttpContext.Request.Scheme + "://" + this.HttpContext.Request.Host + $"/api/uploads/cardTemplate/  equipmentCard/{item}/{item}{cardName}";
                result.Add(cardPath);
            });
            return Ok(new ResultDto()
            {
                data = result
            });
        }


        /// <summary>
        ///   生成标识卡
        /// </summary>
        /// <param name="cardType">标识卡类型（0：标准版；1：二维码版）</param>
        /// <param name="equipmentId">设备id</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult CreatCardTemplate([BindRequired] string cardType, [BindRequired] List<string> equipmentId)
        {
            var claims = User.ToClaimsDto();
            var res = _equipmentDocService.GetCardElement(cardType, equipmentId);
            equipmentId.ForEach(item =>
            {
                var itemRes = res.Where(p => p.EQUIPMENT_ID == item).FirstOrDefault();
                string printType = cardType;

                string cardName = itemRes.EQUIPMENT_CODE + "_" + printType + ".png";
                var serializer = new XmlSerializer(typeof(CardElementDto));
                var writer = new StringWriter();
                serializer.Serialize(writer, itemRes);
                var xmlString = writer.ToString();
                xmlString = xmlString.Replace("<?xml version=\"1.0\" encoding=\"utf-16\"?>", null).Replace("xsi:nil=\"true\"", null).ToString();

                //原逻辑：
                ////文件名
                //var itemRes = res.Where(p => p.EQUIPMENT_ID == item).FirstOrDefault();
                //string cardName = itemRes.EQUIPMENT_CODE + "_" + (cardType == "0" ? "StandardCard" : "QRcodeCard") + ".png";
                //var serializer = new XmlSerializer(typeof(CardElementDto));
                //var writer = new StringWriter();
                //serializer.Serialize(writer, itemRes);
                //var xmlString = writer.ToString();
                //xmlString = xmlString.Replace("<?xml version=\"1.0\" encoding=\"utf-16\"?>", null).Replace("xsi:nil=\"true\"", null).ToString();
                //var printType = cardType == "0" ? "StandardCard_preview" : "QRcodeCard_preview";
                ////var printType = cardType = "TEST_H82_StausCard";
                var resXml = $"<REQUEST><PRINT_ID></PRINT_ID><PRINT_TYPE>{printType}</PRINT_TYPE><PRINT_MODE>preview</PRINT_MODE><SAVE_MODE>2</SAVE_MODE><AREA_ID></AREA_ID><MODULE_ID>H82</MODULE_ID><CLIENT_IP>127.0.0.1</CLIENT_IP><MAC_ADDRESS>127.0.0.1</MAC_ADDRESS><DATA_SOURCE><![CDATA[<DATA>{xmlString}</DATA>]]></DATA_SOURCE></REQUEST>";
                var pdfBase64 = _equipmentDocService.CardPrintBase64(resXml);
                var obj = new
                {
                    fileName = cardName,//文件名称
                    src = pdfBase64,//baes64字符串
                    folderName = @"EMS/datafile",//文件夹路径
                    ifCover = true,
                };
                var jsonStr = JsonConvert.SerializeObject(obj);
                ResultDto result = _baseDataServices.UploadPathFile(jsonStr, "");
            });
            return Ok(new ResultDto { success = true, msg = "生成成功" });
        }

        /// <summary>
        /// 标识卡打印
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult CardPrint([BindRequired] string? cardType, List<string> equipmentId)
        {

            var res = _equipmentDocService.GetCardElement(cardType, equipmentId);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        /// 获取设备类型
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetCompanyClassList()
        {
            var res = _equipmentDocService.GetCompanyClass();
            return Ok(res.ToResultDto());
        }


        /// <summary>
        /// 解析excel文件
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult ParseExcel(IFormFile file)
        {

            Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
            DataTable dt = new DataTable();

            using (var stream = new MemoryStream())
            {
                file.CopyTo(stream);
                stream.Position = 0;
                using (var reader = ExcelReaderFactory.CreateReader(stream))
                {
                    var result = reader.AsDataSet();
                    dt = result.Tables[0];
                }
            }

            DataTable dtHead = dt.Copy();
            dtHead.Clear();
            dtHead.ImportRow(dt.Rows[0]);
            for (int i = dtHead.Columns.Count - 1; i >= 0; i--)
            {
                if (string.IsNullOrEmpty(dtHead.Rows[0][i].ToString()))
                {
                    dtHead.Columns.RemoveAt(i);
                }
            }
            dt.Rows.RemoveAt(0);

            for (int i = 0; i < dt.Rows.Count; i++)
            {
                Console.WriteLine(dt.Rows[i][0].ToString());
                dt.Rows[i][0] = dt.Rows[i][0].ToString().Replace("'", "").Replace("%", "");
            }

            var str = JsonConvert.SerializeObject(dt);
            var str1 = JsonConvert.SerializeObject(dtHead);



            if (dt.Rows.Count > 10000)
            {
                return Ok(new ResultDto()
                {
                    success = false,
                    msg = "单次上传不允许超过5000条记录"
                });
            }
            return Ok(new ResultDto()
            {
                data = str,
                data1 = str1
            });
        }

        [HttpPost]
        public IActionResult PdfToImage(dynamic pdfBase64)
        {
            LicenseProvider.SetLicenseKey("Exgti80+VQEAWvzoJ2DBP9zbFeaWRiqJ/S7/AglNpZZMbHab9dehnYHo44VeuHrtD6stIccrs26WJqPdD782lV4VYGOWZaF1H+i1WGT1X9BdQ9h5p7QNXQeCHbCLH5oBErmfA7Y4eCsh5M3J1lmDVDFfNKQ9VS6jJjKFA7EgQOa2P4G26eoExTZewyaF3AIgftu4BuX/S3aXH5rVG6RtmwG0pLuTDVyn2mcVw1nYlzc1jPIvyzpAZNSehpG33QXRXMRo7Wp9POYi/egG8NhPpl2f85235tt0d3LXjnIDNZblQcXGzTdIIjX3QR+dsirkqVg7A4mGX3JA9W+M1iRdAfRz3P42Nh/AOFQ5sz1ZUAhfHpWwebFZ/u9AtqJe1CPTwgW1pyKZVyKBXTNITYLkK7priwixTOxnL2s5i6uT/gUFfYrTPJfP/Ynyqlb6DMxxiMGAjB0GzLe51Q57vGaCCGtivTacHWKML65k90FW3AAOhXdhwKqLyWy+amkcJg7lpW82ZsxHYERTZGkDwRf4EODIWbqhVc3x+rZUKabBZULB3LJK8TjRfp5A1LDLnqqom5L9ZFtR+cdG78wFuVCV9dXFY66AZhspaJp8ALj/jQyoRNfih2zBqkbGzvsVS1XkYVFG0AM4CAKUuY2sM0cHoPdqyI/N1s4Y16Ec66hJzYzIEJUmkQ25WGMQWYoZUQzjFyWIO6/tr6uiwu+q3dx/eXFOouQ5pqDDjqxEa4S7cL+FgWZmPIh5lu12lYGyk9ahgOi/1SBrRun3U9LJxPx2lg92XXHG+/yOoRBa2twX6Tx6fRMflERyRi+8SECJu8IoMjcTwW/zqEclKhe3Au9PpDuRZ9PBQt1LBCBbSaIuE2poeoQ3DWHUlHar9wjFX7RQVWrVEi5roaIrZMr1f63acNFil2U9f0VJSeYgIM1kM9gM5f4fxISh6A0pkdSO1SykBHqIQFkpxzbzPIU8KGEuTE/MnYOBkxXIdQZ1aMepYX+p5sv7Brf2vkU+y2JajxXusOdoVaGeiMIWrbMOF6D0CGpuFvKRiktIuxLX1FaKi4SvpNZAcvZgUReZO+HHEDS45jIPkCXiVjcA7R5OiwfAzvMFxI2IZW9oNY+se9OTf/7/UZ1rSETlCY0NbYmSVX9X4yeHSGL/IpqsdJN+JvbXjBb88UZgZN/M1Yu1f+dsMqf8n0Iz3IH9PFEw6KK1NpNj5tRq1FDV+Av8NJy07ovpYBuGitvZZCSixgcUfo92v1IFJ6sI4FYaodPo8OC58NGuB8jR4FZH3CDbW0CbAwRf0gmMdUw0UMJV1fKetp48b/K1/UtK4KyutIlYqMQQBZHlMe1iu0InccHEMOo/XMulrpM/phc8nvdKYWSnJcYQRtdyNIvmLWKnTUGmWDEUG/PEzJaDl3d6v4Q3dwlUb/aFqDPsrOsofNwG9CTs0B0z7Y71bW/nyeCE4Lk/iHUZmgK9Le1t1+vclc4yrSo2oOziyJ/fyqg00FSr13qazezxyVs=");
            PdfDocument doc = new PdfDocument();
            string templatePath = Path.Combine(Environment.CurrentDirectory, "ExampleFile", "1.pdf");
            doc.LoadFromFile(templatePath);
            var ms = doc.SaveAsImage(0);
            //var ms = doc.SaveAsImage(0);
            //var bitmap = SKBitmap.Decode(ms);
            //var image = SKImage.FromBitmap(bitmap);
            //var data = image.Encode(SKEncodedImageFormat.Png, 100);
            //var memoryStream = new MemoryStream();
            //data.SaveTo(memoryStream);
            //var res = Convert.ToBase64String(memoryStream.ToArray());
            return Ok();
        }

        /// <summary>
        /// 获取设备导入模板
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetEquipmentTemplate()
        {
            var fileUrl = Path.Combine(Environment.CurrentDirectory, "ExampleFile");
            if (Directory.Exists(fileUrl) == false)
            {
                Directory.CreateDirectory(fileUrl);
            }
            var filePath = Path.Combine(fileUrl, "EquipmentImportTemplate.xls");
            FileStream fs = new FileStream(filePath, FileMode.OpenOrCreate, FileAccess.Read);
            if (fs.Length == 0)
            {
                throw new BizException("找不到模板，请通知管理员");
            }
            byte[] vs = new byte[fs.Length];
            while (true)
            {
                int r = fs.Read(vs, 0, vs.Length);
                if (r == 0)
                {
                    fs.Close();
                    break;
                }
            }
            return File(vs, "application/" + "vnd.ms-excel", "设备导入模板.xls");
        }

        /// <summary>
        /// 获取流水线下拉
        /// </summary>
        /// <param name="pgroupId">检验专业组ID</param>
        /// <param name="areaId">院区ID</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetPipelineList(string pgroupId, string areaId)
        {
            var claims = User.ToClaimsDto();
            var clientMac = GetLocalMac.GetMac();
            currentLabKey = currentLabKey + claims.USER_NO + "." + clientMac;
            var labId = _easyCacheMemory.Get<string>(currentLabKey);
            var res = _equipmentDocService.GetPipelineList(pgroupId, labId, areaId, claims.USER_NO);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        /// 导出设备清单
        /// </summary>
        /// <param name="record"></param>
        /// <param name="exportType">导出类型（1：word，2：pdf）</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult ExportDeviceList(List<EMS_EQUIPMENT_INFO> record, string exportType)
        {
            var res = _equipmentDocService.ExportDeviceList(record, exportType);
            var filePath = res.data.ToString();
            FileStream fs = new FileStream(filePath, FileMode.OpenOrCreate, FileAccess.Read);
            if (fs.Length == 0)
            {
                throw new BizException("清单生成失败");
            }
            byte[] vs = new byte[fs.Length];
            while (true)
            {
                int r = fs.Read(vs, 0, vs.Length);
                if (r == 0)
                {
                    fs.Close();
                    break;
                }
            }
            if (exportType == "1")
            {
                return File(vs, "application/" + "vnd.ms-word", "EquipmentList.docx");
            }
            return File(vs, "application/" + "vnd.ms-pdf", "EquipmentList.pdf");
        }

        /// <summary>
        /// 下载设备标识卡
        /// </summary>
        /// <param name="imageUrls"></param>
        /// <returns></returns>
        [HttpPost]
        [AllowAnonymous]
        public IActionResult DownloadEquipmentCard(List<string> imageUrls)
        {
            ServicePointManager.ServerCertificateValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true;
            using (WebClient webClient = new WebClient())
            {
                if (imageUrls.Count == 1)
                {
                    var imageUrl = imageUrls[0].Replace("\\", "/");
                    var imageName = imageUrl.Split("datafile/")[1];
                    byte[] imageData = webClient.DownloadData(imageUrl);
                    return File(imageData, "image/png", imageName);
                }
                else
                {
                    using (MemoryStream memoryStream = new MemoryStream())
                    {
                        using (ZipArchive zipArchive = new ZipArchive(memoryStream, ZipArchiveMode.Create, true))
                        {
                            foreach (string imageUrl in imageUrls)
                            {
                                var i = imageUrl.Replace("\\", "/");
                                byte[] imageBytes = webClient.DownloadData(i);
                                var fileName = i.Split("datafile/")[1];
                                ZipArchiveEntry entry = zipArchive.CreateEntry(fileName);
                                using (Stream entryStream = entry.Open())
                                {
                                    entryStream.Write(imageBytes, 0, imageBytes.Length);
                                }
                            }
                        }
                        byte[] zipBytes = memoryStream.ToArray();
                        var zipName = DateTime.Now.ToString("yyyyMMddHHmmss").ToString() + "标识卡.zip";
                        return File(zipBytes, "application/zip", zipName);
                    }
                }
            }
        }

        /// <summary>
        /// 分配备案实验室
        /// </summary>
        /// <param name="smblLabId">备案实验室id</param>
        /// <param name="equipmentIds">设备ids</param>
        /// <returns></returns>
        [HttpPost("{smblLabId}")]
        [CustomResponseType(typeof(bool))]
        public IActionResult DistributionEquipmentsToSmblLab([Required]string smblLabId , [Required] List<string> equipmentIds)
        {

            if (!equipmentIds.Any())
            {
                return Ok(true.ToResultDto());
            }
            _xhEquipmentDocService.DistributionEquipmentsToSmblLab(smblLabId, equipmentIds);
            _smblServicve.UpdateSmblLabTime(smblLabId);
            return Ok(true.ToResultDto());
        }
        
        /// <summary>
        /// 取消分配备案实验室
        /// </summary>
        /// <param name="equipmentIds">设备ids</param>
        /// <returns></returns>
        [HttpPost]
        [CustomResponseType(typeof(bool))]
        public IActionResult UnDistributionEquipmentsToSmblLab( [Required] List<string> equipmentIds)
        {

            if (!equipmentIds.Any())
            {
                return Ok(true.ToResultDto());
            }
            _xhEquipmentDocService.UnDistributionEquipmentsToSmblLab(equipmentIds);
            //todo 通知备案实验室
            return Ok(true.ToResultDto());
        }



    }
}
