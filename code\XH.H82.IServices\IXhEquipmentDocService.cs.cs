﻿using H.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XH.H82.Models.Dtos;
using XH.H82.Models.Entities;
using XH.LAB.UTILS.Models;
using XH.LAB.UTILS.Models.Dto;

namespace XH.H82.IServices
{
    public interface IXhEquipmentDocService
    {
        string GetAreaIdByGroupId(string pGroupid);
        ResultDto AutoDispatchUploadFile(Dictionary<string, object> dataDict, UploadZipDto zipFile);
        List<EMS_EQUIPMENT_INFO> GetEquipmentListByName(string hospitalId, string? areaId, string? userNo, string? state, string? type, string? mgroupId, string? equipName, string? labId, string? pgroupId);
        public ResultDto HideEquipment(string equipmentId, string userName);
        NewOATreeDto GetEquipmentListByMgroup(string userNo, string hospitalId, string equipmentNo, string labId, string areaId, string ifHide);
        NewEquipClassTreeDto GetEquipmentClassList(string userNo, string hospitalId, string equipmentNo, string labId, string areaId, string ifHide);

        /// <summary>
        /// 生成设备预览信息
        /// </summary>
        /// <param name="equipmentId">设备id</param>
        /// <param name="userName">操作人</param>
        /// <param name="hospitalId">设备id</param>
        /// <returns></returns>

        ResultDto GenerateEquipmentInfo(string equipmentId, string userName, string hospitalId);

        //List<CardElementDto> GetCardElement(string cardType, List<string> equipmentId);

        ResultDto ExportEquipmentList(List<ExportEquipmentDto> record, string labId, string hospitalId, string userName);
        List<CardTypeDto> GetCardTypeList();


        List<NewOATreeDto> GetEquipmentListByInspection(string userNo, string equipmentNo, string labId, string areaId, string mgroupid, string pgroupid , string? isHide , string? smblFlag);
        /// <summary>
        /// 分配备案实验室到设备
        /// </summary>
        /// <param name="smblLabId"></param>
        /// <param name="equipmentIds"></param>
        void DistributionEquipmentsToSmblLab(string smblLabId, List<string> equipmentIds);
        /// <summary>
        /// 取消分配备案实验室到设备
        /// </summary>
        /// <param name="equipmentIds"></param>
        void UnDistributionEquipmentsToSmblLab( List<string> equipmentIds);

    }
}
