﻿using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using Serilog;
using System.Security.Cryptography;
using System.Text;
using XH.H82.IServices;
using XH.H82.Models.DeviceRelevantInformation;
using XH.H82.Models.Entities;
using XH.H82.Models.Entities.Common;
using XH.H82.Models.SugarDbContext;
using XH.LAB.UTILS.Models;
using static iTextSharp.text.pdf.AcroFields;

namespace XH.H82.Services.DeviceDataRefresh
{


    public class DeviceSynchronizationContext
    {

        private readonly ISqlSugarUow<SugarDbContext_Master> _dbContext;
        EquipmentContext equipmentContext;
        public DeviceSynchronizationContext(ISqlSugarUow<SugarDbContext_Master> dbContext)
        {
            _dbContext = dbContext;
            equipmentContext = new EquipmentContext(_dbContext);
        }
        public void Synchronization(string labId, string hospitalId)
        {
            
            //检测仪器同步
            var equipment_insert = new List<EMS_EQUIPMENT_INFO>();
            var equipment_update = new List<EMS_EQUIPMENT_INFO>();
            var workPlan_insert = new List<EMS_WORK_PLAN>();
            //检测仪器列表
            var instruments = _dbContext.Db.Queryable<LIS6_INSTRUMENT_INFO>()
                .Where(a => a.INSTRUMENT_STATE == "1" && a.LAB_ID == labId)
                .ToList();
            var instrumentsModels = _dbContext.Db.Queryable<LIS6_SINSTRUMENT_INFO>().ToList();
            
            var equipments = _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>().ToList();
            
            var pipeliningList = _dbContext.Db.Queryable<LIS6_PIPELINING_INSTRUMENT>().ToList();
            
            //检验单元
            var groupInfo = _dbContext.Db.Queryable<LIS6_INSPECTION_GROUP>().ToList();
            
            //常规检测仪器类型
            var regularInstrumentClass = "1";
            
            foreach (var instrument in instruments)
            {
                //若系统管理配置了设备型号的自定义型号则取自定义型号 
                var model = instrumentsModels.Where(x => x.SINSTRUMENT_ID == instrument.SINSTRUMENT_ID).FirstOrDefault();
                var groupId = groupInfo.Where(p => p.GROUP_ID == instrument.GROUP_ID).FirstOrDefault()?.PGROUP_ID; 
                if (model is null)
                {
                    model = new();
                }
                
                if (instrument.EQUIPMENT_ID.IsNullOrEmpty() & instrument.EQUIPMENT_ID == "0")
                {
                    var id = IDGenHelper.CreateGuid().Substring(0, 18);
                    if (regularInstrumentClass != null)
                    {
                        //若系统管理配置了设备型号的自定义型号则取自定义型号
                        
                        equipment_insert.Add(new EMS_EQUIPMENT_INFO
                        {
                            EQUIPMENT_ID = id,
                            HOSPITAL_ID = instrument.HOSPITAL_ID,
                            UNIT_ID = groupId,
                            LAB_ID = instrument.LAB_ID,
                            EQUIPMENT_CLASS = regularInstrumentClass,
                            INSTRUMENT_ID = instrument.INSTRUMENT_ID,
                            EQUIPMENT_NAME = instrument.INSTRUMENT_NAME,
                            EQUIPMENT_MODEL = model.SINSTRUMENT_MODE,
                            EQUIPMENT_NUM = instrument.INSTRUMENT_SNUM,
                            EQUIPMENT_CODE = instrument.INSTRUMENT_CNAME,
                            EQUIPMENT_TYPE = instrument.INSTRUMENT_TYPE,
                            MANUFACTURER_ID = instrument.INSTRUMENT_FACTORY,
                            EQUIPMENT_STATE = instrument.INSTRUMENT_STATE ?? "1",
                            FIRST_RPERSON = "检测仪器同步数据",
                            FIRST_RTIME = DateTime.Now,
                            LAST_MPERSON = "检测仪器同步数据",
                            LAST_MTIME = DateTime.Now,
                            EQUIPMENT_UCODE = instrument.INSTRUMENT_CNAME,
                        });
                        workPlan_insert.Add(new EMS_WORK_PLAN
                        {
                            WORK_PLAN_ID = IDGenHelper.CreateGuid().ToString(),
                            WORK_PLAN_STATE = "1",
                            HOSPITAL_ID = instrument.HOSPITAL_ID,
                            EQUIPMENT_ID = id,
                            FIRST_RPERSON = "检测仪器同步数据",
                            FIRST_RTIME = DateTime.Now,
                            LAST_MPERSON = "检测仪器同步数据",
                            LAST_MTIME = DateTime.Now

                        });
                        _dbContext.Db.Updateable<LIS6_INSTRUMENT_INFO>().SetColumns(p => new LIS6_INSTRUMENT_INFO
                        {
                            EQUIPMENT_ID = id
                        }).Where(p => p.INSTRUMENT_ID == instrument.INSTRUMENT_ID).ExecuteCommand();
                    }
                }
                else
                {
                    var equipmentUpdate = equipments.Where(p => p.EQUIPMENT_ID == instrument.EQUIPMENT_ID).FirstOrDefault();
                    if (equipmentUpdate is not null)
                    {
                        var pipeId = pipeliningList.Where(p => p.INSTRUMENT_ID == equipmentUpdate.INSTRUMENT_ID).FirstOrDefault()?.PIPELINING_ID;
                        if (pipeId.IsNullOrEmpty())
                        {
                            equipmentUpdate.VEST_PIPELINE = null;
                        }
                        else
                        {
                            var vestPipeId = equipments.Where(p => p.INSTRUMENT_ID == pipeId).FirstOrDefault()?.EQUIPMENT_ID;
                            if (vestPipeId.IsNotNullOrEmpty())
                            {
                                equipmentUpdate.VEST_PIPELINE = vestPipeId;
                            }
                        }
                        //若系统管理配置了设备型号的自定义型号则取自定义型号
                        equipmentUpdate.UNIT_ID = groupId;
                        equipmentUpdate.LAB_ID = instrument.LAB_ID;
                        equipmentUpdate.EQUIPMENT_MODEL = model.SINSTRUMENT_MODE;
                        equipmentUpdate.EQUIPMENT_NUM = instrument.INSTRUMENT_SNUM;
                        equipmentUpdate.EQUIPMENT_CODE = instrument.INSTRUMENT_CNAME;
                        equipmentUpdate.EQUIPMENT_TYPE = instrument.INSTRUMENT_TYPE;
                        equipmentUpdate.EQUIPMENT_CLASS = regularInstrumentClass;
                        equipmentUpdate.LAST_MTIME = DateTime.Now;
                        equipmentUpdate.EQUIPMENT_UCODE = equipmentUpdate.EQUIPMENT_UCODE.IsNullOrEmpty()
                            ? equipmentUpdate.EQUIPMENT_CODE
                            : equipmentUpdate.EQUIPMENT_UCODE;
                        equipment_update.Add(equipmentUpdate);
                    }
                }
            }

            _dbContext.Db.Insertable(equipment_insert).ExecuteCommand();
            _dbContext.Db.Insertable(workPlan_insert).ExecuteCommand();
            _dbContext.Db.Updateable(equipment_update).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommand();
        }
        private bool NeedSynchronizationUnitId(string id)
        {
            const string KEY = "H0701003";
            var result = false;

            var SetUpValue1 = _dbContext.Db.Queryable<SYS6_SETUP>().Where(x => x.UNIT_ID == id && x.SETUP_NO == KEY).First();
            var SetUpValue2 = _dbContext.Db.Queryable<SYS6_SETUP_DICT>().Where(x => x.SETUP_NO == KEY && x.SETUP_STATE == "1").First();
            if (SetUpValue1 is not null)
            {
                result = SetUpValue1.SETUP_VALUE == "1";
            }
            if (SetUpValue2 is not null)
            {
                result = SetUpValue2.DEFAULT_VALUE == "1";
            }
            return result;
        }
    }
}
