﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.Entities.OperationLog
{
    /// <summary>
    /// 流转操作枚举
    /// </summary>
    public enum OperationStateEnum
    {
        /// <summary>
        /// 驳回
        /// </summary>
        [Description("驳回")]
        Overruled = 0,
        /// <summary>
        /// 未提交
        /// </summary>
        [Description("未提交")]
        NotSubmitted = 1,
        /// <summary>
        /// 已提交
        /// </summary>
        [Description("已提交")]
        Submitted = 2,
        /// <summary>
        /// 已审核
        /// </summary>
        [Description("已审核")]

        Audited = 3,
    }
}
