﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.Entities.Common
{
    public class EquipmentClassTreeDto
    {
        public int TOTAL_EQUIPMENT_AMOUNT { get; set; }
        public List<SECONDARY_CLASS_NODE> SECONDARY_CLASS_NODE { get; set; }
    }
    public class SECONDARY_CLASS_NODE
    {
        public string EQUIPMENT_CLASS { get; set; }
        public string EQUIPMENT_CLASS_ID { get; set; }
        public string EQUIPMENT_CLASS_ENAME { get;set; }
        public int EQUIPMENT_AMOUNT { get; set; }
        public List<TERTIARY_CLASS_NODE> TERTIARY_CLASS_NODE { get; set; }
    }
    public class TERTIARY_CLASS_NODE
    {
        public string EQUIPMENT_ID { get; set; }
        public string EQUIPMENT_NAME { get; set; }
        public string EQUIPMENT_MGROUP { get; set; }
        public string EQUIPMENT_STATE { get; set; }
    }
}
