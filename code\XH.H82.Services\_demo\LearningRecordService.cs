﻿using H.Utility;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using RestSharp;
using Serilog;
using System.Diagnostics;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;
using XH.H82.IServices._demo;
using XH.H82.Models.Demo;

namespace XH.H82.Services._demo
{
    public class LearningRecordService : ILearningRecordService
    {
        private readonly IHttpContextAccessor _httpContext;
        private readonly IConfiguration _configuration;

        private string H91Address { get; set; } = "";

        public LearningRecordService(IConfiguration configuration, IHttpContextAccessor httpContext)
        {
            H91Address = configuration["H91"];
            _httpContext = httpContext;
        }

        public List<UserLearningRecords> GetUserLearningRecords(string labId, string userNo, string postId = null, bool isLearned = true)
        {
            var startDate = DateTime.Now.AddYears(-50).ToString("yyyy-MM");
            var endDate = DateTime.Now.AddYears(50).ToString("yyyy-MM");
            var urlH9101 = $"/api/Dmis/GetJobLogDocInfo?lab_id={labId}&firstmenukey=H9101&class_id&user_id={userNo}&start_time={startDate}&end_time={endDate}&doc_name=&learning_state={isLearned}"; //体系
            var urlH9102 = $"/api/Dmis/GetJobLogDocInfo?lab_id={labId}&firstmenukey=H9102&class_id&user_id={userNo}&start_time={startDate}&end_time={endDate}&doc_name=&learning_state={isLearned}"; //非体系
            var urlH9103 = $"/api/Dmis/GetJobLogDocInfo?lab_id={labId}&firstmenukey=H9103&class_id&user_id={userNo}&start_time={startDate}&end_time={endDate}&doc_name=&learning_state={isLearned}"; //资料库

            var urls = new Dictionary<string, string>();
            urls.Add("体系文件", urlH9101);
            urls.Add("非体系文件", urlH9102);
            urls.Add("资料库", urlH9103);
            var result = new List<UserLearningRecords>();

            foreach (var url in urls)
            {
                try
                {
                    var rep = H91ClientGet(url.Value);
                    var repData = JsonConvert.DeserializeObject<List<LearningDto>>(rep.data.ToString());
                    var userLearned = new UserLearningRecords()
                    {
                        PostId = $"岗位ID",
                        PostName = $"岗位名称",
                    };
                    foreach (var item in repData)
                    {
                        var task = new LearningTask()
                        {
                            TaskName = $"【{url.Key}-{item.ClassName}】{item.PgroupName}-{item.DocTypeName}",
                        };
                        task.TaskContents.Add(new TaskContent()
                        {
                            State = item.LearningTime < item.StudyDuration ? "已完成" : "学习中",
                            FileName = item.DocName,
                            LearnedTime = item.LearningTime,
                            TotalTime = item.StudyDuration,
                        });
                        userLearned.Tasks.Add(task);
                    }
                    result.Add(userLearned);
                }
                catch (Exception)
                {
                    continue;
                }
            }
            return result;
        }

        private ResultDto H91ClientGet(string url)
        {
            return H91ClientGet<object>(url);
        }

        private ResultDto H91ClientGet<T>(string url, T requestBody = default(T))
        {
            if (H91Address.IsNullOrEmpty())
            {
                throw new ArgumentNullException("addressH91 为空");
            }

            using RestClient client = new RestClient(new RestClientOptions
            {
                RemoteCertificateValidationCallback = (object sender, X509Certificate? certificate, X509Chain? chain, SslPolicyErrors sslPolicyErrors) => true,
                BaseUrl = new Uri(H91Address),
                ThrowOnAnyError = true
            });
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            string value = _httpContext.HttpContext.Request.Headers.Authorization.ToString();
            RestRequest request = new RestRequest(url);
            if (requestBody != null)
            {
                request.AddBody(requestBody);
            }

            request.AddHeader("Authorization", value);
            try
            {
                RestResponse<ResultDto> restResponse = client.ExecuteGet<ResultDto>(request);
                stopwatch.Stop();
                Log.ForContext("elapsed", stopwatch.ElapsedMilliseconds).Information($"调用H91模块[{url}],耗时:{stopwatch.ElapsedMilliseconds}ms");
                if (restResponse.IsSuccessful && restResponse.Data.success)
                {
                    return restResponse.Data;
                }

                Log.Error($"调用H91模块[{url}]发生错误:{restResponse.ErrorException}");
                throw new BizException($"调用H91模块[{url}]发生错误:{restResponse.ErrorException}", restResponse.ErrorException);
            }
            catch (Exception ex)
            {
                Log.Error($"调用H91模块[{url}]发生错误:{ex}");
                throw new BizException(ex.Message);
            }
        }
    }
}