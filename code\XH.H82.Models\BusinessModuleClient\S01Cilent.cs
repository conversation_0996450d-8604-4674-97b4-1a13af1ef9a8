﻿using H.Utility;
using H.Utility.Helper;
using Microsoft.AspNetCore.Http;
using RestSharp;
using Serilog;
using System.Diagnostics;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;

namespace XH.H82.Models.BusinessModuleClient
{
    public class S01Cilent
    {
        private string addressS01 { get; set; } = "";
        private IHttpContextAccessor _httpContext;


        public S01Cilent(string ip, IHttpContextAccessor httpContext)
        {
            if (ip.IsNullOrEmpty())
            {
                throw new ArgumentNullException("addressH115 为空");
            }
            addressS01 = ip;
            _httpContext = httpContext;

        }

        public ResultDto S01ClientGet<T>(string url, T requestBody = default(T), bool isNeedToken = true)
        {
            if (addressS01.IsNullOrEmpty())
            {
                throw new ArgumentNullException("addressS01 为空");
            }

            using RestClient client = new RestClient(new RestClientOptions
            {
                RemoteCertificateValidationCallback = (object sender, X509Certificate? certificate, X509Chain? chain, SslPolicyErrors sslPolicyErrors) => true,
                BaseUrl = new Uri(addressS01),
                ThrowOnAnyError = true
            });
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            string value = _httpContext.HttpContext.Request.Headers.Authorization.ToString();
            RestRequest request = new RestRequest(url);
            if (requestBody != null)
            {
                request.AddBody(requestBody);
            }
            if (isNeedToken)
            {
                request.AddHeader("Authorization", value);
            }
            try
            {
                RestResponse<ResultDto> restResponse = client.ExecuteGet<ResultDto>(request);
                stopwatch.Stop();
                Log.ForContext("elapsed", stopwatch.ElapsedMilliseconds).Information($"调用S01模块[{url}],耗时:{stopwatch.ElapsedMilliseconds}ms");
                if (restResponse.IsSuccessful && restResponse.Data.success)
                {
                    return restResponse.Data;
                }

                Log.Error($"调用S01模块[{url}]发生错误:{restResponse.ErrorException}");
                throw new BizException($"调用S01模块[{url}]发生错误:{restResponse.ErrorException}", restResponse.ErrorException);
            }
            catch (Exception ex)
            {
                Log.Error($"调用S01模块[{url}]发生错误:{ex}");
                throw new BizException(ex.Message);
            }
        }

        public ResultDto S01ClientPost<T>(string url, T requestBody = default(T), bool isNeedToken = true)
        {

            if (addressS01.IsNullOrEmpty())
            {
                throw new BizException("addressS01 为空");
            }

            using RestClient client = new RestClient(new RestClientOptions
            {
                RemoteCertificateValidationCallback = (object sender, X509Certificate? certificate, X509Chain? chain, SslPolicyErrors sslPolicyErrors) => true,
                BaseUrl = new Uri(addressS01),
                ThrowOnAnyError = true
            });
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            RestRequest request = new RestRequest(url);
            if (requestBody != null)
            {
                request.AddBody(requestBody);
            }

            if (isNeedToken)
            {
                string token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();
                request.AddHeader("Authorization", token);
            }
            try
            {
                RestResponse<ResultDto> restResponse = client.ExecutePost<ResultDto>(request);
                stopwatch.Stop();
                Log.ForContext("elapsed", stopwatch.ElapsedMilliseconds).Information($"调用S01模块[{url}],耗时:{stopwatch.ElapsedMilliseconds}ms");
                if (restResponse.IsSuccessful && restResponse.Data.success)
                {
                    return restResponse.Data;
                }

                Log.Error($"调用S01模块[{url}]发生错误:{restResponse.ErrorException}");
                throw new BizException($"调用S01模块[{url}]发生错误:{restResponse.ErrorException}", restResponse.ErrorException);
            }
            catch (Exception ex)
            {
                Log.Error($"调用S01模块[{url}]发生错误:{ex}");
                throw new BizException(ex.Message);
            }
        }



        public ResultDto GetCustomToken(ThirdVerifyInfoDto infoDto)
        {
            string url = $"/api/Account/GetCustomToken";
            return S01ClientPost(url, infoDto, false);
        }

    }

}
