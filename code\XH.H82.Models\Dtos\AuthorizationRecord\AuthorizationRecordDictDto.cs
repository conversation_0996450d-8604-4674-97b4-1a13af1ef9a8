using System.ComponentModel.DataAnnotations;
using AutoMapper;
using AutoMapper.Configuration.Annotations;
using XH.H82.Models.BusinessModuleClient;

namespace XH.H82.Models.Dtos.AuthorizationRecord;
[AutoMap(typeof(OA_BASE_DATA))]
public class AuthorizationRecordDictDto
{
    public string DATA_ID { get; set; }
    [SourceMember("DATA_NAME")]
    public string CONTENT { get; set; }

    public string REMARK { get; set; }
    public string FIRST_RTIME { get; set; }
}

/// <summary>
/// 
/// </summary>
/// <param name="Content">字典内容</param>
/// <param name="Remark">备注</param>
public record AuthorizationRecordDictInput([Required] string Content, string? Remark);

