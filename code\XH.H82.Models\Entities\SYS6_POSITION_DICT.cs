﻿using H.Utility.SqlSugarInfra;
using NPOI.SS.Formula.Functions;
using SqlSugar;

namespace XH.H82.Models.Entities;

[DBOwner("XH_SYS")]
[SugarTable("SYS6_POSITION_DICT")]
public class SYS6_POSITION_DICT : BaseField
{
    /// <summary>
    /// 位置id
    /// </summary>
    [SugarColumn(IsPrimaryKey = true)]
    public string POSITION_ID { get; set; }
    /// <summary>
    /// 机构id
    /// </summary>
    public string HOSPITAL_ID { get; set; }
    /// <summary>
    /// 科室id
    /// </summary>
    public string LAB_ID { get; set; }
    /// <summary>
    /// 院区id
    /// </summary>
    public string AREA_ID { get; set; }
    /// <summary>
    /// 位置名称
    /// </summary>
    public string POSITION_NAME { get; set; }
    /// <summary>
    /// 楼号
    /// </summary>
    public string TOWER_NO { get; set; }
    /// <summary>
    /// 层数
    /// </summary>
    public string FLOOR_NO { get; set; }
    /// <summary>
    /// 房号
    /// </summary>
    public string ROOM_NO { get; set; }
    /// <summary>
    /// 排序号
    /// </summary>
    public string POSITION_SORT { get; set; }
    /// <summary>
    /// 备案实验室id
    /// </summary>
    public string SMBL_LAB_ID { get; set; }
    /// <summary>
    /// 状态 0 禁用   1再用  2 删除
    /// </summary>
    public string POSITION_STATE { get; set; }

}