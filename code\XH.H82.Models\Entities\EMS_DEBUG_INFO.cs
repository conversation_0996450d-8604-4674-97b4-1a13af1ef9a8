﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using H.Utility.SqlSugarInfra;
using SqlSugar;

namespace XH.H82.Models.Entities
{
    [DBOwner("XH_OA")]
    public class EMS_DEBUG_INFO
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string DEBUG_ID { get; set; }
        public string HOSPITAL_ID { get; set; }
        [Required(ErrorMessage = "设备id不能为空")]
        public string EQUIPMENT_ID { get; set; }
        public DateTime? DEBUG_DATE { get; set; }
        public string DEBUG_PERSON { get; set; }
        public string LAB_PERSON { get; set; }
        public string HOSPITAL_PERSON { get; set; }
        public string DEBUG_CONDITION { get; set; }
        public string ENGINEER { get; set; }
        public string RELATION_WAY { get; set; }
        public string DEBUG_STATE { get; set; }
        public string FIRST_RPERSON { get; set; }
        public DateTime? FIRST_RTIME { get; set; }
        public string LAST_MPERSON { get; set; }
        public DateTime? LAST_MTIME { get; set; }
        public string REMARK { get; set; }

    }
}
