﻿using EasyCaching.Core;
using H.Utility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.Extensions.Caching.Memory;
using XH.H82.Base.Setup;
using XH.H82.IServices;
using XH.H82.Models.Entities;

namespace XH.H82.API.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize]

    public class SubscribeController : ControllerBase
    {
        private readonly ISubscribeService _subscribeService;
        private readonly string file_preview_address;
        private readonly string file_upload_address;
        private readonly IConfiguration _configuration;
        private readonly IMemoryCache _easyCacheMemory;
        private string currentLabKey = "XH:LIS:H82:CURRENTLAB:UserSelectedLab:";
        private readonly string RedisModule;
        public SubscribeController(ISubscribeService subscribeService, IConfiguration configuration, IMemoryCache easyCahceFactory
)
        {
            _subscribeService = subscribeService;
            _configuration = configuration;
            file_preview_address = _configuration["S54"];
            file_upload_address = _configuration["S28"];
            RedisModule = _configuration["RedisModule"];

            _easyCacheMemory = easyCahceFactory;
        }

        /// <summary>
        ///   获取申购信息列表
        /// </summary>
        /// <param name="startTime">开始日期</param>
        /// <param name="endTime">结束日期</param>
        /// <param name="mgroupId">管理专业组ID</param>
        /// <param name="state">状态</param>
        /// <param name="search">检索内容</param>
        /// <param name="pgroupId">检验专业组ID</param>
        /// <param name="smblFlag">生安安全标识</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetSubscribeInfoList([BindRequired] DateTime startTime, [BindRequired] DateTime endTime, string mgroupId, string state, string search, string pgroupId, [BindRequired] string areaId , string? smblFlag )
        {
            var claims = User.ToClaimsDto();
            var clientMac = GetLocalMac.GetMac();
            currentLabKey = currentLabKey + claims.USER_NO + "." + clientMac;
            var labId = _easyCacheMemory.Get<string>(currentLabKey);
            if (mgroupId != null)
            {
                if (mgroupId.Contains("MG") == false)
                {
                    pgroupId = mgroupId;
                    mgroupId = null;
                }
            }
            var res = _subscribeService.GetSubscribeInfoList(startTime, endTime, claims.USER_NO
                , claims.HOSPITAL_ID, mgroupId, state, search, labId, pgroupId, areaId);
            foreach (var item in res)
            {
                item.SMBL_FLAG = item.SMBL_FLAG == "1" ? "1" : "0";
            }
            if (smblFlag is not null)
            {
                res = res.Where(x => x.SMBL_FLAG == smblFlag).ToList();
            }
            return Ok(res.ToResultDto());
        }


        /// <summary>
        ///   添加申购信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult AddSubscribeInfo([FromBody] EMS_SUBSCRIBE_INFO record)
        {
            var claims = User.ToClaimsDto();
            record.SUBSCRIBE_ID = IDGenHelper.CreateGuid();
            var clientMac = GetLocalMac.GetMac();
            currentLabKey = currentLabKey + claims.USER_NO + "." + clientMac;
            record.LAB_ID = _easyCacheMemory.Get<string>(currentLabKey);
            record.HOSPITAL_ID = claims.HOSPITAL_ID;
            record.EQUIPMENT_ID = ",";
            record.FIRST_RPERSON = claims.HIS_NAME;
            record.FIRST_RTIME = DateTime.Now;
            record.LAST_MPERSON = claims.HIS_NAME;
            record.LAST_MTIME = DateTime.Now;
            if (record.SMBL_LAB_ID.IsNotNullOrEmpty())
            {
                record.SMBL_FLAG = "1";
            }
            var res = _subscribeService.SaveSubscribeInfo(record);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   修改申购信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UpdateSubscribeInfo([FromBody] EMS_SUBSCRIBE_INFO record)
        {
            var claims = User.ToClaimsDto();
            record.LAST_MPERSON = claims.HIS_NAME;
            record.LAST_MTIME = DateTime.Now;
            if (record.SMBL_LAB_ID.IsNotNullOrEmpty())
            {
                record.SMBL_FLAG = "1";
            }
            var res = _subscribeService.SaveSubscribeInfo(record);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   删除申购信息
        /// </summary>
        /// <param name="subscribeId">申购项目编号</param>
        /// <returns></returns>
        [HttpDelete]
        public IActionResult DeleteSubscribeInfo([BindRequired] string subscribeId)
        {
            var claims = User.ToClaimsDto();
            var userName = claims.HIS_NAME;
            var res = _subscribeService.DeleteSubscribeInfo(subscribeId, userName);
            return Ok(res);
        }

        /// <summary>
        ///   申购流程
        /// </summary>
        /// <param name="subscribeId">申购项目id</param>
        /// <returns></returns>

        [HttpGet]
        public IActionResult GetSubscribeProcess([BindRequired] string subscribeId)
        {
            var res = _subscribeService.GetSubscribeProcess(subscribeId);
            return Ok(res.ToResultDto());
        }
    }
}
