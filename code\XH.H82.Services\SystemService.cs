﻿using System.Diagnostics;
using System.ServiceModel;
using System.Text;
using System.Xml;
using AutoMapper;
using H.BASE;
using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using H.Utility.Helper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RestSharp;
using Serilog;
using XH.H82.IServices;
using XH.H82.Models.Dtos.Base;
using XH.H82.Models.SugarDbContext;
using XH.LAB.UTILS.Interface;
using XH.LAB.UTILS.Models;
using XingHePlatform;

namespace XH.H82.Services
{
    public class SystemService : ISystemService
    {
        private readonly IMapper _mapper;
        private readonly IConfiguration _configuration;
        private readonly ILogger<SystemService> _logger;
        private readonly IHttpContextAccessor _httpContext;
        private readonly XingHePlatformSoapClient _clientXhPlatform;
        private readonly IAuthorityService2 _authorityService;
        private readonly RestClient _clientH04;
        private readonly RestClient _clientS01;
        private readonly RestClient _clientS10;
        private readonly RestClient _clientH5702;
        private readonly ISqlSugarUow<SugarDbContext_Master> _sqlSugarUow;

        public SystemService(
            IConfiguration configuration,
            ILogger<SystemService> logger,
            IHttpContextAccessor httpContext,
            IAuthorityService2 authorityService,
            ISqlSugarUow<SugarDbContext_Master> sqlSugarUow,
            IMapper mapper
        )
        {
            var addressS10 = configuration["S10"]; //5.0接口地址
            var addressH04 = configuration["H04-13"]; //检验工具箱
            var addressS01 = configuration["UrlModuleS01"]; //S01
            var addressH5702 = configuration["H57-02"];
            _configuration = configuration;
            _logger = logger;
            _httpContext = httpContext;
            _authorityService = authorityService;
            _sqlSugarUow = sqlSugarUow;
            _mapper = mapper;
            try
            {
                var binding = new BasicHttpBinding();
                binding.MaxReceivedMessageSize = 241000000;
                _clientXhPlatform = new XingHePlatformSoapClient(
                    binding,
                    new EndpointAddress(addressS10)
                );
            }
            catch (Exception e)
            {
                throw new BizException("接口服务初始化出错:" + e.Message);
            }
            _clientH04 = new RestClient(
                new RestClientOptions()
                {
                    RemoteCertificateValidationCallback = (
                        sender,
                        certificate,
                        chain,
                        sslPolicyErrors
                    ) => true,
                    BaseUrl = new Uri(addressH04),
                    ThrowOnAnyError = true,
                }
            );
            _clientS01 = new RestClient(
                new RestClientOptions()
                {
                    RemoteCertificateValidationCallback = (
                        sender,
                        certificate,
                        chain,
                        sslPolicyErrors
                    ) => true,
                    BaseUrl = new Uri(addressS01),
                    ThrowOnAnyError = true,
                }
            );
            _clientS10 = new RestClient(
                new RestClientOptions()
                {
                    RemoteCertificateValidationCallback = (
                        sender,
                        certificate,
                        chain,
                        sslPolicyErrors
                    ) => true,
                    BaseUrl = new Uri(addressS10),
                    ThrowOnAnyError = true,
                }
            );
            if (addressH5702.IsNotNullOrEmpty())
            {
                _clientH5702 = new RestClient(
                    new RestClientOptions()
                    {
                        RemoteCertificateValidationCallback = (
                            sender,
                            certificate,
                            chain,
                            sslPolicyErrors
                        ) => true,
                        BaseUrl = new Uri(addressH5702),
                        ThrowOnAnyError = true,
                    }
                );
            }
        }

        /// <summary>
        /// 调用接口返回原始数据
        /// </summary>
        /// <param name="headXml"></param>
        /// <param name="bodyXml"></param>
        /// <param name="dataformat"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public string CallXhPlatformInterfaceSource(
            string headXml,
            string bodyXml,
            out string dataformat
        )
        {
            string format = "JSON";
            string methodCode = "";
            Stopwatch sw = new Stopwatch();
            sw.Start();
            //头部新增签名节点
            XmlDocument xmlHead = new XmlDocument();
            xmlHead.LoadXml(headXml);
            XmlNode headRoot = xmlHead.SelectSingleNode("Root");
            //接口以后可能会需要鉴权,此处加入签名字段
            XmlElement node = xmlHead.CreateElement("Sign");
            node.InnerText = SmxUtilsHelper.SM3Utils(
                AppSettingsProvider.XingHePlatFormKey + bodyXml
            );
            headRoot.AppendChild(node); //
            //没有定义数据格式节点的,自动加入<DataFormat>JSON</DataFormat>以返回json格式数据(接口默认返回xml)
            var formater = headRoot.SelectNodes("DataFormat");
            if (formater.Count == 0)
            {
                XmlElement nodeFormat = xmlHead.CreateElement("DataFormat");
                nodeFormat.InnerText = "JSON";
                headRoot.AppendChild(nodeFormat);
            }
            else
            {
                format = formater[0].InnerText.ToUpper();
            }

            var MethodCode = headRoot.SelectSingleNode("MethodCode");
            if (MethodCode == null)
            {
                throw new BizException("接口HeadXml中MethodCode未定义");
            }
            else
            {
                methodCode = MethodCode.InnerText;
            }

            if (format != "JSON" && format != "XML")
            {
                throw new BizException($"5.0接口:不支持的DataFormat类型:{format}");
            }
            dataformat = format;

            headXml = XMLHelper.ConvertXmlToString(xmlHead);
            var resFromInterface = _clientXhPlatform
                .CallInterfaceAsync(headXml, bodyXml)
                .Result.Body.CallInterfaceResult;
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .ForContext("SourceContext", "XinghePlatform")
                .ForContext("HeadXml", headXml)
                .ForContext("BodyXml", bodyXml)
                .Information($"调用接口方法:{methodCode},耗时:{sw.ElapsedMilliseconds}ms");

            return resFromInterface;
        }

        /// <summary>
        /// 直接取Items内容并转为T
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="headXml"></param>
        /// <param name="bodyXml"></param>
        /// <returns>T</returns>
        public T CallXhPlatformInterface<T>(string headXml, string bodyXml)
            where T : class
        {
            var res = this.CallXhPlatformInterfaceSource(headXml, bodyXml, out string dataformat);
            string code = "";
            string message = "";
            string data = "";
            JObject obj;
            if (dataformat == "XML")
            {
                var doc = new XmlDocument();
                doc.LoadXml(res);
                obj = JObject.Parse(JsonConvert.SerializeXmlNode(doc));
                code = obj["Response"]["ResultCode"].ToString();
                message = obj["Response"]["ResultMsg"].ToString();
                if (code == "1")
                {
                    data = obj["Response"]["Items"]?["Item"].ToString();
                }
            }
            if (dataformat == "JSON")
            {
                obj = JObject.Parse(res);
                code = obj["ResultCode"].ToString();
                message = obj["ResultMsg"].ToString();
                if (code == "1")
                {
                    data = obj["Items"].ToString();
                }
            }

            if (code == "-1")
            {
                throw new BizException("接口调用成功,但是返回了错误信息:" + message);
            }

            if (code == "0")
            {
                //无数据
                return null;
            }
            //Xml转json时,如果只有一个子节点,此节点的数据讲返回一个对象,而不是array 为了统一 此处强行转array
            if (data.Substring(0, 1) == "{")
                data = "[" + data + "]";

            return JsonHelper.FromJson<T>(data.ToString());
        }

        public object GetAllConfig(
            string hospitalId,
            string moduleId,
            string pageId,
            string? qunitId
        )
        {
            hospitalId.CheckNotNullOrEmpty("hospitalId");
            hospitalId.CheckNotNullOrEmpty("moduleId");
            hospitalId.CheckNotNullOrEmpty("pageId");

            var token = _httpContext.HttpContext.Request.Headers.Authorization;
            Stopwatch sw = new Stopwatch();
            sw.Start();

            string url =
                $"/externalapi/External/GetSetItemsByPageId?hospitalId={hospitalId}&moduleId={moduleId}&pageId={pageId}&qunitId={qunitId}";

            RestRequest request = new RestRequest(url);
            request.AddHeader("Authorization", "Bearer " + token);
            var response = _clientH04.ExecuteGet<ResultDto>(request);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用H04模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
            if (response.ErrorException != null)
            {
                Log.Error($"调用H04模块[{url}]发生错误:{response.ErrorException}");
                throw new BizException(
                    $"调用H04模块[{url}]发生错误:{response.ErrorException}",
                    response.ErrorException
                );
            }
            else
            {
                if (response.Data.success)
                {
                    return response.Data.data;
                }
                else
                {
                    Log.Error($"调用H04模块[{url}]请求完成,但是返回了错误:" + response.Data.msg);
                    return null;
                }
            }
        }

        public object GetSingleConfig(
            EnumSetupType stepType,
            string hospitalId,
            string moduleId,
            string setupName,
            string? qunitId
        )
        {
            Stopwatch sw = new Stopwatch();
            sw.Start();
            string url = "";
            switch (stepType)
            {
                case EnumSetupType.表格设置:
                    url = "/externalapi/External/GetXhGridConfigJsonObj";
                    break;
                case EnumSetupType.条件设置:
                    url = "/externalapi/External/GetXhConditonConfigJsonObj";
                    break;
                case EnumSetupType.tab页设置:
                    url = "/externalapi/External/GetReportUnitTabJsonObj";
                    break;
                case EnumSetupType.表单设置:
                    url = "/externalapi/External/GetFormConfig";
                    break;
                case EnumSetupType.显示设置:
                    url = "/externalapi/External/GetDisplayInfoConfig";
                    break;
            }
            url +=
                $"?hospitalId={hospitalId}&moduleId={moduleId}&setupName={setupName}&qunitId={qunitId}";
            RestRequest request = new RestRequest(url);
            var response = _clientH04.ExecuteGet<ResultDto>(request);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用H04模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
            if (response.ErrorException != null)
            {
                Log.Error($"调用H04模块[{url}]发生错误:{response.ErrorException}");
                throw new BizException(
                    $"调用H04模块[{url}]发生错误:{response.ErrorException}",
                    response.ErrorException
                );
            }
            else
            {
                if (response.Data.success)
                {
                    return response.Data.data;
                }
                else
                {
                    Log.Error($"调用H04模块[{url}]请求完成,但是返回了错误:" + response.Data.msg);
                    return null;
                }
            }
        }

        public ResultDto GetIssueTokenInfo(
            string userNo,
            string tokenGuid,
            string moduleId,
            string? extraInfo
        )
        {
            Stopwatch sw = new Stopwatch();
            sw.Start();
            string url =
                $"/api/Account/GetIssueTokenInfo?userNo={userNo}&tokenGuid={tokenGuid}&moduleId={moduleId}&extraInfo={extraInfo}";

            RestRequest request = new RestRequest(url);
            var response = _clientS01.ExecutePost<ResultDto>(request);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用S01模块[颁发Token],耗时:{sw.ElapsedMilliseconds}ms");
            if (response.ErrorException != null)
            {
                Log.Error($"调用S01模块[颁发Token]发生错误:{response.ErrorException}");
                throw new BizException(
                    $"调用S01模块[颁发Token]发生错误:{response.ErrorException}",
                    response.ErrorException
                );
            }
            else
            {
                if (response.Data.success)
                {
                    return response.Data;
                }
                else
                {
                    Log.Error(
                        $"调用S01模块[颁发Token]请求完成,但是返回了错误:" + response.Data.msg
                    );
                    return null;
                }
            }
        }

        public ResultDto ReNewToken(string expriedToken, string refreshToken)
        {
            Stopwatch sw = new Stopwatch();
            sw.Start();
            string url = $"/api/Account/GetExtensionTokenInfo?accountToken={expriedToken}";

            RestRequest request = new RestRequest(url);
            request.AddHeader("Authorization", "Bearer " + refreshToken);
            var response = _clientS01.ExecutePost<ResultDto>(request);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用S01模块续签Token,耗时:{sw.ElapsedMilliseconds}ms");
            if (response.ErrorException != null)
            {
                Log.Error($"调用S01模块[续签Token]发生错误:{response.ErrorException}");
                throw new BizException(
                    $"调用S01模块[续签Token]发生错误:{response.ErrorException}",
                    response.ErrorException
                );
            }
            else
            {
                if (response.Data.success)
                {
                    return response.Data;
                }
                else
                {
                    Log.Error(
                        $"调用S01模块[续签Token]请求完成,但是返回了错误:" + response.Data.msg
                    );

                    throw new BizException(
                        $"调用S01模块[续签Token]请求完成,但是返回了错误:" + response.Data.msg
                    );
                }
            }
        }

        /// <summary>
        /// 传入用户信息自定义生成TOKEN
        /// </summary>
        /// <param name="userNo"></param>
        /// <param name="tokenGuid"></param>
        /// <param name="moduleId"></param>
        /// <param name="obj">自定义用户信息</param>
        /// <returns></returns>
        public ResultDto CustomCreateToken(
            string userNo,
            dynamic obj,
            string tokenGuid,
            string moduleId
        )
        {
            Stopwatch sw = new Stopwatch();
            sw.Start();
            string sendObj = JsonConvert.SerializeObject(obj);
            string url =
                $"/api/Account/CustomCreateToken?userNo={userNo}&tokenGuid={tokenGuid}&moduleId={moduleId}";
            RestRequest request = new RestRequest(url);
            request.AddJsonBody(sendObj);
            var response = _clientS01.ExecutePost<ResultDto>(request);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用S01模块颁发Token,耗时:{sw.ElapsedMilliseconds}ms");
            if (response.ErrorException != null)
            {
                Log.Error($"调用S01模块[颁发Token]发生错误:{response.ErrorException}");
                throw new BizException(
                    $"调用S01模块[颁发Token]发生错误:{response.ErrorException}",
                    response.ErrorException
                );
            }
            else
            {
                if (response.Data.success)
                {
                    return response.Data;
                }
                else
                {
                    Log.Error(
                        $"调用S01模块[颁发Token]请求完成,但是返回了错误:" + response.Data.msg
                    );
                    throw new BizException(
                        $"调用S01模块[颁发Token]请求完成,但是返回了错误:" + response.Data.msg
                    );
                }
            }
        }

        /// <summary>
        /// 调用6.0方法方法
        /// </summary>
        /// <param name="headXml"></param>
        /// <param name="bodyXml"></param>
        /// <returns></returns>
        public ResultDto CallS10InterfaceSource(string headXml, string bodyXml)
        {
            ResultDto result = new ResultDto();
            Stopwatch sw = new Stopwatch();
            sw.Start();
            JObject patientinfo = new JObject();
            //进行Base64编码
            patientinfo["headXml"] = Convert.ToBase64String(Encoding.UTF8.GetBytes(headXml));
            patientinfo["bodyxml"] = Convert.ToBase64String(Encoding.UTF8.GetBytes(bodyXml));
            string sendObj = JsonConvert.SerializeObject(patientinfo);
            string url = "/S10/API";
            RestRequest request = new RestRequest(url);
            request.AddJsonBody(sendObj);
            var response = _clientS10.ExecutePost<string>(request);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用S10(6.0接口),耗时:{sw.ElapsedMilliseconds}ms");
            if (response.ErrorException != null)
            {
                Log.Error($"调用S10(6.0接口)发生错误:{response.ErrorException}");
                throw new BizException(
                    $"调用S10(6.0接口)发生错误:{response.ErrorException}",
                    response.ErrorException
                );
            }
            else
            {
                result.data = response.Content;
            }
            return result;
        }

        /// <summary>
        /// 换token
        /// </summary>
        /// <param name="expriedToken">token</param>
        /// <param name="refreshToken">刷新token</param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public ResultDto TokenSubstitution(string expriedToken, string refreshToken)
        {
            Stopwatch sw = new Stopwatch();
            sw.Start();
            string url =
                $"/api/Account/TokenSubstitution?accountToken={expriedToken}&refreshToken={refreshToken}";

            RestRequest request = new RestRequest(url);
            request.AddHeader("Authorization", "Bearer " + refreshToken);
            var response = _clientS01.ExecutePost<ResultDto>(request);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用S01模块TokenSubstitution,耗时:{sw.ElapsedMilliseconds}ms");
            if (response.ErrorException != null)
            {
                Log.Error($"调用S01模块[TokenSubstitution]发生错误:{response.ErrorException}");
                throw new BizException(
                    $"调用S01模块[TokenSubstitution]发生错误:{response.ErrorException}",
                    response.ErrorException
                );
            }
            else
            {
                if (response.Data.success)
                {
                    return response.Data;
                }
                else
                {
                    Log.Error(
                        $"调用S01模块[TokenSubstitution]请求完成,但是返回了错误:"
                            + response.ToString()
                    );

                    throw new BizException(
                        $"调用S01模块[TokenSubstitution]请求完成,但是返回了错误:"
                            + response.Data.msg
                    );
                }
            }
        }

        #region 密码校验


        /// <summary>
        /// 用户验证
        /// </summary>
        /// <param name="jsonStr"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public ResultDto UserVerify(string jsonStr)
        {
            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();
            Stopwatch sw = new Stopwatch();
            sw.Start();
            string url = $"/api/Login/UserVerify";
            RestRequest request = new RestRequest(url);
            request.AddHeader("Authorization", token);
            request.AddJsonBody(jsonStr);
            RestResponse<ResultDto> response;
            try
            {
                response = _clientH5702.ExecutePost<ResultDto>(request);
            }
            catch (Exception e)
            {
                Log.Error($"调用H5702模块[{url}]发生错误:{e.ToString()}");
                throw new BizException($"调用H5702模块发生错误!");
            }
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用H5702模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
            if (response == null || response.ErrorException != null)
            {
                Log.Error($"调用H5702模块[{url}]发生错误:{response?.ErrorException}");
                throw new BizException($"调用H5702模块发生错误!");
            }
            else
            {
                if (response.Data.success)
                {
                    return response.Data;
                }
                else
                {
                    Log.Error($"调用H5702模块[{url}]请求完成,但是返回了错误:" + response.Data.msg);
                    return response.Data;
                }
            }
        }

        public sys6UserDto Sm2PasswordCheck(string loginId, string smPassword = "")
        {
            try
            {
                if (smPassword.IsNotNullOrEmpty())
                {
                    var res = H57Post(loginId, smPassword);
                    if (!res.success)
                    {
                        throw new BizException("账号或密码错误！");
                    }
                }
                var user = _sqlSugarUow
                    .Db.Queryable<SYS6_USER>()
                    .Where(p => p.LOGID == loginId)
                    .First();
                var result = _mapper.Map<sys6UserDto>(user);
                return result;
            }
            catch (BizException e)
            {
                throw e;
            }
        }

        private ResultDto H57Post(string loginId, string smPassword = "")
        {
            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();
            Stopwatch sw = new Stopwatch();
            sw.Start();
            string url = $"/api/Login/UserVerify";
            RestRequest request = new RestRequest(url);
            request.AddHeader("Authorization", token);
            request.AddBody(new { logId = loginId, password = smPassword });
            RestResponse<ResultDto> response;
            try
            {
                response = _clientH5702.ExecutePost<ResultDto>(request);
            }
            catch (Exception e)
            {
                Log.Error($"调用H5702模块[{url}]发生错误:{e.ToString()}");
                throw new BizException($"调用H5702模块发生错误!");
            }
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用H5702模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
            if (response == null || response.ErrorException != null)
            {
                Log.Error($"调用H5702模块[{url}]发生错误:{response?.ErrorException}");
                throw new BizException($"调用H5702模块发生错误!");
            }
            else
            {
                if (response.Data.success)
                {
                    return response.Data;
                }
                else
                {
                    Log.Error($"调用H5702模块[{url}]请求完成,但是返回了错误:" + response.Data.msg);
                    return response.Data;
                }
            }
        }

        #endregion


        /// <summary>
        /// 获取医院信息
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        /// <exception cref="BizException"></exception>
        public ResultDto GetHospitalInfo(string hospitalId)
        {
            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();
            string url = $"/api/Infra/GetHospitalInfo?hospitalId={hospitalId}";
            RestRequest request = new RestRequest(url);
            request.AddHeader("Authorization", token);
            try
            {
                var response = _clientH5702.ExecuteGet<ResultDto>(request);
                if (response.IsSuccessStatusCode || response.ErrorException is null)
                {
                    return response.Data;
                }
                else
                {
                    Log.Error(
                        $"调用H5702模块获取机构信息[{url}]发生错误:{response?.ErrorException}"
                    );
                    throw new BizException($"调用H5702模块获取机构信息发生错误!");
                }
            }
            catch (Exception e)
            {
                Log.Error($"调用H5702模块[{url}]获取机构信息发生错误:{e.ToString()}");
                throw new BizException($"调用H5702模块获取机构信息发生错误!");
            }
        }

        /// <summary>
        /// 查询当权用户是否有设备档案删除权限
        /// </summary>
        /// <returns></returns>
        public bool HasEquipmentDeletedPromiss()
        {
            const string equipmentDeletedPromiss = "H8253001";
            return HasButtonPermissions(equipmentDeletedPromiss);
        }
  
        public bool HasButtonPermissions(string promiss)
        {
            var user = _httpContext.HttpContext.User.ToClaimsDto();
            var menu = _authorityService.GetUserMenuList(
                _sqlSugarUow,
                AppSettingsProvider.CurrModuleId,
                MenuClassEnum.BUTTON
            );
            return menu.Any(x => x.MENU_ID == promiss);
        }

        public LogonHospitalLabList GetLogonHospitalLablist(ClaimsDto user)
        {
            var result = _authorityService.GetLogonHospitalLabList(
                _sqlSugarUow,
                "H82"
            );

            return result;
        }
    }
}
