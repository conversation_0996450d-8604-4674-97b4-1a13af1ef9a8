﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.Entities.Certificate
{
    using System;
    using H.Utility.SqlSugarInfra;
    using SqlSugar;

    /// <summary>
    /// 设备证书信息
    /// </summary>
    [DBOwner("XH_OA")]
    [SugarTable("EMS_CERTIFICATE_INFO", TableDescription = "设备证书信息")]
    public class EMS_CERTIFICATE_INFO
    {
        /// <summary>
        /// PK
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string CERTIFICATE_ID { get; set; }

        /// <summary>
        /// 医疗机构id
        /// </summary>
        public string HOSPITAL_ID { get; set; }

        /// <summary>
        /// 设备id
        /// </summary>
        public string EQUIPMENT_ID { get; set; }

        /// <summary>
        /// 证书类型
        /// </summary>
        public string CRETIFICATE_TYPE { get; set; }

        /// <summary>
        /// 提醒时间
        /// </summary>
        public DateTime? CER_WANR_DATE { get; set; }

        /// <summary>
        /// 发证时间
        /// </summary>
        public DateTime CER_DATE { get; set; }

        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime? EXPIRY_DATE { get; set; }

        /// <summary>
        /// 设备证书状态;0禁用、1在用、2删除、默认1
        /// </summary>
        public string CERTIFICATE_STATE { get; set; }

        /// <summary>
        /// 首次创建时间
        /// </summary>
        public DateTime? FIRST_RTIME { get; set; }

        /// <summary>
        /// 首次操作人
        /// </summary>
        public string? FIRST_RPERSON { get; set; }

        /// <summary>
        /// 最后一次操作时间
        /// </summary>
        public DateTime? LAST_MTIME { get; set; }

        /// <summary>
        /// 最后一次操作人
        /// </summary>
        public string? LAST_MPERSON { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? REMARK { get; set; }

        /// <summary>
        /// 附件列表
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<EMS_DOC_INFO> ATTACHMENTS { get; set; } = new List<EMS_DOC_INFO>();
    }
}
