2025-07-11 15:24:53.143 +08:00 [INF] ==>App Start..2025-07-11 15:24:53
2025-07-11 15:24:53.449 +08:00 [INF] ==>当前模块:H82,6.25.329
2025-07-11 15:24:53.454 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-11 15:24:55.671 +08:00 [INF] ==>基础连接请求完成.
2025-07-11 15:24:56.101 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-11 15:24:56.720 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-11 15:24:57.056 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-11 15:24:57.075 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-11 15:24:57.459 +08:00 [INF] ==>版本写入成功:6.25.329
2025-07-11 15:24:59.901 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-11 15:25:00.476 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-11 15:25:01.210 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-11 15:25:01.210 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-11 15:25:01.967 +08:00 [ERR] 查询出错:中文提示 :  连接数据库过程中发生错误，检查服务器是否正常连接字符串是否正确，错误信息：ORA-12545: Network Transport: Unable to resolve connect hostnameDbType="Oracle";ConfigId="".
English Message : Connection open error . ORA-12545: Network Transport: Unable to resolve connect hostnameDbType="Oracle";ConfigId="" :
SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON" FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "LAST_MTIME" IS NULL )
2025-07-11 15:25:12.872 +08:00 [INF] ==>App Start..2025-07-11 15:25:12
2025-07-11 15:25:13.039 +08:00 [INF] ==>当前模块:H82,6.25.329
2025-07-11 15:25:13.042 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-11 15:25:14.533 +08:00 [INF] ==>基础连接请求完成.
2025-07-11 15:25:14.957 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-11 15:25:15.300 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-11 15:25:15.617 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-11 15:25:15.623 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-11 15:25:15.951 +08:00 [INF] ==>版本写入成功:6.25.329
2025-07-11 15:25:16.444 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-11 15:25:16.533 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-11 15:25:16.982 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-11 15:25:16.982 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-11 15:25:19.176 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-11 15:25:21.565 +08:00 [INF] ==>初始化完成..
2025-07-11 15:25:21.636 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-11 15:25:21.637 +08:00 [INF] 设备启用任务
2025-07-11 15:25:21.638 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-11 15:25:22.030 +08:00 [INF] 【SQL执行耗时:367.6015ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-11 15:25:22.202 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-11 15:25:22.220 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-11 15:25:22.222 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-11 15:25:22.222 +08:00 [INF] Hosting environment: Development
2025-07-11 15:25:22.222 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-11 15:25:53.109 +08:00 [INF] 【SQL执行耗时:376.9484ms】

[Sql]:SELECT * FROM (SELECT "CLASS_LEVEL",ROW_NUMBER() OVER(ORDER BY "CLASS_LEVEL" ASC) AS RowIndex  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE  ("CLASS_ID" IN ('0')) ) T WHERE RowIndex BETWEEN 1 AND 1

2025-07-11 15:25:53.575 +08:00 [INF] 【SQL执行耗时:378.2813ms】

[Sql]: SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK"  FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_ID" = :EqpNoId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EqpNoId0 [Value]:B035558B184742DCAD75C5996534A88B [Type]:String    

2025-07-11 15:25:54.152 +08:00 [INF] 【SQL执行耗时:394.6951ms】

[Sql]:UPDATE "XH_OA"."EMS_EQPNO_FORMAT_DICT"  SET
           "HOSPITAL_ID"=:HOSPITAL_ID,"EQP_NO_NAME"=:EQP_NO_NAME,"EQP_NO_LEVEL"=:EQP_NO_LEVEL,"EQP_NO_CLASS"=:EQP_NO_CLASS,"EQP_DISPLAY_JSON"=:EQP_DISPLAY_JSON,"EQP_NO_APPLYS"=:EQP_NO_APPLYS,"EQP_NO_STATE"=:EQP_NO_STATE,"FIRST_RPERSON"=:FIRST_RPERSON,"FIRST_RTIME"=:FIRST_RTIME,"LAST_MPERSON"=:LAST_MPERSON,"LAST_MTIME"=:LAST_MTIME,"REMARK"=:REMARK  WHERE "EQP_NO_ID"=:EQP_NO_ID 
[Pars]:
[Name]::EQP_NO_ID [Value]:B035558B184742DCAD75C5996534A88B [Type]:String    
[Name]::HOSPITAL_ID [Value]:33A001 [Type]:String    
[Name]::EQP_NO_NAME [Value]:管理临床模板 [Type]:String    
[Name]::EQP_NO_LEVEL [Value]:0 [Type]:String    
[Name]::EQP_NO_CLASS [Value]:0 [Type]:String    
[Name]::EQP_DISPLAY_JSON [Value]:{"DisplayContentString":"{管理专业组}_{设备代号}","DisplayContentCode":"{MGROUP_NAME}_{EQUIPMENT_CODE}","FixedFieldDisplays":[{"Key":"{MGROUP_NAME}","Value":""}]} [Type]:String    
[Name]::EQP_NO_APPLYS [Value]:PG054 [Type]:String    
[Name]::EQP_NO_STATE [Value]:1 [Type]:String    
[Name]::FIRST_RPERSON [Value]:gz1_广测重 [Type]:String    
[Name]::FIRST_RTIME [Value]:2025/7/9 10:16:42 [Type]:DateTime    
[Name]::LAST_MPERSON [Value]:fr_李影 [Type]:String    
[Name]::LAST_MTIME [Value]:2025/7/11 15:25:53 [Type]:DateTime    
[Name]::REMARK [Value]: [Type]:String    

2025-07-11 15:25:57.340 +08:00 [INF] 【SQL执行耗时:397.6636ms】

[Sql]:SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_ID" = :EqpNoId0 )  AND ( "EQP_NO_STATE" = :EqpNoState1 ) 
[Pars]:
[Name]::EqpNoId0 [Value]:B035558B184742DCAD75C5996534A88B [Type]:String    
[Name]::EqpNoState1 [Value]:1 [Type]:String    

2025-07-11 15:26:11.058 +08:00 [INF] 【SQL执行耗时:359.2146ms】

[Sql]:SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON" FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE  ("UNIT_ID" IN ('PG054')) 

2025-07-11 15:26:48.139 +08:00 [INF] 【SQL执行耗时:1624.0104ms】

[Sql]: SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON"  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "CLASS_ID" = :ClassId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::ClassId0 [Value]:2 [Type]:String    

2025-07-11 15:26:48.702 +08:00 [INF] 【SQL执行耗时:349.5214ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" = :EqpNoState0 )  AND  ("EQP_NO_APPLYS" like '%'||:MethodConst1||'%')  
[Pars]:
[Name]::EqpNoState0 [Value]:1 [Type]:String    
[Name]::MethodConst1 [Value]:PG054 [Type]:String    

2025-07-11 15:26:49.108 +08:00 [INF] 【SQL执行耗时:360.5089ms】

[Sql]:SELECT * FROM (SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK",ROW_NUMBER() OVER( ORDER BY sysdate ) AS RowIndex  FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" = :EqpNoState0 )  AND  ("EQP_NO_APPLYS" like '%'||:MethodConst1||'%') ) T WHERE RowIndex BETWEEN 1 AND 300 
[Pars]:
[Name]::EqpNoState0 [Value]:1 [Type]:String    
[Name]::MethodConst1 [Value]:PG054 [Type]:String    

2025-07-11 15:27:10.221 +08:00 [INF] 【SQL执行耗时:1075.2888ms】

[Sql]:SELECT "HOSPITAL_ID","HOSPITAL_LOGIN","HOSPITAL_CNAME","HOSPITAL_SNAME","HOSPITAL_TYPE","HOSPITAL_LEVEL","LEVEL1_CODE","LEVEL2_CODE","LEVEL3_CODE","HOSPITAL_BARCODE","HOSPITAL_PROPERTY","HOSPITAL_PERSON","HOSPITAL_ADDRESS","HOSPITAL_TEL","HOSPITAL_STATE","FIRST_PERSON","FIRST_RTIME","LAST_MPRESON","LAST_MTIME","REMARK","HOSPITAL_BCODE","HOSPITAL_RANGE","BARCODE_TYPE","BARCODE_RANGE","CHARGE_PROVIDE_TYPE","DATADB_IP","WECHAT_NAME","LAST_MPERSON","FIRST_RPERSON","UPDATE_FLAG","INSERFACE_SERVICE","SAMELEVEL_HOSPITAL","HOSPITAL_CLASS","HOSPITAL_URL","REGISTER_CODE","PACK_BAR_PREFIX","IF_ITEM_MANAGE","IF_TEMPLATE_HOS","MEDICAL_CONGENER","MEDICAL_CONGENER_ROLE","VEST_IN_DC","INSTANCE_ID","HOSPITAL_ATTRIBUTE","DATASYNC_TYPE","HGROUP_ID","CITEM_MANAGE" FROM "XH_SYS"."SYS6_HOSPITAL_INFO" 

2025-07-11 15:27:10.833 +08:00 [INF] 【SQL执行耗时:343.0088ms】

[Sql]:SELECT "LAB_ID","FIRST_RPERSON","LAB_NAME","LAB_ADDRESS","LAB_CODE","LAST_MTIME","LAB_SORT","LAB_RNAME","LAB_CLASS","LAB_PERSON","LINKMAN_TEL","REGISTER_CODE","LAB_LINKMAN","STATE_FLAG","REMARK","LAST_MPERSON","FIRST_RTIME","SPELL_CODE","LAB_TEL","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_LAB" 

2025-07-11 15:27:11.260 +08:00 [INF] 【SQL执行耗时:348.4517ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-11 15:27:11.696 +08:00 [INF] 【SQL执行耗时:348.5637ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP" 

2025-07-11 15:27:13.253 +08:00 [INF] 【SQL执行耗时:1473.4294ms】

[Sql]:SELECT "AREA_ID","AREA_CODE","AREA_SORT","AREA_NAME","AREA_CLASS","AREA_PERSON","AREA_ADDRESS","AREA_TEL","SPELL_CODE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","STATE_FLAG","REMARK","HOSPITAL_ID","TAG_SIGN","AREA_ANAME","PACK_BAR_PREFIX" FROM "XH_SYS"."SYS6_INSPECTION_AREA" 

2025-07-11 15:27:20.638 +08:00 [INF] 【SQL执行耗时:389.5617ms】

[Sql]: SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON"  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "CLASS_ID" = :ClassId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::ClassId0 [Value]:2 [Type]:String    

2025-07-11 15:27:22.155 +08:00 [INF] 【SQL执行耗时:1476.5072ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" = :EqpNoState0 )  AND  ("EQP_NO_APPLYS" like '%'||:MethodConst1||'%')  
[Pars]:
[Name]::EqpNoState0 [Value]:1 [Type]:String    
[Name]::MethodConst1 [Value]:PG054 [Type]:String    

2025-07-11 15:27:22.545 +08:00 [INF] 【SQL执行耗时:354.7807ms】

[Sql]:SELECT * FROM (SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK",ROW_NUMBER() OVER( ORDER BY sysdate ) AS RowIndex  FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" = :EqpNoState0 )  AND  ("EQP_NO_APPLYS" like '%'||:MethodConst1||'%') ) T WHERE RowIndex BETWEEN 1 AND 300 
[Pars]:
[Name]::EqpNoState0 [Value]:1 [Type]:String    
[Name]::MethodConst1 [Value]:PG054 [Type]:String    

2025-07-11 15:27:45.089 +08:00 [INF] 【SQL执行耗时:1196.3109ms】

[Sql]: SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON"  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "CLASS_ID" = :ClassId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::ClassId0 [Value]:2 [Type]:String    

2025-07-11 15:27:45.647 +08:00 [INF] 【SQL执行耗时:357.8699ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" = :EqpNoState0 )  AND  ("EQP_NO_APPLYS" like '%'||:MethodConst1||'%')  
[Pars]:
[Name]::EqpNoState0 [Value]:1 [Type]:String    
[Name]::MethodConst1 [Value]:PG054 [Type]:String    

2025-07-11 15:27:46.040 +08:00 [INF] 【SQL执行耗时:358.0538ms】

[Sql]:SELECT * FROM (SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK",ROW_NUMBER() OVER( ORDER BY sysdate ) AS RowIndex  FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" = :EqpNoState0 )  AND  ("EQP_NO_APPLYS" like '%'||:MethodConst1||'%') ) T WHERE RowIndex BETWEEN 1 AND 300 
[Pars]:
[Name]::EqpNoState0 [Value]:1 [Type]:String    
[Name]::MethodConst1 [Value]:PG054 [Type]:String    

2025-07-11 15:28:05.841 +08:00 [INF] 【SQL执行耗时:467.9606ms】

[Sql]: SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON"  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "CLASS_ID" = :ClassId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::ClassId0 [Value]:1 [Type]:String    

2025-07-11 15:28:07.876 +08:00 [INF] 【SQL执行耗时:1997.8945ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" = :EqpNoState0 )  AND  ("EQP_NO_APPLYS" like '%'||:MethodConst1||'%')  
[Pars]:
[Name]::EqpNoState0 [Value]:1 [Type]:String    
[Name]::MethodConst1 [Value]:PG054 [Type]:String    

2025-07-11 15:28:08.471 +08:00 [INF] 【SQL执行耗时:375.9562ms】

[Sql]:SELECT * FROM (SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK",ROW_NUMBER() OVER( ORDER BY sysdate ) AS RowIndex  FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" = :EqpNoState0 )  AND  ("EQP_NO_APPLYS" like '%'||:MethodConst1||'%') ) T WHERE RowIndex BETWEEN 1 AND 300 
[Pars]:
[Name]::EqpNoState0 [Value]:1 [Type]:String    
[Name]::MethodConst1 [Value]:PG054 [Type]:String    

2025-07-11 15:28:08.988 +08:00 [INF] 【SQL执行耗时:337.263ms】

[Sql]: SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON"  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "CLASS_ID" = :ClassId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::ClassId0 [Value]:1 [Type]:String    

2025-07-11 15:28:09.372 +08:00 [INF] 【SQL执行耗时:347.8308ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" = :EqpNoState0 )  AND  ("EQP_NO_APPLYS" like '%'||:MethodConst1||'%')  
[Pars]:
[Name]::EqpNoState0 [Value]:1 [Type]:String    
[Name]::MethodConst1 [Value]:PG054 [Type]:String    

2025-07-11 15:28:11.510 +08:00 [INF] 【SQL执行耗时:2103.122ms】

[Sql]:SELECT * FROM (SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK",ROW_NUMBER() OVER( ORDER BY sysdate ) AS RowIndex  FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" = :EqpNoState0 )  AND  ("EQP_NO_APPLYS" like '%'||:MethodConst1||'%') ) T WHERE RowIndex BETWEEN 1 AND 300 
[Pars]:
[Name]::EqpNoState0 [Value]:1 [Type]:String    
[Name]::MethodConst1 [Value]:PG054 [Type]:String    

2025-07-11 15:28:12.949 +08:00 [INF] 【SQL执行耗时:367.4853ms】

[Sql]: SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON"  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "CLASS_ID" = :ClassId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::ClassId0 [Value]:1 [Type]:String    

2025-07-11 15:28:13.359 +08:00 [INF] 【SQL执行耗时:372.5932ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" = :EqpNoState0 )  AND  ("EQP_NO_APPLYS" like '%'||:MethodConst1||'%')  
[Pars]:
[Name]::EqpNoState0 [Value]:1 [Type]:String    
[Name]::MethodConst1 [Value]:PG054 [Type]:String    

2025-07-11 15:28:14.668 +08:00 [INF] 【SQL执行耗时:1274.1377ms】

[Sql]:SELECT * FROM (SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK",ROW_NUMBER() OVER( ORDER BY sysdate ) AS RowIndex  FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" = :EqpNoState0 )  AND  ("EQP_NO_APPLYS" like '%'||:MethodConst1||'%') ) T WHERE RowIndex BETWEEN 1 AND 300 
[Pars]:
[Name]::EqpNoState0 [Value]:1 [Type]:String    
[Name]::MethodConst1 [Value]:PG054 [Type]:String    

2025-07-11 15:28:15.987 +08:00 [INF] 【SQL执行耗时:363.5064ms】

[Sql]: SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON"  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "CLASS_ID" = :ClassId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::ClassId0 [Value]:1 [Type]:String    

2025-07-11 15:28:16.378 +08:00 [INF] 【SQL执行耗时:353.0843ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" = :EqpNoState0 )  AND  ("EQP_NO_APPLYS" like '%'||:MethodConst1||'%')  
[Pars]:
[Name]::EqpNoState0 [Value]:1 [Type]:String    
[Name]::MethodConst1 [Value]:PG054 [Type]:String    

2025-07-11 15:28:16.766 +08:00 [INF] 【SQL执行耗时:352.1717ms】

[Sql]:SELECT * FROM (SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK",ROW_NUMBER() OVER( ORDER BY sysdate ) AS RowIndex  FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" = :EqpNoState0 )  AND  ("EQP_NO_APPLYS" like '%'||:MethodConst1||'%') ) T WHERE RowIndex BETWEEN 1 AND 300 
[Pars]:
[Name]::EqpNoState0 [Value]:1 [Type]:String    
[Name]::MethodConst1 [Value]:PG054 [Type]:String    

2025-07-11 15:28:16.979 +08:00 [INF] HTTP PUT /api/CodeCustom/UpdateEquipmentCodeCustomDict/B035558B184742DCAD75C5996534A88B responded 200 in 148029.4979 ms
2025-07-11 15:28:16.983 +08:00 [INF] 【接口超时阀值预警】 [1404ac5c7983273db7fddc38dbe78030]接口/api/CodeCustom/UpdateEquipmentCodeCustomDict/B035558B184742DCAD75C5996534A88B,耗时:[148036]毫秒
