﻿namespace XH.H82.Models.BusinessModuleClient.Dto;


/// <summary>
/// 包含全部基础数据的模型
/// </summary>
public class AllBaseDataDto
{
    /// <summary>
    /// 唯一标识
    /// </summary>
    public string Id => Guid.NewGuid().ToString();
    
    /// <summary>
    /// 数据的value
    /// </summary>
    public string DataId { get; set; }
    
    /// <summary>
    /// 数据的lable
    /// </summary>
    public string DataName { get; set; }
    
    /// <summary>
    /// 数据来源
    /// </summary>
    public string DataTable { get; set; }

    /// <summary>
    /// 父级id/父级名称
    /// </summary>
    public string FatherId { get; set; }
    /// <summary>
    /// 分类名称
    /// </summary>
    public string ClassId { get; set; }

    /// <summary>
    /// 是否能编辑选项
    /// </summary>
    public bool IsEdit { get; set; } = false;

    /// <summary>
    /// 是否下拉可选
    /// </summary>
    public bool IsPull { get; set; } = true;

    /// <summary>
    /// 状态
    /// </summary>
    public string Status { get; set; } = "1";

    /// <summary>
    /// 状态名称
    /// </summary>
    public string StatusName => Status switch { "0" => "禁用", "1" => "在用", "2" => "删除",
        _ => "-"
    };

}


