﻿namespace XH.H82.API.Controllers.IoTDevice;
    public class  IoTDviceMonitorRecordDto
    {
        public string Id { get; set; }
        /// <summary>
        /// 监测时间
        /// </summary>
        public  string  MonitorTime  { get; set; }
        /// <summary>
        /// 监测类型
        /// </summary>
        public  string  MonitorType { get; set; }
        /// <summary>
        /// 电压
        /// </summary>
        public string   Voltage { get; set; }
        /// <summary>
        /// 电流
        /// </summary>
        public string   Current { get; set; }
        /// <summary>
        /// 功率
        /// </summary>
        public string   Power { get; set; }
        /// <summary>
        /// 用电量
        /// </summary>
        public string   Electricity{ get; set; }
        /// <summary>
        /// 采集时间
        /// </summary>
        public string AcquisitionTime { get; set; }

        /// <summary>
        /// 状态结束时间
        /// </summary>
        public string OverTime { get; set; } = DateTime.Now.ToString("HH:mm:ss");
    }
