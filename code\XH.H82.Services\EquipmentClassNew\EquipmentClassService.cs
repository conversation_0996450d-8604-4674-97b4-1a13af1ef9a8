﻿using System.Linq.Expressions;
using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using NuGet.Packaging;
using SqlSugar;
using XH.H82.Base.Tree;
using XH.H82.IServices.EquipmentClassNew;
using XH.H82.Models.EquipmengtClassNew;
using XH.H82.Models.SugarDbContext;
using XH.LAB.UTILS.Helpers;
 
namespace XH.H82.Services.EquipmentClassNew;

public class EquipmentClassService : IEquipmentClassService ,IEquipomentArchivesService
{
    private readonly ISqlSugarUow<SugarDbContext_Master> _dbContext;
     private readonly ClaimsDto _user;

     private readonly Dictionary<string, EMS_EQUIPMENT_CLASS_DICT> _equipmentClassDict;
     
     private readonly Dictionary<string, EMS_EQP_ARCHIVES_DICT> _equipmentArchivesDict;
    public EquipmentClassService(IHttpContextAccessor httpContextAccessor, ISqlSugarUow<SugarDbContext_Master> dbContext)
    {
        _dbContext = dbContext;
        _user = httpContextAccessor.HttpContext.User.ToClaimsDto();
        _equipmentClassDict = new();
        _equipmentArchivesDict = new();
    }


    public List<EMS_EQUIPMENT_CLASS_DICT> GetEquipmentClassDictByCondition(Expression<Func<EMS_EQUIPMENT_CLASS_DICT, bool>>  filter)
    {
        if (_dbContext.Db.Queryable<EMS_EQUIPMENT_CLASS_DICT>().Count()>0)
        {
             return _dbContext.Db.Queryable<EMS_EQUIPMENT_CLASS_DICT>()
                .Where(filter)
                .Where(x => x.ClassState != "2")
                .Includes(x=>x.ClassArchives)
                .ToList();
        }
        var result =  EMS_EQUIPMENT_CLASS_DICT.CreatInit(_user.HOSPITAL_ID);
        _dbContext.Db.GetSimpleClient<EMS_EQUIPMENT_CLASS_DICT>().InsertOrUpdate(result);
        foreach (var emsEqpArchivesDict in result)
        {
            emsEqpArchivesDict.ClassArchives = new List<EMS_EQP_CLASS_ARCHIVES>();
        }
        return result.Where(filter.Compile())
                .Where(x => x.ClassState != "2")
                .ToList();
    }

    public List<EMS_EQUIPMENT_CLASS_DICT> GetEquipmentClassDict()
    {
        var result = GetEquipmentClassDictByCondition(x => x.HospitalId == _user.HOSPITAL_ID);
        
        foreach (var equipmentClass in result)
        {
            _equipmentClassDict[equipmentClass.ClassId] = equipmentClass;
        }
        return result;
    }

    
    /// <summary>
    /// 创建ITreeNode对象
    /// </summary>
    /// <param name="equipmentClass">设备分类对象</param>
    /// <returns>对应的ITreeNode对象</returns>
    private  ITreeNode CreateTreeNode(EMS_EQUIPMENT_CLASS_DICT equipmentClass )
    {
        var  source =  new EquipmentClassDto()
        {
            ClassId = equipmentClass.ClassId,
            ClassName = equipmentClass.ClassName,
            ParentClassId = equipmentClass.ParentClassId,
            ParentClassName = _equipmentClassDict.TryGetValue( equipmentClass.ParentClassId , out var parentClass) ? parentClass.ClassName  : "",
            ClassSname = equipmentClass.ClassSname,
            ClassTag = equipmentClass.ClassTag,
            ClassStyle = equipmentClass.ClassStyle,
            ClassLevel = equipmentClass.ClassLevel,
            ClassState = equipmentClass.ClassState,
            LAST_MTIME = equipmentClass.LAST_MTIME,
            LAST_MPERSON = equipmentClass.LAST_MPERSON,
            FIRST_RTIME = equipmentClass.FIRST_RTIME,
            FIRST_RPERSON = equipmentClass.FIRST_RPERSON,
            ClassArchivesName = ""
        };
        var names = Array.Empty<string>().ToList();

        foreach (var  equipmentClassArchives in equipmentClass.ClassArchives)
        {
            if (_equipmentArchivesDict.TryGetValue(equipmentClassArchives.EqpArchivesId, out var equipmentArchivesDict))
            {
                names.Add(equipmentArchivesDict.EqpArchivesName);
            }
        }
        
        source.ClassArchivesName = string.Join("、", names);
        
        
        
        return new AllTreeNode()
        {
            NAME = equipmentClass.ClassName,
            NODE_NO = 0, // 根据业务逻辑设置
            AREA_ID = "",
            LAB_ID = "",
            NODE_TYPE = equipmentClass.ClassLevel switch{
                "0"=> TreeNodeTypeEnum.EQUIPMENTNEWCLASSNO0, 
                "1"=> TreeNodeTypeEnum.EQUIPMENTNEWCLASSNO1,
                "2"=> TreeNodeTypeEnum.EQUIPMENTNEWCLASSNO2,
                _ => throw new ArgumentOutOfRangeException()
            }, // 根据业务逻辑设置
            NODE_TYPE_NAME = equipmentClass.ClassLevel switch{
                "0"=> TreeNodeTypeEnum.EQUIPMENTNEWCLASSNO0.ToDesc(), 
                "1"=> TreeNodeTypeEnum.EQUIPMENTNEWCLASSNO1.ToDesc(),
                "2"=> TreeNodeTypeEnum.EQUIPMENTNEWCLASSNO2.ToDesc(),
                _ => throw new ArgumentOutOfRangeException()
            },
            SOURCE_ID = equipmentClass.ClassId,
            SOURCE = source,
            NUM = 0,
            SORT = "0",
            SOURCE_PATH = "",
            CHILDREN = new List<ITreeNode>(),
            ISLEAVES = equipmentClass.ClassLevel is not "0"
        };
    }
    
    public List<ITreeNode> GetEquipmentClassDictTree(bool hasAll = true ,bool isEdit = false)
    {
        var rootNode = new RootNode();
        var equipmentClassDicts = GetEquipmentClassDict();
        if (isEdit)
        {
            equipmentClassDicts.RemoveAll(x => x.ClassState == "0");
        }
        var archivesDicts = GetEquipmentArchives();
        // 创建一个字典来存储所有节点，键为CLASS_ID
        var nodeDictionary = new Dictionary<string, ITreeNode>();
        rootNode.SOURCE_ID = "0";
        rootNode.NAME = "全部设备";
        nodeDictionary["0"] = rootNode;
        // 遍历所有设备分类，将它们转换为ITreeNode对象并存入字典
        foreach (var equipmentClass in equipmentClassDicts)
        {
            var treeNode = CreateTreeNode(equipmentClass);
            treeNode.NODE_NO = nodeDictionary.Count;
            nodeDictionary[equipmentClass.ClassId] = treeNode;
        }
        foreach (var equipmentClass in equipmentClassDicts)
        {
            // 找到父节点并添加当前节点作为子节点
            var parentNode = nodeDictionary[equipmentClass.ParentClassId];
            var treeNode = nodeDictionary[equipmentClass.ClassId];
            parentNode.AddChild(treeNode);
        }
        if (hasAll)
        {
            rootNode.ConutChildrensNoAppendRootPath("0");
            return new List<ITreeNode>(){rootNode}; 
        }
        rootNode.ConutChildrensNoAppendRootPath();
        return  rootNode.CHILDREN;
        
        
    }
    
    public List<EMS_EQP_ARCHIVES_DICT> GetEquipmentArchivesByCondition(Expression<Func<EMS_EQP_ARCHIVES_DICT, bool>>?  filter = null)
    {
        if (_dbContext.Db.Queryable<EMS_EQP_ARCHIVES_DICT>().Count()>0)
        {
            return _dbContext.Db.Queryable<EMS_EQP_ARCHIVES_DICT>()
                    .WhereIF(filter != null, filter)
                    .Where(x => x.EqpArchivesState != "2")
                    .Includes(x => x.ClassArchives)
                    .ToList();
        }
        var result =  EMS_EQP_ARCHIVES_DICT.CreatInit(_user.HOSPITAL_ID);
        _dbContext.Db.GetSimpleClient<EMS_EQP_ARCHIVES_DICT>().InsertOrUpdate(result);
        foreach (var emsEqpArchivesDict in result)
        {
            emsEqpArchivesDict.ClassArchives = new List<EMS_EQP_CLASS_ARCHIVES>();
        }

        return result.WhereIF(filter != null,filter?.Compile())
             .Where(x => x.EqpArchivesState != "2")
             .ToList();
    }

    public List<EMS_EQP_ARCHIVES_DICT> GetEquipmentArchives()
    {
        var result = GetEquipmentArchivesByCondition(x =>  x.EqpArchivesState == "1");
        foreach (var  emsEqpArchives in result)
        {
            _equipmentArchivesDict[emsEqpArchives.EqpArchivesId] = emsEqpArchives;
        }
        return result;
    }
    
    public List<EMS_EQP_ARCHIVES_DICT> GetEquipmentArchivesLinkByClassId(string classId)
    {
        var result = Array.Empty<EMS_EQP_ARCHIVES_DICT>().ToList();
        var classDict = _dbContext.Db.Queryable<EMS_EQUIPMENT_CLASS_DICT>()
            .Where(x => x.ClassId == classId)
            .Includes(x => x.ClassArchives)
            .First();
        if (classDict is null)
        {
            return result.ToList();   
        }
        var archivesIds = classDict.ClassArchives.Select(x => x.EqpArchivesId);
        var archives =  GetEquipmentArchivesByCondition(x => archivesIds.Contains(x.EqpArchivesId) && x.EqpArchivesState == "1");
        result.AddRange(archives);

        foreach (var item in result)
        {
            item.EqpArchivesPName = ExchangeArchivesName(item.EqpArchivesPid);
        }
        return result;
    }


    private string ExchangeArchivesName(string? archivesId)
    {
        if (archivesId is "0" or null)
        {
            return "";
        }
        if (_equipmentArchivesDict.TryGetValue(archivesId, out var archives))
        {
            return  archives.EqpArchivesName;
        }
        GetEquipmentArchives();
        return _equipmentArchivesDict.TryGetValue(archivesId, out var newArchives) ? newArchives.EqpArchivesName : "";
    }

    public List<EMS_EQP_ARCHIVES_DICT> GetEquipmentArchivesNotLinkByClassId(string classId)
    {
        var result = GetEquipmentArchivesByCondition(x => x.EqpArchivesState == "1");
        var linkRecordDict = GetEquipmentArchivesLinkByClassId(classId);
        foreach (var item in result)
        {
            item.EqpArchivesPName = ExchangeArchivesName(item.EqpArchivesPid);
        }
        
        var difference = result
            .Where(p1 => linkRecordDict.All(p2 => p2.EqpArchivesId != p1.EqpArchivesId));
        
        return difference.ToList();
    }

    public EMS_EQUIPMENT_CLASS_DICT AddClassDict(AddEquipmentClassDto classDto)
    {
        var result = new EMS_EQUIPMENT_CLASS_DICT()
        {
            ClassId = IDGenHelper.CreateGuid(),
            HospitalId = _user.HOSPITAL_ID,
            ParentClassId = classDto.ParentClassId,
            ClassName = classDto.ClassName,
            ClassSname = classDto.ClassSname,
            ClassTag = classDto.ClassTag,
            ClassStyle = classDto.ClassStyle,
            ClassLevel = "2",
            ClassState = "1",
            FIRST_RPERSON = _user.HIS_NAME,
            FIRST_RTIME = DateTime.Now,
            LAST_MPERSON = _user.HIS_NAME,
            LAST_MTIME = DateTime.Now,
        };
        using var uow = _dbContext.Begin();
        if (classDto.IsUseParentRecord)
        {
            //插入于父级节点相同的数据
            var  PClass = _dbContext.Db.Queryable<EMS_EQUIPMENT_CLASS_DICT>().First(x => x.ClassId == classDto.ParentClassId);
            if (PClass is null)
            {
                throw new BizException("父级节点不存在");
            }
            result.ParentClassId = PClass.ParentClassId;
            result.ClassTag = PClass.ClassTag;
            result.ClassSname = PClass.ClassSname;
            result.ClassTag = PClass.ClassTag;
            result.ClassStyle = PClass.ClassStyle;
            
            var classArchives = _dbContext.Db.Queryable<EMS_EQP_CLASS_ARCHIVES>()
                .Where(x => x.EqpClassId == PClass.ClassId)
                .ToList();
            foreach (var classArchive in classArchives)
            {
                LinkClassAndArchives(result.ClassId , classArchive.EqpArchivesId);
            }
        }
        _dbContext.Db.Insertable(result).ExecuteCommand();
        _dbContext.SaveChanges();
        return result;
    }

    public EMS_EQUIPMENT_CLASS_DICT UpdateClassDict(string classId, AddEquipmentClassDto classDto)
    {
        var result = _dbContext.Db.Queryable<EMS_EQUIPMENT_CLASS_DICT>().First(x => x.ClassId == classId);
        if (result  is null)
        {
            throw new BizException("未找到该分类字典");
        }
        
        result.ClassTag = classDto.ClassTag;
        result.ClassSname = classDto.ClassSname;
        result.ClassTag = classDto.ClassTag;
        result.ClassStyle = classDto.ClassStyle;
        result.LAST_MTIME = DateTime.Now;
        result.LAST_MPERSON = _user.HIS_ID;
        _dbContext.Db.Updateable(result).ExecuteCommand();
        return result;
    }

    public bool DeleteClassDict(string classId)
    {
        var classDict = _dbContext.Db.Queryable<EMS_EQUIPMENT_CLASS_DICT>()
            .Where(x => x.ClassId == classId)
            .First();
        if (classDict is null)
        {
            return true;
        }
        
        _dbContext.Db.Updateable<EMS_EQUIPMENT_CLASS_DICT>()
            .SetColumns(x => new EMS_EQUIPMENT_CLASS_DICT()
            {
                ClassState = "2",
                LAST_MTIME = DateTime.Now,
                LAST_MPERSON = _user.USER_NAME
            } )
            .Where(x => x.ClassId == classId)
            .ExecuteCommand();
        return true;
    }

    public bool DisableOrEnableClassDict(string classId)
    {
        var classDict = _dbContext.Db.Queryable<EMS_EQUIPMENT_CLASS_DICT>()
            .Where(x => x.ClassId == classId)
            .First();
        if (classDict is null)
        {
            return true;
        }
        
        _dbContext.Db.Updateable<EMS_EQUIPMENT_CLASS_DICT>()
            .SetColumns(x => new EMS_EQUIPMENT_CLASS_DICT()
            {
                ClassState = classDict.ClassState == "1" ? "0" : "1",
                LAST_MTIME = DateTime.Now,
                LAST_MPERSON = _user.USER_NAME
            } )
            .Where(x => x.ClassId == classId)
            .ExecuteCommand();
        return true;
    }

    
    public bool LinkClassAndArchives(string classId, string[] archivesIds)
    {
        using  var uow =_dbContext.Begin();
        foreach (var archivesId in archivesIds)
        {
            LinkClassAndArchives(classId, archivesId);
        }
        _dbContext.SaveChanges();
        return true;
    }
    public bool UnLinkClassAndArchives(string classId, string[] archivesIds)
    {
        using  var uow =_dbContext.Begin();
        foreach (var archivesId in archivesIds)
        {
            UnLinkClassAndArchives(classId, archivesId);
        }
        _dbContext.SaveChanges();
        return true;
    }
    
    private bool LinkClassAndArchives(string classId, string archivesId)
    {
        var link = new EMS_EQP_CLASS_ARCHIVES()
        {
            ClassArchivesId = IDGenHelper.CreateGuid(),
            HospitalId = _user.HOSPITAL_ID,
            EqpClassId = classId,
            EqpArchivesId = archivesId,
            FIRST_RTIME = DateTime.Now,
            FIRST_RPERSON = _user.USER_NAME,
            LAST_MTIME = DateTime.Now,
            LAST_MPERSON = _user.USER_NAME,
        };
        return _dbContext.Db.Insertable(link).ExecuteCommand() > 0;
    }
    private bool UnLinkClassAndArchives(string classId, string archivesId)
    {
        var result = _dbContext.Db.Deleteable<EMS_EQP_CLASS_ARCHIVES>()
            .Where(x => x.EqpClassId == classId && x.EqpArchivesId == archivesId)
            .ExecuteCommand() > 0;
            return result;
    }

    public EMS_EQP_ARCHIVES_DICT AddEquipmentArchives(AddEquipmentArchivesDto archivesDto)
    {
        var  conut = _dbContext.Db.Queryable<EMS_EQP_ARCHIVES_DICT>()
            .Count();
        conut++;
        var  ext = new EqpArchivesExt(){ IsUpload = archivesDto.IsIpload };
        var  newArchives = new  EMS_EQP_ARCHIVES_DICT
        {
            EqpArchivesId = IDGenHelper.CreateGuid(),
            HospitalId = _user.HOSPITAL_ID,
            EqpArchivesPid = archivesDto.EqpArchivesPid,
            EqpArchivesName = archivesDto.EqpArchivesName,
            EqpArchivesSort = conut,
            EqpArchivesJson = JsonConvert.SerializeObject(ext),
            Remark = archivesDto.Remark,
            EqpArchivesType = "1",
            EqpArchivesState = "1",
            FIRST_RTIME = DateTime.Now,
            LAST_MTIME = DateTime.Now,
            FIRST_RPERSON = _user.HIS_NAME,
            LAST_MPERSON = _user.HIS_NAME
        };
        return _dbContext.Db.Insertable(newArchives).ExecuteReturnEntity();
    }

    public EMS_EQP_ARCHIVES_DICT UpdateEquipmentArchives(string archivesId, AddEquipmentArchivesDto archivesDto)
    {
        var  archives = _dbContext.Db.Queryable<EMS_EQP_ARCHIVES_DICT>().Single(x => x.EqpArchivesId == archivesId);
        if(archives == null)
        {
            return null;
        }
        var  ext = new EqpArchivesExt(){ IsUpload = archivesDto.IsIpload };
        archives.EqpArchivesPid = archivesDto.EqpArchivesPid;
        archives.EqpArchivesName = archivesDto.EqpArchivesName;
        archives.EqpArchivesJson = JsonConvert.SerializeObject(ext);
        archives.Remark = archivesDto.Remark;
        _dbContext.Db.Updateable(archives).ExecuteCommand();
        return archives;
    }

    public bool DeleteEquipmentArchives(string archivesId)
    {
       return  _dbContext.Db.Updateable<EMS_EQP_ARCHIVES_DICT>()
            .SetColumns(x=>  new  EMS_EQP_ARCHIVES_DICT ()
            {
                EqpArchivesState = "2",
                LAST_MTIME = DateTime.Now,
                LAST_MPERSON = _user.HIS_NAME
            })
            .Where(x=>x.EqpArchivesId == archivesId)
            .ExecuteCommand()>0;
    }

    public bool EnableOrDisableEquipmentArchives(string archivesId)
    {
        var archives = _dbContext.Db.Queryable<EMS_EQP_ARCHIVES_DICT>().Single(x => x.EqpArchivesId == archivesId);
        return  _dbContext.Db.Updateable<EMS_EQP_ARCHIVES_DICT>()
            .SetColumns(x=>  new  EMS_EQP_ARCHIVES_DICT ()
            {
                EqpArchivesState = archives.EqpArchivesState == "1" ? "0" : "1",
                LAST_MTIME = DateTime.Now,
                LAST_MPERSON = _user.HIS_NAME
            })
            .Where(x=>x.EqpArchivesId == archivesId)
            .ExecuteCommand()>0;
    }
}