﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.BusinessModuleClient
{
    /// <summary>
    /// 供应商资料表
    /// </summary>
    [DBOwner("XH_OA")]
    public class SMS_COMPANY_FILE
    {
        /// <summary>
        /// 资料文件ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string FILE_ID { get; set; }
        /// <summary>
        /// 供应商ID
        /// </summary>
        public string COMPANY_ID { get; set; }
        /// <summary>
        /// 类型
        /// </summary>
        public string FILE_TYPE { get; set; }
        /// <summary>
        /// 资料姓名
        /// </summary>
        public string FILE_NAME { get; set; }
        /// <summary>
        /// 排序号
        /// </summary>
        public string FILE_SORT { get; set; }
        /// <summary>
        /// 文件路径
        /// </summary>
        public string FILE_PATH { get; set; }
        /// <summary>
        /// 有效期
        /// </summary>
        public DateTime? FILE_EXPIRY_DATE { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public string FILE_STATE { get; set; }
        /// <summary>
        /// 首次登记人
        /// </summary>
        public string FIRST_RPERSON { get; set; }
        /// <summary>
        /// 首次登记时间
        /// </summary>
        public DateTime? FIRST_RTIME { get; set; }
        /// <summary>
        /// 最后修改人
        /// </summary>
        public string LAST_MPERSON { get; set; }
        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime? LAST_MTIME { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string REMARK { get; set; }
        public string DOC_TYPE { get; set; }
        /// <summary>
        /// 发证日期
        /// </summary>
        public DateTime CER_DATA { get; set; }

        /// <summary>
        /// 校期提醒日期
        /// </summary>
        public DateTime? CER_WARN_DATE { get; set; }

        /// <summary>
        /// 校期提醒日期文本
        /// </summary>
        public string CER_WARN_CONTENT { get; set; }

        /// <summary>
        /// 上传文件ID
        /// </summary>
        public string UPLOAD_FILE_ID { get; set; }
        public string FILE_PREVIEW_PATH { get; set; }

        /// <summary>
        /// 附件
        /// </summary>
        public string? FILE_RECORD { get; set; }

        [SugarColumn(IsIgnore = true)]
        public string FILEBASE64 { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string FILE_SUFFIX { get; set; }
    }
}
