﻿using Microsoft.EntityFrameworkCore;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.Dtos.Tim
{
    public class ExecRecord
    {
        /// <summary>
        /// 执行记录ID
        /// </summary>
        public string WEXEC_ID { get; set; }

        /// <summary>
        /// 主体ID
        /// </summary>
        public string WORK_MAINID { get; set; }
        /// <summary>
        /// 事务主体名称/设备名称
        /// </summary>
        public string WORK_MAINNAME { get; set; }

        /// <summary>
        /// 记录单版本ID
        /// </summary>
        public string FORM_VER_MAIN_ID { get; set; }
        /// <summary>
        /// 记录单版本
        /// </summary>
        public string FORM_VER_NO { get; set; }

        /// <summary>
        /// 事务ID
        /// </summary>
        public string WORK_ID { get; set; }

        /// <summary>
        /// 事务名称
        /// </summary>
        public string WORK_NAME { get; set; }

        /// <summary>
        /// 记录单分类ID
        /// </summary>
        public string CLASS_ID { get; set; }

        /// <summary>
        /// 记录单分类名称
        /// </summary>
        public string CLASS_NAME { get; set; }
        /// <summary>
        /// 填写时间
        /// </summary>
        public DateTime? RECORD_TIME { get; set; }

        /// <summary>
        /// 记录单ID
        /// </summary>
        public string FORM_ID { get; set; }
        /// <summary>
        /// 记录单名称
        /// </summary>
        public string FORM_NAME { get; set; }

        /// <summary>
        /// 执行人员
        /// </summary>
        public string WEXEC_PERSON { get; set; }

        /// <summary>
        /// 执行结果
        /// </summary>
        public string WEXEC_RESULT { get; set; }

        /// <summary>
        /// 执行时间
        /// </summary>
        public DateTime? WEXEC_TIME { get; set; }

        /// <summary>
        /// 填写人
        /// </summary>
        public string RECORD_PERSON { get; set; }

        /// <summary>
        /// 执行日期
        /// </summary>
        public DateTime? WEXEC_DATE { get; set; }

        /// <summary>
        /// 保养类型
        /// </summary>
        public string WORK_PLAN_TYPE { get; set; }

        /// <summary>
        /// 执行状态
        /// </summary>
        public string WEXEC_STATE { get; set; }

        public DateTime? FIRST_RTIME { get; set; }

        public string LAST_MPERSON { get; set; }

        public string FIRST_RPERSON { get; set; }

        public DateTime? LAST_MTIME { get; set; }

    }
}
