using System;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using SqlSugar;
using H.Utility.SqlSugarInfra;

namespace XH.H82.Models.Entities.Common
{
    [DBOwner("XH_SYS")]
    [SugarTable("SYS6_SOFT_MODULE_INFO")]
    public class SYS6_SOFT_MODULE_INFO
    {
        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Column("MODULE_ID")]
        [Required(ErrorMessage = "不允许为空")]
        [StringLength(20, ErrorMessage = "MODULE_ID长度不能超出20字符")]
        [Unicode(false)]
        public string MODULE_ID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("MODULE_NO")]
        [StringLength(50, ErrorMessage = "MODULE_NO长度不能超出50字符")]
        [Unicode(false)]
        public string? MODULE_NO { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("MODULE_CODE")]
        [StringLength(50, ErrorMessage = "MODULE_CODE长度不能超出50字符")]
        [Unicode(false)]
        public string? MODULE_CODE { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("MODULE_NAME")]
        [StringLength(50, ErrorMessage = "MODULE_NAME长度不能超出50字符")]
        [Unicode(false)]
        public string? MODULE_NAME { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("SOFT_KEY")]
        [StringLength(50, ErrorMessage = "SOFT_KEY长度不能超出50字符")]
        [Unicode(false)]
        public string? SOFT_KEY { get; set; }

        ///// <summary>
        ///// 
        ///// </summary>
        //[Column("MODULE_LINK")]
        //[StringLength(500, ErrorMessage = "MODULE_LINK长度不能超出500字符")]
        //[Unicode(false)]
        //public string? MODULE_LINK { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("MODULE_STATE")]
        [StringLength(50, ErrorMessage = "MODULE_STATE长度不能超出50字符")]
        [Unicode(false)]
        public string? MODULE_STATE { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("FIRST_RPERSON")]
        [StringLength(50, ErrorMessage = "FIRST_RPERSON长度不能超出50字符")]
        [Unicode(false)]
        public string? FIRST_RPERSON { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("FIRST_RTIME")]
        [Unicode(false)]
        public DateTime? FIRST_RTIME { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("LAST_MPERSON")]
        [StringLength(50, ErrorMessage = "LAST_MPERSON长度不能超出50字符")]
        [Unicode(false)]
        public string? LAST_MPERSON { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("LAST_MTIME")]
        [Unicode(false)]
        public DateTime? LAST_MTIME { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("REMARK")]
        [StringLength(200, ErrorMessage = "REMARK长度不能超出200字符")]
        [Unicode(false)]
        public string? REMARK { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("REGISTER_PERSON")]
        [StringLength(50, ErrorMessage = "REGISTER_PERSON长度不能超出50字符")]
        [Unicode(false)]
        public string? REGISTER_PERSON { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("REGISTER_TIME")]
        [StringLength(50, ErrorMessage = "REGISTER_TIME长度不能超出50字符")]
        [Unicode(false)]
        public string? REGISTER_TIME { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("PATIENT_CODE")]
        [StringLength(50, ErrorMessage = "PATIENT_CODE长度不能超出50字符")]
        [Unicode(false)]
        public string? PATIENT_CODE { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("MODULE_CNAME")]
        [StringLength(50, ErrorMessage = "MODULE_CNAME长度不能超出50字符")]
        [Unicode(false)]
        public string? MODULE_CNAME { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("PROGRAM_TYPE")]
        [StringLength(50, ErrorMessage = "PROGRAM_TYPE长度不能超出50字符")]
        [Unicode(false)]
        public string? PROGRAM_TYPE { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("MODULE_PATH")]
        [StringLength(100, ErrorMessage = "MODULE_PATH长度不能超出100字符")]
        [Unicode(false)]
        public string? MODULE_PATH { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("DATABASE_ID")]
        [StringLength(20, ErrorMessage = "DATABASE_ID长度不能超出20字符")]
        [Unicode(false)]
        public string? DATABASE_ID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("IIS_SERVER")]
        [StringLength(100, ErrorMessage = "IIS_SERVER长度不能超出100字符")]
        [Unicode(false)]
        public string? IIS_SERVER { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("MODULE_PERSON")]
        [StringLength(50, ErrorMessage = "MODULE_PERSON长度不能超出50字符")]
        [Unicode(false)]
        public string? MODULE_PERSON { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("MODULE_TIME")]
        [StringLength(50, ErrorMessage = "MODULE_TIME长度不能超出50字符")]
        [Unicode(false)]
        public string? MODULE_TIME { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("MODULE_VER")]
        [StringLength(50, ErrorMessage = "MODULE_VER长度不能超出50字符")]
        [Unicode(false)]
        public string? MODULE_VER { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("MODULE_CLASS")]
        [StringLength(50, ErrorMessage = "MODULE_CLASS长度不能超出50字符")]
        [Unicode(false)]
        public string? MODULE_CLASS { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("ROLE_TYPE")]
        [StringLength(20, ErrorMessage = "ROLE_TYPE长度不能超出20字符")]
        [Unicode(false)]
        public string? ROLE_TYPE { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("PROGRAM_URL")]
        [StringLength(2000, ErrorMessage = "PROGRAM_URL长度不能超出2000字符")]
        [Unicode(false)]
        public string? PROGRAM_URL { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("SYSTEM_ID")]
        [StringLength(50, ErrorMessage = "SYSTEM_ID长度不能超出50字符")]
        [Unicode(false)]
        public string? SYSTEM_ID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("MODULE_PORT")]
        [StringLength(20, ErrorMessage = "MODULE_PORT长度不能超出20字符")]
        [Unicode(false)]
        public string? MODULE_PORT { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("INSTANCE_ID")]
        [StringLength(20, ErrorMessage = "INSTANCE_ID长度不能超出20字符")]
        [Unicode(false)]
        public string? INSTANCE_ID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("CALL_MODULE")]
        [StringLength(500, ErrorMessage = "CALL_MODULE长度不能超出500字符")]
        [Unicode(false)]
        public string? CALL_MODULE { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("IF_MAIN_MODULE")]
        [StringLength(20, ErrorMessage = "IF_MAIN_MODULE长度不能超出20字符")]
        [Unicode(false)]
        public string? IF_MAIN_MODULE { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("IF_MAIN_PROGRAM")]
        [StringLength(20, ErrorMessage = "IF_MAIN_PROGRAM长度不能超出20字符")]
        [Unicode(false)]
        public string? IF_MAIN_PROGRAM { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("IF_REGISTER")]
        [StringLength(20, ErrorMessage = "IF_REGISTER长度不能超出20字符")]
        [Unicode(false)]
        public string? IF_REGISTER { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("REGISTER_STATE")]
        [StringLength(10, ErrorMessage = "REGISTER_STATE长度不能超出10字符")]
        [Unicode(false)]
        public string? REGISTER_STATE { get; set; }

        ///// <summary>
        ///// 
        ///// </summary>
        //[Column("HOSPITAL_ID")]
        //[StringLength(20, ErrorMessage = "HOSPITAL_ID长度不能超出20字符")]
        //[Unicode(false)]
        //public string? HOSPITAL_ID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("SETUP_UNIT")]
        [StringLength(50, ErrorMessage = "SETUP_UNIT长度不能超出50字符")]
        [Unicode(false)]
        public string? SETUP_UNIT { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("TWO_FACTOR_MODE")]
        [StringLength(20, ErrorMessage = "TWO_FACTOR_MODE长度不能超出20字符")]
        [Unicode(false)]
        public string? TWO_FACTOR_MODE { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("IF_USE_ELK")]
        [StringLength(20, ErrorMessage = "IF_USE_ELK长度不能超出20字符")]
        [Unicode(false)]
        public string? IF_USE_ELK { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("DATABASE_ID2")]
        [StringLength(20, ErrorMessage = "DATABASE_ID2长度不能超出20字符")]
        [Unicode(false)]
        public string? DATABASE_ID2 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("XT_MENU_CLASS")]
        [StringLength(20, ErrorMessage = "XT_MENU_CLASS长度不能超出20字符")]
        [Unicode(false)]
        public string? XT_MENU_CLASS { get; set; }


    }
}
