﻿using H.BASE.SqlSugarInfra.Uow;
using <PERSON><PERSON>;
using Org.BouncyCastle.Crypto.Generators;
using SqlSugar;
using XH.H82.Models.DeactivationScrapping;
using XH.H82.Models.Entities;
using XH.H82.Models.SugarDbContext;
using static iTextSharp.text.pdf.AcroFields;

namespace XH.H82.Services.DeactivationScrapping
{
    public class DeactivationScrappingContext
    {
        private readonly ISqlSugarUow<SugarDbContext_Master> _dbContext;

        private List<EMS_EQUIPMENT_INFO> _equipments { get; set; } = new();

        public DeactivationScrappingContext(ISqlSugarUow<SugarDbContext_Master> dbContext) => _dbContext = dbContext;

        /// <summary>
        /// 切换上下文
        /// </summary>
        /// <param name="ids"></param>
        /// <exception cref="BizException"></exception>
        public void AsEquipment(List<string> ids)
        {
            var equipments = _dbContext.Db
                .Queryable<EMS_EQUIPMENT_INFO>()
                .Includes(x => x.eMS_SCRAP_INFO)
                .Includes(x => x.eMS_START_STOP)
                .Where(x => ids.Contains(x.EQUIPMENT_ID))
                .ToList();

            if (equipments.Count() == 0)
            {
                throw new BizException("当前设备已不存在");
            }
            _equipments.Clear();
            _equipments.AddRange(equipments);
        }

        public void AsEquipment()
        {
            _equipments.Clear();
            _dbContext.Db
                .Queryable<EMS_EQUIPMENT_INFO>()
                .Where(x => x.EQUIPMENT_STATE == "1")
                .Includes(x => x.eMS_SCRAP_INFO)
                .Includes(x => x.eMS_START_STOP)
                .ForEach(x => _equipments.Add(x));
        }

        public List<EMS_EQUIPMENT_INFO> GetEquipments() => _equipments;

        /// <summary>
        /// 停用审批通过后，设备状态变为待停用
        /// </summary>
        /// <param name="user"></param>
        /// <param name="scrap"></param>
        public void CreatStopRecord(ClaimsDto user, EMS_SCRAP_INFO scrap)
        {
            var stop = new EMS_START_STOP();
            stop.START_STOP_ID = IDGenHelper.CreateGuid().ToString();
            stop.HOSPITAL_ID = user.HOSPITAL_ID;
            stop.EQUIPMENT_ID = scrap.EQUIPMENT_ID;
            stop.START_CAUSE = scrap.SCRAP_CAUSE;
            stop.OPER_PERSON = scrap.OPER_PERSON;
            stop.OPER_TIME = scrap.OPER_TIME;
            stop.START_STOP_STATE = "1";
            stop.START_STOP_TYPE = "0";
            stop.START_DATE = scrap.SCRAP_DATE;
            stop.FIRST_RPERSON = user.HIS_NAME;
            stop.FIRST_RTIME = DateTime.Now;
            stop.LAST_MPERSON = user.HIS_NAME;
            stop.LAST_MTIME = DateTime.Now;
            _dbContext.Db.Insertable(stop).ExecuteCommand();
            _dbContext.Db.Updateable<EMS_EQUIPMENT_INFO>().SetColumns(p => new EMS_EQUIPMENT_INFO
            {
                EQUIPMENT_STATE = "2",
                LAST_MPERSON = user.HIS_NAME,
                LAST_MTIME = DateTime.Now
            }).Where(p => p.EQUIPMENT_ID == scrap.EQUIPMENT_ID).ExecuteCommand();

        }

        /// <summary>
        /// 当停用审批被撤销时，停用记录对应删除
        /// </summary>
        /// <param name="scrap"></param>
        public void CancelStopRecord(ClaimsDto user, EMS_SCRAP_INFO scrap)
        {
            var stop = _dbContext.Db.Queryable<EMS_START_STOP>().Where(x => x.EQUIPMENT_ID == scrap.EQUIPMENT_ID).OrderByDescending(x => x.OPER_TIME).First();

            if (stop is null)
            {
                return;
            }
            _dbContext.Db.Deleteable(stop).ExecuteCommand();

            _dbContext.Db.Updateable<EMS_EQUIPMENT_INFO>().SetColumns(p => new EMS_EQUIPMENT_INFO
            {
                EQUIPMENT_STATE = "1",
                LAST_MPERSON = user.HIS_NAME,
                LAST_MTIME = DateTime.Now
            }).Where(p => p.EQUIPMENT_ID == scrap.EQUIPMENT_ID).ExecuteCommand();

        }

        /// <summary>
        /// 报废审批通过后，设备状态改为待报废
        /// </summary>
        /// <param name="user"></param>
        /// <param name="scrap"></param>
        public void EquipmentScaped(ClaimsDto user, EMS_SCRAP_INFO scrap)
        {
            _dbContext.Db.Updateable<EMS_EQUIPMENT_INFO>().SetColumns(p => new EMS_EQUIPMENT_INFO
            {
                EQUIPMENT_STATE = "3",
                LAST_MPERSON = user.HIS_NAME,
                LAST_MTIME = DateTime.Now
            }).Where(p => p.EQUIPMENT_ID == scrap.EQUIPMENT_ID).ExecuteCommand();
        }


        /// <summary>
        /// 报废审批通过后，设备状态改为启用
        /// </summary>
        /// <param name="user"></param>
        /// <param name="scrap"></param>
        public void CancelEquipmentScaped(ClaimsDto user, EMS_SCRAP_INFO scrap)
        {
            _dbContext.Db.Updateable<EMS_EQUIPMENT_INFO>().SetColumns(p => new EMS_EQUIPMENT_INFO
            {
                EQUIPMENT_STATE = "1",
                LAST_MPERSON = user.HIS_NAME,
                LAST_MTIME = DateTime.Now
            }).Where(p => p.EQUIPMENT_ID == scrap.EQUIPMENT_ID).ExecuteCommand();
        }


        /// <summary>
        /// 保存报废停用草稿
        /// </summary>
        /// <param name="user"></param>
        /// <param name="input"></param>
        public void DraftScapOrStop(ClaimsDto user, SubmitScapOrStopInput input)
        {
            var scraps = new List<EMS_SCRAP_INFO>();
            foreach (var equipment in _equipments)
            {
                var scrap = new EMS_SCRAP_INFO()
                {
                    SCRAP_ID = IDGenHelper.CreateGuid().ToString(),
                    APPLY_TYPE = input.applyType,
                    HOSPITAL_ID = user.HOSPITAL_ID,
                    EQUIPMENT_ID = equipment.EQUIPMENT_ID,
                    SCRAP_CAUSE = input.applyReason,
                    SCRAP_STATE = "1",
                    OPER_PERSON = user.HIS_NAME,
                    OPER_PERSON_ID = user.USER_NO,
                    OPER_TIME = DateTime.Now,
                    FIRST_RPERSON = user.HIS_NAME,
                    FIRST_RTIME = DateTime.Now,
                    LAST_MPERSON = user.HIS_NAME,
                    LAST_MTIME = DateTime.Now,
                    EXAMINE_PERSON = input.examinePerson,
                    EXAMINE_PERSON_ID = input.examinePersonId,
                    APPLY_STATE = "0", //草稿状态   待提交
                    IF_RE_APPLY = "0",
                    SCRAP_DATE = input.scrapDate,
                };
                scraps.Add(scrap);
            }
            _dbContext.Db.Insertable(scraps).ExecuteCommand();
        }

        /// <summary>
        /// 保存报废停用记录信息更新
        /// </summary>
        /// <param name="user"></param>
        /// <param name="scrap"></param>
        /// <param name="input"></param>
        public void UpdateDraftScapOrStop(ClaimsDto user, EMS_SCRAP_INFO scrap, SubmitScapOrStopInput input)
        {
            var scrapInfo = _dbContext.Db.Queryable<EMS_SCRAP_INFO>().First(x => x.SCRAP_ID == scrap.SCRAP_ID);

            if (scrapInfo is null)
            {
                AsEquipment(new List<string> { scrap.EQUIPMENT_ID });
                DraftScapOrStop(user, input);
                return;
            }

            scrap.HOSPITAL_ID = user.HOSPITAL_ID;
            scrap.OPER_PERSON = user.HIS_NAME;
            scrap.OPER_PERSON_ID = user.USER_NO;
            scrap.LAST_MPERSON = user.HIS_NAME;
            scrap.LAST_MTIME = DateTime.Now;

            scrap.APPLY_TYPE = input.applyType;
            scrap.SCRAP_CAUSE = input.applyReason;
            scrap.EXAMINE_PERSON = input.examinePerson;
            scrap.EXAMINE_PERSON_ID = input.examinePersonId;
            scrap.SCRAP_DATE = input.scrapDate;
            _dbContext.Db.Insertable(scrap).ExecuteCommand();
        }

        /// <summary>
        /// 提交报废停用记录
        /// </summary>
        /// <param name="user"></param>
        /// <param name="inputs"></param>
        public void SubmitScapOrStop(ClaimsDto user, List<EMS_SCRAP_INFO> scraps, SubmitScapOrStopInput input)
        {
            foreach (var scrap in scraps)
            {
                scrap.APPLY_TYPE = input.applyType;
                scrap.SCRAP_CAUSE = input.applyReason;
                scrap.EXAMINE_PERSON = input.examinePerson;
                scrap.EXAMINE_PERSON_ID = input.examinePersonId;
                scrap.SCRAP_DATE = input.scrapDate;
                scrap.APPLY_STATE = "1";
                scrap.LAST_MPERSON = user.HIS_NAME;
                scrap.LAST_MTIME = DateTime.Now;
            }
            _dbContext.Db.Updateable(scraps).IgnoreColumns(ignoreAllNullColumns:true).ExecuteCommand();
            AddScapOrStopLog(user, scraps, $"{input.applyType}申请", input.applyReason);
        }

        /// <summary>
        /// 重新提交报废停用记录
        /// </summary>
        /// <param name="user"></param>
        /// <param name="input"></param>
        public void ReSubmitScapOrStop(ClaimsDto user, List<EMS_SCRAP_INFO> scraps, SubmitScapOrStopInput input)
        {
            foreach (var scrap in scraps)
            {
                scrap.APPLY_TYPE = input.applyType;
                scrap.SCRAP_CAUSE = input.applyReason;
                scrap.EXAMINE_PERSON = input.examinePerson;
                scrap.EXAMINE_PERSON_ID = input.examinePersonId;
                scrap.SCRAP_DATE = input.scrapDate;
                scrap.IF_RE_APPLY = "1";
                scrap.APPLY_STATE = "1";
                scrap.LAST_MPERSON = user.HIS_NAME;
                scrap.LAST_MTIME = DateTime.Now;
            }
            _dbContext.Db.Updateable(scraps).ExecuteCommand();
            AddScapOrStopLog(user, scraps, $"{input.applyType}重新申请", input.applyReason);
        }

        /// <summary>
        /// 提交记录被驳回
        /// </summary>
        /// <param name="user"></param>
        /// <param name="scraps"></param>
        /// <param name="examineOpinion"></param>
        /// <returns></returns>
        public void SubmitFailed(ClaimsDto user, List<EMS_SCRAP_INFO> scraps, string examineOpinion)
        {
            foreach (var scrap in scraps)
            {
                scrap.APPLY_STATE = "4";
                scrap.EXAMINE_DATE = DateTime.Now;
                scrap.EXAMINE_OPINION = examineOpinion;
                scrap.LAST_MPERSON = user.HIS_NAME;
                scrap.LAST_MTIME = DateTime.Now;
            }
            _dbContext.Db.Updateable(scraps).IgnoreColumns(ignoreAllNullColumns:true).ExecuteCommand();
            AddScapOrStopLog(user, scraps, $"审核驳回", examineOpinion);
        }


        /// <summary>
        /// 提交的报废停用申请通过
        /// </summary>
        /// <param name="user"></param>
        /// <param name="scraps"></param>
        /// <param name="input"></param>
        public void SubmitPass(ClaimsDto user, List<EMS_SCRAP_INFO> scraps, SubmitPassInput input)
        {
            var records = new List<EMS_SCRAP_LOG>();
            var ids = scraps.Select(x => x.EQUIPMENT_ID).ToList();
            AsEquipment(ids);
            foreach (var scrap in scraps)
            {
                var equipment = _equipments.FirstOrDefault(x => x.EQUIPMENT_ID == scrap.EQUIPMENT_ID);

                if (equipment is not null)
                {
                    scrap.APPLY_STATE = "2";
                    scrap.EXAMINE_DATE = DateTime.Now;
                    scrap.EXAMINE_OPINION = $"同意{equipment!.EQUIPMENT_CODE}" + scrap.APPLY_TYPE;
                    scrap.APPROVE_PERSON = input.auditPerson;
                    scrap.APPROVE_PERSON_ID = input.auditPersonId;
                    scrap.LAST_MPERSON = user.HIS_NAME;
                    scrap.LAST_MTIME = DateTime.Now;

                    records.Add(CreateScapOrStopLog(user, scrap, "审核通过", $"同意{equipment.EQUIPMENT_CODE}{scrap.APPLY_TYPE}"));
                }
            }
            _dbContext.Db.Updateable(scraps).ExecuteCommand();
            _dbContext.Db.Insertable(records).ExecuteCommand();

        }

        /// <summary>
        /// 审核结果被通过
        /// </summary>
        /// <param name="user"></param>
        /// <param name="scraps"></param>
        /// <param name="input"></param>
        public void AuditPass(ClaimsDto user, List<EMS_SCRAP_INFO> scraps, AuditPassInput input)
        {

            var records = new List<EMS_SCRAP_LOG>();
            var ids = scraps.Select(x => x.EQUIPMENT_ID).ToList();
            AsEquipment(ids);
            foreach (var scrap in scraps)
            {
                var equipment = _equipments.FirstOrDefault(x => x.EQUIPMENT_ID == scrap.EQUIPMENT_ID);
                if (equipment is not null)
                {
                    scrap.APPLY_STATE = "3";
                    scrap.EXAMINE_DATE = DateTime.Now;
                    scrap.APPROVE_OPINION = $"同意{equipment!.EQUIPMENT_CODE}" + scrap.APPLY_TYPE;
                    scrap.LAST_MPERSON = user.HIS_NAME;
                    scrap.LAST_MTIME = DateTime.Now;

                    records.Add(CreateScapOrStopLog(user, scrap, "审批通过", $"同意{equipment.EQUIPMENT_CODE}{scrap.APPLY_TYPE}"));
                }
            }
            _dbContext.Db.Updateable(scraps).ExecuteCommand();
            _dbContext.Db.Insertable(records).ExecuteCommand();
        }

        /// <summary>
        /// 审核结果被驳回
        /// </summary>
        /// <param name="user"></param>
        /// <param name="scraps"></param>
        /// <param name="approveOpinion"></param>
        public void AuditFailed(ClaimsDto user, List<EMS_SCRAP_INFO> scraps, string approveOpinion)
        {
            foreach (var scrap in scraps)
            {
                scrap.APPLY_STATE = "5";
                scrap.EXAMINE_DATE = DateTime.Now;
                scrap.APPROVE_OPINION = approveOpinion;
                scrap.LAST_MPERSON = user.HIS_NAME;
                scrap.LAST_MTIME = DateTime.Now;
            }
            _dbContext.Db.Updateable(scraps).ExecuteCommand();
            AddScapOrStopLog(user, scraps, $"审批驳回", approveOpinion);

        }


        /// <summary>
        /// 停用报废记录撤销
        /// </summary>
        /// <param name="user"></param>
        /// <param name="scraps"></param>
        public void RevokedApprove(ClaimsDto user, List<EMS_SCRAP_INFO> scraps)
        {
            foreach (var scrap in scraps)
            {
                var records = _dbContext.Db.Queryable<EMS_SCRAP_LOG>()
                    .Where(x => x.PROCESS_STATE == "1")
                    .Where(x => x.SCRAP_ID == scrap.SCRAP_ID)
                    .OrderByDescending(x => x.OPER_TIME)
                    .ToList();
                if (records.Count() == 0)
                {
                    continue;
                }
                switch (scrap.APPLY_STATE)
                {
                    case "1":
                        scrap.APPLY_STATE = "0";
                        scrap.LAST_MPERSON = user.HIS_NAME;
                        scrap.LAST_MTIME = DateTime.Now;
                        break;

                    case "2":
                        scrap.APPLY_STATE = "1";
                        scrap.APPROVE_PERSON = null;
                        scrap.APPROVE_PERSON_ID = null;
                        scrap.EXAMINE_OPINION = null;
                        scrap.EXAMINE_DATE = null;
                        scrap.LAST_MPERSON = user.HIS_NAME;
                        scrap.LAST_MTIME = DateTime.Now;
                        break;
                    case "4":
                        scrap.APPLY_STATE = "1";
                        scrap.APPROVE_PERSON = null;
                        scrap.APPROVE_PERSON_ID = null;
                        scrap.EXAMINE_OPINION = null;
                        scrap.EXAMINE_DATE = null;
                        scrap.LAST_MPERSON = user.HIS_NAME;
                        scrap.LAST_MTIME = DateTime.Now;
                        break;
                    case "3":
                        scrap.APPLY_STATE = "2";
                        scrap.APPROVE_DATE = null;
                        scrap.APPROVE_OPINION = null;
                        scrap.LAST_MPERSON = user.HIS_NAME;
                        scrap.LAST_MTIME = DateTime.Now;
                        break;
                    case "5":
                        scrap.APPLY_STATE = "2";
                        scrap.APPROVE_DATE = null;
                        scrap.APPROVE_OPINION = null;
                        scrap.LAST_MPERSON = user.HIS_NAME;
                        scrap.LAST_MTIME = DateTime.Now;
                        break;
                    default:
                        break;
                }

                _dbContext.Db.Deleteable(records[0]).ExecuteCommand();
            }
            _dbContext.Db.Updateable(scraps).ExecuteCommand();
        }


        /// <summary>
        /// 新增报废停用操作记录
        /// </summary>
        /// <param name="user"></param>
        /// <param name="scraps"></param>
        private void AddScapOrStopLog(ClaimsDto user, List<EMS_SCRAP_INFO> scraps, string logType, string content)
        {

            var records = new List<EMS_SCRAP_LOG>();
            foreach (var scrap in scraps)
            {
                var scrapLog = new EMS_SCRAP_LOG()
                {
                    SCRAP_PROCESS_ID = IDGenHelper.CreateGuid().ToString(),
                    HOSPITAL_ID = user.HOSPITAL_ID,
                    SCRAP_ID = scrap.SCRAP_ID,
                    PROCESS_STATE = "1",
                    OPER_PERSON = user.HIS_NAME,
                    OPER_TIME = DateTime.Now,
                    PROCESS_TYPE = $"{logType}",
                    PROCESS_CONTENT = content,
                    FIRST_RPERSON = user.HIS_NAME,
                    FIRST_RTIME = DateTime.Now,
                    LAST_MPERSON = user.HIS_NAME,
                    LAST_MTIME = DateTime.Now,
                };
                records.Add(scrapLog);
            }
            _dbContext.Db.Insertable(records).ExecuteCommand();
        }

        /// <summary>
        /// 处保存草稿操作外，都需要补充一个操作记录的日志
        /// </summary>
        /// <param name="user"></param>
        /// <param name="scrap"></param>
        /// <param name="logType"></param>
        /// <param name="content"></param>
        /// <returns></returns>
        private EMS_SCRAP_LOG CreateScapOrStopLog(ClaimsDto user, EMS_SCRAP_INFO scrap, string logType, string content)
        {
            var scrapLog = new EMS_SCRAP_LOG()
            {
                SCRAP_PROCESS_ID = IDGenHelper.CreateGuid().ToString(),
                HOSPITAL_ID = user.HOSPITAL_ID,
                SCRAP_ID = scrap.SCRAP_ID,
                PROCESS_STATE = "1",
                OPER_PERSON = user.HIS_NAME,
                OPER_TIME = DateTime.Now,
                PROCESS_TYPE = $"{logType}",
                PROCESS_CONTENT = content,
                FIRST_RPERSON = user.HIS_NAME,
                FIRST_RTIME = DateTime.Now,
                LAST_MPERSON = user.HIS_NAME,
                LAST_MTIME = DateTime.Now,
            };

            return scrapLog;
        }
    }
}
