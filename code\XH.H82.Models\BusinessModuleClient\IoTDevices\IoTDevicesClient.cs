﻿using System.Net.Security;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using H.Utility;
using Microsoft.AspNetCore.Http;
using NetTaste;
using Newtonsoft.Json;
using RestSharp;
using Serilog;
using XH.H82.Models.BusinessModuleClient.Dto.IoTDevices;

namespace XH.H82.Models.BusinessModuleClient.IoTDevices;

public class IoTDevicesClient  
{
    // const string key = "fcacf51f667db1f0";
    // const string content = "sysczy004:sysczy!";
    //省人内网
    const string key = "d910c2a468a639c2";
    private const string content = "xhkj:Xhkj@123";
    private string address { get; set; } = "";
    private string token { get; set; } = "";
    private IHttpContextAccessor _httpContext;
    public IoTDevicesClient(string ip, IHttpContextAccessor httpContext)
    {
        Log.Information($"第三方url地址为：{ip}");
        address = ip;
        _httpContext = httpContext;
    }

    public T1 ClientGet<T,T1>(string url, T requestBody = default(T), bool isNeedToken = true)
    {
        
        using RestClient client = new RestClient(
            new RestClientOptions
            {
                RemoteCertificateValidationCallback = (object sender, X509Certificate? certificate, X509Chain? chain, SslPolicyErrors sslPolicyErrors) => true,
                BaseUrl = new Uri(address),
                ThrowOnAnyError = true,
            });
        Log.Information($"开始请求第三方接口:{address}{url}");
        RestRequest request = new RestRequest(url);
        if (requestBody != null)
        {
            request.AddBody(requestBody);
        }
        if (isNeedToken)
        {
            request.AddHeader("Authorization", token);
        }
        try
        {
            Log.Information($"请求第三方接口为:{address}{url}");
            RestResponse<T1> restResponse = client.ExecuteGet<T1>(request);
            if (restResponse.IsSuccessful)
            {
                Log.Information($"请求第三方接口{address}{url}成功:{JsonConvert.SerializeObject(restResponse.Data,Formatting.Indented)}");
                return restResponse.Data;
            }
            Log.Error($"调用第三方接口[{address}{url}]失败:{restResponse.ErrorException}");
            throw new BizException($"调用第三方接口[{address}{url}]失败:{restResponse.ErrorException}");
        }
        catch (Exception ex)
        {
            Log.Error($"调用第三方接口[{address}{url}]错误:{ex.InnerException}");
            throw new BizException(ex.Message);
        }
    }
    
    public T1 ClientPost<T,T1>(string url, T requestBody = default(T), bool isNeedToken = true)
            {
                
                using RestClient client = new RestClient(new RestClientOptions
                {
                    RemoteCertificateValidationCallback = (object sender, X509Certificate? certificate, X509Chain? chain, SslPolicyErrors sslPolicyErrors) => true,
                    BaseUrl = new Uri(address),
                    ThrowOnAnyError = true
                });
                Log.Information($"开始请求第三方接口:{address}{url}");
                RestRequest request = new RestRequest(url);
                if (requestBody != null)
                {
                    Log.Information($"开始请求第三方入参:{JsonConvert.SerializeObject(requestBody,Formatting.Indented)}");
                    request.AddBody(requestBody);
                }
                if (isNeedToken)
                {
                    request.AddHeader("Authorization", token);
                }
                try
                {
                    RestResponse<T1> restResponse = client.ExecutePost<T1>(request);
                    if (restResponse.IsSuccessful)
                    {
                        Log.Information($"请求第三方接口{address}{url}成功:{JsonConvert.SerializeObject(restResponse.Data,Formatting.Indented)}");
                        return restResponse.Data;
                    }
                    Log.Error($"调用第三方接口[{address}{url}]失败:{restResponse.ErrorException}");
                    throw new BizException($"调用第三方接口[{address}{url}]失败:{restResponse.ErrorException}");
                }
                catch (Exception ex)
                {
                    Log.Error($"调用第三方接口[{address}{url}]错误:{ex.InnerException}");
                    throw new BizException(ex.Message);
                }
            }

    /// <summary>
    /// 加密
    /// </summary>
    /// <param name="sn">设备sn</param>
    /// <returns></returns>
    private void GetToken(string sn = "")
    {
        var aesKey =  GetAesKey();
        var rsp =  ClientPost<TokenInput,TokenResult>("/ocean-api/getAccessToken",new TokenInput(aesKey), false);
        token = rsp.token;
        Log.Information($"第三方接口token为:{token}");
    }
    
    /// <summary>
    /// AES加密
    /// </summary>
    /// <param name="plainText"></param>
    /// <param name="password"></param>
    /// <returns></returns>
    public static string Encrypt(string plainText, string password)
    {
        Log.Information($"加密前：{plainText}");

        var newKey = GetKey(password);
        try
        {
            // 将密码转换为字节数组
            // 使用密码生成AES密钥
            using (Aes aesAlg = Aes.Create())
            {
                aesAlg.Key = newKey;
                aesAlg.Mode = CipherMode.ECB;
                aesAlg.Padding = PaddingMode.PKCS7;

                ICryptoTransform encryptor = aesAlg.CreateEncryptor(aesAlg.Key, aesAlg.IV);

                byte[] byteContent = Encoding.UTF8.GetBytes(content);

                byte[] encryptedBytes = encryptor.TransformFinalBlock(byteContent, 0, byteContent.Length);

                string finalKey = Convert.ToBase64String(encryptedBytes);
                return finalKey;
            }
        }
        catch (Exception e)
        {
            Console.WriteLine(e.Message);
            return null;
        }
        

    }

    /// <summary>
    /// 解密
    /// </summary>
    /// <param name="content">待解密内容</param>
    /// <param name="password">解密密钥</param>
    /// <returns>解密后的字符串</returns>
    public static string Decrypt(string content, string password)
    {
        try
        {
            byte[] secretKey = GetKey(password);
            using (Aes aesAlg = Aes.Create())
            {
                aesAlg.Key = secretKey;
                aesAlg.Mode = CipherMode.ECB;
                aesAlg.Padding = PaddingMode.PKCS7;

                ICryptoTransform decryptor = aesAlg.CreateDecryptor(aesAlg.Key, aesAlg.IV);

                byte[] encryptedBytes = Convert.FromBase64String(content);

                byte[] decryptedBytes = decryptor.TransformFinalBlock(encryptedBytes, 0, encryptedBytes.Length);

                return Encoding.UTF8.GetString(decryptedBytes);
            }
        }
        catch (Exception e)
        {
            Console.WriteLine(e.Message);
            return null;
        }
    }

    /// <summary>
    /// 获取最终key
    /// </summary>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    public static string GetAesKey()
    {
        try
        {
            byte[] secretKey = GetKey(key);
            using (Aes aesAlg = Aes.Create())
            {
                aesAlg.Key = secretKey;
                aesAlg.Mode = CipherMode.ECB;
                aesAlg.Padding = PaddingMode.PKCS7;

                ICryptoTransform encryptor = aesAlg.CreateEncryptor(aesAlg.Key, aesAlg.IV);

                byte[] byteContent = Encoding.UTF8.GetBytes(content);

                byte[] encryptedBytes = encryptor.TransformFinalBlock(byteContent, 0, byteContent.Length);

                string finalKey = Convert.ToBase64String(encryptedBytes);
                return finalKey;
            }
        }
        catch (Exception e)
        {
            throw new Exception("获取最终密钥出现异常", e);
        }
        
    }
    /// <summary>
    /// 生成密钥
    /// </summary>
    /// <param name="strKey">密钥字符串</param>
    /// <returns>生成的密钥</returns>
    public static byte[] GetKey(string strKey)
    {
        try
        {
            using (var st = new SHA1CryptoServiceProvider())
            {
                //return sha1.ComputeHash(seed);
                using(var nd = new SHA1CryptoServiceProvider())
                {
                    var rd = nd.ComputeHash(st.ComputeHash(Encoding.UTF8.GetBytes(strKey)));
                    byte[] keyArray = rd.Take(16).ToArray();
                    return keyArray;
                }
            }
        }
        catch (Exception e)
        {
            throw new Exception("初始化密钥出现异常", e);
        }
    }
    /// <summary>
    /// 实时获取开关物联设备功率
    /// </summary>
    /// <param name="sn">设备sn码</param>
    /// <param name="model">设备型号</param>
    /// <returns></returns>
    public IoTDevicesResult GetIoTDeviceInfo(string? id, string? model)
    {
        var url = $"/ocean-api/api/bio/device/switch/statistic?model={model}";
        if (id.IsNotNullOrEmpty())
        {
            url = $"/ocean-api/api/bio/device/switch/statistic?id={id}";
        }
        GetToken();
        var result = ClientGet<object,IoTDevicesResult>(url, null, true);

        if (result.Code ==  500)
        {
            Log.Information($"调用第三方接口[{address}{url}]成功，但是业务错误：{result.Data}");
            throw new BizException($"调用第三方接口[{address}{url}]成功，但是业务错误：{result.Data}");
        }
        return result;

        // return new IoTDevicesResult()
        // {
        //     Code = 0, Msg = "操作成功",
        //     Rows = new IoTDevicesDto()
        //     {
        //         Id = "405953804392022016",
        //         Sn = "8CCE4E4E3C4F",
        //         Name = "生物安全柜_1",
        //         Model = "1379323011108",
        //         Type = 701,
        //         Ip = "**************",
        //         OpenStatus = 1,
        //         RoomId = 2000000,
        //         RoomName = "房间001",
        //         CheckpointId = null,
        //         CheckpointName = null,
        //         LabId = "1000001",
        //         LabName = null,
        //         IsOnline = 0,
        //         SwitchStatus = 0,
        //         Remark = null,
        //         CreateTime = DateTime.Parse("2025-01-04 14:40:03"),
        //         Voltage = 237.113,
        //         Current = 0.0,
        //         Power = 0.027,
        //         Energy = 2.986,
        //         PowerSyncTime = DateTime.Parse("2025-01-08 12:25:02")
        //
        //     }
        // };
    }

    
    public RowResult<List<object>> GetIoTDevices()
    {
        var url = $"/ocean-api/api/bio/device/switch/list";
        GetToken();
        var result = ClientGet<object,RowResult<List<object>>>(url, null, true);
        return result;
    }
    
    public RowResult<List<object>> GetIChuanganqi()
    {
        var url = $"/ocean-api/api/bio/device/sensor/list";
        GetToken();
        var result = ClientGet<object,RowResult<List<object>>>(url, null, true);
        return result;
    }

    public object GetMentjin()
    {
        var url = $"/ocean-api/api/bio/device/list";
        GetToken();
        using RestClient client = new RestClient(
            new RestClientOptions
            {
                RemoteCertificateValidationCallback = (object sender, X509Certificate? certificate, X509Chain? chain, SslPolicyErrors sslPolicyErrors) => true,
                BaseUrl = new Uri(address),
                ThrowOnAnyError = true,
            });
        Log.Information($"开始请求第三方接口:{address}{url}");
        RestRequest request = new RestRequest(url);
        request.AddHeader("Authorization", token);
        try
        {
            Log.Information($"请求第三方接口为:{address}{url}");
            var restResponse = client.ExecuteGet<object>(request);
            Log.Information(JsonConvert.SerializeObject(restResponse));
            if (restResponse.IsSuccessful)
            {
                return restResponse.Data;
            }
            Log.Error($"调用第三方接口[{address}{url}]失败:{restResponse.ErrorException}");
            throw new BizException($"调用第三方接口[{address}{url}]失败:{restResponse.ErrorException}");
        }
        catch (Exception ex)
        {
            Log.Error($"调用第三方接口[{address}{url}]错误:{ex.InnerException}");
            throw new BizException(ex.Message);
        }
    }

    /// <summary>
    /// 获取环境一体机的实时信息
    /// </summary>
    /// <param name="sn"></param>
    /// <param name="model"></param>
    /// <returns></returns>
    /// <exception cref="BizException"></exception>
    public IoTDevicesResult<List<EnvironmentDevicesDto>> GetEnvironmentDevicesInfo(string? sn ,  string? model)
    {
        var url = $"/ocean-api/api/bio/env/list?model={model}";
        if (sn.IsNotNullOrEmpty())
        {
            url = $"/ocean-api/api/bio/env/list?sn={sn}";
        }
        GetToken();
        var result = ClientGet<object,IoTDevicesResult<List<EnvironmentDevicesDto>>>(url, null, true);
        if (result.Code ==  500)
        {
            Log.Information($"调用第三方接口[{address}{url}]成功，但是业务错误：{result.Data}");
            throw new BizException($"调用第三方接口[{address}{url}]成功，但是业务错误：{result.Data}");
        }
        return result;
    }

    /// <summary>
    /// 获取紫外灯实时监数据
    /// </summary>
    /// <param name="sn"></param>
    /// <param name="model"></param>
    /// <returns></returns>
    /// <exception cref="BizException"></exception>
    public UltravioletLampDto GetGetUltravioletLampDeviceInfo(string sn = "", string model = "")
    {
        var url = $"/ocean-api/api/bio/env/uv/list/page?model={model}&pageNum=1&pageSize=1";
        if (sn.IsNotNullOrEmpty())
        {
            url = $"/ocean-api/api/bio/env/uv/list/page?sn={sn}&pageNum=1&pageSize=1";
        }
        GetToken();
        var result = ClientGet<object,RowResult<List<UltravioletLampDto>>>(url, null, true);
        if (result.Code ==  500)
        {
            Log.Information($"调用第三方接口[{address}{url}]成功，但是业务错误：{result.Rows}");
            throw new BizException($"调用第三方接口[{address}{url}]成功，但是业务错误：{result.Rows}");
        }

        if (result.Rows.Count > 0)
        {
            return result.Rows[0];
        }
        else
        {
            return null;
        }
    }

    
    /// <summary>
    /// 获取水压实时监数据
    /// </summary>
    /// <param name="sn"></param>
    /// <param name="model"></param>
    /// <returns></returns>
    /// <exception cref="BizException"></exception>
    public WaterPressureDto GetGetWaterPressureDeviceInfo(string sn = "", string model = "")
    {
        var url = $"/ocean-api/api/bio/env/wp/list/page?model={model}&pageNum=1&pageSize=1";
        if (sn.IsNotNullOrEmpty())
        {
            url = $"/ocean-api/api/bio/env/wp/list/page?sn={sn}&pageNum=1&pageSize=1";
        }
        GetToken();
        var result = ClientGet<object,RowResult<List<WaterPressureDto>>>(url, null, true);
        if (result.Code ==  500)
        {
            Log.Information($"调用第三方接口[{address}{url}]成功，但是业务错误：{result.Rows}");
            throw new BizException($"调用第三方接口[{address}{url}]成功，但是业务错误：{result.Rows}");
        }

        if (result.Rows.Count > 0)
        {
            return result.Rows[0];
        }
        else
        {
            return null;
        }
    }

    

    /// <summary>
    /// 获取房间信息
    /// </summary>
    /// <returns></returns>
    /// <exception cref="BizException"></exception>
    public List<Room> GetRooms()
    {
        var url = $"/ocean-api/api/bio/room/list";
        GetToken();
        var result = ClientGet<object,RowResult<List<Room>>>(url, null, true);
        if (result.Code ==  500)
        {
            Log.Information($"调用第三方接口[{address}{url}]成功，但是业务错误：{result.Rows}");
            throw new BizException($"调用第三方接口[{address}{url}]成功，但是业务错误：{result.Rows}");
        }
        if (result.Rows.Count > 0)
        {
            return result.Rows;
        }
        return new List<Room>();
    }
    
    /// <summary>
    /// 医疗设备类型与传感器类型字典查询
    /// </summary>
    /// <returns></returns>
    public List<DeviceTypeAndSensorTypeDto> GetDeviceTypeAndSensorTypes()
    {
        var result = new List<DeviceTypeAndSensorTypeDto>();
        var dictionary = new Dictionary<int, string>();
        dictionary.Add(800, "生物安全柜");
        dictionary.Add(801, "冰箱");
        dictionary.Add(802, "洗眼器");
        dictionary.Add(803, "紫外灯");
        dictionary.Add(804, "高压灭菌锅");
        
        var dictionary2 = new Dictionary<int, List<SensorsType>>();
        dictionary2.Add(800, new()
            {
                new SensorsType()
                {
                    SensorType = 701,
                    Name = "智能插座"
                },
            }
        );
        dictionary2.Add(801, new()
            {
                new SensorsType()
                {
                    SensorType = 701,
                    Name = "智能插座"
                },
                new SensorsType()
                {
                    SensorType = 405,
                    Name = "宽温传感器"
                },
            }
        );
        
        dictionary2.Add(802, new()
            {
                new SensorsType()
                {
                    SensorType = 702,
                    Name = "单开开关"
                },
                new SensorsType()
                {
                    SensorType = 407,
                    Name = "宽温传感器"
                },
            }
        );
        
        dictionary2.Add(803, new()
            {
                new SensorsType()
                {
                    SensorType = 702,
                    Name = "单开开关"
                },
                new SensorsType()
                {
                    SensorType = 406,
                    Name = "宽温传感器"
                },
            }
        );
        dictionary2.Add(804, new()
            {
                new SensorsType()
                {
                    SensorType = 701,
                    Name = "智能插座"
                }
            }
        );
        
        
        foreach (var item in dictionary)
        {
            result.Add(new DeviceTypeAndSensorTypeDto()
            {
                Key = item.Key,
                Value = item.Value,
                SensorTypes = dictionary2[item.Key]
            });
        }        
        return result;
    }
    
    /// <summary>
    /// 传感器类型与监测类型字典查询
    /// </summary>
    /// <returns></returns>
    public List<SensorTypeAndMonitorTypeDto> GetSensorTypeAndMonitorTypes()
    {
        var result = new List<SensorTypeAndMonitorTypeDto>();
        var dictionary = new Dictionary<int, string>();
        dictionary.Add(701, "智能插座");
        dictionary.Add(702, "单开开关");
        dictionary.Add(301, "环境一体机");
        dictionary.Add(405, "宽温传感器");
        dictionary.Add(406, "紫外线传感器");
        dictionary.Add(407, "水压传感器");
        
        
        var dictionary2 = new Dictionary<int, List<MonitorType>>();
        dictionary2.Add(701, new()
            {
                new MonitorType()
                {
                    BioAlarmType = 100,
                    Name = "开关使用状态"
                },
                new MonitorType()
                {
                    BioAlarmType = 101,
                    Name = "电压"
                },
                new MonitorType()
                {
                    BioAlarmType = 102,
                    Name = "电流"
                },
                new MonitorType()
                {
                    BioAlarmType = 103,
                    Name = "功率"
                },
                new MonitorType()
                {
                    BioAlarmType = 104,
                    Name = "累计用电量"
                },
            }
        );
        dictionary2.Add(702, new()
            {
                new MonitorType()
                {
                    BioAlarmType = 100,
                    Name = "开关使用状态"
                }
            }
        );
        dictionary2.Add(304, new()
            {
                new MonitorType()
                {
                    BioAlarmType = 200,
                    Name = "温度"
                },
                new MonitorType()
                {
                    BioAlarmType = 201,
                    Name = "湿度"
                },
                new MonitorType()
                {
                    BioAlarmType = 202,
                    Name = "噪声"
                },
                new MonitorType()
                {
                    BioAlarmType = 203,
                    Name = "气压"
                }
            }
        );
        dictionary2.Add(405, new()
            {
                new MonitorType()
                {
                    BioAlarmType = 205,
                    Name = "紫外线强度"
                }
            }
        );
        dictionary2.Add(407, new()
            {
                new MonitorType()
                {
                    BioAlarmType = 206,
                    Name = "水压"
                }
            }
        );
        foreach (var item in dictionary)
        {
            result.Add(new SensorTypeAndMonitorTypeDto()
            {
                Key = item.Key,
                Value = item.Value,
                MonitorTypes = dictionary2[item.Key]
            });

        }
        return result;
    }
    
    
    
    
}