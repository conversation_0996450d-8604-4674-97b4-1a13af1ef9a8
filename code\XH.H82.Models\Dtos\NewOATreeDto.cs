﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.Dtos
{
    /// <summary>
    /// 实验室列表树
    /// </summary>
    public class NewOATreeDto
    {
        /// <summary>
        /// 院区ID
        /// </summary>
        public string AREA_ID { get; set; }
        /// <summary>
        /// 院区名称
        /// </summary>
        public string AREA_NAME { get; set; }
        /// <summary>
        /// 申购总数
        /// </summary>
        public int TOTAL_SUBSCRIBE_AMOUNT { get; set; }
        /// <summary>
        /// 设备总数
        /// </summary>
        public int TOTAL_EQUIPMENT_AMOUNT { get; set; }
        /// <summary>
        /// 报废停用总数
        /// </summary>
        public int TOTAL_SCRAP_AMOUNT { get; set; }
        /// <summary>
        /// 本人待处理
        /// </summary>
        public int SELF_PEND { get; set; }
        /// <summary>
        /// 本实验室待处理
        /// </summary>
        public int SELF_PGROUP_PEND { get; set; }
        /// <summary>
        /// 检测仪器总数
        /// </summary>
        public int TOTAL_INSTRUMENT_AMOUNT { get;set; }
        /// <summary>
        /// 二级树
        /// </summary>
        public List<NewMgroupTreeDto> SecondStageTree { get; set; }
    }
    /// <summary>
    /// 管理专业组树
    /// </summary>
    public class NewMgroupTreeDto
    {
        /// <summary>
        /// 管理专业组ID
        /// </summary>
        public string MGROUP_ID { get; set; }
        /// <summary>
        /// 管理专业组名称
        /// </summary>
        public string MGROUP_NAME { get; set; }
        /// <summary>
        /// 申购总数
        /// </summary>
        public int MGROUP_SUBSCRIBE_AMOUNT { get; set; }
        /// <summary>
        /// 设备总数
        /// </summary>
        public int MGROUP_EQUIPMENT_AMOUNT { get; set; }
        /// <summary>
        /// 报废停用总数
        /// </summary>
        public int MGROUP_SCRAP_AMOUNT { get; set; }
        /// <summary>
        /// 检测仪器总数
        /// </summary>
        public int MGROUP_INSTRUMENT_AMOUNT { get; set; }
        /// <summary>
        /// 三级树
        /// </summary>
        public List<NewPgroupTreeDto> ThirdStageTree { get; set; }

    }
    /// <summary>
    /// 检验专业组树
    /// </summary>
    public class NewPgroupTreeDto
    {
        /// <summary>
        /// 检验专业组ID
        /// </summary>
        public string PGROUP_ID { get; set; }
        /// <summary>
        /// 检验专业组名称
        /// </summary>
        public string PGROUP_NAME { get; set; }
        /// <summary>
        /// 申购总数
        /// </summary>
        public int PGROUP_SUBSCRIBE_AMOUNT { get; set; }
        /// <summary>
        /// 设备总数
        /// </summary>
        public int PGROUP_EQUIPMENT_AMOUNT { get; set; }
        /// <summary>
        /// 报废停用总数
        /// </summary>
        public int PGROUP_SCRAP_AMOUNT { get; set; }
        /// <summary>
        /// 检测仪器总数
        /// </summary>
        public int PGROUP_INSTRUMENT_AMOUNT { get; set; }
        /// <summary>
        /// 四级树
        /// </summary>
        public List<NewEquipmentTreeDto> FourthStageTree { get; set; }
    }
    /// <summary>
    /// 设备树
    /// </summary>
    public class NewEquipmentTreeDto
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        public string EQUIPMENT_ID { get; set; }
        /// <summary>
        /// 设备代号
        /// </summary>
        public string EQUIPMENT_CODE { get; set; }
        /// <summary>
        /// 设备分类
        /// </summary>
        public string EQUIPMENT_CLASS { get;set; }
        /// <summary>
        /// 设备状态
        /// </summary>
        public string EQUIPMENT_STATE { get; set; }
        /// <summary>
        /// 是否隐藏
        /// </summary>
        public string IS_HIDE { get; set; }
        public string SMBL_FLAG { get; set; }
        /// <summary>
        /// 是否仪器
        /// </summary>
        public bool IsInstrument { get; set; } = false;
        /// <summary>
        /// 五级树
        /// </summary>
        public List<NewEquipmentTreeDto> FifthStageTree { get; set; }
    }
}
