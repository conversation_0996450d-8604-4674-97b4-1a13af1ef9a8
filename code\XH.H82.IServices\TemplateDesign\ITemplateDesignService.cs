using H.Utility;
using XH.H82.Models.BusinessModuleClient.H04;

namespace XH.H82.IServices.TemplateDesign;

public interface ITemplateDesignService
{
    public ResultDto InsertPersonInfoSetting();

    /// <summary>
    /// 初始化「基本信息」公共库字段
    /// </summary>
    ResultDto InitBaseInfoFieldDict();


    /// <summary>
    /// 创建设备分类的复杂表单
    /// </summary>
    /// <param name="classId">设备分类id</param>
    /// <param name="layoutId">样式id</param>
    bool CreateComplexForms(string classId , string layoutId = "H8205618");
    
    /// <summary>
    /// 查询设备类型的复杂表单
    /// </summary>
    /// <param name="setUpId">设备类型id</param>
    /// <param name="merge">是否合并字典json</param>
    /// <param name="qunitId">单元 预留</param>
    /// <returns></returns>
    ComplexFormDto? GetComplexForms(string setUpId, string merge, string?qunitId);

    /// <summary>
    /// 查询
    /// </summary>
    /// <param name="filedClass">复杂表单的分类</param>
    /// <returns></returns>
    List<object> GetFieldDicts(string filedClass);



    /// <summary>
    /// 新增或者保存复杂表单基础字段
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    bool SaveFieldDic(FieldDictDto input);



}