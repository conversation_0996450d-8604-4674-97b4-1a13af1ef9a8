﻿using System.Linq.Expressions;
using XH.H82.Models.Entities;
using XH.H82.Models.EquipmengtClassNew;
using XH.H82.Models.EquipmentCodeCustom;

namespace XH.H82.IServices.EquipmentCodeCustom;

public interface ICustomCodeService
{

    /// <summary>
    /// 获取当前所有的设备自定义编号
    /// </summary>
    /// <returns></returns>
    List<EquipmentCodeCustomDict> GetEquipmentCodeCustomDict();

    /// <summary>
    /// 查询当前设备的code自定义字典
    /// </summary>
    /// <param name="equipment"></param>
    /// <returns></returns>
    List<EquipmentCodeCustomDict> GetEquipmentCodeCustomDict(EMS_EQUIPMENT_INFO equipment);

    /// <summary>
    /// 替换设备自定义编码
    /// </summary>
    /// <param name="EqpNoId">模板字典id</param>
    /// <param name="equipment">设备</param>
    string ExchangeEquipmentUCode(DisplayContent dictContent, EMS_EQUIPMENT_INFO equipment);

    /// <summary>
    /// 添加新的设备自定义编码字典
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    EquipmentCodeCustomDict AddEquipmentCodeCustom(AddEquipmentCodeCustomDictDto input);

    /// <summary>
    /// 更新设备自定义编码字典
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    EquipmentCodeCustomDict UpdateEquipmentCodeCustomDict(UpdateEquipmentCodeCustomDictDto input);
    
    /// <summary>
    /// 删除设备自定义编码字典
    /// </summary>
    /// <param name="EqpNoId"></param>
    void DeleteEquipmentCodeCustomDict(string EqpNoId);

    /// <summary>
    /// 启用或者禁用设备自定义编码字典
    /// </summary>
    void EnableOrDisableEquipmentCodeCustomDict(string EqpNoId);

    
    List<EquipmentCodeCustomDict> GetEquipmentCodeCustomDictByCondition(Expression<Func<EMS_EQPNO_FORMAT_DICT , bool>> expression);
   
    /// <summary>
    /// 选取一个模板设备的设备信息
    /// </summary>
    /// <param name="eqpNoClass"></param>
    /// <returns></returns>
    EMS_EQUIPMENT_INFO GetEquipmentInfo(string eqpNoClass);
    
    /// <summary>
    /// 选择设备自定义名称优先级最高的模板
    /// </summary>
    /// <param name="equipment">设备</param>
    /// <returns></returns>
    EquipmentCodeCustomDict GetEquipmentCodeCustomDictByEquipment(EMS_EQUIPMENT_INFO equipment);
}