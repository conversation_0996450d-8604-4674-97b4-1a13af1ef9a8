using System.ComponentModel.DataAnnotations;

namespace XH.H82.Models.Dtos.EquipmentAdditionalRecord
{
    /// <summary>
    /// 保存设备扩展记录请求DTO
    /// </summary>
    public class SaveAdditionalRecordDto
    {
        /// <summary>
        /// 记录ID（更新时需要）
        /// </summary>
        public string? EqpRecordId { get; set; }

        /// <summary>
        /// 设备ID
        /// </summary>
        [Required(ErrorMessage = "设备ID不能为空")]
        public string EquipmentId { get; set; }

        /// <summary>
        /// 档案记录字典ID
        /// </summary>
        [Required(ErrorMessage = "档案记录ID不能为空")]
        public string EqpArchivesId { get; set; }

        /// <summary>
        /// 附加信息JSON
        /// </summary>
        public string? EquitmentJson { get; set; }

        /// <summary>
        /// 附件信息
        /// </summary>
        public string? EqpRecordAffix { get; set; }

        /// <summary>
        /// 排序号
        /// </summary>
        public string? EqpRecordSort { get; set; }

        /// <summary>
        /// 状态 0禁用 1在用 2删除
        /// </summary>
        public string? EqpRecordState { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
    }

    /// <summary>
    /// 查询设备档案记录请求DTO
    /// </summary>
    public class GetArchiveDetailDto
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        [Required(ErrorMessage = "设备ID不能为空")]
        public string EquipmentId { get; set; }

        /// <summary>
        /// 档案记录ID（可选，不传则返回所有）
        /// </summary>
        public string? EqpArchivesId { get; set; }

        /// <summary>
        /// 是否包含业务数据
        /// </summary>
        public bool IncludeBusinessData { get; set; } = true;

        /// <summary>
        /// 是否包含扩展字段数据
        /// </summary>
        public bool IncludeAdditionalData { get; set; } = true;
    }

    /// <summary>
    /// 扩展字段数据模板DTO
    /// </summary>
    public class AdditionalFieldTemplateDto
    {
        /// <summary>
        /// 字段名称
        /// </summary>
        public string FieldName { get; set; }

        /// <summary>
        /// 字段标签
        /// </summary>
        public string FieldLabel { get; set; }

        /// <summary>
        /// 字段类型（text, number, date, select, textarea等）
        /// </summary>
        public string FieldType { get; set; }

        /// <summary>
        /// 字段值
        /// </summary>
        public object? FieldValue { get; set; }

        /// <summary>
        /// 是否必填
        /// </summary>
        public bool Required { get; set; }

        /// <summary>
        /// 选项列表（当FieldType为select时使用）
        /// </summary>
        public List<SelectOptionDto>? Options { get; set; }

        /// <summary>
        /// 字段验证规则
        /// </summary>
        public string? ValidationRule { get; set; }

        /// <summary>
        /// 字段描述
        /// </summary>
        public string? Description { get; set; }
    }

    /// <summary>
    /// 选择项DTO
    /// </summary>
    public class SelectOptionDto
    {
        /// <summary>
        /// 选项值
        /// </summary>
        public string Value { get; set; }

        /// <summary>
        /// 选项标签
        /// </summary>
        public string Label { get; set; }

        /// <summary>
        /// 是否选中
        /// </summary>
        public bool Selected { get; set; }
    }
}
