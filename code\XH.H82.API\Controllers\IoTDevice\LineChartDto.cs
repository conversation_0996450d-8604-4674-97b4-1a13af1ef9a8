namespace XH.H82.API.Controllers.IoTDevice;

public class LineChartDto
{
    /// <summary>
    /// 值
    /// </summary>
    public string Value  { get; set; }
    /// <summary>
    /// 对应状态
    /// </summary>
    public string Status { get; set; }


    /// <summary>
    /// 根据不同设备类型以及监测项 计算开机关机待机消毒状态
    /// </summary>
    /// <param name="value">监测记录中的item_value</param>
    /// <param name="monitoringProject">不同类型用于判断使用状态的监测项item_id</param>
    /// <param name="smblClass">生安设备类型</param>
    /// <returns></returns>
    public static string  GetStatus(double value ,  string monitoringProject , string  smblClass)
    {
        var result = ""; 
        if (monitoringProject == "SMBL_5")
        {
            if (smblClass == "1")
            {
                result =  value switch
                {
                    double.NaN => "离线",
                    < 1 => "关机",
                    < 22 => "待机",
                    < 101 => "消毒",
                    < 800 => "使用中",
                    < 1000 => "过载",
                    _ => "过载"
                };
            }

            if (smblClass == "2")
            {
                result =  value switch
                {
                    double.NaN => "离线",
                    < 1 => "关机",
                    < 50 => "待机",
                    // < 101 => "消毒",
                    < 3500 => "使用中",
                    // < 1000 => "过载",
                    _ => "过载"
                };
            }
            
        }
        if (monitoringProject == "SMBL_205")
        {
            result = value switch
            {
                double.NaN => "离线",
               0=> "关机",
               _=>"使用中",
            };
        }
        if (monitoringProject == "SMBL_206")
        {
            if (smblClass == "4")
            {
                result = value switch
                {
                    double.NaN => "离线",
                    0 => "关机",
                    <=0.15 => "使用中",
                    > 0.15 =>"关机",
                    
                };
            }

            if (smblClass == "9")
            {
                result = value switch
                {
                    double.NaN => "离线",
                    0 => "关机",
                    <=0.15 => "使用中",
                    > 0.15 =>"关机",
                };
            }
            
        }
        if (monitoringProject == "SMBL_200")
        {
            if (smblClass == "7")
            {
                result =  value switch
                {
                    Double.NaN => "离线",
                    _ => "使用中"
                };
            }
        }
        if (monitoringProject == "SMBL_201")
        {
            if (smblClass == "7")
            {
                result =  value switch
                {
                    Double.NaN => "离线",
                    _ => "使用中"
                };
            }
        }
        if (monitoringProject == "SMBL_202")
        {
            if (smblClass == "7")
            {
                result =  value switch
                {
                    Double.NaN => "离线",
                    _ => "使用中"
                };
            }
        }
        if (monitoringProject == "SMBL_203")
        {
            if (smblClass == "7")
            {
                result =  value switch
                {
                    Double.NaN => "离线",
                    _ => "使用中"
                };
            }
        }

        return result;
    }
}