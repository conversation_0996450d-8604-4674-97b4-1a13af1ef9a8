﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using H.Utility.SqlSugarInfra;
using SqlSugar;

namespace XH.H82.Models.Entities
{
    [DBOwner("XH_SYS")]
    public class SYS_USER
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string USER_NO { get; set; }
        public string LOGID { get; set; }
        public string? USERNAME { get; set; }
        public string? PASSWORD { get; set; }

    }
}
