﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using H.Utility.SqlSugarInfra;
using SqlSugar;

namespace XH.H82.Models.Entities
{
    [DBOwner("XH_OA")]
    public class EMS_WORK_PLAN
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string WORK_PLAN_ID { get; set; }
        public string HOSPITAL_ID { get; set; }
        public string EQUIPMENT_ID { get; set; }
        public string WORK_PLAN_STATE { get; set; }
        public string OPER_PERSON { get; set; }
        public DateTime? OPER_TIME { get; set; }
        public string MAINTAIN_INTERVALS { get; set; }
        public string MAINTAIN_WARN_INTERVALS { get; set; }


        /// <summary>
        /// 季保养周期
        /// </summary>
        public string QUARTERLY_MAINTAIN { get; set; }
        /// <summary>
        /// 季保养提醒周期
        /// </summary>
        public string QUARTERLY_MAINTAIN_WARN { get; set; }
        /// <summary>
        /// 年保养周期
        /// </summary>
        public string YEARLY_MAINTAIN { get; set; }
        /// <summary>
        /// 年保养提醒周期
        /// </summary>
        public string YEARLY_MAINTAIN_WARN { get; set; }

        /// <summary>
        /// 提交人ID
        /// </summary>
        public string SUBMIT_USER_ID { get; set; }
        /// <summary>
        /// 提交时间
        /// </summary>
        public DateTime? SUBMIT_TIME { get; set; }

        /// <summary>
        /// 计划状态 0已驳回；1未提交；2已提交|未审核|重新提交；3已审核
        /// </summary>
        public string CURRENT_STATE { get; set; } = "1";
        public string MONTHLY_MAINTAIN { get; set; }
        public string MONTHLY_MAINTAIN_WARN { get; set; }
        public string COMPARISON_INTERVALS { get; set; }
        public string COMPARISON_WARN_INTERVALS { get; set; }
        public string VERIFICATION_INTERVALS { get; set; }
        public string VERIFICATION_WARN_INTERVALS { get; set; }
        public string CORRECT_INTERVALS { get; set; }
        public string CORRECT_WARN_INTERVALS { get; set; }
        public string FIRST_RPERSON { get; set; }
        public DateTime? FIRST_RTIME { get; set; }
        public string LAST_MPERSON { get; set; }
        public DateTime? LAST_MTIME { get; set; }
        public string REMARK { get; set; }



    }
}
