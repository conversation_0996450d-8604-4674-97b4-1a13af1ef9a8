using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using Microsoft.AspNetCore.Http;
using NetTaste;
using SqlSugar;
using XH.H82.Base.Setup;
using XH.H82.IServices;
using XH.H82.IServices.Certificate;
using XH.H82.Models.BusinessModuleClient;
using XH.H82.Models.Dtos;
using XH.H82.Models.Dtos.Certificate;
using XH.H82.Models.Entities;
using XH.H82.Models.Entities.Certificate;
using XH.H82.Models.SugarDbContext;
using ClaimsDto = H.Utility.ClaimsDto;

namespace XH.H82.Services.Certificate;

public class CertificateService : ICertificateService
{
    private H92Client _h92Client;
    private readonly IBaseDataServices _baseDataServices;
    private readonly IBaseService _baseService;
    private readonly ISqlSugarUow<SugarDbContext_Master> _dbContext;
    private readonly IHttpContextAccessor _httpContext;
    private readonly ClaimsDto _user;
    private const string InitCertificateClassId = "0X000H82CRE000";
    private const string FixedCertificateClassId = "0X000H82CRE001";
    private const string CertificateClass = "资质证书";
    private List<OA_BASE_DATA> _CertificateTypes = new ();

    public CertificateService(ISqlSugarUow<SugarDbContext_Master> dbContext, IHttpContextAccessor httpContext, IBaseService baseService, IBaseDataServices baseDataServices)
    {
        _dbContext = dbContext;
        _httpContext = httpContext;
        _baseService = baseService;
        _baseDataServices = baseDataServices;
        _h92Client = new();
        _user = _httpContext.HttpContext.User.ToClaimsDto();
        _dbContext.SetCreateTimeAndCreatePersonData(_user);
    }

    /// <summary>
    /// 添加设备证书信息
    /// </summary>
    /// <param name="equipmentId"></param>
    /// <param name="cretificateType"></param>
    /// <param name="expiryDate"></param>
    /// <param name="cerDate"></param>
    /// <param name="cerWanrDate"></param>
    /// <returns></returns>
    public EMS_CERTIFICATE_INFO AddEquipmentCertificate(string equipmentId , string cretificateType ,DateTime? expiryDate,DateTime cerDate ,DateTime? cerWanrDate )
    {
        var equipmentCertificate = new EMS_CERTIFICATE_INFO()
        {
            CERTIFICATE_ID = IDGenHelper.CreateGuid(),
            HOSPITAL_ID = _user.HOSPITAL_ID,
            EQUIPMENT_ID = equipmentId,
            CRETIFICATE_TYPE = cretificateType,
            CER_WANR_DATE = cerWanrDate,
            CER_DATE = cerDate,
            EXPIRY_DATE = expiryDate,
            CERTIFICATE_STATE = "1",
        };
        return  _dbContext.Db.Insertable(equipmentCertificate).ExecuteReturnEntity();
    }
    /// <summary>
    /// 证书信息上传附件
    /// </summary>
    /// <param name="cretificateId"></param>
    /// <param name="file"></param>
    /// <returns></returns>
    public EMS_CERTIFICATE_INFO AddEquipmentCertificateAttachments(string cretificateId,UploadFileDto file)
    {
        const string doc_folder = "EMS/datafile";
        const string if_cover = "true";
        var dateTime = DateTime.Now;
        var guid = Guid.NewGuid().ToString("N");
        
        var equipmentCertificate = _dbContext.Db.Queryable<EMS_CERTIFICATE_INFO>()
            .Where(x => x.CERTIFICATE_ID == cretificateId)
            .First();
        if (equipmentCertificate is null)
        {
            throw new BizException("不存在该证书信息");
        }
        var docs = _dbContext.Db.Queryable<EMS_DOC_INFO>()
            .Where(x => x.EMS_INFO_ID == equipmentCertificate.CERTIFICATE_ID)
            .ToList();
        equipmentCertificate.ATTACHMENTS.AddRange(docs);

        (string uploadFileName, string uploadFileType, byte[] uploadFileByte) uploadFileInfo = _baseService.GetUploadFileInfo(file, guid);
        
        var docInfo = new EMS_DOC_INFO
        {
            DOC_ID = IDGenHelper.CreateGuid(),
            DOC_INFO_ID = equipmentCertificate.EQUIPMENT_ID, //为了和旧数据兼容，把设备id存至该字段
            EQUIPMENT_ID = equipmentCertificate.EQUIPMENT_ID,
            HOSPITAL_ID = _user.HOSPITAL_ID,
            DOC_CLASS = CertificateClass,
            DOC_NAME = file.DOC_NAME,//原文件名
            DOC_TYPE = uploadFileInfo.uploadFileType,
            UPLOAD_PERSON = _user.HIS_NAME,
            UPLOAD_TIME = dateTime,
            DOC_STATE = "1",
            DOC_PATH = "",
            DOC_SUFFIX = file.DOC_SUFFIX,
            PDF_PREVIEW_PATH = "",
            EMS_INFO_ID = equipmentCertificate.CERTIFICATE_ID
        };
        docInfo.DOC_PATH = _baseDataServices.UploadToS28(doc_folder, if_cover, uploadFileInfo.uploadFileName, uploadFileInfo.uploadFileByte);
        if (uploadFileInfo.uploadFileType != "IMG")
        {
            var bytes = PdfPreview.PDFPreview_Byte(uploadFileInfo.uploadFileByte);
            var doc_preview_name = $"{file.DOC_NAME}_{guid}_预览.jpg";
            docInfo.PDF_PREVIEW_PATH = _baseDataServices.UploadToS28(doc_folder, if_cover, doc_preview_name, bytes);
        }
        else
        {
            docInfo.PDF_PREVIEW_PATH = docInfo.DOC_PATH;
        }
        var doc =  _dbContext.Db.Insertable(docInfo).ExecuteReturnEntity();
        equipmentCertificate.ATTACHMENTS.Add(doc);
        return equipmentCertificate;
    }

    /// <summary>
    /// 删除证书信息附件
    /// </summary>
    /// <param name="id"></param>
    public void DeleteEquipmentCertificateAttachment(string id)
    {
        var doc = _dbContext.Db.Queryable<EMS_DOC_INFO>()
            .Where(x => x.DOC_CLASS == CertificateClass)
            .Where(x => x.DOC_ID == id)
            .First();
        if (doc is not null)
        {
            doc.DOC_STATE = "2";
            _dbContext.Db.Updateable(doc).IgnoreColumns(ignoreAllNullColumns:true).ExecuteCommand();
        }
    }
    /// <summary>
    /// 删除设备证书信息
    /// </summary>
    /// <param name="cretificateId"></param>
    public void DeleteEquipmentCertificate(string cretificateId)
    {
        var equipmentCertificate = _dbContext.Db.Queryable<EMS_CERTIFICATE_INFO>()
            .Where(x => x.CERTIFICATE_ID == cretificateId)
            .First();
        if (equipmentCertificate is not null)
        {
            equipmentCertificate.CERTIFICATE_STATE = "2";
            _dbContext.Db.Updateable(equipmentCertificate).IgnoreColumns(ignoreAllNullColumns:true).ExecuteCommand();
            _dbContext.Db.Updateable<EMS_DOC_INFO>().SetColumns(x => new()
                {
                    DOC_STATE = "2"
                })
                .Where(x=>x.DOC_CLASS== CertificateClass)
                .Where(x=>x.EMS_INFO_ID != null)
                .Where(x=>x.EMS_INFO_ID == cretificateId)
                ;
        }
    }
    /// <summary>
    /// 编辑设备证书信息
    /// </summary>
    /// <param name="cretificateId"></param>
    /// <param name="cretificateType"></param>
    /// <param name="expiryDate"></param>
    /// <param name="cerDate"></param>
    /// <param name="cerWanrDate"></param>
    /// <returns></returns>
    /// <exception cref="BizException"></exception>
    public EMS_CERTIFICATE_INFO EditEquipmentCertificate(string cretificateId,  string cretificateType ,DateTime? expiryDate,DateTime cerDate ,DateTime? cerWanrDate )
    {
        var equipmentCertificate = _dbContext.Db.Queryable<EMS_CERTIFICATE_INFO>()
            .Where(x => x.CERTIFICATE_ID == cretificateId)
            .First();

        if (equipmentCertificate is null)
        {
            throw new BizException("该证书不存在，请重新选择证书信息进行修改");
        }
        
        equipmentCertificate.CRETIFICATE_TYPE = cretificateType;
        equipmentCertificate.CER_DATE = cerDate;
        equipmentCertificate.EXPIRY_DATE = expiryDate;
        equipmentCertificate.CER_WANR_DATE = cerWanrDate;
        _dbContext.Db.Updateable(equipmentCertificate).ExecuteCommand();

        var docs = _dbContext.Db.Queryable<EMS_DOC_INFO>()
            .Where(x => x.DOC_CLASS == CertificateClass)
            .Where(x => x.DOC_INFO_ID == equipmentCertificate.EQUIPMENT_ID)
            .Where(x => x.EMS_INFO_ID == equipmentCertificate.CERTIFICATE_ID)
            .ToList();
        equipmentCertificate.ATTACHMENTS.AddRange(docs);
        return equipmentCertificate;
    }
    /// <summary>
    /// 获取设备自身证书信息
    /// </summary>
    /// <param name="equipmentId"></param>
    /// <returns></returns>
    public List<EMS_CERTIFICATE_INFO> GetEquipmentCertificates( string equipmentId) 
    {
        var result = new List<EMS_CERTIFICATE_INFO>();
        var certificateTypes = GetCertificateTypes();
        var equipmentCertificates = _dbContext.Db.Queryable<EMS_CERTIFICATE_INFO>()
            .Where(x=>x.CERTIFICATE_STATE =="1")
            .Where(x=>x.EQUIPMENT_ID == equipmentId)
            .ToList();

        var equipmentCertificateAttachments = _dbContext.Db.Queryable<EMS_DOC_INFO>()
            .Where(x=>x.DOC_INFO_ID == equipmentId)
            .Where(x=>x.DOC_STATE =="1")
            .Where(x => x.DOC_CLASS == CertificateClass)
            .ToList();
        
        foreach (var equipmentCertificate in equipmentCertificates)
        {
            var certificateAttachments =
                equipmentCertificateAttachments
                    .Where(x=>x.EMS_INFO_ID != null)
                    .Where(x => x.EMS_INFO_ID == equipmentCertificate.CERTIFICATE_ID)
                    .ToList();
            equipmentCertificate.ATTACHMENTS.AddRange(certificateAttachments);
        }
        
        result.AddRange(equipmentCertificates);
        //旧数据初始化为医疗器械注册证的附件
        if (equipmentCertificateAttachments.Any(x=>x.EMS_INFO_ID == null))
        {
            var docs = equipmentCertificateAttachments.Where(x => x.EMS_INFO_ID == null).ToList();
            var equipmentCertificate = new EMS_CERTIFICATE_INFO()
            {
                CERTIFICATE_ID = IDGenHelper.CreateGuid(),
                HOSPITAL_ID = _user.HOSPITAL_ID,
                EQUIPMENT_ID = equipmentId,
                CRETIFICATE_TYPE = InitCertificateClassId,
                CER_WANR_DATE = null,
                CER_DATE = DateTime.Now,
                EXPIRY_DATE = null,
                CERTIFICATE_STATE = "1",
                FIRST_RTIME = DateTime.Now,
                FIRST_RPERSON = "system",
                LAST_MTIME = DateTime.Now,
                LAST_MPERSON = "system",
                REMARK = "",
            };
            foreach (var equipmentCertificateAttachment in docs)
            {
                equipmentCertificateAttachment.EMS_INFO_ID = equipmentCertificate.CERTIFICATE_ID;
                equipmentCertificate.ATTACHMENTS.Add(equipmentCertificateAttachment);
            }
            result.Add(equipmentCertificate);
            _dbContext.Db.Insertable(equipmentCertificate).ExecuteCommand();
            _dbContext.Db.Updateable(docs).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommand();
        }
        return result;
    }
    /// <summary>
    /// 如果不存在 设备证书类型 数据  则初始化
    /// </summary>
    /// <returns></returns>
    private List<OA_BASE_DATA> GetCertificateTypes()
    {
        var baseDatas = _dbContext.Db.Queryable<OA_BASE_DATA>()
            .Where(x => x.CLASS_ID == "设备证书类型")
            .ToList();
        if (baseDatas.All(x => x.DATA_ID != InitCertificateClassId))
        {
            var init0 = new OA_BASE_DATA()
            {
                DATA_ID = InitCertificateClassId,
                HOSPITAL_ID = _user.HOSPITAL_ID,
                MODULE_ID = "H82",
                FATHER_ID = "Certificate",
                CLASS_ID = "设备证书类型",
                DATA_SORT = "000",
                DATA_NAME = "未归类证书",
                DATA_SNAME="未归类证书",
                DATA_ENAME = "",
                STANDART_ID = "H82CRE000",
                CUSTOM_CODE = "",
                STATE_FLAG = "1"                
            };
            
            var init = new OA_BASE_DATA()
            {
                DATA_ID = FixedCertificateClassId,
                HOSPITAL_ID = _user.HOSPITAL_ID,
                MODULE_ID = "H82",
                FATHER_ID = "Certificate",
                CLASS_ID = "设备证书类型",
                DATA_SORT = "001",
                DATA_NAME = "医疗器械注册证",
                DATA_SNAME="医疗器械注册证",
                DATA_ENAME = "",
                STANDART_ID = "H82CRE001",
                CUSTOM_CODE = "",
                STATE_FLAG = "1"                
            };
            _dbContext.Db.Insertable(init0).ExecuteCommand();
            _dbContext.Db.Insertable(init).ExecuteCommand();
            return new List<OA_BASE_DATA>() { init ,init0};
        }
        else
        {
            return baseDatas;
        }
    }
    /// <summary>
    /// 查询设备证书类型列表
    /// </summary>
    /// <param name="CertificateTypeName"></param>
    /// <param name="state"></param>
    /// <returns></returns>
    public List<OA_BASE_DATA> GetEquipmentCertificateTypes(string? CertificateTypeName, string? state)
    {
        var result = new List<OA_BASE_DATA>();
        var baseDatas = _dbContext.Db.Queryable<OA_BASE_DATA>()
            .WhereIF(state.IsNotNullOrEmpty(),x=>x.STATE_FLAG == state)
            .WhereIF(CertificateTypeName.IsNotNullOrEmpty(),x=>x.DATA_NAME!.Contains(CertificateTypeName!))
            .Where(x => x.CLASS_ID == "设备证书类型")
            .Where(x => x.FATHER_ID == "Certificate")
            .Where(x=>x.STATE_FLAG != "2")
            .ToList();
        result.AddRange(baseDatas);
        return result;
    }
    /// <summary>
    /// 新增证书类型
    /// </summary>
    /// <param name="certificateTypeName"></param>
    /// <param name="remark"></param>
    /// <returns></returns>
    public  OA_BASE_DATA AddCertificateType(string certificateTypeName , string? remark)
    {
        var certificate = _dbContext.Db.Queryable<OA_BASE_DATA>()
            .Where(x => x.CLASS_ID == "设备证书类型")
            .Where(x => x.FATHER_ID == "Certificate")
            .Where(x=>x.STATE_FLAG != "2")
            .Where(x => x.DATA_NAME == certificateTypeName)
            .First();
        if (certificate is not null) 
        {
            if (certificate.STATE_FLAG == "2")
            {
                _dbContext.Db.Updateable<OA_BASE_DATA>().SetColumns(x=>new()
                    {
                        STATE_FLAG = "1"
                    } )
                    .Where(x => x.CLASS_ID == "设备证书类型")
                    .Where(x => x.FATHER_ID == "Certificate")
                    .Where(x => x.DATA_NAME == certificateTypeName)
                    .ExecuteCommand();
                return certificate;
            }
            else
            {
                throw new BizException("已存在相同名称的证书类型！");
            }
        }
        else
        {
            var baseDate = new OA_BASE_DATA()
            {
                DATA_ID = IDGenHelper.CreateGuid(),
                HOSPITAL_ID = _user.HOSPITAL_ID,
                MODULE_ID = "H82",
                FATHER_ID = "Certificate",
                CLASS_ID = "设备证书类型",
                DATA_SORT = "000",
                DATA_NAME = certificateTypeName,
                DATA_SNAME= certificateTypeName,
                DATA_ENAME = "",
                STANDART_ID = "H82CRE001",
                CUSTOM_CODE = "",
                STATE_FLAG = "1" ,
                REMARK = remark
            };
           var result = _dbContext.Db.Insertable(baseDate).ExecuteReturnEntity();
           return result;
        }
    }
    /// <summary>
    /// 修改证书信息
    /// </summary>
    /// <param name="id"></param>
    /// <param name="certificateTypeName"></param>
    /// <param name="remark"></param>
    /// <returns></returns>
    public OA_BASE_DATA EditCertificateType(string id, string certificateTypeName , string? remark)
    {
        var certificate = _dbContext.Db.Queryable<OA_BASE_DATA>()
            .Where(x => x.CLASS_ID == "设备证书类型")
            .Where(x => x.FATHER_ID == "Certificate")
            .Where(x=>x.STATE_FLAG != "2")
            .Where(x => x.DATA_NAME == certificateTypeName)
            .First();
        if (certificate is  not null)
        {
            throw new BizException("已存在相同名字的证书类型");
        }
        
        var baseDate = _dbContext.Db.Queryable<OA_BASE_DATA>()
            .Where(x => x.CLASS_ID == "设备证书类型")
            .Where(x => x.FATHER_ID == "Certificate")
            .Where(x => x.DATA_ID == id)
            .First();
        
        if (baseDate is not null)
        {
            baseDate.DATA_NAME = certificateTypeName;
            baseDate.REMARK = remark;
            _dbContext.Db.Updateable(baseDate).ExecuteCommand();
            return baseDate;
        }
        else
        {
            return  AddCertificateType(certificateTypeName, remark);
        }
    }
    /// <summary>
    /// 停用/启用证书类型
    /// </summary>
    /// <param name="id"></param>
    public void DisableOrEnableCertificateType(string id)
    {
        if (id == InitCertificateClassId || id == FixedCertificateClassId)
        {
            throw new BizException("该数据位基础数据，不可禁用");
        }

        var data =  _dbContext.Db.Queryable<OA_BASE_DATA>()
            .Where(x => x.CLASS_ID == "设备证书类型")
            .Where(x => x.FATHER_ID == "Certificate")
            .Where(x => x.DATA_ID == id)
            .First();
        if (data is null)
        {
            return;
        }
        else
        {
            data.STATE_FLAG = data.STATE_FLAG == "0" ? "1" : "0";
            _dbContext.Db.Updateable(data).ExecuteCommand();
        }
        
    }

    /// <summary>
    /// 删除证书类型
    /// </summary>
    /// <param name="id"></param>
    public void DeleteCertificateType(string id)
    {
        if (id == InitCertificateClassId || id == FixedCertificateClassId)
        {
            throw new BizException("该数据位基础数据，不可删除");
        }
        
        _dbContext.Db.Updateable<OA_BASE_DATA>()
            .SetColumns(x=>new()
            {
                STATE_FLAG = "2"
            } )
            .Where(x => x.CLASS_ID == "设备证书类型")
            .Where(x => x.FATHER_ID == "Certificate")
            .Where(x => x.DATA_ID == id)
            .ExecuteCommand();
    }
    /// <summary>
    /// 查询名称
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public string GetCertificateTypeName(string id)
    {
        if (!_CertificateTypes.Any())
        {
            var datas = GetCertificateTypes();
            _CertificateTypes.AddRange(datas);
        }
        return _CertificateTypes.FirstOrDefault(x => x.DATA_ID == id).DATA_NAME ?? "";
    }

    /// <summary>
    /// 获取设备关联供应商证书信息
    /// </summary>
    /// <param name="equipmentId"></param>
    /// <returns></returns>
    public List<CompanyCertificatDto> GetEquipmentCompanyCertificates(string equipmentId)
    {

        var result = new List<CompanyCertificatDto>();
        var equipment = _dbContext.Db.Queryable<EMS_EQUIPMENT_INFO>()
            .Where(x => x.EQUIPMENT_ID == equipmentId)
            .First();
        if (equipment is null)
        {
            return result;
        }

        if (equipment.DEALER_ID.IsNotNullOrEmpty())
        {
           var dealerCertificats =  _h92Client.GetCompanyCerlist(equipment.DEALER_ID,_dbContext);
           foreach (var dealerCertificat in dealerCertificats)
           {
               var companyCertificatDto = new CompanyCertificatDto(equipment.DEALER, dealerCertificat);
               result.Add(companyCertificatDto);
           }
        }

        if (equipment.MANUFACTURER_ID.IsNotNullOrEmpty())
        {
            var nufacturerCertificats =  _h92Client.GetCompanyCerlist(equipment.MANUFACTURER_ID,_dbContext);
            foreach (var nufacturerCertificat in nufacturerCertificats)
            {
                var companyCertificatDto = new CompanyCertificatDto(equipment.MANUFACTURER, nufacturerCertificat);
                result.Add(companyCertificatDto);
            }
        }

        return result;
    }

    /// <summary>
    /// 证书初始化
    /// </summary>
    public void InItCertificates()
    {
        GetCertificateTypes();
        var hasDocs = _dbContext.Db.Queryable<EMS_DOC_INFO>()
            .Where(x => x.DOC_STATE == "1")
            .Where(x => x.DOC_CLASS == CertificateClass)
            .ToList()
            .GroupBy(x => x.DOC_INFO_ID);
        
        foreach (var docInfo in hasDocs)
        {
            var docs = docInfo.ToList();
            var equipmentCertificate = new EMS_CERTIFICATE_INFO()
            {
                CERTIFICATE_ID = IDGenHelper.CreateGuid(),
                HOSPITAL_ID = _user.HOSPITAL_ID,
                EQUIPMENT_ID = docInfo.Key,
                CRETIFICATE_TYPE = InitCertificateClassId,
                CER_WANR_DATE = null,
                CER_DATE = DateTime.Now,
                EXPIRY_DATE = null,
                CERTIFICATE_STATE = "1",
                FIRST_RTIME = DateTime.Now,
                FIRST_RPERSON = "system",
                LAST_MTIME = DateTime.Now,
                LAST_MPERSON = "system",
                REMARK = "",
            };
            foreach (var equipmentCertificateAttachment in docs)
            {
                equipmentCertificateAttachment.EMS_INFO_ID = equipmentCertificate.CERTIFICATE_ID;
                equipmentCertificate.ATTACHMENTS.Add(equipmentCertificateAttachment);
            }
            _dbContext.Db.GetSimpleClient<EMS_CERTIFICATE_INFO>().InsertOrUpdate(equipmentCertificate);
            _dbContext.Db.Updateable(docs).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommand();
        }
        Console.WriteLine('='*10);
    }
    
    /// <summary>
    /// 证书数据还原
    /// </summary>
    public void ReductionInItCertificates()
    {
        var hasDocs = _dbContext.Db.Queryable<EMS_DOC_INFO>()
            .Where(x => x.DOC_STATE == "1")
            .Where(x => x.DOC_CLASS == CertificateClass)
            .Where(x=>x.EMS_INFO_ID != null)
            .ToList()
            .GroupBy(x => x.DOC_INFO_ID);

        var ids = _dbContext.Db.GetSimpleClient<EMS_CERTIFICATE_INFO>().GetList().Select(x=>x.CERTIFICATE_ID).ToArray();
        _dbContext.Db.GetSimpleClient<EMS_CERTIFICATE_INFO>().DeleteByIds(ids);
        
        foreach (var docInfo in hasDocs)
        {
            var docs = docInfo.ToList();
            foreach (var equipmentCertificateAttachment in docs)
            {
                equipmentCertificateAttachment.EMS_INFO_ID = null;
            }
            _dbContext.Db.Updateable(docs).ExecuteCommand();
        }
        Console.WriteLine('='*10);
    }
}