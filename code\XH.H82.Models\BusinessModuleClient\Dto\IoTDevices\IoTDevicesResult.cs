﻿using Newtonsoft.Json;

namespace XH.H82.Models.BusinessModuleClient.Dto.IoTDevices;

public class IoTDevicesResult
{
    [JsonProperty("code")]
    public long Code { get; set; }

    [JsonProperty("data")]
    public IoTDevicesDto Data { get; set; }

    [JsonProperty("msg")]
    public string Msg { get; set; }
}



public class IoTDevicesResult<T>
{
    
    [JsonProperty("code")]
    public long Code { get; set; }

    [JsonProperty("data")]
    public T Data { get; set; }

    [JsonProperty("msg")]
    public string Msg { get; set; }
}

public class RowResult<T>
{
    public long Code { get; set; }

    [JsonProperty("Rows")]
    public T Rows { get; set; }

    [JsonProperty("Total")]
    public int Total { get; set; }
}