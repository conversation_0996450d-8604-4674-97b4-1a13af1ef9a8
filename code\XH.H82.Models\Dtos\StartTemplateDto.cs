﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using AutoMapper.Configuration.Annotations;
using XH.H82.Models.Entities;


namespace XH.H82.Models.Dtos
{

    [AutoMap(typeof(TEST_START_TEMPLATE),ReverseMap = true)]
    public class StartTemplateDto
    {
        /// <summary>
        /// ID
        /// </summary>
        [Required(ErrorMessage ="ID必填")]
        [SourceMember("ID")]
        public string Id { get; set; }
        /// <summary>
        /// 名称
        /// </summary>
        [SourceMember("NAME")]
        public string Name { get; set; }
 
    }
}
