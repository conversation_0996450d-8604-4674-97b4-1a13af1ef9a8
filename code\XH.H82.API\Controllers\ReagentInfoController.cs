﻿using System.ComponentModel.DataAnnotations;
using H.Utility;
using Microsoft.AspNetCore.Mvc;
using XH.H82.IServices;

namespace XH.H82.API.Controllers
{
    /// <summary>
    /// 试剂信息
    /// </summary>
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class ReagentInfoController : ControllerBase
    {
        private readonly IReagentInfoService _reagentInfoService;
        public ReagentInfoController(IReagentInfoService reagentInfoService)
        {
            _reagentInfoService = reagentInfoService;
        }
        /// <summary>
        /// 专属试剂-项目列表
        /// </summary>
        /// <param name="equipmentNo">设备ID</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetInstrumentItemList([Required] string equipmentId)
        {
            var claims = User.ToClaimsDto();
            var res = _reagentInfoService.GetInstrumentItemList(equipmentId);
            return Ok(res.ToResultDto());
        }
        /// <summary>
        /// 专属试剂-专属试剂列表
        /// </summary>
        /// <param name="itemId">项目ID</param>
        /// <param name="channelId">项目通道ID（不用传了）</param>
        /// <param name="equipmentId">设备ID</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetInstrumentItemReagentList([Required]string itemId,string channelId,string equipmentId)
        {
            var claims = User.ToClaimsDto();
            var res = _reagentInfoService.GetInstrumentItemReagentList(itemId, channelId, "1",equipmentId);
            return Ok(res);
        }
        /// <summary>
        /// 专属试剂-历史记录
        /// </summary>
        /// <param name="itemId">项目ID</param>
        /// <param name="channelId">项目通道ID（不用传了）</param>
        /// <param name="equipmentId">设备ID</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetInstrumentItemReagentHistoryeList([Required] string itemId, string channelId,string equipmentId)
        {
            var claims = User.ToClaimsDto();
            var res = _reagentInfoService.GetInstrumentItemReagentList(itemId, channelId, "0",equipmentId);
            return Ok(res);
        }
        /// <summary>
        ///  仪器公共试剂
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetInstrumentReagentCommonList(string equipmentId)
        {
            var claims = User.ToClaimsDto();
            
            var res = _reagentInfoService.GetInstrumentReagentCommonList(equipmentId);
            return Ok(res);
        }

        /// <summary>
        /// 校准品-校准品列表
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetCalibratorList([Required] string equipmentId)
        {
            var claims = User.ToClaimsDto();
            
            var res = _reagentInfoService.GetCalibratorList(equipmentId);
            return Ok(res);
        }

        /// <summary>
        /// 校准品-校准品项目列表
        /// </summary>
        /// <param name="itemId">项目ID(不用传)</param>
        /// <param name="equipmentId">设备ID</param>
        /// <param name="materialId">校准品ID</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetCalibratorItemList(string itemId, string equipmentId, string materialId)
        {
            var claims = User.ToClaimsDto();
            var res = _reagentInfoService.GetCalibratorItemList(itemId,equipmentId,materialId);
            return Ok(res.ToResultDto());
        }
    }
}
