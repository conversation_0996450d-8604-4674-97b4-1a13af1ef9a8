﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.Dtos.SysDocDtos
{
    public class InspectionProfessionalGroupNode
    {

        [JsonProperty("CLASS_CID")]
        public string ClassCid { get; set; }

        [<PERSON><PERSON><PERSON>roperty("CLASS_CNAME")]
        public string ClassCname { get; set; }

        [JsonProperty("TYPE_NAME")]
        public string TypeName { get; set; }

        [JsonProperty("DocPgroupTypeDto")]
        public List<DocumentClassNode> DocumentClasses { get; set; }
    }
}
