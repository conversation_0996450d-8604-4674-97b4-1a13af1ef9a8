﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.Dtos.SysDocDtos
{
    public class DocumentClassNode
    {
        [JsonProperty("DATA_ID")]
        public string DataId { get; set; }

        [JsonProperty("DATA_CNAME")]
        public string DataCname { get; set; }

        [JsonProperty("TYPE_NAME")]
        public string TypeName { get; set; }

        [JsonProperty("DocLabDto")]
        public List<FileSystemFileInfo> FileInfos { get; set; }
    }
}
