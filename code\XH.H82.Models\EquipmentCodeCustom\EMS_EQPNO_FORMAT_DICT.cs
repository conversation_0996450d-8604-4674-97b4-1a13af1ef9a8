﻿using H.Utility;
using H.Utility.SqlSugarInfra;
using Newtonsoft.Json;
using SqlSugar;
using XH.H82.Models.EquipmentCodeCustom;

namespace XH.H82.Models.EquipmengtClassNew;

/// <summary>
/// 设备自定义名称模板
/// </summary>
[DBOwner("XH_OA")]
[SugarTable("EMS_EQPNO_FORMAT_DICT", TableDescription = "设备自定义名称模板")]
public class EMS_EQPNO_FORMAT_DICT
{
    /// <summary>
    /// 自定义名称模板id
    /// </summary>
    [SugarColumn(IsPrimaryKey = true,ColumnName = "EQP_NO_ID")]
    public string EqpNoId{ get; set; }
    
    /// <summary>
    /// 医疗机构ID
    /// </summary>
    [SugarColumn(ColumnName = "HOSPITAL_ID")]
    public string HospitalId{ get; set; }
    
    /// <summary>
    /// 设备自定义名称模板名;模板名称
    /// </summary>
    [SugarColumn(ColumnName = "EQP_NO_NAME")]
    public string EqpNoName{ get; set; }
    
    /// <summary>
    /// 模板优先级;模板优先级、数值越大、优先级越高
    /// </summary>
    [SugarColumn(ColumnName = "EQP_NO_LEVEL")]
    public string EqpNoLevel{ get; set; }
    
    /// <summary>
    /// 应用设备类型;0 全部 设备类型
    /// </summary>
    [SugarColumn(ColumnName = "EQP_NO_CLASS")]
    public string EqpNoClass{ get; set; }
    
    /// <summary>
    /// 设备代号配置
    /// </summary>
    [SugarColumn(ColumnName = "EQP_DISPLAY_JSON")]
    public string? EqpDisplayJson{ get; set; }
    
    /// <summary>
    /// 应用范围
    /// </summary>
    [SugarColumn(ColumnName = "EQP_NO_APPLYS")]
    public string? EqpNoApplys{ get; set; }
    
    /// <summary>
    /// 数据状态;0 禁用   1在用   2删除
    /// </summary>
    [SugarColumn(ColumnName = "EQP_NO_STATE")]
    public string EqpNoState{ get; set; }
    
    /// <summary>
    /// 首次登记人
    /// </summary>
    [SugarColumn(ColumnName = "FIRST_RPERSON")]
    public string? FirstRperson{ get; set; }
    
    /// <summary>
    /// 首次登记时间
    /// </summary>
    [SugarColumn(ColumnName = "FIRST_RTIME")]
    public DateTime? FirstRtime{ get; set; }
    
    /// <summary>
    /// 最后修改人员
    /// </summary>
    [SugarColumn(ColumnName = "LAST_MPERSON")]
    public string? LastMperson{ get; set; }
    
    /// <summary>
    /// 最后修改时间
    /// </summary>
    [SugarColumn(ColumnName = "LAST_MTIME")]
    public DateTime? LastMtime{ get; set; }
    
    /// <summary>
    /// 备注0
    /// </summary>
    [SugarColumn(ColumnName = "REMARK")]
    public string? Remark{ get; set; }


    public EquipmentCodeCustomDict GetEquipmentCodeCustomDict()
    {
        return new EquipmentCodeCustomDict
        {
            EqpNoId = EqpNoId,
            HospitalId = HospitalId,
            EqpNoName = EqpNoName,
            EqpNoLevel = EqpNoLevel,
            EqpNoClass = EqpNoClass,
            EqpNoApplys = EqpNoApplys,
            EqpNoState = EqpNoState,
            DisplayContent = EqpDisplayJson.IsNullOrEmpty() ? new DisplayContent() : JsonConvert.DeserializeObject<DisplayContent>(EqpDisplayJson)
        };
    }
}