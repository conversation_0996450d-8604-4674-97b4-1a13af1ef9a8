﻿using System.ComponentModel.DataAnnotations;
using H.Utility.SqlSugarInfra;
using SqlSugar;

namespace XH.H82.Models.BusinessModuleClient
{
    /// <summary>
    /// 实验室管理基础数据表
    /// </summary>
    [SugarTable("OA_BASE_DATA")]
    [DBOwner("XH_OA")]
    public class OA_BASE_DATA
    {
        /// <summary>
        /// 基础数据ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        //[Column("DATA_ID")]
        [Required(ErrorMessage = "基础数据ID不允许为空")]
        [StringLength(50, ErrorMessage = "基础数据ID长度不能超出20字符")]
        //[Unicode(false)]
        public string DATA_ID { get; set; }

        /// <summary>
        /// 医疗机构ID
        /// </summary>
        //[Column("HOSPITAL_ID")]
        [Required(ErrorMessage = "医疗机构ID不允许为空")]
        [StringLength(50, ErrorMessage = "医疗机构ID长度不能超出20字符")]
        //[Unicode(false)]
        public string HOSPITAL_ID { get; set; }

        /// <summary>
        /// 模块ID
        /// </summary>
        //[Column("MODULE_ID")]
        [Required(ErrorMessage = "模块ID不允许为空")]
        [StringLength(50, ErrorMessage = "模块ID长度不能超出20字符")]
        //[Unicode(false)]
        public string MODULE_ID { get; set; }

        /// <summary>
        /// 父级ID
        /// </summary>
        //[Column("FATHER_ID")]
        [StringLength(50, ErrorMessage = "父级ID长度不能超出20字符")]
        //[Unicode(false)]
        public string? FATHER_ID { get; set; }

        /// <summary>
        /// 分类ID
        /// </summary>
        //[Column("CLASS_ID")]
        [StringLength(50, ErrorMessage = "分类ID长度不能超出50字符")]
        //[Unicode(false)]
        public string? CLASS_ID { get; set; }

        /// <summary>
        /// 排序号
        /// </summary>
        //[Column("DATA_SORT")]
        [StringLength(20, ErrorMessage = "排序号长度不能超出20字符")]
        //[Unicode(false)]
        public string? DATA_SORT { get; set; }

        /// <summary>
        /// 中文名
        /// </summary>
        //[Column("DATA_NAME")]
        [StringLength(100, ErrorMessage = "中文名长度不能超出100字符")]
        //[Unicode(false)]
        public string? DATA_NAME { get; set; }

        /// <summary>
        /// 简称
        /// </summary>
        //[Column("DATA_SNAME")]
        [StringLength(50, ErrorMessage = "简称长度不能超出50字符")]
        //[Unicode(false)]
        public string? DATA_SNAME { get; set; }

        /// <summary>
        /// 英文名
        /// </summary>
        //[Column("DATA_ENAME")]
        [StringLength(50, ErrorMessage = "英文名长度不能超出50字符")]
        //[Unicode(false)]
        public string? DATA_ENAME { get; set; }

        /// <summary>
        /// 标准代码
        /// </summary>
        //[Column("STANDART_ID")]
        [StringLength(50, ErrorMessage = "标准代码长度不能超出50字符")]
        //[Unicode(false)]
        public string? STANDART_ID { get; set; }

        /// <summary>
        /// 自定义码
        /// </summary>
        //[Column("CUSTOM_CODE")]
        [StringLength(20, ErrorMessage = "自定义码长度不能超出20字符")]
        //[Unicode(false)]
        public string? CUSTOM_CODE { get; set; }

        /// <summary>
        /// 拼音码
        /// </summary>
        //[Column("SPELL_CODE")]
        [StringLength(20, ErrorMessage = "拼音码长度不能超出20字符")]
        //[Unicode(false)]
        public string? SPELL_CODE { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        //[Column("STATE_FLAG")]
        [StringLength(20, ErrorMessage = "状态长度不能超出20字符")]
        //[Unicode(false)]
        public string? STATE_FLAG { get; set; }

        /// <summary>
        /// 首次登记人
        /// </summary>
        //[Column("FIRST_RPERSON")]
        [StringLength(50, ErrorMessage = "首次登记人长度不能超出50字符")]
        //[Unicode(false)]
        public string? FIRST_RPERSON { get; set; }

        /// <summary>
        /// 首次登记时间
        /// </summary>
        //[Column("FIRST_RTIME")]

        //[Unicode(false)]
        public DateTime? FIRST_RTIME { get; set; }

        /// <summary>
        /// 最后修改人员
        /// </summary>
        //[Column("LAST_MPERSON")]
        [StringLength(50, ErrorMessage = "最后修改人员长度不能超出50字符")]
        //[Unicode(false)]
        public string? LAST_MPERSON { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        //[Column("LAST_MTIME")]

        //[Unicode(false)]
        public DateTime? LAST_MTIME { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        //[Column("REMARK")]
        [StringLength(200, ErrorMessage = "备注长度不能超出200字符")]
        //[Unicode(false)]
        public string? REMARK { get; set; }


        /// <summary>
        /// 自定义扩展配置
        /// </summary>
        [StringLength(2000, ErrorMessage = "备注长度不能超出2000字符")]
        public string? ADDN_CONFIG_JSON { get; set; }
        public OA_BASE_DATA()
        {
        }

        public OA_BASE_DATA(string dATA_ID, string hOSPITAL_ID, string mODULE_ID, string fATHER_ID, string cLASS_ID, string dATA_SORT, string dATA_NAME, string dATA_SNAME, string dATA_ENAME, string sTANDART_ID, string cUSTOM_CODE, string sPELL_CODE, string sTATE_FLAG, string fIRST_RPERSON, DateTime? fIRST_RTIME, string lAST_MPERSON, DateTime? lAST_MTIME, string rEMARK)
        {
            DATA_ID = dATA_ID;
            HOSPITAL_ID = hOSPITAL_ID;
            MODULE_ID = mODULE_ID;
            FATHER_ID = fATHER_ID;
            CLASS_ID = cLASS_ID;
            DATA_SORT = dATA_SORT;
            DATA_NAME = dATA_NAME;
            DATA_SNAME = dATA_SNAME;
            DATA_ENAME = dATA_ENAME;
            STANDART_ID = sTANDART_ID;
            CUSTOM_CODE = cUSTOM_CODE;
            SPELL_CODE = sPELL_CODE;
            STATE_FLAG = sTATE_FLAG;
            FIRST_RPERSON = fIRST_RPERSON;
            FIRST_RTIME = fIRST_RTIME;
            LAST_MPERSON = lAST_MPERSON;
            LAST_MTIME = lAST_MTIME;
            REMARK = rEMARK;
        }
    }
}
