﻿using H.IRepository;
using H.Utility;
using H.Utility.Helper;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XH.H82.IServices;
using XH.H82.Models.Dtos;
using XH.H82.Models.Entities.Common;
using XH.H82.Models.Entities;
using Microsoft.EntityFrameworkCore;
using XH.H82.Models;
using Microsoft.Extensions.Logging;
using XH.H82.Models.SugarDbContext;
using H.BASE.SqlSugarInfra.Uow;
using NPOI.HSSF.Record.Chart;
using XH.LAB.UTILS.Models;
using Microsoft.AspNetCore.Http;
using XH.LAB.UTILS.Interface;

namespace XH.H82.Services
{
    public class SubscribeService : ISubscribeService
    {
        private IBaseService _baseService;
        private readonly ISqlSugarUow<SugarDbContext_Master> _sqlSugarUow;
        private readonly ILogger<OperationRecordService> _logger;
        public readonly IHttpContextAccessor _httpContext;
        private readonly IAuthorityService2 _authorityService;

        public SubscribeService(IBaseService baseService, ISqlSugarUow<SugarDbContext_Master> sqlSugarUow, IHttpContextAccessor httpContext, IAuthorityService2 authorityService)
        {
            _baseService = baseService;
            _sqlSugarUow = sqlSugarUow;
            _httpContext = httpContext;
            _authorityService = authorityService;
            //ExecutingChangeSqlHelper.ExecutingChangeSql(_sqlSugarUow);
        }
        //获取申购信息列表
        public List<EMS_SUBSCRIBE_INFO> GetSubscribeInfoList(DateTime startTime, DateTime endTime, string userNo, string hospitalId, string mgroupId, string state, string search,string labId, string pgroupId, string areaId)
        {
            var authorityContext = new AuthorityContext(_sqlSugarUow,_authorityService);
            authorityContext.SetUser(_httpContext.HttpContext.User.ToClaimsDto(),  labId, areaId);
            //authorityContext.CheckedUserAuth(labId, areaId, mgroupId, pgroupId);
            //专业组列表
            var groupList = authorityContext.GetAccessibleProfessionalGroups(labId, areaId);
            var q = _sqlSugarUow.Db.Queryable<EMS_SUBSCRIBE_INFO>()
                .Where(p => p.SUBSCRIBE_DATE <= endTime && p.SUBSCRIBE_DATE >= startTime && groupList.Select(i => i.PGROUP_ID).Contains(p.MGROUP_ID))
                .WhereIF(pgroupId.IsNotNullOrEmpty(), p => p.MGROUP_ID == pgroupId)
                .WhereIF(state.IsNotNullOrEmpty(), p => p.SUBSCRIBE_STATE == state)
                .WhereIF(search.IsNotNullOrEmpty(), p => p.SUBSCRIBE_NAME.ToLower().Contains(search.ToLower())
                || p.SUBSCRIBE_PERSON.ToLower().Contains(search.ToLower()));
            if (mgroupId.IsNotNullOrEmpty())
            {
                var pList = _sqlSugarUow.Db.Queryable<SYS6_INSPECTION_PGROUP>()
                    .Where(p => p.MGROUP_ID == mgroupId && p.PGROUP_STATE == "1" && p.HOSPITAL_ID==hospitalId)
                    .Select(i => i.PGROUP_ID).ToList();
                q = q.Where(p => pList.Contains(p.MGROUP_ID));
            }
            var res = q.ToList();
            res.ForEach(item =>
            {
                item.MGROUP_NAME = groupList.Where(p => p.PGROUP_ID == item.MGROUP_ID)
                .FirstOrDefault()?.PGROUP_NAME; 
            });
 
            //获取设备信息列表
            var equipmentList = _sqlSugarUow.Db.Queryable<EMS_EQUIPMENT_INFO>()
                .Includes(i => i.eMS_INSTALL_INFO)
                .Includes(i => i.eMS_PURCHASE_INFO)
                .Where(i=>i.HOSPITAL_ID==hospitalId)
                .ToList();
            for (int i = 0; i < res.Count; i++)
            {
                if (res[i].EQUIPMENT_ID.IsNullOrEmpty())
                {
                    continue;
                }
                //将申购的设备ID字段整理成数组
                var arrEquipmentId = res[i].EQUIPMENT_ID.TrimStart(',').TrimEnd(',').Split(',');
                equipmentList.ForEach(item =>
                {
                    if (arrEquipmentId.Contains(item.EQUIPMENT_ID))
                    {
                        res[i].EQUIPMENT_LIST += item.EQUIPMENT_CODE + ";";
                        if(res[i].SUBSCRIBE_STATE == "合同已签订" && res[i].SUBSCRIBE_STATE == "通过" || res[i].SUBSCRIBE_STATE == "仪器已安装")
                        {
                            if (item.eMS_INSTALL_INFO != null)
                            {
                                _sqlSugarUow.Db.Updateable<EMS_SUBSCRIBE_INFO>()
                                .SetColumns(p => new EMS_SUBSCRIBE_INFO
                                {
                                    SUBSCRIBE_STATE = "仪器已安装"
                                }).Where(p => p.SUBSCRIBE_ID == res[i].SUBSCRIBE_ID)
                                .ExecuteCommand();

                            }
                            else if (item.eMS_PURCHASE_INFO != null)
                            {
                                _sqlSugarUow.Db.Updateable<EMS_SUBSCRIBE_INFO>().SetColumns(p => new EMS_SUBSCRIBE_INFO
                                {
                                    SUBSCRIBE_STATE = "合同已签订"
                                }).Where(p => p.SUBSCRIBE_ID == res[i].SUBSCRIBE_ID).ExecuteCommand();

                            }
                        }
                    }
                });
            
            }
            return res.OrderByDescending(i => i.SUBSCRIBE_DATE).ToList();
        }
        //保存申购项目
        public EMS_SUBSCRIBE_INFO SaveSubscribeInfo(EMS_SUBSCRIBE_INFO record)
        {
            var old = _sqlSugarUow.Db.Queryable<EMS_SUBSCRIBE_INFO>()
                .Where(p => p.SUBSCRIBE_ID == record.SUBSCRIBE_ID)
                .First();
            if(old != null)
            {
                old.SUBSCRIBE_NAME = record.SUBSCRIBE_NAME;
                old.SUBSCRIBE_DATE = record.SUBSCRIBE_DATE;
                old.SUBSCRIBE_PERSON = record.SUBSCRIBE_PERSON;
                old.MGROUP_ID = record.MGROUP_ID;
                old.SUBSCRIBE_STATE = record.SUBSCRIBE_STATE;
                old.LAST_MPERSON = record.LAST_MPERSON;
                old.LAST_MTIME = record.LAST_MTIME;
                old.REMARK = record.REMARK;
                old.SMBL_FLAG = record.SMBL_FLAG;
                old.SMBL_LAB_ID = record.SMBL_LAB_ID;
                if(record.SUBSCRIBE_STATE != "待审")
                {
                    old.APPROVE_PERSON = record.APPROVE_PERSON; 
                    old.APPROVE_TIME = record.APPROVE_TIME;
                    old.APPROVE_OPINION = record.APPROVE_OPINION;
                }
                _sqlSugarUow.Db.Updateable(old).ExecuteCommand();
            }
            else
            {                   
                _sqlSugarUow.Db.Insertable(record).ExecuteCommand();
            }
            return record;
        }

        //删除申购项目
        public ResultDto DeleteSubscribeInfo(string subscribeId,string userName)
        {
            ResultDto result = new ResultDto();
            try
            {
                _sqlSugarUow.Db.Deleteable<EMS_SUBSCRIBE_INFO>().In(subscribeId).ExecuteCommand();
                var fileList = _sqlSugarUow.Db.Queryable<EMS_DOC_INFO>()
                    .Where(p => p.DOC_INFO_ID == subscribeId)
                    .ToList();
                fileList.ForEach(item =>
                {
                    _baseService.DeleteEnclosureInfo(item.DOC_ID, userName);
                });
                result.success = true;
            }
            catch(Exception ex)
            {
                result.success = false;
                result.msg = "删除失败";
                _logger.LogError("删除失败:\n" + ex.Message);
            }
            return result;
        }
        
        //申购流程
        public SubscribeProcessDto GetSubscribeProcess (string subscribeId)
        {
            //申购信息
            var subscribeInfo = _sqlSugarUow.Db.Queryable<EMS_SUBSCRIBE_INFO>()
                .Where(p => p.SUBSCRIBE_ID == subscribeId)
                .First();
            if(subscribeInfo == null)
            {
                throw new BizException($"申购id{subscribeId}不存在");
            }
            //申购流程dto
            var processDto = new SubscribeProcessDto();
            //合同签订信息
            var contactSigningList = new List<CONTACT_SIGNING>();
            //仪器安装信息
            var instrumentInstallList = new List<INSTRUMENT_INSTALL>();
            //申购人
            processDto.SUBSCRIBE_PERSON = subscribeInfo.SUBSCRIBE_PERSON;
            //申购时间
            processDto.SUBSCRIBE_DATE = Convert.ToDateTime(subscribeInfo.SUBSCRIBE_DATE).ToString("yyyy-MM-dd");
            //若申购状态不为待审
            if(subscribeInfo.SUBSCRIBE_STATE != "待审")
            {
                processDto.APPROVAL_PERSON = subscribeInfo.APPROVE_PERSON;
                processDto.APPROVAL_DATE = Convert.ToDateTime(subscribeInfo.APPROVE_TIME).ToString("yyyy-MM-dd");
                processDto.IF_APPROVAL = "审批通过";
                if (subscribeInfo.SUBSCRIBE_STATE == "驳回")
                {
                    processDto.IF_APPROVAL = "审批未通过";
                }
                if (subscribeInfo.SUBSCRIBE_STATE == "仪器已安装" || subscribeInfo.SUBSCRIBE_STATE == "合同已签订")
                {
                    if (!subscribeInfo.EQUIPMENT_ID.IsNullOrEmpty())
                    {
                        var arrEquipmentId = subscribeInfo.EQUIPMENT_ID.TrimStart(',').TrimEnd(',').Split(',');
                        if (arrEquipmentId.Length > 0)
                        {
                            var contactList = _sqlSugarUow.Db.Queryable<EMS_EQUIPMENT_INFO>()
                                .LeftJoin<EMS_PURCHASE_INFO>((a, b) => a.EQUIPMENT_ID == b.EQUIPMENT_ID)
                                .Where((a, b) => arrEquipmentId.Contains(a.EQUIPMENT_ID))
                                .Select((a, b) => new
                                {
                                    b.CONTRACT_NAME,
                                    b.CONTRACT_NO,
                                    b.CONTRACT_DATE
                                }).ToList();
                            contactList.ForEach(item =>
                            {
                                contactSigningList.Add(new CONTACT_SIGNING()
                                {
                                    CONTACT_NAME = item.CONTRACT_NAME,
                                    CONTACT_NO = item.CONTRACT_NO,
                                    CONTACT_DATE = item.CONTRACT_DATE != null ? Convert.ToDateTime(item.CONTRACT_DATE).ToString("yyyy-MM-dd") : null
                                });
                            });
                        }
                        if (subscribeInfo.SUBSCRIBE_STATE == "仪器已安装")
                        {
                            var instrumentList = _sqlSugarUow.Db.Queryable<EMS_EQUIPMENT_INFO>()
                                .InnerJoin<EMS_INSTALL_INFO>((a, b) => a.EQUIPMENT_ID == b.EQUIPMENT_ID)
                                .Where((a, b) => arrEquipmentId.Contains(a.EQUIPMENT_ID))
                                .Select((a, b) => new
                                {
                                    a.EQUIPMENT_NAME,
                                    b.INSTALL_DATE
                                }).ToList();
                            instrumentList.ForEach(item =>
                            {
                                instrumentInstallList.Add(new INSTRUMENT_INSTALL()
                                {
                                    INSTRUMENT_NAME = item.EQUIPMENT_NAME,
                                    INSTALL_DATE = Convert.ToDateTime(item.INSTALL_DATE).ToString("yyyy-MM-dd")
                                });
                            });
                        }
                    }
                }       
        }
            processDto.CONTACT_SIGNING = contactSigningList;
            processDto.INSTRUMENT_INSTALL = instrumentInstallList;
            return processDto;
    }
}
}
