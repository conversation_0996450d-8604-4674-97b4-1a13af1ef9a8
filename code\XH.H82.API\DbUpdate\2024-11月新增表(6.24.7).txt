/*2024/11/20 XH_OA.EMS_CERTIFICATE_INFO 新增设备证书信息表*/
CREATE TABLE XH_OA.EMS_CERTIFICATE_INFO(
    CERTIFICATE_ID VARCHAR2(50) NOT NULL,
    EQUIPMENT_ID VARCHAR2(50) NOT NULL,
    HOSPITAL_ID VARCHAR2(50) NOT NULL,
    CRETIFICATE_TYPE VARCHAR2(50) NOT NULL,
    CER_WANR_DATE DATE,
    CER_DATE DATE NOT NULL,
    EXPIRY_DATE DATE,
    CERTIFICATE_STATE VARCHAR2(20) DEFAULT  1 NOT NULL,
    FIRST_RTIME DATE,
    FIRST_RPERSON VARCHAR2(50),
    LAST_MTIME DATE,
    LAST_MPERSON VARCHAR2(50),
    REMARK VARCHAR2(200)
);
alter table XH_OA.EMS_CERTIFICATE_INFO add constraint PK_OA_EMS_CERTIFICATE_INFO primary key (CERTIFICATE_ID);

COMMENT ON COLUMN XH_OA.EMS_CERTIFICATE_INFO.CERTIFICATE_ID IS 'PK';
COMMENT ON COLUMN XH_OA.EMS_CERTIFICATE_INFO.EQUIPMENT_ID IS '设备ID';
COMMENT ON COLUMN XH_OA.EMS_CERTIFICATE_INFO.HOSPITAL_ID IS '医疗机构ID';
COMMENT ON COLUMN XH_OA.EMS_CERTIFICATE_INFO.CRETIFICATE_TYPE IS '证书类型;OA_BASE_DATA 对应的DATA_ID';
COMMENT ON COLUMN XH_OA.EMS_CERTIFICATE_INFO.CER_WANR_DATE IS '提醒时间';
COMMENT ON COLUMN XH_OA.EMS_CERTIFICATE_INFO.CER_DATE IS '发证时间';
COMMENT ON COLUMN XH_OA.EMS_CERTIFICATE_INFO.EXPIRY_DATE IS '过期时间';
COMMENT ON COLUMN XH_OA.EMS_CERTIFICATE_INFO.CERTIFICATE_STATE IS '设备证书状态;0禁用、1在用、2删除、默认1';
COMMENT ON COLUMN XH_OA.EMS_CERTIFICATE_INFO.FIRST_RTIME IS '首次创建时间';
COMMENT ON COLUMN XH_OA.EMS_CERTIFICATE_INFO.FIRST_RPERSON IS '首次操作人';
COMMENT ON COLUMN XH_OA.EMS_CERTIFICATE_INFO.LAST_MTIME IS '最后一次操作时间';
COMMENT ON COLUMN XH_OA.EMS_CERTIFICATE_INFO.LAST_MPERSON IS '最后一次操作人';
COMMENT ON COLUMN XH_OA.EMS_CERTIFICATE_INFO.REMARK IS '备注';
grant select, insert, update, delete on XH_OA.EMS_CERTIFICATE_INFO to XH_COM;