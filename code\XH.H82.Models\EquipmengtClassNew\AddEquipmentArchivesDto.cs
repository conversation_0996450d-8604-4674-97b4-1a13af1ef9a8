﻿namespace XH.H82.IServices.EquipmentClassNew;

/// <summary>
/// 档案记录编辑模型
/// </summary>
public class AddEquipmentArchivesDto
{
    /// <summary>
    /// 档案记录父级ID;细分类的父级id 父级id可为空
    /// </summary>
    public string? EqpArchivesPid { get; set; } = "0";

    /// <summary>
    /// 档案记录名称
    /// </summary>
    public string EqpArchivesName { get; set; }
    
    /// <summary>
    /// 是否上传附件
    /// </summary>
    public bool IsIpload { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }
}


