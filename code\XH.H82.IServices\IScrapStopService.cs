﻿using H.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XH.H82.Models.Dtos;
using XH.H82.Models.Entities;

namespace XH.H82.IServices
{
    public interface IScrapStopService
    {
        List<ScrapStopListDto> GetScrapStopList(DateTime startTime, DateTime endTime, string hospitalId,
             string mgroupId, string equipmentClass, string state, string applyClass, string equipmentKey, string personKey,
             string file_preview_address, string userNo,
             string person_select, string mgroup_select, string selfMgroupId, string labId, string areaId, string pgroupId);

        List<EMS_EQUIPMENT_INFO> GetEquipmentApplyList(string userNo, string hospitalId, string mgroupId, string equipmentClass, string keyword, string labId, string areaId);
        //ResultDto ScrapStopApply(List<ScrapStopListDto> record, string hospitalId, string userName, string userNo,string state);
        //ResultDto SaveApply(List<ScrapStopListDto> record, string hospitalId, string userName, string state);
        //ResultDto BatchSubmit(List<ScrapStopListDto> record, string userName, string hospitalId);
        ResultDto DeleteAwaitSubmit(List<ScrapStopListDto> record);
        //ResultDto ApplyRevoke(List<ScrapStopListDto> record, string userName);
        //ResultDto ApplyAdopt(List<ScrapStopListDto> record, string hospitalId, string userName, string file_upload_address);
        //ResultDto ApplyReject(List<ScrapStopListDto> record, string hospitalId, string userName);
        List<EMS_SCRAP_LOG> ScrapStopProcess(string scrapId);
    }
}