﻿using System.ComponentModel.DataAnnotations;
using AutoMapper;
using AutoMapper.Configuration.Annotations;
using XH.H82.Models.BusinessModuleClient;

namespace XH.H82.Models.Dtos.Transaction
{
    [AutoMap(typeof(OA_BASE_DATA))]
    public class ContentDictDto
    {
        public string DATA_ID { get; set; }
        [SourceMember("DATA_NAME")]
        public string CONTENT { get; set; }

        public string REMARK { get; set; }
        public string FIRST_RTIME { get; set; }

    }


    /// <summary>
    /// 
    /// </summary>
    /// <param name="ClassId">记录单类型  "保养记录、使用记录、校准记录、比对记录、维修记录、性能验证记录"</param>
    /// <param name="Content">字典内容</param>
    /// <param name="Remark">备注</param>
    public record AddContentDictInput([Required] string ClassId, [Required] string Content, string? Remark);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="Content">字典内容</param>
    /// <param name="Remark">备注</param>
    public record UpdateContentDictInput([Required] string Content, string? Remark);
}
