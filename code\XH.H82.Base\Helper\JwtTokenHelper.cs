﻿using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using H.Utility;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;

namespace XH.H82.Base.Helper
{
    public static class JwtTokenHelper
    {
        private static string _key = "E291A2AFF63C4D07B663FC7014ACD62B@xhlis";
        /// <summary>
        /// 创建Token 注意,token统一由S01颁发,请勿自行颁发
        /// </summary>
        /// <param name="claims"></param>
        /// <param name="expiresHours"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public static string CreateToke(Claim[] claims, string key,int expiresHours = 12)
        {
        
            var keyToken = new SymmetricSecurityKey(
                Encoding.UTF8.GetBytes(key));
            var credentials = new SigningCredentials(keyToken, SecurityAlgorithms.HmacSha256);
            var token = new JwtSecurityToken(
                //简化,不验证颁发者和订阅者
                //issuer: "xh",
                //audience: "xh",
                claims: claims,
                //令牌有效期为48小时 时间太短需要添加令牌有效期滑动更新并无缝更新,不然影响用户体验(需要频繁登录)
                //此项目只需保证基础安全性,遂设置为12小时
                expires: DateTime.Now.AddHours(expiresHours),
                signingCredentials: credentials
            );
            //令牌
            string res = new JwtSecurityTokenHandler().WriteToken(token);
            return res;
        }


        /// <summary>
        /// 创建Token 注意,token统一由S01颁发,请勿自行颁发
        /// </summary>
        /// <param name="claims"></param>
        /// <param name="expiresHours"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public static string CreateToken(Claim[] claims, int expiresHours = 12)
        {

            var keyToken = new SymmetricSecurityKey(
                Encoding.UTF8.GetBytes(_key));
            var credentials = new SigningCredentials(keyToken, SecurityAlgorithms.HmacSha256);
            var token = new JwtSecurityToken(
                //简化,不验证颁发者和订阅者
                //issuer: "xh",
                //audience: "xh",
                claims: claims,
                //令牌有效期为48小时 时间太短需要添加令牌有效期滑动更新并无缝更新,不然影响用户体验(需要频繁登录)
                //此项目只需保证基础安全性,遂设置为12小时
                expires: DateTime.Now.AddHours(expiresHours),
                signingCredentials: credentials
            );
            //令牌
            string res = new JwtSecurityTokenHandler().WriteToken(token);
            return res;
        }
    }
}
