﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.Entities.FileTemplate
{
    /// <summary>
    /// 导出的excel预览信息体
    /// </summary>
    public class WorkPlansInfo
    {

        public int NO { get; set; } = 1;
        /// <summary>
        /// 工作计划主键
        /// </summary>
        public string WORK_PLAN_ID { get; set; } = "";
        /// <summary>
        /// 设备主键
        /// </summary>
        public string EQUIPMENT_ID { get; set; } = "";
        /// <summary>
        /// 计划状态中文
        /// </summary>
        public string CURRENT_STATE_NAME { get; set; } = "";

        /// <summary>
        /// 计划状态 0已驳回；1未提交；2已提交|未审核|重新提交；3已审核
        /// </summary>
        public string CURRENT_STATE { get; set; } = "";
        /// <summary>
        /// 设备名称
        /// </summary>
        public string EQUIPMENT_NAME { get; set; } = "";
        /// <summary>
        /// 设备型号
        /// </summary>
        public string EQUIPMENT_MODEL { get; set; } = "";
        /// <summary>
        /// 设备代号
        /// </summary>
        public string EQUIPMENT_CODE { get; set; } = "";
        /// <summary>
        /// 周保养周期/天
        /// </summary>
        public string MAINTAIN_INTERVALS { get; set; } = "";
        /// <summary>
        /// 周保养提醒周期/天
        /// </summary>
        public string MAINTAIN_WARN_INTERVALS { get; set; } = "";

        /// <summary>
        /// 最近一次周保养时间
        /// </summary>
        public DateTime? LAST_MAINTAIN_DATE { get; set; }

        /// <summary>
        /// 下一次周保养时间
        /// </summary>
        public DateTime? NEXT_MAINTAIN_DATE { get; set; }

        /// <summary>
        /// 月保养周期/天
        /// </summary>
        public string MONTHLY_MAINTAIN { get; set; } = "";

        /// <summary>
        /// 月保养提醒周期/天
        /// </summary>
        public string MONTHLY_MAINTAIN_WARN { get; set; } = "";

        /// <summary>
        /// 最近一次月保养时间
        /// </summary>
        public DateTime? LAST_MONTHLY_MAINTAIN_DATE { get; set; }
        /// <summary>
        /// 下一次月保养时间
        /// </summary>
        public DateTime? NEXT_MONTHLY_MAINTAIN_DATE { get; set; }

        /// <summary>
        /// 年保养周期/天
        /// </summary>
        public string YEARLY_MAINTAIN { get; set; } = "";

        /// <summary>
        /// 年保养提醒周期/天
        /// </summary>
        public string YEARLY_MAINTAIN_WARN { get; set; } = "";

        /// <summary>
        /// 最近一次年保养时间
        /// </summary>
        public DateTime? LAST_YEARLY_MAINTAIN_DATE { get; set; }

        /// <summary>
        /// 下一次年保养时间
        /// </summary>
        public DateTime? NEXT_YEARLY_MAINTAIN_DATE { get; set; }
        /// <summary>
        /// 校准周期/天
        /// </summary>
        public string CORRECT_INTERVALS { get; set; } = "";
        /// <summary>
        /// 校准提醒周期/天
        /// </summary>
        public string CORRECT_WARN_INTERVALS { get; set; } = "";

        /// <summary>
        /// 最近一次校准时间
        /// </summary>
        public DateTime? LAST_CORRECT_INTERVALS_DATE { get; set; }
        /// <summary>
        /// 下一次校准时间
        /// </summary>
        public DateTime? NEXT_CORRECT_INTERVALS_DATE { get; set; }

        /// <summary>
        /// 校准单位
        /// </summary>
        public string CORRECT_UNIT { get; set; } = "";

        /// <summary>
        /// 负责人
        /// </summary>
        public string EQ_IN_PERSON { get; set; } = "";

        /// <summary>
        /// 比对周期/天
        /// </summary>
        public string COMPARISON_INTERVALS { get; set; } = "";
        /// <summary>
        /// 比对提醒周期/天
        /// </summary>
        public string COMPARISON_WARN_INTERVALS { get; set; } = "";

        /// <summary>
        /// 最近一次比对时间
        /// </summary>
        public DateTime? LAST_COMPARISON_INTERVALS_DATE { get; set; }
        /// <summary>
        /// 下一次比对时间
        /// </summary>
        public DateTime? NEXT_COMPARISON_INTERVALS_DATE { get; set; }

        /// <summary>
        /// 操作人
        /// </summary>
        public string OPER_PERSON { get; set; } = "";

        /// <summary>
        /// 操作时间
        /// </summary>
        public DateTime? OPER_TIME { get; set; }

        /// <summary>
        /// 审核人
        /// </summary>
        public string AUDITOR_USER_NAME { get; set; }
        /// <summary>
        /// 审核时间
        /// </summary>
        public DateTime? AUDITOR_TIME { get; set; }
        /// <summary>
        /// 审核内容
        /// </summary>
        public string AUDITOR_CONTEXT { get; set; } = "";

        /// <summary>
        /// 注册证号
        /// </summary>
        public string REGISTRATION_NUM { get; set; } = "";
        /// <summary>
        /// 注册人名称
        /// </summary>
        public string REGISTRATION_NAME { get; set; } = "";



    }
}
