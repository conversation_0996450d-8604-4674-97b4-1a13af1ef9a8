﻿using Microsoft.Extensions.Configuration;
//using XHLISService;
using ReportService;
using Spire.Xls;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography.X509Certificates;
using System.ServiceModel;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.Linq;
using XH.H82.IServices;
using XH.H82.Models.Dtos;
using XH.H82.Models.ViewDtos;
using H.Utility;


namespace XH.H82.Services
{
    public class XHLISPrintService:IXHLISPrintService
    {
        private XingHePrintServiceSoapClient _client;
        //接口服务地址
        private readonly string _serviceAddress;
        public XHLISPrintService(IConfiguration configuration)
        {
            try
            {
                var binding = new BasicHttpBinding();
                binding.MaxReceivedMessageSize = *********;
                _serviceAddress = configuration["ReportService"];
                _client = new XingHePrintServiceSoapClient(binding, new EndpointAddress(_serviceAddress));
            }
            catch (Exception e)
            {
                throw new BizException("单据打印服务出错:" + e.Message);
            }
        }
        public ResultDto CardPrint(string dataXml)
        {
            string strXml = _client.ExportPdfSheet6Async(dataXml).Result;
            var ele = XElement.Parse(strXml);
            string ResultCode = ele.Element("ResultCode").Value;
            if(ResultCode == "1")
            {
                if(ele.Element("PRINT_MODE").Value == "PRINT" || ele.Element("PRINT_MODE").Value == "SHELL")
                {
                    return new ResultDto()
                    {
                        success = true,msg = "成功"
                    };
                }
                else
                {
                    return new ResultDto()
                    {
                        success = true,msg = ele.Element("DATA_SOURCE").Value
                    };
                }
            }
            else
            {
                return new ResultDto()
                {
                    success = false
                };
            }
        }
    }
}
