﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.Entities
{
    [DBOwner("XH_OA")]
    public class DMIS_SYS_DOC
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string DOC_ID { get; set; }
        public string HOSPITAL_ID { get; set; }
        public string CLASS_ID { get; set; }
        public string DOC_FATHER_ID { get; set; }
        public string LAB_ID { get; set; }
        public string PGROUP_ID { get; set; }
        public string DOC_TYPE { get; set; }
        public string DOC_TITLE { get; set; }
        public string DOC_SORT { get; set; }
        public string DOC_NAME { get; set; }
        public string DOC_CODE { get; set; }
        public string DOC_DESC { get; set; }
        public string DOC_VERSION { get; set; }
        public DateTime? EXECUTION_DATE { get; set; }
        public string DOC_DRAFTERS { get; set; }
        public DateTime? DRAFTERS_DATE { get; set; }
        public string CHECK_PERSON { get; set; }
        public DateTime? CHECK_TIME { get; set; }
        public string CHECK_COMPUTER { get; set; }
        public string APPROVAL_PERSON { get; set; }
        public DateTime? APPROVAL_TIME { get; set; }
        public string APPROVAL_COMPUTER { get; set; }
        public string ISSUED_NO { get; set; }
        public string ISSUED_PERSON { get; set; }
        public DateTime? ISSUED_TIME { get; set; }
        public string ISSUED_COMPUTER { get; set; }
        public DateTime? CUSTODY_DATE { get; set; }
        public string CUSTODY_PERSON { get; set; }
        public string DOC_COPIES { get; set; }
        public string NEW_VERSION { get; set; }
        public string DOC_LABEL { get; set; }
        public string AMEND_CAUSE { get; set; }
        public string AMEND_CONTENT { get; set; }
        public string VERSION_CHANGE { get; set; }
        public string SUBMIT_PERSON { get; set; }
        public DateTime? SUBMIT_TIME { get; set; }
        public string SUBMIT_COMPUTER { get; set; }
        public string CONTROLLED_STATE { get; set; }
        public string DOC_PLACE { get; set; }
        public string DOC_KEYWORD { get; set; }
        public int? STUDY_DURATION { get; set; }
        public string DOC_PROCCESS_STATE { get; set; }
        public string DOC_SOURCE { get; set; }
        public string DOC_STATE { get; set; }
        public string FIRST_RPERSON { get; set; }
        public DateTime? FIRST_RTIME { get; set; }
        public string LAST_MPERSON { get; set; }
        public DateTime? LAST_MTIME { get; set; }
        public string REMARK { get; set; }
        public string DOC_CLASS_ID { get; set; }
    }
}
