﻿using System.ComponentModel;
using System.Reflection;
using H.Utility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using XH.H82.API.Extensions;
using XH.H82.IServices.EquipmentCodeCustom;
using XH.H82.Models.Dtos;
using XH.H82.Models.EquipmentCodeCustom;

namespace XH.H82.API.Controllers.EquipmentCodeCustom;

/// <summary>
/// 设备自定义编码字段方法
/// </summary>
[Route("api/[controller]/[action]")]
[ApiController]
[Authorize]
public class CodeCustomController : ControllerBase
{
    private readonly ICustomCodeService _customCodeService;

    public CodeCustomController(ICustomCodeService customCodeService)
    {
        _customCodeService = customCodeService;
    }
    
    
    /// <summary>
    /// 获取自定义名称模板列表
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [CustomResponseType(typeof(List<EquipmentCodeCustomDict>))]
    public IActionResult GetEquipmentCodeCustomDict()
    {
        var result = _customCodeService.GetEquipmentCodeCustomDict();
        return Ok(result.Success());
    }
    
    /// <summary>
    /// 新增自定义名称模板
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [CustomResponseType(typeof(List<EquipmentCodeCustomDict>))]
    public IActionResult AddEquipmentCodeCustomDict([FromBody] AddEquipmentCodeCustomDictDto input)
    {
        if (input.DisplayContent.DisplayContentCode.IsNullOrEmpty())
        {
            input.DisplayContent.DisplayContentCode = "{EQUIPMENT_CODE}";
            input.DisplayContent.DisplayContentString = "{设备代号}";
        }
        if (input.EqpNoName.IsNullOrEmpty())
        { 
            input.EqpNoName = "默认名称";
        }
        
        var result = _customCodeService.AddEquipmentCodeCustom(input);
        return Ok(result.Success());
    }
    
    /// <summary>
    /// 停用/启用自定义名称模板列表
    /// </summary>
    /// <returns></returns>
    [HttpPost("{EqpNoId}")]
    [CustomResponseType(typeof(bool))]
    public IActionResult EnableOrDisableEquipmentCodeCustomDict(string EqpNoId)
    {
         _customCodeService.EnableOrDisableEquipmentCodeCustomDict(EqpNoId);
        return Ok(true.Success());
    }
    
    /// <summary>
    /// 更新自定义名称模板
    /// </summary>
    /// <returns></returns>
    [HttpPut("{EqpNoId}")]
    [CustomResponseType(typeof(List<EquipmentCodeCustomDict>))]
    public IActionResult UpdateEquipmentCodeCustomDict(string EqpNoId, [FromBody] UpdateEquipmentCodeCustomDictDto input)
    {
        input.EqpNoId = EqpNoId;
        var result = _customCodeService.UpdateEquipmentCodeCustomDict(input);
        return Ok(result.Success());
    }

    /// <summary>
    /// 删除字典模板列表
    /// </summary>
    /// <returns></returns>
    [HttpDelete("{EqpNoId}")]
    [CustomResponseType(typeof(bool))]
    public IActionResult DeleteEquipmentCodeCustomDict(string EqpNoId)
    {
        _customCodeService.DeleteEquipmentCodeCustomDict(EqpNoId);
        return Ok(true.Success());
    }


    /// <summary>
    /// 获取设备下预览模板名字
    /// </summary>
    /// <param name="dictContent">自定义模板</param>
    /// <param name="eqpNoClass">一个设备类型的ID</param>
    /// <returns></returns>
    [HttpPost("{eqpNoClass}")]
    [CustomResponseType(typeof(string))]
    public IActionResult GetEquipmentUCodePreview(string eqpNoClass , [FromBody] DisplayContent dictContent)
    {
        var equipment = _customCodeService.GetEquipmentInfo(eqpNoClass);
        var result = _customCodeService.ExchangeEquipmentUCode(dictContent,equipment);
        return Ok(result.Success());
    }
    
    /// <summary>
    /// 查询可选的设备自定义属性字段
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [CustomResponseType(typeof(List<KeyValueDto>))]
    public IActionResult GetCustomDictCode()
    {
        var result = new List<KeyValueDto>();
        var props = typeof(EquipmentDictCode).GetProperties();
        foreach (var prop in props)
        {
            var descriptionAttribute = prop.GetCustomAttribute<DescriptionAttribute>();
            if (descriptionAttribute != null)
            {
                Console.WriteLine($"属性名: {prop.Name}, Description 特性值: {descriptionAttribute.Description}");
                result.Add(new KeyValueDto() { Value = descriptionAttribute.Description, Key = "{" + prop.Name + "}" });
            }
            else
            {
                Console.WriteLine($"属性名: {prop.Name}, 该属性没有 Description 特性");
            }
        }
        return Ok(result.Success());
    }
    
    

}