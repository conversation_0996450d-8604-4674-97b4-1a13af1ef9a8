﻿namespace XH.H82.API.Controllers.IoTDevice;

public class CalendarRecordsDto
{
    /// <summary>
    /// 记录类型
    /// </summary>
    public string Type { get; set; }
    /// <summary>
    /// 记录类型 中文
    /// </summary>
    public string TypeName { get; set; }
    public List<CalendarRecordDto> RecordCalendars { get; set; } = new List<CalendarRecordDto>();
    public int Conut {
        get
        {
            return RecordCalendars.Count;
        }  }
}