using H.Utility.SqlSugarInfra;
using SqlSugar;

namespace XH.H82.Models.Smbl;

[DBOwner("XH_SMBL", "")]
public class SMBL_HOSPITAL
{
    /// <summary>
    /// 医疗机构
    /// </summary>
    [SugarColumn(IsPrimaryKey = true)]
    public string HOSPITAL_ID { get; set; }
    /// <summary>
    /// 社会信用码
    /// </summary>
    public string HOSPITAL_USCC { get; set; }
    /// <summary>
    /// 机构名称
    /// </summary>
    public string? HOSPITAL_NAME { get; set; }
    /// <summary>
    /// 机构自定义名称
    /// </summary>
    public string? HOSPITAL_CNAME { get; set; }
    /// <summary>
    /// 注册码
    /// </summary>
    public string REGISTER_CODE { get; set; }
    /// <summary>
    /// 注册时间
    /// </summary>
    public DateTime? REGISTER_TIME { get; set; }
    /// <summary>
    /// 注册人员
    /// </summary>
    public string? REGISTER_PERSON { get; set; }
    /// <summary>
    /// 注册状态  0 已注册   1 未注册
    /// </summary>
    public string REGISTER_STATE { get; set; }
    public string? FIRST_RPERSON { get; set; }
    public DateTime? FIRST_RTIME { get; set; }
    public string? LAST_MPERSON { get; set; }
    public DateTime? LAST_MTIME { get; set; }
    public string? REMARK { get; set; }
}