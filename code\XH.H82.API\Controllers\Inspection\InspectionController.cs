﻿using H.Utility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using XH.H82.API.Extensions;
using XH.H82.IServices;
using XH.H82.Models.Dtos;

namespace XH.H82.API.Controllers.Inspection
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize]
    public class InspectionController : ControllerBase
    {

        private readonly IEquipmentDocService _equipmentDocService;
        private readonly IXhEquipmentDocService _xhEquipmentDocService;
        private IBaseService _baseService;

        public InspectionController(IXhEquipmentDocService xhEquipmentDocService, IBaseService baseService, IEquipmentDocService equipmentDocService)
        {
            _xhEquipmentDocService = xhEquipmentDocService;
            _baseService = baseService;
            _equipmentDocService = equipmentDocService;
        }


        /// <summary>
        ///   设备树结构（专业组）
        /// </summary>
        /// <param name="labId">科室id</param>
        /// <param name="pgroupId">专业组id</param>
        /// <param name="equipmentNo">设备代号</param>
        /// <returns></returns>
        [HttpGet]
        [CustomResponseType(typeof(List<NewOATreeDto>))]

        public IActionResult GetEquipmentListByMgroup(string labId, string? areaId, string? mgroupid, string? pgroupId, string? equipmentNo ,string? smblFlag)
        {
            var claims = User.ToClaimsDto();
            var res = _xhEquipmentDocService.GetEquipmentListByInspection(claims.USER_NO, equipmentNo, labId, areaId, mgroupid, pgroupId, "0",smblFlag);

            return Ok(res.ToResultDto());
        }


        /// <summary>
        ///   获取设备信息列表
        /// </summary>
        /// <param name="labId">科室id</param>
        /// <param name="type">设备类型</param>
        /// <param name="keyWord">检索关键字</param>
        /// <param name="pgroupId">检验专业组ID</param>
        /// <returns></returns>
        [HttpGet]
        [CustomResponseType(typeof(List<EquipmentInfoDto>))]

        public IActionResult GetEquipmentList(string labId, string? areaId, string? mgroupId, string? pgroupId, string state, string type, string keyWord)
        {
            var claims = User.ToClaimsDto();
            var res = _equipmentDocService.GetEquipments(claims.USER_NO, labId, areaId, mgroupId, pgroupId, state, type, keyWord);
            res = res.Where(x => x.IS_HIDE == null || x.IS_HIDE == "0").ToList();
            return Ok(res.ToResultDto());
        }

        /// <summary>
        /// 获取当前科室所有院区的管理专业组下的检验专业组
        /// </summary>
        /// <param name="labId"></param>
        /// <returns></returns>
        [HttpGet]
        [CustomResponseType(typeof(List<NewOATreeDto>))]
        public IActionResult GetHospitalProfessionalGroups(string labId)
        {
            var claims = User.ToClaimsDto();
            var result = _baseService.GetAllHospitalMgroups(claims.USER_NO, labId);
            return Ok(result.ToResultDto());
        }
    }
}
