﻿using System.ComponentModel.DataAnnotations;
using System.Reflection;
using System.Security.Claims;
using System.Text;
using H.BASE;
using H.Utility;
using H.Utility.Dtos;
using H.Utility.Helper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Infrastructure;
using Microsoft.IdentityModel.Tokens;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using XH.H82.API.Extensions;
using XH.H82.Base.Helper;
using XH.H82.IServices;
using XH.H82.Models.Dtos;
using XH.H82.Models.Enums;
using XH.LAB.UTILS.Models;

namespace XH.H82.API.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class SystemController : ControllerBase
    {
        private readonly IActionDescriptorCollectionProvider _actionProvider;
        private readonly IConfiguration _configuration;
        private IModuleLabGroupService _moduleLabGroupService;
        private readonly IModuleLabGroupService _IModuleLabGroupService;
        private readonly ISystemService _systemService;

        public SystemController(IActionDescriptorCollectionProvider actionProvider, IConfiguration configuration,
            IModuleLabGroupService iModuleLabGroupService, ISystemService iSystemService, IModuleLabGroupService moduleLabGroupService)
        {
            _actionProvider = actionProvider;
            _configuration = configuration;
            _IModuleLabGroupService = iModuleLabGroupService;
            _systemService = iSystemService;
            _moduleLabGroupService = moduleLabGroupService;
        }

        /// <summary>
        /// 获取其他系统的token
        /// </summary>
        /// <param name="moduleId">模块id</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetIssueTokenInfo([Required]string moduleId)
        {
            var user = User.ToClaimsDto();
            var tokenGuid = Guid.NewGuid().ToString();
            var tokenResult = _systemService.GetIssueTokenInfo(user.USER_NO, tokenGuid, moduleId, "");
            if (tokenResult.success)
            {
                var result = new
                {
                    AddressUrl = _configuration[$"{moduleId}"],
                    token = tokenResult.data,
                };
                return Ok(result.ToResultDto());
            }
            return Ok(false.ToResultDto());
        }
        
        /// <summary>
        /// 健康检查接口
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IActionResult HealthCheck()
        {
            string version = Assembly.GetExecutingAssembly().GetName().Version.ToString();
            SysInfoDto sys = new SysInfoDto();
            sys.currTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
            sys.version = version;

            return Ok(sys);
        }

        /// <summary>
        /// 导出API列表,需标记ExportInfo描述器
        /// 参考 BaseDataDemoController/ApiExportTest
        /// ApiID根据模块+路径计算哈希值生成,只要路径保持不变,则哈希值保持不变
        /// MenuId需要先约定,多个菜单用逗号隔开
        /// </summary>
        /// <param name="format">EXCEL|JSON</param>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IActionResult ExportMenuApiList(string format = "excel")
        {
            //所有路由 反射不出路径.只能先搜集所有路径
            var routes = _actionProvider.ActionDescriptors.Items.Select(x => new
            {
                Action = x.RouteValues["Action"],
                Controller = x.RouteValues["Controller"],
                Name = x.AttributeRouteInfo.Name,
                Template = x.AttributeRouteInfo.Template
            }).ToList();
            string moduleId = AppSettingsProvider.CurrModuleId;
            List<ExportApiListDto> list = new List<ExportApiListDto>();

            var types = Assembly.GetExecutingAssembly().GetTypes()
                .Where(type => typeof(ControllerBase).IsAssignableFrom(type));
            List<Type> typeList = new List<Type>();
            foreach (Type type in types)
            {
                string s = type.FullName.ToLower();
                typeList.Add(type);
            }

            typeList.Sort(delegate(Type type1, Type type2) { return type1.FullName.CompareTo(type2.FullName); });
            foreach (Type type in typeList)
            {
                MemberInfo[] members = type.FindMembers(MemberTypes.Method,
                    BindingFlags.Public |
                    BindingFlags.Static |
                    BindingFlags.NonPublic | //【位屏蔽】 
                    BindingFlags.Instance |
                    BindingFlags.DeclaredOnly,
                    Type.FilterName, "*");
                string controllerName = type.Name.Replace("Controller", "");


                foreach (var m in members)
                {
                    var attr = m.GetCustomAttributes(true)
                        .FirstOrDefault(x => x.GetType() == typeof(ExportInfo)) as ExportInfo;

                    if (attr == null)
                    {
                        continue;
                    }

                    if (m.DeclaringType.Attributes.HasFlag(TypeAttributes.Public) != true)
                        continue;

                    string actionName = m.Name;

                    var menuIds = attr.MenuId.Split(',');

                    foreach (string id in menuIds)
                    {
                        if (id.IsNotNullOrEmpty())
                        {
                            ExportApiListDto api = new ExportApiListDto();
                            var r = routes.FirstOrDefault(p =>
                                p.Action == actionName && p.Controller == controllerName);
                            api.ModuleId = moduleId;
                            api.ApiUrl = "/" + r.Template;
                            api.ApiName = attr.ApiName;
                            api.MenuId = id;
                            api.ApiId = HashHelper.ComputeHash(moduleId + api.ApiUrl).ToString();
                            list.Add(api);
                        }
                    }
                }
            }

            if (format.ToUpper() == "JSON")
            {
                return Ok(list.ToResultDto());
            }
            else
            {
                IWorkbook workbook = new XSSFWorkbook();
                ISheet sheet = workbook.CreateSheet("Sheet1"); //创建一个名称为Sheet0的表;
                IRow row = sheet.CreateRow(0); //（第一行写标题)
                row.CreateCell(0).SetCellValue("模块ID"); //第一列标题，以此类推
                row.CreateCell(1).SetCellValue("API ID");
                row.CreateCell(2).SetCellValue("API NAME");
                row.CreateCell(3).SetCellValue("路径");
                row.CreateCell(4).SetCellValue("菜单ID");
                int count = list.Count; //

                //每一行依次写入
                for (int i = 0; i < list.Count; i++)
                {
                    row = sheet.CreateRow(i + 1);
                    row.CreateCell(0).SetCellValue(list[i].ModuleId);
                    row.CreateCell(1).SetCellValue(list[i].ApiId);
                    row.CreateCell(2).SetCellValue(list[i].ApiName);
                    row.CreateCell(3).SetCellValue(list[i].ApiUrl);
                    row.CreateCell(4).SetCellValue(list[i].MenuId);
                }

                //文件写入的位置
                using (MemoryStream ms = new MemoryStream())
                {
                    workbook.Write(ms, true); //向打开的这个xls文件中写入数据
                    return File(ms.ToArray(), "application/mx-excel",
                        $"API菜单信息导出_{moduleId}_{DateTime.Now.ToString("yyyyMMdd")}.xlsx");
                }
            }
        }

        /// <summary>
        /// 获取统一登录地址
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IActionResult GetUnitLoginUrl()
        {
            var addressUnitlogin = _configuration["H57-01"]; //统一登录地址
            return Ok(addressUnitlogin.ToResultDto());
        }

        /// <summary>
        /// 获取链接文档url地址
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetLinkDocumentationSystemUrl([Required] string labId)
        {
            var url = _configuration["H91-15"];
            //var url = "https://*************:18091/public-docs-entrance?lab_Id={lab_id}&access_token={access_token}&refresh_token={refresh_token}";
            if (url is null)
            {
                throw new BizException("需要提前配置链接文档Url");
            }

            var result = url.Replace("{lab_id}", labId);
            return Ok(result.ToResultDto());
        }


        /// <summary>
        /// 获取科室信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public object GetUserLabGroup()
        {
            ResultDto dto = new ResultDto();
            try
            {
                var claims = this.User.ToClaimsDto();
                var user = _IModuleLabGroupService.GetUserInfo(claims.LOGID);

                if (user is null)
                {
                    throw new BizException("当前用户不存在");
                }

                var mgrouplist = _IModuleLabGroupService.GetLisInspectionMgroupInfo(claims.USER_NO, claims.HOSPITAL_ID);
                string strMgroupId = "";
                mgrouplist.ToList().ForEach(item => { strMgroupId += "," + item.MGROUP_ID; });
                strMgroupId = strMgroupId.TrimStart(',');
                user.MGROUPID_AGGREGATE = strMgroupId;
                return Ok(new ResultDto { msg = "", success = true, data = user, data1 = claims });
            }
            catch (Exception ex)
            {
                return Ok(new ResultDto { msg = ex.ToString(), success = false });
            }
        }

        /// <summary>
        /// 测试token
        /// </summary>
        /// <param name="logId"></param>
        /// <param name="password"></param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetToken([FromQuery] string logId = "test",
            [FromQuery] string password = "C94580BEB9D5576CA50283B4A761E599E023CFF3774A51AC8051DC57CF0961AD")
        {
            var user = _IModuleLabGroupService.GetUserInfo(logId, password);
            // 用户检查
            if (user == null)
                return Ok(new ResultDto { success = false, msg = "无该用户信息" });

            string fileUploadAddress =
                "http://" + _IModuleLabGroupService.GetModuleInfoByModuleId(user.HOSPITAL_ID, "J12"); //
            string filePreviewAddress =
                "http://" + _IModuleLabGroupService.GetModuleInfoByModuleId(user.HOSPITAL_ID, "J12-03"); //J12-03
            user.FILE_UPLOAD_ADDRESS = fileUploadAddress;
            user.FILE_PREVIEW_ADDRESS = filePreviewAddress;
            var mgrouplist = _IModuleLabGroupService.GetLisInspectionMgroupInfo(user.USER_NO, user.HOSPITAL_ID);
            string strMgroupId = "";
            mgrouplist.ToList().ForEach(item => { strMgroupId += "," + item.MGROUP_ID; });
            strMgroupId = strMgroupId.TrimStart(',');

            var claims = new[]
            {
                new Claim("USER_NO", user.USER_NO),
                new Claim("USER_NAME", user.USERNAME),
                new Claim("USER_NO", user.USER_NO),
                new Claim("HOSPITAL_ID", user.HOSPITAL_ID),
                new Claim("LAB_ID", user.LAB_ID),
                new Claim("PASSWORD", password),
                new Claim("FILE_UPLOAD_ADDRESS", fileUploadAddress),
                new Claim("FILE_PREVIEW_ADDRESS", filePreviewAddress),
                new Claim("MGROUPID_AGGREGATE", strMgroupId),
            };
            var key = new SymmetricSecurityKey(
                Encoding.UTF8.GetBytes("SA_JWT_SCRET_LONGLONGLONGLONGLONGLONGLONG"));
            var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);
            //用户信息挂载访问令牌
            user.Token = JwtTokenHelper.CreateToken(claims, 24);
            return Ok(new ResultDto()
            {
                data = $"Bearer {user.Token}",
                data1 = claims
            });
            //return Ok(user.ToResultDto());
        }

        [HttpGet]
        public IActionResult ReNewToken(string expriedToken, string refreshToken)
        {
            var claim = this.User.ToClaimsDto();

            var res = _systemService.ReNewToken(expriedToken, refreshToken);
            return Ok(res);
        }

        /// <summary>
        /// 读取更新日志
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet]
        public IActionResult UpdateLog()
        {
            string pathFile = Path.Combine(AppContext.BaseDirectory, "update_log.md");
            if (!System.IO.File.Exists(pathFile))
            {
                throw new FileNotFoundException("更新日志文件不存在");
            }

            string str = System.IO.File.ReadAllText(pathFile);
            return Ok(str.ToResultDto());
        }

        /// <summary>
        /// 获取关于系统
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult AboutSystem()
        {
            var address = _configuration["H57-03"].Replace("{MODULE_ID}", "H82");
            return Ok(address.ToResultDto());
        }

        [HttpGet]
        [AllowAnonymous]
        public IActionResult GetTokenSubstitution(string accessToken, string refreshToken)
        {
            return Ok(_systemService.TokenSubstitution(accessToken, refreshToken));
        }

        /// <summary>
        /// 用户验证
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult UserVerify(string smPassword)
        {
            var user = this.User.ToClaimsDto();
            //用户验证
            var passwordIsTrue = _IModuleLabGroupService.GetUserInfo(user.LOGID, smPassword);
            if (passwordIsTrue is null)
            {
                return Ok(false.ToResultDto(false, "密码错误！"));
            }
            else
            {
                return Ok(true.ToResultDto());
            }
            
        }

        /// <summary>
        /// 获取机构信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Authorize]
        public IActionResult GetHospitalInfo([Required] string hospitalId)
        {
            return Ok(_systemService.GetHospitalInfo(hospitalId));
        }

        
        /// <summary>
        /// 查询当前用户是否拥有设备档案删除权限
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Authorize]
        public IActionResult HasEquipmentDeletedPromiss()
        {
            return Ok(_systemService.HasEquipmentDeletedPromiss().ToResultDto());
        }
        
        /// <summary>
        /// 查询当前用户是否拥有某按钮权限
        /// </summary>
        /// <param name="buttonPermissionId"></param>
        /// <returns></returns>
        [HttpGet]
        [Authorize]
        public IActionResult HasButtonPermissions(string buttonPermissionId)
        {
            return Ok(_systemService.HasButtonPermissions(buttonPermissionId).ToResultDto());
        }

        /// <summary>
        /// 生安相关接口
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Authorize]
        [CustomResponseType(typeof(LogonHospitalLabList))]
        public IActionResult GetLogonHospitalLablist()
        {
            var user = User.ToClaimsDto();
            var result  =  _systemService.GetLogonHospitalLablist(user);
            return Ok(result.ToResultDto());
        }
        
       /// <summary>
       /// 根据供应商类型查询供应商服务范围
       /// </summary>
       /// <param name="type">供应商类型</param>
       /// <returns></returns>
        [HttpGet]
        [Authorize]
        [CustomResponseType(typeof(List<KeyValueDto>))]
        public IActionResult ServiceAreaList(string type)
        {
            var result = new List<KeyValueDto>();

            if (type == "5")
            {
                foreach (var enumValue in Enum.GetValues<HospitalInternalServiceAreaEnum>())
                {
                    result.Add(new KeyValueDto()
                    {
                        Key = ((int)enumValue).ToString(),
                        Value = enumValue.GetEnumDescription()
                    });
                }
            }
            else
            {
                foreach (var enumValue in Enum.GetValues<ServiceAreaEnum>())
                {
                    result.Add(new KeyValueDto()
                    {
                        Key = ((int)enumValue).ToString(),
                        Value = enumValue.GetEnumDescription()
                    });
                }
            }
            return Ok(result.ToResultDto());
        }
    }
}