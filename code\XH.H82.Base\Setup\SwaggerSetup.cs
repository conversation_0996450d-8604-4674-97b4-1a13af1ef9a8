﻿#region (c) 2022 杏和软件. All rights reserved.

// @Author: chen<PERSON><PERSON><PERSON>
// @CreateRecordDict: 2022-11-03 18:42
// @LastModified: 2022-11-03 15:44
// @Des:Swagger注册类

#endregion

using Microsoft.Extensions.DependencyInjection;
using Microsoft.OpenApi.Models;
using Serilog;
using Swashbuckle.AspNetCore.Filters;
using Unchase.Swashbuckle.AspNetCore.Extensions.Extensions;

namespace H.BASE;

public static class SwaggerSetup
{
    public static void AddSwaggerSetup(this IServiceCollection services)
    {
        
        services.AddSwaggerGen(c =>
        {
            //根路径
            var basePath = AppContext.BaseDirectory;
            c.SwaggerDoc("XH.H82.API", new OpenApiInfo
            {
                Title = "XH.H82.API 接口文档 "
            });
            c.SwaggerDoc("DEMO", new OpenApiInfo
            {
                Title = "DEMO演示接口(注意:_demos下控制器仅开发模式下可用)"
            });
            c.OrderActionsBy(o => o.RelativePath);

            try
            {
                var xmlPath = Path.Combine(basePath, "XH.H82.API.xml");
                c.IncludeXmlCommentsWithRemarks(xmlPath, true);

                var xmlModelPath = Path.Combine(basePath, "XH.H82.Models.xml"); //这个就是Model层的xml文件名
                c.IncludeXmlCommentsWithRemarks(xmlModelPath, true);
            }
            catch (Exception ex)
            {
                Log.Error("XH.H82.API.xml或XXH.H82.Models.xml 丢失，请检查并拷贝。\n" + ex.Message);
            }

            // 开启加权小锁
            c.OperationFilter<AddResponseHeadersFilter>();
            c.OperationFilter<AppendAuthorizeToSummaryOperationFilter>();

            // 在header中添加token，传递到后台
            c.OperationFilter<SecurityRequirementsOperationFilter>();

            // 必须是 oauth2
            c.AddSecurityDefinition("oauth2", new OpenApiSecurityScheme
            {
                Description = "JWT授权(数据将在请求头中进行传输) 直接在下框中输入: Bearer {token}（注意两者之间是一个空格）\"",
                Name = "Authorization", //jwt默认的参数名称
                In = ParameterLocation.Header, //jwt默认存放Authorization信息的位置(请求头中)
                Type = SecuritySchemeType.ApiKey
            });
        });
        
    }
}