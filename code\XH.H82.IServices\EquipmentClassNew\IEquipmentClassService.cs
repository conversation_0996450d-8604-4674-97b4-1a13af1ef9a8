﻿using System.Linq.Expressions;
using XH.H82.Base.Tree;
using XH.H82.Models.EquipmengtClassNew;

namespace XH.H82.IServices.EquipmentClassNew;

public interface IEquipmentClassService
{
    /// <summary>
    /// 获取设备分类字典
    /// </summary>
    /// <returns></returns>
    public List<EMS_EQUIPMENT_CLASS_DICT> GetEquipmentClassDict();

    /// <summary>
    /// 获取设备分类字典树
    /// </summary>
    /// <param name="hasAll"> 是否包含根节点</param>
    /// <param name="isEdit">是否编辑</param>
    /// <returns></returns>
    public List<ITreeNode> GetEquipmentClassDictTree(bool hasAll = true , bool isEdit = false);
    
    /// <summary>
    /// 获取设备档案字典
    /// </summary>
    /// <param name="filter"></param>
    /// <returns></returns>
    List<EMS_EQP_ARCHIVES_DICT> GetEquipmentArchivesByCondition(Expression<Func<EMS_EQP_ARCHIVES_DICT, bool>>  filter);
    
    
    /// <summary>
    /// 查询设备类型已关联的档案记录
    /// </summary>
    /// <param name="classId">设备分类id</param>
    /// <returns></returns>
    List<EMS_EQP_ARCHIVES_DICT> GetEquipmentArchivesLinkByClassId(string classId);

    
    /// <summary>
    /// 查询设备类型未关联的档案记录
    /// </summary>
    /// <param name="classId">设备分类id</param>
    /// <returns></returns>
    List<EMS_EQP_ARCHIVES_DICT> GetEquipmentArchivesNotLinkByClassId(string classId);
    
    

    /// <summary>
    /// 添加设备类型字段（细分类）
    /// </summary>
    /// <param name="classDto"></param>
    /// <returns></returns>
    EMS_EQUIPMENT_CLASS_DICT AddClassDict( AddEquipmentClassDto classDto);


    /// <summary>
    /// 更新设备类型字段（细分类）
    /// </summary>
    /// <param name="classId">分类id</param>
    /// <param name="classDto">更新内容</param>
    /// <returns></returns>
    EMS_EQUIPMENT_CLASS_DICT UpdateClassDict(string classId, AddEquipmentClassDto classDto);
    
    /// <summary>
    /// 删除设备类型字段（细分类）
    /// </summary>
    ///  <param name="classId">分类id</param>
    bool DeleteClassDict(string classId);

    /// <summary>
    /// 禁用或启用设备类型字段（细分类）
    /// </summary>
    ///  <param name="classId">分类id</param>
    bool DisableOrEnableClassDict(string classId);

    /// <summary>
    /// 添加关联关系
    /// </summary>
    /// <param name="classId">设备类型字典id</param>
    /// <param name="archivesId">档案记录id</param>
    /// <returns></returns>
    bool LinkClassAndArchives(string classId, string[] archivesIds);
    /// <summary>
    /// 删除关联关系
    /// </summary>
    /// <param name="classId">设备类型字典id</param>
    /// <param name="archivesId">档案记录id</param>
    /// <returns></returns>
    bool UnLinkClassAndArchives(string classId, string[] archivesIds);

    List<EMS_EQP_ARCHIVES_DICT> GetEquipmentArchives();
}