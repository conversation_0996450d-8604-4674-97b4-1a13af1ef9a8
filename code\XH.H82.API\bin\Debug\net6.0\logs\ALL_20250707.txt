2025-07-07 09:26:00.644 +08:00 [INF] ==>App Start..2025-07-07 09:26:00
2025-07-07 09:26:00.852 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-07 09:26:00.857 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-07 09:26:03.243 +08:00 [INF] ==>基础连接请求完成.
2025-07-07 09:26:03.656 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-07 09:26:04.100 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-07 09:26:04.441 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-07 09:26:04.444 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-07 09:26:04.782 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-07 09:26:07.137 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-07 09:26:07.563 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-07 09:26:08.337 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-07 09:26:08.338 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-07 09:26:09.792 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-07 09:26:12.475 +08:00 [INF] ==>初始化完成..
2025-07-07 09:26:12.551 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-07 09:26:12.553 +08:00 [INF] 设备启用任务
2025-07-07 09:26:12.553 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-07 09:26:12.979 +08:00 [INF] 【SQL执行耗时:404.7215ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-07 09:26:13.172 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-07 09:26:13.191 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-07 09:26:13.193 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-07 09:26:13.193 +08:00 [INF] Hosting environment: Development
2025-07-07 09:26:13.193 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-07 09:26:14.553 +08:00 [INF] HTTP GET /favicon.ico responded 404 in 253.2801 ms
2025-07-07 10:15:03.377 +08:00 [INF] ==>App Start..2025-07-07 10:15:03
2025-07-07 10:15:03.546 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-07 10:15:03.549 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-07 10:15:04.316 +08:00 [ERR] ==>请求基础连接时发生错误:不知道这样的主机。 (d01.lis-china.com:18801)
System.Net.Http.HttpRequestException: 不知道这样的主机。 (d01.lis-china.com:18801)
 ---> System.Net.Sockets.SocketException (11001): 不知道这样的主机。
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|277_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at RestSharp.ResponseThrowExtension.ThrowIfError(RestResponse response)
   at RestSharp.RestClient.ExecuteAsync(RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.ExecuteAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.GetAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.AsyncHelpers.<>c__DisplayClass1_0`1.<<RunSync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.<Run>g__PostCallback|7_0(Object _)
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.Run()
   at RestSharp.AsyncHelpers.RunSync(Func`1 task)
   at RestSharp.AsyncHelpers.RunSync[T](Func`1 task)
   at RestSharp.RestClientExtensions.Get[T](IRestClient client, RestRequest request)
   at H.BASE.Infrastructure.BaseConfigRequestHelper.GetBaseConfigFromBaseService(String urlS01, String hospitalId, String moduleId)
2025-07-07 10:15:04.398 +08:00 [WRN] 调用[S01]获取基础连接配置失败 将在：2秒后重试
2025-07-07 10:15:06.408 +08:00 [ERR] ==>请求基础连接时发生错误:不知道这样的主机。 (d01.lis-china.com:18801)
System.Net.Http.HttpRequestException: 不知道这样的主机。 (d01.lis-china.com:18801)
 ---> System.Net.Sockets.SocketException (11001): 不知道这样的主机。
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|277_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at RestSharp.ResponseThrowExtension.ThrowIfError(RestResponse response)
   at RestSharp.RestClient.ExecuteAsync(RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.ExecuteAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.GetAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.AsyncHelpers.<>c__DisplayClass1_0`1.<<RunSync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.<Run>g__PostCallback|7_0(Object _)
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.Run()
   at RestSharp.AsyncHelpers.RunSync(Func`1 task)
   at RestSharp.AsyncHelpers.RunSync[T](Func`1 task)
   at RestSharp.RestClientExtensions.Get[T](IRestClient client, RestRequest request)
   at H.BASE.Infrastructure.BaseConfigRequestHelper.GetBaseConfigFromBaseService(String urlS01, String hospitalId, String moduleId)
2025-07-07 10:15:06.413 +08:00 [WRN] 调用[S01]获取基础连接配置失败 将在：2秒后重试
2025-07-07 10:15:08.431 +08:00 [ERR] ==>请求基础连接时发生错误:不知道这样的主机。 (d01.lis-china.com:18801)
System.Net.Http.HttpRequestException: 不知道这样的主机。 (d01.lis-china.com:18801)
 ---> System.Net.Sockets.SocketException (11001): 不知道这样的主机。
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|277_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at RestSharp.ResponseThrowExtension.ThrowIfError(RestResponse response)
   at RestSharp.RestClient.ExecuteAsync(RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.ExecuteAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.GetAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.AsyncHelpers.<>c__DisplayClass1_0`1.<<RunSync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.<Run>g__PostCallback|7_0(Object _)
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.Run()
   at RestSharp.AsyncHelpers.RunSync(Func`1 task)
   at RestSharp.AsyncHelpers.RunSync[T](Func`1 task)
   at RestSharp.RestClientExtensions.Get[T](IRestClient client, RestRequest request)
   at H.BASE.Infrastructure.BaseConfigRequestHelper.GetBaseConfigFromBaseService(String urlS01, String hospitalId, String moduleId)
2025-07-07 10:15:08.436 +08:00 [WRN] 调用[S01]获取基础连接配置失败 将在：2秒后重试
2025-07-07 10:15:11.645 +08:00 [INF] ==>基础连接请求完成.
2025-07-07 10:15:11.993 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-07 10:15:12.350 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-07 10:15:12.683 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-07 10:15:12.684 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-07 10:15:13.023 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-07 10:15:13.540 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-07 10:15:13.640 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-07 10:15:14.077 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-07 10:15:14.077 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-07 10:15:15.115 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-07 10:15:17.781 +08:00 [INF] ==>初始化完成..
2025-07-07 10:15:17.800 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-07 10:15:17.804 +08:00 [INF] 设备启用任务
2025-07-07 10:15:17.805 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-07 10:15:18.164 +08:00 [INF] 【SQL执行耗时:341.8205ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-07 10:15:18.310 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-07 10:15:18.323 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-07 10:15:18.323 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-07 10:15:18.324 +08:00 [INF] Hosting environment: Development
2025-07-07 10:15:18.324 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-07 10:15:37.671 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetEquipmentArchivesLinkByClassId/0 responded 401 in 191.9994 ms
2025-07-07 10:16:16.820 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetEquipmentArchivesLinkByClassId/0 responded 401 in 161.8484 ms
2025-07-07 10:16:50.993 +08:00 [INF] ==>App Start..2025-07-07 10:16:50
2025-07-07 10:16:51.177 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-07 10:16:51.180 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-07 10:16:52.601 +08:00 [INF] ==>基础连接请求完成.
2025-07-07 10:16:52.962 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-07 10:16:53.315 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-07 10:16:53.630 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-07 10:16:53.632 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-07 10:16:53.962 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-07 10:16:54.386 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-07 10:16:54.492 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-07 10:16:54.947 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-07 10:16:54.947 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-07 10:16:55.792 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-07 10:16:58.457 +08:00 [INF] ==>初始化完成..
2025-07-07 10:16:58.476 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-07 10:16:58.479 +08:00 [INF] 设备启用任务
2025-07-07 10:16:58.479 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-07 10:16:58.844 +08:00 [INF] 【SQL执行耗时:348.002ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-07 10:16:58.977 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-07 10:16:58.989 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-07 10:16:58.991 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-07 10:16:58.991 +08:00 [INF] Hosting environment: Development
2025-07-07 10:16:58.991 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-07 10:17:12.355 +08:00 [ERR] 未处理的异常::System.NullReferenceException: Object reference not set to an instance of an object.
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService.GetEquipmentArchivesLinkByClassId(String classId) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 172
   at Castle.Proxies.Invocations.IEquipmentClassService_GetEquipmentArchivesLinkByClassId.InvokeMethodOnTarget()
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.UseCacheWhenFailedAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.CacheAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at Castle.Proxies.IEquipmentClassServiceProxy.GetEquipmentArchivesLinkByClassId(String classId)
   at XH.H82.API.Controllers.EquipmentClassNew.EquipmentClassNewController.GetEquipmentArchivesLinkByClassId(String classId) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\Controllers\EquipmentClassNew\EquipmentClassNewController.cs:line 96
   at lambda_method910(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-07-07 10:17:12.380 +08:00 [ERR] HTTP GET /api/EquipmentClassNew/GetEquipmentArchivesLinkByClassId/1 responded 500 in 7456.7313 ms
2025-07-07 10:17:12.384 +08:00 [INF] 【接口超时阀值预警】 [92980ba2c4b2ce3c3dc47a2431d8fe84]接口/api/EquipmentClassNew/GetEquipmentArchivesLinkByClassId/1,耗时:[7466]毫秒
2025-07-07 10:22:20.363 +08:00 [ERR] 未处理的异常::System.NullReferenceException: Object reference not set to an instance of an object.
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService.GetEquipmentArchivesLinkByClassId(String classId)
   at Castle.Proxies.Invocations.IEquipmentClassService_GetEquipmentArchivesLinkByClassId.InvokeMethodOnTarget()
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.UseCacheWhenFailedAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.CacheAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at Castle.Proxies.IEquipmentClassServiceProxy.GetEquipmentArchivesLinkByClassId(String classId)
   at XH.H82.API.Controllers.EquipmentClassNew.EquipmentClassNewController.GetEquipmentArchivesLinkByClassId(String classId) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\Controllers\EquipmentClassNew\EquipmentClassNewController.cs:line 96
   at lambda_method910(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-07-07 10:22:20.365 +08:00 [ERR] HTTP GET /api/EquipmentClassNew/GetEquipmentArchivesLinkByClassId/1 responded 500 in 5631.4114 ms
2025-07-07 10:22:20.365 +08:00 [INF] 【接口超时阀值预警】 [c71cf116e07a7d3ce3931d439d4d0edb]接口/api/EquipmentClassNew/GetEquipmentArchivesLinkByClassId/1,耗时:[5631]毫秒
2025-07-07 10:23:43.950 +08:00 [INF] ==>App Start..2025-07-07 10:23:43
2025-07-07 10:23:44.123 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-07 10:23:44.126 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-07 10:23:44.784 +08:00 [ERR] ==>请求基础连接时发生错误:不知道这样的主机。 (d01.lis-china.com:18801)
System.Net.Http.HttpRequestException: 不知道这样的主机。 (d01.lis-china.com:18801)
 ---> System.Net.Sockets.SocketException (11001): 不知道这样的主机。
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|277_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at RestSharp.ResponseThrowExtension.ThrowIfError(RestResponse response)
   at RestSharp.RestClient.ExecuteAsync(RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.ExecuteAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.GetAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.AsyncHelpers.<>c__DisplayClass1_0`1.<<RunSync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.<Run>g__PostCallback|7_0(Object _)
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.Run()
   at RestSharp.AsyncHelpers.RunSync(Func`1 task)
   at RestSharp.AsyncHelpers.RunSync[T](Func`1 task)
   at RestSharp.RestClientExtensions.Get[T](IRestClient client, RestRequest request)
   at H.BASE.Infrastructure.BaseConfigRequestHelper.GetBaseConfigFromBaseService(String urlS01, String hospitalId, String moduleId)
2025-07-07 10:23:44.869 +08:00 [WRN] 调用[S01]获取基础连接配置失败 将在：2秒后重试
2025-07-07 10:23:46.885 +08:00 [ERR] ==>请求基础连接时发生错误:不知道这样的主机。 (d01.lis-china.com:18801)
System.Net.Http.HttpRequestException: 不知道这样的主机。 (d01.lis-china.com:18801)
 ---> System.Net.Sockets.SocketException (11001): 不知道这样的主机。
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|277_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at RestSharp.ResponseThrowExtension.ThrowIfError(RestResponse response)
   at RestSharp.RestClient.ExecuteAsync(RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.ExecuteAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.GetAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.AsyncHelpers.<>c__DisplayClass1_0`1.<<RunSync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.<Run>g__PostCallback|7_0(Object _)
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.Run()
   at RestSharp.AsyncHelpers.RunSync(Func`1 task)
   at RestSharp.AsyncHelpers.RunSync[T](Func`1 task)
   at RestSharp.RestClientExtensions.Get[T](IRestClient client, RestRequest request)
   at H.BASE.Infrastructure.BaseConfigRequestHelper.GetBaseConfigFromBaseService(String urlS01, String hospitalId, String moduleId)
2025-07-07 10:23:46.890 +08:00 [WRN] 调用[S01]获取基础连接配置失败 将在：2秒后重试
2025-07-07 10:23:48.901 +08:00 [ERR] ==>请求基础连接时发生错误:不知道这样的主机。 (d01.lis-china.com:18801)
System.Net.Http.HttpRequestException: 不知道这样的主机。 (d01.lis-china.com:18801)
 ---> System.Net.Sockets.SocketException (11001): 不知道这样的主机。
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|277_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at RestSharp.ResponseThrowExtension.ThrowIfError(RestResponse response)
   at RestSharp.RestClient.ExecuteAsync(RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.ExecuteAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.GetAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.AsyncHelpers.<>c__DisplayClass1_0`1.<<RunSync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.<Run>g__PostCallback|7_0(Object _)
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.Run()
   at RestSharp.AsyncHelpers.RunSync(Func`1 task)
   at RestSharp.AsyncHelpers.RunSync[T](Func`1 task)
   at RestSharp.RestClientExtensions.Get[T](IRestClient client, RestRequest request)
   at H.BASE.Infrastructure.BaseConfigRequestHelper.GetBaseConfigFromBaseService(String urlS01, String hospitalId, String moduleId)
2025-07-07 10:23:48.906 +08:00 [WRN] 调用[S01]获取基础连接配置失败 将在：2秒后重试
2025-07-07 10:23:52.144 +08:00 [INF] ==>基础连接请求完成.
2025-07-07 10:23:52.493 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-07 10:23:52.845 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-07 10:23:53.161 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-07 10:23:53.162 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-07 10:23:53.501 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-07 10:23:54.011 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-07 10:23:54.114 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-07 10:23:54.542 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-07 10:23:54.542 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-07 10:23:55.458 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-07 10:23:58.097 +08:00 [INF] ==>初始化完成..
2025-07-07 10:23:58.120 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-07 10:23:58.123 +08:00 [INF] 设备启用任务
2025-07-07 10:23:58.124 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-07 10:23:58.491 +08:00 [INF] 【SQL执行耗时:348.3027ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-07 10:23:58.635 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-07 10:23:58.649 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-07 10:23:58.650 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-07 10:23:58.650 +08:00 [INF] Hosting environment: Development
2025-07-07 10:23:58.650 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-07 10:24:40.446 +08:00 [INF] ==>App Start..2025-07-07 10:24:40
2025-07-07 10:24:40.619 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-07 10:24:40.622 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-07 10:24:42.020 +08:00 [INF] ==>基础连接请求完成.
2025-07-07 10:24:42.390 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-07 10:24:42.721 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-07 10:24:43.026 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-07 10:24:43.032 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-07 10:24:43.358 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-07 10:24:43.792 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-07 10:24:43.891 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-07 10:24:44.306 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-07 10:24:44.306 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-07 10:24:45.197 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-07 10:24:47.909 +08:00 [INF] ==>初始化完成..
2025-07-07 10:24:47.931 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-07 10:24:47.934 +08:00 [INF] 设备启用任务
2025-07-07 10:24:47.934 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-07 10:24:48.291 +08:00 [INF] 【SQL执行耗时:339.2171ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-07 10:24:48.425 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-07 10:24:48.441 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-07 10:24:48.443 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-07 10:24:48.443 +08:00 [INF] Hosting environment: Development
2025-07-07 10:24:48.443 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-07 10:25:04.018 +08:00 [INF] 【SQL执行耗时:363.4837ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT" 

2025-07-07 10:25:04.610 +08:00 [INF] 【SQL执行耗时:498.5607ms】

[Sql]:SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON" FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( (   "CLASS_ID" = :Condit_CLASS_ID_1000   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1001   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1002   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1003   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1004   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1005   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1006   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1007   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1008   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1009   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1010   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1011   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1012   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1013   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1014   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1015   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1016   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1017   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1018   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1019   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1020   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1021   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1022   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1023   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1024   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1025   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1026   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1027   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1028   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1029   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1030   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1031   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1032   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1033   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1034   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1035   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1036   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1037   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1038   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1039   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1040   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1041   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1042   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1043   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1044   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1045   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1046   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1047   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1048   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1049   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1050   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1051   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1052   )  OR (   "CLASS_ID" = :Condit_CLASS_ID_1053   ) )  
[Pars]:
[Name]::Condit_CLASS_ID_1000 [Value]:H82CLASSJZL [Type]:String    
[Name]::Condit_CLASS_ID_1001 [Value]:H82CLASSXXL [Type]:String    
[Name]::Condit_CLASS_ID_1002 [Value]:H82CLASSFZL [Type]:String    
[Name]::Condit_CLASS_ID_1003 [Value]:H82CLASSGYL [Type]:String    
[Name]::Condit_CLASS_ID_1004 [Value]:1 [Type]:String    
[Name]::Condit_CLASS_ID_1005 [Value]:4 [Type]:String    
[Name]::Condit_CLASS_ID_1006 [Value]:6 [Type]:String    
[Name]::Condit_CLASS_ID_1007 [Value]:7 [Type]:String    
[Name]::Condit_CLASS_ID_1008 [Value]:11 [Type]:String    
[Name]::Condit_CLASS_ID_1009 [Value]:2 [Type]:String    
[Name]::Condit_CLASS_ID_1010 [Value]:3 [Type]:String    
[Name]::Condit_CLASS_ID_1011 [Value]:12 [Type]:String    
[Name]::Condit_CLASS_ID_1012 [Value]:13 [Type]:String    
[Name]::Condit_CLASS_ID_1013 [Value]:5 [Type]:String    
[Name]::Condit_CLASS_ID_1014 [Value]:10 [Type]:String    
[Name]::Condit_CLASS_ID_1015 [Value]:14 [Type]:String    
[Name]::Condit_CLASS_ID_1016 [Value]:15 [Type]:String    
[Name]::Condit_CLASS_ID_1017 [Value]:16 [Type]:String    
[Name]::Condit_CLASS_ID_1018 [Value]:17 [Type]:String    
[Name]::Condit_CLASS_ID_1019 [Value]:10001 [Type]:String    
[Name]::Condit_CLASS_ID_1020 [Value]:10002 [Type]:String    
[Name]::Condit_CLASS_ID_1021 [Value]:10003 [Type]:String    
[Name]::Condit_CLASS_ID_1022 [Value]:60001 [Type]:String    
[Name]::Condit_CLASS_ID_1023 [Value]:60002 [Type]:String    
[Name]::Condit_CLASS_ID_1024 [Value]:40001 [Type]:String    
[Name]::Condit_CLASS_ID_1025 [Value]:40002 [Type]:String    
[Name]::Condit_CLASS_ID_1026 [Value]:70001 [Type]:String    
[Name]::Condit_CLASS_ID_1027 [Value]:70002 [Type]:String    
[Name]::Condit_CLASS_ID_1028 [Value]:70003 [Type]:String    
[Name]::Condit_CLASS_ID_1029 [Value]:110001 [Type]:String    
[Name]::Condit_CLASS_ID_1030 [Value]:110002 [Type]:String    
[Name]::Condit_CLASS_ID_1031 [Value]:110003 [Type]:String    
[Name]::Condit_CLASS_ID_1032 [Value]:110004 [Type]:String    
[Name]::Condit_CLASS_ID_1033 [Value]:110005 [Type]:String    
[Name]::Condit_CLASS_ID_1034 [Value]:110006 [Type]:String    
[Name]::Condit_CLASS_ID_1035 [Value]:20001 [Type]:String    
[Name]::Condit_CLASS_ID_1036 [Value]:20002 [Type]:String    
[Name]::Condit_CLASS_ID_1037 [Value]:30001 [Type]:String    
[Name]::Condit_CLASS_ID_1038 [Value]:30002 [Type]:String    
[Name]::Condit_CLASS_ID_1039 [Value]:120001 [Type]:String    
[Name]::Condit_CLASS_ID_1040 [Value]:130001 [Type]:String    
[Name]::Condit_CLASS_ID_1041 [Value]:130002 [Type]:String    
[Name]::Condit_CLASS_ID_1042 [Value]:130003 [Type]:String    
[Name]::Condit_CLASS_ID_1043 [Value]:50001 [Type]:String    
[Name]::Condit_CLASS_ID_1044 [Value]:100001 [Type]:String    
[Name]::Condit_CLASS_ID_1045 [Value]:100002 [Type]:String    
[Name]::Condit_CLASS_ID_1046 [Value]:100003 [Type]:String    
[Name]::Condit_CLASS_ID_1047 [Value]:140001 [Type]:String    
[Name]::Condit_CLASS_ID_1048 [Value]:140002 [Type]:String    
[Name]::Condit_CLASS_ID_1049 [Value]:140003 [Type]:String    
[Name]::Condit_CLASS_ID_1050 [Value]:150001 [Type]:String    
[Name]::Condit_CLASS_ID_1051 [Value]:150002 [Type]:String    
[Name]::Condit_CLASS_ID_1052 [Value]:160001 [Type]:String    
[Name]::Condit_CLASS_ID_1053 [Value]:170001 [Type]:String    

2025-07-07 10:25:05.133 +08:00 [INF] 【SQL执行耗时:414.6893ms】

[Sql]:INSERT ALL
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'H82CLASSJZL',N'33A001',N'0',N'检测类',N'检测类',N'0',NULL,N'0',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085337', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085467', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'H82CLASSXXL',N'33A001',N'0',N'信息设备类',N'信息设备类',N'0',NULL,N'0',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085803', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085803', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'H82CLASSFZL',N'33A001',N'0',N'辅助监测类',N'辅助监测类',N'0',NULL,N'0',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085803', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085803', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'H82CLASSGYL',N'33A001',N'0',N'公用设备',N'公用设备',N'0',NULL,N'0',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085803', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085803', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'1',N'33A001',N'H82CLASSJZL',N'检测仪器/流水线',N'常',N'0',N'#1677FF-#ffffff',N'1',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085803', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085803', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'4',N'33A001',N'H82CLASSJZL',N'POCT检测设备',N'P',N'0',N'#1677FF-#ffffff',N'1',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085803', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085803', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'6',N'33A001',N'H82CLASSJZL',N'计量设备',N'计',N'0',N'#1677FF-#ffffff',N'1',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085803', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085803', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'7',N'33A001',N'H82CLASSXXL',N'信息设备',N'信',N'0',N'#1677FF-#ffffff',N'1',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085803', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085803', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'11',N'33A001',N'H82CLASSXXL',N'监测设备',N'监',N'0',N'#1677FF-#ffffff',N'1',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085804', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085804', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'2',N'33A001',N'H82CLASSFZL',N'标本处理设备',N'标',N'0',N'#1677FF-#ffffff',N'1',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085804', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085804', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'3',N'33A001',N'H82CLASSFZL',N'检测辅助设备',N'辅',N'0',N'#1677FF-#ffffff',N'1',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085804', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085804', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'12',N'33A001',N'H82CLASSFZL',N'消毒设备',N'消',N'0',N'#1677FF-#ffffff',N'1',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085804', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085804', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'13',N'33A001',N'H82CLASSFZL',N'孵育设备',N'孵',N'0',N'#1677FF-#ffffff',N'1',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085804', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085804', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'5',N'33A001',N'H82CLASSFZL',N'存储设备',N'存',N'0',N'#1677FF-#ffffff',N'1',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085804', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085804', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'10',N'33A001',N'H82CLASSFZL',N'特种设备',N'特',N'0',N'#1677FF-#ffffff',N'1',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085804', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085804', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'14',N'33A001',N'H82CLASSGYL',N'样本前处理设备',N'前',N'0',N'#1677FF-#ffffff',N'1',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085804', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085804', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'15',N'33A001',N'H82CLASSGYL',N'冲洗设备',N'洗',N'0',N'#1677FF-#ffffff',N'1',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085804', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085804', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'16',N'33A001',N'H82CLASSGYL',N'纯化设备',N'纯',N'0',N'#1677FF-#ffffff',N'1',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085804', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085805', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'17',N'33A001',N'H82CLASSGYL',N'制冷设备',N'冷',N'0',N'#1677FF-#ffffff',N'1',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085805', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085805', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'10001',N'33A001',N'1',N'检测仪器',N'检测仪器',N'0',N'#1677FF-#ffffff',N'2',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085805', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085805', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'10002',N'33A001',N'1',N'流水线',N'流水线',N'0',N'#1677FF-#ffffff',N'2',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085805', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085805', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'10003',N'33A001',N'1',N'虚拟手工',N'虚',N'0',N'#1677FF-#ffffff',N'2',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085805', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085805', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'60001',N'33A001',N'6',N'移液器',N'移液器',N'0',N'#1677FF-#ffffff',N'2',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085805', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085805', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'60002',N'33A001',N'6',N'称重',N'称重',N'0',N'#1677FF-#ffffff',N'2',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085805', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085805', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'40001',N'33A001',N'4',N'POCT血糖仪',N'POCT血糖仪',N'0',N'#1677FF-#ffffff',N'2',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085805', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085805', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'40002',N'33A001',N'4',N'POCT血气',N'POCT血气',N'0',N'#1677FF-#ffffff',N'2',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085805', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085805', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'70001',N'33A001',N'7',N'电脑',N'电脑',N'0',N'#1677FF-#ffffff',N'2',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085805', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085805', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'70002',N'33A001',N'7',N'打印机',N'打印机',N'0',N'#1677FF-#ffffff',N'2',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085806', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085806', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'70003',N'33A001',N'7',N'高拍仪',N'高拍仪',N'0',N'#1677FF-#ffffff',N'2',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085806', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085806', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'110001',N'33A001',N'11',N'智能开关',N'智能开关',N'0',N'#1677FF-#ffffff',N'2',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085806', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085806', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'110002',N'33A001',N'11',N'环境一体机',N'环境一体机',N'0',N'#1677FF-#ffffff',N'2',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085806', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085806', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'110003',N'33A001',N'11',N'温湿度探头',N'温湿度探头',N'0',N'#1677FF-#ffffff',N'2',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085806', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085806', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'110004',N'33A001',N'11',N'AI摄像头',N'AI摄像头',N'0',N'#1677FF-#ffffff',N'2',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085806', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085806', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'110005',N'33A001',N'11',N'IPC摄像头',N'IPC摄像头',N'0',N'#1677FF-#ffffff',N'2',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085806', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085806', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'110006',N'33A001',N'11',N'门禁设备',N'门禁设备',N'0',N'#1677FF-#ffffff',N'2',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085806', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085806', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'20001',N'33A001',N'2',N'轨道',N'轨道',N'0',N'#1677FF-#ffffff',N'2',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085806', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085806', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'20002',N'33A001',N'2',N'开盖机',N'开盖机',N'0',N'#1677FF-#ffffff',N'2',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085806', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085807', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'30001',N'33A001',N'3',N'生物安全柜',N'生安',N'1',N'#1677FF-#ffffff',N'2',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085807', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085807', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'30002',N'33A001',N'3',N'离心机',N'生安',N'1',N'#1677FF-#ffffff',N'2',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085807', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085807', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'120001',N'33A001',N'12',N'紫外线灯',N'生安',N'1',N'#1677FF-#ffffff',N'2',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085807', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085807', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'130001',N'33A001',N'13',N'培育箱',N'培育箱',N'0',N'#1677FF-#ffffff',N'2',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085807', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085807', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'130002',N'33A001',N'13',N'孵育箱',N'孵育箱',N'0',N'#1677FF-#ffffff',N'2',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085807', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085807', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'130003',N'33A001',N'13',N'金属浴',N'金属浴',N'0',N'#1677FF-#ffffff',N'2',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085807', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085807', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'50001',N'33A001',N'5',N'冰箱',N'冰箱',N'0',N'#1677FF-#ffffff',N'2',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085807', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085807', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'100001',N'33A001',N'10',N'电梯',N'电梯',N'0',N'#1677FF-#ffffff',N'2',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085807', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085807', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'100002',N'33A001',N'10',N'高压灭菌锅',N'高压灭菌锅',N'1',N'#1677FF-#ffffff',N'2',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085807', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085807', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'100003',N'33A001',N'10',N'气瓶',N'气瓶',N'0',N'#1677FF-#ffffff',N'2',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085808', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085808', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'140001',N'33A001',N'14',N'切片机',N'切片机',N'0',N'#1677FF-#ffffff',N'2',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085808', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085808', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'140002',N'33A001',N'14',N'甩片机',N'甩片机',N'0',N'#1677FF-#ffffff',N'2',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085808', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085808', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'140003',N'33A001',N'14',N'细菌培养皿',N'细菌培养皿',N'0',N'#1677FF-#ffffff',N'2',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085808', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085808', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'150001',N'33A001',N'15',N'洗眼装置',N'洗眼装置',N'1',N'#1677FF-#ffffff',N'2',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085808', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085808', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'150002',N'33A001',N'15',N'冲淋装置',N'冲淋装置',N'1',N'#1677FF-#ffffff',N'2',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085808', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085808', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'160001',N'33A001',N'16',N'纯化制水机',N'纯化制水机',N'0',N'#1677FF-#ffffff',N'2',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085808', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085808', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
INTO "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  ("CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON") VALUES(N'170001',N'33A001',N'17',N'制冰机',N'制冰机',N'0',N'#1677FF-#ffffff',N'2',N'1',N'初始化数据',to_timestamp('2025-07-07 10:25:04.085808', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 10:25:04.085808', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化')  
SELECT 1 FROM DUAL


2025-07-07 10:25:05.798 +08:00 [INF] 【SQL执行耗时:350.7026ms】

[Sql]: SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON"  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "CLASS_ID" = :ClassId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::ClassId0 [Value]:1 [Type]:String    

2025-07-07 10:25:06.196 +08:00 [INF] 【SQL执行耗时:348.3436ms】

[Sql]:SELECT "CLASS_ARCHIVES_ID" AS "CLASSARCHIVESID","HOSPITAL_ID" AS "HOSPITALID","EQP_CLASS_ID" AS "EQPCLASSID","EQP_ARCHIVES_ID" AS "EQPARCHIVESID","REMARK" AS "REMARK","LAST_MTIME" AS "LAST_MTIME","LAST_MPERSON" AS "LAST_MPERSON","FIRST_RTIME" AS "FIRST_RTIME","FIRST_RPERSON" AS "FIRST_RPERSON" FROM "XH_OA"."EMS_EQP_CLASS_ARCHIVES"  WHERE   "EQP_ARCHIVES_ID" = '1'   

2025-07-07 10:25:06.273 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetEquipmentArchivesLinkByClassId/1 responded 200 in 5471.2051 ms
2025-07-07 10:25:06.275 +08:00 [INF] 【接口超时阀值预警】 [22621808d6778c29d6c31eb03fd261a7]接口/api/EquipmentClassNew/GetEquipmentArchivesLinkByClassId/1,耗时:[5478]毫秒
2025-07-07 10:25:10.134 +08:00 [INF] 【SQL执行耗时:373.4525ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT" 

2025-07-07 10:25:10.541 +08:00 [INF] 【SQL执行耗时:356.0651ms】

[Sql]:SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON" FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "HOSPITAL_ID" = :HospitalId0 ) 
[Pars]:
[Name]::HospitalId0 [Value]:33A001 [Type]:String    

2025-07-07 10:25:36.797 +08:00 [ERR] 未处理的异常::Autofac.Core.DependencyResolutionException: An exception was thrown while activating XH.H82.Services.EquipmentClassNew.EquipmentClassService.
 ---> Autofac.Core.DependencyResolutionException: An exception was thrown while invoking the constructor 'Void .ctor(Microsoft.AspNetCore.Http.IHttpContextAccessor, H.BASE.SqlSugarInfra.Uow.ISqlSugarUow`1[XH.H82.Models.SugarDbContext.SugarDbContext_Master])' on type 'EquipmentClassService'.
 ---> System.NullReferenceException: Object reference not set to an instance of an object.
   at SqlSugar.NavigatManager`1.ExecuteByLay(Expression expression, List`1 list, Func`2 selector)
   at SqlSugar.NavigatManager`1.ExecuteByLay(Int32 i, Expression item)
   at SqlSugar.NavigatManager`1.Execute()
   at SqlSugar.QueryableProvider`1._InitNavigat[TResult](List`1 result)
   at SqlSugar.QueryableProvider`1._ToList[TResult]()
   at SqlSugar.QueryableProvider`1.ToList()
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService.GetEquipmentClassDictByCondition(Expression`1 filter) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 37
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService.GetEquipmentClassDict() in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 55
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService..ctor(IHttpContextAccessor httpContextAccessor, ISqlSugarUow`1 dbContext) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 29
   at lambda_method150(Closure , Object[] )
   at Autofac.Core.Activators.Reflection.BoundConstructor.Instantiate()
   --- End of inner exception stack trace ---
   at Autofac.Core.Activators.Reflection.BoundConstructor.Instantiate()
   at Autofac.Core.Activators.Reflection.ReflectionActivator.<>c__DisplayClass12_0.<UseSingleConstructorActivation>b__0(ResolveRequestContext ctxt, Action`1 next)
   at Autofac.Core.Resolving.Middleware.DelegateMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.DisposalTrackingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Extras.DynamicProxy.RegistrationExtensions.<>c__DisplayClass8_0`3.<EnableInterfaceInterceptors>b__1(ResolveRequestContext ctxt, Action`1 next)
   at Autofac.Core.Resolving.Middleware.DelegateMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.ActivatorErrorHandlingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   --- End of inner exception stack trace ---
   at Autofac.Core.Resolving.Middleware.ActivatorErrorHandlingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Pipeline.ResolvePipeline.Invoke(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.RegistrationPipelineInvokeMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.SharingMiddleware.<>c__DisplayClass5_0.<Execute>b__0()
   at Autofac.Core.Lifetime.LifetimeScope.CreateSharedInstance(Guid id, Func`1 creator)
   at Autofac.Core.Lifetime.LifetimeScope.CreateSharedInstance(Guid primaryId, Nullable`1 qualifyingId, Func`1 creator)
   at Autofac.Core.Resolving.Middleware.SharingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.ScopeSelectionMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.CircularDependencyDetectorMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Pipeline.ResolvePipeline.Invoke(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.ResolveOperation.GetOrCreateInstance(ISharingLifetimeScope currentOperationScope, ResolveRequest request)
   at Autofac.Core.Resolving.ResolveOperation.ExecuteOperation(ResolveRequest request)
   at Autofac.Core.Resolving.ResolveOperation.Execute(ResolveRequest request)
   at Autofac.Core.Lifetime.LifetimeScope.ResolveComponent(ResolveRequest request)
   at Autofac.ResolutionExtensions.TryResolveService(IComponentContext context, Service service, IEnumerable`1 parameters, Object& instance)
   at Autofac.ResolutionExtensions.ResolveOptionalService(IComponentContext context, Service service, IEnumerable`1 parameters)
   at Autofac.ResolutionExtensions.ResolveOptional(IComponentContext context, Type serviceType, IEnumerable`1 parameters)
   at Autofac.ResolutionExtensions.ResolveOptional(IComponentContext context, Type serviceType)
   at Autofac.Extensions.DependencyInjection.AutofacServiceProvider.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ActivatorUtilities.GetService(IServiceProvider sp, Type type, Type requiredBy, Boolean isDefaultParameterRequired)
   at lambda_method911(Closure , IServiceProvider , Object[] )
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerActivatorProvider.<>c__DisplayClass7_0.<CreateActivator>b__0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)Autofac.Core.DependencyResolutionException: An exception was thrown while invoking the constructor 'Void .ctor(Microsoft.AspNetCore.Http.IHttpContextAccessor, H.BASE.SqlSugarInfra.Uow.ISqlSugarUow`1[XH.H82.Models.SugarDbContext.SugarDbContext_Master])' on type 'EquipmentClassService'.
 ---> System.NullReferenceException: Object reference not set to an instance of an object.
   at SqlSugar.NavigatManager`1.ExecuteByLay(Expression expression, List`1 list, Func`2 selector)
   at SqlSugar.NavigatManager`1.ExecuteByLay(Int32 i, Expression item)
   at SqlSugar.NavigatManager`1.Execute()
   at SqlSugar.QueryableProvider`1._InitNavigat[TResult](List`1 result)
   at SqlSugar.QueryableProvider`1._ToList[TResult]()
   at SqlSugar.QueryableProvider`1.ToList()
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService.GetEquipmentClassDictByCondition(Expression`1 filter) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 37
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService.GetEquipmentClassDict() in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 55
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService..ctor(IHttpContextAccessor httpContextAccessor, ISqlSugarUow`1 dbContext) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 29
   at lambda_method150(Closure , Object[] )
   at Autofac.Core.Activators.Reflection.BoundConstructor.Instantiate()
   --- End of inner exception stack trace ---
   at Autofac.Core.Activators.Reflection.BoundConstructor.Instantiate()
   at Autofac.Core.Activators.Reflection.ReflectionActivator.<>c__DisplayClass12_0.<UseSingleConstructorActivation>b__0(ResolveRequestContext ctxt, Action`1 next)
   at Autofac.Core.Resolving.Middleware.DelegateMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.DisposalTrackingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Extras.DynamicProxy.RegistrationExtensions.<>c__DisplayClass8_0`3.<EnableInterfaceInterceptors>b__1(ResolveRequestContext ctxt, Action`1 next)
   at Autofac.Core.Resolving.Middleware.DelegateMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.ActivatorErrorHandlingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
2025-07-07 10:25:36.805 +08:00 [ERR] HTTP GET /api/EquipmentClassNew/GetEquipmentArchivesLinkByClassId/1 responded 500 in 27086.6532 ms
2025-07-07 10:25:36.805 +08:00 [INF] 【接口超时阀值预警】 [a3d18d5b948f135948d6a3b17c734d31]接口/api/EquipmentClassNew/GetEquipmentArchivesLinkByClassId/1,耗时:[27087]毫秒
2025-07-07 10:26:03.742 +08:00 [INF] 【SQL执行耗时:363.5728ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT" 

2025-07-07 10:26:04.122 +08:00 [INF] 【SQL执行耗时:340.5011ms】

[Sql]:SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON" FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "HOSPITAL_ID" = :HospitalId0 ) 
[Pars]:
[Name]::HospitalId0 [Value]:33A001 [Type]:String    

2025-07-07 10:26:30.028 +08:00 [ERR] 未处理的异常::Autofac.Core.DependencyResolutionException: An exception was thrown while activating XH.H82.Services.EquipmentClassNew.EquipmentClassService.
 ---> Autofac.Core.DependencyResolutionException: An exception was thrown while invoking the constructor 'Void .ctor(Microsoft.AspNetCore.Http.IHttpContextAccessor, H.BASE.SqlSugarInfra.Uow.ISqlSugarUow`1[XH.H82.Models.SugarDbContext.SugarDbContext_Master])' on type 'EquipmentClassService'.
 ---> System.NullReferenceException: Object reference not set to an instance of an object.
   at SqlSugar.NavigatManager`1.ExecuteByLay(Expression expression, List`1 list, Func`2 selector)
   at SqlSugar.NavigatManager`1.ExecuteByLay(Int32 i, Expression item)
   at SqlSugar.NavigatManager`1.Execute()
   at SqlSugar.QueryableProvider`1._InitNavigat[TResult](List`1 result)
   at SqlSugar.QueryableProvider`1._ToList[TResult]()
   at SqlSugar.QueryableProvider`1.ToList()
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService.GetEquipmentClassDictByCondition(Expression`1 filter) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 37
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService.GetEquipmentClassDict() in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 55
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService..ctor(IHttpContextAccessor httpContextAccessor, ISqlSugarUow`1 dbContext) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 29
   at lambda_method150(Closure , Object[] )
   at Autofac.Core.Activators.Reflection.BoundConstructor.Instantiate()
   --- End of inner exception stack trace ---
   at Autofac.Core.Activators.Reflection.BoundConstructor.Instantiate()
   at Autofac.Core.Activators.Reflection.ReflectionActivator.<>c__DisplayClass12_0.<UseSingleConstructorActivation>b__0(ResolveRequestContext ctxt, Action`1 next)
   at Autofac.Core.Resolving.Middleware.DelegateMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.DisposalTrackingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Extras.DynamicProxy.RegistrationExtensions.<>c__DisplayClass8_0`3.<EnableInterfaceInterceptors>b__1(ResolveRequestContext ctxt, Action`1 next)
   at Autofac.Core.Resolving.Middleware.DelegateMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.ActivatorErrorHandlingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   --- End of inner exception stack trace ---
   at Autofac.Core.Resolving.Middleware.ActivatorErrorHandlingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Pipeline.ResolvePipeline.Invoke(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.RegistrationPipelineInvokeMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.SharingMiddleware.<>c__DisplayClass5_0.<Execute>b__0()
   at Autofac.Core.Lifetime.LifetimeScope.CreateSharedInstance(Guid id, Func`1 creator)
   at Autofac.Core.Lifetime.LifetimeScope.CreateSharedInstance(Guid primaryId, Nullable`1 qualifyingId, Func`1 creator)
   at Autofac.Core.Resolving.Middleware.SharingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.ScopeSelectionMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.CircularDependencyDetectorMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Pipeline.ResolvePipeline.Invoke(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.ResolveOperation.GetOrCreateInstance(ISharingLifetimeScope currentOperationScope, ResolveRequest request)
   at Autofac.Core.Resolving.ResolveOperation.ExecuteOperation(ResolveRequest request)
   at Autofac.Core.Resolving.ResolveOperation.Execute(ResolveRequest request)
   at Autofac.Core.Lifetime.LifetimeScope.ResolveComponent(ResolveRequest request)
   at Autofac.ResolutionExtensions.TryResolveService(IComponentContext context, Service service, IEnumerable`1 parameters, Object& instance)
   at Autofac.ResolutionExtensions.ResolveOptionalService(IComponentContext context, Service service, IEnumerable`1 parameters)
   at Autofac.ResolutionExtensions.ResolveOptional(IComponentContext context, Type serviceType, IEnumerable`1 parameters)
   at Autofac.ResolutionExtensions.ResolveOptional(IComponentContext context, Type serviceType)
   at Autofac.Extensions.DependencyInjection.AutofacServiceProvider.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ActivatorUtilities.GetService(IServiceProvider sp, Type type, Type requiredBy, Boolean isDefaultParameterRequired)
   at lambda_method911(Closure , IServiceProvider , Object[] )
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerActivatorProvider.<>c__DisplayClass7_0.<CreateActivator>b__0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)Autofac.Core.DependencyResolutionException: An exception was thrown while invoking the constructor 'Void .ctor(Microsoft.AspNetCore.Http.IHttpContextAccessor, H.BASE.SqlSugarInfra.Uow.ISqlSugarUow`1[XH.H82.Models.SugarDbContext.SugarDbContext_Master])' on type 'EquipmentClassService'.
 ---> System.NullReferenceException: Object reference not set to an instance of an object.
   at SqlSugar.NavigatManager`1.ExecuteByLay(Expression expression, List`1 list, Func`2 selector)
   at SqlSugar.NavigatManager`1.ExecuteByLay(Int32 i, Expression item)
   at SqlSugar.NavigatManager`1.Execute()
   at SqlSugar.QueryableProvider`1._InitNavigat[TResult](List`1 result)
   at SqlSugar.QueryableProvider`1._ToList[TResult]()
   at SqlSugar.QueryableProvider`1.ToList()
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService.GetEquipmentClassDictByCondition(Expression`1 filter) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 37
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService.GetEquipmentClassDict() in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 55
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService..ctor(IHttpContextAccessor httpContextAccessor, ISqlSugarUow`1 dbContext) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 29
   at lambda_method150(Closure , Object[] )
   at Autofac.Core.Activators.Reflection.BoundConstructor.Instantiate()
   --- End of inner exception stack trace ---
   at Autofac.Core.Activators.Reflection.BoundConstructor.Instantiate()
   at Autofac.Core.Activators.Reflection.ReflectionActivator.<>c__DisplayClass12_0.<UseSingleConstructorActivation>b__0(ResolveRequestContext ctxt, Action`1 next)
   at Autofac.Core.Resolving.Middleware.DelegateMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.DisposalTrackingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Extras.DynamicProxy.RegistrationExtensions.<>c__DisplayClass8_0`3.<EnableInterfaceInterceptors>b__1(ResolveRequestContext ctxt, Action`1 next)
   at Autofac.Core.Resolving.Middleware.DelegateMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.ActivatorErrorHandlingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
2025-07-07 10:26:30.035 +08:00 [ERR] HTTP GET /api/EquipmentClassNew/GetEquipmentArchivesLinkByClassId/1 responded 500 in 26700.0958 ms
2025-07-07 10:26:30.035 +08:00 [INF] 【接口超时阀值预警】 [983ae10f7665ec63be22ff2517781296]接口/api/EquipmentClassNew/GetEquipmentArchivesLinkByClassId/1,耗时:[26700]毫秒
2025-07-07 10:26:42.140 +08:00 [INF] 【SQL执行耗时:360.3107ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT" 

2025-07-07 10:27:35.006 +08:00 [INF] ==>App Start..2025-07-07 10:27:34
2025-07-07 10:27:35.197 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-07 10:27:35.200 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-07 10:27:36.639 +08:00 [INF] ==>基础连接请求完成.
2025-07-07 10:27:37.157 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-07 10:27:37.514 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-07 10:27:37.821 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-07 10:27:37.823 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-07 10:27:38.159 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-07 10:27:38.622 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-07 10:27:38.726 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-07 10:27:39.155 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-07 10:27:39.155 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-07 10:27:40.038 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-07 10:27:42.755 +08:00 [INF] ==>初始化完成..
2025-07-07 10:27:42.779 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-07 10:27:42.782 +08:00 [INF] 设备启用任务
2025-07-07 10:27:42.783 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-07 10:27:43.197 +08:00 [INF] 【SQL执行耗时:395.8485ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-07 10:27:43.332 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-07 10:27:43.343 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-07 10:27:43.344 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-07 10:27:43.344 +08:00 [INF] Hosting environment: Development
2025-07-07 10:27:43.345 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-07 10:27:48.770 +08:00 [INF] 【SQL执行耗时:349.6334ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT" 

2025-07-07 10:27:51.164 +08:00 [INF] 【SQL执行耗时:398.4177ms】

[Sql]:SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON" FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "HOSPITAL_ID" = :HospitalId0 ) 
[Pars]:
[Name]::HospitalId0 [Value]:33A001 [Type]:String    

2025-07-07 10:27:53.637 +08:00 [ERR] 未处理的异常::Autofac.Core.DependencyResolutionException: An exception was thrown while activating XH.H82.Services.EquipmentClassNew.EquipmentClassService.
 ---> Autofac.Core.DependencyResolutionException: An exception was thrown while invoking the constructor 'Void .ctor(Microsoft.AspNetCore.Http.IHttpContextAccessor, H.BASE.SqlSugarInfra.Uow.ISqlSugarUow`1[XH.H82.Models.SugarDbContext.SugarDbContext_Master])' on type 'EquipmentClassService'.
 ---> System.NullReferenceException: Object reference not set to an instance of an object.
   at SqlSugar.NavigatManager`1.ExecuteByLay(Expression expression, List`1 list, Func`2 selector)
   at SqlSugar.NavigatManager`1.ExecuteByLay(Int32 i, Expression item)
   at SqlSugar.NavigatManager`1.Execute()
   at SqlSugar.QueryableProvider`1._InitNavigat[TResult](List`1 result)
   at SqlSugar.QueryableProvider`1._ToList[TResult]()
   at SqlSugar.QueryableProvider`1.ToList()
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService.GetEquipmentClassDictByCondition(Expression`1 filter) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 37
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService.GetEquipmentClassDict() in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 55
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService..ctor(IHttpContextAccessor httpContextAccessor, ISqlSugarUow`1 dbContext) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 29
   at lambda_method150(Closure , Object[] )
   at Autofac.Core.Activators.Reflection.BoundConstructor.Instantiate()
   --- End of inner exception stack trace ---
   at Autofac.Core.Activators.Reflection.BoundConstructor.Instantiate()
   at Autofac.Core.Activators.Reflection.ReflectionActivator.<>c__DisplayClass12_0.<UseSingleConstructorActivation>b__0(ResolveRequestContext ctxt, Action`1 next)
   at Autofac.Core.Resolving.Middleware.DelegateMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.DisposalTrackingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Extras.DynamicProxy.RegistrationExtensions.<>c__DisplayClass8_0`3.<EnableInterfaceInterceptors>b__1(ResolveRequestContext ctxt, Action`1 next)
   at Autofac.Core.Resolving.Middleware.DelegateMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.ActivatorErrorHandlingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   --- End of inner exception stack trace ---
   at Autofac.Core.Resolving.Middleware.ActivatorErrorHandlingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Pipeline.ResolvePipeline.Invoke(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.RegistrationPipelineInvokeMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.SharingMiddleware.<>c__DisplayClass5_0.<Execute>b__0()
   at Autofac.Core.Lifetime.LifetimeScope.CreateSharedInstance(Guid id, Func`1 creator)
   at Autofac.Core.Lifetime.LifetimeScope.CreateSharedInstance(Guid primaryId, Nullable`1 qualifyingId, Func`1 creator)
   at Autofac.Core.Resolving.Middleware.SharingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.ScopeSelectionMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.CircularDependencyDetectorMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Pipeline.ResolvePipeline.Invoke(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.ResolveOperation.GetOrCreateInstance(ISharingLifetimeScope currentOperationScope, ResolveRequest request)
   at Autofac.Core.Resolving.ResolveOperation.ExecuteOperation(ResolveRequest request)
   at Autofac.Core.Resolving.ResolveOperation.Execute(ResolveRequest request)
   at Autofac.Core.Lifetime.LifetimeScope.ResolveComponent(ResolveRequest request)
   at Autofac.ResolutionExtensions.TryResolveService(IComponentContext context, Service service, IEnumerable`1 parameters, Object& instance)
   at Autofac.ResolutionExtensions.ResolveOptionalService(IComponentContext context, Service service, IEnumerable`1 parameters)
   at Autofac.ResolutionExtensions.ResolveOptional(IComponentContext context, Type serviceType, IEnumerable`1 parameters)
   at Autofac.ResolutionExtensions.ResolveOptional(IComponentContext context, Type serviceType)
   at Autofac.Extensions.DependencyInjection.AutofacServiceProvider.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ActivatorUtilities.GetService(IServiceProvider sp, Type type, Type requiredBy, Boolean isDefaultParameterRequired)
   at lambda_method911(Closure , IServiceProvider , Object[] )
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerActivatorProvider.<>c__DisplayClass7_0.<CreateActivator>b__0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)Autofac.Core.DependencyResolutionException: An exception was thrown while invoking the constructor 'Void .ctor(Microsoft.AspNetCore.Http.IHttpContextAccessor, H.BASE.SqlSugarInfra.Uow.ISqlSugarUow`1[XH.H82.Models.SugarDbContext.SugarDbContext_Master])' on type 'EquipmentClassService'.
 ---> System.NullReferenceException: Object reference not set to an instance of an object.
   at SqlSugar.NavigatManager`1.ExecuteByLay(Expression expression, List`1 list, Func`2 selector)
   at SqlSugar.NavigatManager`1.ExecuteByLay(Int32 i, Expression item)
   at SqlSugar.NavigatManager`1.Execute()
   at SqlSugar.QueryableProvider`1._InitNavigat[TResult](List`1 result)
   at SqlSugar.QueryableProvider`1._ToList[TResult]()
   at SqlSugar.QueryableProvider`1.ToList()
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService.GetEquipmentClassDictByCondition(Expression`1 filter) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 37
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService.GetEquipmentClassDict() in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 55
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService..ctor(IHttpContextAccessor httpContextAccessor, ISqlSugarUow`1 dbContext) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 29
   at lambda_method150(Closure , Object[] )
   at Autofac.Core.Activators.Reflection.BoundConstructor.Instantiate()
   --- End of inner exception stack trace ---
   at Autofac.Core.Activators.Reflection.BoundConstructor.Instantiate()
   at Autofac.Core.Activators.Reflection.ReflectionActivator.<>c__DisplayClass12_0.<UseSingleConstructorActivation>b__0(ResolveRequestContext ctxt, Action`1 next)
   at Autofac.Core.Resolving.Middleware.DelegateMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.DisposalTrackingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Extras.DynamicProxy.RegistrationExtensions.<>c__DisplayClass8_0`3.<EnableInterfaceInterceptors>b__1(ResolveRequestContext ctxt, Action`1 next)
   at Autofac.Core.Resolving.Middleware.DelegateMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.ActivatorErrorHandlingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
2025-07-07 10:27:53.649 +08:00 [ERR] HTTP GET /api/EquipmentClassNew/GetEquipmentArchivesLinkByClassId/1 responded 500 in 7947.5494 ms
2025-07-07 10:27:53.654 +08:00 [INF] 【接口超时阀值预警】 [2ae4f990f79d0d4e76af48abe0a063ca]接口/api/EquipmentClassNew/GetEquipmentArchivesLinkByClassId/1,耗时:[7957]毫秒
2025-07-07 10:28:02.045 +08:00 [INF] 【SQL执行耗时:346.3771ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT" 

2025-07-07 10:28:02.412 +08:00 [INF] 【SQL执行耗时:329.6956ms】

[Sql]:SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON" FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "HOSPITAL_ID" = :HospitalId0 ) 
[Pars]:
[Name]::HospitalId0 [Value]:33A001 [Type]:String    

2025-07-07 10:28:19.876 +08:00 [ERR] 未处理的异常::Autofac.Core.DependencyResolutionException: An exception was thrown while activating XH.H82.Services.EquipmentClassNew.EquipmentClassService.
 ---> Autofac.Core.DependencyResolutionException: An exception was thrown while invoking the constructor 'Void .ctor(Microsoft.AspNetCore.Http.IHttpContextAccessor, H.BASE.SqlSugarInfra.Uow.ISqlSugarUow`1[XH.H82.Models.SugarDbContext.SugarDbContext_Master])' on type 'EquipmentClassService'.
 ---> System.NullReferenceException: Object reference not set to an instance of an object.
   at SqlSugar.NavigatManager`1.ExecuteByLay(Expression expression, List`1 list, Func`2 selector)
   at SqlSugar.NavigatManager`1.ExecuteByLay(Int32 i, Expression item)
   at SqlSugar.NavigatManager`1.Execute()
   at SqlSugar.QueryableProvider`1._InitNavigat[TResult](List`1 result)
   at SqlSugar.QueryableProvider`1._ToList[TResult]()
   at SqlSugar.QueryableProvider`1.ToList()
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService.GetEquipmentClassDictByCondition(Expression`1 filter) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 37
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService.GetEquipmentClassDict() in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 55
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService..ctor(IHttpContextAccessor httpContextAccessor, ISqlSugarUow`1 dbContext) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 29
   at lambda_method150(Closure , Object[] )
   at Autofac.Core.Activators.Reflection.BoundConstructor.Instantiate()
   --- End of inner exception stack trace ---
   at Autofac.Core.Activators.Reflection.BoundConstructor.Instantiate()
   at Autofac.Core.Activators.Reflection.ReflectionActivator.<>c__DisplayClass12_0.<UseSingleConstructorActivation>b__0(ResolveRequestContext ctxt, Action`1 next)
   at Autofac.Core.Resolving.Middleware.DelegateMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.DisposalTrackingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Extras.DynamicProxy.RegistrationExtensions.<>c__DisplayClass8_0`3.<EnableInterfaceInterceptors>b__1(ResolveRequestContext ctxt, Action`1 next)
   at Autofac.Core.Resolving.Middleware.DelegateMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.ActivatorErrorHandlingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   --- End of inner exception stack trace ---
   at Autofac.Core.Resolving.Middleware.ActivatorErrorHandlingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Pipeline.ResolvePipeline.Invoke(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.RegistrationPipelineInvokeMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.SharingMiddleware.<>c__DisplayClass5_0.<Execute>b__0()
   at Autofac.Core.Lifetime.LifetimeScope.CreateSharedInstance(Guid id, Func`1 creator)
   at Autofac.Core.Lifetime.LifetimeScope.CreateSharedInstance(Guid primaryId, Nullable`1 qualifyingId, Func`1 creator)
   at Autofac.Core.Resolving.Middleware.SharingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.ScopeSelectionMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.CircularDependencyDetectorMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Pipeline.ResolvePipeline.Invoke(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.ResolveOperation.GetOrCreateInstance(ISharingLifetimeScope currentOperationScope, ResolveRequest request)
   at Autofac.Core.Resolving.ResolveOperation.ExecuteOperation(ResolveRequest request)
   at Autofac.Core.Resolving.ResolveOperation.Execute(ResolveRequest request)
   at Autofac.Core.Lifetime.LifetimeScope.ResolveComponent(ResolveRequest request)
   at Autofac.ResolutionExtensions.TryResolveService(IComponentContext context, Service service, IEnumerable`1 parameters, Object& instance)
   at Autofac.ResolutionExtensions.ResolveOptionalService(IComponentContext context, Service service, IEnumerable`1 parameters)
   at Autofac.ResolutionExtensions.ResolveOptional(IComponentContext context, Type serviceType, IEnumerable`1 parameters)
   at Autofac.ResolutionExtensions.ResolveOptional(IComponentContext context, Type serviceType)
   at Autofac.Extensions.DependencyInjection.AutofacServiceProvider.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ActivatorUtilities.GetService(IServiceProvider sp, Type type, Type requiredBy, Boolean isDefaultParameterRequired)
   at lambda_method911(Closure , IServiceProvider , Object[] )
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerActivatorProvider.<>c__DisplayClass7_0.<CreateActivator>b__0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)Autofac.Core.DependencyResolutionException: An exception was thrown while invoking the constructor 'Void .ctor(Microsoft.AspNetCore.Http.IHttpContextAccessor, H.BASE.SqlSugarInfra.Uow.ISqlSugarUow`1[XH.H82.Models.SugarDbContext.SugarDbContext_Master])' on type 'EquipmentClassService'.
 ---> System.NullReferenceException: Object reference not set to an instance of an object.
   at SqlSugar.NavigatManager`1.ExecuteByLay(Expression expression, List`1 list, Func`2 selector)
   at SqlSugar.NavigatManager`1.ExecuteByLay(Int32 i, Expression item)
   at SqlSugar.NavigatManager`1.Execute()
   at SqlSugar.QueryableProvider`1._InitNavigat[TResult](List`1 result)
   at SqlSugar.QueryableProvider`1._ToList[TResult]()
   at SqlSugar.QueryableProvider`1.ToList()
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService.GetEquipmentClassDictByCondition(Expression`1 filter) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 37
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService.GetEquipmentClassDict() in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 55
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService..ctor(IHttpContextAccessor httpContextAccessor, ISqlSugarUow`1 dbContext) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 29
   at lambda_method150(Closure , Object[] )
   at Autofac.Core.Activators.Reflection.BoundConstructor.Instantiate()
   --- End of inner exception stack trace ---
   at Autofac.Core.Activators.Reflection.BoundConstructor.Instantiate()
   at Autofac.Core.Activators.Reflection.ReflectionActivator.<>c__DisplayClass12_0.<UseSingleConstructorActivation>b__0(ResolveRequestContext ctxt, Action`1 next)
   at Autofac.Core.Resolving.Middleware.DelegateMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.DisposalTrackingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Extras.DynamicProxy.RegistrationExtensions.<>c__DisplayClass8_0`3.<EnableInterfaceInterceptors>b__1(ResolveRequestContext ctxt, Action`1 next)
   at Autofac.Core.Resolving.Middleware.DelegateMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.ActivatorErrorHandlingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
2025-07-07 10:28:19.881 +08:00 [ERR] HTTP GET /api/EquipmentClassNew/GetEquipmentArchivesLinkByClassId/1 responded 500 in 18227.1355 ms
2025-07-07 10:28:19.882 +08:00 [INF] 【接口超时阀值预警】 [8f23fa08ecc39a48f4f108bdfb7ea0a1]接口/api/EquipmentClassNew/GetEquipmentArchivesLinkByClassId/1,耗时:[18227]毫秒
2025-07-07 10:28:21.797 +08:00 [INF] 【SQL执行耗时:349.304ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT" 

2025-07-07 10:28:22.196 +08:00 [INF] 【SQL执行耗时:361.3253ms】

[Sql]:SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON" FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "HOSPITAL_ID" = :HospitalId0 ) 
[Pars]:
[Name]::HospitalId0 [Value]:33A001 [Type]:String    

2025-07-07 10:28:32.161 +08:00 [ERR] 未处理的异常::Autofac.Core.DependencyResolutionException: An exception was thrown while activating XH.H82.Services.EquipmentClassNew.EquipmentClassService.
 ---> Autofac.Core.DependencyResolutionException: An exception was thrown while invoking the constructor 'Void .ctor(Microsoft.AspNetCore.Http.IHttpContextAccessor, H.BASE.SqlSugarInfra.Uow.ISqlSugarUow`1[XH.H82.Models.SugarDbContext.SugarDbContext_Master])' on type 'EquipmentClassService'.
 ---> System.NullReferenceException: Object reference not set to an instance of an object.
   at SqlSugar.NavigatManager`1.ExecuteByLay(Expression expression, List`1 list, Func`2 selector)
   at SqlSugar.NavigatManager`1.ExecuteByLay(Int32 i, Expression item)
   at SqlSugar.NavigatManager`1.Execute()
   at SqlSugar.QueryableProvider`1._InitNavigat[TResult](List`1 result)
   at SqlSugar.QueryableProvider`1._ToList[TResult]()
   at SqlSugar.QueryableProvider`1.ToList()
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService.GetEquipmentClassDictByCondition(Expression`1 filter) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 37
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService.GetEquipmentClassDict() in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 55
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService..ctor(IHttpContextAccessor httpContextAccessor, ISqlSugarUow`1 dbContext) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 29
   at lambda_method150(Closure , Object[] )
   at Autofac.Core.Activators.Reflection.BoundConstructor.Instantiate()
   --- End of inner exception stack trace ---
   at Autofac.Core.Activators.Reflection.BoundConstructor.Instantiate()
   at Autofac.Core.Activators.Reflection.ReflectionActivator.<>c__DisplayClass12_0.<UseSingleConstructorActivation>b__0(ResolveRequestContext ctxt, Action`1 next)
   at Autofac.Core.Resolving.Middleware.DelegateMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.DisposalTrackingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Extras.DynamicProxy.RegistrationExtensions.<>c__DisplayClass8_0`3.<EnableInterfaceInterceptors>b__1(ResolveRequestContext ctxt, Action`1 next)
   at Autofac.Core.Resolving.Middleware.DelegateMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.ActivatorErrorHandlingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   --- End of inner exception stack trace ---
   at Autofac.Core.Resolving.Middleware.ActivatorErrorHandlingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Pipeline.ResolvePipeline.Invoke(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.RegistrationPipelineInvokeMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.SharingMiddleware.<>c__DisplayClass5_0.<Execute>b__0()
   at Autofac.Core.Lifetime.LifetimeScope.CreateSharedInstance(Guid id, Func`1 creator)
   at Autofac.Core.Lifetime.LifetimeScope.CreateSharedInstance(Guid primaryId, Nullable`1 qualifyingId, Func`1 creator)
   at Autofac.Core.Resolving.Middleware.SharingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.ScopeSelectionMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.CircularDependencyDetectorMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Pipeline.ResolvePipeline.Invoke(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.ResolveOperation.GetOrCreateInstance(ISharingLifetimeScope currentOperationScope, ResolveRequest request)
   at Autofac.Core.Resolving.ResolveOperation.ExecuteOperation(ResolveRequest request)
   at Autofac.Core.Resolving.ResolveOperation.Execute(ResolveRequest request)
   at Autofac.Core.Lifetime.LifetimeScope.ResolveComponent(ResolveRequest request)
   at Autofac.ResolutionExtensions.TryResolveService(IComponentContext context, Service service, IEnumerable`1 parameters, Object& instance)
   at Autofac.ResolutionExtensions.ResolveOptionalService(IComponentContext context, Service service, IEnumerable`1 parameters)
   at Autofac.ResolutionExtensions.ResolveOptional(IComponentContext context, Type serviceType, IEnumerable`1 parameters)
   at Autofac.ResolutionExtensions.ResolveOptional(IComponentContext context, Type serviceType)
   at Autofac.Extensions.DependencyInjection.AutofacServiceProvider.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ActivatorUtilities.GetService(IServiceProvider sp, Type type, Type requiredBy, Boolean isDefaultParameterRequired)
   at lambda_method911(Closure , IServiceProvider , Object[] )
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerActivatorProvider.<>c__DisplayClass7_0.<CreateActivator>b__0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)Autofac.Core.DependencyResolutionException: An exception was thrown while invoking the constructor 'Void .ctor(Microsoft.AspNetCore.Http.IHttpContextAccessor, H.BASE.SqlSugarInfra.Uow.ISqlSugarUow`1[XH.H82.Models.SugarDbContext.SugarDbContext_Master])' on type 'EquipmentClassService'.
 ---> System.NullReferenceException: Object reference not set to an instance of an object.
   at SqlSugar.NavigatManager`1.ExecuteByLay(Expression expression, List`1 list, Func`2 selector)
   at SqlSugar.NavigatManager`1.ExecuteByLay(Int32 i, Expression item)
   at SqlSugar.NavigatManager`1.Execute()
   at SqlSugar.QueryableProvider`1._InitNavigat[TResult](List`1 result)
   at SqlSugar.QueryableProvider`1._ToList[TResult]()
   at SqlSugar.QueryableProvider`1.ToList()
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService.GetEquipmentClassDictByCondition(Expression`1 filter) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 37
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService.GetEquipmentClassDict() in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 55
   at XH.H82.Services.EquipmentClassNew.EquipmentClassService..ctor(IHttpContextAccessor httpContextAccessor, ISqlSugarUow`1 dbContext) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentClassNew\EquipmentClassService.cs:line 29
   at lambda_method150(Closure , Object[] )
   at Autofac.Core.Activators.Reflection.BoundConstructor.Instantiate()
   --- End of inner exception stack trace ---
   at Autofac.Core.Activators.Reflection.BoundConstructor.Instantiate()
   at Autofac.Core.Activators.Reflection.ReflectionActivator.<>c__DisplayClass12_0.<UseSingleConstructorActivation>b__0(ResolveRequestContext ctxt, Action`1 next)
   at Autofac.Core.Resolving.Middleware.DelegateMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.DisposalTrackingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Extras.DynamicProxy.RegistrationExtensions.<>c__DisplayClass8_0`3.<EnableInterfaceInterceptors>b__1(ResolveRequestContext ctxt, Action`1 next)
   at Autofac.Core.Resolving.Middleware.DelegateMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext ctxt)
   at Autofac.Core.Resolving.Middleware.ActivatorErrorHandlingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
2025-07-07 10:28:32.165 +08:00 [ERR] HTTP GET /api/EquipmentClassNew/GetEquipmentArchivesLinkByClassId/1 responded 500 in 10759.4243 ms
2025-07-07 10:28:32.165 +08:00 [INF] 【接口超时阀值预警】 [50699f97f49f8cb67431025f0b56650f]接口/api/EquipmentClassNew/GetEquipmentArchivesLinkByClassId/1,耗时:[10759]毫秒
2025-07-07 10:28:56.807 +08:00 [INF] ==>App Start..2025-07-07 10:28:56
2025-07-07 10:28:56.970 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-07 10:28:56.973 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-07 10:28:58.534 +08:00 [INF] ==>基础连接请求完成.
2025-07-07 10:28:58.907 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-07 10:28:59.240 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-07 10:28:59.553 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-07 10:28:59.555 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-07 10:28:59.982 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-07 10:29:00.319 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-07 10:29:00.381 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-07 10:29:00.803 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-07 10:29:00.803 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-07 10:29:01.775 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-07 10:29:10.400 +08:00 [INF] ==>初始化完成..
2025-07-07 10:29:10.421 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-07 10:29:10.422 +08:00 [INF] 设备启用任务
2025-07-07 10:29:10.422 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-07 10:29:10.777 +08:00 [INF] 【SQL执行耗时:336.3848ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-07 10:29:10.925 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-07 10:29:10.938 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-07 10:29:10.939 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-07 10:29:10.939 +08:00 [INF] Hosting environment: Development
2025-07-07 10:29:10.939 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-07 10:29:22.447 +08:00 [INF] 【SQL执行耗时:342.5297ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT" 

2025-07-07 10:29:22.860 +08:00 [INF] 【SQL执行耗时:335.1252ms】

[Sql]:SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON" FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "HOSPITAL_ID" = :HospitalId0 ) 
[Pars]:
[Name]::HospitalId0 [Value]:33A001 [Type]:String    

2025-07-07 10:29:23.276 +08:00 [INF] 【SQL执行耗时:336.5034ms】

[Sql]:SELECT "CLASS_ARCHIVES_ID" AS "CLASSARCHIVESID","HOSPITAL_ID" AS "HOSPITALID","EQP_CLASS_ID" AS "EQPCLASSID","EQP_ARCHIVES_ID" AS "EQPARCHIVESID","REMARK" AS "REMARK","LAST_MTIME" AS "LAST_MTIME","LAST_MPERSON" AS "LAST_MPERSON","FIRST_RTIME" AS "FIRST_RTIME","FIRST_RPERSON" AS "FIRST_RPERSON" FROM "XH_OA"."EMS_EQP_CLASS_ARCHIVES"  WHERE   "EQP_ARCHIVES_ID" IN ('H82CLASSJZL','H82CLASSXXL','H82CLASSFZL','H82CLASSGYL','1','4','6','7','11','2','3','12','13','5','10','14','15','16','17','10001','10002','10003','60001','60002','40001','40002','70001','70002','70003','110001','110002','110003','110004','110005','110006','20001','20002','30001','30002','120001','130001','130002','130003','50001','100001','100002','100003','140001','140002','140003','150001','150002','160001','170001')   

2025-07-07 10:29:26.251 +08:00 [INF] 【SQL执行耗时:335.9142ms】

[Sql]: SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON"  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "CLASS_ID" = :ClassId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::ClassId0 [Value]:1 [Type]:String    

2025-07-07 10:29:26.667 +08:00 [INF] 【SQL执行耗时:380.2408ms】

[Sql]:SELECT "CLASS_ARCHIVES_ID" AS "CLASSARCHIVESID","HOSPITAL_ID" AS "HOSPITALID","EQP_CLASS_ID" AS "EQPCLASSID","EQP_ARCHIVES_ID" AS "EQPARCHIVESID","REMARK" AS "REMARK","LAST_MTIME" AS "LAST_MTIME","LAST_MPERSON" AS "LAST_MPERSON","FIRST_RTIME" AS "FIRST_RTIME","FIRST_RPERSON" AS "FIRST_RPERSON" FROM "XH_OA"."EMS_EQP_CLASS_ARCHIVES"  WHERE   "EQP_ARCHIVES_ID" = '1'   

2025-07-07 10:29:26.744 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetEquipmentArchivesLinkByClassId/1 responded 200 in 7507.6044 ms
2025-07-07 10:29:26.747 +08:00 [INF] 【接口超时阀值预警】 [ad32b1b6482fd502dd66c6c77a1c7f65]接口/api/EquipmentClassNew/GetEquipmentArchivesLinkByClassId/1,耗时:[7515]毫秒
2025-07-07 10:29:29.404 +08:00 [INF] 【SQL执行耗时:354.5506ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT" 

2025-07-07 10:29:29.789 +08:00 [INF] 【SQL执行耗时:340.1418ms】

[Sql]:SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON" FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "HOSPITAL_ID" = :HospitalId0 ) 
[Pars]:
[Name]::HospitalId0 [Value]:33A001 [Type]:String    

2025-07-07 10:29:30.231 +08:00 [INF] 【SQL执行耗时:371.6259ms】

[Sql]:SELECT "CLASS_ARCHIVES_ID" AS "CLASSARCHIVESID","HOSPITAL_ID" AS "HOSPITALID","EQP_CLASS_ID" AS "EQPCLASSID","EQP_ARCHIVES_ID" AS "EQPARCHIVESID","REMARK" AS "REMARK","LAST_MTIME" AS "LAST_MTIME","LAST_MPERSON" AS "LAST_MPERSON","FIRST_RTIME" AS "FIRST_RTIME","FIRST_RPERSON" AS "FIRST_RPERSON" FROM "XH_OA"."EMS_EQP_CLASS_ARCHIVES"  WHERE   "EQP_ARCHIVES_ID" IN ('H82CLASSJZL','H82CLASSXXL','H82CLASSFZL','H82CLASSGYL','1','4','6','7','11','2','3','12','13','5','10','14','15','16','17','10001','10002','10003','60001','60002','40001','40002','70001','70002','70003','110001','110002','110003','110004','110005','110006','20001','20002','30001','30002','120001','130001','130002','130003','50001','100001','100002','100003','140001','140002','140003','150001','150002','160001','170001')   

2025-07-07 10:29:30.614 +08:00 [INF] 【SQL执行耗时:341.5154ms】

[Sql]: SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON"  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "CLASS_ID" = :ClassId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::ClassId0 [Value]:1 [Type]:String    

2025-07-07 10:29:31.004 +08:00 [INF] 【SQL执行耗时:354.4858ms】

[Sql]:SELECT "CLASS_ARCHIVES_ID" AS "CLASSARCHIVESID","HOSPITAL_ID" AS "HOSPITALID","EQP_CLASS_ID" AS "EQPCLASSID","EQP_ARCHIVES_ID" AS "EQPARCHIVESID","REMARK" AS "REMARK","LAST_MTIME" AS "LAST_MTIME","LAST_MPERSON" AS "LAST_MPERSON","FIRST_RTIME" AS "FIRST_RTIME","FIRST_RPERSON" AS "FIRST_RPERSON" FROM "XH_OA"."EMS_EQP_CLASS_ARCHIVES"  WHERE   "EQP_ARCHIVES_ID" = '1'   

2025-07-07 10:29:31.041 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetEquipmentArchivesLinkByClassId/1 responded 200 in 2035.6886 ms
2025-07-07 10:29:31.041 +08:00 [INF] 【接口超时阀值预警】 [569063186cf8f8455b243ba4e916cbf4]接口/api/EquipmentClassNew/GetEquipmentArchivesLinkByClassId/1,耗时:[2036]毫秒
2025-07-07 10:29:32.792 +08:00 [INF] 【SQL执行耗时:348.968ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT" 

2025-07-07 10:29:33.194 +08:00 [INF] 【SQL执行耗时:365.1563ms】

[Sql]:SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON" FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "HOSPITAL_ID" = :HospitalId0 ) 
[Pars]:
[Name]::HospitalId0 [Value]:33A001 [Type]:String    

2025-07-07 10:29:33.614 +08:00 [INF] 【SQL执行耗时:344.4018ms】

[Sql]:SELECT "CLASS_ARCHIVES_ID" AS "CLASSARCHIVESID","HOSPITAL_ID" AS "HOSPITALID","EQP_CLASS_ID" AS "EQPCLASSID","EQP_ARCHIVES_ID" AS "EQPARCHIVESID","REMARK" AS "REMARK","LAST_MTIME" AS "LAST_MTIME","LAST_MPERSON" AS "LAST_MPERSON","FIRST_RTIME" AS "FIRST_RTIME","FIRST_RPERSON" AS "FIRST_RPERSON" FROM "XH_OA"."EMS_EQP_CLASS_ARCHIVES"  WHERE   "EQP_ARCHIVES_ID" IN ('H82CLASSJZL','H82CLASSXXL','H82CLASSFZL','H82CLASSGYL','1','4','6','7','11','2','3','12','13','5','10','14','15','16','17','10001','10002','10003','60001','60002','40001','40002','70001','70002','70003','110001','110002','110003','110004','110005','110006','20001','20002','30001','30002','120001','130001','130002','130003','50001','100001','100002','100003','140001','140002','140003','150001','150002','160001','170001')   

2025-07-07 10:29:34.006 +08:00 [INF] 【SQL执行耗时:354.0127ms】

[Sql]: SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON"  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "CLASS_ID" = :ClassId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::ClassId0 [Value]:1 [Type]:String    

2025-07-07 10:29:34.406 +08:00 [INF] 【SQL执行耗时:363.5854ms】

[Sql]:SELECT "CLASS_ARCHIVES_ID" AS "CLASSARCHIVESID","HOSPITAL_ID" AS "HOSPITALID","EQP_CLASS_ID" AS "EQPCLASSID","EQP_ARCHIVES_ID" AS "EQPARCHIVESID","REMARK" AS "REMARK","LAST_MTIME" AS "LAST_MTIME","LAST_MPERSON" AS "LAST_MPERSON","FIRST_RTIME" AS "FIRST_RTIME","FIRST_RPERSON" AS "FIRST_RPERSON" FROM "XH_OA"."EMS_EQP_CLASS_ARCHIVES"  WHERE   "EQP_ARCHIVES_ID" = '1'   

2025-07-07 10:29:34.443 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetEquipmentArchivesLinkByClassId/1 responded 200 in 2039.1301 ms
2025-07-07 10:29:34.444 +08:00 [INF] 【接口超时阀值预警】 [a190358e7e6daefb5f6058370e6f00e5]接口/api/EquipmentClassNew/GetEquipmentArchivesLinkByClassId/1,耗时:[2039]毫秒
2025-07-07 10:29:39.819 +08:00 [INF] 【SQL执行耗时:350.5037ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT" 

2025-07-07 10:29:40.273 +08:00 [INF] 【SQL执行耗时:416.4473ms】

[Sql]:SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON" FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "HOSPITAL_ID" = :HospitalId0 ) 
[Pars]:
[Name]::HospitalId0 [Value]:33A001 [Type]:String    

2025-07-07 10:29:40.760 +08:00 [INF] 【SQL执行耗时:359.7235ms】

[Sql]:SELECT "CLASS_ARCHIVES_ID" AS "CLASSARCHIVESID","HOSPITAL_ID" AS "HOSPITALID","EQP_CLASS_ID" AS "EQPCLASSID","EQP_ARCHIVES_ID" AS "EQPARCHIVESID","REMARK" AS "REMARK","LAST_MTIME" AS "LAST_MTIME","LAST_MPERSON" AS "LAST_MPERSON","FIRST_RTIME" AS "FIRST_RTIME","FIRST_RPERSON" AS "FIRST_RPERSON" FROM "XH_OA"."EMS_EQP_CLASS_ARCHIVES"  WHERE   "EQP_ARCHIVES_ID" IN ('H82CLASSJZL','H82CLASSXXL','H82CLASSFZL','H82CLASSGYL','1','4','6','7','11','2','3','12','13','5','10','14','15','16','17','10001','10002','10003','60001','60002','40001','40002','70001','70002','70003','110001','110002','110003','110004','110005','110006','20001','20002','30001','30002','120001','130001','130002','130003','50001','100001','100002','100003','140001','140002','140003','150001','150002','160001','170001')   

2025-07-07 10:29:41.142 +08:00 [INF] 【SQL执行耗时:344.1644ms】

[Sql]: SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON"  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "CLASS_ID" = :ClassId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::ClassId0 [Value]:1 [Type]:String    

2025-07-07 10:29:41.916 +08:00 [INF] 【SQL执行耗时:738.4947ms】

[Sql]:SELECT "CLASS_ARCHIVES_ID" AS "CLASSARCHIVESID","HOSPITAL_ID" AS "HOSPITALID","EQP_CLASS_ID" AS "EQPCLASSID","EQP_ARCHIVES_ID" AS "EQPARCHIVESID","REMARK" AS "REMARK","LAST_MTIME" AS "LAST_MTIME","LAST_MPERSON" AS "LAST_MPERSON","FIRST_RTIME" AS "FIRST_RTIME","FIRST_RPERSON" AS "FIRST_RPERSON" FROM "XH_OA"."EMS_EQP_CLASS_ARCHIVES"  WHERE   "EQP_ARCHIVES_ID" = '1'   

2025-07-07 10:29:41.995 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetEquipmentArchivesLinkByClassId/1 responded 200 in 2564.0989 ms
2025-07-07 10:29:41.996 +08:00 [INF] 【接口超时阀值预警】 [2524856587f05d6b6c36417f711db1f8]接口/api/EquipmentClassNew/GetEquipmentArchivesLinkByClassId/1,耗时:[2564]毫秒
2025-07-07 10:29:54.856 +08:00 [INF] 【SQL执行耗时:412.8162ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT" 

2025-07-07 10:29:55.244 +08:00 [INF] 【SQL执行耗时:346.9901ms】

[Sql]:SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON" FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "HOSPITAL_ID" = :HospitalId0 ) 
[Pars]:
[Name]::HospitalId0 [Value]:33A001 [Type]:String    

2025-07-07 10:29:55.662 +08:00 [INF] 【SQL执行耗时:342.8919ms】

[Sql]:SELECT "CLASS_ARCHIVES_ID" AS "CLASSARCHIVESID","HOSPITAL_ID" AS "HOSPITALID","EQP_CLASS_ID" AS "EQPCLASSID","EQP_ARCHIVES_ID" AS "EQPARCHIVESID","REMARK" AS "REMARK","LAST_MTIME" AS "LAST_MTIME","LAST_MPERSON" AS "LAST_MPERSON","FIRST_RTIME" AS "FIRST_RTIME","FIRST_RPERSON" AS "FIRST_RPERSON" FROM "XH_OA"."EMS_EQP_CLASS_ARCHIVES"  WHERE   "EQP_ARCHIVES_ID" IN ('H82CLASSJZL','H82CLASSXXL','H82CLASSFZL','H82CLASSGYL','1','4','6','7','11','2','3','12','13','5','10','14','15','16','17','10001','10002','10003','60001','60002','40001','40002','70001','70002','70003','110001','110002','110003','110004','110005','110006','20001','20002','30001','30002','120001','130001','130002','130003','50001','100001','100002','100003','140001','140002','140003','150001','150002','160001','170001')   

2025-07-07 10:29:55.715 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetEquipmentClassDictTree responded 200 in 1311.6989 ms
2025-07-07 10:29:55.716 +08:00 [INF] 【接口超时阀值预警】 [c533afdbdeb3a33d22f27bfb838fd022]接口/api/EquipmentClassNew/GetEquipmentClassDictTree,耗时:[1312]毫秒
2025-07-07 10:30:02.097 +08:00 [INF] 【SQL执行耗时:333.3446ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT" 

2025-07-07 10:30:02.480 +08:00 [INF] 【SQL执行耗时:347.1115ms】

[Sql]:SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON" FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "HOSPITAL_ID" = :HospitalId0 ) 
[Pars]:
[Name]::HospitalId0 [Value]:33A001 [Type]:String    

2025-07-07 10:30:02.885 +08:00 [INF] 【SQL执行耗时:334.0649ms】

[Sql]:SELECT "CLASS_ARCHIVES_ID" AS "CLASSARCHIVESID","HOSPITAL_ID" AS "HOSPITALID","EQP_CLASS_ID" AS "EQPCLASSID","EQP_ARCHIVES_ID" AS "EQPARCHIVESID","REMARK" AS "REMARK","LAST_MTIME" AS "LAST_MTIME","LAST_MPERSON" AS "LAST_MPERSON","FIRST_RTIME" AS "FIRST_RTIME","FIRST_RPERSON" AS "FIRST_RPERSON" FROM "XH_OA"."EMS_EQP_CLASS_ARCHIVES"  WHERE   "EQP_ARCHIVES_ID" IN ('H82CLASSJZL','H82CLASSXXL','H82CLASSFZL','H82CLASSGYL','1','4','6','7','11','2','3','12','13','5','10','14','15','16','17','10001','10002','10003','60001','60002','40001','40002','70001','70002','70003','110001','110002','110003','110004','110005','110006','20001','20002','30001','30002','120001','130001','130002','130003','50001','100001','100002','100003','140001','140002','140003','150001','150002','160001','170001')   

2025-07-07 10:30:03.268 +08:00 [INF] 【SQL执行耗时:341.8629ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT" 

2025-07-07 10:30:03.666 +08:00 [INF] 【SQL执行耗时:360.7949ms】

[Sql]:SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON" FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "HOSPITAL_ID" = :HospitalId0 ) 
[Pars]:
[Name]::HospitalId0 [Value]:33A001 [Type]:String    

2025-07-07 10:30:04.082 +08:00 [INF] 【SQL执行耗时:342.4458ms】

[Sql]:SELECT "CLASS_ARCHIVES_ID" AS "CLASSARCHIVESID","HOSPITAL_ID" AS "HOSPITALID","EQP_CLASS_ID" AS "EQPCLASSID","EQP_ARCHIVES_ID" AS "EQPARCHIVESID","REMARK" AS "REMARK","LAST_MTIME" AS "LAST_MTIME","LAST_MPERSON" AS "LAST_MPERSON","FIRST_RTIME" AS "FIRST_RTIME","FIRST_RPERSON" AS "FIRST_RPERSON" FROM "XH_OA"."EMS_EQP_CLASS_ARCHIVES"  WHERE   "EQP_ARCHIVES_ID" IN ('H82CLASSJZL','H82CLASSXXL','H82CLASSFZL','H82CLASSGYL','1','4','6','7','11','2','3','12','13','5','10','14','15','16','17','10001','10002','10003','60001','60002','40001','40002','70001','70002','70003','110001','110002','110003','110004','110005','110006','20001','20002','30001','30002','120001','130001','130002','130003','50001','100001','100002','100003','140001','140002','140003','150001','150002','160001','170001')   

2025-07-07 10:30:04.150 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetEquipmentClassDictTree responded 200 in 2430.7953 ms
2025-07-07 10:30:04.150 +08:00 [INF] 【接口超时阀值预警】 [e2155af4a024f1e810cf706df0f1b702]接口/api/EquipmentClassNew/GetEquipmentClassDictTree,耗时:[2431]毫秒
2025-07-07 10:30:46.111 +08:00 [INF] 【SQL执行耗时:352.8963ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT" 

2025-07-07 10:30:46.507 +08:00 [INF] 【SQL执行耗时:358.3262ms】

[Sql]:SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON" FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "HOSPITAL_ID" = :HospitalId0 ) 
[Pars]:
[Name]::HospitalId0 [Value]:33A001 [Type]:String    

2025-07-07 10:30:46.912 +08:00 [INF] 【SQL执行耗时:331.4678ms】

[Sql]:SELECT "CLASS_ARCHIVES_ID" AS "CLASSARCHIVESID","HOSPITAL_ID" AS "HOSPITALID","EQP_CLASS_ID" AS "EQPCLASSID","EQP_ARCHIVES_ID" AS "EQPARCHIVESID","REMARK" AS "REMARK","LAST_MTIME" AS "LAST_MTIME","LAST_MPERSON" AS "LAST_MPERSON","FIRST_RTIME" AS "FIRST_RTIME","FIRST_RPERSON" AS "FIRST_RPERSON" FROM "XH_OA"."EMS_EQP_CLASS_ARCHIVES"  WHERE   "EQP_ARCHIVES_ID" IN ('H82CLASSJZL','H82CLASSXXL','H82CLASSFZL','H82CLASSGYL','1','4','6','7','11','2','3','12','13','5','10','14','15','16','17','10001','10002','10003','60001','60002','40001','40002','70001','70002','70003','110001','110002','110003','110004','110005','110006','20001','20002','30001','30002','120001','130001','130002','130003','50001','100001','100002','100003','140001','140002','140003','150001','150002','160001','170001')   

2025-07-07 10:30:47.306 +08:00 [INF] 【SQL执行耗时:358.7483ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT" 

2025-07-07 10:30:47.689 +08:00 [INF] 【SQL执行耗时:344.0447ms】

[Sql]:SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON" FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "HOSPITAL_ID" = :HospitalId0 ) 
[Pars]:
[Name]::HospitalId0 [Value]:33A001 [Type]:String    

2025-07-07 10:30:48.123 +08:00 [INF] 【SQL执行耗时:363.5055ms】

[Sql]:SELECT "CLASS_ARCHIVES_ID" AS "CLASSARCHIVESID","HOSPITAL_ID" AS "HOSPITALID","EQP_CLASS_ID" AS "EQPCLASSID","EQP_ARCHIVES_ID" AS "EQPARCHIVESID","REMARK" AS "REMARK","LAST_MTIME" AS "LAST_MTIME","LAST_MPERSON" AS "LAST_MPERSON","FIRST_RTIME" AS "FIRST_RTIME","FIRST_RPERSON" AS "FIRST_RPERSON" FROM "XH_OA"."EMS_EQP_CLASS_ARCHIVES"  WHERE   "EQP_ARCHIVES_ID" IN ('H82CLASSJZL','H82CLASSXXL','H82CLASSFZL','H82CLASSGYL','1','4','6','7','11','2','3','12','13','5','10','14','15','16','17','10001','10002','10003','60001','60002','40001','40002','70001','70002','70003','110001','110002','110003','110004','110005','110006','20001','20002','30001','30002','120001','130001','130002','130003','50001','100001','100002','100003','140001','140002','140003','150001','150002','160001','170001')   

2025-07-07 10:30:48.163 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetEquipmentClassDictTree responded 200 in 2450.2025 ms
2025-07-07 10:30:48.163 +08:00 [INF] 【接口超时阀值预警】 [7b8831922044cf131ac6cedc954adbd8]接口/api/EquipmentClassNew/GetEquipmentClassDictTree,耗时:[2450]毫秒
2025-07-07 10:31:01.913 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetEquipmentArchivesNotLinkByClassId/ responded 404 in 45.4314 ms
2025-07-07 10:31:04.551 +08:00 [INF] 【SQL执行耗时:363.0041ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT" 

2025-07-07 10:31:04.948 +08:00 [INF] 【SQL执行耗时:357.7439ms】

[Sql]:SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON" FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "HOSPITAL_ID" = :HospitalId0 ) 
[Pars]:
[Name]::HospitalId0 [Value]:33A001 [Type]:String    

2025-07-07 10:31:05.387 +08:00 [INF] 【SQL执行耗时:365.9005ms】

[Sql]:SELECT "CLASS_ARCHIVES_ID" AS "CLASSARCHIVESID","HOSPITAL_ID" AS "HOSPITALID","EQP_CLASS_ID" AS "EQPCLASSID","EQP_ARCHIVES_ID" AS "EQPARCHIVESID","REMARK" AS "REMARK","LAST_MTIME" AS "LAST_MTIME","LAST_MPERSON" AS "LAST_MPERSON","FIRST_RTIME" AS "FIRST_RTIME","FIRST_RPERSON" AS "FIRST_RPERSON" FROM "XH_OA"."EMS_EQP_CLASS_ARCHIVES"  WHERE   "EQP_ARCHIVES_ID" IN ('H82CLASSJZL','H82CLASSXXL','H82CLASSFZL','H82CLASSGYL','1','4','6','7','11','2','3','12','13','5','10','14','15','16','17','10001','10002','10003','60001','60002','40001','40002','70001','70002','70003','110001','110002','110003','110004','110005','110006','20001','20002','30001','30002','120001','130001','130002','130003','50001','100001','100002','100003','140001','140002','140003','150001','150002','160001','170001')   

2025-07-07 10:31:05.778 +08:00 [INF] 【SQL执行耗时:349.5463ms】

[Sql]: SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON"  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "CLASS_ID" = :ClassId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::ClassId0 [Value]:1 [Type]:String    

2025-07-07 10:31:06.156 +08:00 [INF] 【SQL执行耗时:340.7994ms】

[Sql]:SELECT "CLASS_ARCHIVES_ID" AS "CLASSARCHIVESID","HOSPITAL_ID" AS "HOSPITALID","EQP_CLASS_ID" AS "EQPCLASSID","EQP_ARCHIVES_ID" AS "EQPARCHIVESID","REMARK" AS "REMARK","LAST_MTIME" AS "LAST_MTIME","LAST_MPERSON" AS "LAST_MPERSON","FIRST_RTIME" AS "FIRST_RTIME","FIRST_RPERSON" AS "FIRST_RPERSON" FROM "XH_OA"."EMS_EQP_CLASS_ARCHIVES"  WHERE   "EQP_ARCHIVES_ID" = '1'   

2025-07-07 10:31:06.196 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetEquipmentArchivesNotLinkByClassId/1 responded 200 in 2045.3355 ms
2025-07-07 10:31:06.196 +08:00 [INF] 【接口超时阀值预警】 [36ef62f9c05f5884f65d923527baad87]接口/api/EquipmentClassNew/GetEquipmentArchivesNotLinkByClassId/1,耗时:[2045]毫秒
2025-07-07 10:32:12.185 +08:00 [INF] 【SQL执行耗时:364.8013ms】

[Sql]:SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" <> :EqpNoState0 ) 
[Pars]:
[Name]::EqpNoState0 [Value]:2 [Type]:String    

2025-07-07 10:32:12.984 +08:00 [INF] 【SQL执行耗时:366.8024ms】

[Sql]:SELECT "DATA_ID","HOSPITAL_ID","LAB_ID","CLASS_ID","DATA_SORT","DATA_CNAME","DATA_ENAME","HIS_ID","CUSTOM_CODE","SPELL_CODE","DATA_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","DATA_SNAME","DATA_SOURCE","ONE_CLASS","DATA_UNAME","IF_REPEAT","SYSTEM_ID" FROM "XH_SYS"."SYS6_BASE_DATA"  WHERE ((( "CLASS_ID" = :CLASS_ID0 ) OR ( "CLASS_ID" = :CLASS_ID1 )) AND ( "DATA_STATE" = :DATA_STATE2 )) 
[Pars]:
[Name]::CLASS_ID0 [Value]:设备分类 [Type]:String    
[Name]::CLASS_ID1 [Value]:设备类型 [Type]:String    
[Name]::DATA_STATE2 [Value]:1 [Type]:String    

2025-07-07 10:32:13.400 +08:00 [INF] 【SQL执行耗时:366.3493ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-07 10:32:13.843 +08:00 [INF] 【SQL执行耗时:363.4873ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_CLASS" = :EQUIPMENT_CLASS0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_CLASS0 [Value]:10001 [Type]:String    

2025-07-07 10:32:41.313 +08:00 [ERR] 未处理的异常::System.NullReferenceException: Object reference not set to an instance of an object.
   at XH.H82.Services.EquipmentCodeCustom.CustomCodeService.GetEquipmentInfoByCache(String eqpNoClass) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentCodeCustom\CustomCodeService.cs:line 295
   at XH.H82.Services.EquipmentCodeCustom.CustomCodeService.GetEquipmentCodeCustomDictByCondetion(Expression`1 expression) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentCodeCustom\CustomCodeService.cs:line 86
   at XH.H82.Services.EquipmentCodeCustom.CustomCodeService.GetEquipmentCodeCustomDict() in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentCodeCustom\CustomCodeService.cs:line 94
   at Castle.Proxies.Invocations.ICustomCodeService_GetEquipmentCodeCustomDict.InvokeMethodOnTarget()
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.UseCacheWhenFailedAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.CacheAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at Castle.Proxies.ICustomCodeServiceProxy.GetEquipmentCodeCustomDict()
   at XH.H82.API.Controllers.EquipmentCodeCustom.CodeCustomController.GetEquipmentCodeCustomDict() in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\Controllers\EquipmentCodeCustom\CodeCustomController.cs:line 37
   at lambda_method968(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-07-07 10:32:41.316 +08:00 [ERR] HTTP GET /api/CodeCustom/GetEquipmentCodeCustomDict responded 500 in 29609.4196 ms
2025-07-07 10:32:41.317 +08:00 [INF] 【接口超时阀值预警】 [c0714c3e615b75d9c337f08fb2523312]接口/api/CodeCustom/GetEquipmentCodeCustomDict,耗时:[29610]毫秒
2025-07-07 10:33:01.101 +08:00 [INF] 【SQL执行耗时:364.3364ms】

[Sql]:SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" <> :EqpNoState0 ) 
[Pars]:
[Name]::EqpNoState0 [Value]:2 [Type]:String    

2025-07-07 10:33:01.852 +08:00 [INF] 【SQL执行耗时:351.5624ms】

[Sql]:SELECT "DATA_ID","HOSPITAL_ID","LAB_ID","CLASS_ID","DATA_SORT","DATA_CNAME","DATA_ENAME","HIS_ID","CUSTOM_CODE","SPELL_CODE","DATA_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","DATA_SNAME","DATA_SOURCE","ONE_CLASS","DATA_UNAME","IF_REPEAT","SYSTEM_ID" FROM "XH_SYS"."SYS6_BASE_DATA"  WHERE ((( "CLASS_ID" = :CLASS_ID0 ) OR ( "CLASS_ID" = :CLASS_ID1 )) AND ( "DATA_STATE" = :DATA_STATE2 )) 
[Pars]:
[Name]::CLASS_ID0 [Value]:设备分类 [Type]:String    
[Name]::CLASS_ID1 [Value]:设备类型 [Type]:String    
[Name]::DATA_STATE2 [Value]:1 [Type]:String    

2025-07-07 10:33:02.290 +08:00 [INF] 【SQL执行耗时:401.3369ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-07 10:33:07.889 +08:00 [INF] 【SQL执行耗时:380.8896ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE ( "EQUIPMENT_CLASS" = :EQUIPMENT_CLASS0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::EQUIPMENT_CLASS0 [Value]:10001 [Type]:String    

2025-07-07 10:33:52.621 +08:00 [INF] 【SQL执行耗时:410.119ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-07 10:33:55.852 +08:00 [INF] 【SQL执行耗时:405.1563ms】

[Sql]:SELECT "HOSPITAL_ID","HOSPITAL_LOGIN","HOSPITAL_CNAME","HOSPITAL_SNAME","HOSPITAL_TYPE","HOSPITAL_LEVEL","LEVEL1_CODE","LEVEL2_CODE","LEVEL3_CODE","HOSPITAL_BARCODE","HOSPITAL_PROPERTY","HOSPITAL_PERSON","HOSPITAL_ADDRESS","HOSPITAL_TEL","HOSPITAL_STATE","FIRST_PERSON","FIRST_RTIME","LAST_MPRESON","LAST_MTIME","REMARK","HOSPITAL_BCODE","HOSPITAL_RANGE","BARCODE_TYPE","BARCODE_RANGE","CHARGE_PROVIDE_TYPE","DATADB_IP","WECHAT_NAME","LAST_MPERSON","FIRST_RPERSON","UPDATE_FLAG","INSERFACE_SERVICE","SAMELEVEL_HOSPITAL","HOSPITAL_CLASS","HOSPITAL_URL","REGISTER_CODE","PACK_BAR_PREFIX","IF_ITEM_MANAGE","IF_TEMPLATE_HOS","MEDICAL_CONGENER","MEDICAL_CONGENER_ROLE","VEST_IN_DC","INSTANCE_ID","HOSPITAL_ATTRIBUTE","DATASYNC_TYPE","HGROUP_ID","CITEM_MANAGE" FROM "XH_SYS"."SYS6_HOSPITAL_INFO" 

2025-07-07 10:33:56.293 +08:00 [INF] 【SQL执行耗时:358.96ms】

[Sql]:SELECT "LAB_ID","FIRST_RPERSON","LAB_NAME","LAB_ADDRESS","LAB_CODE","LAST_MTIME","LAB_SORT","LAB_RNAME","LAB_CLASS","LAB_PERSON","LINKMAN_TEL","REGISTER_CODE","LAB_LINKMAN","STATE_FLAG","REMARK","LAST_MPERSON","FIRST_RTIME","SPELL_CODE","LAB_TEL","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_LAB" 

2025-07-07 10:33:56.774 +08:00 [INF] 【SQL执行耗时:352.9854ms】

[Sql]:SELECT "MGROUP_ID","LAB_ID","MGROUP_TEL","FIRST_RPERSON","MGROUP_ADDRESS","LAST_MTIME","MGROUP_PERSON","MGROUP_SORT","MGROUP_CODE","MGROUP_CLASS","MGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","MGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_MGROUP" 

2025-07-07 10:33:57.224 +08:00 [INF] 【SQL执行耗时:373.0923ms】

[Sql]:SELECT "AREA_ID","AREA_CODE","AREA_SORT","AREA_NAME","AREA_CLASS","AREA_PERSON","AREA_ADDRESS","AREA_TEL","SPELL_CODE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","STATE_FLAG","REMARK","HOSPITAL_ID","TAG_SIGN","AREA_ANAME","PACK_BAR_PREFIX" FROM "XH_SYS"."SYS6_INSPECTION_AREA" 

2025-07-07 10:34:01.654 +08:00 [INF] 【SQL执行耗时:374.2284ms】

[Sql]: SELECT "EQUIPMENT_ID","UNIT_ID","HOSPITAL_ID","LAB_ID","PROFESSIONAL_CLASS","INSTRUMENT_ID","ESERIES_ID","EQUIPMENT_NUM","EQUIPMENT_NAME","DEPT_SECTION_NO","EQUIPMENT_ENAME","EQUIPMENT_MODEL","VEST_PIPELINE","EQUIPMENT_CLASS","DEPT_NAME","SECTION_NO","EQUIPMENT_SORT","FACTORY_NUM","EQUIPMENT_FEATURE","BUY_DATE","SELL_PRICE","KEEP_PERSON","INSTALL_DATE","INSTALL_AREA","DEPRECIATION_TIME","ANNUAL_SURVEY_DATE","MANUFACTURER","DEALER","REPAIR_COMPANY","APPLY_STATE","CERTIFICATE_STATE","ACCEPT_REPORT_STATE","EQUIPMENT_GRAPH_STATE","MANUAL_STATE","REPAIR_PERSON","REPAIR_PERSON_STATE","CONTACT_PHONE","REGISTER_PERSON","REGISTER_TIME","REGISTRATION_NUM","REGISTRATION_ENUM","EQUIPMENT_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","MANUFACTURER_ID","DEALER_ID","EQUIPMENT_SIZE","EQUIPMENT_POWER","EQUIPMENT_VOLTAGE","EQUIPMENT_TEMP","EQUIPMENT_TEMP_RANGE","EQ_IN_PERSON","EQ_IN_TIME","EQ_OUT_PERSON","EQ_OUT_TIME","EQ_SCRAP_PERSON","EQ_SCRAP_TIME","SERIAL_NUMBER","EQUIPMENT_CODE","DEALER_ENAME","MANUFACTURER_ENAME","EQUIPMENT_TYPE","ENABLE_TIME","IS_HIDE","EQ_SERVICE_LIFE","SMBL_FLAG","SMBL_LAB_ID","PROVIDER_ID","PROVIDER","EQUIPMENT_UCODE","SMBL_CLASS","SMBL_STATE","COUNTRY_ORIGIN","POSITION_ID","EQUIPMENT_JSON"  FROM "XH_OA"."EMS_EQUIPMENT_INFO"  WHERE   ROWNUM = 1 

2025-07-07 10:34:01.744 +08:00 [INF] HTTP GET /api/CodeCustom/GetEquipmentCodeCustomDict responded 200 in 61044.7321 ms
2025-07-07 10:34:01.745 +08:00 [INF] 【接口超时阀值预警】 [d5dc5da0e5155ddcbe02f2b7c0279e37]接口/api/CodeCustom/GetEquipmentCodeCustomDict,耗时:[61045]毫秒
2025-07-07 10:34:35.739 +08:00 [INF] 【SQL执行耗时:493.4902ms】

[Sql]:SELECT "EQP_NO_ID","HOSPITAL_ID","EQP_NO_NAME","EQP_NO_LEVEL","EQP_NO_CLASS","EQP_DISPLAY_JSON","EQP_NO_APPLYS","EQP_NO_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQPNO_FORMAT_DICT"  WHERE ( "EQP_NO_STATE" <> :EqpNoState0 ) 
[Pars]:
[Name]::EqpNoState0 [Value]:2 [Type]:String    

2025-07-07 10:34:36.475 +08:00 [INF] 【SQL执行耗时:343.4581ms】

[Sql]:SELECT "DATA_ID","HOSPITAL_ID","LAB_ID","CLASS_ID","DATA_SORT","DATA_CNAME","DATA_ENAME","HIS_ID","CUSTOM_CODE","SPELL_CODE","DATA_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","DATA_SNAME","DATA_SOURCE","ONE_CLASS","DATA_UNAME","IF_REPEAT","SYSTEM_ID" FROM "XH_SYS"."SYS6_BASE_DATA"  WHERE ((( "CLASS_ID" = :CLASS_ID0 ) OR ( "CLASS_ID" = :CLASS_ID1 )) AND ( "DATA_STATE" = :DATA_STATE2 )) 
[Pars]:
[Name]::CLASS_ID0 [Value]:设备分类 [Type]:String    
[Name]::CLASS_ID1 [Value]:设备类型 [Type]:String    
[Name]::DATA_STATE2 [Value]:1 [Type]:String    

2025-07-07 10:34:36.884 +08:00 [INF] 【SQL执行耗时:373.0311ms】

[Sql]:SELECT "PGROUP_ID","LAB_ID","PGROUP_TEL","FIRST_RPERSON","AREA_ID","PGROUP_ADDRESS","LAST_MTIME","MGROUP_ID","PGROUP_PERSON","PGROUP_SORT","PGROUP_CODE","HOSP_SERVICEID","PGROUP_CLASS","PGROUP_STATE","REMARK","FIRST_RTIME","LAST_MPERSON","PGROUP_NAME","SPELL_CODE","HOSPITAL_ID" FROM "XH_SYS"."SYS6_INSPECTION_PGROUP" 

2025-07-07 10:34:36.962 +08:00 [INF] HTTP GET /api/CodeCustom/GetEquipmentCodeCustomDict responded 200 in 1762.8406 ms
2025-07-07 10:34:36.962 +08:00 [INF] 【接口超时阀值预警】 [a1d22bef6571589c60e6ecd0ebee4c4d]接口/api/CodeCustom/GetEquipmentCodeCustomDict,耗时:[1763]毫秒
2025-07-07 10:43:33.620 +08:00 [INF] ==>App Start..2025-07-07 10:43:33
2025-07-07 10:43:33.787 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-07 10:43:33.790 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-07 10:43:35.245 +08:00 [INF] ==>基础连接请求完成.
2025-07-07 10:43:35.623 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-07 10:43:35.967 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-07 10:43:36.275 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-07 10:43:36.278 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-07 10:43:36.631 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-07 10:43:37.199 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-07 10:43:37.303 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-07 10:43:37.743 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-07 10:43:37.743 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-07 10:43:38.637 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-07 10:43:41.311 +08:00 [INF] ==>初始化完成..
2025-07-07 10:43:41.335 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-07 10:43:41.338 +08:00 [INF] 设备启用任务
2025-07-07 10:43:41.339 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-07 10:43:41.737 +08:00 [INF] 【SQL执行耗时:378.5608ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-07 10:43:41.895 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-07 10:43:41.911 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-07 10:43:41.913 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-07 10:43:41.913 +08:00 [INF] Hosting environment: Development
2025-07-07 10:43:41.913 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-07 11:04:28.455 +08:00 [INF] ==>App Start..2025-07-07 11:04:28
2025-07-07 11:04:28.658 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-07 11:04:28.661 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-07 11:04:30.485 +08:00 [INF] ==>基础连接请求完成.
2025-07-07 11:04:30.849 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-07 11:04:31.173 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-07 11:04:31.466 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-07 11:04:31.468 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-07 11:04:31.793 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-07 11:04:33.643 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-07 11:04:34.069 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-07 11:04:34.567 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-07 11:04:34.567 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-07 11:04:35.623 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-07 11:04:38.630 +08:00 [INF] ==>初始化完成..
2025-07-07 11:04:38.680 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-07 11:04:38.681 +08:00 [INF] 设备启用任务
2025-07-07 11:04:38.681 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-07 11:04:39.118 +08:00 [INF] 【SQL执行耗时:417.8071ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-07 11:04:39.305 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-07 11:04:39.318 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-07 11:04:39.319 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-07 11:04:39.320 +08:00 [INF] Hosting environment: Development
2025-07-07 11:04:39.320 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-07 11:06:04.347 +08:00 [INF] HTTP GET /api/EquipmentArchives/GetEquipmentArchives responded 401 in 274.8160 ms
2025-07-07 11:06:11.592 +08:00 [INF] 【SQL执行耗时:348.3964ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQP_ARCHIVES_DICT" 

2025-07-07 11:06:12.008 +08:00 [INF] 【SQL执行耗时:359.7734ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT" 

2025-07-07 11:06:12.468 +08:00 [INF] 【SQL执行耗时:410.0276ms】

[Sql]:SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON" FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "HOSPITAL_ID" = :HospitalId0 )  AND ( "CLASS_STATE" <> :ClassState1 ) 
[Pars]:
[Name]::HospitalId0 [Value]:33A001 [Type]:String    
[Name]::ClassState1 [Value]:2 [Type]:String    

2025-07-07 11:06:12.908 +08:00 [INF] 【SQL执行耗时:355.5116ms】

[Sql]:SELECT "CLASS_ARCHIVES_ID" AS "CLASSARCHIVESID","HOSPITAL_ID" AS "HOSPITALID","EQP_CLASS_ID" AS "EQPCLASSID","EQP_ARCHIVES_ID" AS "EQPARCHIVESID","REMARK" AS "REMARK","LAST_MTIME" AS "LAST_MTIME","LAST_MPERSON" AS "LAST_MPERSON","FIRST_RTIME" AS "FIRST_RTIME","FIRST_RPERSON" AS "FIRST_RPERSON" FROM "XH_OA"."EMS_EQP_CLASS_ARCHIVES"  WHERE   "EQP_ARCHIVES_ID" IN ('H82CLASSJZL','H82CLASSXXL','H82CLASSFZL','H82CLASSGYL','1','4','6','7','11','2','3','12','13','5','10','14','15','16','17','10001','10002','10003','60001','60002','40001','40002','70001','70002','70003','110001','110002','110003','110004','110005','110006','20001','20002','30001','30002','120001','130001','130002','130003','50001','100001','100002','100003','140001','140002','140003','150001','150002','160001','170001')   

2025-07-07 11:06:12.979 +08:00 [INF] HTTP GET /api/EquipmentArchives/GetEquipmentArchives responded 200 in 4465.1945 ms
2025-07-07 11:06:12.981 +08:00 [INF] 【接口超时阀值预警】 [8fd8185499b9349c9d1f11b03c798a97]接口/api/EquipmentArchives/GetEquipmentArchives,耗时:[4465]毫秒
2025-07-07 11:06:32.264 +08:00 [INF] 【SQL执行耗时:333.2971ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQP_ARCHIVES_DICT" 

2025-07-07 11:06:32.669 +08:00 [INF] 【SQL执行耗时:369.9072ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT" 

2025-07-07 11:06:33.058 +08:00 [INF] 【SQL执行耗时:351.4977ms】

[Sql]:SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON" FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "HOSPITAL_ID" = :HospitalId0 )  AND ( "CLASS_STATE" <> :ClassState1 ) 
[Pars]:
[Name]::HospitalId0 [Value]:33A001 [Type]:String    
[Name]::ClassState1 [Value]:2 [Type]:String    

2025-07-07 11:06:33.463 +08:00 [INF] 【SQL执行耗时:333.25ms】

[Sql]:SELECT "CLASS_ARCHIVES_ID" AS "CLASSARCHIVESID","HOSPITAL_ID" AS "HOSPITALID","EQP_CLASS_ID" AS "EQPCLASSID","EQP_ARCHIVES_ID" AS "EQPARCHIVESID","REMARK" AS "REMARK","LAST_MTIME" AS "LAST_MTIME","LAST_MPERSON" AS "LAST_MPERSON","FIRST_RTIME" AS "FIRST_RTIME","FIRST_RPERSON" AS "FIRST_RPERSON" FROM "XH_OA"."EMS_EQP_CLASS_ARCHIVES"  WHERE   "EQP_ARCHIVES_ID" IN ('H82CLASSJZL','H82CLASSXXL','H82CLASSFZL','H82CLASSGYL','1','4','6','7','11','2','3','12','13','5','10','14','15','16','17','10001','10002','10003','60001','60002','40001','40002','70001','70002','70003','110001','110002','110003','110004','110005','110006','20001','20002','30001','30002','120001','130001','130002','130003','50001','100001','100002','100003','140001','140002','140003','150001','150002','160001','170001')   

2025-07-07 11:06:33.505 +08:00 [INF] HTTP GET /api/EquipmentArchives/GetEquipmentArchives responded 200 in 1614.2717 ms
2025-07-07 11:06:33.506 +08:00 [INF] 【接口超时阀值预警】 [a321ed7e8f0d82d61aa645cf6c7d680d]接口/api/EquipmentArchives/GetEquipmentArchives,耗时:[1614]毫秒
2025-07-07 11:10:46.639 +08:00 [INF] 【SQL执行耗时:475.8641ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQP_ARCHIVES_DICT" 

2025-07-07 11:10:47.148 +08:00 [INF] 【SQL执行耗时:450.5698ms】

[Sql]: SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON"  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "CLASS_ID" = :ClassId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::ClassId0 [Value]:1 [Type]:String    

2025-07-07 11:10:47.545 +08:00 [INF] 【SQL执行耗时:346.0782ms】

[Sql]:SELECT "CLASS_ARCHIVES_ID" AS "CLASSARCHIVESID","HOSPITAL_ID" AS "HOSPITALID","EQP_CLASS_ID" AS "EQPCLASSID","EQP_ARCHIVES_ID" AS "EQPARCHIVESID","REMARK" AS "REMARK","LAST_MTIME" AS "LAST_MTIME","LAST_MPERSON" AS "LAST_MPERSON","FIRST_RTIME" AS "FIRST_RTIME","FIRST_RPERSON" AS "FIRST_RPERSON" FROM "XH_OA"."EMS_EQP_CLASS_ARCHIVES"  WHERE   "EQP_ARCHIVES_ID" = '1'   

2025-07-07 11:10:47.999 +08:00 [INF] 【SQL执行耗时:417.0434ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQP_ARCHIVES_DICT" 

2025-07-07 11:10:48.437 +08:00 [INF] 【SQL执行耗时:375.1655ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQP_ARCHIVES_DICT" 

2025-07-07 11:10:48.491 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetEquipmentArchivesNotLinkByClassId/1 responded 200 in 2387.4867 ms
2025-07-07 11:10:48.491 +08:00 [INF] 【接口超时阀值预警】 [45abe2eca87bdd6ce6a969fbb316e58d]接口/api/EquipmentClassNew/GetEquipmentArchivesNotLinkByClassId/1,耗时:[2387]毫秒
2025-07-07 11:11:28.845 +08:00 [INF] 【SQL执行耗时:520.1739ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQP_ARCHIVES_DICT" 

2025-07-07 11:11:29.250 +08:00 [INF] 【SQL执行耗时:367.1197ms】

[Sql]: SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON"  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "CLASS_ID" = :ClassId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::ClassId0 [Value]:1 [Type]:String    

2025-07-07 11:11:29.634 +08:00 [INF] 【SQL执行耗时:341.5596ms】

[Sql]:SELECT "CLASS_ARCHIVES_ID" AS "CLASSARCHIVESID","HOSPITAL_ID" AS "HOSPITALID","EQP_CLASS_ID" AS "EQPCLASSID","EQP_ARCHIVES_ID" AS "EQPARCHIVESID","REMARK" AS "REMARK","LAST_MTIME" AS "LAST_MTIME","LAST_MPERSON" AS "LAST_MPERSON","FIRST_RTIME" AS "FIRST_RTIME","FIRST_RPERSON" AS "FIRST_RPERSON" FROM "XH_OA"."EMS_EQP_CLASS_ARCHIVES"  WHERE   "EQP_ARCHIVES_ID" = '1'   

2025-07-07 11:11:30.015 +08:00 [INF] 【SQL执行耗时:345.6454ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQP_ARCHIVES_DICT" 

2025-07-07 11:11:30.379 +08:00 [INF] 【SQL执行耗时:326.3409ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQP_ARCHIVES_DICT" 

2025-07-07 11:11:44.579 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetEquipmentArchivesNotLinkByClassId/1 responded 200 in 16298.6459 ms
2025-07-07 11:11:44.579 +08:00 [INF] 【接口超时阀值预警】 [f9184f187be8ae7a5f0ab1ae864447b8]接口/api/EquipmentClassNew/GetEquipmentArchivesNotLinkByClassId/1,耗时:[16299]毫秒
2025-07-07 11:12:32.667 +08:00 [INF] 【SQL执行耗时:630.1888ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQP_ARCHIVES_DICT" 

2025-07-07 11:12:34.167 +08:00 [INF] 【SQL执行耗时:1457.8189ms】

[Sql]: SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON"  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "CLASS_ID" = :ClassId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::ClassId0 [Value]:1 [Type]:String    

2025-07-07 11:12:35.387 +08:00 [INF] 【SQL执行耗时:1146.6709ms】

[Sql]:SELECT "CLASS_ARCHIVES_ID" AS "CLASSARCHIVESID","HOSPITAL_ID" AS "HOSPITALID","EQP_CLASS_ID" AS "EQPCLASSID","EQP_ARCHIVES_ID" AS "EQPARCHIVESID","REMARK" AS "REMARK","LAST_MTIME" AS "LAST_MTIME","LAST_MPERSON" AS "LAST_MPERSON","FIRST_RTIME" AS "FIRST_RTIME","FIRST_RPERSON" AS "FIRST_RPERSON" FROM "XH_OA"."EMS_EQP_CLASS_ARCHIVES"  WHERE   "EQP_ARCHIVES_ID" = '1'   

2025-07-07 11:12:35.874 +08:00 [INF] 【SQL执行耗时:368.5661ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQP_ARCHIVES_DICT" 

2025-07-07 11:12:36.266 +08:00 [INF] 【SQL执行耗时:354.9235ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQP_ARCHIVES_DICT" 

2025-07-07 11:13:09.885 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetEquipmentArchivesNotLinkByClassId/1 responded 200 in 37893.1115 ms
2025-07-07 11:13:10.076 +08:00 [INF] 【接口超时阀值预警】 [3949645d8720f70ab712f2268a1d6c5c]接口/api/EquipmentClassNew/GetEquipmentArchivesNotLinkByClassId/1,耗时:[38083]毫秒
2025-07-07 11:13:12.952 +08:00 [INF] 【SQL执行耗时:373.1434ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQP_ARCHIVES_DICT" 

2025-07-07 11:13:13.360 +08:00 [INF] 【SQL执行耗时:356.5505ms】

[Sql]: SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON"  FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "CLASS_ID" = :ClassId0 ) AND   ROWNUM = 1  
[Pars]:
[Name]::ClassId0 [Value]:1 [Type]:String    

2025-07-07 11:13:13.759 +08:00 [INF] 【SQL执行耗时:363.2754ms】

[Sql]:SELECT "CLASS_ARCHIVES_ID" AS "CLASSARCHIVESID","HOSPITAL_ID" AS "HOSPITALID","EQP_CLASS_ID" AS "EQPCLASSID","EQP_ARCHIVES_ID" AS "EQPARCHIVESID","REMARK" AS "REMARK","LAST_MTIME" AS "LAST_MTIME","LAST_MPERSON" AS "LAST_MPERSON","FIRST_RTIME" AS "FIRST_RTIME","FIRST_RPERSON" AS "FIRST_RPERSON" FROM "XH_OA"."EMS_EQP_CLASS_ARCHIVES"  WHERE   "EQP_ARCHIVES_ID" = '1'   

2025-07-07 11:13:14.148 +08:00 [INF] 【SQL执行耗时:351.4146ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQP_ARCHIVES_DICT" 

2025-07-07 11:13:14.556 +08:00 [INF] 【SQL执行耗时:371.3371ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQP_ARCHIVES_DICT" 

2025-07-07 11:13:15.857 +08:00 [INF] HTTP GET /api/EquipmentClassNew/GetEquipmentArchivesNotLinkByClassId/1 responded 200 in 3318.7223 ms
2025-07-07 11:13:15.858 +08:00 [INF] 【接口超时阀值预警】 [1ad5116acd3a8641d6e0fb1c2ce42b82]接口/api/EquipmentClassNew/GetEquipmentArchivesNotLinkByClassId/1,耗时:[3319]毫秒
2025-07-07 11:34:21.149 +08:00 [INF] ==>App Start..2025-07-07 11:34:21
2025-07-07 11:34:21.320 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-07 11:34:21.324 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-07 11:34:22.782 +08:00 [INF] ==>基础连接请求完成.
2025-07-07 11:34:23.154 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-07 11:34:23.519 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-07 11:34:23.906 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-07 11:34:23.915 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-07 11:34:24.270 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-07 11:34:24.794 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-07 11:34:24.902 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-07 11:34:25.345 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-07 11:34:25.345 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-07 11:34:26.371 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-07 11:34:29.064 +08:00 [INF] ==>初始化完成..
2025-07-07 11:34:29.084 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-07 11:34:29.087 +08:00 [INF] 设备启用任务
2025-07-07 11:34:29.088 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-07 11:34:29.594 +08:00 [INF] 【SQL执行耗时:486.9776ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-07 11:34:29.781 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-07 11:34:29.794 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-07 11:34:29.796 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-07 11:34:29.796 +08:00 [INF] Hosting environment: Development
2025-07-07 11:34:29.796 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-07 11:35:11.524 +08:00 [ERR] 未处理的异常::System.InvalidOperationException: Unable to resolve service for type 'XH.H82.IServices.EquipmentClassNew.IEquipomentArchivesService' while attempting to activate 'XH.H82.API.Controllers.EquipmentClassNew.EquipmentArchivesController'.
   at Microsoft.Extensions.DependencyInjection.ActivatorUtilities.GetService(IServiceProvider sp, Type type, Type requiredBy, Boolean isDefaultParameterRequired)
   at lambda_method911(Closure , IServiceProvider , Object[] )
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerActivatorProvider.<>c__DisplayClass7_0.<CreateActivator>b__0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-07-07 11:35:11.552 +08:00 [ERR] HTTP GET /api/EquipmentArchives/GetEquipmentArchives responded 500 in 4233.8825 ms
2025-07-07 11:35:11.556 +08:00 [INF] 【接口超时阀值预警】 [5d951ff67c37aa593152a8ca48d4a1c9]接口/api/EquipmentArchives/GetEquipmentArchives,耗时:[4245]毫秒
2025-07-07 11:35:13.301 +08:00 [ERR] 未处理的异常::System.InvalidOperationException: Unable to resolve service for type 'XH.H82.IServices.EquipmentClassNew.IEquipomentArchivesService' while attempting to activate 'XH.H82.API.Controllers.EquipmentClassNew.EquipmentArchivesController'.
   at Microsoft.Extensions.DependencyInjection.ActivatorUtilities.GetService(IServiceProvider sp, Type type, Type requiredBy, Boolean isDefaultParameterRequired)
   at lambda_method911(Closure , IServiceProvider , Object[] )
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerActivatorProvider.<>c__DisplayClass7_0.<CreateActivator>b__0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-07-07 11:35:13.304 +08:00 [ERR] HTTP GET /api/EquipmentArchives/GetEquipmentArchives responded 500 in 49.5437 ms
2025-07-07 11:35:14.707 +08:00 [ERR] 未处理的异常::System.InvalidOperationException: Unable to resolve service for type 'XH.H82.IServices.EquipmentClassNew.IEquipomentArchivesService' while attempting to activate 'XH.H82.API.Controllers.EquipmentClassNew.EquipmentArchivesController'.
   at Microsoft.Extensions.DependencyInjection.ActivatorUtilities.GetService(IServiceProvider sp, Type type, Type requiredBy, Boolean isDefaultParameterRequired)
   at lambda_method911(Closure , IServiceProvider , Object[] )
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerActivatorProvider.<>c__DisplayClass7_0.<CreateActivator>b__0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-07-07 11:35:14.709 +08:00 [ERR] HTTP GET /api/EquipmentArchives/GetEquipmentArchives responded 500 in 158.0060 ms
2025-07-07 11:35:15.946 +08:00 [ERR] 未处理的异常::System.InvalidOperationException: Unable to resolve service for type 'XH.H82.IServices.EquipmentClassNew.IEquipomentArchivesService' while attempting to activate 'XH.H82.API.Controllers.EquipmentClassNew.EquipmentArchivesController'.
   at Microsoft.Extensions.DependencyInjection.ActivatorUtilities.GetService(IServiceProvider sp, Type type, Type requiredBy, Boolean isDefaultParameterRequired)
   at lambda_method911(Closure , IServiceProvider , Object[] )
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerActivatorProvider.<>c__DisplayClass7_0.<CreateActivator>b__0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-07-07 11:35:15.947 +08:00 [ERR] HTTP GET /api/EquipmentArchives/GetEquipmentArchives responded 500 in 42.6073 ms
2025-07-07 11:35:49.298 +08:00 [INF] ==>App Start..2025-07-07 11:35:49
2025-07-07 11:35:49.471 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-07 11:35:49.475 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-07 11:35:50.899 +08:00 [INF] ==>基础连接请求完成.
2025-07-07 11:35:51.259 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-07 11:35:51.582 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-07 11:35:51.914 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-07 11:35:51.920 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-07 11:35:52.253 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-07 11:35:52.675 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-07 11:35:52.782 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-07 11:35:53.198 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-07 11:35:53.199 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-07 11:35:54.085 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-07 11:35:57.157 +08:00 [INF] ==>初始化完成..
2025-07-07 11:35:57.180 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-07 11:35:57.183 +08:00 [INF] 设备启用任务
2025-07-07 11:35:57.184 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-07 11:35:57.574 +08:00 [INF] 【SQL执行耗时:368.5894ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-07 11:35:57.728 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-07 11:35:57.743 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-07 11:35:57.744 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-07 11:35:57.745 +08:00 [INF] Hosting environment: Development
2025-07-07 11:35:57.745 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-07 11:36:03.780 +08:00 [INF] 【SQL执行耗时:352.296ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQP_ARCHIVES_DICT" 

2025-07-07 11:36:04.246 +08:00 [INF] 【SQL执行耗时:371.104ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT" 

2025-07-07 11:36:04.654 +08:00 [INF] 【SQL执行耗时:359.5324ms】

[Sql]:SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON" FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "HOSPITAL_ID" = :HospitalId0 )  AND ( "CLASS_STATE" <> :ClassState1 ) 
[Pars]:
[Name]::HospitalId0 [Value]:33A001 [Type]:String    
[Name]::ClassState1 [Value]:2 [Type]:String    

2025-07-07 11:36:05.117 +08:00 [INF] 【SQL执行耗时:377.7523ms】

[Sql]:SELECT "CLASS_ARCHIVES_ID" AS "CLASSARCHIVESID","HOSPITAL_ID" AS "HOSPITALID","EQP_CLASS_ID" AS "EQPCLASSID","EQP_ARCHIVES_ID" AS "EQPARCHIVESID","REMARK" AS "REMARK","LAST_MTIME" AS "LAST_MTIME","LAST_MPERSON" AS "LAST_MPERSON","FIRST_RTIME" AS "FIRST_RTIME","FIRST_RPERSON" AS "FIRST_RPERSON" FROM "XH_OA"."EMS_EQP_CLASS_ARCHIVES"  WHERE   "EQP_ARCHIVES_ID" IN ('H82CLASSJZL','H82CLASSXXL','H82CLASSFZL','H82CLASSGYL','1','4','6','7','11','2','3','12','13','5','10','14','15','16','17','10001','10002','10003','60001','60002','40001','40002','70001','70002','70003','110001','110002','110003','110004','110005','110006','20001','20002','30001','30002','120001','130001','130002','130003','50001','100001','100002','100003','140001','140002','140003','150001','150002','160001','170001')   

2025-07-07 11:36:05.237 +08:00 [INF] HTTP GET /api/EquipmentArchives/GetEquipmentArchives responded 200 in 4703.0998 ms
2025-07-07 11:36:05.241 +08:00 [INF] 【接口超时阀值预警】 [b986ce2b7b99c6e167b71cc51d265a9c]接口/api/EquipmentArchives/GetEquipmentArchives,耗时:[4711]毫秒
2025-07-07 11:36:09.469 +08:00 [INF] 【SQL执行耗时:345.4986ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQP_ARCHIVES_DICT" 

2025-07-07 11:36:09.913 +08:00 [INF] 【SQL执行耗时:407.5541ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT" 

2025-07-07 11:36:10.301 +08:00 [INF] 【SQL执行耗时:342.5552ms】

[Sql]:SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON" FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "HOSPITAL_ID" = :HospitalId0 )  AND ( "CLASS_STATE" <> :ClassState1 ) 
[Pars]:
[Name]::HospitalId0 [Value]:33A001 [Type]:String    
[Name]::ClassState1 [Value]:2 [Type]:String    

2025-07-07 11:36:10.725 +08:00 [INF] 【SQL执行耗时:352.5ms】

[Sql]:SELECT "CLASS_ARCHIVES_ID" AS "CLASSARCHIVESID","HOSPITAL_ID" AS "HOSPITALID","EQP_CLASS_ID" AS "EQPCLASSID","EQP_ARCHIVES_ID" AS "EQPARCHIVESID","REMARK" AS "REMARK","LAST_MTIME" AS "LAST_MTIME","LAST_MPERSON" AS "LAST_MPERSON","FIRST_RTIME" AS "FIRST_RTIME","FIRST_RPERSON" AS "FIRST_RPERSON" FROM "XH_OA"."EMS_EQP_CLASS_ARCHIVES"  WHERE   "EQP_ARCHIVES_ID" IN ('H82CLASSJZL','H82CLASSXXL','H82CLASSFZL','H82CLASSGYL','1','4','6','7','11','2','3','12','13','5','10','14','15','16','17','10001','10002','10003','60001','60002','40001','40002','70001','70002','70003','110001','110002','110003','110004','110005','110006','20001','20002','30001','30002','120001','130001','130002','130003','50001','100001','100002','100003','140001','140002','140003','150001','150002','160001','170001')   

2025-07-07 11:36:10.763 +08:00 [INF] HTTP GET /api/EquipmentArchives/GetEquipmentArchives responded 200 in 1681.0664 ms
2025-07-07 11:36:10.763 +08:00 [INF] 【接口超时阀值预警】 [69d136f0ce639535dc99315c16d99de1]接口/api/EquipmentArchives/GetEquipmentArchives,耗时:[1681]毫秒
2025-07-07 11:37:38.819 +08:00 [INF] 【SQL执行耗时:367.0038ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQP_ARCHIVES_DICT" 

2025-07-07 11:37:45.324 +08:00 [INF] 【SQL执行耗时:384.6622ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT" 

2025-07-07 11:37:45.736 +08:00 [INF] 【SQL执行耗时:370.9501ms】

[Sql]:SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON" FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "HOSPITAL_ID" = :HospitalId0 )  AND ( "CLASS_STATE" <> :ClassState1 ) 
[Pars]:
[Name]::HospitalId0 [Value]:33A001 [Type]:String    
[Name]::ClassState1 [Value]:2 [Type]:String    

2025-07-07 11:37:46.148 +08:00 [INF] 【SQL执行耗时:327.0081ms】

[Sql]:SELECT "CLASS_ARCHIVES_ID" AS "CLASSARCHIVESID","HOSPITAL_ID" AS "HOSPITALID","EQP_CLASS_ID" AS "EQPCLASSID","EQP_ARCHIVES_ID" AS "EQPARCHIVESID","REMARK" AS "REMARK","LAST_MTIME" AS "LAST_MTIME","LAST_MPERSON" AS "LAST_MPERSON","FIRST_RTIME" AS "FIRST_RTIME","FIRST_RPERSON" AS "FIRST_RPERSON" FROM "XH_OA"."EMS_EQP_CLASS_ARCHIVES"  WHERE   "EQP_ARCHIVES_ID" IN ('H82CLASSJZL','H82CLASSXXL','H82CLASSFZL','H82CLASSGYL','1','4','6','7','11','2','3','12','13','5','10','14','15','16','17','10001','10002','10003','60001','60002','40001','40002','70001','70002','70003','110001','110002','110003','110004','110005','110006','20001','20002','30001','30002','120001','130001','130002','130003','50001','100001','100002','100003','140001','140002','140003','150001','150002','160001','170001')   

2025-07-07 11:37:46.182 +08:00 [INF] HTTP GET /api/EquipmentArchives/GetEquipmentArchives responded 200 in 12712.2074 ms
2025-07-07 11:37:46.183 +08:00 [INF] 【接口超时阀值预警】 [f0c00f48478b71ed0ac6287dc1b5da5c]接口/api/EquipmentArchives/GetEquipmentArchives,耗时:[12712]毫秒
2025-07-07 11:38:16.082 +08:00 [INF] 【SQL执行耗时:350.303ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQP_ARCHIVES_DICT" 

2025-07-07 11:38:16.493 +08:00 [INF] 【SQL执行耗时:347.6553ms】

[Sql]:SELECT "EQP_ARCHIVES_ID","HOSPITAL_ID","EQP_ARCHIVES_PID","EQP_ARCHIVES_TYPE","EQP_ARCHIVES_NAME","EQP_ARCHIVES_SORT","EQP_ARCHIVES_STATE","FORM_SETUP_ID","TABLE_SETUP_ID","EQP_ARCHIVES_JSON","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQP_ARCHIVES_DICT"  WHERE ( (   "EQP_ARCHIVES_ID" = :Condit_EQP_ARCHIVES_ID_1000   )  OR (   "EQP_ARCHIVES_ID" = :Condit_EQP_ARCHIVES_ID_1001   )  OR (   "EQP_ARCHIVES_ID" = :Condit_EQP_ARCHIVES_ID_1002   )  OR (   "EQP_ARCHIVES_ID" = :Condit_EQP_ARCHIVES_ID_1003   )  OR (   "EQP_ARCHIVES_ID" = :Condit_EQP_ARCHIVES_ID_1004   )  OR (   "EQP_ARCHIVES_ID" = :Condit_EQP_ARCHIVES_ID_1005   )  OR (   "EQP_ARCHIVES_ID" = :Condit_EQP_ARCHIVES_ID_1006   )  OR (   "EQP_ARCHIVES_ID" = :Condit_EQP_ARCHIVES_ID_1007   )  OR (   "EQP_ARCHIVES_ID" = :Condit_EQP_ARCHIVES_ID_1008   )  OR (   "EQP_ARCHIVES_ID" = :Condit_EQP_ARCHIVES_ID_1009   )  OR (   "EQP_ARCHIVES_ID" = :Condit_EQP_ARCHIVES_ID_1010   )  OR (   "EQP_ARCHIVES_ID" = :Condit_EQP_ARCHIVES_ID_1011   )  OR (   "EQP_ARCHIVES_ID" = :Condit_EQP_ARCHIVES_ID_1012   )  OR (   "EQP_ARCHIVES_ID" = :Condit_EQP_ARCHIVES_ID_1013   )  OR (   "EQP_ARCHIVES_ID" = :Condit_EQP_ARCHIVES_ID_1014   )  OR (   "EQP_ARCHIVES_ID" = :Condit_EQP_ARCHIVES_ID_1015   )  OR (   "EQP_ARCHIVES_ID" = :Condit_EQP_ARCHIVES_ID_1016   )  OR (   "EQP_ARCHIVES_ID" = :Condit_EQP_ARCHIVES_ID_1017   )  OR (   "EQP_ARCHIVES_ID" = :Condit_EQP_ARCHIVES_ID_1018   )  OR (   "EQP_ARCHIVES_ID" = :Condit_EQP_ARCHIVES_ID_1019   )  OR (   "EQP_ARCHIVES_ID" = :Condit_EQP_ARCHIVES_ID_1020   ) )  
[Pars]:
[Name]::Condit_EQP_ARCHIVES_ID_1000 [Value]:H82JBXX [Type]:String    
[Name]::Condit_EQP_ARCHIVES_ID_1001 [Value]:H82EQPARCHIVESHJXX [Type]:String    
[Name]::Condit_EQP_ARCHIVES_ID_1002 [Value]:H82GYSXX [Type]:String    
[Name]::Condit_EQP_ARCHIVES_ID_1003 [Value]:H82SWXX [Type]:String    
[Name]::Condit_EQP_ARCHIVES_ID_1004 [Value]:H82AZXX [Type]:String    
[Name]::Condit_EQP_ARCHIVES_ID_1005 [Value]:H82QTXX [Type]:String    
[Name]::Condit_EQP_ARCHIVES_ID_1006 [Value]:H82BFXX [Type]:String    
[Name]::Condit_EQP_ARCHIVES_ID_1007 [Value]:H82ZZZSXX [Type]:String    
[Name]::Condit_EQP_ARCHIVES_ID_1008 [Value]:H82SBSMSXX [Type]:String    
[Name]::Condit_EQP_ARCHIVES_ID_1009 [Value]:H82SOPDAXX [Type]:String    
[Name]::Condit_EQP_ARCHIVES_ID_1010 [Value]:H82BSKXX [Type]:String    
[Name]::Condit_EQP_ARCHIVES_ID_1011 [Value]:H82YXJLXX [Type]:String    
[Name]::Condit_EQP_ARCHIVES_ID_1012 [Value]:H82SYJLXX [Type]:String    
[Name]::Condit_EQP_ARCHIVES_ID_1013 [Value]:H82BYJLXX [Type]:String    
[Name]::Condit_EQP_ARCHIVES_ID_1014 [Value]:H82WXJLXX [Type]:String    
[Name]::Condit_EQP_ARCHIVES_ID_1015 [Value]:H82JZLLXX [Type]:String    
[Name]::Condit_EQP_ARCHIVES_ID_1016 [Value]:H82BDJLXX [Type]:String    
[Name]::Condit_EQP_ARCHIVES_ID_1017 [Value]:H82XNYZJLXX [Type]:String    
[Name]::Condit_EQP_ARCHIVES_ID_1018 [Value]:H82QWRJLXX [Type]:String    
[Name]::Condit_EQP_ARCHIVES_ID_1019 [Value]:H82BGJLXX [Type]:String    
[Name]::Condit_EQP_ARCHIVES_ID_1020 [Value]:H82BLSJJLXX [Type]:String    

2025-07-07 11:38:16.998 +08:00 [INF] 【SQL执行耗时:383.6172ms】

[Sql]:INSERT ALL
INTO "XH_OA"."EMS_EQP_ARCHIVES_DICT"  ("EQP_ARCHIVES_ID","HOSPITAL_ID","EQP_ARCHIVES_PID","EQP_ARCHIVES_TYPE","EQP_ARCHIVES_NAME","EQP_ARCHIVES_SORT","EQP_ARCHIVES_STATE","FORM_SETUP_ID","TABLE_SETUP_ID","EQP_ARCHIVES_JSON","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK") VALUES(N'H82JBXX',N'33A001',N'0',N'0',N'基本信息',N'1',N'1',NULL,NULL,N'{"IsUpload":true}',N'H82初始化',to_timestamp('2025-07-07 11:38:16.117835', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 11:38:16.117835', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'初始化数据')  
INTO "XH_OA"."EMS_EQP_ARCHIVES_DICT"  ("EQP_ARCHIVES_ID","HOSPITAL_ID","EQP_ARCHIVES_PID","EQP_ARCHIVES_TYPE","EQP_ARCHIVES_NAME","EQP_ARCHIVES_SORT","EQP_ARCHIVES_STATE","FORM_SETUP_ID","TABLE_SETUP_ID","EQP_ARCHIVES_JSON","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK") VALUES(N'H82EQPARCHIVESHJXX',N'33A001',N'0',N'0',N'环境信息',N'2',N'1',NULL,NULL,N'{"IsUpload":true}',N'H82初始化',to_timestamp('2025-07-07 11:38:16.117837', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 11:38:16.117837', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'初始化数据')  
INTO "XH_OA"."EMS_EQP_ARCHIVES_DICT"  ("EQP_ARCHIVES_ID","HOSPITAL_ID","EQP_ARCHIVES_PID","EQP_ARCHIVES_TYPE","EQP_ARCHIVES_NAME","EQP_ARCHIVES_SORT","EQP_ARCHIVES_STATE","FORM_SETUP_ID","TABLE_SETUP_ID","EQP_ARCHIVES_JSON","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK") VALUES(N'H82GYSXX',N'33A001',N'0',N'0',N'供应商信息',N'3',N'1',NULL,NULL,N'{"IsUpload":true}',N'H82初始化',to_timestamp('2025-07-07 11:38:16.117840', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 11:38:16.117840', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'初始化数据')  
INTO "XH_OA"."EMS_EQP_ARCHIVES_DICT"  ("EQP_ARCHIVES_ID","HOSPITAL_ID","EQP_ARCHIVES_PID","EQP_ARCHIVES_TYPE","EQP_ARCHIVES_NAME","EQP_ARCHIVES_SORT","EQP_ARCHIVES_STATE","FORM_SETUP_ID","TABLE_SETUP_ID","EQP_ARCHIVES_JSON","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK") VALUES(N'H82SWXX',N'33A001',N'0',N'0',N'商务信息',N'4',N'1',NULL,NULL,N'{"IsUpload":true}',N'H82初始化',to_timestamp('2025-07-07 11:38:16.117842', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 11:38:16.117842', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'初始化数据')  
INTO "XH_OA"."EMS_EQP_ARCHIVES_DICT"  ("EQP_ARCHIVES_ID","HOSPITAL_ID","EQP_ARCHIVES_PID","EQP_ARCHIVES_TYPE","EQP_ARCHIVES_NAME","EQP_ARCHIVES_SORT","EQP_ARCHIVES_STATE","FORM_SETUP_ID","TABLE_SETUP_ID","EQP_ARCHIVES_JSON","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK") VALUES(N'H82AZXX',N'33A001',N'0',N'0',N'安装信息',N'5',N'1',NULL,NULL,N'{"IsUpload":true}',N'H82初始化',to_timestamp('2025-07-07 11:38:16.117843', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 11:38:16.117843', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'初始化数据')  
INTO "XH_OA"."EMS_EQP_ARCHIVES_DICT"  ("EQP_ARCHIVES_ID","HOSPITAL_ID","EQP_ARCHIVES_PID","EQP_ARCHIVES_TYPE","EQP_ARCHIVES_NAME","EQP_ARCHIVES_SORT","EQP_ARCHIVES_STATE","FORM_SETUP_ID","TABLE_SETUP_ID","EQP_ARCHIVES_JSON","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK") VALUES(N'H82QTXX',N'33A001',N'0',N'0',N'启停信息',N'6',N'1',NULL,NULL,N'{"IsUpload":true}',N'H82初始化',to_timestamp('2025-07-07 11:38:16.117845', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 11:38:16.117845', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'初始化数据')  
INTO "XH_OA"."EMS_EQP_ARCHIVES_DICT"  ("EQP_ARCHIVES_ID","HOSPITAL_ID","EQP_ARCHIVES_PID","EQP_ARCHIVES_TYPE","EQP_ARCHIVES_NAME","EQP_ARCHIVES_SORT","EQP_ARCHIVES_STATE","FORM_SETUP_ID","TABLE_SETUP_ID","EQP_ARCHIVES_JSON","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK") VALUES(N'H82BFXX',N'33A001',N'0',N'0',N'报废信息',N'7',N'1',NULL,NULL,N'{"IsUpload":true}',N'H82初始化',to_timestamp('2025-07-07 11:38:16.117848', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 11:38:16.117848', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'初始化数据')  
INTO "XH_OA"."EMS_EQP_ARCHIVES_DICT"  ("EQP_ARCHIVES_ID","HOSPITAL_ID","EQP_ARCHIVES_PID","EQP_ARCHIVES_TYPE","EQP_ARCHIVES_NAME","EQP_ARCHIVES_SORT","EQP_ARCHIVES_STATE","FORM_SETUP_ID","TABLE_SETUP_ID","EQP_ARCHIVES_JSON","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK") VALUES(N'H82ZZZSXX',N'33A001',N'0',N'0',N'资质证书',N'8',N'1',NULL,NULL,N'{"IsUpload":true}',N'H82初始化',to_timestamp('2025-07-07 11:38:16.117849', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 11:38:16.117849', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'初始化数据')  
INTO "XH_OA"."EMS_EQP_ARCHIVES_DICT"  ("EQP_ARCHIVES_ID","HOSPITAL_ID","EQP_ARCHIVES_PID","EQP_ARCHIVES_TYPE","EQP_ARCHIVES_NAME","EQP_ARCHIVES_SORT","EQP_ARCHIVES_STATE","FORM_SETUP_ID","TABLE_SETUP_ID","EQP_ARCHIVES_JSON","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK") VALUES(N'H82SBSMSXX',N'33A001',N'0',N'0',N'设备说明书',N'9',N'1',NULL,NULL,N'{"IsUpload":true}',N'H82初始化',to_timestamp('2025-07-07 11:38:16.117851', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 11:38:16.117851', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'初始化数据')  
INTO "XH_OA"."EMS_EQP_ARCHIVES_DICT"  ("EQP_ARCHIVES_ID","HOSPITAL_ID","EQP_ARCHIVES_PID","EQP_ARCHIVES_TYPE","EQP_ARCHIVES_NAME","EQP_ARCHIVES_SORT","EQP_ARCHIVES_STATE","FORM_SETUP_ID","TABLE_SETUP_ID","EQP_ARCHIVES_JSON","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK") VALUES(N'H82SOPDAXX',N'33A001',N'0',N'0',N'基本信息',N'10',N'1',NULL,NULL,N'{"IsUpload":true}',N'H82初始化',to_timestamp('2025-07-07 11:38:16.117852', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 11:38:16.117852', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'初始化数据')  
INTO "XH_OA"."EMS_EQP_ARCHIVES_DICT"  ("EQP_ARCHIVES_ID","HOSPITAL_ID","EQP_ARCHIVES_PID","EQP_ARCHIVES_TYPE","EQP_ARCHIVES_NAME","EQP_ARCHIVES_SORT","EQP_ARCHIVES_STATE","FORM_SETUP_ID","TABLE_SETUP_ID","EQP_ARCHIVES_JSON","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK") VALUES(N'H82BSKXX',N'33A001',N'0',N'0',N'设备标识卡',N'11',N'1',NULL,NULL,N'{"IsUpload":true}',N'H82初始化',to_timestamp('2025-07-07 11:38:16.117853', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 11:38:16.117854', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'初始化数据')  
INTO "XH_OA"."EMS_EQP_ARCHIVES_DICT"  ("EQP_ARCHIVES_ID","HOSPITAL_ID","EQP_ARCHIVES_PID","EQP_ARCHIVES_TYPE","EQP_ARCHIVES_NAME","EQP_ARCHIVES_SORT","EQP_ARCHIVES_STATE","FORM_SETUP_ID","TABLE_SETUP_ID","EQP_ARCHIVES_JSON","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK") VALUES(N'H82YXJLXX',N'33A001',N'0',N'0',N'运行记录',N'12',N'1',NULL,NULL,N'{"IsUpload":true}',N'H82初始化',to_timestamp('2025-07-07 11:38:16.117855', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 11:38:16.117855', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'初始化数据')  
INTO "XH_OA"."EMS_EQP_ARCHIVES_DICT"  ("EQP_ARCHIVES_ID","HOSPITAL_ID","EQP_ARCHIVES_PID","EQP_ARCHIVES_TYPE","EQP_ARCHIVES_NAME","EQP_ARCHIVES_SORT","EQP_ARCHIVES_STATE","FORM_SETUP_ID","TABLE_SETUP_ID","EQP_ARCHIVES_JSON","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK") VALUES(N'H82SYJLXX',N'33A001',N'H82YXJLXX',N'0',N'使用记录',N'13',N'1',NULL,NULL,N'{"IsUpload":true}',N'H82初始化',to_timestamp('2025-07-07 11:38:16.117863', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 11:38:16.117863', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'初始化数据')  
INTO "XH_OA"."EMS_EQP_ARCHIVES_DICT"  ("EQP_ARCHIVES_ID","HOSPITAL_ID","EQP_ARCHIVES_PID","EQP_ARCHIVES_TYPE","EQP_ARCHIVES_NAME","EQP_ARCHIVES_SORT","EQP_ARCHIVES_STATE","FORM_SETUP_ID","TABLE_SETUP_ID","EQP_ARCHIVES_JSON","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK") VALUES(N'H82BYJLXX',N'33A001',N'H82YXJLXX',N'0',N'保养记录',N'14',N'1',NULL,NULL,N'{"IsUpload":true}',N'H82初始化',to_timestamp('2025-07-07 11:38:16.117864', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 11:38:16.117864', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'初始化数据')  
INTO "XH_OA"."EMS_EQP_ARCHIVES_DICT"  ("EQP_ARCHIVES_ID","HOSPITAL_ID","EQP_ARCHIVES_PID","EQP_ARCHIVES_TYPE","EQP_ARCHIVES_NAME","EQP_ARCHIVES_SORT","EQP_ARCHIVES_STATE","FORM_SETUP_ID","TABLE_SETUP_ID","EQP_ARCHIVES_JSON","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK") VALUES(N'H82WXJLXX',N'33A001',N'H82YXJLXX',N'0',N'基本信息',N'15',N'1',NULL,NULL,N'{"IsUpload":true}',N'H82初始化',to_timestamp('2025-07-07 11:38:16.117866', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 11:38:16.117866', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'初始化数据')  
INTO "XH_OA"."EMS_EQP_ARCHIVES_DICT"  ("EQP_ARCHIVES_ID","HOSPITAL_ID","EQP_ARCHIVES_PID","EQP_ARCHIVES_TYPE","EQP_ARCHIVES_NAME","EQP_ARCHIVES_SORT","EQP_ARCHIVES_STATE","FORM_SETUP_ID","TABLE_SETUP_ID","EQP_ARCHIVES_JSON","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK") VALUES(N'H82JZLLXX',N'33A001',N'H82YXJLXX',N'0',N'校准记录',N'16',N'1',NULL,NULL,N'{"IsUpload":true}',N'H82初始化',to_timestamp('2025-07-07 11:38:16.117867', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 11:38:16.117867', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'初始化数据')  
INTO "XH_OA"."EMS_EQP_ARCHIVES_DICT"  ("EQP_ARCHIVES_ID","HOSPITAL_ID","EQP_ARCHIVES_PID","EQP_ARCHIVES_TYPE","EQP_ARCHIVES_NAME","EQP_ARCHIVES_SORT","EQP_ARCHIVES_STATE","FORM_SETUP_ID","TABLE_SETUP_ID","EQP_ARCHIVES_JSON","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK") VALUES(N'H82BDJLXX',N'33A001',N'H82YXJLXX',N'0',N'比对记录',N'17',N'1',NULL,NULL,N'{"IsUpload":true}',N'H82初始化',to_timestamp('2025-07-07 11:38:16.117869', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 11:38:16.117869', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'初始化数据')  
INTO "XH_OA"."EMS_EQP_ARCHIVES_DICT"  ("EQP_ARCHIVES_ID","HOSPITAL_ID","EQP_ARCHIVES_PID","EQP_ARCHIVES_TYPE","EQP_ARCHIVES_NAME","EQP_ARCHIVES_SORT","EQP_ARCHIVES_STATE","FORM_SETUP_ID","TABLE_SETUP_ID","EQP_ARCHIVES_JSON","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK") VALUES(N'H82XNYZJLXX',N'33A001',N'H82YXJLXX',N'0',N'性能验证记录',N'18',N'1',NULL,NULL,N'{"IsUpload":true}',N'H82初始化',to_timestamp('2025-07-07 11:38:16.117870', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 11:38:16.117870', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'初始化数据')  
INTO "XH_OA"."EMS_EQP_ARCHIVES_DICT"  ("EQP_ARCHIVES_ID","HOSPITAL_ID","EQP_ARCHIVES_PID","EQP_ARCHIVES_TYPE","EQP_ARCHIVES_NAME","EQP_ARCHIVES_SORT","EQP_ARCHIVES_STATE","FORM_SETUP_ID","TABLE_SETUP_ID","EQP_ARCHIVES_JSON","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK") VALUES(N'H82QWRJLXX',N'33A001',N'H82YXJLXX',N'0',N'去污染记录',N'19',N'1',NULL,NULL,N'{"IsUpload":true}',N'H82初始化',to_timestamp('2025-07-07 11:38:16.117874', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 11:38:16.117874', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'初始化数据')  
INTO "XH_OA"."EMS_EQP_ARCHIVES_DICT"  ("EQP_ARCHIVES_ID","HOSPITAL_ID","EQP_ARCHIVES_PID","EQP_ARCHIVES_TYPE","EQP_ARCHIVES_NAME","EQP_ARCHIVES_SORT","EQP_ARCHIVES_STATE","FORM_SETUP_ID","TABLE_SETUP_ID","EQP_ARCHIVES_JSON","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK") VALUES(N'H82BGJLXX',N'33A001',N'H82YXJLXX',N'0',N'变更记录',N'20',N'1',NULL,NULL,N'{"IsUpload":true}',N'H82初始化',to_timestamp('2025-07-07 11:38:16.117875', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 11:38:16.117875', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'初始化数据')  
INTO "XH_OA"."EMS_EQP_ARCHIVES_DICT"  ("EQP_ARCHIVES_ID","HOSPITAL_ID","EQP_ARCHIVES_PID","EQP_ARCHIVES_TYPE","EQP_ARCHIVES_NAME","EQP_ARCHIVES_SORT","EQP_ARCHIVES_STATE","FORM_SETUP_ID","TABLE_SETUP_ID","EQP_ARCHIVES_JSON","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK") VALUES(N'H82BLSJJLXX',N'33A001',N'H82YXJLXX',N'0',N'不良时间',N'21',N'1',NULL,NULL,N'{"IsUpload":true}',N'H82初始化',to_timestamp('2025-07-07 11:38:16.117877', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'H82初始化',to_timestamp('2025-07-07 11:38:16.117877', 'YYYY-MM-DD HH24:MI:SS.FF') ,N'初始化数据')  
SELECT 1 FROM DUAL


2025-07-07 11:38:17.439 +08:00 [INF] 【SQL执行耗时:391.2868ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT" 

2025-07-07 11:38:17.830 +08:00 [INF] 【SQL执行耗时:352.6928ms】

[Sql]:SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON" FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "HOSPITAL_ID" = :HospitalId0 )  AND ( "CLASS_STATE" <> :ClassState1 ) 
[Pars]:
[Name]::HospitalId0 [Value]:33A001 [Type]:String    
[Name]::ClassState1 [Value]:2 [Type]:String    

2025-07-07 11:38:18.244 +08:00 [INF] 【SQL执行耗时:342.944ms】

[Sql]:SELECT "CLASS_ARCHIVES_ID" AS "CLASSARCHIVESID","HOSPITAL_ID" AS "HOSPITALID","EQP_CLASS_ID" AS "EQPCLASSID","EQP_ARCHIVES_ID" AS "EQPARCHIVESID","REMARK" AS "REMARK","LAST_MTIME" AS "LAST_MTIME","LAST_MPERSON" AS "LAST_MPERSON","FIRST_RTIME" AS "FIRST_RTIME","FIRST_RPERSON" AS "FIRST_RPERSON" FROM "XH_OA"."EMS_EQP_CLASS_ARCHIVES"  WHERE   "EQP_ARCHIVES_ID" IN ('H82CLASSJZL','H82CLASSXXL','H82CLASSFZL','H82CLASSGYL','1','4','6','7','11','2','3','12','13','5','10','14','15','16','17','10001','10002','10003','60001','60002','40001','40002','70001','70002','70003','110001','110002','110003','110004','110005','110006','20001','20002','30001','30002','120001','130001','130002','130003','50001','100001','100002','100003','140001','140002','140003','150001','150002','160001','170001')   

2025-07-07 11:38:18.280 +08:00 [INF] HTTP GET /api/EquipmentArchives/GetEquipmentArchives responded 200 in 5378.2710 ms
2025-07-07 11:38:18.280 +08:00 [INF] 【接口超时阀值预警】 [9365acbfd4e6d311bc4f343f2ad72ad3]接口/api/EquipmentArchives/GetEquipmentArchives,耗时:[5378]毫秒
2025-07-07 11:38:21.119 +08:00 [INF] 【SQL执行耗时:348.5172ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQP_ARCHIVES_DICT" 

2025-07-07 11:38:21.518 +08:00 [INF] 【SQL执行耗时:361.9654ms】

[Sql]:SELECT "EQP_ARCHIVES_ID","HOSPITAL_ID","EQP_ARCHIVES_PID","EQP_ARCHIVES_TYPE","EQP_ARCHIVES_NAME","EQP_ARCHIVES_SORT","EQP_ARCHIVES_STATE","FORM_SETUP_ID","TABLE_SETUP_ID","EQP_ARCHIVES_JSON","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_EQP_ARCHIVES_DICT"  WHERE ( "EQP_ARCHIVES_STATE" = :EqpArchivesState0 )  AND ( "EQP_ARCHIVES_STATE" <> :EqpArchivesState1 ) 
[Pars]:
[Name]::EqpArchivesState0 [Value]:1 [Type]:String    
[Name]::EqpArchivesState1 [Value]:2 [Type]:String    

2025-07-07 11:38:22.692 +08:00 [INF] 【SQL执行耗时:331.9715ms】

[Sql]:SELECT "CLASS_ARCHIVES_ID" AS "CLASSARCHIVESID","HOSPITAL_ID" AS "HOSPITALID","EQP_CLASS_ID" AS "EQPCLASSID","EQP_ARCHIVES_ID" AS "EQPARCHIVESID","REMARK" AS "REMARK","LAST_MTIME" AS "LAST_MTIME","LAST_MPERSON" AS "LAST_MPERSON","FIRST_RTIME" AS "FIRST_RTIME","FIRST_RPERSON" AS "FIRST_RPERSON" FROM "XH_OA"."EMS_EQP_CLASS_ARCHIVES"  WHERE   "EQP_ARCHIVES_ID" IN ('H82JBXX','H82EQPARCHIVESHJXX','H82GYSXX','H82SWXX','H82AZXX','H82QTXX','H82BFXX','H82ZZZSXX','H82SBSMSXX','H82SOPDAXX','H82BSKXX','H82YXJLXX','H82SYJLXX','H82BYJLXX','H82WXJLXX','H82JZLLXX','H82BDJLXX','H82XNYZJLXX','H82QWRJLXX','H82BGJLXX','H82BLSJJLXX')   

2025-07-07 11:38:23.330 +08:00 [INF] 【SQL执行耗时:602.5102ms】

[Sql]:SELECT COUNT(1) FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT" 

2025-07-07 11:38:23.704 +08:00 [INF] 【SQL执行耗时:335.6051ms】

[Sql]:SELECT "CLASS_ID","HOSPITAL_ID","PARENT_CLASS_ID","CLASS_NAME","CLASS_SNAME","CLASS_TAG","CLASS_STYLE","CLASS_LEVEL","CLASS_STATE","REMARK","LAST_MTIME","LAST_MPERSON","FIRST_RTIME","FIRST_RPERSON" FROM "XH_OA"."EMS_EQUIPMENT_CLASS_DICT"  WHERE ( "HOSPITAL_ID" = :HospitalId0 )  AND ( "CLASS_STATE" <> :ClassState1 ) 
[Pars]:
[Name]::HospitalId0 [Value]:33A001 [Type]:String    
[Name]::ClassState1 [Value]:2 [Type]:String    

2025-07-07 11:38:24.122 +08:00 [INF] 【SQL执行耗时:349.8227ms】

[Sql]:SELECT "CLASS_ARCHIVES_ID" AS "CLASSARCHIVESID","HOSPITAL_ID" AS "HOSPITALID","EQP_CLASS_ID" AS "EQPCLASSID","EQP_ARCHIVES_ID" AS "EQPARCHIVESID","REMARK" AS "REMARK","LAST_MTIME" AS "LAST_MTIME","LAST_MPERSON" AS "LAST_MPERSON","FIRST_RTIME" AS "FIRST_RTIME","FIRST_RPERSON" AS "FIRST_RPERSON" FROM "XH_OA"."EMS_EQP_CLASS_ARCHIVES"  WHERE   "EQP_ARCHIVES_ID" IN ('H82CLASSJZL','H82CLASSXXL','H82CLASSFZL','H82CLASSGYL','1','4','6','7','11','2','3','12','13','5','10','14','15','16','17','10001','10002','10003','60001','60002','40001','40002','70001','70002','70003','110001','110002','110003','110004','110005','110006','20001','20002','30001','30002','120001','130001','130002','130003','50001','100001','100002','100003','140001','140002','140003','150001','150002','160001','170001')   

2025-07-07 11:38:24.160 +08:00 [INF] HTTP GET /api/EquipmentArchives/GetEquipmentArchives responded 200 in 3435.7588 ms
2025-07-07 11:38:24.160 +08:00 [INF] 【接口超时阀值预警】 [5e15f2dd335f4bc7d39f0c0bc91145ef]接口/api/EquipmentArchives/GetEquipmentArchives,耗时:[3436]毫秒
2025-07-07 14:54:03.967 +08:00 [INF] ==>App Start..2025-07-07 14:54:03
2025-07-07 14:54:04.146 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-07 14:54:04.149 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-07 14:54:06.070 +08:00 [INF] ==>基础连接请求完成.
2025-07-07 14:54:06.455 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-07 14:54:06.832 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-07 14:54:07.150 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-07 14:54:07.152 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-07 14:54:07.485 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-07 14:54:09.339 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-07 14:54:09.761 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-07 14:54:10.243 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-07 14:54:10.243 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-07 14:54:11.414 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-07 14:54:14.171 +08:00 [INF] ==>初始化完成..
2025-07-07 14:54:14.213 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-07 14:54:14.214 +08:00 [INF] 设备启用任务
2025-07-07 14:54:14.214 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-07 14:54:14.616 +08:00 [INF] 【SQL执行耗时:383.4524ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-07 14:54:14.770 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-07 14:54:14.783 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-07 14:54:14.784 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-07 14:54:14.785 +08:00 [INF] Hosting environment: Development
2025-07-07 14:54:14.785 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-07 15:44:56.958 +08:00 [INF] ==>App Start..2025-07-07 15:44:56
2025-07-07 15:44:57.130 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-07 15:44:57.133 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-07 15:44:58.678 +08:00 [INF] ==>基础连接请求完成.
2025-07-07 15:44:59.042 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-07 15:44:59.546 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-07 15:44:59.911 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-07 15:44:59.913 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-07 15:45:00.251 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-07 15:45:00.824 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-07 15:45:00.914 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-07 15:45:01.343 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-07 15:45:01.343 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-07 15:45:02.576 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-07 15:45:05.300 +08:00 [INF] ==>初始化完成..
2025-07-07 15:45:05.328 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-07 15:45:05.331 +08:00 [INF] 设备启用任务
2025-07-07 15:45:05.332 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-07 15:45:05.699 +08:00 [INF] 【SQL执行耗时:346.7044ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-07 15:45:05.851 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-07 15:45:05.865 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-07 15:45:05.866 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-07 15:45:05.866 +08:00 [INF] Hosting environment: Development
2025-07-07 15:45:05.867 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-07 15:45:11.578 +08:00 [ERR] An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Ambiguous HTTP method for action - XH.H82.API.Controllers.EquipmentClassNew.EquipmentArchivesController.AddEquipmentArchives (XH.H82.API). Actions require an explicit HttpMethod binding for Swagger/OpenAPI 3.0
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperations(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePaths(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwagger(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)
2025-07-07 15:45:15.011 +08:00 [INF] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 173.7231 ms
2025-07-07 15:45:15.517 +08:00 [INF] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 2.0957 ms
2025-07-07 15:45:16.808 +08:00 [ERR] An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Ambiguous HTTP method for action - XH.H82.API.Controllers.EquipmentClassNew.EquipmentArchivesController.AddEquipmentArchives (XH.H82.API). Actions require an explicit HttpMethod binding for Swagger/OpenAPI 3.0
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperations(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePaths(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwagger(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)
2025-07-07 15:46:59.887 +08:00 [INF] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5111 ms
2025-07-07 15:47:01.218 +08:00 [ERR] An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Ambiguous HTTP method for action - XH.H82.API.Controllers.EquipmentClassNew.EquipmentArchivesController.AddEquipmentArchives (XH.H82.API). Actions require an explicit HttpMethod binding for Swagger/OpenAPI 3.0
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperations(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePaths(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwagger(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)
2025-07-07 15:47:48.641 +08:00 [INF] ==>App Start..2025-07-07 15:47:48
2025-07-07 15:47:48.810 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-07 15:47:48.814 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-07 15:47:50.249 +08:00 [INF] ==>基础连接请求完成.
2025-07-07 15:47:50.614 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-07 15:47:50.939 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-07 15:47:51.248 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-07 15:47:51.250 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-07 15:47:51.590 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-07 15:47:52.044 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-07 15:47:52.146 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-07 15:47:52.575 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-07 15:47:52.575 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-07 15:47:53.484 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-07 15:47:56.203 +08:00 [INF] ==>初始化完成..
2025-07-07 15:47:56.224 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-07 15:47:56.226 +08:00 [INF] 设备启用任务
2025-07-07 15:47:56.227 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-07 15:47:56.655 +08:00 [INF] 【SQL执行耗时:410.3548ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-07 15:47:56.792 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-07 15:47:56.804 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-07 15:47:56.805 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-07 15:47:56.806 +08:00 [INF] Hosting environment: Development
2025-07-07 15:47:56.806 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-07 15:48:00.774 +08:00 [INF] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 197.8901 ms
2025-07-07 15:48:01.977 +08:00 [ERR] An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Ambiguous HTTP method for action - XH.H82.API.Controllers.EquipmentClassNew.EquipmentArchivesController.AddEquipmentArchives (XH.H82.API). Actions require an explicit HttpMethod binding for Swagger/OpenAPI 3.0
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperations(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePaths(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwagger(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)
2025-07-07 15:50:17.428 +08:00 [INF] ==>App Start..2025-07-07 15:50:17
2025-07-07 15:50:17.596 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-07 15:50:17.600 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-07 15:50:19.154 +08:00 [INF] ==>基础连接请求完成.
2025-07-07 15:50:19.531 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-07 15:50:19.869 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-07 15:50:20.192 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-07 15:50:20.205 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-07 15:50:20.584 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-07 15:50:20.999 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-07 15:50:21.101 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-07 15:50:21.596 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-07 15:50:21.597 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-07 15:50:22.657 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-07 15:50:25.415 +08:00 [INF] ==>初始化完成..
2025-07-07 15:50:25.441 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-07 15:50:25.443 +08:00 [INF] 设备启用任务
2025-07-07 15:50:25.444 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-07 15:50:25.819 +08:00 [INF] 【SQL执行耗时:356.9989ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-07 15:50:25.988 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-07 15:50:26.008 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-07 15:50:26.010 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-07 15:50:26.010 +08:00 [INF] Hosting environment: Development
2025-07-07 15:50:26.011 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-07-07 15:50:32.700 +08:00 [ERR] An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Ambiguous HTTP method for action - XH.H82.API.Controllers.EquipmentClassNew.EquipmentArchivesController.AddEquipmentArchives (XH.H82.API). Actions require an explicit HttpMethod binding for Swagger/OpenAPI 3.0
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperations(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePaths(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwagger(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)
2025-07-07 15:50:48.998 +08:00 [INF] ==>App Start..2025-07-07 15:50:48
2025-07-07 15:50:49.161 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-07-07 15:50:49.164 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-07-07 15:50:53.136 +08:00 [INF] ==>基础连接请求完成.
2025-07-07 15:50:54.552 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-07-07 15:50:54.862 +08:00 [INF]  正在通过S01写入版本信息...
2025-07-07 15:50:55.367 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-07-07 15:50:55.369 +08:00 [INF]  正在通过H04写入版本信息...
2025-07-07 15:50:55.930 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-07-07 15:50:55.948 +08:00 [INF] ==>版本写入成功:6.25.300
2025-07-07 15:50:56.020 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-07-07 15:50:56.440 +08:00 [INF] 正在检查旧数据是否完整...
2025-07-07 15:50:56.440 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-07-07 15:50:57.382 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-07-07 15:51:01.852 +08:00 [INF] ==>初始化完成..
2025-07-07 15:51:01.874 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-07-07 15:51:01.875 +08:00 [INF] 设备启用任务
2025-07-07 15:51:01.875 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-07-07 15:51:02.241 +08:00 [INF] 【SQL执行耗时:347.0126ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-07-07 15:51:02.377 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-07 15:51:02.392 +08:00 [INF] Now listening on: https://[::]:18482
2025-07-07 15:51:02.393 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-07 15:51:02.393 +08:00 [INF] Hosting environment: Development
2025-07-07 15:51:02.394 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
