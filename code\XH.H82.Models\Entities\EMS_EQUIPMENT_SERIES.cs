﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using H.Utility.SqlSugarInfra;
using SqlSugar;

namespace XH.H82.Models.Entities
{
    [DBOwner("XH_OA")]
    public class EMS_EQUIPMENT_SERIES
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string ESERIES_ID { get; set; }
        public string UNIT_ID { get; set; }
        public string HOSPITAL_ID { get; set; }
        public string ESERIES_CLASS { get; set; }
        public string ESERIES_NAME { get; set; }
        public string ESERIES_SORT { get; set; }
        public string ESERIES_STATE { get; set; }
        public string FIRST_RPERSON { get; set; }
        public string FIRST_RTIME { get; set; }
        public string LAST_MPERSON { get; set; }
        public string LAST_MTIME { get; set; }
        public string REMARK { get; set; }
    }
}
