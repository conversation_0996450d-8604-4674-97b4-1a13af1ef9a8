﻿using H.BASE;
using H.Utility;
using Microsoft.AspNetCore.Mvc;
using XH.H82.API.Extensions;
using XH.H82.IServices;
using XH.H82.IServices.EquipmentClassNew;
using XH.H82.IServices.TemplateDesign;
using XH.H82.Models.BusinessModuleClient.H04;
using XH.H82.Models.Dtos;
using XH.H82.Models.EquipmengtClassNew;

namespace XH.H82.API.Controllers.EquipmentClassNew;

/// <summary>
/// 设备类型新版本
/// </summary>
public class EquipmentClassNewController : SuperController
{
    
    private readonly IEquipmentClassService _equipmentClassNewService;
    private readonly ITemplateDesignService _templateDesignService ;
    private readonly IConfiguration _configuration;
    private readonly ISystemService _systemService;

    public EquipmentClassNewController(IEquipmentClassService equipmentClassNewService, ITemplateDesignService templateDesignService, IConfiguration configuration, ISystemService systemService)
    {
        _equipmentClassNewService = equipmentClassNewService;
        _templateDesignService = templateDesignService;
        _configuration = configuration;
        _systemService = systemService;
    }


    /// <summary>
    /// 查询设备类字典树
    /// </summary>
    /// <param name="hasAll">是否需要“全部设备节点” 默认需要</param>
    /// <param name="isEdit">是否编辑</param>
    /// <returns></returns>
    [HttpGet]
    public IActionResult GetEquipmentClassDictTree(bool hasAll = true , bool isEdit = false)
    {
        var result = _equipmentClassNewService.GetEquipmentClassDictTree(hasAll,isEdit);
        

        return Ok(result.Success());
    }

    

    /// <summary>
    /// 添加细分类设备类型
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    public IActionResult AddEquipmentClassDict(AddEquipmentClassDto input)
    {
        var  result = _equipmentClassNewService.AddClassDict(input);
        return Ok(result.Success());
    }


    /// <summary>
    /// 更新细分类设备类型
    /// </summary>
    /// <param name="classId">设备类型id</param>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPut("{classId}")]
    public IActionResult UpdateEquipmentClassDict(string classId,AddEquipmentClassDto input)
    {
        var  result = _equipmentClassNewService.UpdateClassDict(classId,input);
        return Ok(result.Success());
    }
    
    /// <summary>
    /// 停用/启用设备类型
    /// </summary>
    /// <returns></returns>
    [HttpPost("{classId}")]
    [CustomResponseType(typeof(bool))]
    public IActionResult EnableOrDisableEquipmentClassDict(string classId)
    {
        _equipmentClassNewService.DisableOrEnableClassDict(classId);
        return Ok(true.Success());
    }
    
    
    /// <summary>
    /// 删除设备类型字典
    /// </summary>
    /// <returns></returns>
    [HttpDelete("{classId}")]
    [CustomResponseType(typeof(bool))]
    public IActionResult DeleteEquipmentCodeCustomDict(string classId)
    {
        _equipmentClassNewService.DeleteClassDict(classId);
        return Ok(true.Success());
    }

    /// <summary>
    /// 查询设备类型字典已关联的档案
    /// </summary>
    /// <param name="classId"></param>
    /// <returns></returns>
    [HttpGet("{classId}")]
    [CustomResponseType(typeof(List<ClassLinkArchivesDto>))]
    public IActionResult GetEquipmentArchivesLinkByClassId(string classId)
    {
        var  archives = _equipmentClassNewService.GetEquipmentArchivesLinkByClassId(classId);
        var  result = archives.Select(x => new ClassLinkArchivesDto(x));
        return Ok(result.Success());
    }
    
    /// <summary>
    /// 查询设备类型字典未关联的档案
    /// </summary>
    /// <param name="classId"></param>
    /// <returns></returns>
    [HttpGet("{classId}")]
    [CustomResponseType(typeof(List<ClassLinkArchivesDto>))]
    public IActionResult GetEquipmentArchivesNotLinkByClassId(string classId)
    {
        var  archives = _equipmentClassNewService.GetEquipmentArchivesNotLinkByClassId(classId);
        var  result = archives.Select(x => new ClassLinkArchivesDto(x));
        return Ok(result.Success());
    }

    
    /// <summary>
    /// 建立关联设备类型和档案记录
    /// </summary>
    /// <param name="classId">设备类型id</param>
    /// <param name="archivesIds">档案记录ids</param>
    /// <returns></returns>
    [HttpPost("{classId}")]
    [CustomResponseType(typeof(bool))]
    public IActionResult LinkArchivesByClassId(string classId , string[] archivesIds)
    {
        var  result = _equipmentClassNewService.LinkClassAndArchives(classId ,archivesIds);
        return Ok(result.Success());
    }
    
    
    /// <summary>
    /// 取消建立关联设备类型和档案记录
    /// </summary>
    /// <param name="classId">设备类型id</param>
    /// <param name="archivesIds">档案记录ids</param>
    /// <returns></returns>
    [HttpPost("{classId}")]
    [CustomResponseType(typeof(bool))]
    public IActionResult UnLinkArchivesByClassId(string classId , string[] archivesIds)
    {
        var  result = _equipmentClassNewService.UnLinkClassAndArchives(classId ,archivesIds);
        return Ok(result.Success());
    }

    /// <summary>
    /// 复杂表单的属性字典
    /// </summary>
    /// <param name="filedClass"></param>
    /// <returns></returns>
    [HttpGet]
    [CustomResponseType(typeof(List<FieldDictDto>))]
    public IActionResult GetFieldDicts(string filedClass = "基本信息")
    {
        var  result = _templateDesignService.GetFieldDicts(filedClass);
        return Ok(result.Success());
    }
    
    /// <summary>
    /// 添加设备类型字典的复杂表单模板
    /// </summary>
    /// <param name="classId"></param>
    /// <returns></returns>
    [HttpPost("{classId}")]
    [CustomResponseType(typeof(bool))]
    public IActionResult CreateComplexForms(string classId)
    {
          var result = _templateDesignService.CreateComplexForms(classId,"H82_10003");
          return Ok(result.Success());
    }
    
    /// <summary>
    /// 查询复杂表单
    /// </summary>
    /// <param name="classId"></param>
    /// <param name="merge"></param>
    /// <param name="qunitId"></param>
    /// <returns></returns>
    [HttpGet("{classId}")]
    [CustomResponseType(typeof(bool))]
    public IActionResult GetComplexForms(string classId, string merge, string?qunitId)
    {
          var result = _templateDesignService.GetComplexForms(classId , merge,qunitId);
        
          return Ok(result.Success());
    }


    /// <summary>
    /// 初始化字段库数据
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [CustomResponseType(typeof(bool))]
    public IActionResult InitBaseInfoFieldDict()
    {
        var result = _templateDesignService.InitBaseInfoFieldDict();
        return Ok(result);
    }
    
    
    /// <summary>
    /// 复杂表单基础模板新增/保存
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [CustomResponseType(typeof(bool))]
    public IActionResult SaveFieldDict(FieldDictDto input)
    {
        var result = _templateDesignService.SaveFieldDic(input);
        return Ok(result.Success());
    }
    
    
    /// <summary>
    /// 删除复杂表单基础模板
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpDelete]
    [CustomResponseType(typeof(bool))]
    public IActionResult DeleteFieldDict(FieldDictDto input)
    {
        input.FIELD_STATE = "2";
        var result = _templateDesignService.SaveFieldDic(input);
        return Ok(result.Success());
    }


    /// <summary>
    /// 获取工具箱的页面设备url
    /// </summary>
    /// <param name="setupId"></param>
    /// <returns></returns>
    [HttpGet]
    [CustomResponseType(typeof(string))]
    public IActionResult GetToolBoxUrl(string setupId)
    {

        var user = User.ToClaimsDto();
        var complexUrl = _configuration["H04"];
        var toolboxUrl = _configuration["H04-01"];
        var toolBoxUrl = $"{toolboxUrl}";
        complexUrl = complexUrl.Replace("18013", "18214");
        var complexFormsUrl = $"{complexUrl}/page-setting/component-setting?SETUP_ID=H82_{setupId}&FUNC_ID=H8253&merge=1&MODULE_ID={AppSettingsProvider.CurrModuleId}&HOSPITAL_ID={user.HOSPITAL_ID}";

        var tokenResult =
            _systemService.GetIssueTokenInfo(user.USER_NO, Guid.NewGuid().ToString(), "H04-01", "");
        
        var account = StringExtensions.FromJsonString<AccountDto>(tokenResult.data.ToString());
        
        var result = new
        {
            complexFormsUrl,
            toolBoxUrl,
            account.AccessToken,
            account.RefreshToken,
            complexFormsAddress = $"{complexFormsUrl}&refresh_token={account.RefreshToken}&access_token={account.AccessToken}",
            toolBoxAddress = $"{toolBoxUrl}&refresh_token={account.RefreshToken}&access_token={account.AccessToken}"
        };
        return Ok(result.Success());
    }
    
    

    
}
