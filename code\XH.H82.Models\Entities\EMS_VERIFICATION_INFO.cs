﻿using System.ComponentModel.DataAnnotations;
using H.Utility.SqlSugarInfra;
using SqlSugar;

namespace XH.H82.Models.Entities
{
    [DBOwner("XH_OA")]
    public class EMS_VERIFICATION_INFO : IBeRecordEvent
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string VERIFICATION_ID { get; set; }
        public string HOSPITAL_ID { get; set; }
        public string VERIFICATION_NO { get; set; }
        [Required(ErrorMessage ="设备id不能为空")]
        public string EQUIPMENT_ID { get; set; }
        public DateTime? VERIFICATION_DATE { get; set; }
        public string RELATION_EVENT { get; set; }
        public string VERIFICATION_PERSON { get; set; }
        public string VERIFICATION_RESULT { get; set; }
        public string VERIFICATION_STATE { get; set; }
        public string FIRST_RPERSON { get; set; }
        public DateTime? FIRST_RTIME { get; set; }
        public string LAST_MPERSON { get; set; }
        public DateTime? LAST_MTIME { get; set; }
        public string STATE { get; set; }
        public string REMARK { get; set; }

        public string GetId()
        {
            return VERIFICATION_ID;
        }
        public string GetEquipmentId()
        {
            return EQUIPMENT_ID;
        }
        public (string Type, string TypeName) GetType()
        {
            return ("2", "验证");
        }

        public DateTime? GetRecordDate()
        {
            return VERIFICATION_DATE;
        }
    }
}
