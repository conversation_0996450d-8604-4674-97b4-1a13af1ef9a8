﻿using H.Utility;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using RestSharp;
using Serilog;
using Spire.Doc;
using System;
using System.Diagnostics;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;
using XH.H82.Models.Card;

namespace XH.H82.Models.BusinessModuleClient
{
    public class H115Client
    {

        private string addressH115 { get; set; } = "";
        private IHttpContextAccessor _httpContext;
        public H115Client(string ip, IHttpContextAccessor httpContext)
        {
            if (ip.IsNullOrEmpty())
            {
                throw new ArgumentNullException("addressH115 为空");
            }
            addressH115 = ip;
            _httpContext = httpContext;

        }

        public ResultDto ChangeDisplay(EInkChangeDisplayDto body)
        {
            try
            {
                var url = $"api/EInkDevice/ChangeDisplay";
                var result = H115ClientPost(url, body, false);
#if DEBUG

                Console.WriteLine(JsonConvert.SerializeObject(body));
                Console.WriteLine(JsonConvert.SerializeObject(result));
#endif
                return result;
            }
            catch (Exception ex)
            {
                return new ResultDto()
                {
                    success = false,
                    msg = ex.Message,
                };
            }
        }


        /// <summary>
        /// 生成图片base64
        /// </summary>
        /// <param name="body"></param>
        /// <returns></returns>
        public ResultDto GetGenerateImgBase64(EInkChangeDisplayDto body)
        {

            try
            {
                var url = $"api/EInkDevice/GetGenerateImgBase64";
                var result = H115ClientPost(url, body, false);
#if DEBUG

                Console.WriteLine(JsonConvert.SerializeObject(body));
                Console.WriteLine(JsonConvert.SerializeObject(result));
#endif
                return result;
            }
            catch (Exception ex)
            {
                return new ResultDto()
                {
                    success = false,
                    msg = ex.Message,
                };
            }

        }

        /// <summary>
        /// 生成图片流
        /// </summary>
        /// <param name="body"></param>
        /// <returns></returns>
        public byte[] GetGenerateImgStream(EInkChangeDisplayDto body)
        {

            var url = $"api/EInkDevice/GetGenerateImgStream";
            var result = H115ClientPostStream(url, body, false);
#if DEBUG

            Console.WriteLine(JsonConvert.SerializeObject(body));
            Console.WriteLine(JsonConvert.SerializeObject(result));
#endif
            return result;

        }

        public byte[] H115ClientPostStream<T>(string url, T requestBody = default(T), bool isNeedToken = true)
        {
            
            if (addressH115.IsNullOrEmpty())
            {
                throw new BizException("addressH115 为空");
            }

            using RestClient client = new RestClient(new RestClientOptions
            {
                RemoteCertificateValidationCallback = (object sender, X509Certificate? certificate, X509Chain? chain, SslPolicyErrors sslPolicyErrors) => true,
                BaseUrl = new Uri(addressH115),
                ThrowOnAnyError = true
            });
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            RestRequest request = new RestRequest(url);
            if (requestBody != null)
            {
                request.AddBody(requestBody);
            }

            if (isNeedToken)
            {
                string token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();
                request.AddHeader("Authorization", token);
            }
            try
            {
                RestResponse restResponse = client.ExecutePost(request);
                stopwatch.Stop();
                Log.ForContext("elapsed", stopwatch.ElapsedMilliseconds).Information($"调用H115模块[{url}],耗时:{stopwatch.ElapsedMilliseconds}ms");
                if (restResponse.IsSuccessful)
                {
                    return restResponse.RawBytes;
                }

                Log.Error($"调用H115模块[{url}]发生错误:{restResponse.ErrorException}");
                throw new BizException($"调用H115模块[{url}]发生错误:{restResponse.ErrorException}", restResponse.ErrorException);
            }
            catch (Exception ex)
            {
                Log.Error($"调用H115模块[{url}]发生错误:{ex}");
                throw new BizException(ex.Message);
            }
        }


        public ResultDto H115ClientGet<T>(string url, T requestBody = default(T), bool isNeedToken = true)
        {
            if (addressH115.IsNullOrEmpty())
            {
                throw new ArgumentNullException("addressH115 为空");
            }

            using RestClient client = new RestClient(new RestClientOptions
            {
                RemoteCertificateValidationCallback = (object sender, X509Certificate? certificate, X509Chain? chain, SslPolicyErrors sslPolicyErrors) => true,
                BaseUrl = new Uri(addressH115),
                ThrowOnAnyError = true
            });
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            string value = _httpContext.HttpContext.Request.Headers.Authorization.ToString();
            RestRequest request = new RestRequest(url);
            if (requestBody != null)
            {
                request.AddBody(requestBody);
            }
            if (isNeedToken)
            {
                request.AddHeader("Authorization", value);
            }
            try
            {
                RestResponse<ResultDto> restResponse = client.ExecuteGet<ResultDto>(request);
                stopwatch.Stop();
                Log.ForContext("elapsed", stopwatch.ElapsedMilliseconds).Information($"调用H115模块[{url}],耗时:{stopwatch.ElapsedMilliseconds}ms");
                if (restResponse.IsSuccessful && restResponse.Data.success)
                {
                    return restResponse.Data;
                }

                Log.Error($"调用H115模块[{url}]发生错误:{restResponse.ErrorException}");
                throw new BizException($"调用H115模块[{url}]发生错误:{restResponse.ErrorException}", restResponse.ErrorException);
            }
            catch (Exception ex)
            {
                Log.Error($"调用H115模块[{url}]发生错误:{ex}");
                throw new BizException(ex.Message);
            }
        }

        public ResultDto H115ClientPost<T>(string url, T requestBody = default(T), bool isNeedToken = true)
        {

            if (addressH115.IsNullOrEmpty())
            {
                throw new BizException("addressH115 为空");
            }

            using RestClient client = new RestClient(new RestClientOptions
            {
                RemoteCertificateValidationCallback = (object sender, X509Certificate? certificate, X509Chain? chain, SslPolicyErrors sslPolicyErrors) => true,
                BaseUrl = new Uri(addressH115),
                ThrowOnAnyError = true
            });
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            RestRequest request = new RestRequest(url);
            if (requestBody != null)
            {
                request.AddBody(requestBody);
            }

            if (isNeedToken)
            {
                string token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();
                request.AddHeader("Authorization", token);
            }
            try
            {
                RestResponse<ResultDto> restResponse = client.ExecutePost<ResultDto>(request);
                stopwatch.Stop();
                Log.ForContext("elapsed", stopwatch.ElapsedMilliseconds).Information($"调用H115模块[{url}],耗时:{stopwatch.ElapsedMilliseconds}ms");
                if (restResponse.IsSuccessful && restResponse.Data.success)
                {
                    return restResponse.Data;
                }

                Log.Error($"调用H115模块[{url}]发生错误:{restResponse.ErrorException}");
                throw new BizException($"调用H115模块[{url}]发生错误:{restResponse.ErrorException}", restResponse.ErrorException);
            }
            catch (Exception ex)
            {
                Log.Error($"调用H115模块[{url}]发生错误:{ex}");
                throw new BizException(ex.Message);
            }
        }

        ////api/EInkDevice/QueryDeviceMac
        public List<DeviceInfo> QueryDeviceMac()
        {
            try
            {

                var url = $"api/EInkDevice/QueryDeviceMac";
                var rsp = H115ClientGet<object>(url, null, false);
#if DEBUG
                Console.WriteLine(JsonConvert.SerializeObject(rsp.data));
#endif
                var result = JsonConvert.DeserializeObject<List<DeviceInfo>>(rsp.data.ToString());
                return result;
            }
            catch (Exception ex)
            {
                Log.Error($"api/EInkDevice/QueryDeviceMa:{ex.Message}");
                return new List<DeviceInfo>();
            }
        }


        /// <summary>
        /// 查询设备电量
        /// </summary>
        /// <returns></returns>
        public List<EInkItemsItem> QueryDevice()
        {
            try
            {
                var url = $"api/EInkDevice/QueryDevice";
                var rsp = H115ClientGet<object>(url, null, false);

                var result = JsonConvert.DeserializeObject<List<EInkItemsItem>>(rsp.data.ToString());
                return result;
            }
            catch (Exception ex)
            {
                Log.Error($"api/EInkDevice/QueryDevice:{ex.Message}");
                return new List<EInkItemsItem>();
            }
        }


        public void ClosseseLED(string mac)
        {
            try
            {
                var url = $"/api/EInkDevice/CloseLed?mac={mac}";
                var rsp = H115ClientGet<object>(url, null, false);
                if (rsp.success)
                {
                    return;
                }
                else
                {
                    throw new BizException("熄灯灯失败");
                }
            }
            catch (Exception ex)
            {
                Log.Error($"api/EInkDevice/ChangeLed:{ex.Message}");
                throw ex;
            }

        }

        public void LightRedLED(string mac)
        {
            try
            {
                var url = $"/api/EInkDevice/ChangeLed?mac={mac}&rgb=FF0000";
                var rsp = H115ClientGet<object>(url, null, false);
                if (rsp.success)
                {
                    return;
                }
                else
                {
                    throw new BizException("亮灯失败");
                }
            }
            catch (Exception ex)
            {
                Log.Error($"api/EInkDevice/ChangeLed:{ex.Message}");
                throw ex;
            }
        }


        public class EInkResponse<T>
        {
            /// <summary>
            /// 
            /// </summary>
            public int code { get; set; }
            /// <summary>
            /// EInkData或者EInkMsgData或者EInkTokenData
            /// </summary>
            public T data { get; set; }
        }

        public class EInkTokenData
        {
            public string token { get; set; }

        }
        public class EInkMsgData
        {
            public string msgId { get; set; }
            public string mac { get; set; }
        }

        public class EInkData
        {
            /// <summary>
            /// 
            /// </summary>
            public List<EInkItemsItem> items { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public int total { get; set; }
        }
        public class EInkLedColor
        {
            /// <summary>
            /// 
            /// </summary>
            public int red { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public int green { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public int blue { get; set; }
        }

        public class EInkLed
        {
            /// <summary>
            /// 
            /// </summary>
            public EInkLedColor color { get; set; }
        }

        public class EInkStation
        {
            /// <summary>
            /// 
            /// </summary>
            public string ssid { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string password { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public int rssi { get; set; }
        }

        public class EInkMqtt
        {
            /// <summary>
            /// 
            /// </summary>
            public string broker { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public int port { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string username { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string password { get; set; }
        }

        public class EInkDisplay
        {
            /// <summary>
            /// 
            /// </summary>
            public int fail { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public int success { get; set; }
        }

        public class EInkNetwork
        {
            /// <summary>
            /// 
            /// </summary>
            public int conn { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public int conn_fail { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public int discon { get; set; }
        }

        public class EInkSms
        {
            /// <summary>
            /// 
            /// </summary>
            public EInkDisplay display { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public EInkNetwork network { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public int reboot { get; set; }
        }

        public class EinkImages
        {
            /// <summary>
            /// 
            /// </summary>
            public string binFile { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string imageFile { get; set; }
        }

        public class EInkDevtype
        {
            /// <summary>
            /// 
            /// </summary>
            public string _id { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public int type { get; set; }
            /// <summary>
            /// WiFi门牌
            /// </summary>
            public string name { get; set; }
        }

        public class EInkCreatedBy
        {
            /// <summary>
            /// 
            /// </summary>
            public string _id { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string username { get; set; }
        }

        public class EInkProduct
        {
            /// <summary>
            /// 
            /// </summary>
            public string _id { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string name { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string apiKey { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public EInkCreatedBy createdBy { get; set; }
        }

        public class EInkScreentype
        {
            /// <summary>
            /// 
            /// </summary>
            public string _id { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public int type { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string name { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public int width { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public int height { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public int color { get; set; }
        }

        public class EInkAlgorithm
        {
            /// <summary>
            /// 
            /// </summary>
            public string _id { get; set; }
            /// <summary>
            /// 二值化算法
            /// </summary>
            public string name { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string algorithm { get; set; }
        }

        public class EInkItemsItem
        {
            public double voltagePct { get; set; }
            public bool redLightStatus { get; set; }

            /// <summary>
            /// 
            /// </summary>
            public EInkLed led { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public EInkStation station { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public EInkMqtt mqtt { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public EInkSms sms { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public EinkImages images { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string _id { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string mac { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public int __v { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string @alias { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string createdAt { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string createdBy { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string devId { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public EInkDevtype devtype { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public int hw { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string ip { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public EInkProduct product { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public EInkScreentype screentype { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string sn { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public bool status { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public int sw { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string updatedAt { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public bool usbState { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public int voltage { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public EInkAlgorithm algorithm { get; set; }
        }



        public class DeviceInfo
        {
            public string mac { get; set; }
            public string aliasName { get; set; }
        }
    }
}
