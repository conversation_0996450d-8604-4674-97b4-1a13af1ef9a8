﻿using H.BASE.SqlSugarInfra;
using XH.H82.Models.BusinessModuleClient;
using XH.H82.Models.Entities;
using XH.H82.Models.Entities.Common;
using XH.H82.Models.Entities.FileTemplate;
using XH.H82.Models.Entities.InkScreen;
using XH.H82.Models.Entities.OperationLog;
using XH.H82.Models.Entities.THS;
using XH.H82.Models.Entities.Tim;
using XH.H82.Models.Entities.Transaction;
using XH.H82.Models.Entities.WarnRecord;
using XH.H82.Models.Smbl;
using XH.LAB.UTILS.Models;

namespace XH.H82.Models.SugarDbContext
{
    public class SugarDbContext_Base : SugarDbContext_Base_Utils
    {
        public virtual SqlSugarDbSet<EMS_SUBSCRIBE_INFO> EMS_SUBSCRIBE_INFO { get; set; }
        public virtual SqlSugarDbSet<EMS_PURCHASE_INFO> EMS_PURCHASE_INFO { get; set; }
        public virtual SqlSugarDbSet<EMS_UNPACK_INFO> EMS_UNPACK_INFO { get; set; }
        public virtual SqlSugarDbSet<EMS_INSTALL_INFO> EMS_INSTALL_INFO { get; set; }
        public virtual SqlSugarDbSet<EMS_DEBUG_INFO> EMS_DEBUG_INFO { get; set; }
        public virtual SqlSugarDbSet<EMS_TRAIN_INFO> EMS_TRAIN_INFO { get; set; }
        public virtual SqlSugarDbSet<EMS_AUTHORIZE_INFO> EMS_AUTHORIZE_INFO { get; set; }
        public virtual SqlSugarDbSet<EMS_STARTUP_INFO> EMS_STARTUP_INFO { get; set; }
        public virtual SqlSugarDbSet<EMS_EQUIPMENT_INFO> EMS_EQUIPMENT_INFO { get; set; }
        public virtual SqlSugarDbSet<EMS_ENVI_REQUIRE_INFO> EMS_ENVI_REQUIRE_INFO { get; set; }
        public virtual SqlSugarDbSet<EMS_PARTS_INFO> EMS_PARTS_INFO { get; set; }
        public virtual SqlSugarDbSet<EMS_COMPANY_CONTACT> EMS_COMPANY_CONTACT { get; set; }
        public virtual SqlSugarDbSet<SYS6_SETUP> SYS_SETUP { get; set; }
        public virtual SqlSugarDbSet<SYS6_SETUP_DICT> SYS_SETUP_DICT { get; set; }
        public virtual SqlSugarDbSet<EMS_START_STOP> EMS_START_STOP { get; set; }
        public virtual SqlSugarDbSet<SYS6_LABAREA_DICT> SYS6_LABAREA_DICT { get; set; }
        public virtual SqlSugarDbSet<LIS6_PIPELINING_INSTRUMENT> LIS6_PIPELINING_INSTRUMENT { get; set; }
        //public virtual SqlSugarDbSet<SYS_USER> SYS_USER { get; set; }
        public virtual SqlSugarDbSet<SYS6_COMPANY_CONTACT> SYS6_COMPANY_CONTACT { get; set; }
        public virtual SqlSugarDbSet<SYS6_INSPECTION_AREA> SYS6_INSPECTION_AREA { get; set; }
        public virtual SqlSugarDbSet<EMS_MAINTAIN_INFO> EMS_MAINTAIN_INFO { get; set; }
        public virtual SqlSugarDbSet<EMS_REPAIR_INFO> EMS_REPAIR_INFO { get; set; }
        public virtual SqlSugarDbSet<EMS_COMPARISON_INFO> EMS_COMPARISON_INFO { get; set; }
        public virtual SqlSugarDbSet<EMS_CORRECT_INFO> EMS_CORRECT_INFO { get; set; }
        public virtual SqlSugarDbSet<EMS_VERIFICATION_INFO> EMS_VERIFICATION_INFO { get; set; }

        public virtual SqlSugarDbSet<EMS_WORK_PLAN> EMS_WORK_PLAN { get; set; }
        public virtual SqlSugarDbSet<TEST_START_TEMPLATE> TEST_START_TEMPLATE { get; set; }
        public virtual SqlSugarDbSet<LIS_REQUISITION_INFO> LIS_REQUISITION_INFO { get; set; }

        public virtual SqlSugarDbSet<SYS6_MENU_INFO_DICT> SYS6_MENU_INFO_DICT { get; set; }
        public virtual SqlSugarDbSet<SYS6_MATERIAL_INFO> SYS6_MATERIAL_INFO { get; set; }
        public virtual SqlSugarDbSet<LIS6_SINSTRUMENT_INFO> LIS6_SINSTRUMENT_INFO { get; set; }
        public virtual SqlSugarDbSet<LIS6_INSTRUMENT_ITEM> LIS6_INSTRUMENT_ITEM { get; set; }
        public virtual SqlSugarDbSet<LIS6_INSTRUMENT_INFO> LIS6_INSTRUMENT_INFO { get; set; }


        public virtual SqlSugarDbSet<EMS_CHANGE_INFO> EMS_CHANGE_INFO { get; set; }
        public virtual SqlSugarDbSet<EMS_SCRAP_INFO> EMS_SCRAP_INFO { get; set; }
        public virtual SqlSugarDbSet<EMS_DOC_INFO> EMS_DOC_INFO { get; set; }
        public virtual SqlSugarDbSet<EMS_SCRAP_LOG> EMS_SCRAP_LOG { get; set; }
        public virtual SqlSugarDbSet<SYS6_COMPANY_INFO> SYS6_COMPANY_INFO { get; set; }
        public virtual SqlSugarDbSet<EMS_EQUIPMENT_CONTACT> EMS_EQUIPMENT_CONTACT { get; set; }
        public virtual SqlSugarDbSet<DMIS_SYS_DOC> DMIS_SYS_DOC { get; set; }
        public virtual SqlSugarDbSet<DMIS_SYS_FILE> DMIS_SYS_FILE { get; set; }
        public virtual SqlSugarDbSet<TIM_WORK_FORM> TIM_WORK_FORM { get; set; }
        public virtual SqlSugarDbSet<TIM_WORK_FORM_ISSUE> TIM_WORK_FORM_ISSUE { get; set; }

        public virtual SqlSugarDbSet<SYS6_INSPECTION_PGROUP> SYS6_INSPECTION_PGROUP { get; set; }

        public virtual SqlSugarDbSet<SYS6_INSPECTION_LAB> SYS6_INSPECTION_LAB { get; set; }

        public virtual SqlSugarDbSet<LIS6_INSTRUMENT_ITEM_REAGENT> LIS6_INSTRUMENT_ITEM_REAGENT { get; set; }

        public virtual SqlSugarDbSet<EMS_OPER_LOG> CirculationRecords { get; set; }

        public virtual SqlSugarDbSet<EMS_INKSCREEN_TEMPLATE> InkscreenTemplates { get; set; }
        public virtual SqlSugarDbSet<EMS_IMPLEMENT_INFO> EMS_IMPLEMENT_INFO { get; set; }
        public virtual SqlSugarDbSet<SMBL_HOSPITAL> SMBL_HOSPITAL { get; set; }
        /// <summary>
        /// 不良事件主表
        /// </summary>
        public virtual SqlSugarDbSet<AER_ADVERSE_EVENT> AER_ADVERSE_EVENT { get; set; }
        /// <summary>
        /// 不良事件主体
        /// </summary>
        public virtual SqlSugarDbSet<AER_EVENT_SUBJECT> AER_EVENT_SUBJECT { get; set; }



        /// <summary>
        /// 人员基本信息
        /// </summary>
        public virtual SqlSugarDbSet<SYS6_USER> SYS6_USER { get; set; }
        public virtual SqlSugarDbSet<SYS6_USER_PGROUP> SYS6_USER_PGROUP { get; set; }




        public virtual SqlSugarDbSet<SYS6_SOFT_MODULE_INFO> SYS6_SOFT_MODULE_INFO { get; set; }
        public virtual SqlSugarDbSet<SYS6_MENU> SYS6_MENU { get; set; }
        public virtual SqlSugarDbSet<SYS6_SERVER_INFO> SYS6_SERVER_INFO { get; set; }

        /// <summary>
        /// 固定基础数据
        /// </summary>
        public virtual SqlSugarDbSet<SYS6_BASE_DATA> SYS6_BASE_DATA { get; set; }
        public virtual SqlSugarDbSet<SYS6_POST_ROLE_COM> SYS6_POST_ROLE_COM { get; set; }
        public virtual SqlSugarDbSet<SYS6_ROLE_COM_LIST> SYS6_ROLE_COM_LIST { get; set; }
        public virtual SqlSugarDbSet<SYS6_ROLE_MENU> SYS6_ROLE_MENU { get; set; }
        public virtual SqlSugarDbSet<LIS6_INSPECTION_GROUP> LIS6_INSPECTION_GROUP { get; set; }

        public virtual SqlSugarDbSet<OA_FIELD_DICT> OA_FIELD_DICT { get; set; }
        public virtual SqlSugarDbSet<OA_EXCEL_STYLE_TEMPLATE> OA_EXCEL_STYLE_TEMPLATE { get; set; }
        public virtual SqlSugarDbSet<OA_OFFICE_STYLE_TEMPLATE> OA_OFFICE_STYLE_TEMPLATE { get; set; }
        /// <summary>
        /// 科室信息
        /// </summary>
        public virtual SqlSugarDbSet<SYS6_INSPECTION_LAB> LIS_INSPECTION_LAB { get; set; }
        public virtual SqlSugarDbSet<SYS6_POST> SYS6_POST { get; set; }
        public virtual SqlSugarDbSet<SYS6_USER_POST> SYS6_USER_POST { get; set; }


        public virtual SqlSugarDbSet<EMS_INKSCREEN_INFO> EMS_INKSCREEN_INFO { get; set; }
        public virtual SqlSugarDbSet<EMS_EQUIPMENT_WARN> EMS_EQUIPMENT_WARN { get; set; }
        public virtual SqlSugarDbSet<OA_BASE_DATA> OA_BASE_DATA { get; set; }
        public virtual SqlSugarDbSet<THS_MONITOR_HOUR> THS_MONITOR_HOUR { get; set; }
        public virtual SqlSugarDbSet<THS_ABNORMAL_OPER_LIST> THS_ABNORMAL_OPER_LIST { get; set; }




    }
    public class SugarDbContext_Master : SugarDbContext_Base
    {

    }

    public class SugarDbContext_Slave : SugarDbContext_Base
    {

    }

    public class SugarDbContext_Master2 : SugarDbContext_Base
    {

    }


    public class SugarDbContext_Slave2 : SugarDbContext_Base
    {

    }
}
