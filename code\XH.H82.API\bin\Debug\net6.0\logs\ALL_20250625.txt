2025-06-25 10:34:43.311 +08:00 [INF] ==>App Start..2025-06-25 10:34:43
2025-06-25 10:34:43.510 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-06-25 10:34:43.513 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-06-25 10:34:45.636 +08:00 [INF] ==>基础连接请求完成.
2025-06-25 10:34:46.033 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-06-25 10:34:46.430 +08:00 [INF]  正在通过S01写入版本信息...
2025-06-25 10:34:46.855 +08:00 [INF] ==>S01版本写入成功:6.25.300
2025-06-25 10:34:49.279 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-06-25 10:34:49.815 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-06-25 10:34:50.521 +08:00 [INF] 正在检查旧数据是否完整...
2025-06-25 10:34:50.522 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-06-25 10:34:51.848 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-06-25 10:34:54.152 +08:00 [INF] ==>初始化完成..
2025-06-25 10:34:54.208 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-06-25 10:34:54.210 +08:00 [INF] 设备启用任务
2025-06-25 10:34:54.210 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-06-25 10:34:54.618 +08:00 [INF] 【SQL执行耗时:375.5587ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-06-25 10:34:54.802 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-06-25 10:34:54.818 +08:00 [INF] Now listening on: https://[::]:18482
2025-06-25 10:34:54.820 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-25 10:34:54.820 +08:00 [INF] Hosting environment: Development
2025-06-25 10:34:54.820 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-06-25 17:47:43.892 +08:00 [INF] ==>App Start..2025-06-25 17:47:43
2025-06-25 17:47:44.098 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-06-25 17:47:44.102 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-06-25 17:47:46.153 +08:00 [INF] ==>基础连接请求完成.
2025-06-25 17:47:46.555 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-06-25 17:47:47.043 +08:00 [INF]  正在通过S01写入版本信息...
2025-06-25 17:47:47.346 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-06-25 17:47:47.349 +08:00 [INF]  正在通过H04写入版本信息...
2025-06-25 17:47:47.709 +08:00 [INF] ==>版本写入成功:6.25.300
2025-06-25 17:47:49.612 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-06-25 17:47:50.062 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-06-25 17:47:50.626 +08:00 [INF] 正在检查旧数据是否完整...
2025-06-25 17:47:50.627 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-06-25 17:47:51.786 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-06-25 17:47:54.120 +08:00 [INF] ==>初始化完成..
2025-06-25 17:47:54.174 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-06-25 17:47:54.176 +08:00 [INF] 设备启用任务
2025-06-25 17:47:54.176 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-06-25 17:47:54.591 +08:00 [INF] 【SQL执行耗时:388.4385ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-06-25 17:47:54.800 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-06-25 17:47:54.815 +08:00 [INF] Now listening on: https://[::]:18482
2025-06-25 17:47:54.817 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-25 17:47:54.817 +08:00 [INF] Hosting environment: Development
2025-06-25 17:47:54.818 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-06-25 17:51:58.072 +08:00 [INF] HTTP POST /api/EquipmentDoc/CreatCardTemplate responded 401 in 334.4771 ms
2025-06-25 17:52:06.180 +08:00 [INF] 第三方url地址为：http://************
2025-06-25 17:52:43.065 +08:00 [INF] ## 日志组[生成设备标识卡] 开始 2025-06-25 17:52:21.343 ##
#2025-06-25 17:52:21.855   +513 ms =>[调用S16的ExportPdfSheet6接口] 完成 #  
#2025-06-25 17:52:43.062 +21207 ms =>[PDF转换成图片Base64] 完成 #  
## 日志组[生成设备标识卡] 结束 2025-06-25 17:52:43.063 总耗时:21721 ms ##

2025-06-25 17:52:43.255 +08:00 [INF] {"success":true,"msg":null,"exception":null,"errCode":null,"data":"/EMS/datafile/MLS-3751L-PC22_StandardCard_preview.png","data1":null,"data2":null}
2025-06-25 17:52:43.256 +08:00 [INF] 调用S28模块[/api/Common/SetUploadPdf],耗时:178ms
2025-06-25 17:52:43.349 +08:00 [INF] HTTP POST /api/EquipmentDoc/CreatCardTemplate responded 200 in 40338.2134 ms
2025-06-25 17:52:43.350 +08:00 [INF] 【接口超时阀值预警】 [277ca9c9d202e0ca3670c6399fd65787]接口/api/EquipmentDoc/CreatCardTemplate,耗时:[40338]毫秒
2025-06-25 18:00:22.738 +08:00 [INF] 第三方url地址为：http://************
2025-06-25 18:04:30.270 +08:00 [INF] ## 日志组[生成设备标识卡] 开始 2025-06-25 18:00:36.398 ##
#2025-06-25 18:01:38.166 +61768 ms =>[调用S16的ExportPdfSheet6接口] 完成 #  
#2025-06-25 18:04:18.482 +160316 ms =>[PDF转换成图片Base64] 完成 #  
## 日志组[生成设备标识卡] 结束 2025-06-25 18:04:30.270 总耗时:233872 ms ##

2025-06-25 18:04:37.519 +08:00 [INF] {"success":true,"msg":null,"exception":null,"errCode":null,"data":"/EMS/datafile/MLS-3751L-PC22_StandardCard_preview.png","data1":null,"data2":null}
2025-06-25 18:04:37.520 +08:00 [INF] 调用S28模块[/api/Common/SetUploadPdf],耗时:169ms
2025-06-25 18:04:37.528 +08:00 [INF] HTTP POST /api/EquipmentDoc/CreatCardTemplate responded 200 in 254812.1903 ms
2025-06-25 18:04:37.528 +08:00 [INF] 【接口超时阀值预警】 [212b81e763ceca3587fa988dcdc31673]接口/api/EquipmentDoc/CreatCardTemplate,耗时:[254812]毫秒
2025-06-25 19:26:39.404 +08:00 [INF] ==>App Start..2025-06-25 19:26:39
2025-06-25 19:26:39.579 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-06-25 19:26:39.583 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-06-25 19:26:41.205 +08:00 [INF] ==>基础连接请求完成.
2025-06-25 19:26:41.581 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-06-25 19:26:41.982 +08:00 [INF]  正在通过S01写入版本信息...
2025-06-25 19:26:42.345 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-06-25 19:26:42.347 +08:00 [INF]  正在通过H04写入版本信息...
2025-06-25 19:26:42.698 +08:00 [INF] ==>版本写入成功:6.25.300
2025-06-25 19:26:43.214 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-06-25 19:26:43.323 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-06-25 19:26:43.767 +08:00 [INF] 正在检查旧数据是否完整...
2025-06-25 19:26:43.768 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-06-25 19:26:44.962 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-06-25 19:26:47.301 +08:00 [INF] ==>初始化完成..
2025-06-25 19:26:47.326 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-06-25 19:26:47.330 +08:00 [INF] 设备启用任务
2025-06-25 19:26:47.331 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-06-25 19:26:47.735 +08:00 [INF] 【SQL执行耗时:381.5203ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-06-25 19:26:47.888 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-06-25 19:26:47.903 +08:00 [INF] Now listening on: https://[::]:18482
2025-06-25 19:26:47.905 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-25 19:26:47.905 +08:00 [INF] Hosting environment: Development
2025-06-25 19:26:47.906 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-06-25 19:28:57.669 +08:00 [INF] HTTP GET /api/Base/GetEnclosureInfo responded 401 in 268.9017 ms
2025-06-25 19:29:05.641 +08:00 [INF] 【SQL执行耗时:361.6153ms】

[Sql]:SELECT "DOC_ID","DOC_NAME","DOC_PATH","PDF_PREVIEW_PATH","DOC_SUFFIX","DOC_TYPE","EMS_INFO_ID","HOSPITAL_ID","DOC_CLASS","DOC_INFO_ID","EQUIPMENT_ID","DOC_FILE","UPLOAD_PERSON","UPLOAD_TIME","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","DOC_STATE","REMARK" FROM "XH_OA"."EMS_DOC_INFO"  WHERE ( "DOC_CLASS" = :DOC_CLASS0 )  AND ( "DOC_STATE" = :DOC_STATE1 )  AND (( "EQUIPMENT_ID" = :EQUIPMENT_ID2 ) OR ( "DOC_INFO_ID" = :DOC_INFO_ID3 )) 
[Pars]:
[Name]::DOC_CLASS0 [Value]:SOP档案 [Type]:String    
[Name]::DOC_STATE1 [Value]:1 [Type]:String    
[Name]::EQUIPMENT_ID2 [Value]:1783331803105497088 [Type]:String    
[Name]::DOC_INFO_ID3 [Value]:1783331803105497088 [Type]:String    

2025-06-25 19:29:32.938 +08:00 [INF] 【SQL执行耗时:379.4596ms】

[Sql]:begin

INSERT INTO "XH_OA"."OA_DOC_FILE_LINK"  
           ("FILE_LINK_ID","HOSPITAL_ID","DATA_ID","UNION_TYPE","MODULE_ID","FILE_ID","FILE_DATA_TYPE","DOC_ID","DOC_CATALOG_FLAG","FILE_LINK_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","DOC_LINK_ANND")
     VALUES
           (:FILE_LINK_ID_q_2,:HOSPITAL_ID_q_2,:DATA_ID_q_2,:UNION_TYPE_q_2,:MODULE_ID_q_2,:FILE_ID_q_2,:FILE_DATA_TYPE_q_2,:DOC_ID_q_2,:DOC_CATALOG_FLAG_q_2,:FILE_LINK_STATE_q_2,:FIRST_RPERSON_q_2,:FIRST_RTIME_q_2,:LAST_MPERSON_q_2,:LAST_MTIME_q_2,:REMARK_q_2,:Common0_q_2) ;
INSERT INTO "XH_OA"."OA_DOC_FILE_LINK"  
           ("FILE_LINK_ID","HOSPITAL_ID","DATA_ID","UNION_TYPE","MODULE_ID","FILE_ID","FILE_DATA_TYPE","DOC_ID","DOC_CATALOG_FLAG","FILE_LINK_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","DOC_LINK_ANND")
     VALUES
           (:FILE_LINK_ID_q_3,:HOSPITAL_ID_q_3,:DATA_ID_q_3,:UNION_TYPE_q_3,:MODULE_ID_q_3,:FILE_ID_q_3,:FILE_DATA_TYPE_q_3,:DOC_ID_q_3,:DOC_CATALOG_FLAG_q_3,:FILE_LINK_STATE_q_3,:FIRST_RPERSON_q_3,:FIRST_RTIME_q_3,:LAST_MPERSON_q_3,:LAST_MTIME_q_3,:REMARK_q_3,:Common0_q_3) ;
INSERT INTO "XH_OA"."OA_DOC_FILE_LINK"  
           ("FILE_LINK_ID","HOSPITAL_ID","DATA_ID","UNION_TYPE","MODULE_ID","FILE_ID","FILE_DATA_TYPE","DOC_ID","DOC_CATALOG_FLAG","FILE_LINK_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","DOC_LINK_ANND")
     VALUES
           (:FILE_LINK_ID_q_4,:HOSPITAL_ID_q_4,:DATA_ID_q_4,:UNION_TYPE_q_4,:MODULE_ID_q_4,:FILE_ID_q_4,:FILE_DATA_TYPE_q_4,:DOC_ID_q_4,:DOC_CATALOG_FLAG_q_4,:FILE_LINK_STATE_q_4,:FIRST_RPERSON_q_4,:FIRST_RTIME_q_4,:LAST_MPERSON_q_4,:LAST_MTIME_q_4,:REMARK_q_4,:Common0_q_4) ;
INSERT INTO "XH_OA"."OA_DOC_FILE_LINK"  
           ("FILE_LINK_ID","HOSPITAL_ID","DATA_ID","UNION_TYPE","MODULE_ID","FILE_ID","FILE_DATA_TYPE","DOC_ID","DOC_CATALOG_FLAG","FILE_LINK_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","DOC_LINK_ANND")
     VALUES
           (:FILE_LINK_ID_q_5,:HOSPITAL_ID_q_5,:DATA_ID_q_5,:UNION_TYPE_q_5,:MODULE_ID_q_5,:FILE_ID_q_5,:FILE_DATA_TYPE_q_5,:DOC_ID_q_5,:DOC_CATALOG_FLAG_q_5,:FILE_LINK_STATE_q_5,:FIRST_RPERSON_q_5,:FIRST_RTIME_q_5,:LAST_MPERSON_q_5,:LAST_MTIME_q_5,:REMARK_q_5,:Common0_q_5) ;
INSERT INTO "XH_OA"."OA_DOC_FILE_LINK"  
           ("FILE_LINK_ID","HOSPITAL_ID","DATA_ID","UNION_TYPE","MODULE_ID","FILE_ID","FILE_DATA_TYPE","DOC_ID","DOC_CATALOG_FLAG","FILE_LINK_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","DOC_LINK_ANND")
     VALUES
           (:FILE_LINK_ID_q_6,:HOSPITAL_ID_q_6,:DATA_ID_q_6,:UNION_TYPE_q_6,:MODULE_ID_q_6,:FILE_ID_q_6,:FILE_DATA_TYPE_q_6,:DOC_ID_q_6,:DOC_CATALOG_FLAG_q_6,:FILE_LINK_STATE_q_6,:FIRST_RPERSON_q_6,:FIRST_RTIME_q_6,:LAST_MPERSON_q_6,:LAST_MTIME_q_6,:REMARK_q_6,:Common0_q_6) ;
INSERT INTO "XH_OA"."OA_DOC_FILE_LINK"  
           ("FILE_LINK_ID","HOSPITAL_ID","DATA_ID","UNION_TYPE","MODULE_ID","FILE_ID","FILE_DATA_TYPE","DOC_ID","DOC_CATALOG_FLAG","FILE_LINK_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","DOC_LINK_ANND")
     VALUES
           (:FILE_LINK_ID_q_7,:HOSPITAL_ID_q_7,:DATA_ID_q_7,:UNION_TYPE_q_7,:MODULE_ID_q_7,:FILE_ID_q_7,:FILE_DATA_TYPE_q_7,:DOC_ID_q_7,:DOC_CATALOG_FLAG_q_7,:FILE_LINK_STATE_q_7,:FIRST_RPERSON_q_7,:FIRST_RTIME_q_7,:LAST_MPERSON_q_7,:LAST_MTIME_q_7,:REMARK_q_7,:Common0_q_7) ;
INSERT INTO "XH_OA"."OA_DOC_FILE_LINK"  
           ("FILE_LINK_ID","HOSPITAL_ID","DATA_ID","UNION_TYPE","MODULE_ID","FILE_ID","FILE_DATA_TYPE","DOC_ID","DOC_CATALOG_FLAG","FILE_LINK_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","DOC_LINK_ANND")
     VALUES
           (:FILE_LINK_ID_q_8,:HOSPITAL_ID_q_8,:DATA_ID_q_8,:UNION_TYPE_q_8,:MODULE_ID_q_8,:FILE_ID_q_8,:FILE_DATA_TYPE_q_8,:DOC_ID_q_8,:DOC_CATALOG_FLAG_q_8,:FILE_LINK_STATE_q_8,:FIRST_RPERSON_q_8,:FIRST_RTIME_q_8,:LAST_MPERSON_q_8,:LAST_MTIME_q_8,:REMARK_q_8,:Common0_q_8) ;
end ;
 
[Pars]:
[Name]::DOC_CATALOG_FLAG_q_2 [Value]:0 [Type]:String    
[Name]::FILE_LINK_STATE_q_2 [Value]:1 [Type]:String    
[Name]::FILE_DATA_TYPE_q_2 [Value]:2 [Type]:String    
[Name]::FIRST_RPERSON_q_2 [Value]:fr_李影 [Type]:String    
[Name]::FILE_LINK_ID_q_2 [Value]:66898DF2488F4C9B8A2EB22696719307 [Type]:String    
[Name]::LAST_MPERSON_q_2 [Value]:fr_李影 [Type]:String    
[Name]::HOSPITAL_ID_q_2 [Value]:33A001 [Type]:String    
[Name]::FIRST_RTIME_q_2 [Value]:2025/6/25 19:29:04 [Type]:DateTime    
[Name]::UNION_TYPE_q_2 [Value]:H82SOP [Type]:String    
[Name]::LAST_MTIME_q_2 [Value]:2025/6/25 19:29:04 [Type]:DateTime    
[Name]::MODULE_ID_q_2 [Value]:H82 [Type]:String    
[Name]::DATA_ID_q_2 [Value]:1783331803105497088 [Type]:String    
[Name]::FILE_ID_q_2 [Value]: [Type]:String    
[Name]::Common0_q_2 [Value]: [Type]:String    
[Name]::DOC_ID_q_2 [Value]:3610 [Type]:String    
[Name]::REMARK_q_2 [Value]: [Type]:String    
[Name]::DOC_CATALOG_FLAG_q_3 [Value]:0 [Type]:String    
[Name]::FILE_LINK_STATE_q_3 [Value]:1 [Type]:String    
[Name]::FILE_DATA_TYPE_q_3 [Value]:2 [Type]:String    
[Name]::FIRST_RPERSON_q_3 [Value]:fr_李影 [Type]:String    
[Name]::FILE_LINK_ID_q_3 [Value]:3C31274B45654978A8B7AB67C5BB8E14 [Type]:String    
[Name]::LAST_MPERSON_q_3 [Value]:fr_李影 [Type]:String    
[Name]::HOSPITAL_ID_q_3 [Value]:33A001 [Type]:String    
[Name]::FIRST_RTIME_q_3 [Value]:2025/6/25 19:29:04 [Type]:DateTime    
[Name]::UNION_TYPE_q_3 [Value]:H82SOP [Type]:String    
[Name]::LAST_MTIME_q_3 [Value]:2025/6/25 19:29:04 [Type]:DateTime    
[Name]::MODULE_ID_q_3 [Value]:H82 [Type]:String    
[Name]::DATA_ID_q_3 [Value]:1783331803105497088 [Type]:String    
[Name]::FILE_ID_q_3 [Value]: [Type]:String    
[Name]::Common0_q_3 [Value]: [Type]:String    
[Name]::DOC_ID_q_3 [Value]:3641 [Type]:String    
[Name]::REMARK_q_3 [Value]: [Type]:String    
[Name]::DOC_CATALOG_FLAG_q_4 [Value]:0 [Type]:String    
[Name]::FILE_LINK_STATE_q_4 [Value]:1 [Type]:String    
[Name]::FILE_DATA_TYPE_q_4 [Value]:2 [Type]:String    
[Name]::FIRST_RPERSON_q_4 [Value]:fr_李影 [Type]:String    
[Name]::FILE_LINK_ID_q_4 [Value]:EF3B7DA22F2546298475B1524F51D91C [Type]:String    
[Name]::LAST_MPERSON_q_4 [Value]:fr_李影 [Type]:String    
[Name]::HOSPITAL_ID_q_4 [Value]:33A001 [Type]:String    
[Name]::FIRST_RTIME_q_4 [Value]:2025/6/25 19:29:04 [Type]:DateTime    
[Name]::UNION_TYPE_q_4 [Value]:H82SOP [Type]:String    
[Name]::LAST_MTIME_q_4 [Value]:2025/6/25 19:29:04 [Type]:DateTime    
[Name]::MODULE_ID_q_4 [Value]:H82 [Type]:String    
[Name]::DATA_ID_q_4 [Value]:1783331803105497088 [Type]:String    
[Name]::FILE_ID_q_4 [Value]: [Type]:String    
[Name]::Common0_q_4 [Value]: [Type]:String    
[Name]::DOC_ID_q_4 [Value]:3634 [Type]:String    
[Name]::REMARK_q_4 [Value]: [Type]:String    
[Name]::DOC_CATALOG_FLAG_q_5 [Value]:0 [Type]:String    
[Name]::FILE_LINK_STATE_q_5 [Value]:1 [Type]:String    
[Name]::FILE_DATA_TYPE_q_5 [Value]:2 [Type]:String    
[Name]::FIRST_RPERSON_q_5 [Value]:fr_李影 [Type]:String    
[Name]::FILE_LINK_ID_q_5 [Value]:5B4109D361DA448D80599EEAD8584A89 [Type]:String    
[Name]::LAST_MPERSON_q_5 [Value]:fr_李影 [Type]:String    
[Name]::HOSPITAL_ID_q_5 [Value]:33A001 [Type]:String    
[Name]::FIRST_RTIME_q_5 [Value]:2025/6/25 19:29:04 [Type]:DateTime    
[Name]::UNION_TYPE_q_5 [Value]:H82SOP [Type]:String    
[Name]::LAST_MTIME_q_5 [Value]:2025/6/25 19:29:04 [Type]:DateTime    
[Name]::MODULE_ID_q_5 [Value]:H82 [Type]:String    
[Name]::DATA_ID_q_5 [Value]:1783331803105497088 [Type]:String    
[Name]::FILE_ID_q_5 [Value]: [Type]:String    
[Name]::Common0_q_5 [Value]: [Type]:String    
[Name]::DOC_ID_q_5 [Value]:3554 [Type]:String    
[Name]::REMARK_q_5 [Value]: [Type]:String    
[Name]::DOC_CATALOG_FLAG_q_6 [Value]:0 [Type]:String    
[Name]::FILE_LINK_STATE_q_6 [Value]:1 [Type]:String    
[Name]::FILE_DATA_TYPE_q_6 [Value]:2 [Type]:String    
[Name]::FIRST_RPERSON_q_6 [Value]:fr_李影 [Type]:String    
[Name]::FILE_LINK_ID_q_6 [Value]:103F99BEBE2D4BE992512C0DBBAB2265 [Type]:String    
[Name]::LAST_MPERSON_q_6 [Value]:fr_李影 [Type]:String    
[Name]::HOSPITAL_ID_q_6 [Value]:33A001 [Type]:String    
[Name]::FIRST_RTIME_q_6 [Value]:2025/6/25 19:29:04 [Type]:DateTime    
[Name]::UNION_TYPE_q_6 [Value]:H82SOP [Type]:String    
[Name]::LAST_MTIME_q_6 [Value]:2025/6/25 19:29:04 [Type]:DateTime    
[Name]::MODULE_ID_q_6 [Value]:H82 [Type]:String    
[Name]::DATA_ID_q_6 [Value]:1783331803105497088 [Type]:String    
[Name]::FILE_ID_q_6 [Value]: [Type]:String    
[Name]::Common0_q_6 [Value]: [Type]:String    
[Name]::DOC_ID_q_6 [Value]:3601 [Type]:String    
[Name]::REMARK_q_6 [Value]: [Type]:String    
[Name]::DOC_CATALOG_FLAG_q_7 [Value]:0 [Type]:String    
[Name]::FILE_LINK_STATE_q_7 [Value]:1 [Type]:String    
[Name]::FILE_DATA_TYPE_q_7 [Value]:2 [Type]:String    
[Name]::FIRST_RPERSON_q_7 [Value]:fr_李影 [Type]:String    
[Name]::FILE_LINK_ID_q_7 [Value]:D4CBFB2D94FF44CD9C9DF978D3434F45 [Type]:String    
[Name]::LAST_MPERSON_q_7 [Value]:fr_李影 [Type]:String    
[Name]::HOSPITAL_ID_q_7 [Value]:33A001 [Type]:String    
[Name]::FIRST_RTIME_q_7 [Value]:2025/6/25 19:29:04 [Type]:DateTime    
[Name]::UNION_TYPE_q_7 [Value]:H82SOP [Type]:String    
[Name]::LAST_MTIME_q_7 [Value]:2025/6/25 19:29:04 [Type]:DateTime    
[Name]::MODULE_ID_q_7 [Value]:H82 [Type]:String    
[Name]::DATA_ID_q_7 [Value]:1783331803105497088 [Type]:String    
[Name]::FILE_ID_q_7 [Value]: [Type]:String    
[Name]::Common0_q_7 [Value]: [Type]:String    
[Name]::DOC_ID_q_7 [Value]:3635 [Type]:String    
[Name]::REMARK_q_7 [Value]: [Type]:String    
[Name]::DOC_CATALOG_FLAG_q_8 [Value]:0 [Type]:String    
[Name]::FILE_LINK_STATE_q_8 [Value]:1 [Type]:String    
[Name]::FILE_DATA_TYPE_q_8 [Value]:2 [Type]:String    
[Name]::FIRST_RPERSON_q_8 [Value]:fr_李影 [Type]:String    
[Name]::FILE_LINK_ID_q_8 [Value]:F2ED2436AF7D454B819B18FA1060E742 [Type]:String    
[Name]::LAST_MPERSON_q_8 [Value]:fr_李影 [Type]:String    
[Name]::HOSPITAL_ID_q_8 [Value]:33A001 [Type]:String    
[Name]::FIRST_RTIME_q_8 [Value]:2025/6/25 19:29:04 [Type]:DateTime    
[Name]::UNION_TYPE_q_8 [Value]:H82SOP [Type]:String    
[Name]::LAST_MTIME_q_8 [Value]:2025/6/25 19:29:04 [Type]:DateTime    
[Name]::MODULE_ID_q_8 [Value]:H82 [Type]:String    
[Name]::DATA_ID_q_8 [Value]:1783331803105497088 [Type]:String    
[Name]::FILE_ID_q_8 [Value]: [Type]:String    
[Name]::Common0_q_8 [Value]: [Type]:String    
[Name]::DOC_ID_q_8 [Value]:3611 [Type]:String    
[Name]::REMARK_q_8 [Value]: [Type]:String    

2025-06-25 19:29:37.602 +08:00 [INF] 【SQL执行耗时:388.9671ms】

[Sql]:SELECT "FILE_LINK_ID","HOSPITAL_ID","DATA_ID","UNION_TYPE","MODULE_ID","FILE_ID","FILE_DATA_TYPE","DOC_ID","DOC_CATALOG_FLAG","FILE_LINK_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","DOC_LINK_ANND" FROM "XH_OA"."OA_DOC_FILE_LINK"  WHERE ( "MODULE_ID" = :MODULE_ID0 )  AND ( "HOSPITAL_ID" = :HOSPITAL_ID1 )  AND ( "UNION_TYPE" = :UNION_TYPE2 )  AND ((( "DATA_ID" = :DATA_ID3 ) AND ( "FILE_DATA_TYPE" = :FILE_DATA_TYPE4 )) AND ( "FILE_LINK_STATE" = :FILE_LINK_STATE5 )) 
[Pars]:
[Name]::MODULE_ID0 [Value]:H82 [Type]:String    
[Name]::HOSPITAL_ID1 [Value]:33A001 [Type]:String    
[Name]::UNION_TYPE2 [Value]:H82SOP [Type]:String    
[Name]::DATA_ID3 [Value]:1783331803105497088 [Type]:String    
[Name]::FILE_DATA_TYPE4 [Value]:2 [Type]:String    
[Name]::FILE_LINK_STATE5 [Value]:1 [Type]:String    

2025-06-25 19:29:38.092 +08:00 [INF] 【SQL执行耗时:346.5671ms】

[Sql]:SELECT "FILE".* FROM "XH_OA"."DMIS_SYS_DOC" "DOC" Inner JOIN "XH_OA"."DMIS_SYS_FILE" "FILE" ON ( "DOC"."DOC_ID" = "FILE"."DOC_ID" )   WHERE  ("DOC"."DOC_ID" IN ('3610','3641','3634','3554','3601','3635','3611'))   AND ((( "FILE"."REVISION_STATE" = :REVISION_STATE1 ) AND ( "FILE"."FILE_STATE" = :FILE_STATE2 )) AND ( "FILE"."FILE_ORIGIN" <> :FILE_ORIGIN3 )) 
[Pars]:
[Name]::REVISION_STATE1 [Value]:1 [Type]:String    
[Name]::FILE_STATE2 [Value]:1 [Type]:String    
[Name]::FILE_ORIGIN3 [Value]:2 [Type]:String    

2025-06-25 19:29:38.149 +08:00 [INF] HTTP GET /api/Base/GetEnclosureInfo responded 200 in 37201.4264 ms
2025-06-25 19:29:38.151 +08:00 [INF] 【接口超时阀值预警】 [1be1edc51e9e94d35e796b94667a289d]接口/api/Base/GetEnclosureInfo,耗时:[37201]毫秒
2025-06-25 19:29:49.522 +08:00 [INF] 【SQL执行耗时:359.6986ms】

[Sql]:SELECT "DOC_ID","DOC_NAME","DOC_PATH","PDF_PREVIEW_PATH","DOC_SUFFIX","DOC_TYPE","EMS_INFO_ID","HOSPITAL_ID","DOC_CLASS","DOC_INFO_ID","EQUIPMENT_ID","DOC_FILE","UPLOAD_PERSON","UPLOAD_TIME","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","DOC_STATE","REMARK" FROM "XH_OA"."EMS_DOC_INFO"  WHERE ( "DOC_CLASS" = :DOC_CLASS0 )  AND ( "DOC_STATE" = :DOC_STATE1 )  AND (( "EQUIPMENT_ID" = :EQUIPMENT_ID2 ) OR ( "DOC_INFO_ID" = :DOC_INFO_ID3 )) 
[Pars]:
[Name]::DOC_CLASS0 [Value]:SOP档案 [Type]:String    
[Name]::DOC_STATE1 [Value]:1 [Type]:String    
[Name]::EQUIPMENT_ID2 [Value]:1783331803105497088 [Type]:String    
[Name]::DOC_INFO_ID3 [Value]:1783331803105497088 [Type]:String    

2025-06-25 19:29:56.752 +08:00 [INF] 【SQL执行耗时:358.0154ms】

[Sql]:SELECT "FILE_LINK_ID","HOSPITAL_ID","DATA_ID","UNION_TYPE","MODULE_ID","FILE_ID","FILE_DATA_TYPE","DOC_ID","DOC_CATALOG_FLAG","FILE_LINK_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","DOC_LINK_ANND" FROM "XH_OA"."OA_DOC_FILE_LINK"  WHERE ( "MODULE_ID" = :MODULE_ID0 )  AND ( "HOSPITAL_ID" = :HOSPITAL_ID1 )  AND ( "UNION_TYPE" = :UNION_TYPE2 )  AND ((( "DATA_ID" = :DATA_ID3 ) AND ( "FILE_DATA_TYPE" = :FILE_DATA_TYPE4 )) AND ( "FILE_LINK_STATE" = :FILE_LINK_STATE5 )) 
[Pars]:
[Name]::MODULE_ID0 [Value]:H82 [Type]:String    
[Name]::HOSPITAL_ID1 [Value]:33A001 [Type]:String    
[Name]::UNION_TYPE2 [Value]:H82SOP [Type]:String    
[Name]::DATA_ID3 [Value]:1783331803105497088 [Type]:String    
[Name]::FILE_DATA_TYPE4 [Value]:2 [Type]:String    
[Name]::FILE_LINK_STATE5 [Value]:1 [Type]:String    

2025-06-25 19:30:13.125 +08:00 [INF] 【SQL执行耗时:363.7864ms】

[Sql]:SELECT "FILE".* FROM "XH_OA"."DMIS_SYS_DOC" "DOC" Inner JOIN "XH_OA"."DMIS_SYS_FILE" "FILE" ON ( "DOC"."DOC_ID" = "FILE"."DOC_ID" )   WHERE  ("DOC"."DOC_ID" IN ('3610','3641','3634','3554','3601','3635','3611'))   AND ((( "FILE"."REVISION_STATE" = :REVISION_STATE1 ) AND ( "FILE"."FILE_STATE" = :FILE_STATE2 )) AND ( "FILE"."FILE_ORIGIN" <> :FILE_ORIGIN3 )) 
[Pars]:
[Name]::REVISION_STATE1 [Value]:1 [Type]:String    
[Name]::FILE_STATE2 [Value]:1 [Type]:String    
[Name]::FILE_ORIGIN3 [Value]:2 [Type]:String    

2025-06-25 19:30:31.993 +08:00 [INF] HTTP GET /api/Base/GetEnclosureInfo responded 200 in 42878.6145 ms
2025-06-25 19:30:31.993 +08:00 [INF] 【接口超时阀值预警】 [77bc671febdd3c66494af656d42c3c42]接口/api/Base/GetEnclosureInfo,耗时:[42879]毫秒
2025-06-25 19:34:08.350 +08:00 [INF] 【SQL执行耗时:378.4969ms】

[Sql]:SELECT "DOC_ID","DOC_NAME","DOC_PATH","PDF_PREVIEW_PATH","DOC_SUFFIX","DOC_TYPE","EMS_INFO_ID","HOSPITAL_ID","DOC_CLASS","DOC_INFO_ID","EQUIPMENT_ID","DOC_FILE","UPLOAD_PERSON","UPLOAD_TIME","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","DOC_STATE","REMARK" FROM "XH_OA"."EMS_DOC_INFO"  WHERE ( "DOC_CLASS" = :DOC_CLASS0 )  AND ( "DOC_STATE" = :DOC_STATE1 )  AND (( "EQUIPMENT_ID" = :EQUIPMENT_ID2 ) OR ( "DOC_INFO_ID" = :DOC_INFO_ID3 )) 
[Pars]:
[Name]::DOC_CLASS0 [Value]:SOP档案 [Type]:String    
[Name]::DOC_STATE1 [Value]:1 [Type]:String    
[Name]::EQUIPMENT_ID2 [Value]:C3157ECF9C984EE18972526D6AEE2D00 [Type]:String    
[Name]::DOC_INFO_ID3 [Value]:C3157ECF9C984EE18972526D6AEE2D00 [Type]:String    

2025-06-25 19:34:08.745 +08:00 [INF] 【SQL执行耗时:350.5172ms】

[Sql]:begin

INSERT INTO "XH_OA"."OA_DOC_FILE_LINK"  
           ("FILE_LINK_ID","HOSPITAL_ID","DATA_ID","UNION_TYPE","MODULE_ID","FILE_ID","FILE_DATA_TYPE","DOC_ID","DOC_CATALOG_FLAG","FILE_LINK_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","DOC_LINK_ANND")
     VALUES
           (:FILE_LINK_ID_q_2,:HOSPITAL_ID_q_2,:DATA_ID_q_2,:UNION_TYPE_q_2,:MODULE_ID_q_2,:FILE_ID_q_2,:FILE_DATA_TYPE_q_2,:DOC_ID_q_2,:DOC_CATALOG_FLAG_q_2,:FILE_LINK_STATE_q_2,:FIRST_RPERSON_q_2,:FIRST_RTIME_q_2,:LAST_MPERSON_q_2,:LAST_MTIME_q_2,:REMARK_q_2,:Common0_q_2) ;
INSERT INTO "XH_OA"."OA_DOC_FILE_LINK"  
           ("FILE_LINK_ID","HOSPITAL_ID","DATA_ID","UNION_TYPE","MODULE_ID","FILE_ID","FILE_DATA_TYPE","DOC_ID","DOC_CATALOG_FLAG","FILE_LINK_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","DOC_LINK_ANND")
     VALUES
           (:FILE_LINK_ID_q_3,:HOSPITAL_ID_q_3,:DATA_ID_q_3,:UNION_TYPE_q_3,:MODULE_ID_q_3,:FILE_ID_q_3,:FILE_DATA_TYPE_q_3,:DOC_ID_q_3,:DOC_CATALOG_FLAG_q_3,:FILE_LINK_STATE_q_3,:FIRST_RPERSON_q_3,:FIRST_RTIME_q_3,:LAST_MPERSON_q_3,:LAST_MTIME_q_3,:REMARK_q_3,:Common0_q_3) ;
INSERT INTO "XH_OA"."OA_DOC_FILE_LINK"  
           ("FILE_LINK_ID","HOSPITAL_ID","DATA_ID","UNION_TYPE","MODULE_ID","FILE_ID","FILE_DATA_TYPE","DOC_ID","DOC_CATALOG_FLAG","FILE_LINK_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","DOC_LINK_ANND")
     VALUES
           (:FILE_LINK_ID_q_4,:HOSPITAL_ID_q_4,:DATA_ID_q_4,:UNION_TYPE_q_4,:MODULE_ID_q_4,:FILE_ID_q_4,:FILE_DATA_TYPE_q_4,:DOC_ID_q_4,:DOC_CATALOG_FLAG_q_4,:FILE_LINK_STATE_q_4,:FIRST_RPERSON_q_4,:FIRST_RTIME_q_4,:LAST_MPERSON_q_4,:LAST_MTIME_q_4,:REMARK_q_4,:Common0_q_4) ;
end ;
 
[Pars]:
[Name]::DOC_CATALOG_FLAG_q_2 [Value]:0 [Type]:String    
[Name]::FILE_LINK_STATE_q_2 [Value]:1 [Type]:String    
[Name]::FILE_DATA_TYPE_q_2 [Value]:2 [Type]:String    
[Name]::FIRST_RPERSON_q_2 [Value]:fr_李影 [Type]:String    
[Name]::FILE_LINK_ID_q_2 [Value]:4359C9AD7EF646BAA5606FFE8DEEC312 [Type]:String    
[Name]::LAST_MPERSON_q_2 [Value]:fr_李影 [Type]:String    
[Name]::HOSPITAL_ID_q_2 [Value]:33A001 [Type]:String    
[Name]::FIRST_RTIME_q_2 [Value]:2025/6/25 19:34:07 [Type]:DateTime    
[Name]::UNION_TYPE_q_2 [Value]:H82SOP [Type]:String    
[Name]::LAST_MTIME_q_2 [Value]:2025/6/25 19:34:07 [Type]:DateTime    
[Name]::MODULE_ID_q_2 [Value]:H82 [Type]:String    
[Name]::DATA_ID_q_2 [Value]:C3157ECF9C984EE18972526D6AEE2D00 [Type]:String    
[Name]::FILE_ID_q_2 [Value]: [Type]:String    
[Name]::Common0_q_2 [Value]: [Type]:String    
[Name]::DOC_ID_q_2 [Value]:4330 [Type]:String    
[Name]::REMARK_q_2 [Value]: [Type]:String    
[Name]::DOC_CATALOG_FLAG_q_3 [Value]:0 [Type]:String    
[Name]::FILE_LINK_STATE_q_3 [Value]:1 [Type]:String    
[Name]::FILE_DATA_TYPE_q_3 [Value]:2 [Type]:String    
[Name]::FIRST_RPERSON_q_3 [Value]:fr_李影 [Type]:String    
[Name]::FILE_LINK_ID_q_3 [Value]:043B744C86DB4865B7E60622224FE160 [Type]:String    
[Name]::LAST_MPERSON_q_3 [Value]:fr_李影 [Type]:String    
[Name]::HOSPITAL_ID_q_3 [Value]:33A001 [Type]:String    
[Name]::FIRST_RTIME_q_3 [Value]:2025/6/25 19:34:07 [Type]:DateTime    
[Name]::UNION_TYPE_q_3 [Value]:H82SOP [Type]:String    
[Name]::LAST_MTIME_q_3 [Value]:2025/6/25 19:34:07 [Type]:DateTime    
[Name]::MODULE_ID_q_3 [Value]:H82 [Type]:String    
[Name]::DATA_ID_q_3 [Value]:C3157ECF9C984EE18972526D6AEE2D00 [Type]:String    
[Name]::FILE_ID_q_3 [Value]: [Type]:String    
[Name]::Common0_q_3 [Value]: [Type]:String    
[Name]::DOC_ID_q_3 [Value]:4323 [Type]:String    
[Name]::REMARK_q_3 [Value]: [Type]:String    
[Name]::DOC_CATALOG_FLAG_q_4 [Value]:0 [Type]:String    
[Name]::FILE_LINK_STATE_q_4 [Value]:1 [Type]:String    
[Name]::FILE_DATA_TYPE_q_4 [Value]:2 [Type]:String    
[Name]::FIRST_RPERSON_q_4 [Value]:fr_李影 [Type]:String    
[Name]::FILE_LINK_ID_q_4 [Value]:60FE52DB87DA4906A3B3219A2D4514FC [Type]:String    
[Name]::LAST_MPERSON_q_4 [Value]:fr_李影 [Type]:String    
[Name]::HOSPITAL_ID_q_4 [Value]:33A001 [Type]:String    
[Name]::FIRST_RTIME_q_4 [Value]:2025/6/25 19:34:07 [Type]:DateTime    
[Name]::UNION_TYPE_q_4 [Value]:H82SOP [Type]:String    
[Name]::LAST_MTIME_q_4 [Value]:2025/6/25 19:34:07 [Type]:DateTime    
[Name]::MODULE_ID_q_4 [Value]:H82 [Type]:String    
[Name]::DATA_ID_q_4 [Value]:C3157ECF9C984EE18972526D6AEE2D00 [Type]:String    
[Name]::FILE_ID_q_4 [Value]: [Type]:String    
[Name]::Common0_q_4 [Value]: [Type]:String    
[Name]::DOC_ID_q_4 [Value]:4488 [Type]:String    
[Name]::REMARK_q_4 [Value]: [Type]:String    

2025-06-25 19:34:09.153 +08:00 [INF] 【SQL执行耗时:367.4324ms】

[Sql]:begin

INSERT INTO "XH_OA"."OA_DOC_FILE_LINK"  
           ("FILE_LINK_ID","HOSPITAL_ID","DATA_ID","UNION_TYPE","MODULE_ID","FILE_ID","FILE_DATA_TYPE","DOC_ID","DOC_CATALOG_FLAG","FILE_LINK_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","DOC_LINK_ANND")
     VALUES
           (:FILE_LINK_ID,:HOSPITAL_ID,:DATA_ID,:UNION_TYPE,:MODULE_ID,:FILE_ID,:FILE_DATA_TYPE,:DOC_ID,:DOC_CATALOG_FLAG,:FILE_LINK_STATE,:FIRST_RPERSON,:FIRST_RTIME,:LAST_MPERSON,:LAST_MTIME,:REMARK,:Common0) ;
end ;
 
[Pars]:
[Name]::DOC_CATALOG_FLAG [Value]:0 [Type]:String    
[Name]::FILE_LINK_STATE [Value]:1 [Type]:String    
[Name]::FILE_DATA_TYPE [Value]:2 [Type]:String    
[Name]::FIRST_RPERSON [Value]:fr_李影 [Type]:String    
[Name]::FILE_LINK_ID [Value]:9225C77647284E169E5B66DA6002F69D [Type]:String    
[Name]::LAST_MPERSON [Value]:fr_李影 [Type]:String    
[Name]::HOSPITAL_ID [Value]:33A001 [Type]:String    
[Name]::FIRST_RTIME [Value]:2025/6/25 19:34:07 [Type]:DateTime    
[Name]::UNION_TYPE [Value]:H82SOP [Type]:String    
[Name]::LAST_MTIME [Value]:2025/6/25 19:34:07 [Type]:DateTime    
[Name]::MODULE_ID [Value]:H82 [Type]:String    
[Name]::DATA_ID [Value]:C3157ECF9C984EE18972526D6AEE2D00 [Type]:String    
[Name]::FILE_ID [Value]: [Type]:String    
[Name]::Common0 [Value]: [Type]:String    
[Name]::DOC_ID [Value]:3855 [Type]:String    
[Name]::REMARK [Value]: [Type]:String    

2025-06-25 19:34:09.608 +08:00 [INF] 【SQL执行耗时:383.038ms】

[Sql]:SELECT "FILE_LINK_ID","HOSPITAL_ID","DATA_ID","UNION_TYPE","MODULE_ID","FILE_ID","FILE_DATA_TYPE","DOC_ID","DOC_CATALOG_FLAG","FILE_LINK_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","DOC_LINK_ANND" FROM "XH_OA"."OA_DOC_FILE_LINK"  WHERE ( "MODULE_ID" = :MODULE_ID0 )  AND ( "HOSPITAL_ID" = :HOSPITAL_ID1 )  AND ( "UNION_TYPE" = :UNION_TYPE2 )  AND ((( "DATA_ID" = :DATA_ID3 ) AND ( "FILE_DATA_TYPE" = :FILE_DATA_TYPE4 )) AND ( "FILE_LINK_STATE" = :FILE_LINK_STATE5 )) 
[Pars]:
[Name]::MODULE_ID0 [Value]:H82 [Type]:String    
[Name]::HOSPITAL_ID1 [Value]:33A001 [Type]:String    
[Name]::UNION_TYPE2 [Value]:H82SOP [Type]:String    
[Name]::DATA_ID3 [Value]:C3157ECF9C984EE18972526D6AEE2D00 [Type]:String    
[Name]::FILE_DATA_TYPE4 [Value]:2 [Type]:String    
[Name]::FILE_LINK_STATE5 [Value]:1 [Type]:String    

2025-06-25 19:34:10.041 +08:00 [INF] 【SQL执行耗时:351.912ms】

[Sql]:SELECT "FILE".* FROM "XH_OA"."DMIS_SYS_DOC" "DOC" Inner JOIN "XH_OA"."DMIS_SYS_FILE" "FILE" ON ( "DOC"."DOC_ID" = "FILE"."DOC_ID" )   WHERE  ("DOC"."DOC_ID" IN ('4488','4488','4330','4323','4488','4330','4323','4488','3855'))   AND ((( "FILE"."REVISION_STATE" = :REVISION_STATE1 ) AND ( "FILE"."FILE_STATE" = :FILE_STATE2 )) AND ( "FILE"."FILE_ORIGIN" <> :FILE_ORIGIN3 )) 
[Pars]:
[Name]::REVISION_STATE1 [Value]:1 [Type]:String    
[Name]::FILE_STATE2 [Value]:1 [Type]:String    
[Name]::FILE_ORIGIN3 [Value]:2 [Type]:String    

2025-06-25 19:34:10.089 +08:00 [INF] HTTP GET /api/Base/GetEnclosureInfo responded 200 in 2172.7131 ms
2025-06-25 19:34:10.089 +08:00 [INF] 【接口超时阀值预警】 [c7e5e2ba1307af784e11d06cd91e9b9e]接口/api/Base/GetEnclosureInfo,耗时:[2173]毫秒
2025-06-25 19:34:24.198 +08:00 [INF] 【SQL执行耗时:380.9846ms】

[Sql]:SELECT "DOC_ID","DOC_NAME","DOC_PATH","PDF_PREVIEW_PATH","DOC_SUFFIX","DOC_TYPE","EMS_INFO_ID","HOSPITAL_ID","DOC_CLASS","DOC_INFO_ID","EQUIPMENT_ID","DOC_FILE","UPLOAD_PERSON","UPLOAD_TIME","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","DOC_STATE","REMARK" FROM "XH_OA"."EMS_DOC_INFO"  WHERE ( "DOC_CLASS" = :DOC_CLASS0 )  AND ( "DOC_STATE" = :DOC_STATE1 )  AND (( "EQUIPMENT_ID" = :EQUIPMENT_ID2 ) OR ( "DOC_INFO_ID" = :DOC_INFO_ID3 )) 
[Pars]:
[Name]::DOC_CLASS0 [Value]:SOP档案 [Type]:String    
[Name]::DOC_STATE1 [Value]:1 [Type]:String    
[Name]::EQUIPMENT_ID2 [Value]:C3157ECF9C984EE18972526D6AEE2D00 [Type]:String    
[Name]::DOC_INFO_ID3 [Value]:C3157ECF9C984EE18972526D6AEE2D00 [Type]:String    

2025-06-25 19:35:04.958 +08:00 [INF] 【SQL执行耗时:357.5857ms】

[Sql]:SELECT "FILE_LINK_ID","HOSPITAL_ID","DATA_ID","UNION_TYPE","MODULE_ID","FILE_ID","FILE_DATA_TYPE","DOC_ID","DOC_CATALOG_FLAG","FILE_LINK_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","DOC_LINK_ANND" FROM "XH_OA"."OA_DOC_FILE_LINK"  WHERE ( "MODULE_ID" = :MODULE_ID0 )  AND ( "HOSPITAL_ID" = :HOSPITAL_ID1 )  AND ( "UNION_TYPE" = :UNION_TYPE2 )  AND ((( "DATA_ID" = :DATA_ID3 ) AND ( "FILE_DATA_TYPE" = :FILE_DATA_TYPE4 )) AND ( "FILE_LINK_STATE" = :FILE_LINK_STATE5 )) 
[Pars]:
[Name]::MODULE_ID0 [Value]:H82 [Type]:String    
[Name]::HOSPITAL_ID1 [Value]:33A001 [Type]:String    
[Name]::UNION_TYPE2 [Value]:H82SOP [Type]:String    
[Name]::DATA_ID3 [Value]:C3157ECF9C984EE18972526D6AEE2D00 [Type]:String    
[Name]::FILE_DATA_TYPE4 [Value]:2 [Type]:String    
[Name]::FILE_LINK_STATE5 [Value]:1 [Type]:String    

2025-06-25 19:35:05.388 +08:00 [INF] 【SQL执行耗时:355.7752ms】

[Sql]:SELECT "FILE".* FROM "XH_OA"."DMIS_SYS_DOC" "DOC" Inner JOIN "XH_OA"."DMIS_SYS_FILE" "FILE" ON ( "DOC"."DOC_ID" = "FILE"."DOC_ID" )   WHERE  ("DOC"."DOC_ID" IN ('4488','4488','4330','4323','4488','4330','4323','4488','3855'))   AND ((( "FILE"."REVISION_STATE" = :REVISION_STATE1 ) AND ( "FILE"."FILE_STATE" = :FILE_STATE2 )) AND ( "FILE"."FILE_ORIGIN" <> :FILE_ORIGIN3 )) 
[Pars]:
[Name]::REVISION_STATE1 [Value]:1 [Type]:String    
[Name]::FILE_STATE2 [Value]:1 [Type]:String    
[Name]::FILE_ORIGIN3 [Value]:2 [Type]:String    

2025-06-25 19:35:05.424 +08:00 [INF] HTTP GET /api/Base/GetEnclosureInfo responded 200 in 41655.7308 ms
2025-06-25 19:35:05.425 +08:00 [INF] 【接口超时阀值预警】 [b15b19d190eb6d919a02c8d5d5d11929]接口/api/Base/GetEnclosureInfo,耗时:[41656]毫秒
2025-06-25 19:36:53.376 +08:00 [INF] 【SQL执行耗时:359.0592ms】

[Sql]:SELECT "DOC_ID","DOC_NAME","DOC_PATH","PDF_PREVIEW_PATH","DOC_SUFFIX","DOC_TYPE","EMS_INFO_ID","HOSPITAL_ID","DOC_CLASS","DOC_INFO_ID","EQUIPMENT_ID","DOC_FILE","UPLOAD_PERSON","UPLOAD_TIME","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","DOC_STATE","REMARK" FROM "XH_OA"."EMS_DOC_INFO"  WHERE ( "DOC_CLASS" = :DOC_CLASS0 )  AND ( "DOC_STATE" = :DOC_STATE1 )  AND (( "EQUIPMENT_ID" = :EQUIPMENT_ID2 ) OR ( "DOC_INFO_ID" = :DOC_INFO_ID3 )) 
[Pars]:
[Name]::DOC_CLASS0 [Value]:SOP档案 [Type]:String    
[Name]::DOC_STATE1 [Value]:1 [Type]:String    
[Name]::EQUIPMENT_ID2 [Value]:C3157ECF9C984EE18972526D6AEE2D00 [Type]:String    
[Name]::DOC_INFO_ID3 [Value]:C3157ECF9C984EE18972526D6AEE2D00 [Type]:String    

2025-06-25 19:37:37.200 +08:00 [INF] 【SQL执行耗时:534.5111ms】

[Sql]:UPDATE "XH_OA"."EMS_DOC_INFO"  SET
            "DOC_STATE" = :Const0   WHERE ( "DOC_CLASS" = :DOC_CLASS1 ) AND ( "DOC_STATE" = :DOC_STATE2 ) AND (( "EQUIPMENT_ID" = :EQUIPMENT_ID3 ) OR ( "DOC_INFO_ID" = :DOC_INFO_ID4 )) 
[Pars]:
[Name]::Const0 [Value]:0 [Type]:String    
[Name]::DOC_CLASS1 [Value]:SOP档案 [Type]:String    
[Name]::DOC_STATE2 [Value]:1 [Type]:String    
[Name]::EQUIPMENT_ID3 [Value]:C3157ECF9C984EE18972526D6AEE2D00 [Type]:String    
[Name]::DOC_INFO_ID4 [Value]:C3157ECF9C984EE18972526D6AEE2D00 [Type]:String    
[Name]::DOC_ID [Value]: [Type]:String    

2025-06-25 19:37:42.153 +08:00 [INF] 【SQL执行耗时:466.2217ms】

[Sql]:SELECT "FILE_LINK_ID","HOSPITAL_ID","DATA_ID","UNION_TYPE","MODULE_ID","FILE_ID","FILE_DATA_TYPE","DOC_ID","DOC_CATALOG_FLAG","FILE_LINK_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","DOC_LINK_ANND" FROM "XH_OA"."OA_DOC_FILE_LINK"  WHERE ( "MODULE_ID" = :MODULE_ID0 )  AND ( "HOSPITAL_ID" = :HOSPITAL_ID1 )  AND ( "UNION_TYPE" = :UNION_TYPE2 )  AND ((( "DATA_ID" = :DATA_ID3 ) AND ( "FILE_DATA_TYPE" = :FILE_DATA_TYPE4 )) AND ( "FILE_LINK_STATE" = :FILE_LINK_STATE5 )) 
[Pars]:
[Name]::MODULE_ID0 [Value]:H82 [Type]:String    
[Name]::HOSPITAL_ID1 [Value]:33A001 [Type]:String    
[Name]::UNION_TYPE2 [Value]:H82SOP [Type]:String    
[Name]::DATA_ID3 [Value]:C3157ECF9C984EE18972526D6AEE2D00 [Type]:String    
[Name]::FILE_DATA_TYPE4 [Value]:2 [Type]:String    
[Name]::FILE_LINK_STATE5 [Value]:1 [Type]:String    

2025-06-25 19:37:51.792 +08:00 [INF] 【SQL执行耗时:359.5893ms】

[Sql]:SELECT "FILE".* FROM "XH_OA"."DMIS_SYS_DOC" "DOC" Inner JOIN "XH_OA"."DMIS_SYS_FILE" "FILE" ON ( "DOC"."DOC_ID" = "FILE"."DOC_ID" )   WHERE  ("DOC"."DOC_ID" IN ('4488','4488','4330','4323','4488','4330','4323','4488','3855'))   AND ((( "FILE"."REVISION_STATE" = :REVISION_STATE1 ) AND ( "FILE"."FILE_STATE" = :FILE_STATE2 )) AND ( "FILE"."FILE_ORIGIN" <> :FILE_ORIGIN3 )) 
[Pars]:
[Name]::REVISION_STATE1 [Value]:1 [Type]:String    
[Name]::FILE_STATE2 [Value]:1 [Type]:String    
[Name]::FILE_ORIGIN3 [Value]:2 [Type]:String    

2025-06-25 19:37:58.774 +08:00 [INF] HTTP GET /api/Base/GetEnclosureInfo responded 200 in 65802.3685 ms
2025-06-25 19:37:58.774 +08:00 [INF] 【接口超时阀值预警】 [0beb06fa31686755204a644df99a9237]接口/api/Base/GetEnclosureInfo,耗时:[65802]毫秒
2025-06-25 19:38:07.837 +08:00 [INF] 【SQL执行耗时:346.1483ms】

[Sql]:SELECT "DOC_ID","DOC_NAME","DOC_PATH","PDF_PREVIEW_PATH","DOC_SUFFIX","DOC_TYPE","EMS_INFO_ID","HOSPITAL_ID","DOC_CLASS","DOC_INFO_ID","EQUIPMENT_ID","DOC_FILE","UPLOAD_PERSON","UPLOAD_TIME","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","DOC_STATE","REMARK" FROM "XH_OA"."EMS_DOC_INFO"  WHERE ( "DOC_CLASS" = :DOC_CLASS0 )  AND ( "DOC_STATE" = :DOC_STATE1 )  AND (( "EQUIPMENT_ID" = :EQUIPMENT_ID2 ) OR ( "DOC_INFO_ID" = :DOC_INFO_ID3 )) 
[Pars]:
[Name]::DOC_CLASS0 [Value]:SOP档案 [Type]:String    
[Name]::DOC_STATE1 [Value]:1 [Type]:String    
[Name]::EQUIPMENT_ID2 [Value]:C3157ECF9C984EE18972526D6AEE2D00 [Type]:String    
[Name]::DOC_INFO_ID3 [Value]:C3157ECF9C984EE18972526D6AEE2D00 [Type]:String    

2025-06-25 19:38:12.985 +08:00 [INF] 【SQL执行耗时:347.0318ms】

[Sql]:SELECT "FILE_LINK_ID","HOSPITAL_ID","DATA_ID","UNION_TYPE","MODULE_ID","FILE_ID","FILE_DATA_TYPE","DOC_ID","DOC_CATALOG_FLAG","FILE_LINK_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","DOC_LINK_ANND" FROM "XH_OA"."OA_DOC_FILE_LINK"  WHERE ( "MODULE_ID" = :MODULE_ID0 )  AND ( "HOSPITAL_ID" = :HOSPITAL_ID1 )  AND ( "UNION_TYPE" = :UNION_TYPE2 )  AND ((( "DATA_ID" = :DATA_ID3 ) AND ( "FILE_DATA_TYPE" = :FILE_DATA_TYPE4 )) AND ( "FILE_LINK_STATE" = :FILE_LINK_STATE5 )) 
[Pars]:
[Name]::MODULE_ID0 [Value]:H82 [Type]:String    
[Name]::HOSPITAL_ID1 [Value]:33A001 [Type]:String    
[Name]::UNION_TYPE2 [Value]:H82SOP [Type]:String    
[Name]::DATA_ID3 [Value]:C3157ECF9C984EE18972526D6AEE2D00 [Type]:String    
[Name]::FILE_DATA_TYPE4 [Value]:2 [Type]:String    
[Name]::FILE_LINK_STATE5 [Value]:1 [Type]:String    

2025-06-25 19:38:13.442 +08:00 [INF] 【SQL执行耗时:384.7572ms】

[Sql]:SELECT "FILE".* FROM "XH_OA"."DMIS_SYS_DOC" "DOC" Inner JOIN "XH_OA"."DMIS_SYS_FILE" "FILE" ON ( "DOC"."DOC_ID" = "FILE"."DOC_ID" )   WHERE  ("DOC"."DOC_ID" IN ('4488','4488','4330','4323','4488','4330','4323','4488','3855'))   AND ((( "FILE"."REVISION_STATE" = :REVISION_STATE1 ) AND ( "FILE"."FILE_STATE" = :FILE_STATE2 )) AND ( "FILE"."FILE_ORIGIN" <> :FILE_ORIGIN3 )) 
[Pars]:
[Name]::REVISION_STATE1 [Value]:1 [Type]:String    
[Name]::FILE_STATE2 [Value]:1 [Type]:String    
[Name]::FILE_ORIGIN3 [Value]:2 [Type]:String    

2025-06-25 19:38:13.480 +08:00 [INF] HTTP GET /api/Base/GetEnclosureInfo responded 200 in 6057.2546 ms
2025-06-25 19:38:13.481 +08:00 [INF] 【接口超时阀值预警】 [6c2736b20bbca781d11ee0c7fbf433a6]接口/api/Base/GetEnclosureInfo,耗时:[6057]毫秒
2025-06-25 19:49:45.572 +08:00 [INF] ==>App Start..2025-06-25 19:49:45
2025-06-25 19:49:45.745 +08:00 [INF] ==>当前模块:H82,6.25.300
2025-06-25 19:49:45.748 +08:00 [INF] ==>当前框架版本版本:xinghe.utility.**********
2025-06-25 19:49:47.356 +08:00 [INF] ==>基础连接请求完成.
2025-06-25 19:49:47.726 +08:00 [WRN] ==>角色权限验证已全局禁用
2025-06-25 19:49:48.110 +08:00 [INF]  正在通过S01写入版本信息...
2025-06-25 19:49:48.427 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-06-25 19:49:48.435 +08:00 [INF]  正在通过H04写入版本信息...
2025-06-25 19:49:49.231 +08:00 [INF] ==>数据库组2:未定义,[SugarDbContext_Master2/SugarDbContext_Slave2]不可用
2025-06-25 19:49:49.275 +08:00 [INF] ==>版本写入成功:6.25.300
2025-06-25 19:49:49.322 +08:00 [INF] 访问 /api/info 获得更多系统运行信息
2025-06-25 19:49:49.738 +08:00 [INF] 正在检查旧数据是否完整...
2025-06-25 19:49:49.739 +08:00 [INF] 开始检查EMS_EQUIPMENT_INFO旧数据是否完整...
2025-06-25 19:49:51.041 +08:00 [INF] 检查EMS_EQUIPMENT_INFO旧数据结束，已更新0条...
2025-06-25 19:49:53.282 +08:00 [INF] ==>初始化完成..
2025-06-25 19:49:53.303 +08:00 [INF] ==>[自动日志清理]已开启.日志保留天数:15
2025-06-25 19:49:53.305 +08:00 [INF] 设备启用任务
2025-06-25 19:49:53.306 +08:00 [INF] ==>[当前日志文件路径]:C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\bin\Debug\net6.0\logs
2025-06-25 19:49:53.686 +08:00 [INF] 【SQL执行耗时:358.3202ms】

[Sql]:SELECT "START_STOP_ID","HOSPITAL_ID","EQUIPMENT_ID","START_DATE","START_CAUSE","OPER_PERSON","OPER_TIME","START_STOP_STATE","START_STOP_TYPE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK" FROM "XH_OA"."EMS_START_STOP"  WHERE (( "START_STOP_STATE" = :START_STOP_STATE0 ) AND ( "START_STOP_TYPE" = :START_STOP_TYPE1 )) 
[Pars]:
[Name]::START_STOP_STATE0 [Value]:1 [Type]:String    
[Name]::START_STOP_TYPE1 [Value]:1 [Type]:String    

2025-06-25 19:49:53.832 +08:00 [WRN] Overriding address(es) 'https://localhost:18482'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-06-25 19:49:53.846 +08:00 [INF] Now listening on: https://[::]:18482
2025-06-25 19:49:53.848 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-25 19:49:53.848 +08:00 [INF] Hosting environment: Development
2025-06-25 19:49:53.849 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\
2025-06-25 19:52:14.538 +08:00 [INF] 【SQL执行耗时:376.7886ms】

[Sql]:SELECT "DOC_ID","DOC_NAME","DOC_PATH","PDF_PREVIEW_PATH","DOC_SUFFIX","DOC_TYPE","EMS_INFO_ID","HOSPITAL_ID","DOC_CLASS","DOC_INFO_ID","EQUIPMENT_ID","DOC_FILE","UPLOAD_PERSON","UPLOAD_TIME","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","DOC_STATE","REMARK" FROM "XH_OA"."EMS_DOC_INFO"  WHERE ( "DOC_CLASS" = :DOC_CLASS0 )  AND ( "DOC_STATE" = :DOC_STATE1 )  AND (( "EQUIPMENT_ID" = :EQUIPMENT_ID2 ) OR ( "DOC_INFO_ID" = :DOC_INFO_ID3 )) 
[Pars]:
[Name]::DOC_CLASS0 [Value]:SOP档案 [Type]:String    
[Name]::DOC_STATE1 [Value]:1 [Type]:String    
[Name]::EQUIPMENT_ID2 [Value]:C3157ECF9C984EE18972526D6AEE2D00 [Type]:String    
[Name]::DOC_INFO_ID3 [Value]:C3157ECF9C984EE18972526D6AEE2D00 [Type]:String    

2025-06-25 19:52:14.991 +08:00 [INF] 【SQL执行耗时:382.4871ms】

[Sql]:SELECT "FILE_LINK_ID","HOSPITAL_ID","DATA_ID","UNION_TYPE","MODULE_ID","FILE_ID","FILE_DATA_TYPE","DOC_ID","DOC_CATALOG_FLAG","FILE_LINK_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","DOC_LINK_ANND" FROM "XH_OA"."OA_DOC_FILE_LINK"  WHERE ( "MODULE_ID" = :MODULE_ID0 )  AND ( "HOSPITAL_ID" = :HOSPITAL_ID1 )  AND ( "UNION_TYPE" = :UNION_TYPE2 )  AND ((( "DATA_ID" = :DATA_ID3 ) AND ( "FILE_DATA_TYPE" = :FILE_DATA_TYPE4 )) AND ( "FILE_LINK_STATE" = :FILE_LINK_STATE5 )) 
[Pars]:
[Name]::MODULE_ID0 [Value]:H82 [Type]:String    
[Name]::HOSPITAL_ID1 [Value]:33A001 [Type]:String    
[Name]::UNION_TYPE2 [Value]:H82SOP [Type]:String    
[Name]::DATA_ID3 [Value]:C3157ECF9C984EE18972526D6AEE2D00 [Type]:String    
[Name]::FILE_DATA_TYPE4 [Value]:2 [Type]:String    
[Name]::FILE_LINK_STATE5 [Value]:1 [Type]:String    

2025-06-25 19:54:50.947 +08:00 [INF] 【SQL执行耗时:358.1718ms】

[Sql]:SELECT "DOC_ID","DOC_NAME","DOC_PATH","PDF_PREVIEW_PATH","DOC_SUFFIX","DOC_TYPE","EMS_INFO_ID","HOSPITAL_ID","DOC_CLASS","DOC_INFO_ID","EQUIPMENT_ID","DOC_FILE","UPLOAD_PERSON","UPLOAD_TIME","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","DOC_STATE","REMARK" FROM "XH_OA"."EMS_DOC_INFO"  WHERE ( "DOC_CLASS" = :DOC_CLASS0 )  AND ( "DOC_STATE" = :DOC_STATE1 )  AND (( "EQUIPMENT_ID" = :EQUIPMENT_ID2 ) OR ( "DOC_INFO_ID" = :DOC_INFO_ID3 )) 
[Pars]:
[Name]::DOC_CLASS0 [Value]:SOP档案 [Type]:String    
[Name]::DOC_STATE1 [Value]:1 [Type]:String    
[Name]::EQUIPMENT_ID2 [Value]:C3157ECF9C984EE18972526D6AEE2D00 [Type]:String    
[Name]::DOC_INFO_ID3 [Value]:C3157ECF9C984EE18972526D6AEE2D00 [Type]:String    

2025-06-25 19:54:51.918 +08:00 [INF] 【SQL执行耗时:341.2468ms】

[Sql]:SELECT "FILE_LINK_ID","HOSPITAL_ID","DATA_ID","UNION_TYPE","MODULE_ID","FILE_ID","FILE_DATA_TYPE","DOC_ID","DOC_CATALOG_FLAG","FILE_LINK_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","DOC_LINK_ANND" FROM "XH_OA"."OA_DOC_FILE_LINK"  WHERE ( "MODULE_ID" = :MODULE_ID0 )  AND ( "HOSPITAL_ID" = :HOSPITAL_ID1 )  AND ( "UNION_TYPE" = :UNION_TYPE2 )  AND ((( "DATA_ID" = :DATA_ID3 ) AND ( "FILE_DATA_TYPE" = :FILE_DATA_TYPE4 )) AND ( "FILE_LINK_STATE" = :FILE_LINK_STATE5 )) 
[Pars]:
[Name]::MODULE_ID0 [Value]:H82 [Type]:String    
[Name]::HOSPITAL_ID1 [Value]:33A001 [Type]:String    
[Name]::UNION_TYPE2 [Value]:H82SOP [Type]:String    
[Name]::DATA_ID3 [Value]:C3157ECF9C984EE18972526D6AEE2D00 [Type]:String    
[Name]::FILE_DATA_TYPE4 [Value]:2 [Type]:String    
[Name]::FILE_LINK_STATE5 [Value]:1 [Type]:String    

2025-06-25 19:55:32.134 +08:00 [INF] 【SQL执行耗时:406.2973ms】

[Sql]:SELECT "DOC_ID","DOC_NAME","DOC_PATH","PDF_PREVIEW_PATH","DOC_SUFFIX","DOC_TYPE","EMS_INFO_ID","HOSPITAL_ID","DOC_CLASS","DOC_INFO_ID","EQUIPMENT_ID","DOC_FILE","UPLOAD_PERSON","UPLOAD_TIME","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","DOC_STATE","REMARK" FROM "XH_OA"."EMS_DOC_INFO"  WHERE ( "DOC_CLASS" = :DOC_CLASS0 )  AND ( "DOC_STATE" = :DOC_STATE1 )  AND (( "EQUIPMENT_ID" = :EQUIPMENT_ID2 ) OR ( "DOC_INFO_ID" = :DOC_INFO_ID3 )) 
[Pars]:
[Name]::DOC_CLASS0 [Value]:SOP档案 [Type]:String    
[Name]::DOC_STATE1 [Value]:1 [Type]:String    
[Name]::EQUIPMENT_ID2 [Value]:C3157ECF9C984EE18972526D6AEE2D00 [Type]:String    
[Name]::DOC_INFO_ID3 [Value]:C3157ECF9C984EE18972526D6AEE2D00 [Type]:String    

2025-06-25 19:55:34.030 +08:00 [INF] 【SQL执行耗时:351.0229ms】

[Sql]:SELECT "FILE_LINK_ID","HOSPITAL_ID","DATA_ID","UNION_TYPE","MODULE_ID","FILE_ID","FILE_DATA_TYPE","DOC_ID","DOC_CATALOG_FLAG","FILE_LINK_STATE","FIRST_RPERSON","FIRST_RTIME","LAST_MPERSON","LAST_MTIME","REMARK","DOC_LINK_ANND" FROM "XH_OA"."OA_DOC_FILE_LINK"  WHERE ( "MODULE_ID" = :MODULE_ID0 )  AND ( "HOSPITAL_ID" = :HOSPITAL_ID1 )  AND ( "UNION_TYPE" = :UNION_TYPE2 )  AND ((( "DATA_ID" = :DATA_ID3 ) AND ( "FILE_DATA_TYPE" = :FILE_DATA_TYPE4 )) AND ( "FILE_LINK_STATE" = :FILE_LINK_STATE5 )) 
[Pars]:
[Name]::MODULE_ID0 [Value]:H82 [Type]:String    
[Name]::HOSPITAL_ID1 [Value]:33A001 [Type]:String    
[Name]::UNION_TYPE2 [Value]:H82SOP [Type]:String    
[Name]::DATA_ID3 [Value]:C3157ECF9C984EE18972526D6AEE2D00 [Type]:String    
[Name]::FILE_DATA_TYPE4 [Value]:2 [Type]:String    
[Name]::FILE_LINK_STATE5 [Value]:1 [Type]:String    

2025-06-25 19:55:55.768 +08:00 [INF] 【SQL执行耗时:375.735ms】

[Sql]:SELECT "FILE".* FROM "XH_OA"."DMIS_SYS_DOC" "DOC" Inner JOIN "XH_OA"."DMIS_SYS_FILE" "FILE" ON ( "DOC"."DOC_ID" = "FILE"."DOC_ID" )   WHERE  ("DOC"."DOC_ID" IN ('4488','4488','4330','4323','4488','4330','4323','4488','3855'))   AND ((( "FILE"."REVISION_STATE" = :REVISION_STATE1 ) AND ( "FILE"."FILE_STATE" = :FILE_STATE2 )) AND ( "FILE"."FILE_ORIGIN" <> :FILE_ORIGIN3 )) 
[Pars]:
[Name]::REVISION_STATE1 [Value]:1 [Type]:String    
[Name]::FILE_STATE2 [Value]:1 [Type]:String    
[Name]::FILE_ORIGIN3 [Value]:2 [Type]:String    

2025-06-25 19:55:55.868 +08:00 [INF] HTTP GET /api/Base/GetEnclosureInfo responded 200 in 233679.3373 ms
2025-06-25 19:55:55.871 +08:00 [INF] 【接口超时阀值预警】 [5caca1e81466e7f83be85575fbe03da7]接口/api/Base/GetEnclosureInfo,耗时:[233687]毫秒
