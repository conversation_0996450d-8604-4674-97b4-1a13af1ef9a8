﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.Entities.Common
{
    [DBOwner("XH_SYS")]
    [SugarTable("LIS6_SINSTRUMENT_INFO")]
    /// <summary>
    /// 设备型号表
    /// </summary>
    public class LIS6_SINSTRUMENT_INFO
    {
        /// <summary>
        /// 设备型号ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string SINSTRUMENT_ID { get; set; }

        /// <summary>
        /// 医疗机构id
        /// </summary>
        public string HOSPITAL_ID { get; set; }

        /// <summary>
        /// 科室id
        /// </summary>
        public string LAB_ID { get; set; }

        /// <summary>
        /// 设备型号名称
        /// </summary>
        public string SINSTRUMENT_NAME { get; set; }

        /// <summary>
        /// 设备型号简称
        /// </summary>
        public string SINSTRUMENT_SHORT { get; set; }

        /// <summary>
        /// 设备型号编号
        /// </summary>
        public string SINSTRUMENT_NUM { get; set; }


        /// <summary>
        /// 设备型号代号
        /// </summary>
        public string SINSTRUMENT_CODE { get; set; }


        /// <summary>
        /// 自定义型号
        /// </summary>
        public string SINSTRUMENT_MODE { get; set; }


        /// <summary>
        /// 设备型号描述
        /// </summary>
        public string SINSTRUMENT_DES { get; set; }

        /// <summary>
        /// 设备型号状态
        /// </summary>
        public string SINSTRUMENT_STATE { get; set; }


        /// <summary>
        /// 首次登记人
        /// </summary>
        public string FIRST_RPERSON { get; set; }


        /// <summary>
        /// 首次登记时间
        /// </summary>
        public string FIRST_RTIME { get; set; }


        /// <summary>
        /// 最后修改人
        /// </summary>
        public string LAST_MPERSON { get; set; }


        /// <summary>
        /// 最后修改时间
        /// </summary>
        public string LAST_MTIME { get; set; }


        /// <summary>
        /// 备注
        /// </summary>
        public string REMARK { get; set; }

        /// <summary>
        /// 设备型号分类
        /// </summary>
        public string SINSTRUMENT_CLASS { get; set; }


        /// <summary>
        /// 
        /// </summary>
        public string SINSTRU_SERIALID { get; set; }


        /// <summary>
        /// 设备型号类型
        /// </summary>
        public string SINSTRUMENT_TYPE { get; set; }


        /// <summary>
        /// 系统实例ID
        /// </summary>
        public string INSTANCE_ID { get; set; }


        /// <summary>
        /// 设备型号架子类型
        /// </summary>
        public string SHELF_TYPE { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string FACTORY_ID { get; set; }

    }
}
