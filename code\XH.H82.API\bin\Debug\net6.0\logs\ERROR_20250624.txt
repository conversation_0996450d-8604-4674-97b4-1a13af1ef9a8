2025-06-24 17:29:47.917 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-06-24 17:30:55.243 +08:00 [ERR] pdf转图片报错:Invalid/Unknown/Unsupported format
2025-06-24 17:30:55.436 +08:00 [ERR] 调用S28模块[/api/Common/SetUploadFile?folderName=EMS/datafile]请求完成,但是返回了错误:没有找到需要上传的数据！！
2025-06-24 17:30:55.510 +08:00 [ERR] 操作失败:
没有找到需要上传的数据！！
2025-06-24 17:31:20.107 +08:00 [ERR] pdf转图片报错:Invalid/Unknown/Unsupported format
2025-06-24 17:31:35.882 +08:00 [ERR] 调用S28模块[/api/Common/SetUploadFile?folderName=EMS/datafile]请求完成,但是返回了错误:没有找到需要上传的数据！！
2025-06-24 17:31:38.820 +08:00 [ERR] 操作失败:
没有找到需要上传的数据！！
2025-06-24 17:32:53.006 +08:00 [ERR] pdf转图片报错:Invalid/Unknown/Unsupported format
2025-06-24 18:22:04.150 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-06-24 18:26:37.266 +08:00 [ERR] 未处理的异常::System.NullReferenceException: Object reference not set to an instance of an object.
   at XH.H82.Services.AuthorizationRecord.AuthorizationRecordService.AddAuthorizationRecord(String equipmentId, AuthorizationRecordDto input) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\AuthorizationRecord\AuthorizationRecordService.cs:line 152
   at Castle.Proxies.Invocations.IAuthorizationRecordService_AddAuthorizationRecord.InvokeMethodOnTarget()
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.UseCacheWhenFailedAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.CacheAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at Castle.Proxies.IAuthorizationRecordServiceProxy.AddAuthorizationRecord(String equipmentId, AuthorizationRecordDto input)
   at XH.H82.API.Controllers.AuthorizationRecord.AuthorizationRecordController.AddAuthorizeRecord(String equipmentId, AuthorizationRecordDto input) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\Controllers\AuthorizationRecord\AuthorizationRecordController.cs:line 111
   at lambda_method908(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-06-24 18:26:37.270 +08:00 [ERR] HTTP POST /api/AuthorizationRecord/AddAuthorizeRecord/DBA5C9027E7F463F99B46673F5D6ECFA responded 500 in 10674.4972 ms
2025-06-24 18:29:42.683 +08:00 [ERR] 未处理的异常::System.NullReferenceException: Object reference not set to an instance of an object.
   at XH.H82.Services.EquipmentDocService.GetAuthorizeList(String equipmentId)
   at Castle.Proxies.Invocations.IEquipmentDocService_GetAuthorizeList.InvokeMethodOnTarget()
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.UseCacheWhenFailedAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.CacheAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at Castle.Proxies.IEquipmentDocServiceProxy.GetAuthorizeList(String equipmentId)
   at XH.H82.API.Controllers.EquipmentDocController.GetAuthorizeList(String equipmentId) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\Controllers\EquipmentDocController.cs:line 612
   at lambda_method924(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-06-24 18:29:42.685 +08:00 [ERR] HTTP GET /api/EquipmentDoc/GetAuthorizeList responded 500 in 26965.4937 ms
2025-06-24 18:30:55.337 +08:00 [ERR] ==>请求基础连接时发生错误:不知道这样的主机。 (d01.lis-china.com:18801)
System.Net.Http.HttpRequestException: 不知道这样的主机。 (d01.lis-china.com:18801)
 ---> System.Net.Sockets.SocketException (11001): 不知道这样的主机。
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|277_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at RestSharp.ResponseThrowExtension.ThrowIfError(RestResponse response)
   at RestSharp.RestClient.ExecuteAsync(RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.ExecuteAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.GetAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.AsyncHelpers.<>c__DisplayClass1_0`1.<<RunSync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.<Run>g__PostCallback|7_0(Object _)
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.Run()
   at RestSharp.AsyncHelpers.RunSync(Func`1 task)
   at RestSharp.AsyncHelpers.RunSync[T](Func`1 task)
   at RestSharp.RestClientExtensions.Get[T](IRestClient client, RestRequest request)
   at H.BASE.Infrastructure.BaseConfigRequestHelper.GetBaseConfigFromBaseService(String urlS01, String hospitalId, String moduleId)
2025-06-24 18:30:57.424 +08:00 [ERR] ==>请求基础连接时发生错误:不知道这样的主机。 (d01.lis-china.com:18801)
System.Net.Http.HttpRequestException: 不知道这样的主机。 (d01.lis-china.com:18801)
 ---> System.Net.Sockets.SocketException (11001): 不知道这样的主机。
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|277_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at RestSharp.ResponseThrowExtension.ThrowIfError(RestResponse response)
   at RestSharp.RestClient.ExecuteAsync(RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.ExecuteAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.GetAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.AsyncHelpers.<>c__DisplayClass1_0`1.<<RunSync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.<Run>g__PostCallback|7_0(Object _)
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.Run()
   at RestSharp.AsyncHelpers.RunSync(Func`1 task)
   at RestSharp.AsyncHelpers.RunSync[T](Func`1 task)
   at RestSharp.RestClientExtensions.Get[T](IRestClient client, RestRequest request)
   at H.BASE.Infrastructure.BaseConfigRequestHelper.GetBaseConfigFromBaseService(String urlS01, String hospitalId, String moduleId)
2025-06-24 18:30:59.448 +08:00 [ERR] ==>请求基础连接时发生错误:不知道这样的主机。 (d01.lis-china.com:18801)
System.Net.Http.HttpRequestException: 不知道这样的主机。 (d01.lis-china.com:18801)
 ---> System.Net.Sockets.SocketException (11001): 不知道这样的主机。
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|277_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at RestSharp.ResponseThrowExtension.ThrowIfError(RestResponse response)
   at RestSharp.RestClient.ExecuteAsync(RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.ExecuteAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.GetAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.AsyncHelpers.<>c__DisplayClass1_0`1.<<RunSync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.<Run>g__PostCallback|7_0(Object _)
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.Run()
   at RestSharp.AsyncHelpers.RunSync(Func`1 task)
   at RestSharp.AsyncHelpers.RunSync[T](Func`1 task)
   at RestSharp.RestClientExtensions.Get[T](IRestClient client, RestRequest request)
   at H.BASE.Infrastructure.BaseConfigRequestHelper.GetBaseConfigFromBaseService(String urlS01, String hospitalId, String moduleId)
2025-06-24 18:31:03.655 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-06-24 18:31:28.606 +08:00 [ERR] 未处理的异常::System.Exception: No support value(XH.H82.Services.EquipmentDocService+<>c__DisplayClass44_1).authPerson[1] Exception has been thrown by the target of an invocation.
   at SqlSugar.ExpressionTool.DynamicInvoke(Expression expression, MemberExpression memberExpression)
   at SqlSugar.BinaryExpressionResolve.Other(ExpressionParameter parameter)
   at SqlSugar.BinaryExpressionResolve..ctor(ExpressionParameter parameter)
   at SqlSugar.BaseResolve.Start()
   at SqlSugar.BinaryExpressionResolve.Right(ExpressionParameter parameter, String operatorValue, Boolean isEqual, Expression rightExpression, Boolean lsbs)
   at SqlSugar.BinaryExpressionResolve.DefaultBinary(ExpressionParameter parameter, BinaryExpression expression, String operatorValue)
   at SqlSugar.BinaryExpressionResolve.Other(ExpressionParameter parameter)
   at SqlSugar.BinaryExpressionResolve..ctor(ExpressionParameter parameter)
   at SqlSugar.BaseResolve.Start()
   at SqlSugar.LambdaExpressionResolve..ctor(ExpressionParameter parameter)
   at SqlSugar.BaseResolve.Start()
   at SqlSugar.ExpressionContext.Resolve(Expression expression, ResolveExpressType resolveType)
   at SqlSugar.QueryBuilder.GetExpressionValue(Expression expression, ResolveExpressType resolveType)
   at SqlSugar.QueryableProvider`1._Where(Expression expression)
   at SqlSugar.QueryableProvider`1.Where(Expression`1 expression)
   at XH.H82.Services.EquipmentDocService.GetAuthorizeList(String equipmentId) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.Services\EquipmentDocService.cs:line 1063
   at Castle.Proxies.Invocations.IEquipmentDocService_GetAuthorizeList.InvokeMethodOnTarget()
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.UseCacheWhenFailedAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at H.Utility.AOP.CacheAOP.Intercept(IInvocation invocation)
   at Castle.DynamicProxy.AbstractInvocation.Proceed()
   at Castle.Proxies.IEquipmentDocServiceProxy.GetAuthorizeList(String equipmentId)
   at XH.H82.API.Controllers.EquipmentDocController.GetAuthorizeList(String equipmentId) in C:\Users\<USER>\Desktop\projects\h82\behind\code\XH.H82.API\Controllers\EquipmentDocController.cs:line 612
   at lambda_method908(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at XH.LAB.UTILS.Middleware.LabExceptionMiddleware.Invoke(HttpContext context)
2025-06-24 18:31:28.629 +08:00 [ERR] HTTP GET /api/EquipmentDoc/GetAuthorizeList responded 500 in 17340.8449 ms
2025-06-24 18:36:49.073 +08:00 [ERR] ==>请求基础连接时发生错误:不知道这样的主机。 (d01.lis-china.com:18801)
System.Net.Http.HttpRequestException: 不知道这样的主机。 (d01.lis-china.com:18801)
 ---> System.Net.Sockets.SocketException (11001): 不知道这样的主机。
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|277_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at RestSharp.ResponseThrowExtension.ThrowIfError(RestResponse response)
   at RestSharp.RestClient.ExecuteAsync(RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.ExecuteAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.GetAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.AsyncHelpers.<>c__DisplayClass1_0`1.<<RunSync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.<Run>g__PostCallback|7_0(Object _)
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.Run()
   at RestSharp.AsyncHelpers.RunSync(Func`1 task)
   at RestSharp.AsyncHelpers.RunSync[T](Func`1 task)
   at RestSharp.RestClientExtensions.Get[T](IRestClient client, RestRequest request)
   at H.BASE.Infrastructure.BaseConfigRequestHelper.GetBaseConfigFromBaseService(String urlS01, String hospitalId, String moduleId)
2025-06-24 18:36:51.196 +08:00 [ERR] ==>请求基础连接时发生错误:不知道这样的主机。 (d01.lis-china.com:18801)
System.Net.Http.HttpRequestException: 不知道这样的主机。 (d01.lis-china.com:18801)
 ---> System.Net.Sockets.SocketException (11001): 不知道这样的主机。
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|277_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at RestSharp.ResponseThrowExtension.ThrowIfError(RestResponse response)
   at RestSharp.RestClient.ExecuteAsync(RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.ExecuteAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.GetAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.AsyncHelpers.<>c__DisplayClass1_0`1.<<RunSync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.<Run>g__PostCallback|7_0(Object _)
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.Run()
   at RestSharp.AsyncHelpers.RunSync(Func`1 task)
   at RestSharp.AsyncHelpers.RunSync[T](Func`1 task)
   at RestSharp.RestClientExtensions.Get[T](IRestClient client, RestRequest request)
   at H.BASE.Infrastructure.BaseConfigRequestHelper.GetBaseConfigFromBaseService(String urlS01, String hospitalId, String moduleId)
2025-06-24 18:36:53.214 +08:00 [ERR] ==>请求基础连接时发生错误:不知道这样的主机。 (d01.lis-china.com:18801)
System.Net.Http.HttpRequestException: 不知道这样的主机。 (d01.lis-china.com:18801)
 ---> System.Net.Sockets.SocketException (11001): 不知道这样的主机。
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|277_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at RestSharp.ResponseThrowExtension.ThrowIfError(RestResponse response)
   at RestSharp.RestClient.ExecuteAsync(RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.ExecuteAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.RestClientExtensions.GetAsync[T](IRestClient client, RestRequest request, CancellationToken cancellationToken)
   at RestSharp.AsyncHelpers.<>c__DisplayClass1_0`1.<<RunSync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.<Run>g__PostCallback|7_0(Object _)
   at RestSharp.AsyncHelpers.CustomSynchronizationContext.Run()
   at RestSharp.AsyncHelpers.RunSync(Func`1 task)
   at RestSharp.AsyncHelpers.RunSync[T](Func`1 task)
   at RestSharp.RestClientExtensions.Get[T](IRestClient client, RestRequest request)
   at H.BASE.Infrastructure.BaseConfigRequestHelper.GetBaseConfigFromBaseService(String urlS01, String hospitalId, String moduleId)
2025-06-24 18:36:58.684 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
2025-06-24 18:42:39.859 +08:00 [ERR] 写入版本信息时发生错误:==>S01写入版本信息发生错误:相同电脑IP相同版本相同模块的不能重复插入软件模块更新记录表！
