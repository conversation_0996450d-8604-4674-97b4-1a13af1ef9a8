namespace XH.H82.API.Controllers.IoTDevice;

public class IotDeviceUsingRecordDto
{
    /// <summary>
    /// 序号，唯一
    /// </summary>
    public int No { get; set; }
    /// <summary>
    /// 日期 2025-01-02
    /// </summary>
    public string Date { get; set; } = "-";
    /// <summary>
    /// 最早开机时间
    /// </summary>
    public string EarliestStartTime { get; set; } = "-";
    /// <summary>
    /// 最晚关机时间
    /// </summary>
    public string LatestShutdownTime { get; set; } = "-";
    /// <summary>
    /// 待机时长汇总，分钟
    /// </summary>
    public string TotalIdleTime { get; set; } 
    /// <summary>
    /// 使用时长汇总，分钟
    /// </summary>
    public string TotalUsageTime { get; set; }
    
    /// <summary>
    /// 关机时长汇总，分钟
    /// </summary>
    public string TotalShutdownTime { get; set; }
    
    /// <summary>
    /// 最早报警时间
    /// </summary>
    public string FirstWarningTime { get; set; } = "-";
    /// <summary>
    /// 异常处理次数
    /// </summary>
    public int HandleCont { get; set; }
}