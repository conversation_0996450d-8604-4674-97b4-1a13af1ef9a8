﻿using AutoMapper;
using <PERSON>.Utility;
using XH.H82.Models.Entities;
using XH.H82.Models.Entities.InkScreen;

namespace XH.H82.Models.InkScreenTemplate.Dto
{
    [AutoMap(typeof(EMS_INKSCREEN_TEMPLATE))]
    public class TemplatesDto : BaseField
    {

        /// <summary>
        /// 模板id
        /// </summary>
        public string TEMPLATE_ID { get; set; }
        /// <summary>
        /// 模板名称
        /// </summary>
        public string TEMPLATE_NAME { get; set; }
        /// <summary>
        /// 模板内容
        /// </summary>
        public string TEMPLATE_CONTENT { get; set; }
        /// <summary>
        /// 模板标题
        /// </summary>
        public string TEMPLATE_TITLE { get; set; }

        /// <summary>
        /// 异常状态亮灯
        /// </summary>
        public bool SET_ABNORMAL { get; set; }
        /// <summary>
        /// 是否设置二维码
        /// </summary>
        public bool SET_QR_CODE { get; set; }
        /// <summary>
        /// 是否设置表格线框
        /// </summary>
        public bool SET_WIREFRAME { get; set; }
        /// <summary>
        /// 应用的专业组
        /// </summary>
        public string PGROUP_SID { get; set; }

        /// <summary>
        /// 应用的专业组中文
        /// </summary>
        public string APPLICATION_GROUPS_NAME { get; set; } = "";



        private List<string> _GROUPS = new List<string>();

        public List<string> GetGroups()
        {
            if (PGROUP_SID.IsNotNullOrEmpty())
            {
                var groups = PGROUP_SID.Split(';');
                foreach (var group in groups)
                {
                    _GROUPS.Add(group);
                }
            }
            return _GROUPS;
        }

    }


    public class TemplateDataAndValue
    {
        public int rowIndex { get; set; }
        public List <TemplateAttribute> cols { get; set; }
    }
    
}
