﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using XH.H82.Models;
using XH.H82.Models.Flow;

namespace XH.H82.Services.Flow;

public class FlowService : IFlowService
{
    
    private readonly IHttpContextAccessor _httpContext;
    private readonly IConfiguration _configuration;
    private readonly H120Client  _h120Client;
    
    public FlowService(H120Client h120Client, IHttpContextAccessor httpContext, IConfiguration configuration)
    {
        _httpContext = httpContext;
        _configuration = configuration;
        _h120Client = new H120Client(httpContext, configuration);
    }
    
    public FlowDto GetFlowDetailByCode(string flowCode)
    {
        //TODO 业务操作
        return _h120Client.GetFlowDetailByCode(flowCode);
    }

    public FlowInstanceDto GetFlowInstanceDetail(string flowInstanceId)
    {
        
        //TODO 业务操作
        return _h120Client.GetFlowInstanceDetail(flowInstanceId);
    }

    public string InitFlow(string flowCode, List<string>? userNos)
    {
        //TODO 业务操作
        
        var  flowDetail = _h120Client.GetFlowDetailByCode(flowCode);
        if (flowDetail.NeedChoseApprover)
        {
            return _h120Client.StartFlowInstanceByCode(flowCode, userNos);
        }
        return _h120Client.StartFlowInstanceByCode(flowCode,new List<string>());
    }

    public bool NextFlow(string flowInstanceId, string flowInstanceNodeId, ApprovalInput approvalInput)
    {
        //TODO 业务操作
        return _h120Client.NextFlow(flowInstanceId, flowInstanceNodeId, approvalInput);
    }

    public bool ApprovedFlow(string flowInstanceId, string approvedRemark, List<string> approvedUserNos)
    {
        var flowInstanceDetail = _h120Client.GetFlowInstanceDetail(flowInstanceId);
        var approvalInput = new ApprovalInput()
        {
            flowOperateType = "2",
            assignPerson = new List<string>(),
            nodeRemark = approvedRemark,
            targetNodeId = null,
            rejectInfo = null,
        };
        if (flowInstanceDetail.NeedChoseApprover)
        {
            approvalInput.assignPerson = approvedUserNos;
        }
        if (flowInstanceDetail.InstanceState == "1" && flowInstanceDetail.FlowInstanceNodes.Any(x => x.InstanceState == "1"))
        { 
            var flowInstanceNode = flowInstanceDetail.FlowInstanceNodes.First(x => x.InstanceState == "1");
            return  _h120Client.NextFlow(flowInstanceId, flowInstanceNode.InstanceNodeId, approvalInput);
        }
        return false;
    }
    
    /// <summary>
    /// 驳回
    /// </summary>
    /// <param name="flowInstanceId">流程实例id</param>
    /// <param name="rejectInfo">驳回原因</param>
    /// <param name="nodeId">指定的节点id（固定的数据）</param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    public bool RejectFlow(string flowInstanceId, string rejectInfo, string nodeId)
    {
        var flowInstanceDetail = _h120Client.GetFlowInstanceDetail(flowInstanceId);
        var approvalInput = new ApprovalInput()
        {
            flowOperateType = "5",
            assignPerson = new List<string>(),
            nodeRemark = null,
            targetNodeId = nodeId,
            rejectInfo = rejectInfo,
        };
        if (flowInstanceDetail.InstanceState == "1" && flowInstanceDetail.FlowInstanceNodes.Any(x => x.InstanceState == "1"))
        {
          
            var flowInstanceNode = flowInstanceDetail.FlowInstanceNodes.First(x => x.InstanceState == "1");
            if (flowInstanceNode.FlowNodeType == "0")
            {
                throw new Exception("当前流程处于开始节点，不能驳回");
            }
            return  _h120Client.NextFlow(flowInstanceId, flowInstanceNode.InstanceNodeId, approvalInput);
        }
        return false;
    }


    /// <summary>
    /// 拒绝
    /// </summary>
    /// <param name="flowInstanceId">流程实例id</param>
    /// <param name="refusedInfo">不通过理由</param>
    /// <param name="userNos">审批人</param>
    /// <returns></returns>
    public bool RefusedFlow( string flowInstanceId, string refusedInfo , List<string>? userNos)
    {
        var flowInstanceDetail = _h120Client.GetFlowInstanceDetail(flowInstanceId);
        var approvalInput = new ApprovalInput()
        {
            flowOperateType = "3",
            assignPerson = new List<string>(),
            nodeRemark = refusedInfo,
            targetNodeId = null,
            rejectInfo = null,
        };
        if (flowInstanceDetail.InstanceState == "1" && flowInstanceDetail.FlowInstanceNodes.Any(x => x.InstanceState == "1"))
        {
            var flowInstanceNode = flowInstanceDetail.FlowInstanceNodes.First(x => x.InstanceState == "1");
            return  _h120Client.NextFlow(flowInstanceId, flowInstanceNode.InstanceNodeId, approvalInput);
        }
        return false;
    }
}