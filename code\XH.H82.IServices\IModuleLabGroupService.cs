﻿using XH.H82.Models;
using XH.H82.Models.Dtos;
using XH.H82.Models.Dtos.Base;
using XH.H82.Models.Entities.Common;
using XH.H82.Models.Entities;
using XH.LAB.UTILS.Models;

namespace XH.H82.IServices
{
    public interface IModuleLabGroupService
    {
        string GetModuleInfoByModuleId(string strHospitalId, string moduleId);
        List<SYS6_INSPECTION_LAB> GetLisInspectionLabInfo(string strUserNo, string strHospitalId);
        List<SYS6_INSPECTION_PGROUP> GetLisInspectionPgroupInfo(string strUserNo, string strHospitalId);
        List<SYS6_INSPECTION_MGROUP> GetLisInspectionMgroupInfo(string strUserNo, string strHospitalId);
        sys6UserDto GetUserInfo(string logId, string password = "");
    }
}
