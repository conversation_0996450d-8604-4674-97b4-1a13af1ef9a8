﻿namespace XH.H82.Services.AuthorizationRecord;

/// <summary>
/// 悬浮框表格的用户信息
/// </summary>
public class TableUserInfoDto
{
    /// <summary>
    /// 主键
    /// </summary>
    public string Id { get; init; } = Guid.NewGuid().ToString();
    /// <summary>
    /// 专业组id
    /// </summary>
    public string PGroupId { get; set; }
    /// <summary>
    /// 专业组名称
    /// </summary>
    public string PGroupName { get; set; }
    /// <summary>
    /// 用户名字
    /// </summary>
    public string UserName { get; set; }
    /// <summary>
    /// 用户UserNo
    /// </summary>
    public string UserNo { get; set; }
}