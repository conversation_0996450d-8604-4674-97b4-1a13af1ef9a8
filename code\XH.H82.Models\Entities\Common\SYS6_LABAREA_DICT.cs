﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.Entities.Common
{
    [DBOwner("XH_SYS")]
    public class SYS6_LABAREA_DICT
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string LABAREA_ID { get; set; }
        public string HOSPITAL_ID { get;set; }
        public string LAB_ID { get; set; }
        public string AREA_ID { get; set; }
        public string LABAREA_NAME { get; set; }
        public string LABAREA_SORT { get; set; }
        public string LABAREA_STATE { get; set; }
        public string FIRST_RPERSON { get; set; }
        public DateTime? FIRST_RTIME { get; set; }
        public string LAST_MPERSON { get; set; }
        public DateTime? LAST_MTIME { get; set; }
        public string REMARK { get; set; }
    }
}
