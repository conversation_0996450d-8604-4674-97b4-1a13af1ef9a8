﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>disable</Nullable>
		<GenerateDocumentationFile>True</GenerateDocumentationFile>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="EPPlus" Version="6.2.6" />
		<PackageReference Include="protobuf-net.Core" Version="3.1.22">
			<TreatAsUsed>true</TreatAsUsed>
		</PackageReference>
		<PackageReference Include="XH.LAB.UTILS" Version="6.25.301.26" />
		<PackageReference Include="Xinghe.Utility" Version="6.25.206" />

	</ItemGroup>

	<ItemGroup>
		<Compile Update="Entities\SYS_USER.cs">
			<Generator>DtsGenerator</Generator>
			<LastGenOutput>SYS_USER.cs.d.ts</LastGenOutput>
		</Compile>
		<Compile Update="Entities\TEST_START_TEMPLATE.cs">
			<Generator>DtsGenerator</Generator>
			<LastGenOutput>TEST_START_TEMPLATE.cs.d.ts</LastGenOutput>
		</Compile>
	</ItemGroup>

	<ItemGroup>
		<None Update="Entities\SYS_USER.cs.d.ts">
			<DesignTime>True</DesignTime>
			<AutoGen>True</AutoGen>
			<DependentUpon>SYS_USER.cs</DependentUpon>
		</None>
		<None Update="Entities\TEST_START_TEMPLATE.cs.d.ts">
			<DesignTime>True</DesignTime>
			<AutoGen>True</AutoGen>
			<DependentUpon>TEST_START_TEMPLATE.cs</DependentUpon>
		</None>
	</ItemGroup>

	<ItemGroup>
		<Folder Include="DeactivationScrapping\Dto\" />
	</ItemGroup>

</Project>
