using System.ComponentModel.DataAnnotations;
using H.Utility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SqlSugar;
using XH.H82.API.Extensions;
using XH.H82.Base.Tree;
using XH.H82.IServices.Sbml;
using XH.H82.Models.Smbl;
using XH.H82.Models.Smbl.Dto;
using XH.H82.Services.DeviceDataRefresh;

namespace XH.H82.API.Controllers.Organization;

/// <inheritdoc />
[Route("api/[controller]/[action]")]
[ApiController]
[Authorize]
public class OrgnizationTreeForSmblController : ControllerBase
{
    private readonly IHttpContextAccessor _httpContext;
    private readonly ClaimsDto _user;
    private readonly ISmblServicve _smblServicve;
    private EquipmentTreeContext _equipmentTreeContext; 

    public OrgnizationTreeForSmblController(IHttpContextAccessor httpContext, ISmblServicve smblServicve, EquipmentTreeContext equipmentTreeContext)
    {
        _httpContext = httpContext;
        _smblServicve = smblServicve;
        _equipmentTreeContext = equipmentTreeContext;
        _user = httpContext.HttpContext?.User.ToClaimsDto();
    }
    
    
    /// <summary>
    /// 生安入口-申购记录页面左侧树
    /// </summary>
    /// <param name="inlet"> 入口枚举  0机构  1科室 2备案实验室 </param>
    /// <param name="id"> 对应的id  入口是机构就是机构id   入口是科室就是科室id  入口是备案实验室就是备案实验室id</param>
    /// <param name="smblLabId"> 备案实验室id</param>
    /// <returns></returns>
    [HttpGet("{id}")]
    [CustomResponseType(typeof(List<AllTreeNode>))]
    public IActionResult SubscriptionTree([Required]SmblInletEnum inlet , [Required]string id , string? smblLabId)
    {
        var tree = inlet switch
        {
            SmblInletEnum.Hospital => _smblServicve.GetSmblHospitalBaseTree(id),
            SmblInletEnum.RecordLab => _smblServicve.GetSmblLabBaseTree(id),
            SmblInletEnum.Lab => _smblServicve.GetLabBaseTree(id),
        };
        var result = _equipmentTreeContext.SmblSubscriptionTree(tree);
        if (smblLabId.IsNotNullOrEmpty())
        {
            switch (inlet)
            {
               case SmblInletEnum.Hospital :
                   tree.rootNode.CHILDREN
                       .ForEach(hospitalNode =>
                       {
                           hospitalNode.CHILDREN.RemoveAll(labNode =>
                           {
                               bool isDelete = labNode.CHILDREN.All(x => x.SOURCE_ID != smblLabId);
                               labNode.CHILDREN.RemoveAll(pgNode => pgNode.SOURCE_ID != smblLabId);
                               return isDelete;
                           });
                       });
                   break;
               case SmblInletEnum.Lab :
                   tree.rootNode.CHILDREN.ForEach(labNode=>
                   {
                       labNode.CHILDREN.RemoveAll(pgNode => pgNode.SOURCE_ID != smblLabId);
                   });
                   break;
            }
        }
        tree.rootNode.ConutChildrensNoAppendRootPath();
        return Ok(result.ToResultDto());
    }

    /// <summary>
    /// 生安入口-设备档案页面左侧树
    /// </summary>
    /// <param name="inlet"> 入口枚举  0机构  1科室 2备案实验室 </param>
    /// <param name="id"> 对应的id  入口是机构就是机构id   入口是科室就是科室id  入口是备案实验室就是备案实验室id</param>
    /// <param name="smblLabId"> 备案实验室id</param>
    /// <param name="onlySmbl">是否只显示生安类型设备</param>
    /// <param name="showHide"> 是否显示隐藏  true显示 false 不显示 </param>
    /// <param name="hasClass">是否显示设备类型层级</param>
    /// <param name="equipmentCode">设备代号（自定义名称）模糊查询</param>
    /// <returns></returns>
    [HttpGet("{id}")]
    [CustomResponseType(typeof(List<AllTreeNode>))]
    public IActionResult EquipmentTree([Required]SmblInletEnum inlet ,
        [Required]string id ,
        string? smblLabId,
        bool onlySmbl = false ,
        bool showHide = false, 
        bool hasClass = false,
        string equipmentCode = null
    )
    {
         var tree = inlet switch
        {
            SmblInletEnum.Hospital => _smblServicve.GetSmblHospitalBaseTree(id),
            SmblInletEnum.RecordLab => _smblServicve.GetSmblLabBaseTree(id),
            SmblInletEnum.Lab => _smblServicve.GetLabBaseTree(id),
            _ => throw new ArgumentOutOfRangeException(nameof(inlet), inlet, null)
        };
          var result = _equipmentTreeContext.SmblEquipmentsTree(tree,equipmentCode,onlySmbl,showHide,hasClass);
          if (smblLabId.IsNotNullOrEmpty())
          {
              switch (inlet)
              {
                  case SmblInletEnum.Hospital :
                      tree.rootNode.CHILDREN
                          .ForEach(hospitalNode =>
                          {
                              hospitalNode.CHILDREN.RemoveAll(labNode =>
                              {
                                  bool isDelete = labNode.CHILDREN.All(x => x.SOURCE_ID != smblLabId);
                                  labNode.CHILDREN.RemoveAll(pgNode => pgNode.SOURCE_ID != smblLabId);
                                  return isDelete;
                              });
                          });
                      break;
                  case SmblInletEnum.Lab :
                      tree.rootNode.CHILDREN.ForEach(labNode=>
                      {
                          labNode.CHILDREN.RemoveAll(pgNode => pgNode.SOURCE_ID != smblLabId);
                      });
                      break;
              }
          }
          tree.rootNode.ConutChildrensNoAppendRootPath();
          
          return Ok(result.ToResultDto());
    }
    
    
    
    /// <summary>
    /// 生安入口-设备档案生安设备类型页面左侧树
    /// </summary>
    /// <param name="inlet"> 入口枚举  0机构  1科室 2备案实验室 </param>
    /// <param name="id"> 对应的id  入口是机构就是机构id   入口是科室就是科室id  入口是备案实验室就是备案实验室id</param>
    /// <param name="onlySmbl">是否只显示生安类型设备</param>
    /// <param name="showHide"> 是否显示隐藏  true显示 false 不显示 </param>
    /// <param name="equipmentCode">设备代号（自定义名称）模糊查询</param>
    /// <returns></returns>
    [HttpGet("{id}")]
    [CustomResponseType(typeof(List<AllTreeNode>))]
    public IActionResult EquipmentClassTree([Required]SmblInletEnum inlet ,
        [Required]string id ,
        bool onlySmbl = false ,
        bool showHide = false, 
        string equipmentCode = null
    )
    {
         var tree = inlet switch
        {
            SmblInletEnum.Hospital => _smblServicve.GetSmblHospitalBaseTree(id),
            SmblInletEnum.RecordLab => _smblServicve.GetSmblLabBaseTree(id),
            SmblInletEnum.Lab => _smblServicve.GetLabBaseTree(id),
            _ => throw new ArgumentOutOfRangeException(nameof(inlet), inlet, null)
        };
          var result = _equipmentTreeContext.SmblEquipmentsClassTree(tree,inlet,equipmentCode,onlySmbl,showHide);
          tree.rootNode.ConutChildrensNoAppendRootPath();
          
          return Ok(result.ToResultDto());
    }
    
    /// <summary>
    /// 生安入口-仪器生安设备类型页面左侧树
    /// </summary>
    /// <param name="inlet"> 入口枚举  0机构  1科室 2备案实验室 </param>
    /// <param name="id"> 对应的id  入口是机构就是机构id   入口是科室就是科室id  入口是备案实验室就是备案实验室id</param>
    /// <param name="onlySmbl">是否只显示生安类型设备</param>
    /// <param name="showHide"> 是否显示隐藏  true显示 false 不显示 </param>
    /// <param name="equipmentCode">设备代号（自定义名称）模糊查询</param>
    /// <returns></returns>
    [HttpGet("{id}")]
    [CustomResponseType(typeof(List<AllTreeNode>))]
    public IActionResult InstrumentsClassTree([Required]SmblInletEnum inlet ,
        [Required]string id ,
        bool onlySmbl = false ,
        bool showHide = false, 
        string equipmentCode = null
    )
    {
        var tree = inlet switch
        {
            SmblInletEnum.Hospital => _smblServicve.GetSmblHospitalBaseTree(id),
            SmblInletEnum.RecordLab => _smblServicve.GetSmblLabBaseTree(id),
            SmblInletEnum.Lab => _smblServicve.GetLabBaseTree(id),
            _ => throw new ArgumentOutOfRangeException(nameof(inlet), inlet, null)
        };
        var result = _equipmentTreeContext.SmblInstrumentsClassTree(tree,inlet,equipmentCode,onlySmbl,showHide);
        tree.rootNode.ConutChildrensNoAppendRootPath();
        return Ok(result.ToResultDto());
    }
    
    

    /// <summary>
    /// 生安入口-试剂信息页面左侧树
    /// </summary>
    /// <param name="inlet"> 入口枚举  0机构  1科室 2备案实验室 </param>
    /// <param name="id"> 对应的id  入口是机构就是机构id   入口是科室就是科室id  入口是备案实验室就是备案实验室id</param>
    /// <param name="smblLabId"> 备案实验室id</param>
    /// <param name="onlySmbl">是否只显示生安设备标识设备</param>
    /// <param name="showHide">是否显示隐藏设备</param>
    /// <param name="hasClass">是否显示设备类型层级</param>
    /// <param name="equipmentCode">设备代号</param>
    /// <returns></returns>
    [HttpGet("{id}")]
    [CustomResponseType(typeof(List<AllTreeNode>))]
    public IActionResult InstrumentEquipmentTree([Required]SmblInletEnum inlet , [Required]string id , string? smblLabId,bool onlySmbl = false ,  bool showHide = false,bool hasClass = false , string equipmentCode = null)
    {
        var tree = inlet switch
        {
            SmblInletEnum.Hospital => _smblServicve.GetSmblHospitalBaseTree(id),
            SmblInletEnum.RecordLab => _smblServicve.GetSmblLabBaseTree(id),
            SmblInletEnum.Lab => _smblServicve.GetLabBaseTree(id),
        };
        var result = _equipmentTreeContext.SmblInstrumentEquipmentsTree(tree,equipmentCode,onlySmbl,showHide,hasClass);
        if (smblLabId.IsNotNullOrEmpty())
        {
            switch (inlet)
            {
                case SmblInletEnum.Hospital :
                     tree.rootNode.CHILDREN
                        .ForEach(hospitalNode =>
                        {
                            hospitalNode.CHILDREN.RemoveAll(labNode =>
                            {
                                bool isDelete = labNode.CHILDREN.All(x => x.SOURCE_ID != smblLabId);
                                labNode.CHILDREN.RemoveAll(pgNode => pgNode.SOURCE_ID != smblLabId);
                                return isDelete;
                            });
                            
                        });
                    break;
                case SmblInletEnum.Lab :
                    tree.rootNode.CHILDREN.ForEach(labNode=>
                    {
                        labNode.CHILDREN.RemoveAll(pgNode => pgNode.SOURCE_ID != smblLabId);
                    });
                    break;
            }
        }
        tree.rootNode.ConutChildrensNoAppendRootPath();
        return Ok(result.ToResultDto());
    }
    
    
    /// <summary>
    /// 生安入口-工作计划页面左侧树
    /// </summary>
    /// <param name="inlet"> 入口枚举  0机构  1科室 2备案实验室 </param>
    /// <param name="id"> 对应的id  入口是机构就是机构id   入口是科室就是科室id  入口是备案实验室就是备案实验室id</param>
    /// <param name="smblLabId"> 备案实验室id</param>
    /// <returns></returns>
    [HttpGet("{id}")]
    [CustomResponseType(typeof(List<AllTreeNode>))]
    public IActionResult WorkPlanTree([Required]SmblInletEnum inlet , [Required]string id ,string? smblLabId)
    {
        var tree = inlet switch
        {
            SmblInletEnum.Hospital => _smblServicve.GetSmblHospitalBaseTree(id),
            SmblInletEnum.RecordLab => _smblServicve.GetSmblLabBaseTree(id),
            SmblInletEnum.Lab => _smblServicve.GetLabBaseTree(id),
        };
        var result = _equipmentTreeContext.SmblWorkPlanTree(tree);
        if (smblLabId.IsNotNullOrEmpty())
        {
            switch (inlet)
            {
                case SmblInletEnum.Hospital :
                    tree.rootNode.CHILDREN
                        .ForEach(hospitalNode =>
                        {
                            hospitalNode.CHILDREN.RemoveAll(labNode =>
                            {
                                bool isDelete = labNode.CHILDREN.All(x => x.SOURCE_ID != smblLabId);
                                labNode.CHILDREN.RemoveAll(pgNode => pgNode.SOURCE_ID != smblLabId);
                                return isDelete;
                            });
                            
                        });
                    break;
                case SmblInletEnum.Lab :
                    tree.rootNode.CHILDREN.ForEach(labNode=>
                    {
                        labNode.CHILDREN.RemoveAll(pgNode => pgNode.SOURCE_ID != smblLabId);
                    });
                    break;
            }
        }
        tree.rootNode.ConutChildrensNoAppendRootPath();
        return Ok(result.ToResultDto());
    }
    
    /// <summary>
    /// 生安入口-工作计划页面生安设备类型左侧树
    /// </summary>
    /// <param name="inlet"> 入口枚举  0机构  1科室 2备案实验室 </param>
    /// <param name="id"> 对应的id  入口是机构就是机构id   入口是科室就是科室id  入口是备案实验室就是备案实验室id</param>
    /// <param name="smblLabId"> 备案实验室id</param>
    /// <returns></returns>
    [HttpGet("{id}")]
    [CustomResponseType(typeof(List<AllTreeNode>))]
    public IActionResult WorkPlanClassTree([Required]SmblInletEnum inlet , [Required]string id )
    {
        var tree = inlet switch
        {
            SmblInletEnum.Hospital => _smblServicve.GetSmblHospitalBaseTree(id),
            SmblInletEnum.RecordLab => _smblServicve.GetSmblLabBaseTree(id),
            SmblInletEnum.Lab => _smblServicve.GetLabBaseTree(id),
        };
        var result = _equipmentTreeContext.SmblWorkPlanClassTree(tree,inlet);
        tree.rootNode.ConutChildrensNoAppendRootPath();
        return Ok(result.ToResultDto());
    }
    
    
    /// <summary>
    /// 生安入口-报废停用页面左侧树
    /// </summary>
    /// <param name="inlet"> 入口枚举  0机构  1科室 2备案实验室 </param>
    /// <param name="id"> 对应的id  入口是机构就是机构id   入口是科室就是科室id  入口是备案实验室就是备案实验室id</param>
    /// <param name="smblLabId"> 备案实验室id</param>
    /// <returns></returns>
    [HttpGet("{id}")]
    [CustomResponseType(typeof(List<AllTreeNode>))]
    public IActionResult ScrapsOrStopTree([Required]SmblInletEnum inlet , [Required]string id ,string? smblLabId)
    {
        var tree = inlet switch
        {
            SmblInletEnum.Hospital => _smblServicve.GetSmblHospitalBaseTree(id),
            SmblInletEnum.RecordLab => _smblServicve.GetSmblLabBaseTree(id),
            SmblInletEnum.Lab => _smblServicve.GetLabBaseTree(id),
        };
        var result = _equipmentTreeContext.SmblScrapsOrStopTree(tree);
        if (smblLabId.IsNotNullOrEmpty())
        {
            switch (inlet)
            {
                case SmblInletEnum.Hospital :
                    tree.rootNode.CHILDREN
                        .ForEach(hospitalNode =>
                        {
                            hospitalNode.CHILDREN.RemoveAll(labNode =>
                            {
                                bool isDelete = labNode.CHILDREN.All(x => x.SOURCE_ID != smblLabId);
                                labNode.CHILDREN.RemoveAll(pgNode => pgNode.SOURCE_ID != smblLabId);
                                return isDelete;
                            });
                            
                        });
                    break;
                case SmblInletEnum.Lab :
                    tree.rootNode.CHILDREN.ForEach(labNode=>
                    {
                        labNode.CHILDREN.RemoveAll(pgNode => pgNode.SOURCE_ID != smblLabId);
                    });
                    break;
            }
        }
        tree.rootNode.ConutChildrensNoAppendRootPath();
        return Ok(result.ToResultDto());
    }
    
    
    /// <summary>
    /// 生安入口-报废停用生安设备类型页面左侧树
    /// </summary>
    /// <param name="inlet"> 入口枚举  0机构  1科室 2备案实验室 </param>
    /// <param name="id"> 对应的id  入口是机构就是机构id   入口是科室就是科室id  入口是备案实验室就是备案实验室id</param>
    /// <param name="smblLabId"> 备案实验室id</param>
    /// <returns></returns>
    [HttpGet("{id}")]
    [CustomResponseType(typeof(List<AllTreeNode>))]
    public IActionResult ScrapsOrStopClassTree([Required]SmblInletEnum inlet , [Required]string id ,string? smblLabId)
    {
        var tree = inlet switch
        {
            SmblInletEnum.Hospital => _smblServicve.GetSmblHospitalBaseTree(id),
            SmblInletEnum.RecordLab => _smblServicve.GetSmblLabBaseTree(id),
            SmblInletEnum.Lab => _smblServicve.GetLabBaseTree(id),
        };
        var result = _equipmentTreeContext.SmblScrapsOrStopClassTree(tree,inlet);
        if (smblLabId.IsNotNullOrEmpty())
        {
            switch (inlet)
            {
                case SmblInletEnum.Hospital :
                    tree.rootNode.CHILDREN
                        .ForEach(hospitalNode =>
                        {
                            hospitalNode.CHILDREN.RemoveAll(labNode =>
                            {
                                bool isDelete = labNode.CHILDREN.All(x => x.SOURCE_ID != smblLabId);
                                labNode.CHILDREN.RemoveAll(pgNode => pgNode.SOURCE_ID != smblLabId);
                                return isDelete;
                            });
                            
                        });
                    break;
                case SmblInletEnum.Lab :
                    tree.rootNode.CHILDREN.ForEach(labNode=>
                    {
                        labNode.CHILDREN.RemoveAll(pgNode => pgNode.SOURCE_ID != smblLabId);
                    });
                    break;
            }
        }
        tree.rootNode.ConutChildrensNoAppendRootPath();
        return Ok(result.ToResultDto());
    }

    
    
    /// <summary>
    /// 生安-备案实验室选择设备树
    /// </summary>
    /// <param name="inlet"> 入口枚举  0机构  1科室 2备案实验室 </param>
    /// <param name="id"> 对应的id  入口是机构就是机构id   入口是科室就是科室id  入口是备案实验室就是备案实验室id</param>
    /// <returns></returns>
    [HttpGet("{id}")]
    [CustomResponseType(typeof(List<AllTreeNode>))]
    public IActionResult GetDistributionEquipmentTree([Required]SmblInletEnum inlet , [Required]string id )
    {
        var tree = inlet switch
        {
            SmblInletEnum.Hospital => _smblServicve.GetSmblHospitalBaseTree(id),
            SmblInletEnum.RecordLab => _smblServicve.GetSmblLabBaseTree(id),
            SmblInletEnum.Lab => _smblServicve.GetLabBaseTree(id),
        };
        var result = _equipmentTreeContext.DistributionEquipmentTree(tree);
        tree.rootNode.ConutChildrens(tree.rootNode);
        return Ok(result.ToResultDto());
    }

    /// <summary>
    /// 查询已分配备案实验室的设备
    /// </summary>
    /// <param name="smblLabId">备案实验室id</param>
    /// <param name="smblEquipmentClass">生安设备类型id</param>
    /// <param name="smblState">生安设备状态  0 不及格  1 及格</param>
    /// <param name="nameOrCode">设备名称、设备代号（自定义名称）</param>
    /// <returns></returns>
    [HttpGet("{smblLabId}")]
    [CustomResponseType(typeof(List<SmblEquipmentDto>))]
    public IActionResult GetDistributionEquipments([Required] string smblLabId ,string? smblEquipmentClass, string? smblState , string? nameOrCode)
    {
        var result  = _equipmentTreeContext.GetActiveEquipments(_user.HOSPITAL_ID, null, new List<string>())
            .Where(x=>x.SMBL_LAB_ID == smblLabId)
            .Where(x => x.IS_HIDE == "0")
            .Where(x=>x.SMBL_FLAG == "1")
            .WhereIF(smblEquipmentClass.IsNotNullOrEmpty(), x => smblEquipmentClass == x.SMBL_CLASS)
            .WhereIF(smblState.IsNotNullOrEmpty(), x => smblState == x.SMBL_STATE)
            .WhereIF(nameOrCode.IsNotNullOrEmpty(), x => x.EQUIPMENT_NAME.ToLower().Contains(nameOrCode!.ToLower()))
            .WhereIF(nameOrCode.IsNotNullOrEmpty(), x => x.EQUIPMENT_CODE.ToLower().Contains(nameOrCode!.ToLower()))
            .ToList();
        return Ok(result.ToResultDto());
    }
    
    /// <summary>
    /// 查询待分配备案实验室的设备
    /// </summary>
    /// <param name="smblLabId">备案实验室id</param>
    /// <param name="smblEquipmentClass">生安设备类型id</param>
    /// <param name="smblState">生安设备状态  0 不及格  1 及格</param>
    /// <param name="nameOrCode">设备名称、设备代号（自定义名称）</param>
    /// <returns></returns>
    [HttpGet]
    [CustomResponseType(typeof(List<SmblEquipmentDto>))]
    public IActionResult GetPerDistributionEquipments(string? smblLabId , string? smblEquipmentClass, string? smblState , string? nameOrCode)
    {
        var result  = _equipmentTreeContext.GetActiveEquipments(_user.HOSPITAL_ID, null, new List<string>())
            .Where(x => x.IS_HIDE == "0")
            .Where(x=>x.SMBL_LAB_ID ==  null)
            .WhereIF(smblLabId.IsNotNullOrEmpty(), x => smblLabId == x.SMBL_LAB_ID)
            .WhereIF(smblEquipmentClass.IsNotNullOrEmpty(), x => smblEquipmentClass == x.SMBL_CLASS)
            .WhereIF(smblState.IsNotNullOrEmpty(), x => smblState == x.SMBL_STATE)
            .WhereIF(nameOrCode.IsNotNullOrEmpty(), x => x.EQUIPMENT_NAME.ToLower().Contains(nameOrCode!.ToLower()) || x.EQUIPMENT_CODE.ToLower().Contains(nameOrCode!.ToLower()))
            .ToList();
        return Ok(result.ToResultDto());
    }
}