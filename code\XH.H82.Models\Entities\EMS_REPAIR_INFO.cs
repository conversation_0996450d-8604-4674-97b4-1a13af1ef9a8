﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using H.Utility.SqlSugarInfra;
using SqlSugar;

namespace XH.H82.Models.Entities
{
    [DBOwner("XH_OA")]
    public class EMS_REPAIR_INFO :IRecordEvent
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string REPAIR_ID { get; set; }
        public string HOSPITAL_ID { get; set; }
        [Required(ErrorMessage = "设备id不能为空")]
        public string EQUIPMENT_ID { get; set; }
        public string REPAIR_NO { get; set; }
        public DateTime? REPAIR_DATE { get; set; }
        public string REPAIR_PERSON { get; set; }
        public string REPAIR_CONTENT { get; set; }
        public string REPAIR_RESULT { get; set; }
        public string OCCUR_EVENT { get; set; }
        public string REPAIR_STATE { get; set; }
        public string FIRST_RPERSON { get; set; }
        public DateTime? FIRST_RTIME { get; set; }
        public string LAST_MPERSON { get; set; }
        public DateTime? LAST_MTIME { get; set; }
        public string REMARK { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string IF_CORRECT { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string IF_COMPARISON { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string IF_VERIFICATION { get; set; }

        public string GetNo()
        {
            return REPAIR_NO;
        }

        public string GetId()
        {
            return REPAIR_ID;
        }

        public (string Type, string TypeName) GetType()
        {
            return ("5", "维修");
        }
        public string GetEquipmentId()
        {
            return EQUIPMENT_ID;
        }
        public DateTime? GetRecordDate()
        {
            return REPAIR_DATE;
        }
    }
}
