﻿using System.ComponentModel.DataAnnotations;
using AutoMapper;
using H.Utility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using SqlSugar;
using XH.H82.API.Extensions;
using XH.H82.IServices;
using XH.H82.Models.Dtos.Change;
using XH.H82.Models.Dtos.Comparison;
using XH.H82.Models.Dtos.Correct;
using XH.H82.Models.Dtos.Implement;
using XH.H82.Models.Dtos.Maintain;
using XH.H82.Models.Dtos.Repair;
using XH.H82.Models.Dtos.Tim;
using XH.H82.Models.Dtos.Verification;
using XH.H82.Models.Entities;
using XH.H82.Models.Entities.Transaction;
using RepairDto = XH.H82.Models.Dtos.Repair.RepairDto;

namespace XH.H82.API.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize]
    public class OperationRecordController : ControllerBase
    {
        private readonly IOperationRecordService _operationRecordService;
        private readonly IModuleLabGroupService _IModuleLabGroupService;
        private readonly string file_preview_address;
        private readonly string file_upload_address;
        private readonly IConfiguration _configuration;
        private readonly IMapper _mapper;
        public OperationRecordController(IOperationRecordService operationRecordService, IModuleLabGroupService imodulelabgroupservice, IConfiguration configuration, IMapper mapper)
        {
            _operationRecordService = operationRecordService;
            _IModuleLabGroupService = imodulelabgroupservice;
            _configuration = configuration;
            file_preview_address = _configuration["S54"];
            file_upload_address = _configuration["S28"];
            _mapper = mapper;
        }
        /// <summary>
        ///   运行时序图
        /// </summary>
        /// <param name="equipmentId">设备id</param>
        /// <param name="year">年份</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetWorkSequentialList([BindRequired] string equipmentId, [BindRequired] string year)
        {
            var res = _operationRecordService.GetWorkSequentialList(equipmentId, year);
            return Ok(res.ToResultDto());
        }

        #region 设备使用记录相关操作

        /// <summary>
        /// 查询设备使用记录
        /// </summary>
        /// <param name="equipmentId"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        [HttpGet]
        [CustomResponseType(typeof(List<ImplementDto>))]
        public IActionResult GetImplements([BindRequired] string equipmentId, DateTime? startTime, DateTime? endTime, string? context)
        {
            var implements = _operationRecordService.GetImplements(equipmentId, startTime, endTime, context);
            var result = _mapper.Map<List<ImplementDto>>(implements);
            
            return Ok(result.ToResultDto());
        }
        /// <summary>
        /// 修改设备使用记录
        /// </summary>
        /// <param name="ImplementId"></param>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPut("{ImplementId}")]
        [CustomResponseType(typeof(bool))]
        public IActionResult EditImplement([BindRequired] string ImplementId, [FromBody] ImplementInput input)
        {

            var data = new EMS_IMPLEMENT_INFO();

            data.IMPLEMENT_ID = ImplementId;
            data.IMPLEMENT_CONTEXT = input.IMPLEMENT_CONTEXT;
            data.IMPLEMENT_DATA = input.IMPLEMENT_DATA;
            data.IMPLEMENT_CYCLE = input.IMPLEMENT_CYCLE;
            data.IMPLEMENT_PERSON = input.IMPLEMENT_PERSON;
            data.REMARK = input.REMARK;

            _operationRecordService.EditImplement(data);

            return Ok(data.ToResultDto());
        }
        /// <summary>
        /// 添加设备使用记录
        /// </summary>
        /// <param name="equipmentId"></param>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("{equipmentId}")]
        [CustomResponseType(typeof(EMS_IMPLEMENT_INFO))]
        public IActionResult CreateImplement([BindRequired] string equipmentId, [FromBody] ImplementInput input)
        {
            var data = new EMS_IMPLEMENT_INFO();
            data.HOSPITAL_ID = User.ToClaimsDto().HOSPITAL_ID;
            data.EQUIPMENT_ID = equipmentId;
            data.IMPLEMENT_CONTEXT = input.IMPLEMENT_CONTEXT;
            data.IMPLEMENT_DATA = input.IMPLEMENT_DATA;
            data.IMPLEMENT_CYCLE = input.IMPLEMENT_CYCLE;
            data.IMPLEMENT_PERSON = input.IMPLEMENT_PERSON;
            data.REMARK = input.REMARK;
            data.IMPLEMENT_SOURCE = "0";
            var result = _operationRecordService.CreateImplement(data);
            return Ok(result.ToResultDto());
        }

        /// <summary>
        /// 删除设备使用记录
        /// </summary>
        /// <param name="ImplementId"></param>
        /// <returns></returns>
        [HttpDelete("{ImplementId}")]
        [CustomResponseType(typeof(bool))]
        public IActionResult DeleteImplement([BindRequired] string ImplementId)
        {
            _operationRecordService.DeleteImplement(ImplementId);
            return Ok(true.ToResultDto());
        }

        /// <summary>
        /// 获取设备使用记录归档
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <param name="month">月份</param>
        /// <param name="year">年份</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetUsingFile([BindRequired] string equipmentId, [BindRequired] int year, int? month)
        {
            var res = _operationRecordService.GetUsingFile(equipmentId, year, month);
            return Ok(res.ToResultDto());
        }
        
        #endregion
        
        #region 事务项相关查询

        /// <summary>
        /// 获取事务填写记录
        /// </summary>
        /// <param name="equipmentId">设备id</param>
        /// <param name="transactionClass"> 记录单类型 1使用、2保养、3维修、4校准 </param>
        /// <param name="startTime">执行时间-开始时间</param>
        /// <param name="endTime">执行时间-结束时间</param>
        /// <returns></returns>
        [HttpGet("{equipmentId}")]
        [CustomResponseType(typeof(List<ExecRecord>))]
        public IActionResult GetTransactionFillingRecords([Required] string equipmentId, [Required] string transactionClass , DateTime? startTime ,DateTime? endTime)
        {
            var result = _operationRecordService.GetTransactionFillingRecords(equipmentId, transactionClass);
            result = result
                .WhereIF(startTime.HasValue, x => x.WEXEC_DATE >= startTime)
                .WhereIF(endTime.HasValue, x => x.WEXEC_DATE <= endTime.Value.AddDays(1))
                .ToList();

            return Ok(result.ToResultDto());

        }

        /// <summary>
        /// 工作计划-查询设备相关的记录单及事务信息
        /// </summary>
        /// <param name="equipmentId"></param>
        /// <returns></returns>
        [HttpGet("{equipmentId}")]
        [CustomResponseType(typeof(List<TransactionForm>))]
        public IActionResult GetTransactions([Required] string equipmentId)
        {
            var result = _operationRecordService.GetTransactions(equipmentId);
            return Ok(result.ToResultDto());
        }



        #endregion

        #region 设备相关记录附件查询

        /// <summary>
        /// 获取设备tab页手动维护的记录附件列表
        /// </summary>
        /// <param name="docInfoId"> 对应记录的id</param>
        /// <param name="docClass"> 保养记录、校准记录、性能验证记录、维修记录、变更记录、使用记录、比对记录</param>
        /// <returns></returns>
        [HttpGet("{docInfoId}")]
        [CustomResponseType(typeof(List<EMS_DOC_INFO>))]
        public IActionResult GetDocInfos([BindRequired] string docInfoId, [BindRequired] string docClass)
        {
            var result = new List<EMS_DOC_INFO>();
            if (docClass == "保养记录")
            {
                result = _operationRecordService.GetMaintainDocs(docInfoId);
            }
            else if (docClass == "校准记录")
            {
                result = _operationRecordService.GetCorrectDocs(docInfoId);
            }
            else if (docClass == "性能验证记录")
            {
                result = _operationRecordService.GetVerificationDocs(docInfoId);
            }
            else if (docClass == "维修记录")
            {
                result = _operationRecordService.GetRepairDocs(docInfoId);
            }
            else if (docClass == "变更记录")
            {
                result = _operationRecordService.GetChangeDocs(docInfoId);
            }
            else if (docClass == "使用记录")
            {
                result = _operationRecordService.GetImplementDocs(docInfoId);
            }
            else if (docClass == "比对记录")
            {
                result = _operationRecordService.GetComparisonDocs(docInfoId);
            }

            return Ok(result.ToResultDto());
        }

        
        /// <summary>
        ///   获取运行记录附件
        /// </summary>
        /// <param name="module">运行模块（0：保养；1：维修；2：校准；3：比对；4：性能验证；5：变更  6 : 去污）</param>
        /// <param name="operId">id</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetOperFiles([BindRequired] string module, [BindRequired] string operId)
        {
            var claims = User.ToClaimsDto();
            var res = _operationRecordService.GetOperFiles(module, operId);
            return Ok(res.ToResultDto());
        }

        
        #endregion
 
        #region 设备保养记录相关操

         /// <summary>
        ///  获取设备保养信息列表
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetMaintainList([BindRequired] string equipmentId)
        {
            var res = _operationRecordService.GetMaintainList(equipmentId);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        /// 获取设备保养信息列表(6.24.8 版本后的接口)
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <param name="maintainCycle">保养周期</param>
        /// <param name="content">保养内容</param>
        /// <param name="startTime">保养时间开始</param>
        /// <param name="endTime">保养时间结束</param>
        /// <returns></returns>
        [HttpGet("{equipmentId}")]
        public IActionResult GetMaintains([Required] string equipmentId , string? maintainCycle , string? content ,DateTime? startTime, DateTime? endTime)
        {
            var result = _operationRecordService.GetMaintains(equipmentId,maintainCycle,startTime,endTime,content);

            foreach (var item in result)
            {
                if (item.MAINTAIN_CYCLE == "半年")
                {
                    item.MAINTAIN_CYCLE = "年";
                }
            }
            return Ok(result.ToResultDto());
        }

        

        /// <summary>
        ///   添加设备保养信息 
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult AddMaintainInfo([FromBody] EMS_MAINTAIN_INFO record)
        {
            var claims = User.ToClaimsDto();
            record.MAINTAIN_ID = IDGenHelper.CreateGuid().ToString();
            record.HOSPITAL_ID = claims.HOSPITAL_ID;
            record.FIRST_RPERSON = claims.HIS_NAME;
            record.FIRST_RTIME = DateTime.Now;
            record.LAST_MPERSON = claims.HIS_NAME;
            record.LAST_MTIME = DateTime.Now;
            if (record.MAINTAIN_PERSON.IsNotNullOrEmpty() && record.MAINTAIN_PERSON.Contains("_"))
            {
                record.MAINTAIN_PERSON = record.MAINTAIN_PERSON.Split('_')[1];
            }
            record.MAINTAIN_STATE = "1";
            var res = _operationRecordService.SaveMaintainInfo(record);
            return Ok(res.ToResultDto());
        }
        
        /// <summary>
        /// 添加设备保养信息(6.24.8 版本后的接口)
        /// </summary>
        /// <param name="equipmentId">设备id</param>
        /// <param name="input">表单模型</param>
        /// <returns></returns>
        [HttpPost("{equipmentId}")]
        public IActionResult AddMaintain([Required]string equipmentId , [FromBody] MaintainInput input)
        {
            var user = User.ToClaimsDto();
            var record =  MaintainDto.CreateAddModule(equipmentId, user.HOSPITAL_ID, input);
            var result = _operationRecordService.SaveMaintainInfo(record);
            return Ok(result.ToResultDto());
        }

        /// <summary>
        ///   修改设备保养信息 
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UpdateMaintainInfo([FromBody] EMS_MAINTAIN_INFO record)
        {
            var claims = User.ToClaimsDto();
            record.LAST_MPERSON = claims.HIS_NAME;
            if (record.MAINTAIN_PERSON.IsNotNullOrEmpty() && record.MAINTAIN_PERSON.Contains("_"))
            {
                record.MAINTAIN_PERSON = record.MAINTAIN_PERSON.Split('_')[1];
            }
            record.LAST_MTIME = DateTime.Now;
            var res = _operationRecordService.SaveMaintainInfo(record);
            return Ok(res.ToResultDto());
        }
        
        /// <summary>
        ///  修改设备保养信息 (6.24.8 版本后的接口)
        /// </summary>
        /// <param name="id">保养记录id  对应字段 MAINTAIN_ID</param>
        /// <param name="input"> 表单模型 </param>
        /// <returns></returns>
        [HttpPut("{id}")]
        public IActionResult UpdateMaintainInfo([Required]string id, [FromBody] MaintainInput input)
        {
            var record = _operationRecordService.GetRecordById<EMS_MAINTAIN_INFO>(id);
            if (record is null)
            {
                var resultFalse = new ResultDto();
                resultFalse.success = false;
                resultFalse.msg = $"不存在相关记录:{id}";
                return Ok(resultFalse);
            }
            record.MAINTAIN_CYCLE = input.MAINTAIN_CYCLE;
            record.MAINTAIN_DATE = input.MAINTAIN_DATE;
            record.MAINTAIN_PERSON = input.MAINTAIN_PERSON;
            if (record.MAINTAIN_PERSON.IsNotNullOrEmpty() && record.MAINTAIN_PERSON!.Contains("_"))
            {
                record.MAINTAIN_PERSON = record.MAINTAIN_PERSON.Split('_')[1];
            }
            record.MAINTAIN_CONTENT = input.MAINTAIN_CONTENT;
            record.REMARK = input.REMARK;
            record.OCCUR_EVENT = input.OCCUR_EVENT;
            var res = _operationRecordService.SaveMaintainInfo(record);
            return Ok(res.ToResultDto());
        }
        


        /// <summary>
        ///   删除设备保养信息
        /// </summary>
        /// <param name="maintainId">设备保养ID</param>
        /// <returns></returns>
        [HttpDelete]
        public IActionResult DeleteMaintainInfo([BindRequired] string maintainId)
        {
            var claims = User.ToClaimsDto();
            var userName = claims.HIS_NAME;
            var res = _operationRecordService.DeleteMaintainInfo(maintainId, userName);
            return Ok(new ResultDto { success = res.success, msg = res.msg });
        }

        
        
        /// <summary>
        ///   删除设备保养信息(6.24.8 版本后的接口)
        /// </summary>
        /// <param name="id">设备保养记录ID</param>
        /// <returns></returns>
        [HttpDelete("{id}")]
        public IActionResult DeleteMaintain([Required] string id)
        {
            var claims = User.ToClaimsDto();
            var userName = claims.HIS_NAME;
            var res = _operationRecordService.DeleteMaintainInfo(id, userName);
            return Ok(new ResultDto { success = res.success, msg = res.msg });
        }
        
        
        /// <summary>
        /// 获取设备保养记录归档
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <param name="month">月份</param>
        /// <param name="year">年</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetMaintainFile([BindRequired] string equipmentId, [BindRequired] int year, [BindRequired] int month)
        {
            var res = _operationRecordService.GetMaintainFile(equipmentId, year, month);
            return Ok(res.ToResultDto());
        }
        
        /// <summary>
        /// 获取去污归档记录文件
        /// </summary>
        /// <param name="equipmentId"></param>
        /// <param name="year"></param>
        /// <param name="month"></param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetDecontaminationFile([BindRequired] string equipmentId, [BindRequired] int year)
        {
            var res = _operationRecordService.GetDecontaminationFile(equipmentId, year);
            return Ok(res.ToResultDto());
        }
        
        
        #endregion

        #region 设备维修记录相关操作

        
        /// <summary>
        /// 获取设备维修信息列表 
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetRepairList([BindRequired] string equipmentId)
        {
            var res = _operationRecordService.GetRepairList(equipmentId);
            return Ok(res.ToResultDto());
        }


        /// <summary>
        /// 获取设备维修信息列表 (6.24.8 版本后的接口)
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <param name="content">维修内容 模糊搜索</param>
        /// <param name="startTime">维修时间开始</param>
        /// <param name="endTime">维修时间结束</param>
        /// <returns></returns>
        [HttpGet("{equipmentId}")]
        public IActionResult GetRepairs([Required] string equipmentId , string? content ,DateTime? startTime, DateTime? endTime)
        {
            var res = _operationRecordService.GetRepairs(equipmentId,content,startTime,endTime);
            return Ok(res.ToResultDto());
        }
        
        /// <summary>
        ///   添加设备维修信息 
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult AddRepairInfo([FromBody] EMS_REPAIR_INFO record)
        {
            var claims = User.ToClaimsDto();
            record.REPAIR_ID = IDGenHelper.CreateGuid().ToString();
            record.HOSPITAL_ID = claims.HOSPITAL_ID;
            record.FIRST_RPERSON = claims.HIS_NAME;
            record.FIRST_RTIME = DateTime.Now;
            record.LAST_MPERSON = claims.HIS_NAME;
            record.LAST_MTIME = DateTime.Now;
            record.REPAIR_STATE = "1";
            var res = _operationRecordService.SaveRepairInfo(record);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///  添加设备维修信息 (6.24.8 版本后的接口)
        /// </summary>
        /// <param name="equipmentId">设备id</param>
        /// <param name="input">表单模型</param>
        /// <returns></returns>
        [HttpPost("{equipmentId}")]
        public IActionResult AddRepairInfo([Required] string equipmentId ,[FromBody] RepairInput input)
        {
            var user = User.ToClaimsDto();
            var record = RepairDto.CreateModule(equipmentId, user.HOSPITAL_ID, input);
            var res = _operationRecordService.SaveRepairInfo(record);
            return Ok(res.ToResultDto());
        }
        
        
        /// <summary>
        ///   修改设备维修信息
        /// </summary> 
        /// <returns></returns>
        [HttpPost]
        public IActionResult UpdateRepairInfo([FromBody] EMS_REPAIR_INFO record)
        {
            var claims = User.ToClaimsDto();
            record.LAST_MPERSON = claims.HIS_NAME;
            if (record.REPAIR_PERSON.IsNotNullOrEmpty() && record.REPAIR_PERSON.Contains("_"))
            {
                record.REPAIR_PERSON = record.REPAIR_PERSON.Split('_')[1];
            }
            record.LAST_MTIME = DateTime.Now;
            var res = _operationRecordService.SaveRepairInfo(record);
            return Ok(res.ToResultDto());
        }
        
        
        /// <summary>
        /// 修改设备维修信息(6.24.8 版本后的接口)
        /// </summary>
        /// <param name="id">设备维修记录id 对应 REPAIR_ID</param>
        /// <param name="input">表单模型</param>
        /// <returns></returns>
        [HttpPut("{id}")]
        public IActionResult UpdateRepair([Required]string id ,  [FromBody] RepairInput input)
        {
            var record = _operationRecordService.GetRecordById<EMS_REPAIR_INFO>(id);
            if (record is null)
            {
                var resultFalse = new ResultDto();
                resultFalse.success = false;
                resultFalse.msg = $"不存在相关记录:{id}";
                return Ok(resultFalse);
            }
            record.REPAIR_DATE = input.REPAIR_DATE;
            record.REPAIR_PERSON = input.REPAIR_PERSON;
            record.REPAIR_RESULT = input.REPAIR_RESULT;
            if (record.REPAIR_PERSON.IsNotNullOrEmpty() && record.REPAIR_PERSON!.Contains("_"))
            {
                record.REPAIR_PERSON = record.REPAIR_PERSON.Split('_')[1];
            }
            record.REPAIR_CONTENT = input.REPAIR_CONTENT;
            record.REMARK = input.REMARK;
            record.OCCUR_EVENT = input.OCCUR_EVENT;
            var result = _operationRecordService.SaveRepairInfo(record);
            return Ok(result.ToResultDto());
        }
        

        /// <summary>
        ///   删除设备维修信息
        /// </summary>
        /// <param name="repairId">设备维修ID</param>
        /// <returns></returns>
        [HttpDelete]
        public IActionResult DeleteRepairInfo([BindRequired] string repairId)
        {
            var claims = User.ToClaimsDto();
            var userName = claims.HIS_NAME;
            var res = _operationRecordService.DeleteRepairInfo(repairId, userName);
            return Ok(new ResultDto { success = res.success, msg = res.msg });
        }
        
        /// <summary>
        ///   删除设备维修信息(6.24.8 版本后的接口)
        /// </summary>
        /// <param name="id">设备维修记录ID 对应 REPAIR_ID </param>
        /// <returns></returns>
        [HttpDelete("{id}")]
        public IActionResult DeleteRepair([Required] string id)
        {
            var claims = User.ToClaimsDto();
            var userName = claims.HIS_NAME;
            var res = _operationRecordService.DeleteRepairInfo(id, userName);
            return Ok(new ResultDto { success = res.success, msg = res.msg });
        }
        
        
        /// <summary>
        /// 获取设备维修记录归档
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <param name="year">年份</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetRepairFile([BindRequired] string equipmentId, [BindRequired] int year)
        {
            var res = _operationRecordService.GetRepairFile(equipmentId, year);
            return Ok(res.ToResultDto());
        }

        #endregion

        #region 设备校准相关操作

        /// <summary>
        ///   获取设备校准信息列表
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetCorrectList([BindRequired] string equipmentId)
        {
            var res = _operationRecordService.GetCorrectList(equipmentId);
            return Ok(res.ToResultDto());
        }
        
        /// <summary>
        /// 查询设备校准记录(6.24.8 版本后的接口)
        /// </summary>
        /// <param name="equipmentId"></param>
        /// <param name="startTime">设备校准日期开始</param>
        /// <param name="endTime">设备校准日期结束</param>
        /// <returns></returns>
        [HttpGet("{equipmentId}")]
        public IActionResult GetCorrects([Required] string equipmentId,DateTime? startTime, DateTime? endTime)
        {
            var res = _operationRecordService.GetCorrects(equipmentId,startTime,endTime);
            return Ok(res.ToResultDto());
        }
        

        /// <summary>
        /// 添加设备校准信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult AddCorrectInfo([FromBody] EMS_CORRECT_INFO record)
        {
            var claims = User.ToClaimsDto();
            record.CORRECT_ID = IDGenHelper.CreateGuid().ToString();
            if (record.CORRECT_PERSON.IsNotNullOrEmpty() && record.CORRECT_PERSON.Contains("_"))
            {
                record.CORRECT_PERSON = record.CORRECT_PERSON.Split('_')[1];
            }
            record.HOSPITAL_ID = claims.HOSPITAL_ID;
            record.STATE = "已执行";
            record.FIRST_RPERSON = claims.HIS_NAME;
            record.FIRST_RTIME = DateTime.Now;
            record.LAST_MPERSON = claims.HIS_NAME;
            record.LAST_MTIME = DateTime.Now;
            record.CORRECT_STATE = "1";
            var res = _operationRecordService.SaveCorrectInfo(record);
            return Ok(res.ToResultDto());
        }
        
        /// <summary>
        /// 添加设备校准记录 (6.24.8 版本后的接口)
        /// </summary>
        /// <param name="equipmentId"></param>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("{equipmentId}")]
        public IActionResult AddCorrect([Required] string equipmentId,[FromBody] CorrectInput input)
        {
            var user = User.ToClaimsDto();
            var record = CorrectDto.CreateAddModule(equipmentId, user.HOSPITAL_ID, input);
            var result = _operationRecordService.SaveCorrectInfo(record);
            return Ok(result.ToResultDto());
        }
        

        /// <summary>
        /// 删除设备校准信息 
        /// </summary>
        /// <param name="correctId"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult DeleteCorrectInfo(string correctId)
        {
            var claims = User.ToClaimsDto();
            var userName = claims.HIS_NAME;
            var res = _operationRecordService.DeleteCorrectInfo(correctId, userName);
            return Ok(res);
        }

        /// <summary>
        /// 删除设备校准信息 (6.24.8 版本后的接口)
        /// </summary>
        /// <param name="id">  校准记录id 对应 CORRECT_ID</param>
        /// <returns></returns>
        [HttpDelete("{id}")]
        public IActionResult DeleteCorrect([Required]string id)
        {
            var claims = User.ToClaimsDto();
            var userName = claims.HIS_NAME;
            var res = _operationRecordService.DeleteCorrectInfo(id, userName);
            return Ok(res);
        }

        
        
        /// <summary>
        /// 修改设备校准信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UpdateCorrectInfo([FromBody] EMS_CORRECT_INFO record)
        {
            var claims = User.ToClaimsDto();
            record.LAST_MPERSON = claims.HIS_NAME;
            if (record.CORRECT_PERSON.IsNotNullOrEmpty() && record.CORRECT_PERSON.Contains("_"))
            {
                record.CORRECT_PERSON = record.CORRECT_PERSON.Split('_')[1];
            }
            record.LAST_MTIME = DateTime.Now;
            var res = _operationRecordService.SaveCorrectInfo(record);

            return Ok(res.ToResultDto());
        }

        
        /// <summary>
        /// 修改设备校准信息(6.24.8 版本后的接口)
        /// </summary>
        /// <param name="id">校准记录id 对应 CORRECT_ID</param>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPut("{id}")]
        public IActionResult UpdateCorrect([Required]string id,[FromBody] CorrectInput input)
        {
            var record = _operationRecordService.GetRecordById<EMS_CORRECT_INFO>(id);
            if (record is null)
            {
                var resultFalse = new ResultDto();
                resultFalse.success = false;
                resultFalse.msg = $"不存在相关记录:{id}";
                return Ok(resultFalse);
            }
            record.CORRECT_DATE = input.CORRECT_DATE;
            record.CORRECT_PERSON = input.CORRECT_PERSON;
            record.CORRECT_DEPT = input.CORRECT_DEPT;
            if (record.CORRECT_PERSON.IsNotNullOrEmpty() && record.CORRECT_PERSON!.Contains("_"))
            {
                record.CORRECT_PERSON = record.CORRECT_PERSON.Split('_')[1];
            }
            record.CORRECT_RESULT = input.CORRECT_RESULT;
            record.REMARK = input.REMARK;
            record.OCCUR_EVENT = input.OCCUR_EVENT;
            var res = _operationRecordService.SaveCorrectInfo(record);
            _operationRecordService.GetCorrectRecordValidityPeriod(record.EQUIPMENT_ID,
                new List<EMS_CORRECT_INFO> { record });
            
            return Ok(res.ToResultDto());
        }
        /// <summary>
        /// 获取设备校准记录归档
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <param name="year">年份</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetCorrectFile([BindRequired] string equipmentId, int year)
        {
            var res = _operationRecordService.GetCorrectFile(equipmentId, year);
            return Ok(res.ToResultDto());
        }
        #endregion

        #region 设备比对相关操作

        /// <summary>
        /// 获取设备比对信息列表
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetComparisonList([BindRequired] string equipmentId)
        {
            var res = _operationRecordService.GetComparisonList(equipmentId);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        /// 获取设备比对信息列表(6.24.8 版本后的接口)
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <returns></returns>
        [HttpGet("{equipmentId}")]
        public IActionResult GetComparisons([Required] string equipmentId)
        {
            var result = _operationRecordService.GetComparisons(equipmentId);
            return Ok(result.ToResultDto());
        }
          
        /// <summary>
        /// 添加设备比对信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult AddComparisonInfo([FromBody] EMS_COMPARISON_INFO record)
        {
            var claims = User.ToClaimsDto();
            record.COMPARISON_ID = IDGenHelper.CreateGuid().ToString();
            if (record.COMPARISON_PERSON.IsNotNullOrEmpty() && record.COMPARISON_PERSON.Contains("_"))
            {
                record.COMPARISON_PERSON = record.COMPARISON_PERSON.Split('_')[1];
            }
            record.HOSPITAL_ID = claims.HOSPITAL_ID;
            record.STATE = "已执行";
            record.FIRST_RPERSON = claims.HIS_NAME;
            record.FIRST_RTIME = DateTime.Now;
            record.LAST_MPERSON = claims.HIS_NAME;
            record.LAST_MTIME = DateTime.Now;
            record.COMPARISON_STATE = "1";
            var res = _operationRecordService.SaveComparisonInfo(record);
            return Ok(res.ToResultDto());
        }
        
        /// <summary>
        /// 添加设备比对信息(6.24.8 版本后的接口)
        /// </summary>
        /// <param name="equipmentId">设备id </param>
        /// <param name="input">比对表达模型</param>
        /// <returns></returns>
        [HttpPost("{equipmentId}")]
        public IActionResult AddComparison([Required] string equipmentId , [FromBody] ComparisonInput input)
        {
            var user = User.ToClaimsDto();
            var record = ComparisonDto.CreateAddModule(equipmentId, user.HOSPITAL_ID, input);
            var res = _operationRecordService.SaveComparisonInfo(record);
            return Ok(res.ToResultDto());
        }
        

        /// <summary>
        /// 修改设备比对信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UpdateComparisonInfo([FromBody] EMS_COMPARISON_INFO record)
        {
            var claims = User.ToClaimsDto();
            record.STATE = "已执行";
            record.LAST_MPERSON = claims.HIS_NAME;
            if (record.COMPARISON_PERSON.IsNotNullOrEmpty() && record.COMPARISON_PERSON.Contains("_"))
            {
                record.COMPARISON_PERSON = record.COMPARISON_PERSON.Split('_')[1];
            }
            record.LAST_MTIME = DateTime.Now;
            var res = _operationRecordService.SaveComparisonInfo(record);
            return Ok(res.ToResultDto());
        }

        
        /// <summary>
        /// 修改设备比对信息(6.24.8 版本后的接口)
        /// </summary>
        /// <param name="id">比对记录ID  对应 COMPARISON_ID</param>
        /// <param name="input">对比表单入参模型</param>
        /// <returns></returns>
        [HttpPut("{id}")]
        public IActionResult UpdateComparison( [Required] string id , [FromBody] ComparisonInput input)
        {
            var record = _operationRecordService.GetRecordById<EMS_COMPARISON_INFO>(id);
            if (record is null)
            {
                var resultFalse = new ResultDto();
                resultFalse.success = false;
                resultFalse.msg = $"不存在相关记录:{id}";
                return Ok(resultFalse);
            }
            record.COMPARISON_DATE = input.COMPARISON_DATE;
            record.COMPARISON_PERSON = input.COMPARISON_PERSON;
            record.COMPARISON_RESULT = input.COMPARISON_RESULT;
            record.COMPARISON_OBJECT = input.COMPARISON_OBJECT;
            if (record.COMPARISON_PERSON.IsNotNullOrEmpty() && record.COMPARISON_PERSON!.Contains("_"))
            {
                record.COMPARISON_PERSON = record.COMPARISON_PERSON.Split('_')[1];
            }
            record.REMARK = input.REMARK;
            var res = _operationRecordService.SaveComparisonInfo(record);
            return Ok(res.ToResultDto());
        }

        
        /// <summary>
        /// 删除设备比对信息
        /// </summary>
        /// <param name="comparisonId">比对ID</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult DeleteComparisonInfo([BindRequired] string comparisonId)
        {
            var claims = User.ToClaimsDto();
            var userName = claims.HIS_NAME;
            var res = _operationRecordService.DeleteComparisonInfo(comparisonId, userName);
            return Ok(res);
        }
        
        /// <summary>
        /// 删除设备比对信息(6.24.8 版本后的接口)
        /// </summary>
        /// <param name="id">比对记录ID  对应 COMPARISON_ID</param>
        /// <returns></returns>
        [HttpDelete("{id}")]
        public IActionResult DeleteComparison([Required] string id)
        {
            var claims = User.ToClaimsDto();
            var userName = claims.HIS_NAME;
            var res = _operationRecordService.DeleteComparisonInfo(id, userName);
            return Ok(res);
        }
        
        #endregion

        #region 设备性能验证相关操作

        
        /// <summary>
        ///   获取设备性能验证信息列表
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetVerificationList([BindRequired] string equipmentId)
        {
            var res = _operationRecordService.GetVerificationList(equipmentId);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        /// 查询设备性能验证记录 (6.24.8 版本后的接口)
        /// </summary>
        /// <param name="equipmentId"></param>
        /// <returns></returns>
        [HttpGet("{equipmentId}")]
        public IActionResult GetVerifications([Required] string equipmentId)
        {
            var res = _operationRecordService.GetVerificationList(equipmentId);
            return Ok(res.ToResultDto());
        }
        
        
        /// <summary>
        ///   添加设备性能验证
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult AddVerificationInfo([FromBody] EMS_VERIFICATION_INFO record)
        {
            var claims = User.ToClaimsDto();
            record.VERIFICATION_ID = IDGenHelper.CreateGuid().ToString();
            if (record.VERIFICATION_PERSON.IsNotNullOrEmpty() && record.VERIFICATION_PERSON.Contains("_"))
            {
                record.VERIFICATION_PERSON = record.VERIFICATION_PERSON.Split('_')[1];
            }
            record.STATE = "已执行";
            record.HOSPITAL_ID = claims.HOSPITAL_ID;
            record.FIRST_RPERSON = claims.HIS_NAME;
            record.FIRST_RTIME = DateTime.Now;
            record.LAST_MPERSON = claims.HIS_NAME;
            record.LAST_MTIME = DateTime.Now;
            record.VERIFICATION_STATE = "1";
            var res = _operationRecordService.SaveVerificationInfo(record);
            return Ok(res.ToResultDto());
        }
        
        /// <summary>
        /// 添加设备性能验证(6.24.8 版本后的接口)
        /// </summary>
        /// <returns></returns>
        [HttpPost("{equipmentId}")]
        public IActionResult AddVerification([Required] string equipmentId, [FromBody] VerificationInput input)
        {
            var user = User.ToClaimsDto();
            var record = VerificationDto.CreateAddModule(equipmentId,user.HOSPITAL_ID,input);
            var res = _operationRecordService.SaveVerificationInfo(record);
            return Ok(res.ToResultDto());
        }


        
        /// <summary>
        ///   修改设备性能验证
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UpdateVerificationInfo([FromBody] EMS_VERIFICATION_INFO record)
        {
            var claims = User.ToClaimsDto();
            record.LAST_MPERSON = claims.HIS_NAME;
            if (record.VERIFICATION_PERSON.IsNotNullOrEmpty() && record.VERIFICATION_PERSON.Contains("_"))
            {
                record.VERIFICATION_PERSON = record.VERIFICATION_PERSON.Split('_')[1];
            }
            record.LAST_MTIME = DateTime.Now;
            var res = _operationRecordService.SaveVerificationInfo(record);
            return Ok(res.ToResultDto());
        }
        
        /// <summary>
        /// 修改设备性能验证(6.24.8 版本后的接口)
        /// </summary>
        /// <returns></returns>
        [HttpPut("{id}")]
        public IActionResult UpdateVerification([Required]string id ,[FromBody] VerificationInput input)
        {
            var record = _operationRecordService.GetRecordById<EMS_VERIFICATION_INFO>(id);
            record.VERIFICATION_DATE = input.VERIFICATION_DATE;
            record.VERIFICATION_PERSON = input.VERIFICATION_PERSON;
            record.VERIFICATION_RESULT = input.VERIFICATION_RESULT;
            record.REMARK = record.REMARK;
            var result = _operationRecordService.SaveVerificationInfo(record);
            return Ok(result.ToResultDto());
        }

        /// <summary>
        /// 删除设备性能验证信息
        /// </summary>
        /// <param name="verificationInfoId">性能验证ID</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult DeleteVerificationInfo([BindRequired] string verificationInfoId)
        {
            var claims = User.ToClaimsDto();
            var userName = claims.HIS_NAME;
            var res = _operationRecordService.DeleteVerificationInfo(verificationInfoId, userName);
            return Ok(res);
        }
        
        /// <summary>
        /// 删除设备性能验证信息(6.24.8 版本后的接口)
        /// </summary>
        /// <param name="id">性能验证ID</param>
        /// <returns></returns>
        [HttpDelete("{id}")]
        public IActionResult DeleteVerification([Required] string id)
        {
            var claims = User.ToClaimsDto();
            var userName = claims.HIS_NAME;
            var result = _operationRecordService.DeleteVerificationInfo(id, userName);
            return Ok(result);
        }
        #endregion
        
        #region 设备变更记录相关操作

        /// <summary>
        ///   获取设备变更信息列表
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetChangeList([BindRequired] string equipmentId)
        {
            var res = _operationRecordService.GetChangeList(equipmentId);
            return Ok(res.ToResultDto());
        }
        
        /// <summary>
        /// 获取设备变更信息列表(6.24.8 版本后的接口)
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <returns></returns>
        [HttpGet("{equipmentId}")]
        public IActionResult GetChanges([Required] string equipmentId)
        {
            var res = _operationRecordService.GetChangeList(equipmentId);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        ///   添加设备变更信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult AddChangeInfo([FromBody] EMS_CHANGE_INFO record)
        {
            var claims = User.ToClaimsDto();
            record.CHANGE_ID = IDGenHelper.CreateGuid();
            if (record.CHANGE_PERSON.IsNotNullOrEmpty() && record.CHANGE_PERSON.Contains("_"))
            {
                record.CHANGE_PERSON = record.CHANGE_PERSON.Split('_')[1];
            }
            record.HOSPITAL_ID = claims.HOSPITAL_ID;
            record.FIRST_RPERSON = claims.HIS_NAME;
            record.FIRST_RTIME = DateTime.Now;
            record.LAST_MPERSON = claims.HIS_NAME;
            record.LAST_MTIME = DateTime.Now;
            record.CHANGE_STATE = "1";
            var res = _operationRecordService.SaveChangeInfo(record);
            return Ok(res.ToResultDto());
        }
        
        /// <summary>
        ///  添加设备变更信息(6.24.8 版本后的接口)
        /// </summary>
        /// <returns></returns>
        [HttpPost("{equipmentId}")]
        public IActionResult AddChangeInfo([Required] string equipmentId,[FromBody] ChangeInput input)
        {
            var user = User.ToClaimsDto();
            var record = ChangeDto.CreateAddModule(equipmentId, user.HOSPITAL_ID, input);
            var result = _operationRecordService.SaveChangeInfo(record);
            return Ok(result.ToResultDto());
        }

        /// <summary>
        /// 修改设备变更信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UpdateChangeInfo([FromBody] EMS_CHANGE_INFO record)
        {
            var claims = User.ToClaimsDto();
            record.LAST_MPERSON = claims.HIS_NAME;
            if (record.CHANGE_PERSON.IsNotNullOrEmpty() && record.CHANGE_PERSON.Contains("_"))
            {
                record.CHANGE_PERSON = record.CHANGE_PERSON.Split('_')[1];
            }
            record.LAST_MTIME = DateTime.Now;
            var res = _operationRecordService.SaveChangeInfo(record);
            return Ok(res.ToResultDto());
        }
        
        /// <summary>
        /// 修改设备变更信息(6.24.8 版本后的接口)
        /// </summary>
        /// <returns></returns>
        [HttpPut("{id}")]
        public IActionResult UpdateChange([Required]string id , [FromBody] ChangeInput input)
        {
            var record = _operationRecordService.GetRecordById<EMS_CHANGE_INFO>(id);
            record.CHANGE_DATE = input.CHANGE_DATE;
            record.CHANGE_PERSON = input.CHANGE_PERSON;
            record.CHANGE_CONTENT = input.CHANGE_CONTENT;
            record.REMARK = input.REMARK;
            var result = _operationRecordService.SaveChangeInfo(record);
            return Ok(result.ToResultDto());
        }
        
        

        /// <summary>
        ///  删除设备变更信息
        /// </summary>
        /// <param name="changeId">设备变更ID</param>
        /// <returns></returns>
        [HttpDelete]
        public IActionResult DeleteChangeInfo([BindRequired] string changeId)
        {
            var claims = User.ToClaimsDto();
            var userName = claims.HIS_NAME;
            var res = _operationRecordService.DeleteChangeInfo(changeId, userName);
            return Ok(res);
        }

        /// <summary>
        /// 删除设备变更信息 (6.24.8 版本后的接口)
        /// </summary>
        /// <param name="id">设备变更ID</param>
        /// <returns></returns>
        [HttpDelete("{id}")]
        public IActionResult DeleteChange([Required] string id)
        {
            var claims = User.ToClaimsDto();
            var userName = claims.HIS_NAME;
            var res = _operationRecordService.DeleteChangeInfo(id, userName);
            return Ok(res);
        }

        #endregion
        
        /// <summary>
        /// 获取关联事件信息
        /// </summary>
        /// <param name="relationNo"></param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetRelationEventInfo(string relationNo)
        {
            var res = _operationRecordService.GetRelationEventInfo(relationNo);
            return Ok(res.ToResultDto());
        }
    }
}
