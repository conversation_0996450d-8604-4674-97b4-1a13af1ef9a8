﻿using System.ComponentModel;

namespace XH.H82.Models.DeviceRelevantInformation.Enum
{
    /// <summary>
    /// 设备报警信息(0故障、1需要保养、2需要校准、需要比对)
    /// </summary>
    public enum EquipmentSummaryEnum
    {
        /// <summary>
        /// 故障
        /// </summary>
        [Description("故障")]
        Fault,
        /// <summary>
        /// 需要保养
        /// </summary>

        [Description("保养")]
        Maintenance,
        /// <summary>
        /// 需要校准
        /// </summary>
        [Description("校准")]
        Correct,
        /// <summary>
        /// 需要比对
        /// </summary>
        [Description("比对")]
        Comparison,

        /// <summary>
        /// 需要更新证书
        /// </summary>
        [Description("证书")]
        Certificate


    }
}
