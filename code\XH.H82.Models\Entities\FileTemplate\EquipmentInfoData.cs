﻿using iTextSharp.text;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H82.Models.Entities.FileTemplate
{
    /// <summary>
    /// 设备数据项
    /// </summary>
    public class EquipmentInfoData
    {
        public int NO { get; set; }
        public string EQUIPMENT_ID { get; set; } = "";
        public DateTime? EQ_SCRAP_TIME { get; set; }
        public string SELL_PRICE { get; set; } = "";
        public string DEPRECIATION_TIME { get; set; }
        public DateTime? ENABLE_TIME { get; set; }
        public DateTime? INSTALL_DATE { get; set; }
        public DateTime? EQ_IN_TIME { get; set; }
        public DateTime? EQ_OUT_TIME { get; set; }
        public string REMARK { get; set; } = "";
        public string INSTALL_AREA { get; set; } = "";
        public string SERIAL_NUMBER { get; set; } = "";
        public string PROFESSIONAL_CLASS { get; set; } = "";
        public string CONTACT_PHONE { get; set; } = "";
        public string REGISTRATION_ENUM { get; set; } = "";
        public string EQUIPMENT_CLASS { get; set; } = "";
        public string KEEP_PERSON { get; set; } = "";
        public string REGISTRATION_NUM { get; set; } = "";
        public string EQUIPMENT_CODE { get; set; } = "";
        public string UNIT_ID { get; set; } = "";
        public string DEPT_SECTION_NO { get; set; } = "";
        public string EQUIPMENT_MODEL { get; set; } = "";
        public string LAB_NAME { get; set; } = "";
        public string SECTION_NO { get; set; } = "";
        public string EQUIPMENT_ENAME { get; set; } = "";
        public string HOSPITAL_NAME { get; set; } = "";
        public string EQUIPMENT_NUM { get; set; } = "";
        public string EQUIPMENT_NAME { get; set; } = "";
        public string EQUIPMENT_ARCHIVE { get; set; } = "";
        public string EQUIPMENT_STATE { get; set; } = "";


        public EquipmentInfoRecord<WeekWorkPlan> WeekWorkPlans { get; set; } = new("week");
        public EquipmentInfoRecord<MonthWorkPlan> MonthWorkPlans { get; set; } = new("moon");
        public EquipmentInfoRecord<YearWorkPlan> YearWorkPlans { get; set; } = new("year");
        public EquipmentInfoRecord<RunRecord> RunRecords { get; set; } = new("run");

        public EquipmentInfoRecord<EquipmentEnvironment> Environments { get; set; } = new("evn");

        public EquipmentInfoRecord<Dealer> Dealers { get; set; } = new("deal");
        public EquipmentInfoRecord<Manufacturer> Manufacturers { get; set; } = new("manu");

        public EquipmentInfoRecord<SubscriptionRecord> Subscriptions { get; set; } = new("subs");

        public EquipmentInfoRecord<TrainingRecord> TrainingRecords { get; set; } = new("train");

        public EquipmentInfoRecord<Procurement> Procurements { get; set; } = new("purc");
        public EquipmentInfoRecord<Startupinfo> Startupinfos { get; set; } = new("prop");

        public EquipmentInfoRecord<PlanRecord> PlanRecords { get; set; } = new("plan");

        public EquipmentInfoRecord<OpenRecord> OpenRecords { get; set; } = new("open");

        public EquipmentInfoRecord<OpenPartRecord> OpenPartRecords { get; set; } = new("part");

        public EquipmentInfoRecord<InstallRecord> InstallRecords { get; set; } = new("install");

        public EquipmentInfoRecord<CorrectReocrd> CorrectReocrds { get; set; } = new("corr");

        public EquipmentInfoRecord<ComparisonReocrd> ComparisonReocrds { get; set; } = new("com");

        public EquipmentInfoRecord<DebugeRecord> DebugeRecords { get; set; } = new("bug");
        public EquipmentInfoRecord<AuthRecord> AuthRecords { get; set; } = new("auth");
        public EquipmentInfoRecord<Appearance> Appearances { get; set; } = new("appe");
        public EquipmentInfoRecord<AcceptanceReport> AcceptanceReports { get; set; } = new("acce");

        public EquipmentInfoRecord<MaintenanceRecord> MaintenanceRecords { get; set; } = new("mainte");
        public EquipmentInfoRecord<Verification> VerificationRecords { get; set; } = new("verificae");

        public EquipmentInfoRecord<FixRecord> FixRecords { get; set; } = new EquipmentInfoRecord<FixRecord>("fix");
        
        public class FixRecord
        {
            public string REPAIR_ID { get; set; } = "";
            public string REPAIR_NO { get; set; }= "";
            public DateTime? REPAIR_DATE { get; set; }
            public string REPAIR_PERSON { get; set; }= "";
            public string REPAIR_CONTENT { get; set; }= "";
            public string REPAIR_RESULT { get; set; }= "";
            public string OCCUR_EVENT { get; set; }= "";
            public string REMARK { get; set; }= "";
        }
        
        public class AcceptanceReport
        {
            public string APPEARANCE { get; set; } = "";
        }

        public class Appearance
        {
            public string APPEARANCE { get; set; } = "";
        }

        public class AuthRecord
        {
            public string APPEARANCE { get; set; } = "";
            public string AUTHORIZE_PERSON { get; set; } = "";
            public string AUTHORIZED_PERSON { get; set; } = "";
            public string AUTHORIZED_ROLE { get; set; } = "";
            public string AUTHORIZED_PERSON_POST { get; set; } = "";
            public DateTime? AUTHORIZE_DATE { get; set; }
        }

        public class DebugeRecord
        {
            public DateTime? DEBUG_DATE { get; set; }
            public string HOSPITAL_PERSON { get; set; } = "";
            public string ENGINEER { get; set; } = "";
            public string DEBUG_CONDITION { get; set; } = "";
            public string LAB_PERSON { get; set; } = "";
            public string RELATION_WAY { get; set; } = "";
            public string APPEARANCE { get; set; } = "";

        }
        
        /// <summary>
        /// 比对记录
        /// </summary>
        public class ComparisonReocrd
        {
            public string COMPARISON_INTERVALS { get; set; } = "";
            public string COMPARISON_WARN_INTERVALS { get; set; } = "";
            public DateTime? LAST_COMPARISON_INTERVALS_DATE { get; set; }
            public DateTime? NEXT_COMPARISON_INTERVALS_DATE { get; set; }
            public string COMPARISON_ID { get; set; }
            public DateTime? COMPARISON_DATE { get; set; }
            public string? COMPARISON_OBJECT { get; set; }
            public string? COMPARISON_RESULT { get; set; }
            public string? RELATION_EVENT { get; set; }
            public string? COMPARISON_NO { get; set; }
            public string? STATE { get; set; }
            public string? COMPARISON_PERSON { get; set; }
            public string REMARK { get; set; } = "";
        }

        /// <summary>
        /// 校准记录
        /// </summary>
        public class CorrectReocrd
        {
            public string CORRECT_INTERVALS { get; set; } = "";
            public string CORRECT_WARN_INTERVALS { get; set; } = "";
            public DateTime? LAST_CORRECT_INTERVALS_DATE { get; set; }
            public DateTime? NEXT_CORRECT_INTERVALS_DATE { get; set; }
            public string CORRECT_UNIT { get; set; } = "";
            public string EQ_IN_PERSON { get; set; } = "";
            public string CORRECT_ID { get; set; }
            public DateTime? CORRECT_DATE { get; set; }
            public string? CORRECT_PERSON { get; set; }
            public string? CORRECT_RESULT { get; set; }
            public string? RELATION_EVENT { get; set; }
            public string? STATE { get; set; }
            public string? CORRECT_NO { get; set; }
            public string? OCCUR_EVENT { get; set; }
            public string REMARK { get; set; } = "";
        }

        /// <summary>
        /// 安装信息
        /// </summary>
        public class InstallRecord
        {
            public string APPEARANCE { get; set; } = "";
            public DateTime? INSTALL_DATE { get; set; }
            public string INSTALL_AREA { get; set; } = "";
            public string LAB_PERSON { get; set; } = "";
            public string ENGINEER { get; set; } = "";
            public string INSTALL_CONDITION { get; set; } = "";
            public string HOSPITAL_PERSON { get; set; } = "";
            public string RELATION_WAY { get; set; } = "";
            public string REMARK { get; set; } = "";

        }

        /// <summary>
        /// 开箱记录
        /// </summary>
        public class OpenRecord
        {

            public DateTime? UNPACK_DATE { get; set; }
            public string LAB_PERSON { get; set; } = "";
            public string HOSPITAL_PERSON { get; set; } = "";
            public string UNPACK_PERSON { get; set; } = "";
            public string UNPACK_INSPECT { get; set; } = "";
            public string UNPACK_CONDITION { get; set; } = "";
            public string ENGINEER { get; set; } = "";
            public string RELATION_WAY { get; set; } = "";
            public string REMARK { get; set; } = "";


        }
        
        /// <summary>
        /// 配件记录
        /// </summary>
        public class OpenPartRecord
        {
            public int NO { get; set; }
            public string PARTS_NAME { get; set; } = "";
            public string PARTS_MODEL { get; set; } = "";
            public string PARTS_SPEC { get; set; } = "";
            public string PARTS_AMOUNT { get; set; } = "";
            public string PARTS_SNUM { get; set; } = "";
            public string PARTS_ORIGIN { get; set; } = "";
            public string PARTS_BRAND { get; set; } = "";
            public string PARTS_POSITION { get; set; } = "";
            public string APPEARANCE { get; set; } = "";
        }


        /// <summary>
        /// 工作计划记录
        /// </summary>
        public class PlanRecord
        {
            public int NO { get; set; }
            public string OPER_PERSON { get; set; } = "";
            public DateTime? OPER_TIME { get; set; }
            public string AUDITOR_USER_NAME { get; set; } = "";
            public DateTime? AUDITOR_TIME { get; set; }
            public string AUDITOR_CONTEXT { get; set; } = "";
            public string REGISTRATION_NUM { get; set; } = "";
            public string REGISTRATION_NAME { get; set; } = "";
        }

        /// <summary>
        /// 开机性能验证报告
        /// </summary>
        public class Startupinfo
        {
            public string APPEARANCE { get; set; } = "";
            public DateTime? START_DATE { get; set; }
            public DateTime? END_DATE { get; set; }
            public string VERIFY_PERSON { get; set; } = "";
            public string VERIFY_RESULT { get; set; } = "";
            public string REMARK { get; set; } = "";



        }

        /// <summary>
        /// 采购信息
        /// </summary>
        public class Procurement
        {

            public string CALL_BIDS_NAME { get; set; } = "";
            public string CALL_BIDS_NO { get; set; } = "";
            public DateTime? CALL_BIDS_DATE { get; set; }
            public string CONTRACT_NAME { get; set; } = "";
            public string CONTRACT_NO { get; set; } = "";
            public DateTime? CONTRACT_DATE { get; set; }
            public string APPEARANCE { get; set; } = "";
        }

        /// <summary>
        /// 工作计划周保养
        /// </summary>
        public class WeekWorkPlan
        {
            /// <summary>
            /// 最近一次周保养时间
            /// </summary>
            public DateTime? LAST_MAINTAIN_DATE { get; set; }
            /// <summary>
            /// 周保养周期/天
            /// </summary>
            public string MAINTAIN_INTERVALS { get; set; } = "";
            /// <summary>
            /// 周保养提醒周期/天
            /// </summary>
            public string MAINTAIN_WARN_INTERVALS { get; set; } = "";
        }

        public class MonthWorkPlan
        {
            /// <summary>
            /// 月保养周期/天
            /// </summary>
            public string MONTHLY_MAINTAIN { get; set; } = "";

            /// <summary>
            /// 月保养提醒周期/天
            /// </summary>
            public string MONTHLY_MAINTAIN_WARN { get; set; } = "";

            /// <summary>
            /// 最近一次月保养时间
            /// </summary>
            public DateTime? LAST_MONTHLY_MAINTAIN_DATE { get; set; }
        }

        public class YearWorkPlan
        {

            /// <summary>
            /// 年保养周期/天
            /// </summary>
            public string YEARLY_MAINTAIN { get; set; } = "";

            /// <summary>
            /// 年保养提醒周期/天
            /// </summary>
            public string YEARLY_MAINTAIN_WARN { get; set; } = "";

            /// <summary>
            /// 最近一次年保养时间
            /// </summary>
            public DateTime? LAST_YEARLY_MAINTAIN_DATE { get; set; }

        }

        /// <summary>
        /// 运行记录
        /// </summary>
        public class RunRecord
        {
            public DateTime? LAST_MAINTAIN_DATE { get; set; }
            public string MAINTAIN_TYPE { get; set; } = "";
            public DateTime? NEXT_MAINTAIN_DATE { get; set; }
            public DateTime? LAST_CORRECT_DATE { get; set; }
            public string CORRECT_INTERVALS { get; set; } = "";
            public DateTime? NEXT_CORRECT_DATE { get; set; }
            public DateTime? LAST_COMPARISON_DATE { get; set; }
            public string COMPARISON_INTERVALS { get; set; }
            public DateTime? NEXT_COMPARISON_DATE { get; set; }
            public DateTime? LAST_VERIFICATION_DATE { get; set; }
            public string VERIFICATION_INTERVALS { get; set; } = "";
            public DateTime? NEXT_VERIFICATION_DATE { get; set; }
            
        }

        /// <summary>
        /// 经销商联系人
        /// </summary>
        public class Dealer
        {
            public int NO { get; set; } 
            public string CONTACT_TYPE { get; set; } = "";
            public string CONTACT_POST { get; set; } = "";
            public string CONTACT_NAME { get; set; } = "";
            public string PHONE_NO { get; set; } = "";
            public string CONTACT_WX { get; set; } = "";
            public string E_MAIL { get; set; } = "";
            public string REMARK { get; set; } = "";
            public string DEALER { get; set; } = "";
            public string DEALER_ENAME { get; set; } = "";
        }

        /// <summary>
        /// 经销商联系人
        /// </summary>
        public class Manufacturer
        {
            public int NO { get; set; } 
            public string CONTACT_TYPE { get; set; } = "";
            public string CONTACT_POST { get; set; } = "";
            public string CONTACT_NAME { get; set; } = "";
            public string PHONE_NO { get; set; } = "";
            public string CONTACT_WX { get; set; } = "";
            public string E_MAIL { get; set; } = "";
            public string REMARK { get; set; } = "";
            public string MANUFACTURER { get; set; } = "";
            public string MANUFACTURER_ENAME { get; set; } = "";
        }

        /// <summary>
        /// 培训记录
        /// </summary>
        public class TrainingRecord
        {
            public string APPEARANCE { get; set; } = "";
            public DateTime? TRAIN_TIME { get; set; }
            public string TRAIN_NAME { get; set; } = "";
            public string TRAIN_ADDR { get; set; } = "";
            public string TRAIN_HOUR { get; set; } = "";
            public string JOIN_PERSON { get; set; } = "";
            public string TRAIN_TEACHER { get; set; } = "";
            public string REMARK { get; set; } = "";


        }

        /// <summary>
        /// 申购记录
        /// </summary>
        public class SubscriptionRecord
        {
            public string SUBSCRIBE_NAME { get; set; } = "";
            public string SUBSCRIBE_PERSON { get; set; } = "";
            public DateTime? SUBSCRIBE_DATE { get; set; }
            public string MGROUP_ID { get; set; } = "";
            public string APPROVE_PERSON { get; set; } = "";
            public DateTime? APPROVE_TIME { get; set; }
            public string APPROVE_OPINION { get; set; } = "";
            public string SUBSCRIBE_STATE { get; set; } = "";
            public string REMARK { get; set; } = "";
        }

        /// <summary>
        /// 环境信息
        /// </summary>
        public class EquipmentEnvironment
        {
            public string AIR_REQUIRE { get; set; } = "";
            public string SPACE_REQUIRE { get; set; } = "";
            public string WATER_REQUIRE { get; set; } = "";
            public string TEMP_MIN { get; set; } = "";
            public string TEMP_MAX { get; set; } = "";
            public string HUMI_MIN { get; set; } = "";
            public string HUMI_MAX { get; set; } = "";
            public string AIR_PRESSURE_REQUIRE { get; set; } = "";
            public string POWER_REQUIRE { get; set; } = "";
            public string VOLTAGE_REQUIRE { get; set; } = "";
            public string ELECTRICITY_REQUIRE { get; set; } = "";
            public string OTHER_REQUIRE { get; set; } = "";
            public string LENGTH { get; set; } = "";
            public string WIDTH { get; set; } = "";
            public string HEIGHT { get; set; } = "";
            public string EQUIPMENT_WEIGHT { get; set; } = "";
            public string BEARING_REQUIRE { get; set; } = "";

        }
        /// <summary>
        /// 保养记录
        /// </summary>
        public class MaintenanceRecord
        {
            public string MAINTAIN_ID { get; set; } = "";
            public string? MAINTAIN_CYCLE { get; set; } = "";
            public DateTime? MAINTAIN_DATE { get; set; } = null;
            public string? MAINTAIN_PERSON { get; set; } = "";
            public string? MAINTAIN_CONTENT { get; set; } = "";
            public string? OCCUR_EVENT { get; set; } = "";
            public string? MAINTAIN_NO { get; set; } = "";
            
            public string? REMARK { get; set; } = "";

        }

        /// <summary>
        /// 性能验证记录
        /// </summary>
        public class Verification
        {
            public string VERIFICATION_ID { get; set; } = "";
            public DateTime? VERIFICATION_DATE { get; set; } = null;
            public string? RELATION_EVENT { get; set; } = "";
            public string? VERIFICATION_PERSON { get; set; } = "";
            public string? VERIFICATION_RESULT { get; set; } = "";
            public string? STATE { get; set; } = "";
            public string? VERIFICATION_NO { get; set; } = "";
            
            public string? REMARK { get; set; } = "";
            
        }

        public class EquipmentInfoRecord<T> where T : class, new()
        {
            public string ClassCode { get; set; }
            public List<T> Records { get; set; } = new List<T>();

            public EquipmentInfoRecord(string classCode)
            {
                ClassCode = classCode;
            }
        }

    }
}
