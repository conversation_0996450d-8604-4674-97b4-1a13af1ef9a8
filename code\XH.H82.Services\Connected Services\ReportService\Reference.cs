﻿//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//
//     对此文件的更改可能导致不正确的行为，并在以下条件下丢失:
//     代码重新生成。
// </auto-generated>
//------------------------------------------------------------------------------

namespace ReportService
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.ServiceContractAttribute(Namespace="http://www.xhlis.com/", ConfigurationName="ReportService.XingHePrintServiceSoap")]
    public interface XingHePrintServiceSoap
    {
        
        [System.ServiceModel.OperationContractAttribute(Action="http://www.xhlis.com/BarcodePrint", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<string> BarcodePrintAsync(string dataXml, string printXml);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://www.xhlis.com/BarcodeGenerate", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<string> BarcodeGenerateAsync(string dataXml, string printXml);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://www.xhlis.com/BarcodeNumberPrint", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<string> BarcodeNumberPrintAsync(string printXml);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://www.xhlis.com/BarcodePrint6", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<string> BarcodePrint6Async(string dataXml);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://www.xhlis.com/EHMBarcodePrint", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<string> EHMBarcodePrintAsync(string dataXml, string printXml);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://www.xhlis.com/ExportPdfReport", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<string> ExportPdfReportAsync(string DataXml);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://www.xhlis.com/EHMExportPdfReport", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<string> EHMExportPdfReportAsync(string DataXml);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://www.xhlis.com/ExportSumPdfReport", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<string> ExportSumPdfReportAsync(string sumType, string DataXml);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://www.xhlis.com/RecvPdfReport", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<ReportService.RecvPdfReportResponse> RecvPdfReportAsync(ReportService.RecvPdfReportRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://www.xhlis.com/GetPdfReportData", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<ReportService.GetPdfReportDataResponse> GetPdfReportDataAsync(ReportService.GetPdfReportDataRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://www.xhlis.com/ExportPdfOtherSheet", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<string> ExportPdfOtherSheetAsync(string DataXml);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://www.xhlis.com/ExportPdfSheet6", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<string> ExportPdfSheet6Async(string DataXml);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://www.xhlis.com/PrintPdfReport", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<string> PrintPdfReportAsync(string DataXml);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://www.xhlis.com/ModifyPdfReport", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<string> ModifyPdfReportAsync(string DataXml);
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="RecvPdfReport", WrapperNamespace="http://www.xhlis.com/", IsWrapped=true)]
    public partial class RecvPdfReportRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.xhlis.com/", Order=0)]
        public string DataXml;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.xhlis.com/", Order=1)]
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary")]
        public byte[] pdfData;
        
        public RecvPdfReportRequest()
        {
        }
        
        public RecvPdfReportRequest(string DataXml, byte[] pdfData)
        {
            this.DataXml = DataXml;
            this.pdfData = pdfData;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="RecvPdfReportResponse", WrapperNamespace="http://www.xhlis.com/", IsWrapped=true)]
    public partial class RecvPdfReportResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.xhlis.com/", Order=0)]
        public string RecvPdfReportResult;
        
        public RecvPdfReportResponse()
        {
        }
        
        public RecvPdfReportResponse(string RecvPdfReportResult)
        {
            this.RecvPdfReportResult = RecvPdfReportResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetPdfReportData", WrapperNamespace="http://www.xhlis.com/", IsWrapped=true)]
    public partial class GetPdfReportDataRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.xhlis.com/", Order=0)]
        public string pdfPath;
        
        public GetPdfReportDataRequest()
        {
        }
        
        public GetPdfReportDataRequest(string pdfPath)
        {
            this.pdfPath = pdfPath;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetPdfReportDataResponse", WrapperNamespace="http://www.xhlis.com/", IsWrapped=true)]
    public partial class GetPdfReportDataResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.xhlis.com/", Order=0)]
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary")]
        public byte[] GetPdfReportDataResult;
        
        public GetPdfReportDataResponse()
        {
        }
        
        public GetPdfReportDataResponse(byte[] GetPdfReportDataResult)
        {
            this.GetPdfReportDataResult = GetPdfReportDataResult;
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    public interface XingHePrintServiceSoapChannel : ReportService.XingHePrintServiceSoap, System.ServiceModel.IClientChannel
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    public partial class XingHePrintServiceSoapClient : System.ServiceModel.ClientBase<ReportService.XingHePrintServiceSoap>, ReportService.XingHePrintServiceSoap
    {
        
        /// <summary>
        /// 实现此分部方法，配置服务终结点。
        /// </summary>
        /// <param name="serviceEndpoint">要配置的终结点</param>
        /// <param name="clientCredentials">客户端凭据</param>
        static partial void ConfigureEndpoint(System.ServiceModel.Description.ServiceEndpoint serviceEndpoint, System.ServiceModel.Description.ClientCredentials clientCredentials);
        
        public XingHePrintServiceSoapClient(EndpointConfiguration endpointConfiguration) : 
                base(XingHePrintServiceSoapClient.GetBindingForEndpoint(endpointConfiguration), XingHePrintServiceSoapClient.GetEndpointAddress(endpointConfiguration))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public XingHePrintServiceSoapClient(EndpointConfiguration endpointConfiguration, string remoteAddress) : 
                base(XingHePrintServiceSoapClient.GetBindingForEndpoint(endpointConfiguration), new System.ServiceModel.EndpointAddress(remoteAddress))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public XingHePrintServiceSoapClient(EndpointConfiguration endpointConfiguration, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(XingHePrintServiceSoapClient.GetBindingForEndpoint(endpointConfiguration), remoteAddress)
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public XingHePrintServiceSoapClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress)
        {
        }
        
        public System.Threading.Tasks.Task<string> BarcodePrintAsync(string dataXml, string printXml)
        {
            return base.Channel.BarcodePrintAsync(dataXml, printXml);
        }
        
        public System.Threading.Tasks.Task<string> BarcodeGenerateAsync(string dataXml, string printXml)
        {
            return base.Channel.BarcodeGenerateAsync(dataXml, printXml);
        }
        
        public System.Threading.Tasks.Task<string> BarcodeNumberPrintAsync(string printXml)
        {
            return base.Channel.BarcodeNumberPrintAsync(printXml);
        }
        
        public System.Threading.Tasks.Task<string> BarcodePrint6Async(string dataXml)
        {
            return base.Channel.BarcodePrint6Async(dataXml);
        }
        
        public System.Threading.Tasks.Task<string> EHMBarcodePrintAsync(string dataXml, string printXml)
        {
            return base.Channel.EHMBarcodePrintAsync(dataXml, printXml);
        }
        
        public System.Threading.Tasks.Task<string> ExportPdfReportAsync(string DataXml)
        {
            return base.Channel.ExportPdfReportAsync(DataXml);
        }
        
        public System.Threading.Tasks.Task<string> EHMExportPdfReportAsync(string DataXml)
        {
            return base.Channel.EHMExportPdfReportAsync(DataXml);
        }
        
        public System.Threading.Tasks.Task<string> ExportSumPdfReportAsync(string sumType, string DataXml)
        {
            return base.Channel.ExportSumPdfReportAsync(sumType, DataXml);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<ReportService.RecvPdfReportResponse> ReportService.XingHePrintServiceSoap.RecvPdfReportAsync(ReportService.RecvPdfReportRequest request)
        {
            return base.Channel.RecvPdfReportAsync(request);
        }
        
        public System.Threading.Tasks.Task<ReportService.RecvPdfReportResponse> RecvPdfReportAsync(string DataXml, byte[] pdfData)
        {
            ReportService.RecvPdfReportRequest inValue = new ReportService.RecvPdfReportRequest();
            inValue.DataXml = DataXml;
            inValue.pdfData = pdfData;
            return ((ReportService.XingHePrintServiceSoap)(this)).RecvPdfReportAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<ReportService.GetPdfReportDataResponse> ReportService.XingHePrintServiceSoap.GetPdfReportDataAsync(ReportService.GetPdfReportDataRequest request)
        {
            return base.Channel.GetPdfReportDataAsync(request);
        }
        
        public System.Threading.Tasks.Task<ReportService.GetPdfReportDataResponse> GetPdfReportDataAsync(string pdfPath)
        {
            ReportService.GetPdfReportDataRequest inValue = new ReportService.GetPdfReportDataRequest();
            inValue.pdfPath = pdfPath;
            return ((ReportService.XingHePrintServiceSoap)(this)).GetPdfReportDataAsync(inValue);
        }
        
        public System.Threading.Tasks.Task<string> ExportPdfOtherSheetAsync(string DataXml)
        {
            return base.Channel.ExportPdfOtherSheetAsync(DataXml);
        }
        
        public System.Threading.Tasks.Task<string> ExportPdfSheet6Async(string DataXml)
        {
            return base.Channel.ExportPdfSheet6Async(DataXml);
        }
        
        public System.Threading.Tasks.Task<string> PrintPdfReportAsync(string DataXml)
        {
            return base.Channel.PrintPdfReportAsync(DataXml);
        }
        
        public System.Threading.Tasks.Task<string> ModifyPdfReportAsync(string DataXml)
        {
            return base.Channel.ModifyPdfReportAsync(DataXml);
        }
        
        public virtual System.Threading.Tasks.Task OpenAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
        }
        
        public virtual System.Threading.Tasks.Task CloseAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginClose(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndClose));
        }
        
        private static System.ServiceModel.Channels.Binding GetBindingForEndpoint(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.XingHePrintServiceSoap))
            {
                System.ServiceModel.BasicHttpBinding result = new System.ServiceModel.BasicHttpBinding();
                result.MaxBufferSize = int.MaxValue;
                result.ReaderQuotas = System.Xml.XmlDictionaryReaderQuotas.Max;
                result.MaxReceivedMessageSize = int.MaxValue;
                result.AllowCookies = true;
                return result;
            }
            if ((endpointConfiguration == EndpointConfiguration.XingHePrintServiceSoap12))
            {
                System.ServiceModel.Channels.CustomBinding result = new System.ServiceModel.Channels.CustomBinding();
                System.ServiceModel.Channels.TextMessageEncodingBindingElement textBindingElement = new System.ServiceModel.Channels.TextMessageEncodingBindingElement();
                textBindingElement.MessageVersion = System.ServiceModel.Channels.MessageVersion.CreateVersion(System.ServiceModel.EnvelopeVersion.Soap12, System.ServiceModel.Channels.AddressingVersion.None);
                result.Elements.Add(textBindingElement);
                System.ServiceModel.Channels.HttpTransportBindingElement httpBindingElement = new System.ServiceModel.Channels.HttpTransportBindingElement();
                httpBindingElement.AllowCookies = true;
                httpBindingElement.MaxBufferSize = int.MaxValue;
                httpBindingElement.MaxReceivedMessageSize = int.MaxValue;
                result.Elements.Add(httpBindingElement);
                return result;
            }
            throw new System.InvalidOperationException(string.Format("找不到名称为“{0}”的终结点。", endpointConfiguration));
        }
        
        private static System.ServiceModel.EndpointAddress GetEndpointAddress(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.XingHePrintServiceSoap))
            {
                return new System.ServiceModel.EndpointAddress("http://************:16702/XingHePrintService.asmx");
            }
            if ((endpointConfiguration == EndpointConfiguration.XingHePrintServiceSoap12))
            {
                return new System.ServiceModel.EndpointAddress("http://************:16702/XingHePrintService.asmx");
            }
            throw new System.InvalidOperationException(string.Format("找不到名称为“{0}”的终结点。", endpointConfiguration));
        }
        
        public enum EndpointConfiguration
        {
            
            XingHePrintServiceSoap,
            
            XingHePrintServiceSoap12,
        }
    }
}
