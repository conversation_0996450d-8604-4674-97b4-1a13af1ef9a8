﻿using H.Utility;
using RestSharp;
using XH.H82.Models.DeviceRelevantInformation;
using XH.H82.Models.DeviceRelevantInformation.Dto;

namespace XH.H82.IServices.DeviceDataRefresh
{
    public interface IDeviceRefreshService
    {

        EquipmentWarnInfoDto GetEquipmentWarnInfo(string hosptalId, string? areaId, string? labId);

        List<EquipmentDisplay> GetEquipmentDisplayInfo();

        ResultDto GetDisplayInfo(string equipmentId);
        byte[] GetDisplayInfoTest(string equipmentId);
        void AutoRefreshDisplayInfo();

        void AutoRefreshDisplayInfo(string id);


        /// <summary>
        /// 查询可绑定墨水屏的设备列表
        /// </summary>
        /// <param name="hosptialId"></param>
        /// <param name="labId"></param>
        /// <param name="pGourpId"></param>
        /// <param name="codeOrModelOrnName"></param>
        /// <returns></returns>
        List<InkScreenBindEquipmentDto> GetInkScreenBindEquipments(ClaimsDto user, string hosptialId, string? labId, string? pGourpId, string? codeOrModelOrnName);


    }
}
