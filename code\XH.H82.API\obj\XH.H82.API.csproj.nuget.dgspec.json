{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.API\\XH.H82.API.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.API\\XH.H82.API.csproj": {"version": "6.25.300", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.API\\XH.H82.API.csproj", "projectName": "XH.H82.API", "projectPath": "C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.API\\XH.H82.API.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.API\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Base\\XH.H82.Base.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Base\\XH.H82.Base.csproj"}, "C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.IServices\\XH.H82.IServices.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.IServices\\XH.H82.IServices.csproj"}, "C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Models\\XH.H82.Models.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Models\\XH.H82.Models.csproj"}, "C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Services\\XH.H82.Services.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Services\\XH.H82.Services.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Autofac.Extensions.DependencyInjection": {"target": "Package", "version": "[8.0.0, )"}, "Autofac.Extras.DynamicProxy": {"target": "Package", "version": "[6.0.1, )"}, "EPPlus": {"target": "Package", "version": "[6.2.6, )"}, "ExcelDataReader": {"target": "Package", "version": "[3.7.0-develop00385, )"}, "ExcelDataReader.DataSet": {"target": "Package", "version": "[3.7.0-develop00385, )"}, "Microsoft.Extensions.Hosting.WindowsServices": {"target": "Package", "version": "[6.0.1, )"}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": {"target": "Package", "version": "[1.21.0, )"}, "NPOI": {"target": "Package", "version": "[2.6.0, )"}, "Namotion.Reflection": {"target": "Package", "version": "[2.1.1, )"}, "Npoi.Mapper": {"target": "Package", "version": "[6.0.0, )"}, "Yarp.ReverseProxy": {"target": "Package", "version": "[2.1.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Base\\XH.H82.Base.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Base\\XH.H82.Base.csproj", "projectName": "XH.H82.Base", "projectPath": "C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Base\\XH.H82.Base.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Base\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Models\\XH.H82.Models.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Models\\XH.H82.Models.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"EPPlus": {"target": "Package", "version": "[6.2.6, )"}, "EasyCaching.Serialization.Protobuf": {"target": "Package", "version": "[1.7.0, )"}, "FireflySoft.RateLimit.AspNetCore": {"target": "Package", "version": "[3.0.0, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[6.0.15, )"}, "RestSharp": {"target": "Package", "version": "[112.1.0, )"}, "Serilog.Sinks.FastConsole": {"target": "Package", "version": "[2.2.0, )"}, "Serilog.Sinks.SpectreConsole": {"target": "Package", "version": "[0.3.3, )"}, "SkiaSharp": {"target": "Package", "version": "[2.88.9, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.2.3, )"}, "Swashbuckle.AspNetCore.Filters": {"target": "Package", "version": "[7.0.5, )"}, "Swashbuckle.AspNetCore.Filters.Abstractions": {"target": "Package", "version": "[7.0.5, )"}, "Unchase.Swashbuckle.AspNetCore.Extensions": {"target": "Package", "version": "[2.6.12, )"}, "iTextSharp": {"target": "Package", "version": "[5.5.13.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.IServices\\XH.H82.IServices.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.IServices\\XH.H82.IServices.csproj", "projectName": "XH.H82.IServices", "projectPath": "C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.IServices\\XH.H82.IServices.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.IServices\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Base\\XH.H82.Base.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Base\\XH.H82.Base.csproj"}, "C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Models\\XH.H82.Models.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Models\\XH.H82.Models.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"EPPlus": {"target": "Package", "version": "[6.2.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Models\\XH.H82.Models.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Models\\XH.H82.Models.csproj", "projectName": "XH.H82.Models", "projectPath": "C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Models\\XH.H82.Models.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Models\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"EPPlus": {"target": "Package", "version": "[6.2.6, )"}, "XH.LAB.UTILS": {"target": "Package", "version": "[6.25.301.26, )"}, "Xinghe.Utility": {"target": "Package", "version": "[6.25.206, )"}, "protobuf-net.Core": {"target": "Package", "version": "[3.1.22, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Services\\XH.H82.Services.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Services\\XH.H82.Services.csproj", "projectName": "XH.H82.Services", "projectPath": "C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Services\\XH.H82.Services.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Services\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Base\\XH.H82.Base.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.Base\\XH.H82.Base.csproj"}, "C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.IServices\\XH.H82.IServices.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\projects\\h82\\behind\\code\\XH.H82.IServices\\XH.H82.IServices.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"EPPlus": {"target": "Package", "version": "[6.2.6, )"}, "NuGet.Protocol": {"target": "Package", "version": "[6.6.1, )"}, "SkiaSharp.NativeAssets.Linux": {"target": "Package", "version": "[2.88.6, )"}, "System.ServiceModel.Duplex": {"target": "Package", "version": "[4.8.*, )"}, "System.ServiceModel.Federation": {"target": "Package", "version": "[4.8.*, )"}, "System.ServiceModel.Http": {"target": "Package", "version": "[4.10.0, )"}, "System.ServiceModel.NetTcp": {"target": "Package", "version": "[4.8.*, )"}, "System.ServiceModel.Security": {"target": "Package", "version": "[4.8.*, )"}, "System.Xml.XmlSerializer": {"target": "Package", "version": "[4.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}}}