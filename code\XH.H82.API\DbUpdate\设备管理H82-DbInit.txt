-- XH_OA.EMS_AUTHORIZE_INFO definition


-- WARNING: It may differ from actual native database DDL

-- Drop table

-- XH_OA.EMS_AUTHORIZE_INFO;

CREATE TABLE XH_OA.EMS_AUTHORIZE_INFO (
	AUTHORIZE_ID VARCHAR2(50),
	HOSPITAL_ID VARCHAR2(20),
	EQUIPMENT_ID VARCHAR2(20),
	AUTHORIZE_PERSON VARCHAR2(50),
	AUTHORIZED_PERSON VARCHAR2(800),
	AUTHORIZED_ROLE VARCHAR2(200),
	AUTHORIZED_PERSON_POST VARCHAR2(50),
	AUTHORIZE_DATE DATE,
	AUTHORIZE_STATE VARCHAR2(20),
	FIRST_RPERSON VARCHAR2(50),
	FIRST_RTIME DATE,
	LAST_MPERSON VARCHAR2(50),
	LAST_MTIME DATE,
	REMARK VARCHAR2(200),
	CONSTRAINT SYS_C0010767 CHECK ("AUTHORIZE_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010768 CHECK ("HOSPITAL_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010769 CHECK ("EQUIPMENT_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010770 PRIMARY KEY (AUTHORIZE_ID)
);
CREATE UNIQUE INDEX SYS_C0010770 ON XH_OA.EMS_AUTHORIZE_INFO (AUTHORIZE_ID);


-- XH_OA.EMS_CHANGE_INFO definition


-- WARNING: It may differ from actual native database DDL

-- Drop table

-- XH_OA.EMS_CHANGE_INFO;

CREATE TABLE XH_OA.EMS_CHANGE_INFO (
	CHANGE_ID VARCHAR2(50),
	HOSPITAL_ID VARCHAR2(20),
	EQUIPMENT_ID VARCHAR2(20),
	CHANGE_DATE DATE,
	CHANGE_PERSON VARCHAR2(50),
	CHANGE_CONTENT VARCHAR2(200),
	FIRST_RPERSON VARCHAR2(50),
	FIRST_RTIME DATE,
	LAST_MPERSON VARCHAR2(50),
	LAST_MTIME DATE,
	REMARK VARCHAR2(200),
	CHANGE_NO VARCHAR2(50),
	CHANGE_STATE VARCHAR2(20),
	CONSTRAINT SYS_C0010771 CHECK ("CHANGE_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010772 CHECK ("HOSPITAL_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010773 CHECK ("EQUIPMENT_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010774 PRIMARY KEY (CHANGE_ID)
);
CREATE UNIQUE INDEX SYS_C0010774 ON XH_OA.EMS_CHANGE_INFO (CHANGE_ID);


-- XH_OA.EMS_COMPARISON_INFO definition


-- WARNING: It may differ from actual native database DDL

-- Drop table

-- XH_OA.EMS_COMPARISON_INFO;

CREATE TABLE XH_OA.EMS_COMPARISON_INFO (
	COMPARISON_ID VARCHAR2(50),
	HOSPITAL_ID VARCHAR2(20),
	EQUIPMENT_ID VARCHAR2(20),
	COMPARISON_DATE DATE,
	COMPARISON_OBJECT VARCHAR2(50),
	COMPARISON_RESULT VARCHAR2(50),
	RELATION_EVENT VARCHAR2(50),
	FIRST_RPERSON VARCHAR2(50),
	FIRST_RTIME DATE,
	LAST_MPERSON VARCHAR2(50),
	LAST_MTIME DATE,
	REMARK VARCHAR2(200),
	COMPARISON_NO VARCHAR2(50),
	STATE VARCHAR2(50),
	COMPARISON_STATE VARCHAR2(20),
	COMPARISON_PERSON VARCHAR2(50),
	CONSTRAINT SYS_C0010775 CHECK ("COMPARISON_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010776 CHECK ("HOSPITAL_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010777 CHECK ("EQUIPMENT_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010778 PRIMARY KEY (COMPARISON_ID)
);
CREATE UNIQUE INDEX SYS_C0010778 ON XH_OA.EMS_COMPARISON_INFO (COMPARISON_ID);


-- XH_OA.EMS_CORRECT_INFO definition


-- WARNING: It may differ from actual native database DDL

-- Drop table

-- XH_OA.EMS_CORRECT_INFO;

CREATE TABLE XH_OA.EMS_CORRECT_INFO (
	CORRECT_ID VARCHAR2(50),
	HOSPITAL_ID VARCHAR2(20),
	EQUIPMENT_ID VARCHAR2(20),
	CORRECT_DATE DATE,
	CORRECT_DEPT VARCHAR2(50),
	CORRECT_PERSON VARCHAR2(50),
	CORRECT_RESULT VARCHAR2(50),
	RELATION_EVENT VARCHAR2(50),
	FIRST_RPERSON VARCHAR2(50),
	FIRST_RTIME DATE,
	LAST_MPERSON VARCHAR2(50),
	LAST_MTIME DATE,
	REMARK VARCHAR2(200),
	STATE VARCHAR2(50),
	CORRECT_NO VARCHAR2(50),
	CORRECT_STATE VARCHAR2(20),
	OCCUR_EVENT VARCHAR2(50),
	CONSTRAINT SYS_C0010779 CHECK ("CORRECT_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010780 CHECK ("HOSPITAL_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010781 PRIMARY KEY (CORRECT_ID)
);
CREATE UNIQUE INDEX SYS_C0010781 ON XH_OA.EMS_CORRECT_INFO (CORRECT_ID);


-- XH_OA.EMS_DEBUG_INFO definition


-- WARNING: It may differ from actual native database DDL

-- Drop table

-- XH_OA.EMS_DEBUG_INFO;

CREATE TABLE XH_OA.EMS_DEBUG_INFO (
	DEBUG_ID VARCHAR2(50),
	HOSPITAL_ID VARCHAR2(20),
	EQUIPMENT_ID VARCHAR2(20),
	DEBUG_DATE DATE,
	DEBUG_PERSON VARCHAR2(50),
	LAB_PERSON VARCHAR2(50),
	HOSPITAL_PERSON VARCHAR2(50),
	DEBUG_CONDITION VARCHAR2(500),
	ENGINEER VARCHAR2(50),
	RELATION_WAY VARCHAR2(50),
	DEBUG_STATE VARCHAR2(20),
	FIRST_RPERSON VARCHAR2(50),
	FIRST_RTIME DATE,
	LAST_MPERSON VARCHAR2(50),
	LAST_MTIME DATE,
	REMARK VARCHAR2(200),
	CONSTRAINT SYS_C0010782 CHECK ("DEBUG_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010783 CHECK ("HOSPITAL_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010784 CHECK ("EQUIPMENT_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010785 PRIMARY KEY (DEBUG_ID)
);
CREATE UNIQUE INDEX EQUIPMENT_LIMIT5 ON XH_OA.EMS_DEBUG_INFO (EQUIPMENT_ID);
CREATE UNIQUE INDEX SYS_C0010785 ON XH_OA.EMS_DEBUG_INFO (DEBUG_ID);


-- XH_OA.EMS_DOC_INFO definition


-- WARNING: It may differ from actual native database DDL

-- Drop table

-- XH_OA.EMS_DOC_INFO;

CREATE TABLE XH_OA.EMS_DOC_INFO (
	DOC_ID VARCHAR2(50),
	HOSPITAL_ID VARCHAR2(20),
	DOC_CLASS VARCHAR2(50),
	DOC_INFO_ID VARCHAR2(50),
	EQUIPMENT_ID VARCHAR2(50),
	DOC_NAME VARCHAR2(200),
	DOC_FILE VARCHAR2(200),
	DOC_TYPE VARCHAR2(20),
	UPLOAD_PERSON VARCHAR2(50),
	UPLOAD_TIME DATE,
	DOC_STATE VARCHAR2(20),
	FIRST_RPERSON VARCHAR2(50),
	FIRST_RTIME DATE,
	LAST_MPERSON VARCHAR2(50),
	LAST_MTIME DATE,
	REMARK VARCHAR2(200),
	DOC_PATH VARCHAR2(200),
	DOC_SUFFIX VARCHAR2(50),
	PDF_PREVIEW_PATH VARCHAR2(200),
	CONSTRAINT SYS_C0010786 CHECK ("DOC_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010787 CHECK ("HOSPITAL_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010788 CHECK ("DOC_CLASS" IS NOT NULL),
	CONSTRAINT SYS_C0010789 PRIMARY KEY (DOC_ID)
);
CREATE UNIQUE INDEX SYS_C0010789 ON XH_OA.EMS_DOC_INFO (DOC_ID);


-- XH_OA.EMS_ENVI_REQUIRE_INFO definition


-- WARNING: It may differ from actual native database DDL

-- Drop table

-- XH_OA.EMS_ENVI_REQUIRE_INFO;

CREATE TABLE XH_OA.EMS_ENVI_REQUIRE_INFO (
	EQUIPMENT_ID VARCHAR2(20),
	HOSPITAL_ID VARCHAR2(20),
	EQUIPMENT_SIZE VARCHAR2(50),
	EQUIPMENT_WEIGHT VARCHAR2(50),
	BEARING_REQUIRE VARCHAR2(50),
	SPACE_REQUIRE VARCHAR2(50),
	AIR_REQUIRE VARCHAR2(50),
	WATER_REQUIRE VARCHAR2(50),
	TEMPERATURE_REQUIRE VARCHAR2(50),
	HUMIDITY_REQUIRE VARCHAR2(50),
	AIR_PRESSURE_REQUIRE VARCHAR2(50),
	POWER_REQUIRE VARCHAR2(50),
	VOLTAGE_REQUIRE VARCHAR2(200),
	ELECTRICITY_REQUIRE VARCHAR2(200),
	OTHER_REQUIRE VARCHAR2(50),
	REQUIRE_STATE VARCHAR2(20),
	FIRST_RPERSON VARCHAR2(50),
	FIRST_RTIME DATE,
	LAST_MPERSON VARCHAR2(50),
	LAST_MTIME DATE,
	REMARK VARCHAR2(200),
	CONSTRAINT SYS_C0010790 CHECK ("EQUIPMENT_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010791 CHECK ("HOSPITAL_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010792 PRIMARY KEY (EQUIPMENT_ID)
);
CREATE UNIQUE INDEX SYS_C0010792 ON XH_OA.EMS_ENVI_REQUIRE_INFO (EQUIPMENT_ID);


-- XH_OA.EMS_EQUIPMENT_CONTACT definition


-- WARNING: It may differ from actual native database DDL

-- Drop table

-- XH_OA.EMS_EQUIPMENT_CONTACT;

CREATE TABLE XH_OA.EMS_EQUIPMENT_CONTACT (
	CONTACT_ID VARCHAR2(50),
	EQUIPMENT_ID VARCHAR2(20),
	CONTACT_ESTATE VARCHAR2(20),
	FIRST_RPERSON VARCHAR2(50),
	FIRST_RTIME DATE,
	LAST_MPERSON VARCHAR2(50),
	LAST_MTIME DATE,
	REMARK VARCHAR2(200),
	CONTACT_TYPE VARCHAR2(20),
	CONSTRAINT SYS_C0010793 CHECK ("CONTACT_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010794 CHECK ("EQUIPMENT_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010795 CHECK ("CONTACT_TYPE" IS NOT NULL),
	CONSTRAINT SYS_C0010796 PRIMARY KEY (CONTACT_ID,EQUIPMENT_ID,CONTACT_TYPE)
);
CREATE UNIQUE INDEX SYS_C0010796 ON XH_OA.EMS_EQUIPMENT_CONTACT (CONTACT_ID,EQUIPMENT_ID,CONTACT_TYPE);


-- XH_OA.EMS_EQUIPMENT_INFO definition


-- WARNING: It may differ from actual native database DDL

-- Drop table

-- XH_OA.EMS_EQUIPMENT_INFO;

CREATE TABLE XH_OA.EMS_EQUIPMENT_INFO (
	EQUIPMENT_ID VARCHAR2(20),
	UNIT_ID VARCHAR2(20),
	HOSPITAL_ID VARCHAR2(20),
	INSTRUMENT_ID VARCHAR2(50),
	ESERIES_ID VARCHAR2(20),
	EQUIPMENT_NUM VARCHAR2(50),
	EQUIPMENT_NAME VARCHAR2(100),
	EQUIPMENT_MODEL VARCHAR2(50),
	DEPT_NAME VARCHAR2(50),
	SECTION_NO VARCHAR2(50),
	EQUIPMENT_SORT VARCHAR2(20),
	FACTORY_NUM VARCHAR2(50),
	EQUIPMENT_FEATURE VARCHAR2(500),
	BUY_DATE VARCHAR2(20),
	SELL_PRICE NUMBER(18,2),
	KEEP_PERSON VARCHAR2(50),
	INSTALL_DATE VARCHAR2(50),
	INSTALL_AREA VARCHAR2(100),
	DEPRECIATION_TIME VARCHAR2(50),
	ANNUAL_SURVEY_DATE VARCHAR2(50),
	MANUFACTURER VARCHAR2(50),
	DEALER VARCHAR2(50),
	REPAIR_COMPANY VARCHAR2(50),
	APPLY_STATE VARCHAR2(50),
	CERTIFICATE_STATE VARCHAR2(50),
	ACCEPT_REPORT_STATE VARCHAR2(50),
	EQUIPMENT_GRAPH_STATE VARCHAR2(50),
	MANUAL_STATE VARCHAR2(50),
	REPAIR_PERSON VARCHAR2(50),
	REPAIR_PERSON_STATE VARCHAR2(50),
	CONTACT_PHONE VARCHAR2(100),
	REGISTER_PERSON VARCHAR2(50),
	REGISTER_TIME DATE,
	REGISTRATION_NUM VARCHAR2(50),
	EQUIPMENT_STATE VARCHAR2(20),
	FIRST_RPERSON VARCHAR2(50),
	FIRST_RTIME DATE,
	LAST_MPERSON VARCHAR2(50),
	LAST_MTIME DATE,
	REMARK VARCHAR2(500),
	MANUFACTURER_ID VARCHAR2(50),
	DEALER_ID VARCHAR2(50),
	EQUIPMENT_SIZE VARCHAR2(50),
	EQUIPMENT_POWER VARCHAR2(50),
	EQUIPMENT_VOLTAGE VARCHAR2(50),
	EQUIPMENT_TEMP VARCHAR2(50),
	EQUIPMENT_TEMP_RANGE VARCHAR2(50),
	EQ_IN_PERSON VARCHAR2(50),
	EQ_IN_TIME DATE,
	EQ_OUT_PERSON VARCHAR2(50),
	EQ_OUT_TIME DATE,
	EQ_SCRAP_PERSON VARCHAR2(50),
	EQ_SCRAP_TIME DATE,
	EQUIPMENT_TYPE VARCHAR2(20),
	EQUIPMENT_CLASS VARCHAR2(20),
	LAB_ID VARCHAR2(20),
	REGISTRATION_ENUM VARCHAR2(50),
	EQUIPMENT_ENAME VARCHAR2(100),
	EQUIPMENT_CODE VARCHAR2(50),
	PROFESSIONAL_CLASS VARCHAR2(50),
	ENABLE_TIME DATE,
	DEALER_ENAME VARCHAR2(50),
	MANUFACTURER_ENAME VARCHAR2(50),
	SERIAL_NUMBER VARCHAR2(50),
	CALIBRATE_TYPE VARCHAR2(10),
	DEPT_SECTION_NO VARCHAR2(50),
	IS_INDEPT VARCHAR2(10),
	VEST_PIPELINE VARCHAR2(20),
	CONSTRAINT PK_EMS_EQUIPMENT_INFO_1 PRIMARY KEY (EQUIPMENT_ID),
	CONSTRAINT SYS_C0010797 CHECK ("EQUIPMENT_ID" IS NOT NULL)
);
CREATE UNIQUE INDEX PK_EMS_EQUIPMENT_INFO_1 ON XH_OA.EMS_EQUIPMENT_INFO (EQUIPMENT_ID);


-- XH_OA.EMS_INSTALL_INFO definition


-- WARNING: It may differ from actual native database DDL

-- Drop table

-- XH_OA.EMS_INSTALL_INFO;

CREATE TABLE XH_OA.EMS_INSTALL_INFO (
	INSTALL_ID VARCHAR2(50),
	HOSPITAL_ID VARCHAR2(20),
	EQUIPMENT_ID VARCHAR2(20),
	INSTALL_DATE DATE,
	INSTALL_AREA VARCHAR2(50),
	LAB_PERSON VARCHAR2(50),
	HOSPITAL_PERSON VARCHAR2(50),
	INSTALL_CONDITION VARCHAR2(500),
	ENGINEER VARCHAR2(50),
	RELATION_WAY VARCHAR2(50),
	INSTALL_STATE VARCHAR2(20),
	FIRST_RPERSON VARCHAR2(50),
	FIRST_RTIME DATE,
	LAST_MPERSON VARCHAR2(50),
	LAST_MTIME DATE,
	REMARK VARCHAR2(200),
	CONSTRAINT SYS_C0010798 CHECK ("INSTALL_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010799 CHECK ("HOSPITAL_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010800 CHECK ("EQUIPMENT_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010801 PRIMARY KEY (INSTALL_ID)
);
CREATE UNIQUE INDEX EQUIPMENT_LIMIT4 ON XH_OA.EMS_INSTALL_INFO (EQUIPMENT_ID);
CREATE UNIQUE INDEX SYS_C0010801 ON XH_OA.EMS_INSTALL_INFO (INSTALL_ID);


-- XH_OA.EMS_MAINTAIN_INFO definition


-- WARNING: It may differ from actual native database DDL

-- Drop table

-- XH_OA.EMS_MAINTAIN_INFO;

CREATE TABLE XH_OA.EMS_MAINTAIN_INFO (
	MAINTAIN_ID VARCHAR2(50),
	HOSPITAL_ID VARCHAR2(20),
	EQUIPMENT_ID VARCHAR2(20),
	MAINTAIN_CYCLE VARCHAR2(20),
	MAINTAIN_DATE DATE,
	MAINTAIN_PERSON VARCHAR2(50),
	MAINTAIN_CONTENT VARCHAR2(1000),
	FIRST_RPERSON VARCHAR2(50),
	FIRST_RTIME DATE,
	LAST_MPERSON VARCHAR2(50),
	LAST_MTIME DATE,
	REMARK VARCHAR2(200),
	OCCUR_EVENT VARCHAR2(50),
	MAINTAIN_NO VARCHAR2(50),
	MAINTAIN_STATE VARCHAR2(20),
	CONSTRAINT SYS_C0010802 CHECK ("MAINTAIN_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010803 CHECK ("HOSPITAL_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010804 CHECK ("EQUIPMENT_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010805 PRIMARY KEY (MAINTAIN_ID)
);
CREATE UNIQUE INDEX SYS_C0010805 ON XH_OA.EMS_MAINTAIN_INFO (MAINTAIN_ID);


-- XH_OA.EMS_PARTS_INFO definition


-- WARNING: It may differ from actual native database DDL

-- Drop table

-- XH_OA.EMS_PARTS_INFO;

CREATE TABLE XH_OA.EMS_PARTS_INFO (
	PARTS_ID VARCHAR2(50),
	HOSPITAL_ID VARCHAR2(20),
	EQUIPMENT_ID VARCHAR2(20),
	PARTS_NAME VARCHAR2(50),
	PARTS_MODEL VARCHAR2(50),
	PARTS_SPEC VARCHAR2(50),
	PARTS_SNUM VARCHAR2(50),
	PARTS_ORIGIN VARCHAR2(50),
	PARTS_BRAND VARCHAR2(50),
	PARTS_POSITION VARCHAR2(50),
	PARTS_STATE VARCHAR2(50),
	FIRST_RPERSON VARCHAR2(50),
	FIRST_RTIME DATE,
	LAST_MPERSON VARCHAR2(50),
	LAST_MTIME DATE,
	REMARK VARCHAR2(200),
	PARTS_AMOUNT NUMBER(38,0),
	CONSTRAINT SYS_C0010806 CHECK ("PARTS_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010807 CHECK ("HOSPITAL_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010808 CHECK ("EQUIPMENT_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010809 PRIMARY KEY (PARTS_ID)
);
CREATE UNIQUE INDEX SYS_C0010809 ON XH_OA.EMS_PARTS_INFO (PARTS_ID);


-- XH_OA.EMS_PURCHASE_INFO definition


-- WARNING: It may differ from actual native database DDL

-- Drop table

-- XH_OA.EMS_PURCHASE_INFO;

CREATE TABLE XH_OA.EMS_PURCHASE_INFO (
	PURCHASE_ID VARCHAR2(20),
	HOSPITAL_ID VARCHAR2(20),
	LAB_ID VARCHAR2(20),
	MGROUP_ID VARCHAR2(20),
	EQUIPMENT_ID VARCHAR2(20),
	CALL_BIDS_NAME VARCHAR2(100),
	CALL_BIDS_NO VARCHAR2(20),
	CALL_BIDS_DATE DATE,
	CONTRACT_NAME VARCHAR2(50),
	CONTRACT_NO VARCHAR2(20),
	CONTRACT_DATE DATE,
	PURCHASE_STATE VARCHAR2(20),
	FIRST_RPERSON VARCHAR2(50),
	FIRST_RTIME DATE,
	LAST_MPERSON VARCHAR2(50),
	LAST_MTIME DATE,
	REMARK VARCHAR2(200),
	CONSTRAINT SYS_C0010810 CHECK ("PURCHASE_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010811 CHECK ("HOSPITAL_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010812 CHECK ("LAB_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010813 CHECK ("MGROUP_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010814 CHECK ("EQUIPMENT_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010815 PRIMARY KEY (PURCHASE_ID)
);
CREATE UNIQUE INDEX EQUIPMENT_LIMIT ON XH_OA.EMS_PURCHASE_INFO (EQUIPMENT_ID);
CREATE UNIQUE INDEX SYS_C0010815 ON XH_OA.EMS_PURCHASE_INFO (PURCHASE_ID);


-- XH_OA.EMS_REPAIR_INFO definition


-- WARNING: It may differ from actual native database DDL

-- Drop table

-- XH_OA.EMS_REPAIR_INFO;

CREATE TABLE XH_OA.EMS_REPAIR_INFO (
	REPAIR_ID VARCHAR2(50),
	HOSPITAL_ID VARCHAR2(20),
	EQUIPMENT_ID VARCHAR2(20),
	REPAIR_DATE DATE,
	REPAIR_PERSON VARCHAR2(50),
	REPAIR_CONTENT VARCHAR2(1000),
	REPAIR_RESULT VARCHAR2(50),
	OCCUR_EVENT VARCHAR2(50),
	FIRST_RPERSON VARCHAR2(50),
	FIRST_RTIME DATE,
	LAST_MPERSON VARCHAR2(50),
	LAST_MTIME DATE,
	REMARK VARCHAR2(200),
	REPAIR_NO VARCHAR2(50),
	REPAIR_STATE VARCHAR2(20),
	CONSTRAINT SYS_C0010816 CHECK ("REPAIR_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010817 CHECK ("HOSPITAL_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010818 CHECK ("EQUIPMENT_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010819 PRIMARY KEY (REPAIR_ID)
);
CREATE UNIQUE INDEX SYS_C0010819 ON XH_OA.EMS_REPAIR_INFO (REPAIR_ID);


-- XH_OA.EMS_SCRAP_INFO definition


-- WARNING: It may differ from actual native database DDL

-- Drop table

-- XH_OA.EMS_SCRAP_INFO;

CREATE TABLE XH_OA.EMS_SCRAP_INFO (
	SCRAP_ID VARCHAR2(50),
	HOSPITAL_ID VARCHAR2(20),
	EQUIPMENT_ID VARCHAR2(20),
	SCRAP_DATE DATE,
	SCRAP_CAUSE VARCHAR2(200),
	OPER_PERSON VARCHAR2(50),
	OPER_TIME DATE,
	SCRAP_STATE VARCHAR2(20),
	FIRST_RPERSON VARCHAR2(50),
	FIRST_RTIME DATE,
	LAST_MPERSON VARCHAR2(50),
	LAST_MTIME DATE,
	REMARK VARCHAR2(200),
	EXAMINE_PERSON VARCHAR2(50),
	EXAMINE_DATE DATE,
	EXAMINE_OPINION VARCHAR2(200),
	APPROVE_PERSON VARCHAR2(50),
	APPROVE_DATE DATE,
	APPROVE_OPINION VARCHAR2(200),
	APPLY_STATE VARCHAR2(20),
	OPER_PERSON_ID VARCHAR2(50),
	EXAMINE_PERSON_ID VARCHAR2(50),
	APPROVE_PERSON_ID VARCHAR2(50),
	APPLY_TYPE VARCHAR2(20),
	IF_RE_APPLY VARCHAR2(20),
	FILE_PATH VARCHAR2(100),
	CONSTRAINT SYS_C0010820 CHECK ("SCRAP_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010821 CHECK ("HOSPITAL_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010822 CHECK ("EQUIPMENT_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010823 PRIMARY KEY (SCRAP_ID)
);
CREATE UNIQUE INDEX SYS_C0010823 ON XH_OA.EMS_SCRAP_INFO (SCRAP_ID);


-- XH_OA.EMS_SCRAP_LOG definition


-- WARNING: It may differ from actual native database DDL

-- Drop table

-- XH_OA.EMS_SCRAP_LOG;

CREATE TABLE XH_OA.EMS_SCRAP_LOG (
	SCRAP_PROCESS_ID VARCHAR2(50),
	HOSPITAL_ID VARCHAR2(20),
	SCRAP_ID VARCHAR2(50),
	PROCESS_STATE VARCHAR2(20),
	OPER_PERSON VARCHAR2(50),
	OPER_TIME DATE,
	PROCESS_CONTENT VARCHAR2(200),
	FIRST_RPERSON VARCHAR2(50),
	FIRST_RTIME DATE,
	LAST_MPERSON VARCHAR2(50),
	LAST_MTIME DATE,
	REMARK VARCHAR2(200),
	PROCESS_TYPE VARCHAR2(50),
	CONSTRAINT SYS_C0010824 CHECK ("SCRAP_PROCESS_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010825 CHECK ("HOSPITAL_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010826 CHECK ("SCRAP_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010827 PRIMARY KEY (SCRAP_PROCESS_ID)
);
CREATE UNIQUE INDEX SYS_C0010827 ON XH_OA.EMS_SCRAP_LOG (SCRAP_PROCESS_ID);


-- XH_OA.EMS_STARTUP_INFO definition


-- WARNING: It may differ from actual native database DDL

-- Drop table

-- XH_OA.EMS_STARTUP_INFO;

CREATE TABLE XH_OA.EMS_STARTUP_INFO (
	STRATUP_ID VARCHAR2(50),
	HOSPITAL_ID VARCHAR2(20),
	EQUIPMENT_ID VARCHAR2(20),
	START_DATE DATE,
	END_DATE DATE,
	VERIFY_PERSON VARCHAR2(50),
	VERIFY_RESULT VARCHAR2(500),
	STRATUP_STATE VARCHAR2(50),
	FIRST_RPERSON VARCHAR2(50),
	FIRST_RTIME DATE,
	LAST_MPERSON VARCHAR2(50),
	LAST_MTIME DATE,
	REMARK VARCHAR2(200),
	CONSTRAINT STARTUP_EQUIPMENT UNIQUE (EQUIPMENT_ID),
	CONSTRAINT SYS_C0010828 CHECK ("STRATUP_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010829 CHECK ("HOSPITAL_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010830 CHECK ("EQUIPMENT_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010831 PRIMARY KEY (STRATUP_ID)
);
CREATE UNIQUE INDEX STARTUP_EQUIPMENT ON XH_OA.EMS_STARTUP_INFO (EQUIPMENT_ID);
CREATE UNIQUE INDEX SYS_C0010831 ON XH_OA.EMS_STARTUP_INFO (STRATUP_ID);


-- XH_OA.EMS_START_STOP definition


-- WARNING: It may differ from actual native database DDL

-- Drop table

-- XH_OA.EMS_START_STOP;

CREATE TABLE XH_OA.EMS_START_STOP (
	START_STOP_ID VARCHAR2(50),
	HOSPITAL_ID VARCHAR2(20),
	EQUIPMENT_ID VARCHAR2(20),
	START_DATE DATE,
	START_CAUSE VARCHAR2(200),
	OPER_PERSON VARCHAR2(50),
	OPER_TIME DATE,
	START_STOP_STATE VARCHAR2(20),
	FIRST_RPERSON VARCHAR2(50),
	FIRST_RTIME DATE,
	LAST_MPERSON VARCHAR2(50),
	LAST_MTIME DATE,
	REMARK VARCHAR2(200),
	START_STOP_TYPE VARCHAR2(20),
	VERIFY_ID VARCHAR2(50),
	CONSTRAINT SYS_C0010833 CHECK ("START_STOP_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010834 CHECK ("HOSPITAL_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010835 CHECK ("EQUIPMENT_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010836 PRIMARY KEY (START_STOP_ID)
);
CREATE UNIQUE INDEX SYS_C0010836 ON XH_OA.EMS_START_STOP (START_STOP_ID);


-- XH_OA.EMS_SUBSCRIBE_INFO definition


-- WARNING: It may differ from actual native database DDL

-- Drop table

-- XH_OA.EMS_SUBSCRIBE_INFO;

CREATE TABLE XH_OA.EMS_SUBSCRIBE_INFO (
	SUBSCRIBE_ID VARCHAR2(20),
	HOSPITAL_ID VARCHAR2(20),
	LAB_ID VARCHAR2(20),
	MGROUP_ID VARCHAR2(20),
	EQUIPMENT_ID VARCHAR2(200),
	SUBSCRIBE_NAME VARCHAR2(100),
	SUBSCRIBE_NO VARCHAR2(50),
	SUBSCRIBE_DATE DATE,
	SUBSCRIBE_PERSON VARCHAR2(50),
	REQ_TIME DATE,
	APPROVE_PERSON VARCHAR2(50),
	APPROVE_TIME DATE,
	APPROVE_OPINION VARCHAR2(500),
	SUBSCRIBE_STATE VARCHAR2(20),
	FIRST_RPERSON VARCHAR2(50),
	FIRST_RTIME DATE,
	LAST_MPERSON VARCHAR2(50),
	LAST_MTIME DATE,
	REMARK VARCHAR2(50),
	CONSTRAINT SYS_C0010837 CHECK ("SUBSCRIBE_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010838 CHECK ("HOSPITAL_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010839 CHECK ("LAB_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010840 CHECK ("MGROUP_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010841 CHECK ("SUBSCRIBE_NAME" IS NOT NULL),
	CONSTRAINT SYS_C0010842 CHECK ("SUBSCRIBE_DATE" IS NOT NULL),
	CONSTRAINT SYS_C0010843 CHECK ("SUBSCRIBE_PERSON" IS NOT NULL),
	CONSTRAINT SYS_C0010844 PRIMARY KEY (SUBSCRIBE_ID)
);
CREATE UNIQUE INDEX SYS_C0010844 ON XH_OA.EMS_SUBSCRIBE_INFO (SUBSCRIBE_ID);


-- XH_OA.EMS_TRAIN_INFO definition


-- WARNING: It may differ from actual native database DDL

-- Drop table

-- XH_OA.EMS_TRAIN_INFO;

CREATE TABLE XH_OA.EMS_TRAIN_INFO (
	TRAIN_ID VARCHAR2(50),
	HOSPITAL_ID VARCHAR2(20),
	EQUIPMENT_ID VARCHAR2(20),
	TRAIN_TIME DATE,
	TRAIN_NAME VARCHAR2(200),
	TRAIN_ADDR VARCHAR2(200),
	TRAIN_HOUR VARCHAR2(20),
	JOIN_PERSON VARCHAR2(800),
	TRAIN_TEACHER VARCHAR2(50),
	TRAIN_STATE VARCHAR2(20),
	FIRST_RPERSON VARCHAR2(50),
	FIRST_RTIME DATE,
	LAST_MPERSON VARCHAR2(50),
	LAST_MTIME DATE,
	REMARK VARCHAR2(200),
	CONSTRAINT SYS_C0010845 CHECK ("TRAIN_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010846 CHECK ("HOSPITAL_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010847 CHECK ("EQUIPMENT_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010848 PRIMARY KEY (TRAIN_ID)
);
CREATE UNIQUE INDEX SYS_C0010848 ON XH_OA.EMS_TRAIN_INFO (TRAIN_ID);


-- XH_OA.EMS_UNPACK_INFO definition


-- WARNING: It may differ from actual native database DDL

-- Drop table

-- XH_OA.EMS_UNPACK_INFO;

CREATE TABLE XH_OA.EMS_UNPACK_INFO (
	UNPACK_ID VARCHAR2(50),
	HOSPITAL_ID VARCHAR2(20),
	EQUIPMENT_ID VARCHAR2(20),
	UNPACK_DATE DATE,
	UNPACK_PERSON VARCHAR2(50),
	LAB_PERSON VARCHAR2(50),
	HOSPITAL_PERSON VARCHAR2(50),
	APPEARANCE_INSPECT VARCHAR2(50),
	UNPACK_CONDITION VARCHAR2(500),
	ENGINEER VARCHAR2(50),
	RELATION_WAY VARCHAR2(50),
	UNPACK_STATE VARCHAR2(20),
	FIRST_RPERSON VARCHAR2(50),
	FIRST_RTIME DATE,
	LAST_MPERSON VARCHAR2(50),
	LAST_MTIME DATE,
	REMARK VARCHAR2(200),
	CONSTRAINT SYS_C0010849 CHECK ("UNPACK_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010850 CHECK ("HOSPITAL_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010851 CHECK ("EQUIPMENT_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010852 PRIMARY KEY (UNPACK_ID)
);
CREATE UNIQUE INDEX EQUIPMENT_LIMIT3 ON XH_OA.EMS_UNPACK_INFO (EQUIPMENT_ID);
CREATE UNIQUE INDEX SYS_C0010852 ON XH_OA.EMS_UNPACK_INFO (UNPACK_ID);


-- XH_OA.EMS_VERIFICATION_INFO definition


-- WARNING: It may differ from actual native database DDL

-- Drop table

-- XH_OA.EMS_VERIFICATION_INFO;

CREATE TABLE XH_OA.EMS_VERIFICATION_INFO (
	VERIFICATION_ID VARCHAR2(50),
	HOSPITAL_ID VARCHAR2(20),
	EQUIPMENT_ID VARCHAR2(20),
	VERIFICATION_DATE DATE,
	RELATION_EVENT VARCHAR2(50),
	VERIFICATION_PERSON VARCHAR2(50),
	VERIFICATION_RESULT VARCHAR2(50),
	FIRST_RPERSON VARCHAR2(50),
	FIRST_RTIME DATE,
	LAST_MPERSON VARCHAR2(50),
	LAST_MTIME DATE,
	REMARK VARCHAR2(200),
	STATE VARCHAR2(50),
	VERIFICATION_NO VARCHAR2(50),
	VERIFICATION_STATE VARCHAR2(20),
	CONSTRAINT SYS_C0010853 CHECK ("VERIFICATION_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010854 CHECK ("HOSPITAL_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010855 CHECK ("EQUIPMENT_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010856 PRIMARY KEY (VERIFICATION_ID)
);
CREATE UNIQUE INDEX SYS_C0010856 ON XH_OA.EMS_VERIFICATION_INFO (VERIFICATION_ID);


-- XH_OA.EMS_WORK_PLAN definition


-- WARNING: It may differ from actual native database DDL

-- Drop table

-- XH_OA.EMS_WORK_PLAN;

CREATE TABLE XH_OA.EMS_WORK_PLAN (
	WORK_PLAN_ID VARCHAR2(50),
	HOSPITAL_ID VARCHAR2(20),
	EQUIPMENT_ID VARCHAR2(50),
	WORK_PLAN_STATE VARCHAR2(20),
	MAINTAIN_INTERVALS VARCHAR2(50),
	MAINTAIN_WARN_INTERVALS VARCHAR2(50),
	COMPARISON_INTERVALS VARCHAR2(50),
	COMPARISON_WARN_INTERVALS VARCHAR2(50),
	VERIFICATION_INTERVALS VARCHAR2(50),
	VERIFICATION_WARN_INTERVALS VARCHAR2(50),
	FIRST_RPERSON VARCHAR2(50),
	FIRST_RTIME DATE,
	LAST_MPERSON VARCHAR2(50),
	LAST_MTIME DATE,
	REMARK VARCHAR2(200),
	CORRECT_INTERVALS VARCHAR2(50),
	CORRECT_WARN_INTERVALS VARCHAR2(50),
	OPER_TIME DATE,
	OPER_PERSON VARCHAR2(50),
	MONTHLY_MAINTAIN VARCHAR2(50),
	MONTHLY_MAINTAIN_WARN VARCHAR2(50),
	CONSTRAINT SYS_C0010857 CHECK ("WORK_PLAN_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010858 CHECK ("HOSPITAL_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010859 CHECK ("EQUIPMENT_ID" IS NOT NULL),
	CONSTRAINT SYS_C0010860 PRIMARY KEY (WORK_PLAN_ID)
);
CREATE UNIQUE INDEX SYS_C0010860 ON XH_OA.EMS_WORK_PLAN (WORK_PLAN_ID);